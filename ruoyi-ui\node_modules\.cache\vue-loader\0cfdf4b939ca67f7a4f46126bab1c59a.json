{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\info\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\info\\index.vue", "mtime": 1756456282785}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0SW5mbywgYWRkSW5mbywgdXBkYXRlSW5mbywgZGVsSW5mbywgZXhwb3J0SW5mbywgaW1wb3J0SW5mbywgZG93bmxvYWRUZW1wbGF0ZSB9IGZyb20gJ0AvYXBpL3N1cHBseS9pbmZvJw0KaW1wb3J0IHsgZ2V0RmFjLCBhZGRGYWMsIHVwZGF0ZUZhYyB9IGZyb20gJ0AvYXBpL3N1cHBseS9mYWMnDQppbXBvcnQgeyBnZXRIZWFsdGgsIGFkZEhlYWx0aCwgdXBkYXRlSGVhbHRoIH0gZnJvbSAnQC9hcGkvc3VwcGx5L2hlYWx0aCcNCmltcG9ydCB7IGxpc3RGaWxlLCBkZWxGaWxlIH0gZnJvbSAnQC9hcGkvc3VwcGx5L2ZpbGUnDQppbXBvcnQgeyBnZXRTdXBwbHlJbmZvQnlDb2RlLCBsaXN0U3VwcGx5SW5mbyB9IGZyb20gJ0AvYXBpL3N1cHBseS9zdXBwbHlJbmZvJw0KaW1wb3J0IHJlcXVlc3QgZnJvbSAnQC91dGlscy9yZXF1ZXN0Jw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICdAL3V0aWxzL2F1dGgnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1N1cHBseVVzZXJJbmZvJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBzdXBwbHlDb2RlOiAnJw0KICAgICAgfSwNCiAgICAgIHVzZXJMaXN0OiBbXSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g5bKX5L2N6K+G5Yir5Y2hDQogICAgICBmYWNEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGZhY0Zvcm06IHt9LA0KICAgICAgZmFjRm9ybUl0ZW1zOiBbDQogICAgICAgIHsgZmllbGQ6ICd1c2VyUG9zdCcsIHRpdGxlOiAn5bKX5L2N5ZCN56ewJywgc3BhbjogMjQsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZVRleHRhcmVhJywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXlspfkvY3lkI3np7AnLCByb3dzOiAyIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAndXNlckZhY0NsYXNzJywgdGl0bGU6ICflspfkvY3nj63nu4QnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeWyl+S9jeePree7hCcgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICd1c2VyRGVwdE5hbWUnLCB0aXRsZTogJ+aJgOWxnumDqOmXqCcsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5omA5bGe6YOo6ZeoJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ3VzZXJGYWNXb3JrJywgdGl0bGU6ICflspfkvY3mj4/ov7AnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeWyl+S9jeaPj+i/sCcgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICd1c2VyVGltZUJlZ2luJywgdGl0bGU6ICflhaXljoLml7bpl7QnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyB0eXBlOiAnZGF0ZScsIHBsYWNlaG9sZGVyOiAn6YCJ5oup5pel5pyfJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ3VzZXJUaW1lRW5kJywgdGl0bGU6ICfnprvljoLml7bpl7QnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyB0eXBlOiAnZGF0ZScsIHBsYWNlaG9sZGVyOiAn6YCJ5oup5pel5pyfJyB9IH0gfSwNCiAgICAgICAgew0KICAgICAgICAgIGZpZWxkOiAnc3RhdGUnLA0KICAgICAgICAgIHRpdGxlOiAn54q25oCBJywNCiAgICAgICAgICBzcGFuOiAyNCwNCiAgICAgICAgICBpdGVtUmVuZGVyOiB7DQogICAgICAgICAgICBuYW1lOiAnVnhlU2VsZWN0JywNCiAgICAgICAgICAgIG9wdGlvbnM6IFsNCiAgICAgICAgICAgICAgeyBsYWJlbDogJ+i1t+iNiScsIHZhbHVlOiAwIH0sDQogICAgICAgICAgICAgIHsgbGFiZWw6ICfliIbljoLlrqHmoLjkuronLCB2YWx1ZTogMSB9LA0KICAgICAgICAgICAgICB7IGxhYmVsOiAn5Lq65Yqb6LWE5rqQ6YOoJywgdmFsdWU6IDIgfSwNCiAgICAgICAgICAgICAgeyBsYWJlbDogJ+mAgOWbnicsIHZhbHVlOiAtMSB9LA0KICAgICAgICAgICAgICB7IGxhYmVsOiAn56aB55SoJywgdmFsdWU6IDEwMSB9LA0KICAgICAgICAgICAgICB7IGxhYmVsOiAn5a6h5qC46YCa6L+HJywgdmFsdWU6IDk5IH0sDQogICAgICAgICAgICAgIHsgbGFiZWw6ICfliKDpmaQnLCB2YWx1ZTogMTAyIH0NCiAgICAgICAgICAgIF0sDQogICAgICAgICAgICBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+mAieaLqScgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIC8vIOWBpeW6t+S/oeaBrw0KICAgICAgaGVhbHRoRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBoZWFsdGhGb3JtOiB7fSwNCiAgICAgIGhlYWx0aEZvcm1JdGVtczogWw0KICAgICAgICB7IGZpZWxkOiAnaGVhbGRhdGUnLCB0aXRsZTogJ+S9k+ajgOaXpeacnycsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHR5cGU6ICdkYXRlJywgcGxhY2Vob2xkZXI6ICfpgInmi6nml6XmnJ8nIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaG9zJywgdGl0bGU6ICfljLvpmaInLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeWMu+mZoicgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFsdHonLCB0aXRsZTogJ+S9k+mHjScsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5L2T6YeNJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWx0enpzJywgdGl0bGU6ICfkvZPph43mjIfmlbAnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeS9k+mHjeaMh+aVsCcgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFscHR0JywgdGl0bGU6ICfooYDns5YnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeihgOezlicgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFsc3N5JywgdGl0bGU6ICfmlLbnvKnljosnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeaUtue8qeWOiycgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFsc3p5JywgdGl0bGU6ICfoiJLlvKDljosnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeiIkuW8oOWOiycgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFsemRnYycsIHRpdGxlOiAn5oC76IOG5Zu66YaHJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXmgLvog4blm7rphocnIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbGd5c3onLCB0aXRsZTogJ+eUmOayueS4iemFrycsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl55SY5rK55LiJ6YWvJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxnYScsIHRpdGxlOiAn6LC35rCo6YWw6L2s6IK96YW2Jywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXosLfmsKjphbDovazogr3phbYnIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbGdiJywgdGl0bGU6ICfosLfkuJnovazmsKjphbYnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeiwt+S4mei9rOawqOmFticgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFsZ2MnLCB0aXRsZTogJ+iwt+iNiei9rOawqOmFticsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl6LC36I2J6L2s5rCo6YW2JyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxuc2QnLCB0aXRsZTogJ+Wwv+e0oOawricsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5bC/57Sg5rCuJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxqZycsIHRpdGxlOiAn6IKM6YWQJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXogozphZAnIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbHhkJywgdGl0bGU6ICflv4PnlLXlm74nLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeW/g+eUteWbvicgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFseGonLCB0aXRsZTogJ+Wwj+e7kycsIHNwYW46IDI0LCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVUZXh0YXJlYScsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5bCP57uTJywgcm93czogMiB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxqeScsIHRpdGxlOiAn5bu66K6uJywgc3BhbjogMjQsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZVRleHRhcmVhJywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXlu7rorq4nLCByb3dzOiAyIH0gfSB9LA0KICAgICAgICB7DQogICAgICAgICAgZmllbGQ6ICdzdGF0ZScsDQogICAgICAgICAgdGl0bGU6ICfnirbmgIEnLA0KICAgICAgICAgIHNwYW46IDEyLA0KICAgICAgICAgIGl0ZW1SZW5kZXI6IHsNCiAgICAgICAgICAgIG5hbWU6ICdWeGVTZWxlY3QnLA0KICAgICAgICAgICAgb3B0aW9uczogWw0KICAgICAgICAgICAgICB7IGxhYmVsOiAn5q2j5bi4JywgdmFsdWU6IDEgfSwNCiAgICAgICAgICAgICAgeyBsYWJlbDogJ+WIoOmZpCcsIHZhbHVlOiAxMDEgfQ0KICAgICAgICAgICAgXSwNCiAgICAgICAgICAgIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJyB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgLy8g6ZmE5Lu2DQogICAgICBmaWxlRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBmaWxlTGlzdDogW10sDQogICAgICB1cGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL3dlYi9zdXBwbHkvdXNlcmZpbGUvdXBsb2FkJywgLy8g5paw55qEIFNGVFAg5LiK5Lyg5o6l5Y+jDQogICAgICBjdXJyZW50VXNlcklkOiBudWxsLA0KICAgICAgY3VycmVudFVzZXJJbmZvOiB7fSwgLy8g5paw5aKe77ya5L+d5a2Y5b2T5YmN55So5oi35L+h5oGvDQogICAgICAvLyDkuIrkvKDphY3nva4NCiAgICAgIHVwbG9hZDogew0KICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246ICdCZWFyZXIgJyArIGdldFRva2VuKCkgfQ0KICAgICAgfSwNCiAgICAgIC8vIOaWsOWini/nvJbovpHkuLvooagNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgZGlhbG9nVGl0bGU6ICcnLA0KICAgICAgZm9ybToge30sDQogICAgICBpbXBvcnREaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGltcG9ydFJlc3VsdERpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgaW1wb3J0TG9hZGluZzogZmFsc2UsDQogICAgICBpbXBvcnRGaWxlOiBudWxsLA0KICAgICAgaW1wb3J0UmVzdWx0OiB7DQogICAgICAgIHN1Y2Nlc3NDb3VudDogMCwNCiAgICAgICAgcGFydGlhbFN1Y2Nlc3NDb3VudDogMCwNCiAgICAgICAgZmFpbENvdW50OiAwLA0KICAgICAgICBkZXRhaWxzOiBbXQ0KICAgICAgfSwNCiAgICAgIC8vIOS+m+W6lOWVhuS/oeaBr+W8ueeqlw0KICAgICAgc3VwcGx5SW5mb0RpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgc3VwcGx5SW5mb0RhdGE6IG51bGwNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgdXBsb2FkRGF0YSgpIHsNCiAgICAgIHJldHVybiB7DQogICAgICAgIHVzZXJpZDogdGhpcy5jdXJyZW50VXNlcklkLA0KICAgICAgICB1c2VyY29kZTogdGhpcy5jdXJyZW50VXNlckluZm8udXNlckNvZGUsDQogICAgICAgIHVzZXJuYW1lOiB0aGlzLmN1cnJlbnRVc2VySW5mby51c2VyTmFtZSwNCiAgICAgICAgc3VwcGx5Y29kZTogdGhpcy5jdXJyZW50VXNlckluZm8uc3VwcGx5Q29kZSwNCiAgICAgICAgc3VwcGx5bmFtZTogdGhpcy5jdXJyZW50VXNlckluZm8uc3VwcGx5TmFtZSwNCiAgICAgICAgaWRjYXJkOiB0aGlzLmN1cnJlbnRVc2VySW5mby5pZGNhcmQsDQogICAgICAgIHVzZXJkZXB0bmFtZTogdGhpcy5jdXJyZW50VXNlckluZm8udXNlckRlcHROYW1lDQogICAgICB9DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5p+l6K+i55So5oi35YiX6KGoDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICBsaXN0SW5mbyh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMudXNlckxpc3QgPSByZXMucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzLnRvdGFsDQogICAgICB9KQ0KICAgIH0sDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3VwcGx5Q29kZSA9ICcnDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIC8vIOaWsOWing0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVGl0bGUgPSAn5paw5aKe55u45YWz5pa55Lq65ZGYJw0KICAgICAgdGhpcy5mb3JtID0ge30NCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIC8vIOe8lui+kQ0KICAgIGhhbmRsZUVkaXQocm93KSB7DQogICAgICB0aGlzLmRpYWxvZ1RpdGxlID0gJ+e8lui+keebuOWFs+aWueS6uuWRmCcNCiAgICAgIHRoaXMuZm9ybSA9IE9iamVjdC5hc3NpZ24oe30sIHJvdykNCiAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIC8vIOWIoOmZpA0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuWIoOmZpOivpeadoeaVsOaNruWQl++8nycsICfmj5DnpLonLCB7IHR5cGU6ICd3YXJuaW5nJyB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgZGVsSW5mbyhyb3cuaWQpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDmj5DkuqTkuLvooagNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5pZCkgew0KICAgICAgICB1cGRhdGVJbmZvKHRoaXMuZm9ybSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkv67mlLnmiJDlip8nKQ0KICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICBhZGRJbmZvKHRoaXMuZm9ybSkudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlrDlop7miJDlip8nKQ0KICAgICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICAvLyDlr7zlh7oNCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICBleHBvcnRJbmZvKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgY29uc3QgYmxvYiA9IG5ldyBCbG9iKFtyZXNdLCB7IHR5cGU6ICdhcHBsaWNhdGlvbi92bmQubXMtZXhjZWwnIH0pDQogICAgICAgIGNvbnN0IHVybCA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpDQogICAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJykNCiAgICAgICAgbGluay5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnDQogICAgICAgIGxpbmsuaHJlZiA9IHVybA0KICAgICAgICBsaW5rLnNldEF0dHJpYnV0ZSgnZG93bmxvYWQnLCAn55u45YWz5pa55Lq65ZGY5pWw5o2uLnhsc3gnKQ0KICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGxpbmspDQogICAgICAgIGxpbmsuY2xpY2soKQ0KICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGxpbmspDQogICAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKHVybCkNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDlr7zlhaUNCiAgICBoYW5kbGVJbXBvcnQoKSB7DQogICAgICB0aGlzLmltcG9ydERpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB0aGlzLmltcG9ydEZpbGUgPSBudWxsDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnMuaW1wb3J0VXBsb2FkLmNsZWFyRmlsZXMoKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOS4i+i9veWvvOWFpeaooeadvw0KICAgIGhhbmRsZURvd25sb2FkVGVtcGxhdGUoKSB7DQogICAgICBkb3dubG9hZFRlbXBsYXRlKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZG93bmxvYWQocmVzcG9uc2UubXNnLCAi5L6b5bqU5ZWG55So5oi35L+h5oGv5a+85YWl5qih5p2/Lnhsc3giKQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmqKHmnb/kuIvovb3lpLHotKUnKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOaWh+S7tumAieaLqeWPmOWMlg0KICAgIGhhbmRsZUZpbGVDaGFuZ2UoZmlsZSkgew0KICAgICAgdGhpcy5pbXBvcnRGaWxlID0gZmlsZS5yYXcNCiAgICB9LA0KICAgIC8vIOaPkOS6pOWvvOWFpQ0KICAgIGhhbmRsZUltcG9ydFN1Ym1pdCgpIHsNCiAgICAgIGlmICghdGhpcy5pbXBvcnRGaWxlKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+ivt+mAieaLqeimgeWvvOWFpeeahOaWh+S7ticpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpDQogICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCB0aGlzLmltcG9ydEZpbGUpDQoNCiAgICAgIHRoaXMuaW1wb3J0TG9hZGluZyA9IHRydWUNCiAgICAgIGltcG9ydEluZm8oZm9ybURhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmltcG9ydExvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB0aGlzLmltcG9ydERpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KDQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAvLyDlpITnkIblr7zlhaXnu5PmnpwNCiAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgICAvLyDmnInor6bnu4bnu5PmnpzmlbDmja7vvIzmmL7npLrnu5PmnpzlvLnnqpcNCiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0ltcG9ydFJlc3VsdChyZXNwb25zZS5kYXRhKQ0KICAgICAgICAgICAgdGhpcy5pbXBvcnRSZXN1bHREaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDlhajpg6jmiJDlip/vvIzlj6rmmL7npLrmtojmga8NCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcyhyZXNwb25zZS5tc2cgfHwgJ+WvvOWFpeaIkOWKnycpDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICflr7zlhaXlpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuaW1wb3J0TG9hZGluZyA9IGZhbHNlDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WvvOWFpeWksei0pTogJyArIChlcnJvci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSkNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDlpITnkIblr7zlhaXnu5PmnpwNCiAgICBwcm9jZXNzSW1wb3J0UmVzdWx0KHJlc3VsdExpc3QpIHsNCiAgICAgIHRoaXMuaW1wb3J0UmVzdWx0LnN1Y2Nlc3NDb3VudCA9IHJlc3VsdExpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5yZXN1bHQgPT09ICfmiJDlip8nKS5sZW5ndGgNCiAgICAgIHRoaXMuaW1wb3J0UmVzdWx0LnBhcnRpYWxTdWNjZXNzQ291bnQgPSByZXN1bHRMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0ucmVzdWx0ID09PSAn6YOo5YiG5oiQ5YqfJykubGVuZ3RoDQogICAgICB0aGlzLmltcG9ydFJlc3VsdC5mYWlsQ291bnQgPSByZXN1bHRMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0ucmVzdWx0ID09PSAn5aSx6LSlJykubGVuZ3RoDQogICAgICB0aGlzLmltcG9ydFJlc3VsdC5kZXRhaWxzID0gcmVzdWx0TGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLnJlc3VsdCAhPT0gJ+aIkOWKnycpDQogICAgfSwNCiAgICAvLyDmn6Xor6LkvpvlupTllYbkv6Hmga8NCiAgICBoYW5kbGVRdWVyeVN1cHBseUluZm8oKSB7DQogICAgICBsaXN0U3VwcGx5SW5mbygpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZyhyZXNwb25zZSkNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5zdXBwbHlJbmZvRGF0YSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICB0aGlzLnN1cHBseUluZm9EaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuc3VwcGx5SW5mb0RhdGEgPSBudWxsDQogICAgICAgICAgdGhpcy5zdXBwbHlJbmZvRGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+acquaJvuWIsOivpeS+m+W6lOWVhuS/oeaBrycpDQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5p+l6K+i5L6b5bqU5ZWG5L+h5oGv5aSx6LSlOiAnICsgKGVycm9yLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKQ0KICAgICAgfSkNCiAgICAgIC8vIGlmICghdGhpcy5xdWVyeVBhcmFtcy5zdXBwbHlDb2RlKSB7DQogICAgICAvLyAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35YWI6L6T5YWl5L6b5bqU5ZWG5Luj56CBJykNCiAgICAgIC8vICAgcmV0dXJuDQogICAgICAvLyB9DQoNCiAgICAgIC8vIGdldFN1cHBseUluZm9CeUNvZGUodGhpcy5xdWVyeVBhcmFtcy5zdXBwbHlDb2RlKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIC8vICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhKSB7DQogICAgICAvLyAgICAgdGhpcy5zdXBwbHlJbmZvRGF0YSA9IHJlc3BvbnNlLmRhdGENCiAgICAgIC8vICAgICB0aGlzLnN1cHBseUluZm9EaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgLy8gICB9IGVsc2Ugew0KICAgICAgLy8gICAgIHRoaXMuc3VwcGx5SW5mb0RhdGEgPSBudWxsDQogICAgICAvLyAgICAgdGhpcy5zdXBwbHlJbmZvRGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+acquaJvuWIsOivpeS+m+W6lOWVhuS/oeaBrycpDQogICAgICAvLyAgIH0NCiAgICAgIC8vIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgIC8vICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5p+l6K+i5L6b5bqU5ZWG5L+h5oGv5aSx6LSlOiAnICsgKGVycm9yLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKQ0KICAgICAgLy8gfSkNCiAgICB9LA0KICAgIC8vIOmZhOS7tuS4iuS8oOWJjeajgOafpeaWh+S7tuexu+Weiw0KICAgIGJlZm9yZUZpbGVVcGxvYWQoZmlsZSkgew0KICAgICAgLy8g5qOA5p+l5paH5Lu257G75Z6L5piv5ZCm5Li6UERGDQogICAgICBjb25zdCBpc1BERiA9IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3BkZicgfHwgZmlsZS5uYW1lLnRvTG93ZXJDYXNlKCkuZW5kc1dpdGgoJy5wZGYnKQ0KICAgICAgaWYgKCFpc1BERikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflj6rog73kuIrkvKBQREbmoLzlvI/mlofku7bvvIEnKQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIA0KICAgICAgLy8g5qOA5p+l5paH5Lu25aSn5bCP6ZmQ5Yi2DQogICAgICBjb25zdCBtYXhTaXplID0gNTAgKiAxMDI0ICogMTAyNCAvLyA1ME1CDQogICAgICBpZiAoZmlsZS5zaXplID4gbWF4U2l6ZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7blpKflsI/kuI3og73otoXov4c1ME1C77yBJykNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9DQogICAgICANCiAgICAgIHJldHVybiB0cnVlDQogICAgfSwNCiAgICAvLyDlspfkvY3or4bliKvljaENCiAgICBvcGVuRmFjRGlhbG9nKHJvdykgew0KICAgICAgZ2V0RmFjKHJvdy5pZCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmZhY0Zvcm0gPSByZXMuZGF0YSB8fCB7IHVzZXJJZDogcm93LmlkIH0NCiAgICAgICAgdGhpcy5mYWNEaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHN1Ym1pdEZhYygpIHsNCiAgICAgIGNvbnN0IGFwaSA9IHRoaXMuZmFjRm9ybS5pZCA/IHVwZGF0ZUZhYyA6IGFkZEZhYw0KICAgICAgYXBpKHRoaXMuZmFjRm9ybSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+d5a2Y5oiQ5YqfJykNCiAgICAgICAgdGhpcy5mYWNEaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5YGl5bq35L+h5oGvDQogICAgb3BlbkhlYWx0aERpYWxvZyhyb3cpIHsNCiAgICAgIGdldEhlYWx0aChyb3cuaWQpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5oZWFsdGhGb3JtID0gcmVzLmRhdGEgfHwgeyB1c2VyaWQ6IHJvdy5pZCB9DQogICAgICAgIHRoaXMuaGVhbHRoRGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIH0pDQogICAgfSwNCiAgICBzdWJtaXRIZWFsdGgoKSB7DQogICAgICBjb25zdCBhcGkgPSB0aGlzLmhlYWx0aEZvcm0uaWQgPyB1cGRhdGVIZWFsdGggOiBhZGRIZWFsdGgNCiAgICAgIGFwaSh0aGlzLmhlYWx0aEZvcm0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpDQogICAgICAgIHRoaXMuaGVhbHRoRGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOmZhOS7tueuoeeQhg0KICAgIG9wZW5GaWxlRGlhbG9nKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50VXNlcklkID0gcm93LmlkDQogICAgICB0aGlzLmN1cnJlbnRVc2VySW5mbyA9IHJvdyAvLyDkv53lrZjlvZPliY3nlKjmiLfkv6Hmga8NCiAgICAgIHRoaXMuZ2V0RmlsZUxpc3Qocm93LmlkKQ0KICAgICAgdGhpcy5maWxlRGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KICAgIGdldEZpbGVMaXN0KHVzZXJpZCkgew0KICAgICAgbGlzdEZpbGUoeyB1c2VyaWQgfSkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmZpbGVMaXN0ID0gcmVzLnJvd3MNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVGaWxlVXBsb2FkU3VjY2VzcyhyZXNwb25zZSkgew0KICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWh+S7tuS4iuS8oOaIkOWKnycpDQogICAgICAgIHRoaXMuZ2V0RmlsZUxpc3QodGhpcy5jdXJyZW50VXNlcklkKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+aWh+S7tuS4iuS8oOWksei0pScpDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVGaWxlVXBsb2FkRXJyb3IoZXJyKSB7DQogICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmlofku7bkuIrkvKDlpLHotKU6ICcgKyAoZXJyLm1lc3NhZ2UgfHwgJ+acquefpemUmeivrycpKQ0KICAgIH0sDQogICAgZGVsZXRlRmlsZShyb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuWIoOmZpOivpemZhOS7tuWQl++8nycsICfmj5DnpLonLCB7IHR5cGU6ICd3YXJuaW5nJyB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgZGVsRmlsZShyb3cuaWQpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykNCiAgICAgICAgICB0aGlzLmdldEZpbGVMaXN0KHRoaXMuY3VycmVudFVzZXJJZCkNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICBkb3dubG9hZEZpbGUocm93KSB7DQogICAgICAvLyDosIPnlKjkuIvovb3mjqXlj6Pojrflj5bmlofku7ZVUkwNCiAgICAgIHJlcXVlc3QuZ2V0KGAvd2ViL3N1cHBseS91c2VyZmlsZS9kb3dubG9hZC8ke3Jvdy5pZH1gKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIC8vIOiOt+WPluWIsOaWh+S7tlVSTOWQju+8jOWcqOaWsOeql+WPo+S4reaJk+W8gOS4i+i9vQ0KICAgICAgICAgIGNvbnN0IGZpbGVVcmwgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgd2luZG93Lm9wZW4oZmlsZVVybCwgJ19ibGFuaycpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+S4i+i9veWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiL6L295aSx6LSlOiAnICsgZXJyb3IubWVzc2FnZSkNCiAgICAgIH0pDQogICAgfSwNCiAgICB0YWJsZVJvd0NsYXNzTmFtZSh7IHJvdywgcm93SW5kZXggfSkgew0KICAgICAgaWYgKHJvdy5zdGF0ZSA9PT0gMSkgew0KICAgICAgICByZXR1cm4gJ3N1Y2Nlc3Mtcm93Jw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICdkYW5nZXItcm93Jw0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyVA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/supply/info", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 查询表单 -->\r\n    <el-form :inline=\"true\" :model=\"queryParams\" class=\"demo-form-inline\" @submit.native.prevent>\r\n      <el-form-item label=\"供应商代码\">\r\n        <el-input v-model=\"queryParams.supplyCode\" placeholder=\"请输入供应商代码\" clearable />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">查询</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n        <!-- <el-button type=\"info\" icon=\"el-icon-info\" @click=\"handleQuerySupplyInfo\">查询供应商信息</el-button> -->\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 工具栏 -->\r\n    <div style=\"margin-bottom: 10px;\">\r\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\r\n      <el-button type=\"success\" icon=\"el-icon-upload\" @click=\"handleImport\">导入</el-button>\r\n      <el-button type=\"warning\" icon=\"el-icon-download\" @click=\"handleExport\">导出</el-button>\r\n    </div>\r\n\r\n    <!-- 用户列表 -->\r\n    <el-table :data=\"userList\" border stripe style=\"width: 100%\">\r\n      <el-table-column prop=\"id\" label=\"ID\" width=\"60\" />\r\n      <el-table-column prop=\"supplyCode\" label=\"供应商代码\" />\r\n      <el-table-column prop=\"userName\" label=\"用户姓名\" />\r\n      <el-table-column prop=\"idcard\" label=\"身份证\" />\r\n      <el-table-column label=\"岗位识别卡\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFacDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"健康信息\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openHealthDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"附件\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFileDialog(scope.row)\">管理</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" width=\"200\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"operation-buttons\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadFile(scope.row)\"\r\n            >\r\n              下载\r\n            </el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"deleteFile(scope.row)\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <el-pagination\r\n      style=\"margin-top: 10px;\"\r\n      background\r\n      layout=\"total, prev, pager, next, jumper\"\r\n      :total=\"total\"\r\n      :page-size=\"queryParams.pageSize\"\r\n      :current-page.sync=\"queryParams.pageNum\"\r\n      @current-change=\"handleQuery\"\r\n    />\r\n\r\n    <!-- 新增/编辑主表弹窗 -->\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogVisible\">\r\n      <el-form :model=\"form\" label-width=\"100px\">\r\n        <el-form-item label=\"供应商代码\">\r\n          <el-input v-model=\"form.supplyCode\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"供应商名称\">\r\n          <el-input v-model=\"form.supplyName\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户编号\">\r\n          <el-input v-model=\"form.userCode\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"用户姓名\">\r\n          <el-input v-model=\"form.userName\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证\">\r\n          <el-input v-model=\"form.idcard\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-select v-model=\"form.state\" placeholder=\"请选择\">\r\n            <el-option label=\"正常\" :value=\"1\" />\r\n            <el-option label=\"删除\" :value=\"101\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导入弹窗 -->\r\n    <el-dialog title=\"导入相关方人员\" :visible.sync=\"importDialogVisible\" width=\"600px\">\r\n      <div style=\"margin-bottom: 20px;\">\r\n        <el-button type=\"info\" icon=\"el-icon-download\" @click=\"handleDownloadTemplate\">下载导入模板</el-button>\r\n      </div>\r\n      <el-upload\r\n        ref=\"importUpload\"\r\n        :auto-upload=\"false\"\r\n        :on-change=\"handleFileChange\"\r\n        :show-file-list=\"true\"\r\n        :limit=\"1\"\r\n        accept=\".xlsx,.xls\"\r\n        drag\r\n      >\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将Excel文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip\" slot=\"tip\">只能上传xlsx/xls文件，且不超过10MB</div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleImportSubmit\" :loading=\"importLoading\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导入结果弹窗 -->\r\n    <el-dialog title=\"导入结果\" :visible.sync=\"importResultDialogVisible\" width=\"800px\">\r\n      <div style=\"margin-bottom: 15px;\">\r\n        <el-tag type=\"success\">成功：{{ importResult.successCount }}条</el-tag>\r\n        <el-tag type=\"warning\" style=\"margin-left: 10px;\">部分成功：{{ importResult.partialSuccessCount }}条</el-tag>\r\n        <el-tag type=\"danger\" style=\"margin-left: 10px;\">失败：{{ importResult.failCount }}条</el-tag>\r\n      </div>\r\n      <el-table :data=\"importResult.details\" max-height=\"400\" border>\r\n        <el-table-column prop=\"userName\" label=\"用户姓名\" width=\"100\" />\r\n        <el-table-column prop=\"idcard\" label=\"身份证号\" width=\"150\" />\r\n        <el-table-column prop=\"supplyCode\" label=\"供应商代码\" width=\"120\" />\r\n        <el-table-column prop=\"result\" label=\"结果\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"scope.row.result === '成功' ? 'success' : scope.row.result === '部分成功' ? 'warning' : 'danger'\">\r\n              {{ scope.row.result }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"reason\" label=\"说明\" />\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importResultDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 供应商信息弹窗 -->\r\n    <el-dialog title=\"供应商信息\" :visible.sync=\"supplyInfoDialogVisible\" width=\"600px\">\r\n      <div v-if=\"supplyInfoData\">\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"供应商代码\">{{ supplyInfoData.supplyCode }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"供应商名称\">{{ supplyInfoData.supplyName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"供应商税号\">{{ supplyInfoData.supplyTax }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"供应商地址\">{{ supplyInfoData.supplyAddr }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"负责人\">{{ supplyInfoData.supplyCharge }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"联系电话\">{{ supplyInfoData.supplyTel }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"状态\">\r\n            <el-tag :type=\"supplyInfoData.state === '1' ? 'success' : 'danger'\">\r\n              {{ supplyInfoData.state === '1' ? '有效' : '无效' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n      <div v-else>\r\n        <el-empty description=\"未找到供应商信息\" />\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"supplyInfoDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 岗位识别卡弹窗 -->\r\n    <vxe-modal v-model=\"facDialogVisible\" title=\"岗位识别卡\" width=\"700\" show-footer>\r\n      <vxe-form\r\n        :data=\"facForm\"\r\n        :items=\"facFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"facDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitFac\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 健康信息弹窗 -->\r\n    <vxe-modal v-model=\"healthDialogVisible\" title=\"健康信息\" width=\"800\" show-footer>\r\n      <vxe-form\r\n        :data=\"healthForm\"\r\n        :items=\"healthFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"healthDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitHealth\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 附件管理弹窗 -->\r\n    <el-dialog \r\n      :visible.sync=\"fileDialogVisible\" \r\n      title=\"附件管理\" \r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n    >\r\n      <!-- 上传区域 -->\r\n      <div class=\"upload-section\">\r\n        <div class=\"upload-header\">\r\n          <i class=\"el-icon-upload\"></i>\r\n          <span class=\"upload-title\">文件上传</span>\r\n        </div>\r\n        <div class=\"upload-content\">\r\n          <el-upload\r\n            ref=\"fileUpload\"\r\n            :action=\"uploadUrl\"\r\n            :headers=\"upload.headers\"\r\n            :data=\"uploadData\"\r\n            :on-success=\"handleFileUploadSuccess\"\r\n            :on-error=\"handleFileUploadError\"\r\n            :before-upload=\"beforeFileUpload\"\r\n            :show-file-list=\"false\"\r\n            accept=\".pdf\"\r\n            drag\r\n            class=\"upload-dragger\"\r\n          >\r\n            <div class=\"upload-area\">\r\n              <i class=\"el-icon-upload upload-icon\"></i>\r\n              <div class=\"upload-text\">\r\n                <span class=\"upload-main-text\">将PDF文件拖到此处，或</span>\r\n                <em class=\"upload-click-text\">点击上传</em>\r\n              </div>\r\n              <div class=\"upload-tip\">\r\n                <i class=\"el-icon-info\"></i>\r\n                <span>仅支持PDF格式文件，单个文件不超过50MB</span>\r\n              </div>\r\n              <div class=\"upload-limits\">\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span>文件格式：PDF</span>\r\n                </div>\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-files\"></i>\r\n                  <span>文件大小：≤ 50MB</span>\r\n                </div>\r\n                <div class=\"limit-item\">\r\n                  <i class=\"el-icon-upload2\"></i>\r\n                  <span>支持拖拽上传</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 文件列表 -->\r\n      <div class=\"file-list-section\">\r\n        <div class=\"file-list-header\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span class=\"file-list-title\">已上传文件</span>\r\n          <span class=\"file-count\">(共 {{fileList.length}} 个文件)</span>\r\n        </div>\r\n        <div class=\"file-list-content\">\r\n          <el-table \r\n            :data=\"fileList\" \r\n            style=\"width: 100%\"\r\n            :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n            :row-class-name=\"tableRowClassName\"\r\n          >\r\n            <el-table-column prop=\"filename\" label=\"文件名\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"file-info\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span class=\"file-name\">{{scope.row.filename}}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"format\" label=\"格式\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{scope.row.format}}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"state\" label=\"状态\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag \r\n                  size=\"mini\" \r\n                  :type=\"scope.row.state === 1 ? 'success' : 'danger'\"\r\n                >\r\n                  {{scope.row.state === 1 ? '正常' : '异常'}}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"operation-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-download\"\r\n                    @click=\"downloadFile(scope.row)\"\r\n                  >\r\n                    下载\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"danger\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"deleteFile(scope.row)\"\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 弹窗底部 -->\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"fileDialogVisible = false\" icon=\"el-icon-close\">关闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listInfo, addInfo, updateInfo, delInfo, exportInfo, importInfo, downloadTemplate } from '@/api/supply/info'\r\nimport { getFac, addFac, updateFac } from '@/api/supply/fac'\r\nimport { getHealth, addHealth, updateHealth } from '@/api/supply/health'\r\nimport { listFile, delFile } from '@/api/supply/file'\r\nimport { getSupplyInfoByCode, listSupplyInfo } from '@/api/supply/supplyInfo'\r\nimport request from '@/utils/request'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  name: 'SupplyUserInfo',\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        supplyCode: ''\r\n      },\r\n      userList: [],\r\n      total: 0,\r\n      // 岗位识别卡\r\n      facDialogVisible: false,\r\n      facForm: {},\r\n      facFormItems: [\r\n        { field: 'userPost', title: '岗位名称', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入岗位名称', rows: 2 } } },\r\n        { field: 'userFacClass', title: '岗位班组', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位班组' } } },\r\n        { field: 'userDeptName', title: '所属部门', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入所属部门' } } },\r\n        { field: 'userFacWork', title: '岗位描述', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位描述' } } },\r\n        { field: 'userTimeBegin', title: '入厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'userTimeEnd', title: '离厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 24,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '起草', value: 0 },\r\n              { label: '分厂审核人', value: 1 },\r\n              { label: '人力资源部', value: 2 },\r\n              { label: '退回', value: -1 },\r\n              { label: '禁用', value: 101 },\r\n              { label: '审核通过', value: 99 },\r\n              { label: '删除', value: 102 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 健康信息\r\n      healthDialogVisible: false,\r\n      healthForm: {},\r\n      healthFormItems: [\r\n        { field: 'healdate', title: '体检日期', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'hos', title: '医院', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入医院' } } },\r\n        { field: 'healtz', title: '体重', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重' } } },\r\n        { field: 'healtzzs', title: '体重指数', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重指数' } } },\r\n        { field: 'healptt', title: '血糖', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入血糖' } } },\r\n        { field: 'healssy', title: '收缩压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入收缩压' } } },\r\n        { field: 'healszy', title: '舒张压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入舒张压' } } },\r\n        { field: 'healzdgc', title: '总胆固醇', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入总胆固醇' } } },\r\n        { field: 'healgysz', title: '甘油三酯', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入甘油三酯' } } },\r\n        { field: 'healga', title: '谷氨酰转肽酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷氨酰转肽酶' } } },\r\n        { field: 'healgb', title: '谷丙转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷丙转氨酶' } } },\r\n        { field: 'healgc', title: '谷草转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷草转氨酶' } } },\r\n        { field: 'healnsd', title: '尿素氮', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入尿素氮' } } },\r\n        { field: 'healjg', title: '肌酐', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入肌酐' } } },\r\n        { field: 'healxd', title: '心电图', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入心电图' } } },\r\n        { field: 'healxj', title: '小结', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入小结', rows: 2 } } },\r\n        { field: 'healjy', title: '建议', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入建议', rows: 2 } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 12,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '正常', value: 1 },\r\n              { label: '删除', value: 101 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 附件\r\n      fileDialogVisible: false,\r\n      fileList: [],\r\n      uploadUrl: process.env.VUE_APP_BASE_API + '/web/supply/userfile/upload', // 新的 SFTP 上传接口\r\n      currentUserId: null,\r\n      currentUserInfo: {}, // 新增：保存当前用户信息\r\n      // 上传配置\r\n      upload: {\r\n        headers: { Authorization: 'Bearer ' + getToken() }\r\n      },\r\n      // 新增/编辑主表\r\n      dialogVisible: false,\r\n      dialogTitle: '',\r\n      form: {},\r\n      importDialogVisible: false,\r\n      importResultDialogVisible: false,\r\n      importLoading: false,\r\n      importFile: null,\r\n      importResult: {\r\n        successCount: 0,\r\n        partialSuccessCount: 0,\r\n        failCount: 0,\r\n        details: []\r\n      },\r\n      // 供应商信息弹窗\r\n      supplyInfoDialogVisible: false,\r\n      supplyInfoData: null\r\n    }\r\n  },\r\n  computed: {\r\n    uploadData() {\r\n      return {\r\n        userid: this.currentUserId,\r\n        usercode: this.currentUserInfo.userCode,\r\n        username: this.currentUserInfo.userName,\r\n        supplycode: this.currentUserInfo.supplyCode,\r\n        supplyname: this.currentUserInfo.supplyName,\r\n        idcard: this.currentUserInfo.idcard,\r\n        userdeptname: this.currentUserInfo.userDeptName\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 查询用户列表\r\n    handleQuery() {\r\n      listInfo(this.queryParams).then(res => {\r\n        this.userList = res.rows\r\n        this.total = res.total\r\n      })\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.supplyCode = ''\r\n      this.handleQuery()\r\n    },\r\n    // 新增\r\n    handleAdd() {\r\n      this.dialogTitle = '新增相关方人员'\r\n      this.form = {}\r\n      this.dialogVisible = true\r\n    },\r\n    // 编辑\r\n    handleEdit(row) {\r\n      this.dialogTitle = '编辑相关方人员'\r\n      this.form = Object.assign({}, row)\r\n      this.dialogVisible = true\r\n    },\r\n    // 删除\r\n    handleDelete(row) {\r\n      this.$confirm('确定删除该条数据吗？', '提示', { type: 'warning' }).then(() => {\r\n        delInfo(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.handleQuery()\r\n        })\r\n      })\r\n    },\r\n    // 提交主表\r\n    submitForm() {\r\n      if (this.form.id) {\r\n        updateInfo(this.form).then(() => {\r\n          this.$message.success('修改成功')\r\n          this.dialogVisible = false\r\n          this.handleQuery()\r\n        })\r\n      } else {\r\n        addInfo(this.form).then(() => {\r\n          this.$message.success('新增成功')\r\n          this.dialogVisible = false\r\n          this.handleQuery()\r\n        })\r\n      }\r\n    },\r\n    // 导出\r\n    handleExport() {\r\n      exportInfo(this.queryParams).then(res => {\r\n        const blob = new Blob([res], { type: 'application/vnd.ms-excel' })\r\n        const url = window.URL.createObjectURL(blob)\r\n        const link = document.createElement('a')\r\n        link.style.display = 'none'\r\n        link.href = url\r\n        link.setAttribute('download', '相关方人员数据.xlsx')\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        window.URL.revokeObjectURL(url)\r\n      })\r\n    },\r\n    // 导入\r\n    handleImport() {\r\n      this.importDialogVisible = true\r\n      this.importFile = null\r\n      this.$nextTick(() => {\r\n        this.$refs.importUpload.clearFiles()\r\n      })\r\n    },\r\n    // 下载导入模板\r\n    handleDownloadTemplate() {\r\n      downloadTemplate().then(response => {\r\n        this.download(response.msg, \"供应商用户信息导入模板.xlsx\")\r\n      }).catch(() => {\r\n        this.$message.error('模板下载失败')\r\n      })\r\n    },\r\n    // 文件选择变化\r\n    handleFileChange(file) {\r\n      this.importFile = file.raw\r\n    },\r\n    // 提交导入\r\n    handleImportSubmit() {\r\n      if (!this.importFile) {\r\n        this.$message.error('请选择要导入的文件')\r\n        return\r\n      }\r\n\r\n      const formData = new FormData()\r\n      formData.append('file', this.importFile)\r\n\r\n      this.importLoading = true\r\n      importInfo(formData).then(response => {\r\n        this.importLoading = false\r\n        this.importDialogVisible = false\r\n\r\n        if (response.code === 200) {\r\n          // 处理导入结果\r\n          if (response.data && Array.isArray(response.data)) {\r\n            // 有详细结果数据，显示结果弹窗\r\n            this.processImportResult(response.data)\r\n            this.importResultDialogVisible = true\r\n          } else {\r\n            // 全部成功，只显示消息\r\n            this.$message.success(response.msg || '导入成功')\r\n          }\r\n          this.handleQuery()\r\n        } else {\r\n          this.$message.error(response.msg || '导入失败')\r\n        }\r\n      }).catch(error => {\r\n        this.importLoading = false\r\n        this.$message.error('导入失败: ' + (error.message || '未知错误'))\r\n      })\r\n    },\r\n    // 处理导入结果\r\n    processImportResult(resultList) {\r\n      this.importResult.successCount = resultList.filter(item => item.result === '成功').length\r\n      this.importResult.partialSuccessCount = resultList.filter(item => item.result === '部分成功').length\r\n      this.importResult.failCount = resultList.filter(item => item.result === '失败').length\r\n      this.importResult.details = resultList.filter(item => item.result !== '成功')\r\n    },\r\n    // 查询供应商信息\r\n    handleQuerySupplyInfo() {\r\n      listSupplyInfo().then(response => {\r\n        console.log(response)\r\n        if (response.code === 200 && response.data) {\r\n          this.supplyInfoData = response.data\r\n          this.supplyInfoDialogVisible = true\r\n        } else {\r\n          this.supplyInfoData = null\r\n          this.supplyInfoDialogVisible = true\r\n          this.$message.warning('未找到该供应商信息')\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('查询供应商信息失败: ' + (error.message || '未知错误'))\r\n      })\r\n      // if (!this.queryParams.supplyCode) {\r\n      //   this.$message.warning('请先输入供应商代码')\r\n      //   return\r\n      // }\r\n\r\n      // getSupplyInfoByCode(this.queryParams.supplyCode).then(response => {\r\n      //   if (response.code === 200 && response.data) {\r\n      //     this.supplyInfoData = response.data\r\n      //     this.supplyInfoDialogVisible = true\r\n      //   } else {\r\n      //     this.supplyInfoData = null\r\n      //     this.supplyInfoDialogVisible = true\r\n      //     this.$message.warning('未找到该供应商信息')\r\n      //   }\r\n      // }).catch(error => {\r\n      //   this.$message.error('查询供应商信息失败: ' + (error.message || '未知错误'))\r\n      // })\r\n    },\r\n    // 附件上传前检查文件类型\r\n    beforeFileUpload(file) {\r\n      // 检查文件类型是否为PDF\r\n      const isPDF = file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')\r\n      if (!isPDF) {\r\n        this.$message.error('只能上传PDF格式文件！')\r\n        return false\r\n      }\r\n      \r\n      // 检查文件大小限制\r\n      const maxSize = 50 * 1024 * 1024 // 50MB\r\n      if (file.size > maxSize) {\r\n        this.$message.error('文件大小不能超过50MB！')\r\n        return false\r\n      }\r\n      \r\n      return true\r\n    },\r\n    // 岗位识别卡\r\n    openFacDialog(row) {\r\n      getFac(row.id).then(res => {\r\n        this.facForm = res.data || { userId: row.id }\r\n        this.facDialogVisible = true\r\n      })\r\n    },\r\n    submitFac() {\r\n      const api = this.facForm.id ? updateFac : addFac\r\n      api(this.facForm).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.facDialogVisible = false\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    // 健康信息\r\n    openHealthDialog(row) {\r\n      getHealth(row.id).then(res => {\r\n        this.healthForm = res.data || { userid: row.id }\r\n        this.healthDialogVisible = true\r\n      })\r\n    },\r\n    submitHealth() {\r\n      const api = this.healthForm.id ? updateHealth : addHealth\r\n      api(this.healthForm).then(() => {\r\n        this.$message.success('保存成功')\r\n        this.healthDialogVisible = false\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    // 附件管理\r\n    openFileDialog(row) {\r\n      this.currentUserId = row.id\r\n      this.currentUserInfo = row // 保存当前用户信息\r\n      this.getFileList(row.id)\r\n      this.fileDialogVisible = true\r\n    },\r\n    getFileList(userid) {\r\n      listFile({ userid }).then(res => {\r\n        this.fileList = res.rows\r\n      })\r\n    },\r\n    handleFileUploadSuccess(response) {\r\n      if (response.code === 200) {\r\n        this.$message.success('文件上传成功')\r\n        this.getFileList(this.currentUserId)\r\n      } else {\r\n        this.$message.error(response.msg || '文件上传失败')\r\n      }\r\n    },\r\n    handleFileUploadError(err) {\r\n      this.$message.error('文件上传失败: ' + (err.message || '未知错误'))\r\n    },\r\n    deleteFile(row) {\r\n      this.$confirm('确定删除该附件吗？', '提示', { type: 'warning' }).then(() => {\r\n        delFile(row.id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getFileList(this.currentUserId)\r\n        })\r\n      })\r\n    },\r\n    downloadFile(row) {\r\n      // 调用下载接口获取文件URL\r\n      request.get(`/web/supply/userfile/download/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          // 获取到文件URL后，在新窗口中打开下载\r\n          const fileUrl = response.data\r\n          window.open(fileUrl, '_blank')\r\n        } else {\r\n          this.$message.error(response.msg || '下载失败')\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('下载失败: ' + error.message)\r\n      })\r\n    },\r\n    tableRowClassName({ row, rowIndex }) {\r\n      if (row.state === 1) {\r\n        return 'success-row'\r\n      } else {\r\n        return 'danger-row'\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handleQuery()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n\r\n.upload-section {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  padding: 25px;\r\n  border-radius: 8px;\r\n  margin-bottom: 25px;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.upload-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  color: #fff;\r\n}\r\n\r\n.upload-header i {\r\n  font-size: 20px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.upload-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.upload-content {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 180px;\r\n  border: 2px dashed rgba(255, 255, 255, 0.3);\r\n  border-radius: 8px;\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.upload-content:hover {\r\n  border-color: rgba(255, 255, 255, 0.6);\r\n  background-color: rgba(255, 255, 255, 0.15);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.upload-dragger {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 48px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.upload-text {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.upload-main-text {\r\n  color: rgba(255, 255, 255, 0.9);\r\n  font-size: 16px;\r\n}\r\n\r\n.upload-click-text {\r\n  color: #fff;\r\n  font-style: normal;\r\n  font-weight: 600;\r\n  text-decoration: underline;\r\n  cursor: pointer;\r\n}\r\n\r\n.upload-tip {\r\n  display: flex;\r\n  align-items: center;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 14px;\r\n}\r\n\r\n.upload-tip i {\r\n  margin-right: 5px;\r\n  font-size: 12px;\r\n}\r\n\r\n.upload-limits {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  width: 100%;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 13px;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.limit-item {\r\n  display: flex;\r\n  align-items: center;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  padding: 8px 12px;\r\n  border-radius: 20px;\r\n  backdrop-filter: blur(5px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.limit-item:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.limit-item i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.file-list-section {\r\n  background-color: #f5f7fa;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.file-list-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  color: #606266;\r\n}\r\n\r\n.file-list-title {\r\n  margin-left: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-count {\r\n  margin-left: 10px;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.file-list-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  padding: 10px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-name {\r\n  margin-left: 8px;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  max-width: 150px;\r\n}\r\n\r\n.el-table .success-row {\r\n  background-color: #f0f9eb;\r\n}\r\n\r\n.el-table .danger-row {\r\n  background-color: #fef0f0;\r\n}\r\n\r\n.operation-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.operation-buttons .el-button {\r\n  margin: 0;\r\n}\r\n</style>\r\n"]}]}