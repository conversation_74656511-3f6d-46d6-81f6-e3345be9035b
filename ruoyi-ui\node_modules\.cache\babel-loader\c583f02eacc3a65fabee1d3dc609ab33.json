{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dgcb\\supplier\\addSupplyInfo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dgcb\\supplier\\addSupplyInfo\\index.vue", "mtime": 1756456282563}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_supply", "require", "_driver", "_notify", "_MoreUploadImage", "_interopRequireDefault", "_default", "exports", "default", "components", "UploadImage", "name", "props", "submitCancel", "type", "Function", "data", "searchDriverQuery", "filteredDriverOptions", "searchCarQuery", "filteredCarOptions", "itemHeaderStyle", "driverList", "carList", "openIndex", "loading", "title", "open", "contractSelection", "contractList", "selectData", "contractData", "showContractData", "delItemData", "form", "carNum", "supplyTime", "measureFlag", "status", "tdgcb05List", "contractSearchParam", "contractNo", "rules", "required", "message", "trigger", "responseData", "contractName", "validTime", "tdgcb03List", "showDialog", "isAgreeEnabled", "countDown", "timer", "computed", "displayDriverListOptions", "slice", "displayCarListOptions", "created", "_this", "console", "log", "getList", "getDriverList", "getCarList", "param", "businessType", "isNotify", "then", "response", "catch", "error", "setInterval", "clearInterval", "<PERSON><PERSON><PERSON><PERSON>", "methods", "filterDriverData", "query", "filter", "item", "driverInfo", "includes", "filterCarData", "carNumber", "onAgreeClick", "addNotify", "openNewDriverWindow", "newWindowUrl", "window", "openNewCarWindow", "_this2", "listContract", "handleResponseData", "_this3", "getXctgDriverUserList", "_this4", "getXctgDriverCarList", "handleDriverChange", "_this5", "driverId", "for<PERSON>ach", "id", "idCard", "company", "phone", "photo", "faceImgList", "driverLicenseImgs", "vehicleLicenseImgs", "handleCarChange", "_this6", "carId", "vehicleEmissionStandards", "sexFormat", "sex", "vehicleEmissionStandardsFormat", "_this7", "contractInfo", "contractObj", "currentPage", "pageSize", "total", "length", "isOpen", "itemSearchParam", "itemNo", "itemName", "itemInfo", "itemObj", "tdgcb01", "itemSpec", "measureUnit", "factoryDesc", "productionLineDesc", "itemList", "push", "searchItemList", "concat", "showTableList", "handleDifferentData", "_this8", "contract", "c", "i", "amount", "supplyWeight", "itemDataChangeFlag", "itemRef", "handleAddContract", "_this9", "toggleSelection", "measureChange", "e", "_this0", "that", "$confirm", "confirmButtonText", "cancelButtonText", "action", "cancel", "submitContract", "JSON", "parse", "stringify", "handleEmptyContract", "openItemSelection", "closeItemSelection", "handleItemOpenClose", "itemIsSelectable", "row", "index", "submitSupply", "_this1", "msgError", "$refs", "validate", "valid", "handleSubmitData", "addBatch", "code", "msgSuccess", "paramList", "v", "f", "handleSelectionChange", "no", "$set", "flag", "_this10", "rows", "$nextTick", "toggleRowSelection", "splice", "indexOf", "clearSelection", "handleCurrentChange", "handleContractSearch", "_this11", "newlist", "resetContractSearch", "resetForm", "handleItemSearch", "newItemList", "resetItemSearch", "resetFields", "getRowKeys", "handleItemDel", "_this12", "Object", "keys"], "sources": ["src/views/dgcb/supplier/addSupplyInfo/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-alert title=\"提示：若无法找到适合的物资编码，请联系业务部门人员进行物资新增，并绑定合同！\" type=\"success\" effect=\"dark\"></el-alert>\r\n    <br>\r\n    <el-alert title=\"提示：供货清单仅允许选择“X”结尾代表新件的物资编码，若需配送返修件，请前往返修单页面申请\" type=\"success\" effect=\"dark\">\r\n    </el-alert>\r\n    <br>\r\n    <!-- <div class=\"transition-box\">\r\n\r\n        <div>\r\n            <strong>提示：若无法找到适合的物资编码，请联系业务部门人员进行物资新增，并绑定合同！<br>\r\n                提示：供货清单仅允许选择“X”结尾代表新件的物资编码，若需配送返修件，请前往返修单页面申请<br>\r\n                提示：尊敬的供应商们，根据管理需要，特此通知：自3月11日0点至3月17日24点期间，请注意对计量类物资不进行过磅操作。请将送货单交至物管处后前往分厂卸货。从3月18日0点开始，将恢复原有送货流程。请您通知相关司机人员遵守上述安排。感谢您的配合与理解！\r\n            </strong>\r\n        </div>\r\n\r\n    </div> -->\r\n\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n      <div style=\"width:100%\">\r\n        <!-- <el-form-item label=\"运输车牌号：\" prop=\"carNum\">\r\n            <el-input style=\"width:220px\" v-model=\"form.carNum\" clearable></el-input>\r\n        </el-form-item> -->\r\n\r\n        <el-form-item label=\"货车司机\" prop=\"driverId\" :rules=\"[{ required: true, message: '司机信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"form.driverId\" filterable :filter-method=\"filterDriverData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleDriverChange\">\r\n            <el-option v-for=\"item in filteredDriverOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.driverInfo\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewDriverWindow\">前往新增或修改司机信息\r\n          </el-button>\r\n        </el-form-item>\r\n        <el-form-item class=\"custom-form-label\" label=\"提醒：\">\r\n          <span style=\"color:#0052cc;\">应海关高认要求，入厂货车必须提供货车司机具体信息</span>\r\n        </el-form-item>\r\n\r\n\r\n        <el-form-item label=\"司机名称\" prop=\"name\" v-if=\"form.name != null\">\r\n          <el-input v-model=\"form.name\" placeholder=\"请输入司机名称\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\" v-if=\"form.phone != null\">\r\n          <el-input v-model=\"form.phone\" placeholder=\"请输入手机号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idCard\" v-if=\"form.idCard != null\">\r\n          <el-input v-model=\"form.idCard\" placeholder=\"请输入身份证号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"人脸照片\" prop=\"faceImg\" v-if=\"form.photo != null && form.photo != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.faceImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n                      lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"form.photo\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"驾驶证照片\" prop=\"driverLicenseImgs\" v-if=\"form.driverLicenseImgs != null && form.driverLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.drivingLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"form.driverLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"行驶证照片\" prop=\"vehicleLicenseImgs\" v-if=\"form.vehicleLicenseImgs != null && form.vehicleLicenseImgs != ''\">\r\n          <div class=\"image-grid\">\r\n            <!-- <el-image v-for=\"(image, index) in form.driverLicenseImgList\" :key=\"index\" :src=\"image\" fit=\"cover\"\r\n              lazy></el-image> -->\r\n            <el-image style=\"width: 200px; height: 200px\" :src=\"form.vehicleLicenseImgs\" fit=\"cover\"></el-image>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"货车\" prop=\"carId\" :rules=\"[{ required: true, message: '货车信息不能为空' }]\">\r\n          <el-select style=\"width:300px\" v-model=\"form.carId\" filterable :filter-method=\"filterCarData\"\r\n            placeholder=\"请选择（如果显示不出请在输入框搜索）\" @change=\"handleCarChange\">\r\n            <el-option v-for=\"item in filteredCarOptions.slice(0, 50)\" :key=\"item.id\" :label=\"item.carNumber\"\r\n              :value=\"item.id\">\r\n            </el-option>\r\n          </el-select>\r\n\r\n          <el-button style=\"margin-left: 10px; font-size: 14px; padding: 5px 10px;\" type=\"primary\"\r\n            @click=\"openNewCarWindow\">前往新增或修改货车信息\r\n          </el-button>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"车牌号\" prop=\"carNum\" v-if=\"form.carNumber != null\">\r\n          <el-input v-model=\"form.carNumber\" placeholder=\"请输入车牌号\" disabled style=\"width:300px\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"车辆排放标准\" prop=\"vehicleEmissionStandards\" v-if=\"form.vehicleEmissionStandards != null\">\r\n          <!-- <el-input v-model=\"form.vehicleEmissionStandards\" placeholder=\"请选择车辆排放标准\" disabled>\r\n          </el-input> -->\r\n\r\n\r\n          <el-select v-model=\"form.vehicleEmissionStandards\" placeholder=\"请选择车辆排放标准\" disabled style=\"width:300px\">\r\n            <el-option v-for=\"dict in vehicleEmissionStandardsOptions\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n\r\n        <el-form-item label=\"供货时间：\" prop=\"supplyTime\">\r\n          <el-date-picker value-format=\"yyyy-MM-dd\" v-model=\"form.supplyTime\" type=\"date\" placeholder=\"选择日期\"\r\n            style=\"width:300px\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n        <el-form-item class=\"custom-form-label\" label=\"提醒：\">\r\n          <span style=\"color:#0052cc;\">供货时间开始三天内，货车准许入厂，请认真填写供货时间</span>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"是否计量：\" prop=\"measureFlag\">\r\n          <el-radio-group @input=\"measureChange\" v-model=\"form.measureFlag\">\r\n            <el-radio :label=\"1\">是</el-radio>\r\n            <el-radio :label=\"0\">否</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"物资信息：\">\r\n          <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAddContract\">选择物资\r\n              </el-button>\r\n            </el-col>\r\n          </el-row>\r\n          <el-card class=\"box-card contractCard\" v-for=\"(contract, index) in contractList\" :key=\"index\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>合同编号：{{ contract.contractNo }}</span>\r\n            </div>\r\n            <!-- <div v-for=\"item in contract.itemList\" :key=\"item.itemNo\" class=\"text item\">\r\n                    <el-row>\r\n                        <el-col>\r\n                            <div style=\"padding: 6px 0;\">\r\n                                {{ `${item.itemName}（编号：${item.itemNo}，规格：${item.itemSpec})` }}\r\n                                <el-button icon=\"el-icon-delete\" circle type=\"danger\" size=\"mini\" @click=\"handleItemDel(contract,item)\"></el-button>\r\n                            </div>\r\n                        </el-col>\r\n                        <el-col>\r\n                            <el-form ref=\"itemform\" :model=\"item\">\r\n                                <el-form-item v-if=\"!form.measureFlag\" :rules=\"[{ required: true, message: '数量不能为空'}]\"\r\n                                label=\"数量：\" label-width=\"100px\" prop=\"amount\">\r\n                                    <el-input v-model.number=\"item.amount\" style=\"width:220px\"\r\n                    type=\"number\" clearable></el-input> {{ item.measureUnit }}\r\n                                </el-form-item>\r\n                                <el-form-item v-if=\"form.measureFlag\" :rules=\"[{ required: true, message: '数量不能为空'}]\"\r\n                                label=\"数量：\" label-width=\"100px\" prop=\"amount\">\r\n                                    <el-input v-model.number=\"item.amount\" style=\"width:220px\"\r\n                    type=\"number\" clearable></el-input> 件\r\n                                </el-form-item>\r\n                                <el-form-item v-if=\"form.measureFlag\" style=\"margin-top: 10px;\" :rules=\"[{ required: true, message: '数量不能为空'}]\"\r\n                                label=\"净重：\" label-width=\"100px\" prop=\"amount\">\r\n                                    <el-input v-model.number=\"item.supplyWeight\" style=\"width:220px\"\r\n                    type=\"number\" clearable></el-input> {{ item.measureUnit }}\r\n                                </el-form-item>\r\n                            </el-form>\r\n                        </el-col>\r\n                    </el-row>\r\n                </div> -->\r\n            <el-table :data=\"contract.itemList\" :header-cell-style=\"itemHeaderStyle\" style=\"width: 100%\">\r\n              <el-table-column prop=\"itemNo\" label=\"物资编码\" min-width=\"150\">\r\n              </el-table-column>\r\n              <el-table-column prop=\"itemName\" label=\"物资名称\" min-width=\"200\">\r\n              </el-table-column>\r\n              <el-table-column label=\"规格\" min-width=\"150\">\r\n                <template slot-scope=\"scopes\">\r\n                  {{\r\n                    scopes.row.itemSpec == null ?\r\n                      `无(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})` :\r\n                      `${scopes.row.itemSpec}(分厂:${scopes.row.factoryDesc}，产线:${scopes.row.productionLineDesc})`\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <!-- 非计量 -->\r\n              <el-table-column v-if=\"!form.measureFlag\" label=\"数量\" min-width=\"150\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-form ref=\"itemform\" :model=\"scope.row\" inline-message>\r\n                      <el-form-item :rules=\"[{ required: true, message: '数量不能为空' }]\" prop=\"amount\">\r\n                        <el-input v-model.number=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <!-- 计量 -->\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"数量\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-form ref=\"itemform\" :model=\"scope.row\" inline-message>\r\n                      <el-form-item :rules=\"[{ required: true, message: '数量不能为空' }]\" prop=\"amount\">\r\n                        <el-input v-model.number=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">件</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"净重\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-form ref=\"itemform\" :model=\"scope.row\" inline-message>\r\n                      <el-form-item :rules=\"[{ required: true, message: '净重不能为空' }]\" prop=\"supplyWeight\">\r\n                        <el-input v-model.number=\"scope.row.supplyWeight\" placeholder=\"请输入重量\" type=\"number\" />\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" size=\"small\" @click=\"handleItemDel(contract, scope.row.itemNo)\">删除\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </el-card>\r\n        </el-form-item>\r\n        <!-- <el-button v-if=\"contractList.length > 0\" type=\"success\" icon=\"el-icon-check\" size=\"mini\" @click=\"submitSupply\">提交清单</el-button> -->\r\n      </div>\r\n    </el-form>\r\n\r\n    <!-- 添加或修改对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body fullscreen>\r\n      <div>\r\n        <el-form :model=\"contractSearchParam\" ref=\"contractSearchForm\" :inline=\"true\" label-width=\"68px\">\r\n          <el-form-item label=\"合同编号\" prop=\"contractNo\">\r\n            <el-input v-model=\"contractSearchParam.contractNo\" placeholder=\"请输入合同编号\" clearable size=\"small\"\r\n              @keyup.enter.native=\"handleContractSearch\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleContractSearch\">搜索\r\n            </el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetContractSearch\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <div style=\"height:634px\" class=\"dialog-box\">\r\n        <el-empty v-if=\"showContractData.length == 0\" description=\"无数据\"></el-empty>\r\n        <div v-else class=\"mybox-card\" v-for=\"(contract, index) in showContractData\" :key=\"index\">\r\n          <div class=\"mybox-header\" @click=\"handleItemOpenClose(contract)\">\r\n            <div class=\"clearfix\">\r\n              <span style=\"font-size:16px;margin-right: 20px;\">合同名称：{{ contract.contractName }}（编号：{{\r\n                contract.contractNo\r\n              }}）</span>\r\n              <!-- <el-tag v-if=\"contract.status == 1\" type=\"success\">可用</el-tag>\r\n              <el-tag v-else type=\"danger\">禁用</el-tag> -->\r\n              <i v-if=\"contract.isOpen\" style=\"float: right; padding: 3px 0\" class=\"el-icon-arrow-up\"></i>\r\n              <i v-else class=\"el-icon-arrow-down\" style=\"float: right; padding: 3px 0\"></i>\r\n              <!-- <el-button  v-if=\"contract.isOpen\" style=\"float: right; padding: 3px 0\" type=\"text\"  @click=\"closeItemSelection(contract)\">收起</el-button>\r\n              <el-button v-else style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"openItemSelection(contract)\">展开</el-button> -->\r\n            </div>\r\n          </div>\r\n          <div class=\"mybox-body\" v-if=\"contract.isOpen\">\r\n            <el-form :model=\"contract.itemSearchParam\" :ref=\"itemRef(contract)\" :inline=\"true\" label-width=\"68px\">\r\n              <el-form-item label=\"物资编号\" prop=\"itemNo\">\r\n                <el-input v-model=\"contract.itemSearchParam.itemNo\" placeholder=\"请输入物资编号\" clearable size=\"small\" />\r\n              </el-form-item>\r\n              <el-form-item label=\"物资名称\" prop=\"itemName\">\r\n                <el-input v-model=\"contract.itemSearchParam.itemName\" placeholder=\"请输入物资名称\" clearable size=\"small\" />\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleItemSearch(contract)\">搜索\r\n                </el-button>\r\n                <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetItemSearch(contract)\">重置\r\n                </el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n            <el-table :ref=\"contract.contractNo\" :data=\"contract.showTableList\" :header-cell-style=\"itemHeaderStyle\"\r\n              style=\"width: 100%\" :row-key=\"getRowKeys\" @selection-change=\"handleSelectionChange($event, contract)\">\r\n              <el-table-column type=\"selection\" width=\"55\" :selectable=\"itemIsSelectable\" :reserve-selection=\"true\">\r\n              </el-table-column>\r\n              <el-table-column prop=\"itemNo\" label=\"物资编码\" min-width=\"150\">\r\n              </el-table-column>\r\n              <el-table-column prop=\"itemName\" label=\"物资名称\" min-width=\"200\">\r\n              </el-table-column>\r\n              <el-table-column label=\"规格\" min-width=\"150\">\r\n\r\n                <template slot-scope=\"scopes\">\r\n                  {{\r\n                    scopes.row.itemSpec == null ?\r\n                      `无(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})` :\r\n                      `${scopes.row.itemSpec}(分厂:${scopes.row.factoryDesc},产线:${scopes.row.productionLineDesc})`\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <!-- <el-table-column\r\n              label=\"状态\">\r\n              <template slot-scope=\"props\">\r\n                  <el-tag v-if=\"props.row.status == 1\" type=\"success\">可用</el-tag>\r\n                  <el-tag v-else type=\"danger\">禁用</el-tag>\r\n              </template>\r\n          </el-table-column> -->\r\n              <!-- 非计量 -->\r\n              <el-table-column v-if=\"!form.measureFlag\" label=\"数量\" min-width=\"150\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-input v-model=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <!-- 计量 -->\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"数量\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-input v-model=\"scope.row.amount\" placeholder=\"请输入数量\" type=\"number\" />\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">件</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column v-if=\"form.measureFlag\" label=\"净重\" min-width=\"100\">\r\n\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;\">\r\n                    <el-input v-model=\"scope.row.supplyWeight\" placeholder=\"请输入重量\" type=\"number\" />\r\n                    <span style=\"line-height: 36px;padding-left: 5px;\">{{\r\n                      scope.row.measureUnit\r\n                    }}</span>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <el-pagination background small @current-change=\"handleCurrentChange($event, contract, index)\"\r\n              :current-page.sync=\"contract.currentPage\" :page-size=\"contract.pageSize\" layout=\"total,prev, pager, next\"\r\n              :total=\"contract.total\">\r\n            </el-pagination>\r\n            <!-- <el-checkbox-group v-model=\"contract.checkboxGroup\" size=\"small\"> -->\r\n            <!-- <el-checkbox v-for=\"item in contract.itemList\" :key=\"item.itemNo\" :label=\"item.itemName\" @change=\"checkChange($event,item)\" border></el-checkbox> -->\r\n            <!-- </el-checkbox-group> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitContract\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"用户协议\" :visible.sync=\"showDialog\" width=\"60%\" :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\" center :modal=\"false\">\r\n      <div class=\"dialog-body\">\r\n        <p>尊敬的合作伙伴：</p>\r\n        <p>您好！衷心感谢您一直以来对我司的支持，为了强化生产现场管理、保障我司的信息安全，特将有关规定告知如下：</p>\r\n        <ol>\r\n          <li>1、请您在进入我司区域后，包括办公区域、生产区域、研发区域、厂区道路等，<span class=\"highlight\">未经允许，不随意拍照或录像。</span></li>\r\n          <li>2、请您妥善保管工作照或录像，<span class=\"highlight\">不将其随意转发</span>任何无关人员，<span\r\n              class=\"highlight\">不擅自剪辑、传播、上传、发布任何平台。</span>\r\n          </li>\r\n          <li>3、请您在我司许可的指定工作区域内活动，<span class=\"highlight\">不随意走动，全力保护我司的各类信息安全，遵守我司的各项管理规定。</span></li>\r\n          <li>4、请对您公司<span class=\"highlight\">所有派出</span>进入我司区域工作的<span class=\"highlight\">人员进行宣贯，知晓并遵守</span>以上三项规定。</li>\r\n        </ol>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button :disabled=\"!isAgreeEnabled\" @click=\"onAgreeClick\">本人已阅知，并承诺遵守{{ countDown ? '(' + countDown + ')' :\r\n          ''\r\n        }}</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n\r\n\r\n\r\n</template>\r\n\r\n<style scoped>\r\n.mybox-card {\r\n  border-radius: 4px;\r\n  border: 1px solid #ebeef5;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  color: #303133;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);\r\n  margin-bottom: 10px\r\n}\r\n\r\n.mybox-header {\r\n  padding: 18px 20px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.mybox-body {\r\n  border-top: 1px solid #ebeef5;\r\n  /* padding: 20px; */\r\n}\r\n\r\n.el-dialog__body {\r\n  padding: 10px 30px;\r\n}\r\n\r\n.contractCard {\r\n  margin-bottom: 10px\r\n}\r\n\r\n.dialog-box {\r\n  overflow: auto;\r\n}\r\n\r\n.dialog-box::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: center\r\n}\r\n\r\n.transition-box {\r\n  grid-auto-columns: 10px;\r\n  width: auto;\r\n  height: auto;\r\n  border-radius: 4px;\r\n  background-color: #CCCCCC;\r\n  text-align: center;\r\n  color: #DC1437;\r\n  padding: 20px 20px;\r\n  -webkit-box-sizing: border-box;\r\n  box-sizing: border-box;\r\n  margin-right: 20px;\r\n  text-align: left;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.image-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\r\n  gap: 10px;\r\n}\r\n\r\n.custom-form-label>>>.el-form-item__label {\r\n  color: #0052cc;\r\n}\r\n\r\n\r\n.highlight {\r\n  color: red;\r\n  font-weight: bold;\r\n}\r\n\r\n.dialog-body p {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.dialog-body ol {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.dialog-body ol li {\r\n  margin-bottom: 5px;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport { listContract, addBatch } from \"@/api/dgcb/supplier/supply\";\r\nimport { listAllDriver, getXctgDriverUserList, getXctgDriverCarList } from \"@/api/dgcb/driver/driver\";\r\nimport { isNotify, addNotify } from \"@/api/truck/notify/notify\";\r\nimport UploadImage from '@/components/MoreUploadImage';//引用组件\r\n\r\nexport default {\r\n  components: {\r\n    UploadImage,\r\n  },\r\n  name: \"addSupplyInfo\",\r\n  props: {\r\n    submitCancel: {\r\n      type: Function,\r\n      default: null\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      searchDriverQuery: '',\r\n      filteredDriverOptions: [],\r\n\r\n      searchCarQuery: '',\r\n      filteredCarOptions: [],\r\n\r\n      itemHeaderStyle: {\r\n        \"background-color\": \"#fff\"\r\n      },\r\n      driverList: [],\r\n      carList: [],\r\n      // 物资信息隐藏序列\r\n      openIndex: [],\r\n      // 遮罩层\r\n      loading: true,\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 合同待确认选择项\r\n      contractSelection: [],\r\n      // 已选择合同列表\r\n      contractList: [],\r\n      // 物资多选信息\r\n      selectData: {},\r\n      // 显示物资列表\r\n      // showTableList:[],\r\n      // 源合同数据\r\n      contractData: [],\r\n      // 过滤数据\r\n      showContractData: [],\r\n      // 删除数据\r\n      delItemData: [],\r\n      form: {\r\n        carNum: null,\r\n        supplyTime: null,\r\n        measureFlag: 0,\r\n        status: 1,\r\n        tdgcb05List: []\r\n      },\r\n      // 合同搜索条件\r\n      contractSearchParam: {\r\n        contractNo: \"\"\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        /**\r\n         carNum: [\r\n         {\r\n         required: true,\r\n         message: \"车牌号不能为空\",\r\n         trigger: \"blur\"\r\n         },\r\n         {\r\n         pattern: /^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$/,\r\n         message: \"车牌号格式不正确\"\r\n         }\r\n         ],*/\r\n        supplyTime: [\r\n          {\r\n            required: true,\r\n            message: \"供货时间不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n      },\r\n      // 测试合同数据\r\n      responseData: [{\r\n        contractNo: '1',\r\n        contractName: '合同名称1',\r\n        validTime: '2016-05-02',\r\n        tdgcb03List: []\r\n      }],\r\n      showDialog: false,\r\n      isAgreeEnabled: false,\r\n      countDown: 5,\r\n      timer: null,\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 默认显示前50条，若有搜索，则显示搜索后的数据\r\n    displayDriverListOptions() {\r\n      return this.searchDriverQuery ? this.filteredDriverOptions : this.driverList.slice(0, 50);\r\n    },\r\n    displayCarListOptions() {\r\n      return this.searchCarQuery ? this.filteredCarOptions : this.carList.slice(0, 50);\r\n    }\r\n  },\r\n\r\n  created() {\r\n    console.log('showDialog:', this.showDialog);\r\n    this.getList();\r\n    this.getDriverList();\r\n    this.getCarList();\r\n\r\n    let param = {}\r\n    param.businessType = 1;\r\n    isNotify(param).then(response => {\r\n      if (response.data) { // 假设接口返回的数据为 true 或 false\r\n        this.showDialog = true;\r\n      } else {\r\n        this.showDialog = false;\r\n      }\r\n    }).catch(error => {\r\n      console.error('Failed to call isNotify:', error);\r\n      this.showDialog = false; // 如果接口调用失败，不显示弹框\r\n    });\r\n\r\n\r\n    this.timer = setInterval(() => {\r\n      if (this.countDown > 0) {\r\n        this.countDown--;\r\n      } else {\r\n        this.isAgreeEnabled = true;\r\n        clearInterval(this.timer);\r\n      }\r\n    }, 1000);\r\n  },\r\n  beforeDestroy() {\r\n    clearInterval(this.timer);\r\n  },\r\n  methods: {\r\n    // 搜索过滤逻辑\r\n    filterDriverData(query) {\r\n      this.searchDriverQuery = query;\r\n\r\n      if (this.searchDriverQuery) {\r\n\r\n        this.filteredDriverOptions = this.driverList.filter(item =>\r\n          item.driverInfo.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredDriverOptions = this.driverList.slice(0, 50);\r\n      }\r\n    },\r\n\r\n    // 搜索过滤逻辑\r\n    filterCarData(query) {\r\n      this.searchCarQuery = query;\r\n\r\n      if (this.searchCarQuery) {\r\n\r\n        this.filteredCarOptions = this.carList.filter(item =>\r\n          item.carNumber.includes(query)\r\n        );\r\n      } else {\r\n\r\n        this.filteredCarOptions = this.carList.slice(0, 50);\r\n      }\r\n    },\r\n\r\n    onAgreeClick() {\r\n      if (this.isAgreeEnabled) {\r\n        this.showDialog = false;\r\n        // 在这里可以添加用户同意后的逻辑\r\n        let param = {};\r\n        param.businessType = 1;\r\n        addNotify(param).then(response => {\r\n          // 处理 addNotify 接口成功的逻辑\r\n          console.log('addNotify success:', response);\r\n          // 可以在这里添加其他逻辑，比如提示用户操作成功\r\n        }).catch(error => {\r\n          // 处理 addNotify 接口失败的逻辑\r\n          console.error('addNotify failed:', error);\r\n          // 可以在这里添加其他逻辑，比如提示用户操作失败\r\n        });\r\n      }\r\n    },\r\n    openNewDriverWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverUser'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    openNewCarWindow() {\r\n      const newWindowUrl = 'https://ydxt.citicsteel.com:8099/truckManage/xctgDriverCar'; // 替换为实际要跳转的页面 URL\r\n      window.open(newWindowUrl, '_blank'); // 打开新窗口并跳转至指定 URL\r\n    },\r\n    /** 查询批次列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      let param = \"measureFlagTemp=\" + this.form.measureFlag;\r\n      listContract(param).then(response => {\r\n        let responseData = response.data;\r\n        this.handleResponseData(responseData);\r\n      });\r\n      // this.handleResponseData(this.responseData);\r\n      this.loading = false;\r\n    },\r\n    /** 查询司机信息列表 */\r\n    getDriverList() {\r\n      this.loading = true;\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverUserList().then(response => {\r\n        this.driverList = response.data;\r\n        this.filteredDriverOptions = this.driverList,\r\n          this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 查询司机信息列表 */\r\n    getCarList() {\r\n      console.log(\"success\");\r\n      this.loading = true;\r\n      // listAllDriver().then(response => {\r\n      //   this.driverList = response.data;\r\n      //   this.loading = false;\r\n      // });\r\n      getXctgDriverCarList().then(response => {\r\n        this.carList = response.data;\r\n        this.filteredCarOptions = this.carList;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n\r\n\r\n    handleDriverChange() {\r\n      //通过driverId获取司机信息\r\n      if (this.form.driverId != null) {\r\n        this.driverList.forEach(item => {\r\n          if (item.id == this.form.driverId) {\r\n            this.form.name = item.name;\r\n            this.form.idCard = item.idCard;\r\n            this.form.company = item.company;\r\n            this.form.phone = item.phone;\r\n            this.form.photo = item.photo;\r\n            this.form.faceImgList = item.faceImgList;\r\n            this.form.driverLicenseImgs = item.driverLicenseImgs;\r\n            this.form.vehicleLicenseImgs = item.vehicleLicenseImgs;\r\n\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    handleCarChange() {\r\n      console.log(\"success\");\r\n      //通过driverId获取司机信息\r\n      if (this.form.carId != null) {\r\n        this.carList.forEach(item => {\r\n          if (item.id == this.form.carId) {\r\n            this.form.carNumber = item.carNumber;\r\n            if (item.vehicleEmissionStandards == 1) {\r\n              this.form.vehicleEmissionStandards = \"国五\";\r\n            } else if (item.vehicleEmissionStandards == 2) {\r\n              this.form.vehicleEmissionStandards = \"国六\";\r\n            } else if (item.vehicleEmissionStandards == 3) {\r\n              this.form.vehicleEmissionStandards = \"新能源\";\r\n            } else {\r\n              this.form.vehicleEmissionStandards = \"\";\r\n            }\r\n            // this.form.vehicleEmissionStandards = item.vehicleEmissionStandards;\r\n\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    //性别转换\r\n    sexFormat(sex) {\r\n      if (sex == 0) {\r\n        return \"未知\";\r\n      } else if (sex == 1) {\r\n        return \"男\";\r\n      } else if (sex == 2) {\r\n        return \"女\";\r\n      } else {\r\n        return \"\";\r\n      }\r\n\r\n    },\r\n\r\n    //车辆排放标准转换\r\n    vehicleEmissionStandardsFormat(vehicleEmissionStandards) {\r\n      if (vehicleEmissionStandards == 1) {\r\n        return \"国五\";\r\n      } else if (vehicleEmissionStandards == 2) {\r\n        return \"国六\";\r\n      } else if (vehicleEmissionStandards == 3) {\r\n        return \"新能源\";\r\n      } else {\r\n        return \"\";\r\n      }\r\n    },\r\n\r\n    // 处理请求数据\r\n    handleResponseData(response) {\r\n      this.contractData = [];\r\n      this.showContractData = [];\r\n      response.forEach(data => {\r\n        let contractInfo = this.contractObj();\r\n        contractInfo.contractNo = data.contractNo;\r\n        contractInfo.contractName = data.contractName;\r\n        contractInfo.validTime = data.validTime;\r\n        contractInfo.currentPage = 1;\r\n        contractInfo.pageSize = 10;\r\n        contractInfo.total = data.tdgcb03List.length;\r\n        contractInfo.isOpen = false; // 合同默认收起\r\n        contractInfo.itemSearchParam = { itemNo: \"\", itemName: \"\" };\r\n        if (data.tdgcb03List != null) {\r\n          data.tdgcb03List.forEach(item => {\r\n            let itemInfo = this.itemObj();\r\n            itemInfo.contractNo = data.contractNo;\r\n            itemInfo.contractName = data.contractName;\r\n            itemInfo.itemNo = item.itemNo;\r\n            itemInfo.itemName = item.tdgcb01.itemName;\r\n            itemInfo.itemSpec = item.tdgcb01.itemSpec;\r\n            itemInfo.measureUnit = item.tdgcb01.measureUnit;\r\n            itemInfo.factoryDesc = item.tdgcb01.factoryDesc;\r\n            itemInfo.productionLineDesc = item.tdgcb01.productionLineDesc;\r\n            itemInfo.status = item.status;\r\n            contractInfo.itemList.push(itemInfo);\r\n          })\r\n          contractInfo.searchItemList = contractInfo.itemList.concat();\r\n          contractInfo.showTableList = contractInfo.searchItemList.slice(0, contractInfo.pageSize)\r\n        }\r\n        this.contractData.push(contractInfo);\r\n      })\r\n      this.showContractData = this.contractData.concat();\r\n    },\r\n    // 同步选择信息数据\r\n    handleDifferentData() {\r\n      this.contractData.forEach(contract => {\r\n        this.contractList.forEach(c => {\r\n          console.log()\r\n          if (c.contractNo == contract.contractNo) {\r\n            contract.itemList.forEach(item => {\r\n              c.itemList.forEach(i => {\r\n                if (i.itemNo == item.itemNo) {\r\n                  console.log(\"befor\", item)\r\n                  item.amount = i.amount;\r\n                  item.supplyWeight = i.supplyWeight;\r\n                  console.log(\"after\", item)\r\n                }\r\n              })\r\n            })\r\n          }\r\n        })\r\n      })\r\n      this.itemDataChangeFlag = 0;\r\n    },\r\n\r\n    contractObj() {\r\n      return {\r\n        status: 1,\r\n        contractNo: null,\r\n        contractName: null,\r\n        validTime: null,\r\n        itemList: []\r\n      }\r\n    },\r\n\r\n    itemObj() {\r\n      return {\r\n        contractNo: null,  // 所属合同编号\r\n        contractName: null,   // 所属合同名\r\n        itemNo: null,   // 物资名称\r\n        itemName: null,  // 物资名称\r\n        amount: null,  // 数量\r\n        itemSpec: null,  // 物资规格\r\n        measureFlag: null, // 是否计量\r\n        measureUnit: null,  // 计量单位\r\n        supplyWeight: null //供货重量\r\n      }\r\n    },\r\n\r\n    itemRef(contract) {\r\n      return contract.contractNo + \"itemRef\"\r\n    },\r\n\r\n    /** 新增按钮操作 */\r\n    handleAddContract() {\r\n      // this.reset();\r\n      // this.contractSelection = JSON.parse(JSON.stringify(this.contractList));\r\n      // if(this.contractList.length > 0) this.contractSelection = this.contractList.concat();\r\n      if (this.contractList.length > 0) this.handleDifferentData();\r\n      // 处理已选\r\n      this.showContractData.forEach(c => {\r\n        if (c.isOpen) {\r\n          this.toggleSelection(c);\r\n        }\r\n      })\r\n      this.open = true;\r\n      this.title = \"选择物资\";\r\n    },\r\n\r\n    // 计量改变\r\n    measureChange(e) {\r\n      this.form.measureFlag = e;\r\n      let that = this;\r\n      if (this.contractList.length > 0) {\r\n        // 已选择物资\r\n        this.$confirm('改变是否计量将清除当前已选物资，是否继续\"?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function () {\r\n          // 确定\r\n          console.log(\"确定\")\r\n          that.contractList = [];\r\n          that.selectData = {};\r\n          that.contractSelection = [];\r\n          that.getList();\r\n        }).catch(action => {\r\n          // 取消\r\n          if (action == \"cancel\") {\r\n            console.log(action);\r\n            if (this.form.measureFlag == 0) {\r\n              this.form.measureFlag = 1;\r\n            } else {\r\n              this.form.measureFlag = 0;\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        this.getList();\r\n      }\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      // this.reset();\r\n    },\r\n\r\n    // 选择合同确定\r\n    submitContract() {\r\n      // console.log(this.contractSelection);\r\n      this.contractList = JSON.parse(JSON.stringify(this.contractSelection));\r\n      // this.contractList = this.contractSelection.concat();\r\n      // console.log(this.contractList);\r\n      this.handleEmptyContract();\r\n      this.open = false;\r\n    },\r\n    // 展开合同\r\n    openItemSelection(contract) {\r\n      contract.isOpen = true;\r\n      this.toggleSelection(contract);\r\n    },\r\n    // 收起合同\r\n    closeItemSelection(contract) {\r\n      contract.isOpen = false;\r\n    },\r\n    // 处理合同展开收起\r\n    handleItemOpenClose(contract) {\r\n      if (!contract.status) return;\r\n      if (contract.isOpen) {\r\n        this.closeItemSelection(contract);\r\n      } else {\r\n        this.openItemSelection(contract);\r\n      }\r\n    },\r\n\r\n    // 判断物资是否可选\r\n    itemIsSelectable(row, index) {\r\n      if (row.status) {\r\n        console.log(\"是\");\r\n        return true;\r\n      } else {\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 提交清单\r\n    submitSupply() {\r\n      if (this.contractList.length == 0) {\r\n        this.msgError(\"请添加物资\");\r\n        return;\r\n      }\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.handleSubmitData()) {\r\n            // console.log(this.form);\r\n            if (this.form.measureFlag == 1) this.form.status = 11;\r\n            addBatch(this.form).then(response => {\r\n              // console.log(response);\r\n              if (response.code == 200) {\r\n                this.msgSuccess(\"添加成功\");\r\n                this.submitCancel();\r\n              }\r\n            })\r\n          }\r\n          ;\r\n        }\r\n      })\r\n    },\r\n\r\n    // 处理提交数据\r\n    handleSubmitData() {\r\n      let paramList = [];\r\n      let v = true;\r\n      this.$refs[\"itemform\"].forEach(f => {\r\n        f.validate(valid => {\r\n          if (!valid) {\r\n            v = false\r\n          }\r\n          ; //数量验证不通过\r\n        })\r\n      })\r\n      if (v) {\r\n        this.contractList.forEach((contract, index) => {\r\n          contract.itemList.forEach(item => {\r\n            paramList.push(item);\r\n          })\r\n        })\r\n      }\r\n      this.form.tdgcb05List = paramList;\r\n      return v;\r\n    },\r\n\r\n    // 物资选择改变事件\r\n    handleSelectionChange(e, data) {\r\n      let no = data.contractNo;\r\n      if (!this.selectData[`${no}`]) {\r\n        this.$set(this.selectData, `${no}`, []);\r\n      }\r\n      this.selectData[`${no}`] = e;\r\n      let flag = false; // 表示合同还未添加\r\n      this.contractSelection.forEach(contract => {\r\n        if (contract.contractNo == data.contractNo) {\r\n          flag = true;\r\n          // 已有合同则添加物资信息\r\n          // contract.itemList = this.selectData[`${no}`];\r\n          contract.itemList = e;\r\n        }\r\n      })\r\n      if (!flag) {\r\n        // 合同未添加则新增合同\r\n        let contractInfo = this.contractObj();\r\n        contractInfo.contractNo = data.contractNo;\r\n        contractInfo.contractName = data.contractName;\r\n        contractInfo.validTime = data.validTime;\r\n        contractInfo.measureFlag = data.measureFlag;\r\n        // contractInfo.itemList = this.selectData[`${no}`];\r\n        contractInfo.itemList = e;\r\n        this.contractSelection.push(contractInfo);\r\n      }\r\n    },\r\n\r\n    // 当前页选择\r\n    toggleSelection(contract) {\r\n      const no = contract.contractNo;\r\n      const rows = this.selectData[`${no}`];\r\n      console.log(rows)\r\n      this.$nextTick(() => {\r\n        if (rows) {\r\n          rows.forEach(row => {\r\n            if (this.delItemData.includes(row)) {\r\n              this.$refs[`${no}`][0].toggleRowSelection(row, false);\r\n              row.amount = null;\r\n              row.supplyWeight = null;\r\n              this.delItemData.splice(this.delItemData.indexOf(row), 1);\r\n            } else {\r\n              this.$refs[`${no}`][0].toggleRowSelection(row, true);\r\n            }\r\n          });\r\n        } else {\r\n          this.$refs[`${no}`][0].clearSelection();\r\n        }\r\n      })\r\n      console.log(\"del\", this.delItemData)\r\n    },\r\n\r\n    // 物资列表当前页改变\r\n    handleCurrentChange(currentPage, contract) {\r\n      contract.showTableList = contract.searchItemList.slice(contract.pageSize * (currentPage - 1), contract.pageSize * (currentPage - 1) + contract.pageSize);\r\n    },\r\n    // 合同搜索\r\n    handleContractSearch() {\r\n      let newlist = []\r\n      if (this.contractSearchParam.contractNo == \"\") {\r\n        this.showContractData = this.contractData.concat();\r\n      } else {\r\n        this.contractData.forEach(contract => {\r\n          if (contract.contractNo.includes(this.contractSearchParam.contractNo)) {\r\n            newlist.push(contract);\r\n          }\r\n        })\r\n        this.showContractData = newlist;\r\n      }\r\n      // 处理已选\r\n      this.showContractData.forEach(c => {\r\n        if (c.isOpen) {\r\n          this.toggleSelection(c);\r\n        }\r\n      })\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetContractSearch() {\r\n      this.resetForm(\"contractSearchForm\");\r\n      this.handleContractSearch();\r\n    },\r\n\r\n    //物资搜索\r\n    handleItemSearch(contract) {\r\n      const itemSearchParam = contract.itemSearchParam;\r\n      let newItemList = [];\r\n      if (itemSearchParam.itemName == \"\" && itemSearchParam.itemNo == \"\") {\r\n        newItemList = contract.itemList;\r\n      } else {\r\n        contract.itemList.forEach(item => {\r\n          if (item.itemName.includes(itemSearchParam.itemName) && item.itemNo.includes(itemSearchParam.itemNo)) {\r\n            newItemList.push(item);\r\n          }\r\n        })\r\n      }\r\n      contract.searchItemList = newItemList;\r\n      contract.total = newItemList.length;\r\n      contract.currentPage = 1;\r\n      contract.showTableList = contract.searchItemList.slice(0, contract.pageSize);\r\n      // this.toggleSelection(contract);\r\n    },\r\n    // 物资重置\r\n    resetItemSearch(contract) {\r\n      const itemRef = this.itemRef(contract);\r\n      this.$refs[`${itemRef}`][0].resetFields();\r\n      this.handleItemSearch(contract);\r\n    },\r\n\r\n    //  获取行键\r\n    getRowKeys(row) {\r\n      return row.itemNo;\r\n    },\r\n\r\n    // 物资删除点击事件\r\n    handleItemDel(contract, itemNo) {\r\n      // 物资列表中删除\r\n      this.contractList.forEach(c => {\r\n        if (c.contractNo == contract.contractNo) {\r\n          c.itemList.forEach((i, index) => {\r\n            if (i.itemNo == itemNo) {\r\n              c.itemList.splice(index, 1);\r\n            }\r\n          })\r\n        }\r\n      })\r\n      // 已选项中删除\r\n      Object.keys(this.selectData).forEach(no => {\r\n        if (no == contract.contractNo) {\r\n          this.selectData[`${no}`].forEach(i => {\r\n            if (i.itemNo == itemNo) {\r\n              this.delItemData.push(i);\r\n            }\r\n          })\r\n        }\r\n      })\r\n      this.handleEmptyContract();\r\n    },\r\n    // 处理空合同\r\n    handleEmptyContract() {\r\n      this.contractList = this.contractList.filter(c => c.itemList.length != 0)\r\n    }\r\n  },\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAmdA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAC,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAK,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,UAAA;IACAC,WAAA,EAAAA;EACA;EACAC,IAAA;EACAC,KAAA;IACAC,YAAA;MACAC,IAAA,EAAAC,QAAA;MACAP,OAAA;IACA;EACA;EACAQ,IAAA,WAAAA,KAAA;IACA;MACAC,iBAAA;MACAC,qBAAA;MAEAC,cAAA;MACAC,kBAAA;MAEAC,eAAA;QACA;MACA;MACAC,UAAA;MACAC,OAAA;MACA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,iBAAA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACA;MACA;MACA;MACAC,YAAA;MACA;MACAC,gBAAA;MACA;MACAC,WAAA;MACAC,IAAA;QACAC,MAAA;QACAC,UAAA;QACAC,WAAA;QACAC,MAAA;QACAC,WAAA;MACA;MACA;MACAC,mBAAA;QACAC,UAAA;MACA;MACA;MACAC,KAAA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACAN,UAAA,GACA;UACAO,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACA;MACAC,YAAA;QACAL,UAAA;QACAM,YAAA;QACAC,SAAA;QACAC,WAAA;MACA;MACAC,UAAA;MACAC,cAAA;MACAC,SAAA;MACAC,KAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,wBAAA,WAAAA,yBAAA;MACA,YAAAtC,iBAAA,QAAAC,qBAAA,QAAAI,UAAA,CAAAkC,KAAA;IACA;IACAC,qBAAA,WAAAA,sBAAA;MACA,YAAAtC,cAAA,QAAAC,kBAAA,QAAAG,OAAA,CAAAiC,KAAA;IACA;EACA;EAEAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACAC,OAAA,CAAAC,GAAA,qBAAAX,UAAA;IACA,KAAAY,OAAA;IACA,KAAAC,aAAA;IACA,KAAAC,UAAA;IAEA,IAAAC,KAAA;IACAA,KAAA,CAAAC,YAAA;IACA,IAAAC,gBAAA,EAAAF,KAAA,EAAAG,IAAA,WAAAC,QAAA;MACA,IAAAA,QAAA,CAAArD,IAAA;QAAA;QACA2C,KAAA,CAAAT,UAAA;MACA;QACAS,KAAA,CAAAT,UAAA;MACA;IACA,GAAAoB,KAAA,WAAAC,KAAA;MACAX,OAAA,CAAAW,KAAA,6BAAAA,KAAA;MACAZ,KAAA,CAAAT,UAAA;IACA;IAGA,KAAAG,KAAA,GAAAmB,WAAA;MACA,IAAAb,KAAA,CAAAP,SAAA;QACAO,KAAA,CAAAP,SAAA;MACA;QACAO,KAAA,CAAAR,cAAA;QACAsB,aAAA,CAAAd,KAAA,CAAAN,KAAA;MACA;IACA;EACA;EACAqB,aAAA,WAAAA,cAAA;IACAD,aAAA,MAAApB,KAAA;EACA;EACAsB,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAA5D,iBAAA,GAAA4D,KAAA;MAEA,SAAA5D,iBAAA;QAEA,KAAAC,qBAAA,QAAAI,UAAA,CAAAwD,MAAA,WAAAC,IAAA;UAAA,OACAA,IAAA,CAAAC,UAAA,CAAAC,QAAA,CAAAJ,KAAA;QAAA,CACA;MACA;QAEA,KAAA3D,qBAAA,QAAAI,UAAA,CAAAkC,KAAA;MACA;IACA;IAEA;IACA0B,aAAA,WAAAA,cAAAL,KAAA;MACA,KAAA1D,cAAA,GAAA0D,KAAA;MAEA,SAAA1D,cAAA;QAEA,KAAAC,kBAAA,QAAAG,OAAA,CAAAuD,MAAA,WAAAC,IAAA;UAAA,OACAA,IAAA,CAAAI,SAAA,CAAAF,QAAA,CAAAJ,KAAA;QAAA,CACA;MACA;QAEA,KAAAzD,kBAAA,QAAAG,OAAA,CAAAiC,KAAA;MACA;IACA;IAEA4B,YAAA,WAAAA,aAAA;MACA,SAAAjC,cAAA;QACA,KAAAD,UAAA;QACA;QACA,IAAAe,KAAA;QACAA,KAAA,CAAAC,YAAA;QACA,IAAAmB,iBAAA,EAAApB,KAAA,EAAAG,IAAA,WAAAC,QAAA;UACA;UACAT,OAAA,CAAAC,GAAA,uBAAAQ,QAAA;UACA;QACA,GAAAC,KAAA,WAAAC,KAAA;UACA;UACAX,OAAA,CAAAW,KAAA,sBAAAA,KAAA;UACA;QACA;MACA;IACA;IACAe,mBAAA,WAAAA,oBAAA;MACA,IAAAC,YAAA;MACAC,MAAA,CAAA7D,IAAA,CAAA4D,YAAA;IACA;IACAE,gBAAA,WAAAA,iBAAA;MACA,IAAAF,YAAA;MACAC,MAAA,CAAA7D,IAAA,CAAA4D,YAAA;IACA;IACA,aACAzB,OAAA,WAAAA,QAAA;MAAA,IAAA4B,MAAA;MACA,KAAAjE,OAAA;MACA,IAAAwC,KAAA,6BAAA/B,IAAA,CAAAG,WAAA;MACA,IAAAsD,oBAAA,EAAA1B,KAAA,EAAAG,IAAA,WAAAC,QAAA;QACA,IAAAvB,YAAA,GAAAuB,QAAA,CAAArD,IAAA;QACA0E,MAAA,CAAAE,kBAAA,CAAA9C,YAAA;MACA;MACA;MACA,KAAArB,OAAA;IACA;IACA,eACAsC,aAAA,WAAAA,cAAA;MAAA,IAAA8B,MAAA;MACA,KAAApE,OAAA;MACA;MACA;MACA;MACA;MACA,IAAAqE,6BAAA,IAAA1B,IAAA,WAAAC,QAAA;QACAwB,MAAA,CAAAvE,UAAA,GAAA+C,QAAA,CAAArD,IAAA;QACA6E,MAAA,CAAA3E,qBAAA,GAAA2E,MAAA,CAAAvE,UAAA,EACAuE,MAAA,CAAApE,OAAA;MACA;IACA;IAEA,eACAuC,UAAA,WAAAA,WAAA;MAAA,IAAA+B,MAAA;MACAnC,OAAA,CAAAC,GAAA;MACA,KAAApC,OAAA;MACA;MACA;MACA;MACA;MACA,IAAAuE,4BAAA,IAAA5B,IAAA,WAAAC,QAAA;QACA0B,MAAA,CAAAxE,OAAA,GAAA8C,QAAA,CAAArD,IAAA;QACA+E,MAAA,CAAA3E,kBAAA,GAAA2E,MAAA,CAAAxE,OAAA;QACAwE,MAAA,CAAAtE,OAAA;MACA;IACA;IAIAwE,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAAhE,IAAA,CAAAiE,QAAA;QACA,KAAA7E,UAAA,CAAA8E,OAAA,WAAArB,IAAA;UACA,IAAAA,IAAA,CAAAsB,EAAA,IAAAH,MAAA,CAAAhE,IAAA,CAAAiE,QAAA;YACAD,MAAA,CAAAhE,IAAA,CAAAvB,IAAA,GAAAoE,IAAA,CAAApE,IAAA;YACAuF,MAAA,CAAAhE,IAAA,CAAAoE,MAAA,GAAAvB,IAAA,CAAAuB,MAAA;YACAJ,MAAA,CAAAhE,IAAA,CAAAqE,OAAA,GAAAxB,IAAA,CAAAwB,OAAA;YACAL,MAAA,CAAAhE,IAAA,CAAAsE,KAAA,GAAAzB,IAAA,CAAAyB,KAAA;YACAN,MAAA,CAAAhE,IAAA,CAAAuE,KAAA,GAAA1B,IAAA,CAAA0B,KAAA;YACAP,MAAA,CAAAhE,IAAA,CAAAwE,WAAA,GAAA3B,IAAA,CAAA2B,WAAA;YACAR,MAAA,CAAAhE,IAAA,CAAAyE,iBAAA,GAAA5B,IAAA,CAAA4B,iBAAA;YACAT,MAAA,CAAAhE,IAAA,CAAA0E,kBAAA,GAAA7B,IAAA,CAAA6B,kBAAA;UAEA;QACA;MACA;IACA;IAEAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACAlD,OAAA,CAAAC,GAAA;MACA;MACA,SAAA3B,IAAA,CAAA6E,KAAA;QACA,KAAAxF,OAAA,CAAA6E,OAAA,WAAArB,IAAA;UACA,IAAAA,IAAA,CAAAsB,EAAA,IAAAS,MAAA,CAAA5E,IAAA,CAAA6E,KAAA;YACAD,MAAA,CAAA5E,IAAA,CAAAiD,SAAA,GAAAJ,IAAA,CAAAI,SAAA;YACA,IAAAJ,IAAA,CAAAiC,wBAAA;cACAF,MAAA,CAAA5E,IAAA,CAAA8E,wBAAA;YACA,WAAAjC,IAAA,CAAAiC,wBAAA;cACAF,MAAA,CAAA5E,IAAA,CAAA8E,wBAAA;YACA,WAAAjC,IAAA,CAAAiC,wBAAA;cACAF,MAAA,CAAA5E,IAAA,CAAA8E,wBAAA;YACA;cACAF,MAAA,CAAA5E,IAAA,CAAA8E,wBAAA;YACA;YACA;UAEA;QACA;MACA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAAC,GAAA;MACA,IAAAA,GAAA;QACA;MACA,WAAAA,GAAA;QACA;MACA,WAAAA,GAAA;QACA;MACA;QACA;MACA;IAEA;IAEA;IACAC,8BAAA,WAAAA,+BAAAH,wBAAA;MACA,IAAAA,wBAAA;QACA;MACA,WAAAA,wBAAA;QACA;MACA,WAAAA,wBAAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACApB,kBAAA,WAAAA,mBAAAvB,QAAA;MAAA,IAAA+C,MAAA;MACA,KAAArF,YAAA;MACA,KAAAC,gBAAA;MACAqC,QAAA,CAAA+B,OAAA,WAAApF,IAAA;QACA,IAAAqG,YAAA,GAAAD,MAAA,CAAAE,WAAA;QACAD,YAAA,CAAA5E,UAAA,GAAAzB,IAAA,CAAAyB,UAAA;QACA4E,YAAA,CAAAtE,YAAA,GAAA/B,IAAA,CAAA+B,YAAA;QACAsE,YAAA,CAAArE,SAAA,GAAAhC,IAAA,CAAAgC,SAAA;QACAqE,YAAA,CAAAE,WAAA;QACAF,YAAA,CAAAG,QAAA;QACAH,YAAA,CAAAI,KAAA,GAAAzG,IAAA,CAAAiC,WAAA,CAAAyE,MAAA;QACAL,YAAA,CAAAM,MAAA;QACAN,YAAA,CAAAO,eAAA;UAAAC,MAAA;UAAAC,QAAA;QAAA;QACA,IAAA9G,IAAA,CAAAiC,WAAA;UACAjC,IAAA,CAAAiC,WAAA,CAAAmD,OAAA,WAAArB,IAAA;YACA,IAAAgD,QAAA,GAAAX,MAAA,CAAAY,OAAA;YACAD,QAAA,CAAAtF,UAAA,GAAAzB,IAAA,CAAAyB,UAAA;YACAsF,QAAA,CAAAhF,YAAA,GAAA/B,IAAA,CAAA+B,YAAA;YACAgF,QAAA,CAAAF,MAAA,GAAA9C,IAAA,CAAA8C,MAAA;YACAE,QAAA,CAAAD,QAAA,GAAA/C,IAAA,CAAAkD,OAAA,CAAAH,QAAA;YACAC,QAAA,CAAAG,QAAA,GAAAnD,IAAA,CAAAkD,OAAA,CAAAC,QAAA;YACAH,QAAA,CAAAI,WAAA,GAAApD,IAAA,CAAAkD,OAAA,CAAAE,WAAA;YACAJ,QAAA,CAAAK,WAAA,GAAArD,IAAA,CAAAkD,OAAA,CAAAG,WAAA;YACAL,QAAA,CAAAM,kBAAA,GAAAtD,IAAA,CAAAkD,OAAA,CAAAI,kBAAA;YACAN,QAAA,CAAAzF,MAAA,GAAAyC,IAAA,CAAAzC,MAAA;YACA+E,YAAA,CAAAiB,QAAA,CAAAC,IAAA,CAAAR,QAAA;UACA;UACAV,YAAA,CAAAmB,cAAA,GAAAnB,YAAA,CAAAiB,QAAA,CAAAG,MAAA;UACApB,YAAA,CAAAqB,aAAA,GAAArB,YAAA,CAAAmB,cAAA,CAAAhF,KAAA,IAAA6D,YAAA,CAAAG,QAAA;QACA;QACAJ,MAAA,CAAArF,YAAA,CAAAwG,IAAA,CAAAlB,YAAA;MACA;MACA,KAAArF,gBAAA,QAAAD,YAAA,CAAA0G,MAAA;IACA;IACA;IACAE,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,KAAA7G,YAAA,CAAAqE,OAAA,WAAAyC,QAAA;QACAD,MAAA,CAAA/G,YAAA,CAAAuE,OAAA,WAAA0C,CAAA;UACAlF,OAAA,CAAAC,GAAA;UACA,IAAAiF,CAAA,CAAArG,UAAA,IAAAoG,QAAA,CAAApG,UAAA;YACAoG,QAAA,CAAAP,QAAA,CAAAlC,OAAA,WAAArB,IAAA;cACA+D,CAAA,CAAAR,QAAA,CAAAlC,OAAA,WAAA2C,CAAA;gBACA,IAAAA,CAAA,CAAAlB,MAAA,IAAA9C,IAAA,CAAA8C,MAAA;kBACAjE,OAAA,CAAAC,GAAA,UAAAkB,IAAA;kBACAA,IAAA,CAAAiE,MAAA,GAAAD,CAAA,CAAAC,MAAA;kBACAjE,IAAA,CAAAkE,YAAA,GAAAF,CAAA,CAAAE,YAAA;kBACArF,OAAA,CAAAC,GAAA,UAAAkB,IAAA;gBACA;cACA;YACA;UACA;QACA;MACA;MACA,KAAAmE,kBAAA;IACA;IAEA5B,WAAA,WAAAA,YAAA;MACA;QACAhF,MAAA;QACAG,UAAA;QACAM,YAAA;QACAC,SAAA;QACAsF,QAAA;MACA;IACA;IAEAN,OAAA,WAAAA,QAAA;MACA;QACAvF,UAAA;QAAA;QACAM,YAAA;QAAA;QACA8E,MAAA;QAAA;QACAC,QAAA;QAAA;QACAkB,MAAA;QAAA;QACAd,QAAA;QAAA;QACA7F,WAAA;QAAA;QACA8F,WAAA;QAAA;QACAc,YAAA;MACA;IACA;IAEAE,OAAA,WAAAA,QAAAN,QAAA;MACA,OAAAA,QAAA,CAAApG,UAAA;IACA;IAEA,aACA2G,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA;MACA;MACA;MACA,SAAAxH,YAAA,CAAA6F,MAAA,WAAAiB,mBAAA;MACA;MACA,KAAA3G,gBAAA,CAAAoE,OAAA,WAAA0C,CAAA;QACA,IAAAA,CAAA,CAAAnB,MAAA;UACA0B,MAAA,CAAAC,eAAA,CAAAR,CAAA;QACA;MACA;MACA,KAAAnH,IAAA;MACA,KAAAD,KAAA;IACA;IAEA;IACA6H,aAAA,WAAAA,cAAAC,CAAA;MAAA,IAAAC,MAAA;MACA,KAAAvH,IAAA,CAAAG,WAAA,GAAAmH,CAAA;MACA,IAAAE,IAAA;MACA,SAAA7H,YAAA,CAAA6F,MAAA;QACA;QACA,KAAAiC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACA/I,IAAA;QACA,GAAAsD,IAAA;UACA;UACAR,OAAA,CAAAC,GAAA;UACA6F,IAAA,CAAA7H,YAAA;UACA6H,IAAA,CAAA5H,UAAA;UACA4H,IAAA,CAAA9H,iBAAA;UACA8H,IAAA,CAAA5F,OAAA;QACA,GAAAQ,KAAA,WAAAwF,MAAA;UACA;UACA,IAAAA,MAAA;YACAlG,OAAA,CAAAC,GAAA,CAAAiG,MAAA;YACA,IAAAL,MAAA,CAAAvH,IAAA,CAAAG,WAAA;cACAoH,MAAA,CAAAvH,IAAA,CAAAG,WAAA;YACA;cACAoH,MAAA,CAAAvH,IAAA,CAAAG,WAAA;YACA;UACA;QACA;MACA;QACA,KAAAyB,OAAA;MACA;IACA;IAEA;IACAiG,MAAA,WAAAA,OAAA;MACA,KAAApI,IAAA;MACA;IACA;IAEA;IACAqI,cAAA,WAAAA,eAAA;MACA;MACA,KAAAnI,YAAA,GAAAoI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAvI,iBAAA;MACA;MACA;MACA,KAAAwI,mBAAA;MACA,KAAAzI,IAAA;IACA;IACA;IACA0I,iBAAA,WAAAA,kBAAAxB,QAAA;MACAA,QAAA,CAAAlB,MAAA;MACA,KAAA2B,eAAA,CAAAT,QAAA;IACA;IACA;IACAyB,kBAAA,WAAAA,mBAAAzB,QAAA;MACAA,QAAA,CAAAlB,MAAA;IACA;IACA;IACA4C,mBAAA,WAAAA,oBAAA1B,QAAA;MACA,KAAAA,QAAA,CAAAvG,MAAA;MACA,IAAAuG,QAAA,CAAAlB,MAAA;QACA,KAAA2C,kBAAA,CAAAzB,QAAA;MACA;QACA,KAAAwB,iBAAA,CAAAxB,QAAA;MACA;IACA;IAEA;IACA2B,gBAAA,WAAAA,iBAAAC,GAAA,EAAAC,KAAA;MACA,IAAAD,GAAA,CAAAnI,MAAA;QACAsB,OAAA,CAAAC,GAAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACA8G,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAA/I,YAAA,CAAA6F,MAAA;QACA,KAAAmD,QAAA;QACA;MACA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAJ,MAAA,CAAAK,gBAAA;YACA;YACA,IAAAL,MAAA,CAAA1I,IAAA,CAAAG,WAAA,OAAAuI,MAAA,CAAA1I,IAAA,CAAAI,MAAA;YACA,IAAA4I,gBAAA,EAAAN,MAAA,CAAA1I,IAAA,EAAAkC,IAAA,WAAAC,QAAA;cACA;cACA,IAAAA,QAAA,CAAA8G,IAAA;gBACAP,MAAA,CAAAQ,UAAA;gBACAR,MAAA,CAAA/J,YAAA;cACA;YACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAoK,gBAAA,WAAAA,iBAAA;MACA,IAAAI,SAAA;MACA,IAAAC,CAAA;MACA,KAAAR,KAAA,aAAA1E,OAAA,WAAAmF,CAAA;QACAA,CAAA,CAAAR,QAAA,WAAAC,KAAA;UACA,KAAAA,KAAA;YACAM,CAAA;UACA;UACA;QACA;MACA;MACA,IAAAA,CAAA;QACA,KAAAzJ,YAAA,CAAAuE,OAAA,WAAAyC,QAAA,EAAA6B,KAAA;UACA7B,QAAA,CAAAP,QAAA,CAAAlC,OAAA,WAAArB,IAAA;YACAsG,SAAA,CAAA9C,IAAA,CAAAxD,IAAA;UACA;QACA;MACA;MACA,KAAA7C,IAAA,CAAAK,WAAA,GAAA8I,SAAA;MACA,OAAAC,CAAA;IACA;IAEA;IACAE,qBAAA,WAAAA,sBAAAhC,CAAA,EAAAxI,IAAA;MACA,IAAAyK,EAAA,GAAAzK,IAAA,CAAAyB,UAAA;MACA,UAAAX,UAAA,IAAA2G,MAAA,CAAAgD,EAAA;QACA,KAAAC,IAAA,MAAA5J,UAAA,KAAA2G,MAAA,CAAAgD,EAAA;MACA;MACA,KAAA3J,UAAA,IAAA2G,MAAA,CAAAgD,EAAA,KAAAjC,CAAA;MACA,IAAAmC,IAAA;MACA,KAAA/J,iBAAA,CAAAwE,OAAA,WAAAyC,QAAA;QACA,IAAAA,QAAA,CAAApG,UAAA,IAAAzB,IAAA,CAAAyB,UAAA;UACAkJ,IAAA;UACA;UACA;UACA9C,QAAA,CAAAP,QAAA,GAAAkB,CAAA;QACA;MACA;MACA,KAAAmC,IAAA;QACA;QACA,IAAAtE,YAAA,QAAAC,WAAA;QACAD,YAAA,CAAA5E,UAAA,GAAAzB,IAAA,CAAAyB,UAAA;QACA4E,YAAA,CAAAtE,YAAA,GAAA/B,IAAA,CAAA+B,YAAA;QACAsE,YAAA,CAAArE,SAAA,GAAAhC,IAAA,CAAAgC,SAAA;QACAqE,YAAA,CAAAhF,WAAA,GAAArB,IAAA,CAAAqB,WAAA;QACA;QACAgF,YAAA,CAAAiB,QAAA,GAAAkB,CAAA;QACA,KAAA5H,iBAAA,CAAA2G,IAAA,CAAAlB,YAAA;MACA;IACA;IAEA;IACAiC,eAAA,WAAAA,gBAAAT,QAAA;MAAA,IAAA+C,OAAA;MACA,IAAAH,EAAA,GAAA5C,QAAA,CAAApG,UAAA;MACA,IAAAoJ,IAAA,QAAA/J,UAAA,IAAA2G,MAAA,CAAAgD,EAAA;MACA7H,OAAA,CAAAC,GAAA,CAAAgI,IAAA;MACA,KAAAC,SAAA;QACA,IAAAD,IAAA;UACAA,IAAA,CAAAzF,OAAA,WAAAqE,GAAA;YACA,IAAAmB,OAAA,CAAA3J,WAAA,CAAAgD,QAAA,CAAAwF,GAAA;cACAmB,OAAA,CAAAd,KAAA,IAAArC,MAAA,CAAAgD,EAAA,MAAAM,kBAAA,CAAAtB,GAAA;cACAA,GAAA,CAAAzB,MAAA;cACAyB,GAAA,CAAAxB,YAAA;cACA2C,OAAA,CAAA3J,WAAA,CAAA+J,MAAA,CAAAJ,OAAA,CAAA3J,WAAA,CAAAgK,OAAA,CAAAxB,GAAA;YACA;cACAmB,OAAA,CAAAd,KAAA,IAAArC,MAAA,CAAAgD,EAAA,MAAAM,kBAAA,CAAAtB,GAAA;YACA;UACA;QACA;UACAmB,OAAA,CAAAd,KAAA,IAAArC,MAAA,CAAAgD,EAAA,MAAAS,cAAA;QACA;MACA;MACAtI,OAAA,CAAAC,GAAA,aAAA5B,WAAA;IACA;IAEA;IACAkK,mBAAA,WAAAA,oBAAA5E,WAAA,EAAAsB,QAAA;MACAA,QAAA,CAAAH,aAAA,GAAAG,QAAA,CAAAL,cAAA,CAAAhF,KAAA,CAAAqF,QAAA,CAAArB,QAAA,IAAAD,WAAA,OAAAsB,QAAA,CAAArB,QAAA,IAAAD,WAAA,QAAAsB,QAAA,CAAArB,QAAA;IACA;IACA;IACA4E,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,OAAA;MACA,SAAA9J,mBAAA,CAAAC,UAAA;QACA,KAAAT,gBAAA,QAAAD,YAAA,CAAA0G,MAAA;MACA;QACA,KAAA1G,YAAA,CAAAqE,OAAA,WAAAyC,QAAA;UACA,IAAAA,QAAA,CAAApG,UAAA,CAAAwC,QAAA,CAAAoH,OAAA,CAAA7J,mBAAA,CAAAC,UAAA;YACA6J,OAAA,CAAA/D,IAAA,CAAAM,QAAA;UACA;QACA;QACA,KAAA7G,gBAAA,GAAAsK,OAAA;MACA;MACA;MACA,KAAAtK,gBAAA,CAAAoE,OAAA,WAAA0C,CAAA;QACA,IAAAA,CAAA,CAAAnB,MAAA;UACA0E,OAAA,CAAA/C,eAAA,CAAAR,CAAA;QACA;MACA;IACA;IACA,aACAyD,mBAAA,WAAAA,oBAAA;MACA,KAAAC,SAAA;MACA,KAAAJ,oBAAA;IACA;IAEA;IACAK,gBAAA,WAAAA,iBAAA5D,QAAA;MACA,IAAAjB,eAAA,GAAAiB,QAAA,CAAAjB,eAAA;MACA,IAAA8E,WAAA;MACA,IAAA9E,eAAA,CAAAE,QAAA,UAAAF,eAAA,CAAAC,MAAA;QACA6E,WAAA,GAAA7D,QAAA,CAAAP,QAAA;MACA;QACAO,QAAA,CAAAP,QAAA,CAAAlC,OAAA,WAAArB,IAAA;UACA,IAAAA,IAAA,CAAA+C,QAAA,CAAA7C,QAAA,CAAA2C,eAAA,CAAAE,QAAA,KAAA/C,IAAA,CAAA8C,MAAA,CAAA5C,QAAA,CAAA2C,eAAA,CAAAC,MAAA;YACA6E,WAAA,CAAAnE,IAAA,CAAAxD,IAAA;UACA;QACA;MACA;MACA8D,QAAA,CAAAL,cAAA,GAAAkE,WAAA;MACA7D,QAAA,CAAApB,KAAA,GAAAiF,WAAA,CAAAhF,MAAA;MACAmB,QAAA,CAAAtB,WAAA;MACAsB,QAAA,CAAAH,aAAA,GAAAG,QAAA,CAAAL,cAAA,CAAAhF,KAAA,IAAAqF,QAAA,CAAArB,QAAA;MACA;IACA;IACA;IACAmF,eAAA,WAAAA,gBAAA9D,QAAA;MACA,IAAAM,OAAA,QAAAA,OAAA,CAAAN,QAAA;MACA,KAAAiC,KAAA,IAAArC,MAAA,CAAAU,OAAA,MAAAyD,WAAA;MACA,KAAAH,gBAAA,CAAA5D,QAAA;IACA;IAEA;IACAgE,UAAA,WAAAA,WAAApC,GAAA;MACA,OAAAA,GAAA,CAAA5C,MAAA;IACA;IAEA;IACAiF,aAAA,WAAAA,cAAAjE,QAAA,EAAAhB,MAAA;MAAA,IAAAkF,OAAA;MACA;MACA,KAAAlL,YAAA,CAAAuE,OAAA,WAAA0C,CAAA;QACA,IAAAA,CAAA,CAAArG,UAAA,IAAAoG,QAAA,CAAApG,UAAA;UACAqG,CAAA,CAAAR,QAAA,CAAAlC,OAAA,WAAA2C,CAAA,EAAA2B,KAAA;YACA,IAAA3B,CAAA,CAAAlB,MAAA,IAAAA,MAAA;cACAiB,CAAA,CAAAR,QAAA,CAAA0D,MAAA,CAAAtB,KAAA;YACA;UACA;QACA;MACA;MACA;MACAsC,MAAA,CAAAC,IAAA,MAAAnL,UAAA,EAAAsE,OAAA,WAAAqF,EAAA;QACA,IAAAA,EAAA,IAAA5C,QAAA,CAAApG,UAAA;UACAsK,OAAA,CAAAjL,UAAA,IAAA2G,MAAA,CAAAgD,EAAA,GAAArF,OAAA,WAAA2C,CAAA;YACA,IAAAA,CAAA,CAAAlB,MAAA,IAAAA,MAAA;cACAkF,OAAA,CAAA9K,WAAA,CAAAsG,IAAA,CAAAQ,CAAA;YACA;UACA;QACA;MACA;MACA,KAAAqB,mBAAA;IACA;IACA;IACAA,mBAAA,WAAAA,oBAAA;MACA,KAAAvI,YAAA,QAAAA,YAAA,CAAAiD,MAAA,WAAAgE,CAAA;QAAA,OAAAA,CAAA,CAAAR,QAAA,CAAAZ,MAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}