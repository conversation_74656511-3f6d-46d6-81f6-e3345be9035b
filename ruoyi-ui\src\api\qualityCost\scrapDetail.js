import request from '@/utils/request'

// 查询内部损失成本-产品报废损失明细列表
export function listScrapDetail(query) {
  return request({
    url: '/qualityCost/scrapDetail/list',
    method: 'get',
    params: query
  })
}

// 查询内部损失成本-产品报废损失明细列表（全量）
export function listAllScrapDetail(query) {
  return request({
    url: '/qualityCost/scrapDetail/listAll',
    method: 'get',
    params: query
  })
}

// 查询内部损失成本-产品报废损失明细详细
export function getScrapDetail(recCreator) {
  return request({
    url: '/qualityCost/scrapDetail/' + recCreator,
    method: 'get'
  })
}

// 新增内部损失成本-产品报废损失明细
export function addScrapDetail(data) {
  return request({
    url: '/qualityCost/scrapDetail',
    method: 'post',
    data: data
  })
}

// 修改内部损失成本-产品报废损失明细
export function updateScrapDetail(data) {
  return request({
    url: '/qualityCost/scrapDetail',
    method: 'put',
    data: data
  })
}

// 删除内部损失成本-产品报废损失明细
export function delScrapDetail(recCreator) {
  return request({
    url: '/qualityCost/scrapDetail/' + recCreator,
    method: 'delete'
  })
}

// 导出内部损失成本-产品报废损失明细
export function exportScrapDetail(query) {
  return request({
    url: '/qualityCost/scrapDetail/export',
    method: 'get',
    params: query
  })
}

// 按成本中心和会计期查询产品报废损失明细
export function getScrapDetailByCenterAndPeriod(query) {
  return request({
    url: '/qualityCost/scrapDetail/scrapDetailByCenterAndPeriod',
    method: 'get',
    params: query
  })
}

export function getSum(query) {
  return request({
    url: '/qualityCost/scrapDetail/getSum',
    method: 'get',
    params: query
  })
}

export function getAllSum(query) {
  return request({
    url: '/qualityCost/scrapDetail/getAllSum',
    method: 'get',
    params: query
  })
}
