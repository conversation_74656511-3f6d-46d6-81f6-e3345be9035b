<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-row>
        <el-form-item label="考核年月" prop="assessDate">
          <el-date-picker
            v-model="queryParams.assessDate"
            type="month"
            value-format="yyyy-M"
            format="yyyy 年 M 月"
            placeholder="选择考核年月"
            :clearable="false"
            @change="handleQuery">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="部门" prop="deptId">
          <el-select v-model="queryParams.deptId" placeholder="请选择部门" disabled>
            <el-option
              v-for="item in deptOptions"
              :key="item.deptId"
              :label="item.deptName"
              :value="item.deptId"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button type="warning" icon="el-icon-s-tools" size="mini" @click="handleConfig">指标配置</el-button>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="当前状态">
          <el-tag v-if="status == '0' && rejectReason" type="danger">退 回</el-tag>
          <el-tag v-if="status == '0' && !rejectReason" type="info">未提交</el-tag>
            <el-tag v-else-if="status === '1'" type="warning">部门评分</el-tag>
            <el-tag v-else-if="status === '2'" type="warning">事业部评分</el-tag>
            <el-tag v-else-if="status === '3'" type="warning">运改/组织部评分</el-tag>
            <el-tag v-else-if="status === '4' || status === '5'" type="success">已完成</el-tag>
        </el-form-item>
      </el-row>
      <el-row v-if="rejectReason">
        <el-form-item label="退回原因" prop="rejectReason">
            <span class="el-icon-warning" style="color: #f56c6c;"></span>
            {{ rejectReason }}
        </el-form-item>
      </el-row>
    </el-form>
      <h3 style="text-align: center;">月度业绩考核表</h3>
      <el-descriptions class="margin-top" :column="3">
        <el-descriptions-item>
          <template slot="label">
            姓名
          </template>
          {{ userInfo.name }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            部门
          </template>
          {{ deptName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            考核年月
          </template>
          {{ assessDateText }}
        </el-descriptions-item>
      </el-descriptions>
      <el-table v-loading="loading" :data="list"
        :span-method="objectSpanMethod" border>
        <el-table-column label="类型" align="center" prop="item" width="120"/>
        <el-table-column label="指标" align="center" prop="category" width="140"/>
        <el-table-column label="目标" align="center" prop="target" width="150" />
        <el-table-column label="评分标准" align="center" prop="standard" />
        <el-table-column label="完成实绩（若扣分，写明原因）" align="center" prop="performance" width="440">
            <template slot-scope="scope">
                <span v-if="readOnly">{{ scope.row.performance }}</span>
                <div v-else style="display: flex">
                  <!-- <el-button icon="el-icon-search" size="small"></el-button> -->
                  <el-popover
                    placement="left"
                    width="636"
                    trigger="click"
                    :ref="'popover' + scope.$index">
                    <el-table :data="beAssessedList">
                      <el-table-column width="150" property="assessDeptName" label="提出考核单位"></el-table-column>
                      <el-table-column width="300" property="assessContent" label="事项"></el-table-column>
                      <el-table-column width="80" property="deductionOfPoint" label="加减分"></el-table-column>
                      <el-table-column width="80" label="操作">
                        <template slot-scope="beAssessed">
                          <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-edit"
                            @click="handleBeAssessedClick(scope.row,beAssessed.row,scope.$index)"
                          >填入</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button slot="reference" icon="el-icon-search" size="small"></el-button>
                  </el-popover>
                  <el-input class="table-input" type="textarea" autosize v-model="scope.row.performance" placeholder="请输入完成实绩" />
                </div>
            </template>
        </el-table-column>
        <el-table-column label="加减分" align="center" prop="dePoints" width="108">
            <template slot-scope="scope">
                <span v-if="readOnly">{{ scope.row.dePoints }}</span>
                <el-input v-else class="table-input" type="number" autosize v-model="scope.row.dePoints" placeholder="请输入加减分" @input="scoreInput(scope.row)"></el-input>
            </template>
        </el-table-column>
        <el-table-column label="加减分原因" align="center" prop="pointsReason"  width="440">
            <template slot-scope="scope">
                <span v-if="readOnly">{{ scope.row.pointsReason }}</span>
                <el-input v-else class="table-input" type="textarea" autosize v-model="scope.row.pointsReason" placeholder="请输入加减分原因"></el-input>
            </template>
        </el-table-column>
      </el-table>

      <el-form size="small" :inline="false" label-width="200px" style="margin-top: 10px;" label-position="left">
            <el-form-item v-if="readOnly" label="自评分数：" prop="deptId">
              <span>{{ selfScore + " 分" }}</span>
            </el-form-item>
            <el-form-item v-else label="自评分数：" prop="selfScore">
              <div style="display: flex;width: 180px;">
                <el-input type="number" autosize v-model="selfScore" placeholder="请输入分数" :readonly="readOnly" />
                <span style="margin-left: 8px;">分</span>
              </div>
            </el-form-item>
          </el-form>
      <div v-if="!readOnly && status !== '1'" style="text-align: center;">
        <el-button v-if="resetShow" plain type="info" @click="resetInfo">重 置 </el-button>
        <el-button type="success" @click="save">保 存</el-button>
      </div>
    </div>
  </template>

  <script>
  import { getInfoByDate, saveInfo, delInfo, listBeAssessed } from "@/api/assess/self/info";
  import { getDept } from "@/api/assess/lateral/dept";
  import { getByWorkNoDeptId } from "@/api/assess/self/user";

  export default {
    name: "SelfAssessReport",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 显示搜索条件
        showSearch: true,
        // 绩效考核-自评指标配置表格数据
        list: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          userId:null,
          workNo: null,
          deptId:null,
          assessDate: null,
        },
        // 考核年月文本显示
        assessDateText:null,
        // 部门显示
        deptName:null,
        // 表单参数
        form: {},
        // 表单校验
        rules: {
        },
        // 用户信息
        userInfo:{},
        // 合并单元格信息
        spanList:{
          itemList:[],
          standardList:[]
        },
        // 是否显示重置按钮
        resetShow:false,
        // 是否只读
        readOnly:false,
        // 部门选项
        deptOptions:[],
        // 自评信息Id
        id:null,
        // 自评分数
        selfScore:100,
        // 状态
        status:"0",
        // 自评信息
        info:{},
        // 横向被考评信息
        beAssessedList:[],
        // 退回理由
        rejectReason:"",
      };
    },
    created() {
      this.initPageData();
    },

    // 监听路由变化，确保每次进入页面都重新获取数据
    watch: {
      '$route'(to) {
        // 当路由发生变化时，重新初始化页面数据
        if (to.path === '/assess/self/user/report') {
          this.initPageData();
        }
      }
    },

    // 路由更新时的钩子
    beforeRouteUpdate(to, from, next) {
      // 在当前路由改变，但是该组件被复用时调用
      this.initPageData();
      next();
    },
    methods: {
      // 初始化页面数据
      initPageData() {
        // 从路由中获取参数
        this.queryParams.workNo = this.$route.query.workNo;
        this.queryParams.deptId = this.$route.query.deptId;

        // 验证必要参数
        if (!this.queryParams.workNo || !this.queryParams.deptId) {
          this.$message.error('缺少必要参数：用户工号或部门ID');
          this.$router.go(-1);
          return;
        }

        // 设置默认考核年月
        this.queryParams.assessDate = this.getDefaultAssessDate();
        this.assessDateText = this.queryParams.assessDate.replace("-"," 年 ") + " 月";

        // 重置数据
        this.list = [];
        this.userInfo = {};
        this.deptOptions = [];
        this.beAssessedList = [];
        this.id = null;
        this.selfScore = 100;
        this.status = "0";
        this.info = {};
        this.rejectReason = "";
        this.readOnly = false;
        this.resetShow = false;

        // 根据路由参数直接获取用户信息
        this.getDeptInfo();
        this.getUserInfoByParams();
      },

      // 获取默认考核日期
      getDefaultAssessDate() {
        const now = new Date();
        const currentDay = now.getDate();

        let targetDate;
        if (currentDay < 10) {
          // 当前日期小于10日，默认为上个月
          targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        } else {
          // 当前日期大于等于10日，默认为当月
          targetDate = new Date(now.getFullYear(), now.getMonth(), 1);
        }

        // 格式化为 YYYY-M 格式
        const year = targetDate.getFullYear();
        const month = targetDate.getMonth() + 1;
        return `${year}-${month}`;
      },

        getDeptInfo(){
            getDept(this.queryParams.deptId).then(res => {
                // 设置部门名称
                this.deptName = res.data.deptName || '未知部门';

                // 设置部门选项（只包含当前部门）
                this.deptOptions = [{
                    deptId: this.queryParams.deptId,
                    deptName: this.deptName
                }];
            });
        },

      // 根据路由参数直接获取用户信息
      getUserInfoByParams() {
        // 根据工号和部门ID获取用户信息
        getByWorkNoDeptId({
          workNo: this.queryParams.workNo,
          deptId: this.queryParams.deptId
        }).then(res => {
          console.log('获取用户信息:', res);
          if(res.code == 200 && res.data) {
            this.queryParams.userId = res.data.id;
            this.userInfo = res.data;


            // 获取考核数据和被考核信息
            this.getList();
            this.getBeAssessedList();
          } else {
            this.$message.error('获取用户信息失败');
            this.$router.go(-1);
          }
        }).catch(error => {
          console.error('获取用户信息失败:', error);
          this.$message.error('获取用户信息失败');
          this.$router.go(-1);
        });
      },


      
      // 获取被考核信息
      getBeAssessedList(){
        listBeAssessed({deptId:this.queryParams.deptId,assessDate:this.queryParams.assessDate}).then(res =>{
          let beAssessedList = [];
          if(res.code == 200){
            if(res.data.length > 0){
              res.data.forEach(item => {
                beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]
              })
              this.beAssessedList = beAssessedList;
            }
          }
          console.log(beAssessedList)
        })
      },
      /** 查询绩效考核-自评指标配置列表 */
      getList() {
        this.loading = true;
        getInfoByDate(this.queryParams).then(response => {
          console.log(response.data);
          // console.log(typeof response.data);
          if (Array.isArray(response.data)) {
            // 指标配置数据
            this.handleSpanList(response.data);
            this.list = response.data.map(item => {
              item.performance = "";
              // 根据项目类型设置默认分数
              let noSpaceStr = item.item.replace(/\s+/g, '');
              if(noSpaceStr.includes("月度重点工作")){
                item.dePoints = 5; // 月度重点工作默认5分
              } else {
                item.dePoints = 0; // 其他项目默认0分
              }
              return item;
            });
            this.status = "0";
            this.readOnly = false;
            this.resetShow = false;
            this.rejectReason = null;
            this.calculateSelfScore();
          } else {
            // 自评信息
            let info = response.data;
            let list = JSON.parse(info.content);
            this.handleSpanList(list);
            this.list = list;
            this.id = info.id;
            this.selfScore = info.selfScore;
            this.rejectReason = info.rejectReason;
            this.status = info.status;
            this.info = info;
            if(info.status == "0"){
              this.readOnly = false;
              this.resetShow = true;
            }else{
              this.readOnly = true;
              this.resetShow = false;
            }
          }
          this.loading = false;
        });
      },

      // 处理列表
      handleSpanList(data){
        let itemList = [];
        let standardList = [];
        let itemFlag = 0;
        let standardFlag = 0;
        for(let i = 0; i < data.length; i++){
          // 相同考核项、评分标准合并
          if(i == 0){
            itemList.push({
              rowspan: 1,
              colspan: 1
            })
            standardList.push({
              rowspan: 1,
              colspan: 1
            })
          }else{
            // 考核项
            if(data[i - 1].item == data[i].item){
              itemList.push({
                rowspan: 0,
                colspan: 0
              })
              itemList[itemFlag].rowspan += 1;
            }else{
              itemList.push({
                rowspan: 1,
                colspan: 1
              })
              itemFlag = i;
            }
            // 评分标准
            if(data[i - 1].standard == data[i].standard){
              standardList.push({
                rowspan: 0,
                colspan: 0
              })
              standardList[standardFlag].rowspan += 1;
            }else{
              standardList.push({
                rowspan: 1,
                colspan: 1
              })
              standardFlag = i;
            }
          }
        }
        this.spanList.itemList = itemList;
        this.spanList.standardList = standardList;
      },





      /** 搜索按钮操作 */
      handleQuery() {
        // 验证必要参数
        if (!this.queryParams.workNo || !this.queryParams.deptId) {
          this.$message.error('缺少必要参数：用户工号或部门ID');
          return;
        }

        this.assessDateText = this.queryParams.assessDate.replace("-"," 年 ") + " 月";

        // 重置相关数据
        this.id = null;
        this.info = {};
        this.selfScore = 100;
        this.list = [];
        this.beAssessedList = [];
        this.rejectReason = "";
        this.readOnly = false;
        this.resetShow = false;

        // 重新获取数据
        this.getList();
        this.getBeAssessedList();
      },

      // 保存
      save(){
        if(this.list.length == 0){
          this.$message({
              type: 'warning',
              message: '未配置相关信息，请先配置指标'
            });
            return false;
        }
        let data = this.handleData(this.list);
        let form = {
          id:this.id,
          workNo:this.userInfo.workNo,
          assessDate:this.queryParams.assessDate,
          deptId:this.queryParams.deptId,
          content:JSON.stringify(data),
          status:"0",
          userId:this.queryParams.userId,
          deptName:this.deptName,
          name:this.userInfo.name,
          selfScore:this.selfScore,
          job:this.userInfo.job,
          postType:this.userInfo.postType
        }
        saveInfo(form).then(res => {
          if(res.code == 200){
            this.getList();
            this.$message({
              type: 'success',
              message: '保存成功!'
            });
          }
        })
      },



      // 保存重置
      resetInfo(){
        this.$confirm('重置后将清空所有已填写的内容，是否确认重置?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 删除保存信息
          delInfo({id:this.id}).then(res => {
            if(res.code == 200){
              this.id = null;
              this.selfScore = null;
              // 获取配置信息
              this.getList();
              this.$message({
                type: 'success',
                message: '重置成功!'
              });
            }
          }).catch(error => {
            console.error('重置失败:', error);
            this.$message({
              type: 'error',
              message: '重置失败，请重试'
            });
          });
        }).catch(() => {
          // 用户取消重置
        });
      },

      // 处理提交数据
      handleData(data){
        let result = []
        data.forEach(item => {
          let form = {
            item: item.item,
            category: item.category,
            target: item.target,
            standard: item.standard,
            performance: item.performance,
            dePoints: item.dePoints,
            pointsReason:item.pointsReason
          };
          result.push(form);
        })
        return result
      },

      /** 标准配置跳转 */
      handleConfig(){
        if (this.queryParams.userId) {
          // 直接使用当前用户ID跳转
          this.$router.push({
            path:"/assess/self/user/detail",
            query:{
              userId: this.queryParams.userId
            }
          });
        } else {
          // 如果没有用户ID，通过工号和部门ID获取
          getByWorkNoDeptId({
            workNo: this.queryParams.workNo,
            deptId: this.queryParams.deptId
          }).then(res => {
            console.log(res)
            if(res.code == 200 && res.data && res.data.id){
              this.$router.push({
                path:"/assess/self/user/detail",
                query:{
                  userId: res.data.id
                }
              });
            } else {
              this.$message.error('获取用户配置信息失败');
            }
          }).catch(error => {
            console.error('获取用户配置信息失败:', error);
            this.$message.error('获取用户配置信息失败');
          });
        }
      },


      // 合并单元格方法
      objectSpanMethod({ row, rowIndex, columnIndex }) {
        // 第一列相同项合并
        if (columnIndex === 0) {
          return this.spanList.itemList[rowIndex];
        }
        // 评分标准相同合并
        if(columnIndex === 3){
          return this.spanList.standardList[rowIndex];
        }
        // 类别无内容 合并
        if(columnIndex === 1){
          if(!row.category){
            return {
              rowspan: 0,
              colspan: 0
            }
          }
        }
        if(columnIndex === 2){
          if(!row.category){
            return {
              rowspan: 1,
              colspan: 2
            }
          }
        }
      },

      // 被考核事项点击事件
      handleBeAssessedClick(row,optionRow,index){
        console.log(row)
        // 将事项填入完成实绩列（弃用）
        // if(row.performance){
        //   this.$set(row, 'performance', row.performance + "；" + optionRow.assessContent);
        // }else{
        //   this.$set(row, 'performance', optionRow.assessContent);
        // }
        
        // 将分数填入加减分列
        if(row.dePoints){
          this.$set(row, 'dePoints', Number(row.dePoints) + Number(optionRow.deductionOfPoint));
        }else{
          this.$set(row, 'dePoints', Number(optionRow.deductionOfPoint));
        }
        
        // 将事项+分数填入加减分理由列
        let reasonContent = optionRow.assessContent + "(" + optionRow.deductionOfPoint + "分)";
        if(row.pointsReason){
          this.$set(row, 'pointsReason', row.pointsReason + "；" + reasonContent);
        }else{
          this.$set(row, 'pointsReason', reasonContent);
        }
        
        this.$refs[`popover${index}`].showPopper = false;
        // 重新计算自评分数
        this.scoreInput();
      },

      // 加减分输入
      scoreInput(row = null){
        // 验证加减分规则（仅当传入row参数时进行验证）
        if (row && row.item) {
          let noSpaceStr = row.item.replace(/\s+/g, '');
          let value = row.dePoints;

          if (value !== null && value !== undefined && value !== '') {
            let numValue = Number(value);

            // 月度重点工作：只能为1、3或5分
            if (noSpaceStr.includes("月度重点工作")) {
              if (![1, 3, 5].includes(numValue)) {
                this.$message({
                  type: 'warning',
                  message: '月度重点工作为得分制，只能为1分、3分或5分'
                });
                this.$set(row, 'dePoints', null);
                return;
              }
            }
            // 加分项：除月度重点工作外，只能填0
            else if (noSpaceStr.includes("加分项")) {
              if (numValue !== 0) {
                this.$message({
                  type: 'warning',
                  message: '加分项只能填0分'
                });
                this.$set(row, 'dePoints', 0);
                return;
              }
            }
            // 其他类型项：只能填0或负数
            else {
              if (numValue > 0) {
                this.$message({
                  type: 'warning',
                  message: '该项目只能填0分或负数'
                });
                this.$set(row, 'dePoints', 0);
                return;
              }
            }
          }
        }

        // 重新计算自评分数
        this.calculateSelfScore();
      },

      /** 计算自评分数 */
      calculateSelfScore() {
        let points = 0;   // 加减分

        this.list.forEach(item => {
              points += Number(item.dePoints);
        });
        // 计算总分：基础分85 + 加减分
        this.selfScore = 85 + points;
      },


    },
  };
</script>
<style>
  .table-striped{
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%;
    text-align: center;
    border: 1px #888;
    border-collapse: collapse;
  }
  .table-striped th{
    height: 32px;
    border: 1px solid #888;
    background-color: #dedede;
  }
  .table-striped td{
    min-height: 32px;
    border: 1px solid #888;
  }
  .table-input .el-textarea__inner{
    border: 0 !important;
    resize: none !important;
  }
  .table-input .el-input__inner{
    border: 0 !important;
  }
  /* .myUpload .el-upload--picture-card{
    display:none !important; 
  } */
  </style>
