import * as echarts from 'echarts'

export default {
  methods: {
    // 获取矿焦煤物料类型的颜色映射
    getCokingCoalMaterialColorMap() {
      // 使用月度库存金额图表的颜色方案
      const baseColors = ['#0066ff', '#00ff00', '#ff0000', '#8b00ff', '#ffff00', '#ffffff']

      // 基于所有原始数据为每个物料类型分配固定颜色，确保过滤时颜色保持一致
      const allMaterialTypes = []
      const inventoryData = this.cokingCoalInventoryData || []

      // 收集所有物料类型
      inventoryData.forEach(item => {
        const materialName = item.class2Name || '未知物料'
        if (!allMaterialTypes.includes(materialName)) {
          allMaterialTypes.push(materialName)
        }
      })

      // 按字母顺序排序，确保颜色分配的一致性
      allMaterialTypes.sort()

      // 为每个物料类型分配固定颜色
      const colorMap = {}
      allMaterialTypes.forEach((materialName, index) => {
        colorMap[materialName] = baseColors[index % baseColors.length]
      })

      return colorMap
    },

    // 清理并重新初始化图表
    reinitChart(chartId) {
      const chartDom = document.getElementById(chartId)
      if (!chartDom) {
        console.error(`找不到图表DOM: ${chartId}`)
        return null
      }

      if (this.chartInstances[chartId] && this.chartInstances[chartId].intervalId) {
        clearInterval(this.chartInstances[chartId].intervalId)
        this.chartInstances[chartId].intervalId = null
      }

      const existingInstance = echarts.getInstanceByDom(chartDom)

      if (existingInstance) {
        try {
          echarts.dispose(existingInstance)
          console.log(`ECharts instance successfully disposed for: ${chartId}`)
        } catch (err) {
          console.error(`Error disposing ECharts instance for ${chartId}:`, err)
          chartDom.innerHTML = ''
        }
      }

      chartDom.innerHTML = '<div class="chart-placeholder">数据加载中...</div>'
      this.chartInstances[chartId] = null

      try {
        const newChart = echarts.init(chartDom)
        this.chartInstances[chartId] = newChart
        console.log(`创建新图表: ${chartId}`)
        return newChart
      } catch (err) {
        console.error(`创建图表失败: ${chartId}`, err)
        chartDom.innerHTML = '<div class="chart-placeholder">图表加载失败</div>'
        return null
      }
    },

    // 中心仓库月度库存金额曲线图
    initMonthlyInventoryChart() {
      console.log('initMonthlyInventoryChart started with data:', JSON.stringify(this.yearlyInventoryData))
      const myChart = this.reinitChart('monthlyInventoryChart')
      if (!myChart) return

      const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']

      // 年份对应颜色映射
      const yearColorMap = {
        '2020': '#0066ff',  // 蓝色
        '2021': '#00ff00',  // 绿色
        '2022': '#ff0000',  // 红色
        '2023': '#ffff00',  // 黄色
        '2024': '#8b00ff',  // 紫色
        '2025': '#ffffff'   // 白色
      }

      // 构建每年的数据系列
      const seriesData = []
      const legendData = []

      this.yearlyInventoryData.forEach((yearData, index) => {
        const yearAmounts = new Array(12).fill(null)

        // 填充每个月的数据
        yearData.monthlyResultVoList.forEach(monthData => {
          const monthIndex = monthData.monthIndex - 1 // 转换为0-11的索引
          if (monthIndex >= 0 && monthIndex < 12) {
            yearAmounts[monthIndex] = parseFloat(monthData.amount) || 0
          }
        })

        // 根据年份获取对应颜色，如果没有预定义颜色，使用默认颜色
        const year = yearData.year
        const color = yearColorMap[year] || '#83bff6'

        const seriesItem = {
          name: `${yearData.year}年`,
          type: 'line',
          data: yearAmounts,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: color
          },
          itemStyle: {
            color: color
          },
          connectNulls: false
        }

        seriesData.push(seriesItem)
        legendData.push(`${yearData.year}年`)
      })

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function(params) {
            let tooltipText = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.value !== null && param.value !== undefined) {
                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 万元<br/>`
              }
            })
            return tooltipText
          }
        },
        legend: {
          data: legendData,
          textStyle: {
            color: '#fff'
          },
          top: '5%'
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '10%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: monthNames,
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee'
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '库存金额 (万元)',
          axisLabel: {
            color: '#eee',
            formatter: '{value}'
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: seriesData
      }

      myChart.setOption(option, true)
    },

    // 物料入库统计图表
    initMaterialStatisticsChart() {
      console.log('initMaterialStatisticsChart started with data:', JSON.stringify(this.materialStatisticsData))
      const myChart = this.reinitChart('materialStatisticsChart')
      if (!myChart) return

      const statisticsData = this.materialStatisticsData || []

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let tooltipText = params[0].axisValueLabel + '<br/>'
            params.forEach(param => {
              let value = typeof param.value === 'object' ? param.value.value : param.value
              let formattedDisplay = ''
              if (param.seriesName === '入库金额') {
                formattedDisplay = value + ' 万元'
              } else if (param.seriesName === '到货完成度%') {
                formattedDisplay = (typeof value === 'number' ? value.toFixed(2) : value) + '%'
              } else {
                formattedDisplay = value
              }
              tooltipText += `${param.marker}${param.seriesName}: ${formattedDisplay}<br/>`
            })
            tooltipText += '<span style="color: #00d4ff;">点击查看详情</span>'
            return tooltipText
          }
        },
        legend: {
          data: ['入库金额', '到货完成度%'],
          textStyle: {
            color: '#fff'
          },
          top: '5%'
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '10%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: statisticsData.map(item => item.itemName || '未知物料'),
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee',
            interval: 0,
            rotate: 30
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '入库金额 (万元)',
            position: 'left',
            axisLabel: {
              color: '#eee'
            },
            axisLine: {
              lineStyle: {
                color: '#eee'
              }
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255,255,255,0.1)'
              }
            }
          },
          {
            type: 'value',
            name: '到货完成度%',
            position: 'right',
            min: 0,
            max: 100,
            interval: 20,
            axisLabel: {
              formatter: '{value}%',
              color: '#eee'
            },
            axisLine: {
              lineStyle: {
                color: '#eee'
              }
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '入库金额',
            type: 'bar',
            data: statisticsData.map((item, index) => ({
              value: parseFloat(item.inAmt) || 0,
              itemIndex: index,
              itemData: item
            })),
            itemStyle: {
              color: '#83bff6',
              borderRadius: [4, 4, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: '#83bff6',
                borderRadius: [4, 4, 0, 0],
                shadowBlur: 10,
                shadowColor: 'rgba(255, 255, 255, 0.5)',
                borderWidth: 2,
                borderColor: '#fff'
              }
            }
          },
          {
            name: '到货完成度%',
            type: 'bar',
            yAxisIndex: 1,
            data: statisticsData.map((item, index) => ({
              value: parseFloat(item.arriveRate) || 0,
              itemIndex: index,
              itemData: item
            })),
            itemStyle: {
              color: '#ea7ccc',
              borderRadius: [4, 4, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: '#ea7ccc',
                borderRadius: [4, 4, 0, 0],
                shadowBlur: 10,
                shadowColor: 'rgba(255, 255, 255, 0.5)',
                borderWidth: 2,
                borderColor: '#fff'
              }
            }
          }
        ]
      }

      // 添加点击事件监听器
      myChart.off('click') // 先移除之前的点击事件
      myChart.on('click', function(params) {
        if (params.componentType === 'series' && params.seriesType === 'bar') {
          // 获取当前页面URL并替换路径
          const currentUrl = window.location.href
          const baseUrl = currentUrl.replace('purchaseDashboard', 'purchaseMaterial')

          // 获取参数
          const timeFlag = this.getTimeFlagByDimensionType(this.currentDimensionType)
          // 将数字转换为对应的分类字符串
          const itemTypeMap = {
            '1': 'CLASS1',
            '2': 'CLASS2',
            '3': 'CLASS3',
            '4': 'LEAF'
          }
          const itemType = itemTypeMap[this.selectedMaterialCategory] || 'CLASS1'
          const itemId = this.selectedMaterialItem || ''

          // 构建跳转URL
          const url = `${baseUrl}?timeFlag=${timeFlag}&itemType=${itemType}&itemId=${itemId}`

          console.log('跳转到采购物料页面:', url)
          console.log('跳转参数:', { timeFlag, itemType, itemId })

          // 在新窗口中打开页面
          window.open(url, '_blank')
        }
      }.bind(this))

      // 设置鼠标样式，表示可点击
      myChart.getZr().on('mousemove', function(params) {
        const pointInPixel = [params.offsetX, params.offsetY]
        if (myChart.containPixel('grid', pointInPixel)) {
          myChart.getZr().setCursorStyle('pointer')
        } else {
          myChart.getZr().setCursorStyle('default')
        }
      })

      myChart.setOption(option, true)
    },

    // 机旁库当前库存曲线图
    initFactoryStockChart() {
      console.log('initFactoryStockChart started with data:', JSON.stringify(this.factoryStockData))
      const myChart = this.reinitChart('factoryStockChart')
      if (!myChart) return

      let stockData = this.factoryStockData || []

      // 根据选中的物料类型筛选数据
      if (this.selectedFactoryMaterialType && this.selectedFactoryMaterialType !== '') {
        stockData = stockData.filter(item => item.class1 === this.selectedFactoryMaterialType)
      }

      // 物料类型映射
      const materialTypeMap = {
        'A': '通用备件',
        'B': '专用备件',
        'C': '材料类',
        'D': '原材料',
        'E': '辅耐材',
        'G': '办公'
      }

      // 获取所有日期并排序
      const allDates = [...new Set(stockData.map(item => item.kgDate))].sort()

      // 自适应日期范围 - 如果数据不足31天，使用实际数据的日期范围
      const dateRange = allDates.length > 0 ? allDates : []

      // 按class1分组数据
      const groupedData = {}
      stockData.forEach(item => {
        const materialType = item.class1
        if (!groupedData[materialType]) {
          groupedData[materialType] = {}
        }
        groupedData[materialType][item.kgDate] = parseFloat(item.stockMoney) || 0
      })

      // 生成系列数据
      const series = []
      const colors = ['#83bff6', '#ea7ccc', '#fac858', '#ee6666', '#73c0de', '#3ba272']
      let colorIndex = 0

      Object.keys(groupedData).forEach(materialType => {
        const materialName = materialTypeMap[materialType] || materialType
        const data = dateRange.map(date => {
          return groupedData[materialType][date] || 0
        })

        series.push({
          name: materialName,
          type: 'line',
          smooth: true,
          data: data,
          lineStyle: {
            width: 2,
            color: colors[colorIndex % colors.length]
          },
          itemStyle: {
            color: colors[colorIndex % colors.length]
          },
          symbol: 'circle',
          symbolSize: 4
        })
        colorIndex++
      })

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let tooltipText = params[0].axisValue + '<br/>'
            params.forEach(param => {
              if (param.value !== null && param.value !== undefined) {
                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 万元<br/>`
              }
            })
            return tooltipText
          }
        },
        legend: {
          data: series.map(s => s.name),
          textStyle: {
            color: '#fff'
          },
          top: '5%'
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '10%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dateRange.map(date => {
            // 格式化日期显示
            if (date && date.length === 8) {
              return date.substring(4, 6) + '/' + date.substring(6, 8)
            }
            return date
          }),
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee',
            interval: 'auto',
            rotate: 30
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '库存金额 (万元)',
          axisLabel: {
            color: '#eee',
            formatter: '{value}'
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: series
      }

      myChart.setOption(option, true)
    },

    // 实时库存图表
    initRealTimeInventoryChart() {
      console.log('initRealTimeInventoryChart started with data:', JSON.stringify(this.realTimeInventoryData))
      const myChart = this.reinitChart('realTimeInventoryChart')
      if (!myChart) return

      const inventoryData = this.realTimeInventoryData || []

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let tooltipText = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.value !== null && param.value !== undefined) {
                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 万元<br/>`
              }
            })
            return tooltipText
          }
        },
        legend: {
          data: ['中心仓库库存', '机旁库库存', '总库存'],
          textStyle: {
            color: '#fff'
          },
          top: '5%'
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '10%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: inventoryData.map(item => item.materialName || '未知物料'),
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee',
            interval: 0,
            rotate: 30
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '库存金额 (万元)',
          axisLabel: {
            color: '#eee',
            formatter: '{value}'
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            name: '中心仓库库存',
            type: 'bar',
            stack: '库存',
            data: inventoryData.map(item => parseFloat(item.centerInventoryAmount) || 0),
            itemStyle: {
              color: '#83bff6',
              borderRadius: [4, 4, 0, 0]
            }
          },
          {
            name: '机旁库库存',
            type: 'bar',
            stack: '库存',
            data: inventoryData.map(item => parseFloat(item.machineSideInventoryAmount) || 0),
            itemStyle: {
              color: '#ea7ccc',
              borderRadius: [0, 0, 0, 0]
            }
          },
          {
            name: '总库存',
            type: 'line',
            data: inventoryData.map(item => parseFloat(item.totalInventoryAmount) || 0),
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 3,
              color: '#5fd8b6'
            },
            itemStyle: {
              color: '#5fd8b6'
            }
          }
        ]
      }

      myChart.setOption(option, true)
    },

    // 矿焦煤库存图表（饼图+曲线图组合）
    initCokingCoalInventoryChart() {
      console.log('initCokingCoalInventoryChart started with data:', JSON.stringify(this.cokingCoalInventoryData))

      // 初始化饼图和曲线图
      this.initCokingCoalPieChart()
      this.initCokingCoalLineChart()
    },

    // 矿焦煤库存饼图（最新一天数据）
    initCokingCoalPieChart() {
      const pieChartDom = document.getElementById('cokingCoalPieChart')
      if (!pieChartDom) {
        console.error('找不到饼图DOM: cokingCoalPieChart')
        return null
      }

      // 清理现有实例
      if (this.chartInstances.cokingCoalPieChart) {
        this.chartInstances.cokingCoalPieChart.dispose()
      }

      const pieChart = echarts.init(pieChartDom)
      this.chartInstances.cokingCoalPieChart = pieChart

      const inventoryData = this.cokingCoalInventoryData || []

      // 根据选中的类型过滤数据
      let filteredData = inventoryData
      if (this.selectedCokingCoalType && this.selectedCokingCoalType !== '') {
        filteredData = inventoryData.filter(item => {
          return item.class2Name === this.selectedCokingCoalType ||
                 item.class2Name.includes(this.selectedCokingCoalType) ||
                 this.selectedCokingCoalType.includes(item.class2Name)
        })
      }

      // 找到最新日期
      let latestDate = ''
      filteredData.forEach(item => {
        if (item.purchaseCokingDailyDetailList) {
          item.purchaseCokingDailyDetailList.forEach(detail => {
            if (detail.instockDate > latestDate) {
              latestDate = detail.instockDate
            }
          })
        }
      })

      // 构建最新一天的饼图数据
      const pieData = []
      let totalInventory = 0

      filteredData.forEach(item => {
        if (item.purchaseCokingDailyDetailList) {
          const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)
          if (latestDetail) {
            const value = parseFloat(latestDetail.invQty) || 0
            totalInventory += value
            pieData.push({
              name: item.class2Name || '未知物料',
              value: value
            })
          }
        }
      })

      // 计算百分比
      pieData.forEach(item => {
        item.percentage = totalInventory > 0 ? ((item.value / totalInventory) * 100).toFixed(1) : 0
      })

      // 获取统一的颜色映射
      const colorMap = this.getCokingCoalMaterialColorMap()

      // 为饼图数据分配颜色
      pieData.forEach(item => {
        item.itemStyle = {
          color: colorMap[item.name] || '#83bff6',
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 1
        }
      })

      const pieOption = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return `${params.name}<br/>库存量: ${params.value.toFixed(2)} 吨<br/>占比: ${params.data.percentage}%`
          }
        },
        legend: {
          orient: 'vertical',
          left: '5%',
          top: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 10
          },
          itemGap: 6,
          itemWidth: 10,
          itemHeight: 10
        },
        series: [
          {
            name: '矿焦煤库存',
            type: 'pie',
            radius: ['30%', '60%'],
            center: ['65%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '12',
                fontWeight: 'bold',
                color: '#fff',
                formatter: function(params) {
                  return `${params.name}\n${params.data.percentage}%`
                }
              }
            },
            data: pieData
          }
        ]
      }

      pieChart.setOption(pieOption, true)
    },

    // 矿焦煤库存曲线图（近七天趋势）
    initCokingCoalLineChart() {
      const lineChartDom = document.getElementById('cokingCoalLineChart')
      if (!lineChartDom) {
        console.error('找不到曲线图DOM: cokingCoalLineChart')
        return null
      }

      // 清理现有实例
      if (this.chartInstances.cokingCoalLineChart) {
        this.chartInstances.cokingCoalLineChart.dispose()
      }

      const lineChart = echarts.init(lineChartDom)
      this.chartInstances.cokingCoalLineChart = lineChart

      const inventoryData = this.cokingCoalInventoryData || []

      // 根据选中的类型过滤数据
      let filteredData = inventoryData
      if (this.selectedCokingCoalType && this.selectedCokingCoalType !== '') {
        filteredData = inventoryData.filter(item => {
          return item.class2Name === this.selectedCokingCoalType ||
                 item.class2Name.includes(this.selectedCokingCoalType) ||
                 this.selectedCokingCoalType.includes(item.class2Name)
        })
      }

      // 提取所有日期并排序
      const allDates = new Set()
      filteredData.forEach(item => {
        if (item.purchaseCokingDailyDetailList) {
          item.purchaseCokingDailyDetailList.forEach(detail => {
            allDates.add(detail.instockDate)
          })
        }
      })

      const sortedDates = Array.from(allDates).sort()

      // 格式化日期显示（从yyyyMMdd转换为MM-dd）
      const formattedDates = sortedDates.map(dateStr => {
        if (dateStr && dateStr.length === 8) {
          const month = dateStr.substring(4, 6)
          const day = dateStr.substring(6, 8)
          return `${month}-${day}`
        }
        return dateStr
      })

      // 构建每个类型的曲线数据
      const seriesData = []
      const legendData = []

      // 获取统一的颜色映射
      const colorMap = this.getCokingCoalMaterialColorMap()

      filteredData.forEach((item, index) => {
        const typeName = item.class2Name || '未知物料'

        // 为每个日期构建数据点
        const lineData = sortedDates.map(date => {
          const detail = item.purchaseCokingDailyDetailList?.find(d => d.instockDate === date)
          return detail ? parseFloat(detail.invQty) || 0 : null
        })

        // 使用统一的颜色映射
        const materialColor = colorMap[typeName] || '#83bff6'

        const seriesItem = {
          name: typeName,
          type: 'line',
          data: lineData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: materialColor
          },
          itemStyle: {
            color: materialColor
          },
          connectNulls: false
        }

        seriesData.push(seriesItem)
        legendData.push(typeName)
      })

      const lineOption = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function(params) {
            let tooltipText = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.value !== null && param.value !== undefined) {
                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 吨<br/>`
              }
            })
            return tooltipText
          }
        },
        legend: {
          data: legendData,
          textStyle: {
            color: '#fff'
          },
          top: '5%'
        },
        grid: {
          left: '8%',
          right: '5%',
          bottom: '10%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: formattedDates,
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee'
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '库存量 (吨)',
          axisLabel: {
            color: '#eee',
            formatter: '{value}'
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: seriesData
      }

      lineChart.setOption(lineOption, true)
    }
  }
}
