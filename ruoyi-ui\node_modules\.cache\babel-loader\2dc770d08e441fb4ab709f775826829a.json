{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\dashboard\\index.vue", "mtime": 1756456493915}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_qualityCostDetail", "_dashboard", "name", "data", "getDefaultYearMonth", "now", "Date", "year", "getFullYear", "month", "getMonth", "day", "getDate", "hour", "getHours", "prevMonth", "prevYear", "concat", "String", "padStart", "updateTime", "charts", "costCenter", "accountingPeriod", "containType", "costCenterOptions", "costCenterLoading", "qualityCostDetail", "qualityCostData", "watch", "handler", "console", "log", "refreshChartData", "mounted", "_this", "getCostCenterList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resizeObserver", "ResizeObserver", "resi<PERSON><PERSON><PERSON><PERSON>", "observe", "$el", "window", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "Object", "values", "for<PERSON>ach", "chart", "dispose", "disconnect", "removeEventListener", "methods", "isNegativePercentage", "percentage", "toString", "startsWith", "getPercentageClass", "formatNumber", "num", "undefined", "number", "Number", "isNaN", "parseFloat", "toFixed", "addThousandSeparator", "replace", "formatTonnage", "result", "formatAmount", "formatUnitCost", "getFactoryRejectionChartDetail", "_this2", "params", "yearMonth", "then", "response", "updateFactoryRejectionChart", "catch", "error", "$message", "getFactoryScrapChartDetail", "_this3", "updateFactoryScrapChart", "getFactoryContractChartDetail", "_this4", "updateFactoryContractChart", "getFactoryReturnChartDetail", "_this5", "updateFactoryReturnChart", "factoryReturnChart", "factoryReturnMap", "xAxisData", "seriesData", "colors", "dataItems", "entries", "map", "_ref", "_ref2", "_slicedToArray2", "default", "key", "value", "length", "sort", "a", "b", "item", "index", "push", "itemStyle", "color", "warn", "option", "grid", "left", "right", "bottom", "containLabel", "tooltip", "trigger", "axisPointer", "type", "backgroundColor", "borderColor", "textStyle", "formatter", "formattedValue", "marker", "seriesName", "xAxis", "axisLabel", "interval", "rotate", "align", "axisLine", "lineStyle", "yAxis", "nameTextStyle", "splitLine", "series", "setOption", "factoryRejectionChart", "factoryRejectionMap", "_ref3", "_ref4", "factoryScrapChart", "factoryScrapMap", "_ref5", "_ref6", "factoryContractChart", "factoryContractMap", "_ref7", "_ref8", "getWaterfallChartDetail", "_this6", "updateWaterfallChart", "<PERSON><PERSON>hart", "rescueProject", "_ref9", "_ref0", "topTenItems", "slice", "updateScrapLossChart", "scrapLossChart", "_typeof2", "keys", "scrapLossMap", "_ref1", "_ref10", "_ref11", "_ref12", "scrapLoss", "_ref13", "_ref14", "scrapLossProject", "_ref15", "_ref16", "_ref17", "_ref18", "updateQualityObjectionChart", "qualityObjectionChart", "qualityObjectionLossMap", "_ref19", "_ref20", "_ref21", "_ref22", "_ref23", "_ref24", "getQualityObjectionLossDetail", "_this7", "getScrapLossChartDetailsDetail", "_this8", "getExternalCostDetail", "_this9", "updateExternalCostDetailChart", "getInternalCostDetail", "_this0", "updateInternalCostDetailChart", "internalCostDetailChart", "allDataItems", "contraction<PERSON>oss", "_ref25", "_ref26", "numValue", "rescueCost", "_ref27", "_ref28", "revisionLoss", "_ref29", "_ref30", "_ref31", "_ref32", "yAxisData", "reverse", "externalCostDetailChart", "customerClaimCost", "_ref33", "_ref34", "qualityObjectionFeeCost", "_ref35", "_ref36", "qualityObjectionTravelCost", "_ref37", "_ref38", "returnLoss", "_ref39", "_ref40", "getQualityCostDetail", "_this1", "getMultiLineChartData", "_this10", "updateMultiLineChart", "getComboChartDetail", "_this11", "updateComboChart", "comboChart", "months", "generateComboChartMonthsByAccountingPeriod", "yearMonths", "generateYearMonthsByAccountingPeriod", "failureCostData", "controllingCostData", "failureValue", "failureCostMap", "controllingValue", "controllingCostMap", "legend", "boundaryGap", "smooth", "width", "symbol", "symbolSize", "generateComboChartMonths", "currentDate", "i", "date", "generateYearMonths", "_this$accountingPerio", "split", "_this$accountingPerio2", "accountingDate", "monthNum", "_this$accountingPerio3", "_this$accountingPerio4", "yearNum", "getPieChartData", "_this12", "update<PERSON>ie<PERSON>hart", "_this13", "costCenterlist", "$nextTick", "finally", "<PERSON><PERSON><PERSON>", "getOption", "preventionCost", "appraisalCost", "internalCost", "externalCost", "multiLineChart", "preventionData", "processMapData", "preventionCostMap", "appraisalData", "appraisalCostMap", "internalData", "internalCostMap", "externalData", "externalCostMap", "costMap", "convertToWanYuan", "arguments", "Array", "fill", "generateMonthLabels", "parseInt", "handleQuery", "reset<PERSON><PERSON>y", "success", "THEME", "businessColors", "light", "gradient", "offset", "init", "$refs", "setPieChartOption", "setMultiLineChartOption", "setExternalCostDetailChartOption", "setInternalCostDetailChartOption", "setWaterfallChartOption", "setComboChartOption", "setScrapLossChartOption", "setQualityObjectionChartOption", "setFactoryRejectionChartOption", "setFactoryScrapChartOption", "setFactoryContractChartOption", "setFactoryReturnChartOption", "percent", "top", "fontSize", "radius", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "labelLine", "label", "setParetoChartOption", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "yAxisIndex", "stack", "borderWidth", "setDualYChartOption", "dualYChart", "resize"], "sources": ["src/views/qualityCost/dashboard/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"quality-cost-dashboard\">\r\n    <header class=\"header\">\r\n      <div class=\"header-wrapper\">\r\n        <h1>兴澄特钢质量成本看板</h1>\r\n        <!-- 标题右下角筛选区域 -->\r\n        <div class=\"header-filters\">\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">成本中心：</span>\r\n            <el-select v-model=\"costCenter\" placeholder=\"请选择成本中心\" style=\"width: 160px;\" :loading=\"costCenterLoading\"\r\n              size=\"small\">\r\n              <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </div>\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">会计期：</span>\r\n            <el-date-picker v-model=\"accountingPeriod\" type=\"month\" placeholder=\"请选择年月\" format=\"yyyy-MM\"\r\n              value-format=\"yyyy-MM\" style=\"width: 130px;\" size=\"small\">\r\n            </el-date-picker>\r\n          </div>\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">质量成本类型：</span>\r\n            <el-select v-model=\"containType\" placeholder=\"请选择质量成本类型\" style=\"width: 130px;\" size=\"small\">\r\n              <el-option label=\"含不列入项\" :value=\"2\"></el-option>\r\n              <el-option label=\"不含列入项\" :value=\"1\"></el-option>\r\n            </el-select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- <p>数据更新时间: {{ updateTime }}</p> -->\r\n    </header>\r\n\r\n    <div class=\"dashboard-grid\">\r\n      <!-- 第四类：核心绩效指标（KPI）看板 -->\r\n      <div class=\"kpi-grid\">\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">{{ costCenter === 'JYXCTZG' ? '销量' : '产量' }}</div>\r\n          <div class=\"value\">{{ formatTonnage(containType === 2 ? qualityCostData.allcTon : qualityCostData.costTon) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costTonUpPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costTonUpPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costTonUpPercent)\">{{ qualityCostDetail.costTonUpPercent\r\n            }}\r\n              vs 上期</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">总金额</div>\r\n          <div class=\"value\">{{ formatAmount(containType === 2 ? qualityCostData.allcEx : qualityCostData.costEx) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costExPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costExPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costExPercent)\">{{ qualityCostDetail.costExPercent }} vs\r\n              上期</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">吨钢成本</div>\r\n          <div class=\"value\">{{ formatUnitCost(containType === 2 ? qualityCostData.allcPerEx : qualityCostData.costPerEx) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costPerExPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costPerExPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costPerExPercent)\">{{ qualityCostDetail.costPerExPercent\r\n            }}\r\n              vs 上期</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 第2行：质量成本四大类别占比（占1/2宽度）+ 四大质量成本趋势（占1/2宽度） -->\r\n      <div class=\"chart-container\" style=\"grid-column: span 2;\">\r\n        <h3>1. 质量成本四大类别占比</h3>\r\n        <div ref=\"pieChart\" class=\"chart\"></div>\r\n      </div>\r\n      <div class=\"chart-container\" style=\"grid-column: span 2;\">\r\n        <h3>2. 四大质量成本趋势</h3>\r\n        <div ref=\"multiLineChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第3行：外部损失成本构成图表 + 内部损失成本构成图表 - 每个占据行宽的50% -->\r\n      <div class=\"chart-container large-chart\" style=\"grid-column: span 2;\">\r\n        <h3>3. 外部损失成本构成</h3>\r\n        <div ref=\"externalCostDetailChart\" class=\"chart\"></div>\r\n      </div>\r\n      <div class=\"chart-container large-chart\" style=\"grid-column: span 2;\">\r\n        <h3>4. 内部损失成本构成</h3>\r\n        <div ref=\"internalCostDetailChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第3行：产品挽救处理成本分析图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>5. 产品挽救处理成本分析</h3>\r\n        <div ref=\"waterfallChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第4行：产品报废损失明细图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>6. 产品报废损失明细</h3>\r\n        <div ref=\"scrapLossChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第5行：产品质量异议损失明细图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>7. 产品质量异议损失明细</h3>\r\n        <div ref=\"qualityObjectionChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第7行：控制成本 vs 失败成本对比（占满整行） -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>8. \"控制成本\" vs \"失败成本\" 对比</h3>\r\n        <div ref=\"comboChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第8行：各分厂改判汇总图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>9. 各分厂改判汇总</h3>\r\n        <div ref=\"factoryRejectionChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第9行：各分厂报废汇总图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>10. 各分厂报废汇总</h3>\r\n        <div ref=\"factoryScrapChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第10行：各分厂脱合同汇总图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>11. 各分厂脱合同汇总</h3>\r\n        <div ref=\"factoryContractChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第11行：各分厂退货汇总图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>12. 各分厂退货汇总</h3>\r\n        <div ref=\"factoryReturnChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport { costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { getPieChartData, getMultiLineChartData, getQualityCostDetail, getExternalCostDetail, getInternalCostDetail, getComboChartDetail,getWaterfallChartDetail,getScrapLossChartDetailsDetail,getQualityObjectionLossDetail,getFactoryRejectionChartDetail,getFactoryScrapChartDetail,getFactoryContractChartDetail,getFactoryReturnChartDetail } from \"@/api/qualityCost/dashboard\";\r\n\r\nexport default {\r\n  name: 'QualityCostDashboard',\r\n  data() {\r\n    // 获取默认会计期（上个月）\r\n    const getDefaultYearMonth = () => {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    };\r\n\r\n    return {\r\n      updateTime: '2023-10-27 10:00',\r\n      charts: {},\r\n      // 成本中心和会计期\r\n      costCenter: '',\r\n      accountingPeriod: getDefaultYearMonth(),\r\n      // 质量成本类型，默认值为1（不含列入项）\r\n      containType: 1,\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      costCenterLoading: false,\r\n      qualityCostDetail: {},\r\n      qualityCostData: {}\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听成本中心变化\r\n    costCenter: {\r\n      handler() {\r\n        console.log('成本中心变化:', this.costCenter);\r\n        this.refreshChartData();\r\n      }\r\n    },\r\n    // 监听会计期变化\r\n    accountingPeriod: {\r\n      handler() {\r\n        console.log('会计期变化:', this.accountingPeriod);\r\n        this.refreshChartData();\r\n      }\r\n    },\r\n    // 监听质量成本类型变化\r\n    containType: {\r\n      handler() {\r\n        console.log('质量成本类型变化:', this.containType);\r\n        this.refreshChartData();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCostCenterList();\r\n    //质量成本四大类别占比\r\n\r\n    this.initCharts();\r\n    this.resizeObserver = new ResizeObserver(() => {\r\n      this.resizeCharts()\r\n    })\r\n    this.resizeObserver.observe(this.$el)\r\n    window.addEventListener('resize', this.resizeCharts)\r\n  },\r\n  beforeDestroy() {\r\n    // 销毁所有图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      if (chart) {\r\n        chart.dispose()\r\n      }\r\n    })\r\n    if (this.resizeObserver) {\r\n      this.resizeObserver.disconnect()\r\n    }\r\n    window.removeEventListener('resize', this.resizeCharts)\r\n  },\r\n  methods: {\r\n    // 判断百分比是否为负数\r\n    isNegativePercentage(percentage) {\r\n      if (!percentage) return false;\r\n      return percentage.toString().startsWith('-');\r\n    },\r\n\r\n    // 根据百分比正负值返回对应的CSS类\r\n    getPercentageClass(percentage) {\r\n      if (!percentage) return 'neutral';\r\n      return this.isNegativePercentage(percentage) ? 'negative' : 'positive';\r\n    },\r\n\r\n    // 格式化数字，最多保留两位小数\r\n    formatNumber(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0';\r\n      }\r\n      // 使用toFixed(2)保留两位小数，然后用parseFloat去掉末尾的0\r\n      return parseFloat(number.toFixed(2)).toString();\r\n    },\r\n\r\n    // 添加千分位分隔符\r\n    addThousandSeparator(num) {\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    },\r\n\r\n    // 格式化产量/销量为万吨单位\r\n    formatTonnage(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0万吨';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0万吨';\r\n      }\r\n      // 转换为万吨并保留两位小数，添加千分位分隔符\r\n      const result = (number / 10000).toFixed(2);\r\n      return `${this.addThousandSeparator(result)}万吨`;\r\n    },\r\n\r\n    // 格式化总金额为万元单位\r\n    formatAmount(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0万元';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0万元';\r\n      }\r\n      // 转换为万元并保留两位小数，添加千分位分隔符\r\n      const result = (number / 10000).toFixed(2);\r\n      return `${this.addThousandSeparator(result)}万元`;\r\n    },\r\n\r\n    // 格式化吨钢成本为元/吨单位\r\n    formatUnitCost(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0元/吨';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0元/吨';\r\n      }\r\n      // 保留两位小数并添加单位，添加千分位分隔符\r\n      const result = number.toFixed(2);\r\n      return `${this.addThousandSeparator(result)}元/吨`;\r\n    },\r\n\r\n    getFactoryRejectionChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getFactoryRejectionChartDetail(params).then(response => {\r\n        console.log('getFactoryRejectionChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新WaterfallChart柱状图\r\n          this.updateFactoryRejectionChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取WaterfallChart数据失败:', error);\r\n        this.$message.error('获取WaterfallChart数据失败');\r\n      });\r\n    },\r\n\r\n    getFactoryScrapChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getFactoryScrapChartDetail(params).then(response => {\r\n        console.log('getFactoryScrapChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新各分厂报废汇总柱状图\r\n          this.updateFactoryScrapChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取各分厂报废汇总数据失败:', error);\r\n        this.$message.error('获取各分厂报废汇总数据失败');\r\n      });\r\n    },\r\n\r\n    getFactoryContractChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getFactoryContractChartDetail(params).then(response => {\r\n        console.log('getFactoryContractChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新各分厂脱合同汇总柱状图\r\n          this.updateFactoryContractChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取各分厂脱合同汇总数据失败:', error);\r\n        this.$message.error('获取各分厂脱合同汇总数据失败');\r\n      });\r\n    },\r\n\r\n    getFactoryReturnChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getFactoryReturnChartDetail(params).then(response => {\r\n        console.log('getFactoryReturnChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新各分厂退货汇总柱状图\r\n          this.updateFactoryReturnChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取各分厂退货汇总数据失败:', error);\r\n        this.$message.error('获取各分厂退货汇总数据失败');\r\n      });\r\n    },\r\n\r\n    updateFactoryReturnChart(data) {\r\n      if (this.charts.factoryReturnChart && data) {\r\n        console.log('接收到的FactoryReturnChart数据:', data);\r\n        console.log('factoryReturnMap:', data.factoryReturnMap);\r\n\r\n        // 处理各分厂退货数据，单位为元\r\n        const xAxisData = [];      // x轴分厂名称数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];\r\n\r\n        if (data.factoryReturnMap) {\r\n          // 将factoryReturnMap对象转换为数组，单位为元，保留两位小数\r\n          const dataItems = Object.entries(data.factoryReturnMap).map(([key, value]) => ({\r\n            name: key,    // 分厂名称\r\n            value: (Number(value) || 0).toFixed(2)  // 退货金额，单位为元，保留两位小数\r\n          }));\r\n\r\n          console.log('处理后的分厂退货数据:', dataItems);\r\n\r\n          if (dataItems.length > 0) {\r\n            // 按数值从高到低排序\r\n            dataItems.sort((a, b) => b.value - a.value);\r\n\r\n            // 分离排序后的数据，确保每个柱子颜色不同\r\n            dataItems.forEach((item, index) => {\r\n              xAxisData.push(item.name);\r\n              seriesData.push({\r\n                value: item.value,\r\n                itemStyle: { color: colors[index % colors.length] }\r\n              });\r\n            });\r\n          } else {\r\n            console.warn('没有找到有效的分厂退货数据');\r\n            // 添加默认数据以便测试\r\n            xAxisData.push('无数据');\r\n            seriesData.push({\r\n              value: 0,\r\n              itemStyle: { color: colors[0] }\r\n            });\r\n          }\r\n        } else {\r\n          console.warn('factoryReturnMap数据不存在');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴分厂数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              formatter: function(value) {\r\n                // 在Y轴标签上也显示千分位分隔符\r\n                return value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              }\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '退货金额',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.factoryReturnChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('FactoryReturnChart柱状图数据已更新');\r\n      } else {\r\n        console.error('FactoryReturnChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    updateFactoryRejectionChart(data) {\r\n      if (this.charts.factoryRejectionChart && data) {\r\n        console.log('接收到的FactoryRejectionChart数据:', data);\r\n        console.log('factoryRejectionMap:', data.factoryRejectionMap);\r\n\r\n        // 处理各分厂改判数据，单位为元\r\n        const xAxisData = [];      // x轴分厂名称数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];\r\n\r\n        if (data.factoryRejectionMap) {\r\n          // 将factoryRejectionMap对象转换为数组，单位为元，保留两位小数\r\n          const dataItems = Object.entries(data.factoryRejectionMap).map(([key, value]) => ({\r\n            name: key,    // 分厂名称\r\n            value: (Number(value) || 0).toFixed(2)  // 改判金额，单位为元，保留两位小数\r\n          }));\r\n\r\n          console.log('处理后的分厂改判数据:', dataItems);\r\n\r\n          if (dataItems.length > 0) {\r\n            // 按数值从高到低排序\r\n            dataItems.sort((a, b) => b.value - a.value);\r\n\r\n            // 分离排序后的数据，确保每个柱子颜色不同\r\n            dataItems.forEach((item, index) => {\r\n              xAxisData.push(item.name);\r\n              seriesData.push({\r\n                value: item.value,\r\n                itemStyle: { color: colors[index % colors.length] }\r\n              });\r\n            });\r\n          } else {\r\n            console.warn('没有找到有效的分厂改判数据');\r\n            // 添加默认数据以便测试\r\n            xAxisData.push('无数据');\r\n            seriesData.push({\r\n              value: 0,\r\n              itemStyle: { color: colors[0] }\r\n            });\r\n          }\r\n        } else {\r\n          console.warn('factoryRejectionMap数据不存在');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴分厂数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              formatter: function(value) {\r\n                // 在Y轴标签上也显示千分位分隔符\r\n                return value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              }\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '改判金额',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.factoryRejectionChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('FactoryRejectionChart柱状图数据已更新');\r\n      } else {\r\n        console.error('FactoryRejectionChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    updateFactoryScrapChart(data) {\r\n      if (this.charts.factoryScrapChart && data) {\r\n        console.log('接收到的FactoryScrapChart数据:', data);\r\n        console.log('factoryScrapMap:', data.factoryScrapMap);\r\n\r\n        // 处理各分厂报废数据，单位为元\r\n        const xAxisData = [];      // x轴分厂名称数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];\r\n\r\n        if (data.factoryScrapMap) {\r\n          // 将factoryScrapMap对象转换为数组，单位为元，保留两位小数\r\n          const dataItems = Object.entries(data.factoryScrapMap).map(([key, value]) => ({\r\n            name: key,    // 分厂名称\r\n            value: (Number(value) || 0).toFixed(2)  // 报废金额，单位为元，保留两位小数\r\n          }));\r\n\r\n          console.log('处理后的分厂报废数据:', dataItems);\r\n\r\n          if (dataItems.length > 0) {\r\n            // 按数值从高到低排序\r\n            dataItems.sort((a, b) => b.value - a.value);\r\n\r\n            // 分离排序后的数据，确保每个柱子颜色不同\r\n            dataItems.forEach((item, index) => {\r\n              xAxisData.push(item.name);\r\n              seriesData.push({\r\n                value: item.value,\r\n                itemStyle: { color: colors[index % colors.length] }\r\n              });\r\n            });\r\n          } else {\r\n            console.warn('没有找到有效的分厂报废数据');\r\n            // 添加默认数据以便测试\r\n            xAxisData.push('无数据');\r\n            seriesData.push({\r\n              value: 0,\r\n              itemStyle: { color: colors[0] }\r\n            });\r\n          }\r\n        } else {\r\n          console.warn('factoryScrapMap数据不存在');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴分厂数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              formatter: function(value) {\r\n                // 在Y轴标签上也显示千分位分隔符\r\n                return value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              }\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '报废金额',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.factoryScrapChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('FactoryScrapChart柱状图数据已更新');\r\n      } else {\r\n        console.error('FactoryScrapChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    updateFactoryContractChart(data) {\r\n      if (this.charts.factoryContractChart && data) {\r\n        console.log('接收到的FactoryContractChart数据:', data);\r\n        console.log('factoryContractMap:', data.factoryContractMap);\r\n\r\n        // 处理各分厂脱合同数据，单位为元\r\n        const xAxisData = [];      // x轴分厂名称数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];\r\n\r\n        if (data.factoryContractMap) {\r\n          // 将factoryContractMap对象转换为数组，单位为元，保留两位小数\r\n          const dataItems = Object.entries(data.factoryContractMap).map(([key, value]) => ({\r\n            name: key,    // 分厂名称\r\n            value: (Number(value) || 0).toFixed(2)  // 脱合同金额，单位为元，保留两位小数\r\n          }));\r\n\r\n          console.log('处理后的分厂脱合同数据:', dataItems);\r\n\r\n          if (dataItems.length > 0) {\r\n            // 按数值从高到低排序\r\n            dataItems.sort((a, b) => b.value - a.value);\r\n\r\n            // 分离排序后的数据，确保每个柱子颜色不同\r\n            dataItems.forEach((item, index) => {\r\n              xAxisData.push(item.name);\r\n              seriesData.push({\r\n                value: item.value,\r\n                itemStyle: { color: colors[index % colors.length] }\r\n              });\r\n            });\r\n          } else {\r\n            console.warn('没有找到有效的分厂脱合同数据');\r\n            // 添加默认数据以便测试\r\n            xAxisData.push('无数据');\r\n            seriesData.push({\r\n              value: 0,\r\n              itemStyle: { color: colors[0] }\r\n            });\r\n          }\r\n        } else {\r\n          console.warn('factoryContractMap数据不存在');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴分厂数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              formatter: function(value) {\r\n                // 在Y轴标签上也显示千分位分隔符\r\n                return value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              }\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '脱合同金额',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.factoryContractChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('FactoryContractChart柱状图数据已更新');\r\n      } else {\r\n        console.error('FactoryContractChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n\r\n    getWaterfallChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getWaterfallChartDetail(params).then(response => {\r\n        console.log('getWaterfallChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新WaterfallChart柱状图\r\n          this.updateWaterfallChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取WaterfallChart数据失败:', error);\r\n        this.$message.error('获取WaterfallChart数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新WaterfallChart柱状图\r\n    updateWaterfallChart(data) {\r\n      if (this.charts.waterfallChart && data) {\r\n        console.log('接收到的WaterfallChart数据:', data);\r\n\r\n        // 处理rescueProject数据\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n\r\n        let dataItems = [];\r\n\r\n        if (data.rescueProject) {\r\n          // 将rescueProject对象转换为数组，转换为万元\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,    // 第一项为维度名称\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)  // 第二项为对应维度的值，转换为万元\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '挽救处理成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.waterfallChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('WaterfallChart柱状图数据已更新');\r\n      } else {\r\n        console.error('WaterfallChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    // 更新ScrapLossChart柱状图\r\n    updateScrapLossChart(data) {\r\n      if (this.charts.scrapLossChart && data) {\r\n        console.log('接收到的ScrapLossChart数据:', data);\r\n        console.log('数据类型:', typeof data);\r\n        console.log('数据键:', Object.keys(data));\r\n\r\n        // 处理报废损失数据，尝试多种可能的数据结构\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n        let dataItems = [];\r\n\r\n        // 尝试不同的数据结构，转换为万元\r\n        if (data.scrapLossMap) {\r\n          // 情况1: 使用scrapLossMap数据（根据实际API返回的数据结构）\r\n          console.log('使用scrapLossMap数据');\r\n          dataItems = Object.entries(data.scrapLossMap).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.rescueProject) {\r\n          // 情况2: 使用rescueProject数据（与WaterfallChart相同）\r\n          console.log('使用rescueProject数据');\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.scrapLoss) {\r\n          // 情况3: 使用scrapLoss数据\r\n          console.log('使用scrapLoss数据');\r\n          dataItems = Object.entries(data.scrapLoss).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.scrapLossProject) {\r\n          // 情况4: 使用scrapLossProject数据\r\n          console.log('使用scrapLossProject数据');\r\n          dataItems = Object.entries(data.scrapLossProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else {\r\n          // 情况5: 直接使用data作为对象\r\n          console.log('直接使用data对象');\r\n          dataItems = Object.entries(data).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '报废损失成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.scrapLossChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('ScrapLossChart柱状图数据已更新');\r\n      } else {\r\n        console.error('ScrapLossChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    // 更新QualityObjectionChart柱状图\r\n    updateQualityObjectionChart(data) {\r\n      if (this.charts.qualityObjectionChart && data) {\r\n        console.log('接收到的QualityObjectionChart数据:', data);\r\n        console.log('数据类型:', typeof data);\r\n        console.log('数据键:', Object.keys(data));\r\n\r\n        // 处理质量异议损失数据，尝试多种可能的数据结构\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n        let dataItems = [];\r\n\r\n        // 尝试不同的数据结构，转换为万元\r\n        if (data.qualityObjectionLossMap) {\r\n          // 情况1: 使用qualityObjectionLossMap数据\r\n          console.log('使用qualityObjectionLossMap数据');\r\n          dataItems = Object.entries(data.qualityObjectionLossMap).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.rescueProject) {\r\n          // 情况2: 使用rescueProject数据（与WaterfallChart相同）\r\n          console.log('使用rescueProject数据');\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else {\r\n          // 情况3: 直接使用data作为对象\r\n          console.log('直接使用data对象');\r\n          dataItems = Object.entries(data).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '质量异议损失成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.qualityObjectionChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('QualityObjectionChart柱状图数据已更新');\r\n      } else {\r\n        console.error('QualityObjectionChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    getQualityObjectionLossDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getQualityObjectionLossDetail(params).then(response => {\r\n        console.log('getQualityObjectionLossDetail:', response);\r\n        if (response.data) {\r\n          // 更新QualityObjectionChart柱状图\r\n          this.updateQualityObjectionChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取QualityObjectionChart数据失败:', error);\r\n        this.$message.error('获取产品质量异议损失明细数据失败');\r\n      });\r\n    },\r\n\r\n    getScrapLossChartDetailsDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getScrapLossChartDetailsDetail(params).then(response => {\r\n        console.log('getScrapLossChartDetailsDetail:', response);\r\n        if (response.data) {\r\n          // 更新ScrapLossChart柱状图\r\n          this.updateScrapLossChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取ScrapLossChart数据失败:', error);\r\n        this.$message.error('获取产品报废损失明细数据失败');\r\n      });\r\n    },\r\n\r\n    getExternalCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getExternalCostDetail(params).then(response => {\r\n        console.log('getExternalCostDetail:', response);\r\n        if (response.data) {\r\n          // 更新外部损失成本构成图表\r\n          this.updateExternalCostDetailChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取外部损失成本数据失败:', error);\r\n        this.$message.error('获取外部损失成本数据失败');\r\n      });\r\n    },\r\n\r\n    getInternalCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getInternalCostDetail(params).then(response => {\r\n        console.log('getInternalCostDetail:', response);\r\n        if (response.data) {\r\n          // 更新内部损失成本构成图表\r\n          this.updateInternalCostDetailChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取内部损失成本数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新内部损失成本构成图表\r\n    updateInternalCostDetailChart(data) {\r\n      if (this.charts.internalCostDetailChart && data) {\r\n        console.log('接收到的内部损失成本数据:', data);\r\n\r\n        // 收集所有数据项\r\n        const allDataItems = [];\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD'];\r\n\r\n        // 处理各个成本项，收集到统一数组中，转换为万元\r\n        if (data.contractionLoss) {\r\n          Object.entries(data.contractionLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.rescueCost) {\r\n          Object.entries(data.rescueCost).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.revisionLoss) {\r\n          Object.entries(data.revisionLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.scrapLoss) {\r\n          Object.entries(data.scrapLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        console.log('收集到的所有数据项（包含0值）:', allDataItems);\r\n\r\n        // 按数值从高到低排序（0值会排在负值之前，正值之后）\r\n        allDataItems.sort((a, b) => b.value - a.value);\r\n\r\n        console.log('排序后的数据（包含0值）:', allDataItems);\r\n\r\n        // 分离排序后的数据，反转顺序使金额大的显示在上面\r\n        const yAxisData = [];\r\n        const seriesData = [];\r\n\r\n        // 反转数组，使金额大的显示在图表上方\r\n        allDataItems.reverse().forEach((item, index) => {\r\n          yAxisData.push(item.name);\r\n          seriesData.push({\r\n            value: item.value,\r\n            itemStyle: { color: colors[index % colors.length] }\r\n          });\r\n        });\r\n\r\n        console.log('y轴数据:', yAxisData);\r\n        console.log('系列数据（包含0值）:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          yAxis: {\r\n            type: 'category',\r\n            data: yAxisData,\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [{\r\n            name: '金额 (万元)',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.internalCostDetailChart.setOption(option);\r\n        console.log('内部损失成本构成图表数据已更新（包含0值，按数值从高到低排序）');\r\n      }\r\n    },\r\n\r\n    // 更新外部损失成本构成图表\r\n    updateExternalCostDetailChart(data) {\r\n      if (this.charts.externalCostDetailChart && data) {\r\n        // 收集所有数据项\r\n        const allDataItems = [];\r\n        const colors = ['#FCA5A5', '#FDE68A', '#86EFAC', '#93C5FD', '#C4B5FD'];\r\n\r\n        // 处理各个成本项，收集到统一数组中，转换为万元\r\n        if (data.customerClaimCost) {\r\n          Object.entries(data.customerClaimCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.qualityObjectionFeeCost) {\r\n          Object.entries(data.qualityObjectionFeeCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.qualityObjectionTravelCost) {\r\n          Object.entries(data.qualityObjectionTravelCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.returnLoss) {\r\n          Object.entries(data.returnLoss).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        // 按数值从高到低排序\r\n        allDataItems.sort((a, b) => b.value - a.value);\r\n\r\n        // 分离排序后的数据，反转顺序使金额大的显示在上面\r\n        const yAxisData = [];\r\n        const seriesData = [];\r\n\r\n        // 反转数组，使金额大的显示在图表上方\r\n        allDataItems.reverse().forEach((item, index) => {\r\n          yAxisData.push(item.name);\r\n          seriesData.push({\r\n            value: item.value,\r\n            itemStyle: { color: colors[index % colors.length] }\r\n          });\r\n        });\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          yAxis: {\r\n            type: 'category',\r\n            data: yAxisData,\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [{\r\n            name: '金额 (万元)',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.externalCostDetailChart.setOption(option);\r\n        console.log('外部损失成本构成图表数据已更新（已按数值从高到低排序）');\r\n      }\r\n    },\r\n\r\n    getQualityCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getQualityCostDetail(params).then(response => {\r\n        console.log('getQualityCostDetail:', response);\r\n        if (response.data) {\r\n          this.qualityCostData = response.data.qualityCostData;\r\n          this.qualityCostDetail = response.data;\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n\r\n    //质量成本四大类别占比\r\n    getMultiLineChartData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getMultiLineChartData(params).then(response => {\r\n        console.log('getMultiLineChartData:', response);\r\n        if (response.data) {\r\n          this.updateMultiLineChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n\r\n    getComboChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getComboChartDetail(params).then(response => {\r\n        console.log('getComboChartDetail:', response);\r\n        if (response.data) {\r\n          this.updateComboChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取ComboChart数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新ComboChart图表\r\n    updateComboChart(data) {\r\n      if (this.charts.comboChart && data) {\r\n        console.log('接收到的ComboChart数据:', data);\r\n\r\n        // 基于会计期生成近6个月的月份标签作为x轴数据\r\n        const months = this.generateComboChartMonthsByAccountingPeriod();\r\n        console.log('生成的月份标签:', months);\r\n\r\n        // 生成对应的年月格式用于数据匹配\r\n        const yearMonths = this.generateYearMonthsByAccountingPeriod();\r\n        console.log('生成的年月格式:', yearMonths);\r\n\r\n        const failureCostData = [];     // 失败成本数据\r\n        const controllingCostData = []; // 控制成本数据\r\n\r\n        // 为每个月份提取对应的数值，转换为万元\r\n        yearMonths.forEach(yearMonth => {\r\n          // 获取失败成本数据，转换为万元\r\n          const failureValue = data.failureCostMap && data.failureCostMap[yearMonth]\r\n            ? ((Number(data.failureCostMap[yearMonth]) || 0) / 10000).toFixed(2)\r\n            : 0;\r\n          failureCostData.push(failureValue);\r\n\r\n          // 获取控制成本数据，转换为万元\r\n          const controllingValue = data.controllingCostMap && data.controllingCostMap[yearMonth]\r\n            ? ((Number(data.controllingCostMap[yearMonth]) || 0) / 10000).toFixed(2)\r\n            : 0;\r\n          controllingCostData.push(controllingValue);\r\n        });\r\n\r\n        console.log('x轴月份数据:', months.map(month => `${month}月`));\r\n        console.log('失败成本数据:', failureCostData);\r\n        console.log('控制成本数据:', controllingCostData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          // 图例配置 - 标注颜色对应的维度\r\n          legend: {\r\n            data: ['失败成本', '控制成本'], // 失败成本(红色#FCA5A5)，控制成本(绿色#86EFAC)\r\n            textStyle: { color: '#E5E7EB' }\r\n          },\r\n          grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n          xAxis: {\r\n            type: 'category',\r\n            boundaryGap: false,\r\n            data: months.map(month => `${month}月`), // 近6个月的月份\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '成本 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n\r\n          series: [\r\n            {\r\n              name: '失败成本', // 红色曲线 #FCA5A5\r\n              type: 'line',\r\n              data: failureCostData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FCA5A5', width: 3 },\r\n              itemStyle: { color: '#FCA5A5' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '控制成本', // 绿色曲线 #86EFAC\r\n              type: 'line',\r\n              data: controllingCostData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#86EFAC', width: 3 },\r\n              itemStyle: { color: '#86EFAC' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n\r\n        this.charts.comboChart.setOption(option);\r\n        console.log('ComboChart图表数据已更新');\r\n      }\r\n    },\r\n\r\n    // 生成ComboChart的月份标签（当前月份和之前的5个月）\r\n    generateComboChartMonths() {\r\n      const months = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const month = date.getMonth() + 1;\r\n        months.push(month);\r\n      }\r\n\r\n      return months;\r\n    },\r\n\r\n    // 生成对应的年月格式（当前月份和之前的5个月，如202501, 202502等）\r\n    generateYearMonths() {\r\n      const yearMonths = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const year = date.getFullYear();\r\n        const month = date.getMonth() + 1;\r\n        const yearMonth = `${year}${String(month).padStart(2, '0')}`;\r\n        yearMonths.push(yearMonth);\r\n      }\r\n\r\n      return yearMonths;\r\n    },\r\n\r\n    // 基于会计期生成ComboChart的月份标签（会计期当前月份和之前的5个月）\r\n    generateComboChartMonthsByAccountingPeriod() {\r\n      const months = [];\r\n\r\n      if (!this.accountingPeriod) {\r\n        console.warn('会计期为空，使用系统当前时间');\r\n        return this.generateComboChartMonths();\r\n      }\r\n\r\n      // 解析会计期 (格式: 2025-06)\r\n      const [year, month] = this.accountingPeriod.split('-').map(Number);\r\n      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);\r\n        const monthNum = date.getMonth() + 1;\r\n        months.push(monthNum);\r\n      }\r\n\r\n      return months;\r\n    },\r\n\r\n    // 基于会计期生成对应的年月格式（会计期当前月份和之前的5个月）\r\n    generateYearMonthsByAccountingPeriod() {\r\n      const yearMonths = [];\r\n\r\n      if (!this.accountingPeriod) {\r\n        console.warn('会计期为空，使用系统当前时间');\r\n        return this.generateYearMonths();\r\n      }\r\n\r\n      // 解析会计期 (格式: 2025-06)\r\n      const [year, month] = this.accountingPeriod.split('-').map(Number);\r\n      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);\r\n        const yearNum = date.getFullYear();\r\n        const monthNum = date.getMonth() + 1;\r\n        const yearMonth = `${yearNum}${String(monthNum).padStart(2, '0')}`;\r\n        yearMonths.push(yearMonth);\r\n      }\r\n\r\n      return yearMonths;\r\n    },\r\n\r\n    //质量成本四大类别占比\r\n    getPieChartData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n\r\n      getPieChartData(params).then(response => {\r\n        console.log('getPieChartData:', response);\r\n        if (response.data) {\r\n          this.updatePieChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n    // 获取成本中心列表\r\n    getCostCenterList() {\r\n      this.costCenterLoading = true;\r\n      costCenterlist().then(response => {\r\n        this.costCenterOptions = response.data || [];\r\n        // 如果有数据，设置默认选中第一个\r\n        if (this.costCenterOptions.length > 0) {\r\n          console.log('获取成本中心列表:', this.costCenterOptions);\r\n          this.costCenter = this.costCenterOptions[0].key;\r\n          // 设置默认值后，主动触发一次数据刷新\r\n          this.$nextTick(() => {\r\n            this.refreshChartData();\r\n          });\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取成本中心列表失败:', error);\r\n        this.$message.error('获取成本中心列表失败');\r\n      }).finally(() => {\r\n        this.costCenterLoading = false;\r\n      });\r\n    },\r\n\r\n    // 更新饼图数据\r\n    updatePieChart(data) {\r\n      if (this.charts.pieChart && data) {\r\n        // 更新饼图的数据，转换为万元\r\n        const option = this.charts.pieChart.getOption();\r\n        if (option && option.series && option.series[0]) {\r\n          option.series[0].data = [\r\n            { value: (data.preventionCost / 10000).toFixed(2), name: '预防成本', itemStyle: { color: '#93C5FD' } },\r\n            { value: (data.appraisalCost / 10000).toFixed(2), name: '鉴定成本', itemStyle: { color: '#86EFAC' } },\r\n            { value: (data.internalCost / 10000).toFixed(2), name: '内部损失成本', itemStyle: { color: '#FDE68A' } },\r\n            { value: (data.externalCost / 10000).toFixed(2), name: '外部损失成本', itemStyle: { color: '#FCA5A5' } },\r\n          ],\r\n            this.charts.pieChart.setOption(option);\r\n          // console.log('饼图数据已更新');\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新多线图数据\r\n    updateMultiLineChart(data) {\r\n      if (this.charts.multiLineChart && data) {\r\n        // 基于会计期生成月份标签和对应的年月数字\r\n        const months = this.generateComboChartMonthsByAccountingPeriod();\r\n        const yearMonths = this.generateYearMonthsByAccountingPeriod();\r\n\r\n        // 处理各种成本数据，转换为万元\r\n        const preventionData = this.processMapData(data.preventionCostMap, yearMonths, true);\r\n        const appraisalData = this.processMapData(data.appraisalCostMap, yearMonths, true);\r\n        const internalData = this.processMapData(data.internalCostMap, yearMonths, true);\r\n        const externalData = this.processMapData(data.externalCostMap, yearMonths, true);\r\n\r\n        // 更新多线图的配置\r\n        const option = {\r\n          xAxis: {\r\n            type: 'category',\r\n            boundaryGap: false,\r\n            data: months.map(month => `${month}月`), // 格式化为\"X月\"\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [\r\n            {\r\n              name: '预防成本',\r\n              type: 'line',\r\n              data: preventionData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#93C5FD', width: 3 },\r\n              itemStyle: { color: '#93C5FD' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '鉴定成本',\r\n              type: 'line',\r\n              data: appraisalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#86EFAC', width: 3 },\r\n              itemStyle: { color: '#86EFAC' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '内部损失成本',\r\n              type: 'line',\r\n              data: internalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FDE68A', width: 3 },\r\n              itemStyle: { color: '#FDE68A' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '外部损失成本',\r\n              type: 'line',\r\n              data: externalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FCA5A5', width: 3 },\r\n              itemStyle: { color: '#FCA5A5' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n\r\n        this.charts.multiLineChart.setOption(option);\r\n        console.log('多线图数据已更新');\r\n      }\r\n    },\r\n\r\n    // 处理Map数据，根据年月匹配对应的值\r\n    processMapData(costMap, yearMonths, convertToWanYuan = false) {\r\n      if (!costMap) return new Array(yearMonths.length).fill(0);\r\n\r\n      return yearMonths.map(yearMonth => {\r\n        const value = costMap[yearMonth] || 0;\r\n        return convertToWanYuan ? (value / 10000).toFixed(2) : value;\r\n      });\r\n    },\r\n\r\n    // 生成月份标签（当前月份及前五个月份）\r\n    generateMonthLabels() {\r\n      const months = [];\r\n      const yearMonths = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const year = date.getFullYear();\r\n        const month = date.getMonth() + 1;\r\n\r\n        months.push(`${month}月`);\r\n        yearMonths.push(parseInt(`${year}${String(month).padStart(2, '0')}`));\r\n      }\r\n\r\n      return { months, yearMonths };\r\n    },\r\n\r\n    // 刷新图表数据\r\n    refreshChartData() {\r\n      // 只有当成本中心和会计期都有值时才刷新\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        return;\r\n      }\r\n\r\n      this.getQualityCostDetail();\r\n      this.getPieChartData();\r\n      this.getMultiLineChartData();\r\n      this.getExternalCostDetail();\r\n      this.getInternalCostDetail();\r\n      this.getComboChartDetail();\r\n      this.getWaterfallChartDetail();\r\n      this.getScrapLossChartDetailsDetail();\r\n      this.getQualityObjectionLossDetail();\r\n      this.getFactoryRejectionChartDetail();\r\n      this.getFactoryScrapChartDetail();\r\n      this.getFactoryContractChartDetail();\r\n      this.getFactoryReturnChartDetail();\r\n\r\n      // 这里可以添加其他图表的数据刷新\r\n      // this.$message.success(`已切换到成本中心: ${this.costCenter}, 会计期: ${this.accountingPeriod}`);\r\n    },\r\n\r\n    /** 查询按钮操作 */\r\n    handleQuery() {\r\n      this.refreshChartData();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      // 重置为默认值\r\n      if (this.costCenterOptions.length > 0) {\r\n        this.costCenter = this.costCenterOptions[0].key;\r\n      }\r\n\r\n      // 获取默认会计期\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth();\r\n      const prevMonth = month === 0 ? 12 : month;\r\n      const prevYear = month === 0 ? year - 1 : year;\r\n      this.accountingPeriod = `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n\r\n      this.$message.success('查询条件已重置');\r\n    },\r\n\r\n    initCharts() {\r\n      const THEME = 'dark'\r\n\r\n      // 定义商务风淡色系色彩方案\r\n      this.businessColors = {\r\n        light: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#7DD3FC', '#F9A8D4', '#BEF264'],\r\n        gradient: [\r\n          { offset: 0, color: '#3B82F6' },\r\n          { offset: 1, color: '#1E40AF' }\r\n        ]\r\n      }\r\n\r\n      // 初始化所有图表\r\n      this.charts.pieChart = echarts.init(this.$refs.pieChart, THEME)\r\n      this.charts.multiLineChart = echarts.init(this.$refs.multiLineChart, THEME)\r\n      this.charts.externalCostDetailChart = echarts.init(this.$refs.externalCostDetailChart, THEME)\r\n      this.charts.internalCostDetailChart = echarts.init(this.$refs.internalCostDetailChart, THEME)\r\n      this.charts.waterfallChart = echarts.init(this.$refs.waterfallChart, THEME)\r\n      this.charts.comboChart = echarts.init(this.$refs.comboChart, THEME)\r\n      this.charts.scrapLossChart = echarts.init(this.$refs.scrapLossChart, THEME)\r\n      this.charts.qualityObjectionChart = echarts.init(this.$refs.qualityObjectionChart, THEME)\r\n      this.charts.factoryRejectionChart = echarts.init(this.$refs.factoryRejectionChart, THEME)\r\n      this.charts.factoryScrapChart = echarts.init(this.$refs.factoryScrapChart, THEME)\r\n      this.charts.factoryContractChart = echarts.init(this.$refs.factoryContractChart, THEME)\r\n      this.charts.factoryReturnChart = echarts.init(this.$refs.factoryReturnChart, THEME)\r\n\r\n      // 配置所有图表\r\n      this.setPieChartOption()\r\n      this.setMultiLineChartOption()\r\n      this.setExternalCostDetailChartOption()\r\n      this.setInternalCostDetailChartOption()\r\n      this.setWaterfallChartOption()\r\n      this.setComboChartOption()\r\n      this.setScrapLossChartOption()\r\n      this.setQualityObjectionChartOption()\r\n      this.setFactoryRejectionChartOption()\r\n      this.setFactoryScrapChartOption()\r\n      this.setFactoryContractChartOption()\r\n      this.setFactoryReturnChartOption()\r\n    },\r\n\r\n    setPieChartOption() {\r\n      this.charts.pieChart.setOption({\r\n        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: (params) => {\r\n            const value = parseFloat(params.value).toFixed(2);\r\n            const formattedValue = value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n            return `${params.seriesName} <br/>${params.name}: ${formattedValue}万元 (${params.percent}%)`;\r\n          },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          top: 'bottom',\r\n          left: 'center',\r\n          textStyle: { color: '#E5E7EB', fontSize: 12 }\r\n        },\r\n        series: [{\r\n          name: '成本类别',\r\n          type: 'pie',\r\n          radius: '65%',\r\n          data: [],\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 15,\r\n              shadowOffsetX: 0,\r\n              shadowColor: 'rgba(147, 197, 253, 0.6)'\r\n            }\r\n          },\r\n          labelLine: { lineStyle: { color: '#9CA3AF' } },\r\n          label: {\r\n            color: '#E5E7EB',\r\n            formatter: (params) => {\r\n              const value = parseFloat(params.value).toFixed(2);\r\n              const formattedValue = value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              return `${params.name}(${formattedValue}万元, ${params.percent}%)`;\r\n            }\r\n          }\r\n        }]\r\n      })\r\n    },\r\n\r\n\r\n\r\n    setComboChartOption() {\r\n      this.charts.comboChart.setOption({\r\n        color: ['#FCA5A5', '#86EFAC'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['失败成本', '控制成本'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '成本 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '失败成本', // 红色曲线 #FCA5A5\r\n            type: 'line',\r\n            data: [280, 260, 240, 220, 200, 180],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FCA5A5', width: 3 },\r\n            itemStyle: { color: '#FCA5A5' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '控制成本', // 绿色曲线 #86EFAC\r\n            type: 'line',\r\n            data: [120, 125, 130, 135, 140, 145],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#86EFAC', width: 3 },\r\n            itemStyle: { color: '#86EFAC' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    setMultiLineChartOption() {\r\n      this.charts.multiLineChart.setOption({\r\n        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['预防成本', '鉴定成本', '内部损失成本', '外部损失成本'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '成本 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '预防成本',\r\n            type: 'line',\r\n            data: [80, 82, 85, 88, 90, 95],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#93C5FD', width: 3 },\r\n            itemStyle: { color: '#93C5FD' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '鉴定成本',\r\n            type: 'line',\r\n            data: [120, 122, 125, 128, 130, 135],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#86EFAC', width: 3 },\r\n            itemStyle: { color: '#86EFAC' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '内部损失成本',\r\n            type: 'line',\r\n            data: [450, 430, 410, 380, 350, 320],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '外部损失成本',\r\n            type: 'line',\r\n            data: [350, 340, 310, 290, 260, 230],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FCA5A5', width: 3 },\r\n            itemStyle: { color: '#FCA5A5' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    setParetoChartOption() {\r\n      this.charts.paretoChart.setOption({\r\n        color: ['#93C5FD', '#FDE68A'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        grid: { right: '20%' },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['产品报废', '产品改判', '设备故障', '工艺废料', '其他'],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center', // 居中对齐\r\n            color: '#9CA3AF'\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        }],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '损失金额(元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '累计占比',\r\n            min: 0,\r\n            max: 100,\r\n            axisLabel: {\r\n              formatter: '{value} %',\r\n              color: '#9CA3AF'\r\n            },\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '损失金额',\r\n            type: 'bar',\r\n            data: [280, 110, 35, 20, 5],\r\n            itemStyle: { color: '#93C5FD' }\r\n          },\r\n          {\r\n            name: '累计占比',\r\n            type: 'line',\r\n            yAxisIndex: 1,\r\n            data: [62.2, 86.7, 94.4, 98.9, 100],\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 8\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    setExternalCostDetailChartOption() {\r\n      this.charts.externalCostDetailChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>';\r\n            params.forEach(function (item) {\r\n              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';\r\n            });\r\n            return result;\r\n          }\r\n        },\r\n        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '金额（万元）',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n\r\n          data: [],\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        series: [{\r\n          name: '金额 (元)',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setInternalCostDetailChartOption() {\r\n      this.charts.internalCostDetailChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>';\r\n            params.forEach(function (item) {\r\n              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';\r\n            });\r\n            return result;\r\n          }\r\n        },\r\n        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '金额（万元）',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        series: [{\r\n          name: '金额 (元)',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setWaterfallChartOption() {\r\n      this.charts.waterfallChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['初始成本', '修磨', '矫直', '探伤', '热处理', '总成本'],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center', // 居中对齐\r\n            color: '#9CA3AF'\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '辅助',\r\n            type: 'bar',\r\n            stack: '总量',\r\n            itemStyle: {\r\n              color: 'rgba(0,0,0,0)',\r\n              borderColor: 'rgba(0,0,0,0)',\r\n              borderWidth: 0\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                color: 'rgba(0,0,0,0)'\r\n              }\r\n            },\r\n            data: [0, 0, 50, 80, 105, 0]\r\n          },\r\n        ]\r\n      })\r\n    },\r\n\r\n    setScrapLossChartOption() {\r\n      this.charts.scrapLossChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '报废损失成本',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setQualityObjectionChartOption() {\r\n      this.charts.qualityObjectionChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '质量异议损失成本',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setFactoryRejectionChartOption() {\r\n      this.charts.factoryRejectionChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [], // 动态数据，由updateFactoryRejectionChart方法填充\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '改判金额',\r\n          type: 'bar',\r\n          data: [] // 动态数据，由updateFactoryRejectionChart方法填充\r\n        }]\r\n      })\r\n    },\r\n\r\n    setFactoryScrapChartOption() {\r\n      this.charts.factoryScrapChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [], // 动态数据，由updateFactoryScrapChart方法填充\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '报废金额',\r\n          type: 'bar',\r\n          data: [] // 动态数据，由updateFactoryScrapChart方法填充\r\n        }]\r\n      })\r\n    },\r\n\r\n    setFactoryContractChartOption() {\r\n      this.charts.factoryContractChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [], // 动态数据，由updateFactoryContractChart方法填充\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '脱合同金额',\r\n          type: 'bar',\r\n          data: [] // 动态数据，由updateFactoryContractChart方法填充\r\n        }]\r\n      })\r\n    },\r\n\r\n    setFactoryReturnChartOption() {\r\n      this.charts.factoryReturnChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [], // 动态数据，由updateFactoryReturnChart方法填充\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '退货金额',\r\n          type: 'bar',\r\n          data: [] // 动态数据，由updateFactoryReturnChart方法填充\r\n        }]\r\n      })\r\n    },\r\n\r\n    setDualYChartOption() {\r\n      this.charts.dualYChart.setOption({\r\n        color: ['#93C5FD', '#FDE68A'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['产量(吨)', '吨钢成本(元)'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        }],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '产量(吨)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '吨钢成本(元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '产量(吨)',\r\n            type: 'bar',\r\n            data: [80000, 82000, 85000, 83000, 88000, 90000],\r\n            itemStyle: { color: '#93C5FD' }\r\n          },\r\n          {\r\n            name: '吨钢成本(元)',\r\n            type: 'line',\r\n            yAxisIndex: 1,\r\n            data: [13.1, 12.8, 12.5, 12.6, 12.2, 12.0],\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 8\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    resizeCharts() {\r\n      Object.values(this.charts).forEach(chart => {\r\n        if (chart) {\r\n          chart.resize()\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.quality-cost-dashboard {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';\r\n  background-color: #111827;\r\n  /* 深色背景 */\r\n  color: #d1d5db;\r\n  /* 浅色文字 */\r\n  margin: 0;\r\n  padding: 24px;\r\n  min-height: 100vh;\r\n}\r\n\r\n.header {\r\n  margin-bottom: 24px;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n\r\n.header-wrapper {\r\n  display: inline-block;\r\n  position: relative;\r\n}\r\n\r\n.header h1 {\r\n  font-size: 28px;\r\n  color: #f9fafb;\r\n  /* 白色标题 */\r\n  font-weight: 600;\r\n  margin: 0;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.header p {\r\n  font-size: 16px;\r\n  color: #9ca3af;\r\n  /* 中灰色文字 */\r\n  margin: 8px 0 0 0;\r\n}\r\n\r\n.header-filters {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex-wrap: nowrap;\r\n  justify-content: flex-start;\r\n  margin-top: 12px;\r\n  margin-left: 950px;\r\n  /* 向左对齐 */\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.filter-item .label {\r\n  color: #d1d5db;\r\n  /* 浅色标签文字 */\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右上角筛选区域的样式 */\r\n.header-filters .el-input__inner,\r\n.header-filters .el-select .el-input__inner {\r\n  background-color: #111827; /* 与页面背景一致的深色 */\r\n  border-color: #374151;\r\n  color: #ffffff; /* 白色字体 */\r\n}\r\n\r\n.header-filters .el-input__inner:focus,\r\n.header-filters .el-select .el-input__inner:focus {\r\n  border-color: #93c5fd;\r\n  background-color: #111827; /* 聚焦时保持背景色 */\r\n}\r\n\r\n.header-filters .el-select-dropdown {\r\n  background-color: #111827; /* 下拉菜单背景与页面一致 */\r\n  border-color: #374151;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item {\r\n  color: #ffffff; /* 下拉选项白色字体 */\r\n  background-color: #111827;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item:hover {\r\n  background-color: #1f2937; /* 悬浮时稍微亮一点 */\r\n  color: #ffffff;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item.selected {\r\n  background-color: #374151; /* 选中项背景 */\r\n  color: #ffffff;\r\n}\r\n\r\n/* 下拉框箭头颜色 */\r\n.header-filters .el-select .el-input__suffix {\r\n  color: #ffffff;\r\n}\r\n\r\n.header-filters .el-select .el-select__caret {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 占位符文字颜色 */\r\n.header-filters .el-input__inner::placeholder {\r\n  color: #9ca3af;\r\n}\r\n\r\n/* 清除按钮颜色 */\r\n.header-filters .el-select .el-select__clear {\r\n  color: #9ca3af;\r\n}\r\n\r\n.header-filters .el-select .el-select__clear:hover {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1024px) {\r\n  .header-filters {\r\n    flex-wrap: wrap;\r\n    gap: 8px;\r\n    margin-left: 0;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .header {\r\n    text-align: center;\r\n  }\r\n\r\n  .header-filters {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: center;\r\n    margin-left: 0;\r\n    /* 在小屏幕上取消左边距 */\r\n  }\r\n\r\n  .filter-item {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n.dashboard-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr); /* 改为四列布局，支持1/4和3/4分配 */\r\n  gap: 24px;\r\n}\r\n\r\n/* 响应式设计：在小屏幕上改为单列 */\r\n@media (max-width: 1200px) {\r\n  .dashboard-grid {\r\n    grid-template-columns: 1fr; /* 小屏幕时改为单列 */\r\n  }\r\n\r\n  /* 小屏幕时重置所有grid-column样式 */\r\n  .dashboard-grid .chart-container {\r\n    grid-column: 1 !important;\r\n  }\r\n}\r\n\r\n.chart-container,\r\n.kpi-card {\r\n  background-color: #1f2937;\r\n  /* 深色卡片背景 */\r\n  border-radius: 8px;\r\n  border: 1px solid #374151;\r\n  /* 边框 */\r\n  box-shadow: none;\r\n  padding: 24px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.chart-container {\r\n  height: 400px;\r\n}\r\n\r\n/* 大图表样式 - 用于两两排列的图表 */\r\n.chart-container.large-chart {\r\n  height: 500px; /* 增加高度 */\r\n  min-height: 500px;\r\n}\r\n\r\n.chart-container h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #f9fafb;\r\n  /* 白色卡片标题 */\r\n}\r\n\r\n.chart {\r\n  width: 100%;\r\n  flex-grow: 1;\r\n}\r\n\r\n.kpi-grid {\r\n  grid-column: 1 / -1;\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 24px;\r\n}\r\n\r\n.kpi-card {\r\n  justify-content: space-between;\r\n}\r\n\r\n.kpi-card .title {\r\n  font-size: 14px;\r\n  color: #9ca3af;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.kpi-card .value {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #f9fafb;\r\n}\r\n\r\n.kpi-card .comparison {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.kpi-card .comparison .arrow {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin-right: 4px;\r\n  stroke-width: 2.5px;\r\n}\r\n\r\n.kpi-card .comparison .positive {\r\n  color: #34d399;\r\n  /* 亮绿色 */\r\n}\r\n\r\n.kpi-card .comparison .negative {\r\n  color: #f87171;\r\n  /* 亮红色 */\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2JA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;IACA,IAAAC,mBAAA,YAAAA,oBAAA;MACA,IAAAC,GAAA,OAAAC,IAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAJ,GAAA,CAAAK,QAAA;MACA,IAAAC,GAAA,GAAAN,GAAA,CAAAO,OAAA;MACA,IAAAC,IAAA,GAAAR,GAAA,CAAAS,QAAA;;MAEA;MACA,IAAAH,GAAA,SAAAA,GAAA,WAAAE,IAAA;QACA;QACA,IAAAE,SAAA,GAAAN,KAAA,cAAAA,KAAA;QACA,IAAAO,QAAA,GAAAP,KAAA,SAAAF,IAAA,OAAAA,IAAA;QACA,UAAAU,MAAA,CAAAD,QAAA,OAAAC,MAAA,CAAAC,MAAA,CAAAH,SAAA,EAAAI,QAAA;MACA;QACA,UAAAF,MAAA,CAAAV,IAAA,OAAAU,MAAA,CAAAC,MAAA,CAAAT,KAAA,EAAAU,QAAA;MACA;IACA;IAEA;MACAC,UAAA;MACAC,MAAA;MACA;MACAC,UAAA;MACAC,gBAAA,EAAAnB,mBAAA;MACA;MACAoB,WAAA;MACA;MACAC,iBAAA;MACAC,iBAAA;MACAC,iBAAA;MACAC,eAAA;IACA;EACA;EACAC,KAAA;IACA;IACAP,UAAA;MACAQ,OAAA,WAAAA,QAAA;QACAC,OAAA,CAAAC,GAAA,iBAAAV,UAAA;QACA,KAAAW,gBAAA;MACA;IACA;IACA;IACAV,gBAAA;MACAO,OAAA,WAAAA,QAAA;QACAC,OAAA,CAAAC,GAAA,gBAAAT,gBAAA;QACA,KAAAU,gBAAA;MACA;IACA;IACA;IACAT,WAAA;MACAM,OAAA,WAAAA,QAAA;QACAC,OAAA,CAAAC,GAAA,mBAAAR,WAAA;QACA,KAAAS,gBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,iBAAA;IACA;;IAEA,KAAAC,UAAA;IACA,KAAAC,cAAA,OAAAC,cAAA;MACAJ,KAAA,CAAAK,YAAA;IACA;IACA,KAAAF,cAAA,CAAAG,OAAA,MAAAC,GAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAJ,YAAA;EACA;EACAK,aAAA,WAAAA,cAAA;IACA;IACAC,MAAA,CAAAC,MAAA,MAAA1B,MAAA,EAAA2B,OAAA,WAAAC,KAAA;MACA,IAAAA,KAAA;QACAA,KAAA,CAAAC,OAAA;MACA;IACA;IACA,SAAAZ,cAAA;MACA,KAAAA,cAAA,CAAAa,UAAA;IACA;IACAR,MAAA,CAAAS,mBAAA,gBAAAZ,YAAA;EACA;EACAa,OAAA;IACA;IACAC,oBAAA,WAAAA,qBAAAC,UAAA;MACA,KAAAA,UAAA;MACA,OAAAA,UAAA,CAAAC,QAAA,GAAAC,UAAA;IACA;IAEA;IACAC,kBAAA,WAAAA,mBAAAH,UAAA;MACA,KAAAA,UAAA;MACA,YAAAD,oBAAA,CAAAC,UAAA;IACA;IAEA;IACAI,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAA,GAAA,aAAAA,GAAA,KAAAC,SAAA,IAAAD,GAAA;QACA;MACA;MACA,IAAAE,MAAA,GAAAC,MAAA,CAAAH,GAAA;MACA,IAAAI,KAAA,CAAAF,MAAA;QACA;MACA;MACA;MACA,OAAAG,UAAA,CAAAH,MAAA,CAAAI,OAAA,KAAAV,QAAA;IACA;IAEA;IACAW,oBAAA,WAAAA,qBAAAP,GAAA;MACA,OAAAA,GAAA,CAAAJ,QAAA,GAAAY,OAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAAT,GAAA;MACA,IAAAA,GAAA,aAAAA,GAAA,KAAAC,SAAA,IAAAD,GAAA;QACA;MACA;MACA,IAAAE,MAAA,GAAAC,MAAA,CAAAH,GAAA;MACA,IAAAI,KAAA,CAAAF,MAAA;QACA;MACA;MACA;MACA,IAAAQ,MAAA,IAAAR,MAAA,UAAAI,OAAA;MACA,UAAAjD,MAAA,MAAAkD,oBAAA,CAAAG,MAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAAX,GAAA;MACA,IAAAA,GAAA,aAAAA,GAAA,KAAAC,SAAA,IAAAD,GAAA;QACA;MACA;MACA,IAAAE,MAAA,GAAAC,MAAA,CAAAH,GAAA;MACA,IAAAI,KAAA,CAAAF,MAAA;QACA;MACA;MACA;MACA,IAAAQ,MAAA,IAAAR,MAAA,UAAAI,OAAA;MACA,UAAAjD,MAAA,MAAAkD,oBAAA,CAAAG,MAAA;IACA;IAEA;IACAE,cAAA,WAAAA,eAAAZ,GAAA;MACA,IAAAA,GAAA,aAAAA,GAAA,KAAAC,SAAA,IAAAD,GAAA;QACA;MACA;MACA,IAAAE,MAAA,GAAAC,MAAA,CAAAH,GAAA;MACA,IAAAI,KAAA,CAAAF,MAAA;QACA;MACA;MACA;MACA,IAAAQ,MAAA,GAAAR,MAAA,CAAAI,OAAA;MACA,UAAAjD,MAAA,MAAAkD,oBAAA,CAAAG,MAAA;IACA;IAEAG,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAApD,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACAC,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAiD,yCAAA,EAAAE,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,oCAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAuE,MAAA,CAAAK,2BAAA,CAAAD,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,0BAAAA,KAAA;QACAP,MAAA,CAAAQ,QAAA,CAAAD,KAAA;MACA;IACA;IAEAE,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA9D,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACAC,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAA2D,qCAAA,EAAAR,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,gCAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAiF,MAAA,CAAAC,uBAAA,CAAAP,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,mBAAAA,KAAA;QACAG,MAAA,CAAAF,QAAA,CAAAD,KAAA;MACA;IACA;IAEAK,6BAAA,WAAAA,8BAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAjE,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACAC,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAA8D,wCAAA,EAAAX,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,mCAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAoF,MAAA,CAAAC,0BAAA,CAAAV,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,oBAAAA,KAAA;QACAM,MAAA,CAAAL,QAAA,CAAAD,KAAA;MACA;IACA;IAEAQ,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAApE,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACAC,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAiE,sCAAA,EAAAd,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,iCAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAuF,MAAA,CAAAC,wBAAA,CAAAb,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,mBAAAA,KAAA;QACAS,MAAA,CAAAR,QAAA,CAAAD,KAAA;MACA;IACA;IAEAU,wBAAA,WAAAA,yBAAAxF,IAAA;MACA,SAAAkB,MAAA,CAAAuE,kBAAA,IAAAzF,IAAA;QACA4B,OAAA,CAAAC,GAAA,8BAAA7B,IAAA;QACA4B,OAAA,CAAAC,GAAA,sBAAA7B,IAAA,CAAA0F,gBAAA;;QAEA;QACA,IAAAC,SAAA;QACA,IAAAC,UAAA;QACA,IAAAC,MAAA;QAEA,IAAA7F,IAAA,CAAA0F,gBAAA;UACA;UACA,IAAAI,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAA0F,gBAAA,EAAAM,GAAA,WAAAC,IAAA;YAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAH,IAAA;cAAAI,GAAA,GAAAH,KAAA;cAAAI,KAAA,GAAAJ,KAAA;YAAA;cACAnG,IAAA,EAAAsG,GAAA;cAAA;cACAC,KAAA,GAAA1C,MAAA,CAAA0C,KAAA,QAAAvC,OAAA;YACA;UAAA;UAEAnC,OAAA,CAAAC,GAAA,gBAAAiE,SAAA;UAEA,IAAAA,SAAA,CAAAS,MAAA;YACA;YACAT,SAAA,CAAAU,IAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;YAAA;;YAEA;YACAR,SAAA,CAAAjD,OAAA,WAAA8D,IAAA,EAAAC,KAAA;cACAjB,SAAA,CAAAkB,IAAA,CAAAF,IAAA,CAAA5G,IAAA;cACA6F,UAAA,CAAAiB,IAAA;gBACAP,KAAA,EAAAK,IAAA,CAAAL,KAAA;gBACAQ,SAAA;kBAAAC,KAAA,EAAAlB,MAAA,CAAAe,KAAA,GAAAf,MAAA,CAAAU,MAAA;gBAAA;cACA;YACA;UACA;YACA3E,OAAA,CAAAoF,IAAA;YACA;YACArB,SAAA,CAAAkB,IAAA;YACAjB,UAAA,CAAAiB,IAAA;cACAP,KAAA;cACAQ,SAAA;gBAAAC,KAAA,EAAAlB,MAAA;cAAA;YACA;UACA;QACA;UACAjE,OAAA,CAAAoF,IAAA;UACA;UACArB,SAAA,CAAAkB,IAAA;UACAjB,UAAA,CAAAiB,IAAA;YACAP,KAAA;YACAQ,SAAA;cAAAC,KAAA,EAAAlB,MAAA;YAAA;UACA;QACA;QAEAjE,OAAA,CAAAC,GAAA,YAAA8D,SAAA;QACA/D,OAAA,CAAAC,GAAA,WAAA+D,UAAA;;QAEA;QACA,IAAAqB,MAAA;UACAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAC,OAAA;YACAC,OAAA;YACAC,WAAA;cAAAC,IAAA;YAAA;YACAC,eAAA;YACAC,WAAA;YACAC,SAAA;cAAAd,KAAA;YAAA;YACAe,SAAA,WAAAA,UAAAtD,MAAA;cACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;cACAyE,MAAA,CAAA3B,OAAA,WAAA8D,IAAA;gBACA,IAAAoB,cAAA,GAAAjE,UAAA,CAAA6C,IAAA,CAAAL,KAAA,EAAAvC,OAAA,IAAAV,QAAA,GAAAY,OAAA;gBACAE,MAAA,IAAAwC,IAAA,CAAAqB,MAAA,SAAArB,IAAA,CAAAsB,UAAA,UAAAF,cAAA;cACA;cACA,OAAA5D,MAAA;YACA;UACA;UACA+D,KAAA;YACAR,IAAA;YACA1H,IAAA,EAAA2F,SAAA;YACAwC,SAAA;cACApB,KAAA;cACAqB,QAAA;cAAA;cACAC,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACA3H,IAAA;YACA2I,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cACApB,KAAA;cACAe,SAAA,WAAAA,UAAAxB,KAAA;gBACA;gBACA,OAAAA,KAAA,CAAAjD,QAAA,GAAAY,OAAA;cACA;YACA;YACAsE,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACA7I,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA4F;UACA;QACA;QAEA,KAAA1E,MAAA,CAAAuE,kBAAA,CAAAoD,SAAA,CAAA5B,MAAA;QACArF,OAAA,CAAAC,GAAA;MACA;QACAD,OAAA,CAAAkD,KAAA;MACA;IACA;IAEAF,2BAAA,WAAAA,4BAAA5E,IAAA;MACA,SAAAkB,MAAA,CAAA4H,qBAAA,IAAA9I,IAAA;QACA4B,OAAA,CAAAC,GAAA,iCAAA7B,IAAA;QACA4B,OAAA,CAAAC,GAAA,yBAAA7B,IAAA,CAAA+I,mBAAA;;QAEA;QACA,IAAApD,SAAA;QACA,IAAAC,UAAA;QACA,IAAAC,MAAA;QAEA,IAAA7F,IAAA,CAAA+I,mBAAA;UACA;UACA,IAAAjD,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAA+I,mBAAA,EAAA/C,GAAA,WAAAgD,KAAA;YAAA,IAAAC,KAAA,OAAA9C,eAAA,CAAAC,OAAA,EAAA4C,KAAA;cAAA3C,GAAA,GAAA4C,KAAA;cAAA3C,KAAA,GAAA2C,KAAA;YAAA;cACAlJ,IAAA,EAAAsG,GAAA;cAAA;cACAC,KAAA,GAAA1C,MAAA,CAAA0C,KAAA,QAAAvC,OAAA;YACA;UAAA;UAEAnC,OAAA,CAAAC,GAAA,gBAAAiE,SAAA;UAEA,IAAAA,SAAA,CAAAS,MAAA;YACA;YACAT,SAAA,CAAAU,IAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;YAAA;;YAEA;YACAR,SAAA,CAAAjD,OAAA,WAAA8D,IAAA,EAAAC,KAAA;cACAjB,SAAA,CAAAkB,IAAA,CAAAF,IAAA,CAAA5G,IAAA;cACA6F,UAAA,CAAAiB,IAAA;gBACAP,KAAA,EAAAK,IAAA,CAAAL,KAAA;gBACAQ,SAAA;kBAAAC,KAAA,EAAAlB,MAAA,CAAAe,KAAA,GAAAf,MAAA,CAAAU,MAAA;gBAAA;cACA;YACA;UACA;YACA3E,OAAA,CAAAoF,IAAA;YACA;YACArB,SAAA,CAAAkB,IAAA;YACAjB,UAAA,CAAAiB,IAAA;cACAP,KAAA;cACAQ,SAAA;gBAAAC,KAAA,EAAAlB,MAAA;cAAA;YACA;UACA;QACA;UACAjE,OAAA,CAAAoF,IAAA;UACA;UACArB,SAAA,CAAAkB,IAAA;UACAjB,UAAA,CAAAiB,IAAA;YACAP,KAAA;YACAQ,SAAA;cAAAC,KAAA,EAAAlB,MAAA;YAAA;UACA;QACA;QAEAjE,OAAA,CAAAC,GAAA,YAAA8D,SAAA;QACA/D,OAAA,CAAAC,GAAA,WAAA+D,UAAA;;QAEA;QACA,IAAAqB,MAAA;UACAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAC,OAAA;YACAC,OAAA;YACAC,WAAA;cAAAC,IAAA;YAAA;YACAC,eAAA;YACAC,WAAA;YACAC,SAAA;cAAAd,KAAA;YAAA;YACAe,SAAA,WAAAA,UAAAtD,MAAA;cACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;cACAyE,MAAA,CAAA3B,OAAA,WAAA8D,IAAA;gBACA,IAAAoB,cAAA,GAAAjE,UAAA,CAAA6C,IAAA,CAAAL,KAAA,EAAAvC,OAAA,IAAAV,QAAA,GAAAY,OAAA;gBACAE,MAAA,IAAAwC,IAAA,CAAAqB,MAAA,SAAArB,IAAA,CAAAsB,UAAA,UAAAF,cAAA;cACA;cACA,OAAA5D,MAAA;YACA;UACA;UACA+D,KAAA;YACAR,IAAA;YACA1H,IAAA,EAAA2F,SAAA;YACAwC,SAAA;cACApB,KAAA;cACAqB,QAAA;cAAA;cACAC,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACA3H,IAAA;YACA2I,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cACApB,KAAA;cACAe,SAAA,WAAAA,UAAAxB,KAAA;gBACA;gBACA,OAAAA,KAAA,CAAAjD,QAAA,GAAAY,OAAA;cACA;YACA;YACAsE,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACA7I,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA4F;UACA;QACA;QAEA,KAAA1E,MAAA,CAAA4H,qBAAA,CAAAD,SAAA,CAAA5B,MAAA;QACArF,OAAA,CAAAC,GAAA;MACA;QACAD,OAAA,CAAAkD,KAAA;MACA;IACA;IAEAI,uBAAA,WAAAA,wBAAAlF,IAAA;MACA,SAAAkB,MAAA,CAAAgI,iBAAA,IAAAlJ,IAAA;QACA4B,OAAA,CAAAC,GAAA,6BAAA7B,IAAA;QACA4B,OAAA,CAAAC,GAAA,qBAAA7B,IAAA,CAAAmJ,eAAA;;QAEA;QACA,IAAAxD,SAAA;QACA,IAAAC,UAAA;QACA,IAAAC,MAAA;QAEA,IAAA7F,IAAA,CAAAmJ,eAAA;UACA;UACA,IAAArD,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAAmJ,eAAA,EAAAnD,GAAA,WAAAoD,KAAA;YAAA,IAAAC,KAAA,OAAAlD,eAAA,CAAAC,OAAA,EAAAgD,KAAA;cAAA/C,GAAA,GAAAgD,KAAA;cAAA/C,KAAA,GAAA+C,KAAA;YAAA;cACAtJ,IAAA,EAAAsG,GAAA;cAAA;cACAC,KAAA,GAAA1C,MAAA,CAAA0C,KAAA,QAAAvC,OAAA;YACA;UAAA;UAEAnC,OAAA,CAAAC,GAAA,gBAAAiE,SAAA;UAEA,IAAAA,SAAA,CAAAS,MAAA;YACA;YACAT,SAAA,CAAAU,IAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;YAAA;;YAEA;YACAR,SAAA,CAAAjD,OAAA,WAAA8D,IAAA,EAAAC,KAAA;cACAjB,SAAA,CAAAkB,IAAA,CAAAF,IAAA,CAAA5G,IAAA;cACA6F,UAAA,CAAAiB,IAAA;gBACAP,KAAA,EAAAK,IAAA,CAAAL,KAAA;gBACAQ,SAAA;kBAAAC,KAAA,EAAAlB,MAAA,CAAAe,KAAA,GAAAf,MAAA,CAAAU,MAAA;gBAAA;cACA;YACA;UACA;YACA3E,OAAA,CAAAoF,IAAA;YACA;YACArB,SAAA,CAAAkB,IAAA;YACAjB,UAAA,CAAAiB,IAAA;cACAP,KAAA;cACAQ,SAAA;gBAAAC,KAAA,EAAAlB,MAAA;cAAA;YACA;UACA;QACA;UACAjE,OAAA,CAAAoF,IAAA;UACA;UACArB,SAAA,CAAAkB,IAAA;UACAjB,UAAA,CAAAiB,IAAA;YACAP,KAAA;YACAQ,SAAA;cAAAC,KAAA,EAAAlB,MAAA;YAAA;UACA;QACA;QAEAjE,OAAA,CAAAC,GAAA,YAAA8D,SAAA;QACA/D,OAAA,CAAAC,GAAA,WAAA+D,UAAA;;QAEA;QACA,IAAAqB,MAAA;UACAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAC,OAAA;YACAC,OAAA;YACAC,WAAA;cAAAC,IAAA;YAAA;YACAC,eAAA;YACAC,WAAA;YACAC,SAAA;cAAAd,KAAA;YAAA;YACAe,SAAA,WAAAA,UAAAtD,MAAA;cACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;cACAyE,MAAA,CAAA3B,OAAA,WAAA8D,IAAA;gBACA,IAAAoB,cAAA,GAAAjE,UAAA,CAAA6C,IAAA,CAAAL,KAAA,EAAAvC,OAAA,IAAAV,QAAA,GAAAY,OAAA;gBACAE,MAAA,IAAAwC,IAAA,CAAAqB,MAAA,SAAArB,IAAA,CAAAsB,UAAA,UAAAF,cAAA;cACA;cACA,OAAA5D,MAAA;YACA;UACA;UACA+D,KAAA;YACAR,IAAA;YACA1H,IAAA,EAAA2F,SAAA;YACAwC,SAAA;cACApB,KAAA;cACAqB,QAAA;cAAA;cACAC,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACA3H,IAAA;YACA2I,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cACApB,KAAA;cACAe,SAAA,WAAAA,UAAAxB,KAAA;gBACA;gBACA,OAAAA,KAAA,CAAAjD,QAAA,GAAAY,OAAA;cACA;YACA;YACAsE,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACA7I,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA4F;UACA;QACA;QAEA,KAAA1E,MAAA,CAAAgI,iBAAA,CAAAL,SAAA,CAAA5B,MAAA;QACArF,OAAA,CAAAC,GAAA;MACA;QACAD,OAAA,CAAAkD,KAAA;MACA;IACA;IAEAO,0BAAA,WAAAA,2BAAArF,IAAA;MACA,SAAAkB,MAAA,CAAAoI,oBAAA,IAAAtJ,IAAA;QACA4B,OAAA,CAAAC,GAAA,gCAAA7B,IAAA;QACA4B,OAAA,CAAAC,GAAA,wBAAA7B,IAAA,CAAAuJ,kBAAA;;QAEA;QACA,IAAA5D,SAAA;QACA,IAAAC,UAAA;QACA,IAAAC,MAAA;QAEA,IAAA7F,IAAA,CAAAuJ,kBAAA;UACA;UACA,IAAAzD,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAAuJ,kBAAA,EAAAvD,GAAA,WAAAwD,KAAA;YAAA,IAAAC,KAAA,OAAAtD,eAAA,CAAAC,OAAA,EAAAoD,KAAA;cAAAnD,GAAA,GAAAoD,KAAA;cAAAnD,KAAA,GAAAmD,KAAA;YAAA;cACA1J,IAAA,EAAAsG,GAAA;cAAA;cACAC,KAAA,GAAA1C,MAAA,CAAA0C,KAAA,QAAAvC,OAAA;YACA;UAAA;UAEAnC,OAAA,CAAAC,GAAA,iBAAAiE,SAAA;UAEA,IAAAA,SAAA,CAAAS,MAAA;YACA;YACAT,SAAA,CAAAU,IAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;YAAA;;YAEA;YACAR,SAAA,CAAAjD,OAAA,WAAA8D,IAAA,EAAAC,KAAA;cACAjB,SAAA,CAAAkB,IAAA,CAAAF,IAAA,CAAA5G,IAAA;cACA6F,UAAA,CAAAiB,IAAA;gBACAP,KAAA,EAAAK,IAAA,CAAAL,KAAA;gBACAQ,SAAA;kBAAAC,KAAA,EAAAlB,MAAA,CAAAe,KAAA,GAAAf,MAAA,CAAAU,MAAA;gBAAA;cACA;YACA;UACA;YACA3E,OAAA,CAAAoF,IAAA;YACA;YACArB,SAAA,CAAAkB,IAAA;YACAjB,UAAA,CAAAiB,IAAA;cACAP,KAAA;cACAQ,SAAA;gBAAAC,KAAA,EAAAlB,MAAA;cAAA;YACA;UACA;QACA;UACAjE,OAAA,CAAAoF,IAAA;UACA;UACArB,SAAA,CAAAkB,IAAA;UACAjB,UAAA,CAAAiB,IAAA;YACAP,KAAA;YACAQ,SAAA;cAAAC,KAAA,EAAAlB,MAAA;YAAA;UACA;QACA;QAEAjE,OAAA,CAAAC,GAAA,YAAA8D,SAAA;QACA/D,OAAA,CAAAC,GAAA,WAAA+D,UAAA;;QAEA;QACA,IAAAqB,MAAA;UACAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAC,OAAA;YACAC,OAAA;YACAC,WAAA;cAAAC,IAAA;YAAA;YACAC,eAAA;YACAC,WAAA;YACAC,SAAA;cAAAd,KAAA;YAAA;YACAe,SAAA,WAAAA,UAAAtD,MAAA;cACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;cACAyE,MAAA,CAAA3B,OAAA,WAAA8D,IAAA;gBACA,IAAAoB,cAAA,GAAAjE,UAAA,CAAA6C,IAAA,CAAAL,KAAA,EAAAvC,OAAA,IAAAV,QAAA,GAAAY,OAAA;gBACAE,MAAA,IAAAwC,IAAA,CAAAqB,MAAA,SAAArB,IAAA,CAAAsB,UAAA,UAAAF,cAAA;cACA;cACA,OAAA5D,MAAA;YACA;UACA;UACA+D,KAAA;YACAR,IAAA;YACA1H,IAAA,EAAA2F,SAAA;YACAwC,SAAA;cACApB,KAAA;cACAqB,QAAA;cAAA;cACAC,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACA3H,IAAA;YACA2I,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cACApB,KAAA;cACAe,SAAA,WAAAA,UAAAxB,KAAA;gBACA;gBACA,OAAAA,KAAA,CAAAjD,QAAA,GAAAY,OAAA;cACA;YACA;YACAsE,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACA7I,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA4F;UACA;QACA;QAEA,KAAA1E,MAAA,CAAAoI,oBAAA,CAAAT,SAAA,CAAA5B,MAAA;QACArF,OAAA,CAAAC,GAAA;MACA;QACAD,OAAA,CAAAkD,KAAA;MACA;IACA;IAGA4E,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAxI,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAqI,kCAAA,EAAAlF,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,6BAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACA2J,MAAA,CAAAC,oBAAA,CAAAjF,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,0BAAAA,KAAA;QACA6E,MAAA,CAAA5E,QAAA,CAAAD,KAAA;MACA;IACA;IAEA;IACA8E,oBAAA,WAAAA,qBAAA5J,IAAA;MACA,SAAAkB,MAAA,CAAA2I,cAAA,IAAA7J,IAAA;QACA4B,OAAA,CAAAC,GAAA,0BAAA7B,IAAA;;QAEA;QACA,IAAA2F,SAAA;QACA,IAAAC,UAAA;QACA,IAAAC,MAAA;QAEA,IAAAC,SAAA;QAEA,IAAA9F,IAAA,CAAA8J,aAAA;UACA;UACAhE,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAA8J,aAAA,EAAA9D,GAAA,WAAA+D,KAAA;YAAA,IAAAC,KAAA,OAAA7D,eAAA,CAAAC,OAAA,EAAA2D,KAAA;cAAA1D,GAAA,GAAA2D,KAAA;cAAA1D,KAAA,GAAA0D,KAAA;YAAA;cACAjK,IAAA,EAAAsG,GAAA;cAAA;cACAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACA;UAAA;QACA;QAEAnC,OAAA,CAAAC,GAAA,aAAAiE,SAAA;QAEA,IAAAA,SAAA,CAAAS,MAAA;UACA;UACAT,SAAA,CAAAU,IAAA,WAAAC,CAAA,EAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;UAAA;;UAEA;UACA,IAAA2D,WAAA,GAAAnE,SAAA,CAAAoE,KAAA;UACAtI,OAAA,CAAAC,GAAA,cAAAoI,WAAA;;UAEA;UACAA,WAAA,CAAApH,OAAA,WAAA8D,IAAA,EAAAC,KAAA;YACAjB,SAAA,CAAAkB,IAAA,CAAAF,IAAA,CAAA5G,IAAA;YACA6F,UAAA,CAAAiB,IAAA;cACAP,KAAA,EAAAK,IAAA,CAAAL,KAAA;cACAQ,SAAA;gBAAAC,KAAA,EAAAlB,MAAA,CAAAe,KAAA,GAAAf,MAAA,CAAAU,MAAA;cAAA;YACA;UACA;QACA;UACA3E,OAAA,CAAAoF,IAAA;UACA;UACArB,SAAA,CAAAkB,IAAA;UACAjB,UAAA,CAAAiB,IAAA;YACAP,KAAA;YACAQ,SAAA;cAAAC,KAAA,EAAAlB,MAAA;YAAA;UACA;QACA;QAEAjE,OAAA,CAAAC,GAAA,YAAA8D,SAAA;QACA/D,OAAA,CAAAC,GAAA,WAAA+D,UAAA;;QAEA;QACA,IAAAqB,MAAA;UACAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAC,OAAA;YACAC,OAAA;YACAC,WAAA;cAAAC,IAAA;YAAA;YACAC,eAAA;YACAC,WAAA;YACAC,SAAA;cAAAd,KAAA;YAAA;YACAe,SAAA,WAAAA,UAAAtD,MAAA;cACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;cACAyE,MAAA,CAAA3B,OAAA,WAAA8D,IAAA;gBACA,IAAAoB,cAAA,GAAAjE,UAAA,CAAA6C,IAAA,CAAAL,KAAA,EAAAvC,OAAA,IAAAV,QAAA,GAAAY,OAAA;gBACAE,MAAA,IAAAwC,IAAA,CAAAqB,MAAA,SAAArB,IAAA,CAAAsB,UAAA,UAAAF,cAAA;cACA;cACA,OAAA5D,MAAA;YACA;UACA;UACA+D,KAAA;YACAR,IAAA;YACA1H,IAAA,EAAA2F,SAAA;YACAwC,SAAA;cACApB,KAAA;cACAqB,QAAA;cAAA;cACAC,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACA3H,IAAA;YACA2I,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACA7I,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA4F;UACA;QACA;QAEA,KAAA1E,MAAA,CAAA2I,cAAA,CAAAhB,SAAA,CAAA5B,MAAA;QACArF,OAAA,CAAAC,GAAA;MACA;QACAD,OAAA,CAAAkD,KAAA;MACA;IACA;IAEA;IACAqF,oBAAA,WAAAA,qBAAAnK,IAAA;MACA,SAAAkB,MAAA,CAAAkJ,cAAA,IAAApK,IAAA;QACA4B,OAAA,CAAAC,GAAA,0BAAA7B,IAAA;QACA4B,OAAA,CAAAC,GAAA,cAAAwI,QAAA,CAAAjE,OAAA,EAAApG,IAAA;QACA4B,OAAA,CAAAC,GAAA,SAAAc,MAAA,CAAA2H,IAAA,CAAAtK,IAAA;;QAEA;QACA,IAAA2F,SAAA;QACA,IAAAC,UAAA;QACA,IAAAC,MAAA;QACA,IAAAC,SAAA;;QAEA;QACA,IAAA9F,IAAA,CAAAuK,YAAA;UACA;UACA3I,OAAA,CAAAC,GAAA;UACAiE,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAAuK,YAAA,EAAAvE,GAAA,WAAAwE,KAAA;YAAA,IAAAC,MAAA,OAAAtE,eAAA,CAAAC,OAAA,EAAAoE,KAAA;cAAAnE,GAAA,GAAAoE,MAAA;cAAAnE,KAAA,GAAAmE,MAAA;YAAA;cACA1K,IAAA,EAAAsG,GAAA;cACAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACA;UAAA;QACA,WAAA/D,IAAA,CAAA8J,aAAA;UACA;UACAlI,OAAA,CAAAC,GAAA;UACAiE,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAA8J,aAAA,EAAA9D,GAAA,WAAA0E,MAAA;YAAA,IAAAC,MAAA,OAAAxE,eAAA,CAAAC,OAAA,EAAAsE,MAAA;cAAArE,GAAA,GAAAsE,MAAA;cAAArE,KAAA,GAAAqE,MAAA;YAAA;cACA5K,IAAA,EAAAsG,GAAA;cACAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACA;UAAA;QACA,WAAA/D,IAAA,CAAA4K,SAAA;UACA;UACAhJ,OAAA,CAAAC,GAAA;UACAiE,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAA4K,SAAA,EAAA5E,GAAA,WAAA6E,MAAA;YAAA,IAAAC,MAAA,OAAA3E,eAAA,CAAAC,OAAA,EAAAyE,MAAA;cAAAxE,GAAA,GAAAyE,MAAA;cAAAxE,KAAA,GAAAwE,MAAA;YAAA;cACA/K,IAAA,EAAAsG,GAAA;cACAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACA;UAAA;QACA,WAAA/D,IAAA,CAAA+K,gBAAA;UACA;UACAnJ,OAAA,CAAAC,GAAA;UACAiE,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAA+K,gBAAA,EAAA/E,GAAA,WAAAgF,MAAA;YAAA,IAAAC,MAAA,OAAA9E,eAAA,CAAAC,OAAA,EAAA4E,MAAA;cAAA3E,GAAA,GAAA4E,MAAA;cAAA3E,KAAA,GAAA2E,MAAA;YAAA;cACAlL,IAAA,EAAAsG,GAAA;cACAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACA;UAAA;QACA;UACA;UACAnC,OAAA,CAAAC,GAAA;UACAiE,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,EAAAgG,GAAA,WAAAkF,MAAA;YAAA,IAAAC,MAAA,OAAAhF,eAAA,CAAAC,OAAA,EAAA8E,MAAA;cAAA7E,GAAA,GAAA8E,MAAA;cAAA7E,KAAA,GAAA6E,MAAA;YAAA;cACApL,IAAA,EAAAsG,GAAA;cACAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACA;UAAA;QACA;QAEAnC,OAAA,CAAAC,GAAA,aAAAiE,SAAA;QAEA,IAAAA,SAAA,CAAAS,MAAA;UACA;UACAT,SAAA,CAAAU,IAAA,WAAAC,CAAA,EAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;UAAA;;UAEA;UACA,IAAA2D,WAAA,GAAAnE,SAAA,CAAAoE,KAAA;UACAtI,OAAA,CAAAC,GAAA,cAAAoI,WAAA;;UAEA;UACAA,WAAA,CAAApH,OAAA,WAAA8D,IAAA,EAAAC,KAAA;YACAjB,SAAA,CAAAkB,IAAA,CAAAF,IAAA,CAAA5G,IAAA;YACA6F,UAAA,CAAAiB,IAAA;cACAP,KAAA,EAAAK,IAAA,CAAAL,KAAA;cACAQ,SAAA;gBAAAC,KAAA,EAAAlB,MAAA,CAAAe,KAAA,GAAAf,MAAA,CAAAU,MAAA;cAAA;YACA;UACA;QACA;UACA3E,OAAA,CAAAoF,IAAA;UACA;UACArB,SAAA,CAAAkB,IAAA;UACAjB,UAAA,CAAAiB,IAAA;YACAP,KAAA;YACAQ,SAAA;cAAAC,KAAA,EAAAlB,MAAA;YAAA;UACA;QACA;QAEAjE,OAAA,CAAAC,GAAA,YAAA8D,SAAA;QACA/D,OAAA,CAAAC,GAAA,WAAA+D,UAAA;;QAEA;QACA,IAAAqB,MAAA;UACAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAC,OAAA;YACAC,OAAA;YACAC,WAAA;cAAAC,IAAA;YAAA;YACAC,eAAA;YACAC,WAAA;YACAC,SAAA;cAAAd,KAAA;YAAA;YACAe,SAAA,WAAAA,UAAAtD,MAAA;cACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;cACAyE,MAAA,CAAA3B,OAAA,WAAA8D,IAAA;gBACA,IAAAoB,cAAA,GAAAjE,UAAA,CAAA6C,IAAA,CAAAL,KAAA,EAAAvC,OAAA,IAAAV,QAAA,GAAAY,OAAA;gBACAE,MAAA,IAAAwC,IAAA,CAAAqB,MAAA,SAAArB,IAAA,CAAAsB,UAAA,UAAAF,cAAA;cACA;cACA,OAAA5D,MAAA;YACA;UACA;UACA+D,KAAA;YACAR,IAAA;YACA1H,IAAA,EAAA2F,SAAA;YACAwC,SAAA;cACApB,KAAA;cACAqB,QAAA;cAAA;cACAC,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACA3H,IAAA;YACA2I,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACA7I,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA4F;UACA;QACA;QAEA,KAAA1E,MAAA,CAAAkJ,cAAA,CAAAvB,SAAA,CAAA5B,MAAA;QACArF,OAAA,CAAAC,GAAA;MACA;QACAD,OAAA,CAAAkD,KAAA;MACA;IACA;IAEA;IACAsG,2BAAA,WAAAA,4BAAApL,IAAA;MACA,SAAAkB,MAAA,CAAAmK,qBAAA,IAAArL,IAAA;QACA4B,OAAA,CAAAC,GAAA,iCAAA7B,IAAA;QACA4B,OAAA,CAAAC,GAAA,cAAAwI,QAAA,CAAAjE,OAAA,EAAApG,IAAA;QACA4B,OAAA,CAAAC,GAAA,SAAAc,MAAA,CAAA2H,IAAA,CAAAtK,IAAA;;QAEA;QACA,IAAA2F,SAAA;QACA,IAAAC,UAAA;QACA,IAAAC,MAAA;QACA,IAAAC,SAAA;;QAEA;QACA,IAAA9F,IAAA,CAAAsL,uBAAA;UACA;UACA1J,OAAA,CAAAC,GAAA;UACAiE,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAAsL,uBAAA,EAAAtF,GAAA,WAAAuF,MAAA;YAAA,IAAAC,MAAA,OAAArF,eAAA,CAAAC,OAAA,EAAAmF,MAAA;cAAAlF,GAAA,GAAAmF,MAAA;cAAAlF,KAAA,GAAAkF,MAAA;YAAA;cACAzL,IAAA,EAAAsG,GAAA;cACAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACA;UAAA;QACA,WAAA/D,IAAA,CAAA8J,aAAA;UACA;UACAlI,OAAA,CAAAC,GAAA;UACAiE,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAA8J,aAAA,EAAA9D,GAAA,WAAAyF,MAAA;YAAA,IAAAC,MAAA,OAAAvF,eAAA,CAAAC,OAAA,EAAAqF,MAAA;cAAApF,GAAA,GAAAqF,MAAA;cAAApF,KAAA,GAAAoF,MAAA;YAAA;cACA3L,IAAA,EAAAsG,GAAA;cACAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACA;UAAA;QACA;UACA;UACAnC,OAAA,CAAAC,GAAA;UACAiE,SAAA,GAAAnD,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,EAAAgG,GAAA,WAAA2F,MAAA;YAAA,IAAAC,MAAA,OAAAzF,eAAA,CAAAC,OAAA,EAAAuF,MAAA;cAAAtF,GAAA,GAAAuF,MAAA;cAAAtF,KAAA,GAAAsF,MAAA;YAAA;cACA7L,IAAA,EAAAsG,GAAA;cACAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACA;UAAA;QACA;QAEAnC,OAAA,CAAAC,GAAA,aAAAiE,SAAA;QAEA,IAAAA,SAAA,CAAAS,MAAA;UACA;UACAT,SAAA,CAAAU,IAAA,WAAAC,CAAA,EAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;UAAA;;UAEA;UACA,IAAA2D,WAAA,GAAAnE,SAAA,CAAAoE,KAAA;UACAtI,OAAA,CAAAC,GAAA,cAAAoI,WAAA;;UAEA;UACAA,WAAA,CAAApH,OAAA,WAAA8D,IAAA,EAAAC,KAAA;YACAjB,SAAA,CAAAkB,IAAA,CAAAF,IAAA,CAAA5G,IAAA;YACA6F,UAAA,CAAAiB,IAAA;cACAP,KAAA,EAAAK,IAAA,CAAAL,KAAA;cACAQ,SAAA;gBAAAC,KAAA,EAAAlB,MAAA,CAAAe,KAAA,GAAAf,MAAA,CAAAU,MAAA;cAAA;YACA;UACA;QACA;UACA3E,OAAA,CAAAoF,IAAA;UACA;UACArB,SAAA,CAAAkB,IAAA;UACAjB,UAAA,CAAAiB,IAAA;YACAP,KAAA;YACAQ,SAAA;cAAAC,KAAA,EAAAlB,MAAA;YAAA;UACA;QACA;QAEAjE,OAAA,CAAAC,GAAA,YAAA8D,SAAA;QACA/D,OAAA,CAAAC,GAAA,WAAA+D,UAAA;;QAEA;QACA,IAAAqB,MAAA;UACAC,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAC,OAAA;YACAC,OAAA;YACAC,WAAA;cAAAC,IAAA;YAAA;YACAC,eAAA;YACAC,WAAA;YACAC,SAAA;cAAAd,KAAA;YAAA;YACAe,SAAA,WAAAA,UAAAtD,MAAA;cACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;cACAyE,MAAA,CAAA3B,OAAA,WAAA8D,IAAA;gBACA,IAAAoB,cAAA,GAAAjE,UAAA,CAAA6C,IAAA,CAAAL,KAAA,EAAAvC,OAAA,IAAAV,QAAA,GAAAY,OAAA;gBACAE,MAAA,IAAAwC,IAAA,CAAAqB,MAAA,SAAArB,IAAA,CAAAsB,UAAA,UAAAF,cAAA;cACA;cACA,OAAA5D,MAAA;YACA;UACA;UACA+D,KAAA;YACAR,IAAA;YACA1H,IAAA,EAAA2F,SAAA;YACAwC,SAAA;cACApB,KAAA;cACAqB,QAAA;cAAA;cACAC,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACA3H,IAAA;YACA2I,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACA7I,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA4F;UACA;QACA;QAEA,KAAA1E,MAAA,CAAAmK,qBAAA,CAAAxC,SAAA,CAAA5B,MAAA;QACArF,OAAA,CAAAC,GAAA;MACA;QACAD,OAAA,CAAAkD,KAAA;MACA;IACA;IAEA+G,6BAAA,WAAAA,8BAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA3K,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAwK,wCAAA,EAAArH,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,mCAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACA8L,MAAA,CAAAV,2BAAA,CAAAzG,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,iCAAAA,KAAA;QACAgH,MAAA,CAAA/G,QAAA,CAAAD,KAAA;MACA;IACA;IAEAiH,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA7K,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAA0K,yCAAA,EAAAvH,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,oCAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAgM,MAAA,CAAA7B,oBAAA,CAAAxF,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,0BAAAA,KAAA;QACAkH,MAAA,CAAAjH,QAAA,CAAAD,KAAA;MACA;IACA;IAEAmH,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA/K,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAA4K,gCAAA,EAAAzH,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,2BAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAkM,MAAA,CAAAC,6BAAA,CAAAxH,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACA;QACAoH,MAAA,CAAAnH,QAAA,CAAAD,KAAA;MACA;IACA;IAEAsH,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAlL,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAA+K,gCAAA,EAAA5H,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,2BAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA;UACAqM,MAAA,CAAAC,6BAAA,CAAA3H,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAuH,MAAA,CAAAtH,QAAA,CAAAD,KAAA;MACA;IACA;IAEA;IACAwH,6BAAA,WAAAA,8BAAAtM,IAAA;MACA,SAAAkB,MAAA,CAAAqL,uBAAA,IAAAvM,IAAA;QACA4B,OAAA,CAAAC,GAAA,kBAAA7B,IAAA;;QAEA;QACA,IAAAwM,YAAA;QACA,IAAA3G,MAAA;;QAEA;QACA,IAAA7F,IAAA,CAAAyM,eAAA;UACA9J,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAAyM,eAAA,EAAA5J,OAAA,WAAA6J,MAAA;YAAA,IAAAC,MAAA,OAAAxG,eAAA,CAAAC,OAAA,EAAAsG,MAAA;cAAArG,GAAA,GAAAsG,MAAA;cAAArG,KAAA,GAAAqG,MAAA;YACA;YACA,IAAAC,QAAA,KAAAhJ,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACAyI,YAAA,CAAA3F,IAAA;cAAA9G,IAAA,EAAAsG,GAAA;cAAAC,KAAA,EAAAsG;YAAA;UACA;QACA;QAEA,IAAA5M,IAAA,CAAA6M,UAAA;UACAlK,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAA6M,UAAA,EAAAhK,OAAA,WAAAiK,MAAA;YAAA,IAAAC,MAAA,OAAA5G,eAAA,CAAAC,OAAA,EAAA0G,MAAA;cAAAzG,GAAA,GAAA0G,MAAA;cAAAzG,KAAA,GAAAyG,MAAA;YACA;YACA,IAAAH,QAAA,KAAAhJ,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACAyI,YAAA,CAAA3F,IAAA;cAAA9G,IAAA,EAAAsG,GAAA;cAAAC,KAAA,EAAAsG;YAAA;UACA;QACA;QAEA,IAAA5M,IAAA,CAAAgN,YAAA;UACArK,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAAgN,YAAA,EAAAnK,OAAA,WAAAoK,MAAA;YAAA,IAAAC,MAAA,OAAA/G,eAAA,CAAAC,OAAA,EAAA6G,MAAA;cAAA5G,GAAA,GAAA6G,MAAA;cAAA5G,KAAA,GAAA4G,MAAA;YACA;YACA,IAAAN,QAAA,KAAAhJ,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACAyI,YAAA,CAAA3F,IAAA;cAAA9G,IAAA,EAAAsG,GAAA;cAAAC,KAAA,EAAAsG;YAAA;UACA;QACA;QAEA,IAAA5M,IAAA,CAAA4K,SAAA;UACAjI,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAA4K,SAAA,EAAA/H,OAAA,WAAAsK,MAAA;YAAA,IAAAC,MAAA,OAAAjH,eAAA,CAAAC,OAAA,EAAA+G,MAAA;cAAA9G,GAAA,GAAA+G,MAAA;cAAA9G,KAAA,GAAA8G,MAAA;YACA;YACA,IAAAR,QAAA,KAAAhJ,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YACAyI,YAAA,CAAA3F,IAAA;cAAA9G,IAAA,EAAAsG,GAAA;cAAAC,KAAA,EAAAsG;YAAA;UACA;QACA;QAEAhL,OAAA,CAAAC,GAAA,qBAAA2K,YAAA;;QAEA;QACAA,YAAA,CAAAhG,IAAA,WAAAC,CAAA,EAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;QAAA;QAEA1E,OAAA,CAAAC,GAAA,kBAAA2K,YAAA;;QAEA;QACA,IAAAa,SAAA;QACA,IAAAzH,UAAA;;QAEA;QACA4G,YAAA,CAAAc,OAAA,GAAAzK,OAAA,WAAA8D,IAAA,EAAAC,KAAA;UACAyG,SAAA,CAAAxG,IAAA,CAAAF,IAAA,CAAA5G,IAAA;UACA6F,UAAA,CAAAiB,IAAA;YACAP,KAAA,EAAAK,IAAA,CAAAL,KAAA;YACAQ,SAAA;cAAAC,KAAA,EAAAlB,MAAA,CAAAe,KAAA,GAAAf,MAAA,CAAAU,MAAA;YAAA;UACA;QACA;QAEA3E,OAAA,CAAAC,GAAA,UAAAwL,SAAA;QACAzL,OAAA,CAAAC,GAAA,gBAAA+D,UAAA;;QAEA;QACA,IAAAqB,MAAA;UACAwB,KAAA;YACAf,IAAA;YACA1H,IAAA,EAAAqN,SAAA;YACAlF,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACA7I,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA4F;UACA;QACA;QAEA,KAAA1E,MAAA,CAAAqL,uBAAA,CAAA1D,SAAA,CAAA5B,MAAA;QACArF,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAsK,6BAAA,WAAAA,8BAAAnM,IAAA;MACA,SAAAkB,MAAA,CAAAqM,uBAAA,IAAAvN,IAAA;QACA;QACA,IAAAwM,YAAA;QACA,IAAA3G,MAAA;;QAEA;QACA,IAAA7F,IAAA,CAAAwN,iBAAA;UACA7K,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAAwN,iBAAA,EAAA3K,OAAA,WAAA4K,MAAA;YAAA,IAAAC,MAAA,OAAAvH,eAAA,CAAAC,OAAA,EAAAqH,MAAA;cAAApH,GAAA,GAAAqH,MAAA;cAAApH,KAAA,GAAAoH,MAAA;YACAlB,YAAA,CAAA3F,IAAA;cAAA9G,IAAA,EAAAsG,GAAA;cAAAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YAAA;UACA;QACA;QAEA,IAAA/D,IAAA,CAAA2N,uBAAA;UACAhL,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAA2N,uBAAA,EAAA9K,OAAA,WAAA+K,MAAA;YAAA,IAAAC,MAAA,OAAA1H,eAAA,CAAAC,OAAA,EAAAwH,MAAA;cAAAvH,GAAA,GAAAwH,MAAA;cAAAvH,KAAA,GAAAuH,MAAA;YACArB,YAAA,CAAA3F,IAAA;cAAA9G,IAAA,EAAAsG,GAAA;cAAAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YAAA;UACA;QACA;QAEA,IAAA/D,IAAA,CAAA8N,0BAAA;UACAnL,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAA8N,0BAAA,EAAAjL,OAAA,WAAAkL,MAAA;YAAA,IAAAC,MAAA,OAAA7H,eAAA,CAAAC,OAAA,EAAA2H,MAAA;cAAA1H,GAAA,GAAA2H,MAAA;cAAA1H,KAAA,GAAA0H,MAAA;YACAxB,YAAA,CAAA3F,IAAA;cAAA9G,IAAA,EAAAsG,GAAA;cAAAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YAAA;UACA;QACA;QAEA,IAAA/D,IAAA,CAAAiO,UAAA;UACAtL,MAAA,CAAAoD,OAAA,CAAA/F,IAAA,CAAAiO,UAAA,EAAApL,OAAA,WAAAqL,MAAA;YAAA,IAAAC,MAAA,OAAAhI,eAAA,CAAAC,OAAA,EAAA8H,MAAA;cAAA7H,GAAA,GAAA8H,MAAA;cAAA7H,KAAA,GAAA6H,MAAA;YACA3B,YAAA,CAAA3F,IAAA;cAAA9G,IAAA,EAAAsG,GAAA;cAAAC,KAAA,IAAA1C,MAAA,CAAA0C,KAAA,iBAAAvC,OAAA;YAAA;UACA;QACA;;QAEA;QACAyI,YAAA,CAAAhG,IAAA,WAAAC,CAAA,EAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAJ,KAAA,GAAAG,CAAA,CAAAH,KAAA;QAAA;;QAEA;QACA,IAAA+G,SAAA;QACA,IAAAzH,UAAA;;QAEA;QACA4G,YAAA,CAAAc,OAAA,GAAAzK,OAAA,WAAA8D,IAAA,EAAAC,KAAA;UACAyG,SAAA,CAAAxG,IAAA,CAAAF,IAAA,CAAA5G,IAAA;UACA6F,UAAA,CAAAiB,IAAA;YACAP,KAAA,EAAAK,IAAA,CAAAL,KAAA;YACAQ,SAAA;cAAAC,KAAA,EAAAlB,MAAA,CAAAe,KAAA,GAAAf,MAAA,CAAAU,MAAA;YAAA;UACA;QACA;;QAEA;QACA,IAAAU,MAAA;UACAwB,KAAA;YACAf,IAAA;YACA1H,IAAA,EAAAqN,SAAA;YACAlF,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA;YACA7I,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA4F;UACA;QACA;QAEA,KAAA1E,MAAA,CAAAqM,uBAAA,CAAA1E,SAAA,CAAA5B,MAAA;QACArF,OAAA,CAAAC,GAAA;MACA;IACA;IAEAuM,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAlN,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAA+M,+BAAA,EAAA5J,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,0BAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACAqO,MAAA,CAAA5M,eAAA,GAAAkD,QAAA,CAAA3E,IAAA,CAAAyB,eAAA;UACA4M,MAAA,CAAA7M,iBAAA,GAAAmD,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACA;QACAuJ,MAAA,CAAAtJ,QAAA,CAAAD,KAAA;MACA;IACA;IAEA;IACAwJ,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MACA;MACA,UAAApN,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAiN,gCAAA,EAAA9J,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,2BAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACAuO,OAAA,CAAAC,oBAAA,CAAA7J,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACA;QACAyJ,OAAA,CAAAxJ,QAAA,CAAAD,KAAA;MACA;IACA;IAEA2J,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA;MACA,UAAAvN,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAEA,IAAAoN,8BAAA,EAAAjK,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,yBAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA0O,OAAA,CAAAC,gBAAA,CAAAhK,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACA4J,OAAA,CAAA3J,QAAA,CAAAD,KAAA;MACA;IACA;IAEA;IACA6J,gBAAA,WAAAA,iBAAA3O,IAAA;MACA,SAAAkB,MAAA,CAAA0N,UAAA,IAAA5O,IAAA;QACA4B,OAAA,CAAAC,GAAA,sBAAA7B,IAAA;;QAEA;QACA,IAAA6O,MAAA,QAAAC,0CAAA;QACAlN,OAAA,CAAAC,GAAA,aAAAgN,MAAA;;QAEA;QACA,IAAAE,UAAA,QAAAC,oCAAA;QACApN,OAAA,CAAAC,GAAA,aAAAkN,UAAA;QAEA,IAAAE,eAAA;QACA,IAAAC,mBAAA;;QAEA;QACAH,UAAA,CAAAlM,OAAA,WAAA4B,SAAA;UACA;UACA,IAAA0K,YAAA,GAAAnP,IAAA,CAAAoP,cAAA,IAAApP,IAAA,CAAAoP,cAAA,CAAA3K,SAAA,IACA,EAAAb,MAAA,CAAA5D,IAAA,CAAAoP,cAAA,CAAA3K,SAAA,kBAAAV,OAAA,MACA;UACAkL,eAAA,CAAApI,IAAA,CAAAsI,YAAA;;UAEA;UACA,IAAAE,gBAAA,GAAArP,IAAA,CAAAsP,kBAAA,IAAAtP,IAAA,CAAAsP,kBAAA,CAAA7K,SAAA,IACA,EAAAb,MAAA,CAAA5D,IAAA,CAAAsP,kBAAA,CAAA7K,SAAA,kBAAAV,OAAA,MACA;UACAmL,mBAAA,CAAArI,IAAA,CAAAwI,gBAAA;QACA;QAEAzN,OAAA,CAAAC,GAAA,YAAAgN,MAAA,CAAA7I,GAAA,WAAA1F,KAAA;UAAA,UAAAQ,MAAA,CAAAR,KAAA;QAAA;QACAsB,OAAA,CAAAC,GAAA,YAAAoN,eAAA;QACArN,OAAA,CAAAC,GAAA,YAAAqN,mBAAA;;QAEA;QACA,IAAAjI,MAAA;UACA;UACAsI,MAAA;YACAvP,IAAA;YAAA;YACA6H,SAAA;cAAAd,KAAA;YAAA;UACA;UACAG,IAAA;YAAAC,IAAA;YAAAC,KAAA;YAAAC,MAAA;YAAAC,YAAA;UAAA;UACAY,KAAA;YACAR,IAAA;YACA8H,WAAA;YACAxP,IAAA,EAAA6O,MAAA,CAAA7I,GAAA,WAAA1F,KAAA;cAAA,UAAAQ,MAAA,CAAAR,KAAA;YAAA;YAAA;YACA6H,SAAA;cACApB,KAAA;cACAsB,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA0B,KAAA;YACAf,IAAA;YACA3H,IAAA;YACA2I,aAAA;cAAA3B,KAAA;YAAA;YACAoB,SAAA;cAAApB,KAAA;YAAA;YACAwB,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;YACA4B,SAAA;cAAAH,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UAEA6B,MAAA,GACA;YACA7I,IAAA;YAAA;YACA2H,IAAA;YACA1H,IAAA,EAAAiP,eAAA;YACAQ,MAAA;YAAA;YACAjH,SAAA;cAAAzB,KAAA;cAAA2I,KAAA;YAAA;YACA5I,SAAA;cAAAC,KAAA;YAAA;YACA4I,MAAA;YACAC,UAAA;UACA,GACA;YACA7P,IAAA;YAAA;YACA2H,IAAA;YACA1H,IAAA,EAAAkP,mBAAA;YACAO,MAAA;YAAA;YACAjH,SAAA;cAAAzB,KAAA;cAAA2I,KAAA;YAAA;YACA5I,SAAA;cAAAC,KAAA;YAAA;YACA4I,MAAA;YACAC,UAAA;UACA;QAEA;QAEA,KAAA1O,MAAA,CAAA0N,UAAA,CAAA/F,SAAA,CAAA5B,MAAA;QACArF,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAgO,wBAAA,WAAAA,yBAAA;MACA,IAAAhB,MAAA;MACA,IAAAiB,WAAA,OAAA3P,IAAA;MAEA,SAAA4P,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAA7P,IAAA,CAAA2P,WAAA,CAAAzP,WAAA,IAAAyP,WAAA,CAAAvP,QAAA,KAAAwP,CAAA;QACA,IAAAzP,KAAA,GAAA0P,IAAA,CAAAzP,QAAA;QACAsO,MAAA,CAAAhI,IAAA,CAAAvG,KAAA;MACA;MAEA,OAAAuO,MAAA;IACA;IAEA;IACAoB,kBAAA,WAAAA,mBAAA;MACA,IAAAlB,UAAA;MACA,IAAAe,WAAA,OAAA3P,IAAA;MAEA,SAAA4P,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAA7P,IAAA,CAAA2P,WAAA,CAAAzP,WAAA,IAAAyP,WAAA,CAAAvP,QAAA,KAAAwP,CAAA;QACA,IAAA3P,IAAA,GAAA4P,IAAA,CAAA3P,WAAA;QACA,IAAAC,KAAA,GAAA0P,IAAA,CAAAzP,QAAA;QACA,IAAAkE,SAAA,MAAA3D,MAAA,CAAAV,IAAA,EAAAU,MAAA,CAAAC,MAAA,CAAAT,KAAA,EAAAU,QAAA;QACA+N,UAAA,CAAAlI,IAAA,CAAApC,SAAA;MACA;MAEA,OAAAsK,UAAA;IACA;IAEA;IACAD,0CAAA,WAAAA,2CAAA;MACA,IAAAD,MAAA;MAEA,UAAAzN,gBAAA;QACAQ,OAAA,CAAAoF,IAAA;QACA,YAAA6I,wBAAA;MACA;;MAEA;MACA,IAAAK,qBAAA,QAAA9O,gBAAA,CAAA+O,KAAA,MAAAnK,GAAA,CAAApC,MAAA;QAAAwM,sBAAA,OAAAjK,eAAA,CAAAC,OAAA,EAAA8J,qBAAA;QAAA9P,IAAA,GAAAgQ,sBAAA;QAAA9P,KAAA,GAAA8P,sBAAA;MACA,IAAAC,cAAA,OAAAlQ,IAAA,CAAAC,IAAA,EAAAE,KAAA;;MAEA,SAAAyP,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAA7P,IAAA,CAAAkQ,cAAA,CAAAhQ,WAAA,IAAAgQ,cAAA,CAAA9P,QAAA,KAAAwP,CAAA;QACA,IAAAO,QAAA,GAAAN,IAAA,CAAAzP,QAAA;QACAsO,MAAA,CAAAhI,IAAA,CAAAyJ,QAAA;MACA;MAEA,OAAAzB,MAAA;IACA;IAEA;IACAG,oCAAA,WAAAA,qCAAA;MACA,IAAAD,UAAA;MAEA,UAAA3N,gBAAA;QACAQ,OAAA,CAAAoF,IAAA;QACA,YAAAiJ,kBAAA;MACA;;MAEA;MACA,IAAAM,sBAAA,QAAAnP,gBAAA,CAAA+O,KAAA,MAAAnK,GAAA,CAAApC,MAAA;QAAA4M,sBAAA,OAAArK,eAAA,CAAAC,OAAA,EAAAmK,sBAAA;QAAAnQ,IAAA,GAAAoQ,sBAAA;QAAAlQ,KAAA,GAAAkQ,sBAAA;MACA,IAAAH,cAAA,OAAAlQ,IAAA,CAAAC,IAAA,EAAAE,KAAA;;MAEA,SAAAyP,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAA7P,IAAA,CAAAkQ,cAAA,CAAAhQ,WAAA,IAAAgQ,cAAA,CAAA9P,QAAA,KAAAwP,CAAA;QACA,IAAAU,OAAA,GAAAT,IAAA,CAAA3P,WAAA;QACA,IAAAiQ,QAAA,GAAAN,IAAA,CAAAzP,QAAA;QACA,IAAAkE,SAAA,MAAA3D,MAAA,CAAA2P,OAAA,EAAA3P,MAAA,CAAAC,MAAA,CAAAuP,QAAA,EAAAtP,QAAA;QACA+N,UAAA,CAAAlI,IAAA,CAAApC,SAAA;MACA;MAEA,OAAAsK,UAAA;IACA;IAEA;IACA2B,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA;MACA,UAAAxP,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACAO,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAA2C,MAAA;QACArD,UAAA,OAAAA,UAAA;QACAsD,SAAA,OAAArD,gBAAA,CAAA6C,OAAA;QAAA;QACA5C,WAAA,OAAAA;MACA;MAGA,IAAAqP,0BAAA,EAAAlM,MAAA,EAAAE,IAAA,WAAAC,QAAA;QACA/C,OAAA,CAAAC,GAAA,qBAAA8C,QAAA;QACA,IAAAA,QAAA,CAAA3E,IAAA;UACA2Q,OAAA,CAAAC,cAAA,CAAAjM,QAAA,CAAA3E,IAAA;QACA;MACA,GAAA6E,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,cAAAA,KAAA;QACA6L,OAAA,CAAA5L,QAAA,CAAAD,KAAA;MACA;IACA;IACA;IACA7C,iBAAA,WAAAA,kBAAA;MAAA,IAAA4O,OAAA;MACA,KAAAtP,iBAAA;MACA,IAAAuP,iCAAA,IAAApM,IAAA,WAAAC,QAAA;QACAkM,OAAA,CAAAvP,iBAAA,GAAAqD,QAAA,CAAA3E,IAAA;QACA;QACA,IAAA6Q,OAAA,CAAAvP,iBAAA,CAAAiF,MAAA;UACA3E,OAAA,CAAAC,GAAA,cAAAgP,OAAA,CAAAvP,iBAAA;UACAuP,OAAA,CAAA1P,UAAA,GAAA0P,OAAA,CAAAvP,iBAAA,IAAA+E,GAAA;UACA;UACAwK,OAAA,CAAAE,SAAA;YACAF,OAAA,CAAA/O,gBAAA;UACA;QACA;MACA,GAAA+C,KAAA,WAAAC,KAAA;QACAlD,OAAA,CAAAkD,KAAA,gBAAAA,KAAA;QACA+L,OAAA,CAAA9L,QAAA,CAAAD,KAAA;MACA,GAAAkM,OAAA;QACAH,OAAA,CAAAtP,iBAAA;MACA;IACA;IAEA;IACAqP,cAAA,WAAAA,eAAA5Q,IAAA;MACA,SAAAkB,MAAA,CAAA+P,QAAA,IAAAjR,IAAA;QACA;QACA,IAAAiH,MAAA,QAAA/F,MAAA,CAAA+P,QAAA,CAAAC,SAAA;QACA,IAAAjK,MAAA,IAAAA,MAAA,CAAA2B,MAAA,IAAA3B,MAAA,CAAA2B,MAAA;UACA3B,MAAA,CAAA2B,MAAA,IAAA5I,IAAA,IACA;YAAAsG,KAAA,GAAAtG,IAAA,CAAAmR,cAAA,UAAApN,OAAA;YAAAhE,IAAA;YAAA+G,SAAA;cAAAC,KAAA;YAAA;UAAA,GACA;YAAAT,KAAA,GAAAtG,IAAA,CAAAoR,aAAA,UAAArN,OAAA;YAAAhE,IAAA;YAAA+G,SAAA;cAAAC,KAAA;YAAA;UAAA,GACA;YAAAT,KAAA,GAAAtG,IAAA,CAAAqR,YAAA,UAAAtN,OAAA;YAAAhE,IAAA;YAAA+G,SAAA;cAAAC,KAAA;YAAA;UAAA,GACA;YAAAT,KAAA,GAAAtG,IAAA,CAAAsR,YAAA,UAAAvN,OAAA;YAAAhE,IAAA;YAAA+G,SAAA;cAAAC,KAAA;YAAA;UAAA,EACA,EACA,KAAA7F,MAAA,CAAA+P,QAAA,CAAApI,SAAA,CAAA5B,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAuH,oBAAA,WAAAA,qBAAAxO,IAAA;MACA,SAAAkB,MAAA,CAAAqQ,cAAA,IAAAvR,IAAA;QACA;QACA,IAAA6O,MAAA,QAAAC,0CAAA;QACA,IAAAC,UAAA,QAAAC,oCAAA;;QAEA;QACA,IAAAwC,cAAA,QAAAC,cAAA,CAAAzR,IAAA,CAAA0R,iBAAA,EAAA3C,UAAA;QACA,IAAA4C,aAAA,QAAAF,cAAA,CAAAzR,IAAA,CAAA4R,gBAAA,EAAA7C,UAAA;QACA,IAAA8C,YAAA,QAAAJ,cAAA,CAAAzR,IAAA,CAAA8R,eAAA,EAAA/C,UAAA;QACA,IAAAgD,YAAA,QAAAN,cAAA,CAAAzR,IAAA,CAAAgS,eAAA,EAAAjD,UAAA;;QAEA;QACA,IAAA9H,MAAA;UACAiB,KAAA;YACAR,IAAA;YACA8H,WAAA;YACAxP,IAAA,EAAA6O,MAAA,CAAA7I,GAAA,WAAA1F,KAAA;cAAA,UAAAQ,MAAA,CAAAR,KAAA;YAAA;YAAA;YACA6H,SAAA;cACApB,KAAA;cACAsB,MAAA;cAAA;cACAC,KAAA;YACA;YACAC,QAAA;cAAAC,SAAA;gBAAAzB,KAAA;cAAA;YAAA;UACA;UACA6B,MAAA,GACA;YACA7I,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAAwR,cAAA;YACA/B,MAAA;YAAA;YACAjH,SAAA;cAAAzB,KAAA;cAAA2I,KAAA;YAAA;YACA5I,SAAA;cAAAC,KAAA;YAAA;YACA4I,MAAA;YACAC,UAAA;UACA,GACA;YACA7P,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA2R,aAAA;YACAlC,MAAA;YAAA;YACAjH,SAAA;cAAAzB,KAAA;cAAA2I,KAAA;YAAA;YACA5I,SAAA;cAAAC,KAAA;YAAA;YACA4I,MAAA;YACAC,UAAA;UACA,GACA;YACA7P,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA6R,YAAA;YACApC,MAAA;YAAA;YACAjH,SAAA;cAAAzB,KAAA;cAAA2I,KAAA;YAAA;YACA5I,SAAA;cAAAC,KAAA;YAAA;YACA4I,MAAA;YACAC,UAAA;UACA,GACA;YACA7P,IAAA;YACA2H,IAAA;YACA1H,IAAA,EAAA+R,YAAA;YACAtC,MAAA;YAAA;YACAjH,SAAA;cAAAzB,KAAA;cAAA2I,KAAA;YAAA;YACA5I,SAAA;cAAAC,KAAA;YAAA;YACA4I,MAAA;YACAC,UAAA;UACA;QAEA;QAEA,KAAA1O,MAAA,CAAAqQ,cAAA,CAAA1I,SAAA,CAAA5B,MAAA;QACArF,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACA4P,cAAA,WAAAA,eAAAQ,OAAA,EAAAlD,UAAA;MAAA,IAAAmD,gBAAA,GAAAC,SAAA,CAAA5L,MAAA,QAAA4L,SAAA,QAAAzO,SAAA,GAAAyO,SAAA;MACA,KAAAF,OAAA,aAAAG,KAAA,CAAArD,UAAA,CAAAxI,MAAA,EAAA8L,IAAA;MAEA,OAAAtD,UAAA,CAAA/I,GAAA,WAAAvB,SAAA;QACA,IAAA6B,KAAA,GAAA2L,OAAA,CAAAxN,SAAA;QACA,OAAAyN,gBAAA,IAAA5L,KAAA,UAAAvC,OAAA,MAAAuC,KAAA;MACA;IACA;IAEA;IACAgM,mBAAA,WAAAA,oBAAA;MACA,IAAAzD,MAAA;MACA,IAAAE,UAAA;MACA,IAAAe,WAAA,OAAA3P,IAAA;MAEA,SAAA4P,CAAA,MAAAA,CAAA,OAAAA,CAAA;QACA,IAAAC,IAAA,OAAA7P,IAAA,CAAA2P,WAAA,CAAAzP,WAAA,IAAAyP,WAAA,CAAAvP,QAAA,KAAAwP,CAAA;QACA,IAAA3P,IAAA,GAAA4P,IAAA,CAAA3P,WAAA;QACA,IAAAC,KAAA,GAAA0P,IAAA,CAAAzP,QAAA;QAEAsO,MAAA,CAAAhI,IAAA,IAAA/F,MAAA,CAAAR,KAAA;QACAyO,UAAA,CAAAlI,IAAA,CAAA0L,QAAA,IAAAzR,MAAA,CAAAV,IAAA,EAAAU,MAAA,CAAAC,MAAA,CAAAT,KAAA,EAAAU,QAAA;MACA;MAEA;QAAA6N,MAAA,EAAAA,MAAA;QAAAE,UAAA,EAAAA;MAAA;IACA;IAEA;IACAjN,gBAAA,WAAAA,iBAAA;MACA;MACA,UAAAX,UAAA,UAAAC,gBAAA,SAAAC,WAAA,eAAAA,WAAA;QACA;MACA;MAEA,KAAA+M,oBAAA;MACA,KAAAsC,eAAA;MACA,KAAApC,qBAAA;MACA,KAAArC,qBAAA;MACA,KAAAG,qBAAA;MACA,KAAAqC,mBAAA;MACA,KAAA/E,uBAAA;MACA,KAAAqC,8BAAA;MACA,KAAAF,6BAAA;MACA,KAAAvH,8BAAA;MACA,KAAAU,0BAAA;MACA,KAAAG,6BAAA;MACA,KAAAG,2BAAA;;MAEA;MACA;IACA;IAEA,aACAkN,WAAA,WAAAA,YAAA;MACA,KAAA1Q,gBAAA;IACA;IAEA,aACA2Q,UAAA,WAAAA,WAAA;MACA;MACA,SAAAnR,iBAAA,CAAAiF,MAAA;QACA,KAAApF,UAAA,QAAAG,iBAAA,IAAA+E,GAAA;MACA;;MAEA;MACA,IAAAnG,GAAA,OAAAC,IAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAJ,GAAA,CAAAK,QAAA;MACA,IAAAK,SAAA,GAAAN,KAAA,cAAAA,KAAA;MACA,IAAAO,QAAA,GAAAP,KAAA,SAAAF,IAAA,OAAAA,IAAA;MACA,KAAAgB,gBAAA,MAAAN,MAAA,CAAAD,QAAA,OAAAC,MAAA,CAAAC,MAAA,CAAAH,SAAA,EAAAI,QAAA;MAEA,KAAA+D,QAAA,CAAA2N,OAAA;IACA;IAEAxQ,UAAA,WAAAA,WAAA;MACA,IAAAyQ,KAAA;;MAEA;MACA,KAAAC,cAAA;QACAC,KAAA;QACAC,QAAA,GACA;UAAAC,MAAA;UAAAhM,KAAA;QAAA,GACA;UAAAgM,MAAA;UAAAhM,KAAA;QAAA;MAEA;;MAEA;MACA,KAAA7F,MAAA,CAAA+P,QAAA,GAAAvR,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAAhC,QAAA,EAAA0B,KAAA;MACA,KAAAzR,MAAA,CAAAqQ,cAAA,GAAA7R,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAA1B,cAAA,EAAAoB,KAAA;MACA,KAAAzR,MAAA,CAAAqM,uBAAA,GAAA7N,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAA1F,uBAAA,EAAAoF,KAAA;MACA,KAAAzR,MAAA,CAAAqL,uBAAA,GAAA7M,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAA1G,uBAAA,EAAAoG,KAAA;MACA,KAAAzR,MAAA,CAAA2I,cAAA,GAAAnK,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAApJ,cAAA,EAAA8I,KAAA;MACA,KAAAzR,MAAA,CAAA0N,UAAA,GAAAlP,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAArE,UAAA,EAAA+D,KAAA;MACA,KAAAzR,MAAA,CAAAkJ,cAAA,GAAA1K,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAA7I,cAAA,EAAAuI,KAAA;MACA,KAAAzR,MAAA,CAAAmK,qBAAA,GAAA3L,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAA5H,qBAAA,EAAAsH,KAAA;MACA,KAAAzR,MAAA,CAAA4H,qBAAA,GAAApJ,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAAnK,qBAAA,EAAA6J,KAAA;MACA,KAAAzR,MAAA,CAAAgI,iBAAA,GAAAxJ,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAA/J,iBAAA,EAAAyJ,KAAA;MACA,KAAAzR,MAAA,CAAAoI,oBAAA,GAAA5J,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAA3J,oBAAA,EAAAqJ,KAAA;MACA,KAAAzR,MAAA,CAAAuE,kBAAA,GAAA/F,OAAA,CAAAsT,IAAA,MAAAC,KAAA,CAAAxN,kBAAA,EAAAkN,KAAA;;MAEA;MACA,KAAAO,iBAAA;MACA,KAAAC,uBAAA;MACA,KAAAC,gCAAA;MACA,KAAAC,gCAAA;MACA,KAAAC,uBAAA;MACA,KAAAC,mBAAA;MACA,KAAAC,uBAAA;MACA,KAAAC,8BAAA;MACA,KAAAC,8BAAA;MACA,KAAAC,0BAAA;MACA,KAAAC,6BAAA;MACA,KAAAC,2BAAA;IACA;IAEAX,iBAAA,WAAAA,kBAAA;MACA,KAAAhS,MAAA,CAAA+P,QAAA,CAAApI,SAAA;QACA9B,KAAA;QACAQ,OAAA;UACAC,OAAA;UACAM,SAAA,WAAAA,UAAAtD,MAAA;YACA,IAAA8B,KAAA,GAAAxC,UAAA,CAAAU,MAAA,CAAA8B,KAAA,EAAAvC,OAAA;YACA,IAAAgE,cAAA,GAAAzB,KAAA,CAAAjD,QAAA,GAAAY,OAAA;YACA,UAAAnD,MAAA,CAAA0D,MAAA,CAAAyD,UAAA,YAAAnH,MAAA,CAAA0D,MAAA,CAAAzE,IAAA,QAAAe,MAAA,CAAAiH,cAAA,oBAAAjH,MAAA,CAAA0D,MAAA,CAAAsP,OAAA;UACA;UACAnM,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAwI,MAAA;UACAwE,GAAA;UACA5M,IAAA;UACAU,SAAA;YAAAd,KAAA;YAAAiN,QAAA;UAAA;QACA;QACApL,MAAA;UACA7I,IAAA;UACA2H,IAAA;UACAuM,MAAA;UACAjU,IAAA;UACAkU,QAAA;YACApN,SAAA;cACAqN,UAAA;cACAC,aAAA;cACAC,WAAA;YACA;UACA;UACAC,SAAA;YAAA9L,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACAwN,KAAA;YACAxN,KAAA;YACAe,SAAA,WAAAA,UAAAtD,MAAA;cACA,IAAA8B,KAAA,GAAAxC,UAAA,CAAAU,MAAA,CAAA8B,KAAA,EAAAvC,OAAA;cACA,IAAAgE,cAAA,GAAAzB,KAAA,CAAAjD,QAAA,GAAAY,OAAA;cACA,UAAAnD,MAAA,CAAA0D,MAAA,CAAAzE,IAAA,OAAAe,MAAA,CAAAiH,cAAA,oBAAAjH,MAAA,CAAA0D,MAAA,CAAAsP,OAAA;YACA;UACA;QACA;MACA;IACA;IAIAP,mBAAA,WAAAA,oBAAA;MACA,KAAArS,MAAA,CAAA0N,UAAA,CAAA/F,SAAA;QACA9B,KAAA;QACAQ,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAwI,MAAA;UACAvP,IAAA;UACA6H,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACA8H,WAAA;UACAxP,IAAA;UACAmI,SAAA;YACApB,KAAA;YACAsB,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA,GACA;UACA7I,IAAA;UAAA;UACA2H,IAAA;UACA1H,IAAA;UACAyP,MAAA;UAAA;UACAjH,SAAA;YAAAzB,KAAA;YAAA2I,KAAA;UAAA;UACA5I,SAAA;YAAAC,KAAA;UAAA;UACA4I,MAAA;UACAC,UAAA;QACA,GACA;UACA7P,IAAA;UAAA;UACA2H,IAAA;UACA1H,IAAA;UACAyP,MAAA;UAAA;UACAjH,SAAA;YAAAzB,KAAA;YAAA2I,KAAA;UAAA;UACA5I,SAAA;YAAAC,KAAA;UAAA;UACA4I,MAAA;UACAC,UAAA;QACA;MAEA;IACA;IAEAuD,uBAAA,WAAAA,wBAAA;MACA,KAAAjS,MAAA,CAAAqQ,cAAA,CAAA1I,SAAA;QACA9B,KAAA;QACAQ,OAAA;UACAC,OAAA;UACAG,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAwI,MAAA;UACAvP,IAAA;UACA6H,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACA8H,WAAA;UACAxP,IAAA;UACAmI,SAAA;YACApB,KAAA;YACAsB,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA,GACA;UACA7I,IAAA;UACA2H,IAAA;UACA1H,IAAA;UACAyP,MAAA;UAAA;UACAjH,SAAA;YAAAzB,KAAA;YAAA2I,KAAA;UAAA;UACA5I,SAAA;YAAAC,KAAA;UAAA;UACA4I,MAAA;UACAC,UAAA;QACA,GACA;UACA7P,IAAA;UACA2H,IAAA;UACA1H,IAAA;UACAyP,MAAA;UAAA;UACAjH,SAAA;YAAAzB,KAAA;YAAA2I,KAAA;UAAA;UACA5I,SAAA;YAAAC,KAAA;UAAA;UACA4I,MAAA;UACAC,UAAA;QACA,GACA;UACA7P,IAAA;UACA2H,IAAA;UACA1H,IAAA;UACAyP,MAAA;UAAA;UACAjH,SAAA;YAAAzB,KAAA;YAAA2I,KAAA;UAAA;UACA5I,SAAA;YAAAC,KAAA;UAAA;UACA4I,MAAA;UACAC,UAAA;QACA,GACA;UACA7P,IAAA;UACA2H,IAAA;UACA1H,IAAA;UACAyP,MAAA;UAAA;UACAjH,SAAA;YAAAzB,KAAA;YAAA2I,KAAA;UAAA;UACA5I,SAAA;YAAAC,KAAA;UAAA;UACA4I,MAAA;UACAC,UAAA;QACA;MAEA;IACA;IAMA4E,oBAAA,WAAAA,qBAAA;MACA,KAAAtT,MAAA,CAAAuT,WAAA,CAAA5L,SAAA;QACA9B,KAAA;QACAQ,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAE,KAAA;QAAA;QACAc,KAAA;UACAR,IAAA;UACA1H,IAAA;UACAmI,SAAA;YACAC,QAAA;YACAC,MAAA;YAAA;YACAC,KAAA;YAAA;YACAvB,KAAA;UACA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA,GACA;UACAf,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA,GACA;UACAW,IAAA;UACA3H,IAAA;UACA2U,GAAA;UACAC,GAAA;UACAxM,SAAA;YACAL,SAAA;YACAf,KAAA;UACA;UACA2B,aAAA;YAAA3B,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA,EACA;QACA6B,MAAA,GACA;UACA7I,IAAA;UACA2H,IAAA;UACA1H,IAAA;UACA8G,SAAA;YAAAC,KAAA;UAAA;QACA,GACA;UACAhH,IAAA;UACA2H,IAAA;UACAkN,UAAA;UACA5U,IAAA;UACAwI,SAAA;YAAAzB,KAAA;YAAA2I,KAAA;UAAA;UACA5I,SAAA;YAAAC,KAAA;UAAA;UACA4I,MAAA;UACAC,UAAA;QACA;MAEA;IACA;IAEAwD,gCAAA,WAAAA,iCAAA;MACA,KAAAlS,MAAA,CAAAqM,uBAAA,CAAA1E,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;UACAe,SAAA,WAAAA,UAAAtD,MAAA;YACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;YACAyE,MAAA,CAAA3B,OAAA,WAAA8D,IAAA;cACA,IAAAoB,cAAA,GAAAjE,UAAA,CAAA6C,IAAA,CAAAL,KAAA,EAAAvC,OAAA,IAAAV,QAAA,GAAAY,OAAA;cACAE,MAAA,IAAAwC,IAAA,CAAAqB,MAAA,aAAAD,cAAA;YACA;YACA,OAAA5D,MAAA;UACA;QACA;QACA+C,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAA2M,GAAA;UAAA1M,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UAEA1H,IAAA;UACAmI,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACA7I,IAAA;UACA2H,IAAA;UACA1H,IAAA;QACA;MACA;IACA;IAEAqT,gCAAA,WAAAA,iCAAA;MACA,KAAAnS,MAAA,CAAAqL,uBAAA,CAAA1D,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;UACAe,SAAA,WAAAA,UAAAtD,MAAA;YACA,IAAAL,MAAA,GAAAK,MAAA,IAAAzE,IAAA;YACAyE,MAAA,CAAA3B,OAAA,WAAA8D,IAAA;cACA,IAAAoB,cAAA,GAAAjE,UAAA,CAAA6C,IAAA,CAAAL,KAAA,EAAAvC,OAAA,IAAAV,QAAA,GAAAY,OAAA;cACAE,MAAA,IAAAwC,IAAA,CAAAqB,MAAA,aAAAD,cAAA;YACA;YACA,OAAA5D,MAAA;UACA;QACA;QACA+C,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAA2M,GAAA;UAAA1M,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACA1H,IAAA;UACAmI,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACA7I,IAAA;UACA2H,IAAA;UACA1H,IAAA;QACA;MACA;IACA;IAEAsT,uBAAA,WAAAA,wBAAA;MACA,KAAApS,MAAA,CAAA2I,cAAA,CAAAhB,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACA1H,IAAA;UACAmI,SAAA;YACAC,QAAA;YACAC,MAAA;YAAA;YACAC,KAAA;YAAA;YACAvB,KAAA;UACA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA,GACA;UACA7I,IAAA;UACA2H,IAAA;UACAmN,KAAA;UACA/N,SAAA;YACAC,KAAA;YACAa,WAAA;YACAkN,WAAA;UACA;UACAZ,QAAA;YACApN,SAAA;cACAC,KAAA;YACA;UACA;UACA/G,IAAA;QACA;MAEA;IACA;IAEAwT,uBAAA,WAAAA,wBAAA;MACA,KAAAtS,MAAA,CAAAkJ,cAAA,CAAAvB,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACA1H,IAAA;UACAmI,SAAA;YACApB,KAAA;YACAqB,QAAA;YAAA;YACAC,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACA3H,IAAA;UACAoI,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACA7I,IAAA;UACA2H,IAAA;UACA1H,IAAA;QACA;MACA;IACA;IAEAyT,8BAAA,WAAAA,+BAAA;MACA,KAAAvS,MAAA,CAAAmK,qBAAA,CAAAxC,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACA1H,IAAA;UACAmI,SAAA;YACApB,KAAA;YACAqB,QAAA;YAAA;YACAC,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACA3H,IAAA;UACAoI,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACA7I,IAAA;UACA2H,IAAA;UACA1H,IAAA;QACA;MACA;IACA;IAEA0T,8BAAA,WAAAA,+BAAA;MACA,KAAAxS,MAAA,CAAA4H,qBAAA,CAAAD,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACA1H,IAAA;UAAA;UACAmI,SAAA;YACApB,KAAA;YACAqB,QAAA;YAAA;YACAC,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACA7I,IAAA;UACA2H,IAAA;UACA1H,IAAA;QACA;MACA;IACA;IAEA2T,0BAAA,WAAAA,2BAAA;MACA,KAAAzS,MAAA,CAAAgI,iBAAA,CAAAL,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACA1H,IAAA;UAAA;UACAmI,SAAA;YACApB,KAAA;YACAqB,QAAA;YAAA;YACAC,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACA7I,IAAA;UACA2H,IAAA;UACA1H,IAAA;QACA;MACA;IACA;IAEA4T,6BAAA,WAAAA,8BAAA;MACA,KAAA1S,MAAA,CAAAoI,oBAAA,CAAAT,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACA1H,IAAA;UAAA;UACAmI,SAAA;YACApB,KAAA;YACAqB,QAAA;YAAA;YACAC,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACA7I,IAAA;UACA2H,IAAA;UACA1H,IAAA;QACA;MACA;IACA;IAEA6T,2BAAA,WAAAA,4BAAA;MACA,KAAA3S,MAAA,CAAAuE,kBAAA,CAAAoD,SAAA;QACAtB,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAG,IAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,MAAA;UAAAC,YAAA;QAAA;QACAY,KAAA;UACAR,IAAA;UACA1H,IAAA;UAAA;UACAmI,SAAA;YACApB,KAAA;YACAqB,QAAA;YAAA;YACAC,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA;UACAf,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA6B,MAAA;UACA7I,IAAA;UACA2H,IAAA;UACA1H,IAAA;QACA;MACA;IACA;IAEA+U,mBAAA,WAAAA,oBAAA;MACA,KAAA7T,MAAA,CAAA8T,UAAA,CAAAnM,SAAA;QACA9B,KAAA;QACAQ,OAAA;UACAC,OAAA;UACAC,WAAA;YAAAC,IAAA;UAAA;UACAC,eAAA;UACAC,WAAA;UACAC,SAAA;YAAAd,KAAA;UAAA;QACA;QACAwI,MAAA;UACAvP,IAAA;UACA6H,SAAA;YAAAd,KAAA;UAAA;QACA;QACAmB,KAAA;UACAR,IAAA;UACA1H,IAAA;UACAmI,SAAA;YACApB,KAAA;YACAsB,MAAA;YAAA;YACAC,KAAA;UACA;UACAC,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA;QACA0B,KAAA,GACA;UACAf,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;UACA4B,SAAA;YAAAH,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA,GACA;UACAW,IAAA;UACA3H,IAAA;UACA2I,aAAA;YAAA3B,KAAA;UAAA;UACAoB,SAAA;YAAApB,KAAA;UAAA;UACAwB,QAAA;YAAAC,SAAA;cAAAzB,KAAA;YAAA;UAAA;QACA,EACA;QACA6B,MAAA,GACA;UACA7I,IAAA;UACA2H,IAAA;UACA1H,IAAA;UACA8G,SAAA;YAAAC,KAAA;UAAA;QACA,GACA;UACAhH,IAAA;UACA2H,IAAA;UACAkN,UAAA;UACA5U,IAAA;UACAwI,SAAA;YAAAzB,KAAA;YAAA2I,KAAA;UAAA;UACA5I,SAAA;YAAAC,KAAA;UAAA;UACA4I,MAAA;UACAC,UAAA;QACA;MAEA;IACA;IAEAvN,YAAA,WAAAA,aAAA;MACAM,MAAA,CAAAC,MAAA,MAAA1B,MAAA,EAAA2B,OAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAA,KAAA,CAAAmS,MAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}