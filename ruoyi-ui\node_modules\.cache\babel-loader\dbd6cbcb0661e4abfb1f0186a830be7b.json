{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPlan\\chartMethodsExtended.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPlan\\chartMethodsExtended.js", "mtime": 1756456493845}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_default", "exports", "default", "methods", "initMaterialCloud", "console", "log", "JSON", "stringify", "highFrequencyMaterialData", "chartDom", "document", "getElementById", "error", "innerHTML", "rawMaterialList", "length", "highFrequencyMaterials", "sort", "a", "b", "inAmt", "container", "createElement", "style", "width", "height", "position", "overflow", "colors", "maxFontSize", "minFontSize", "for<PERSON>ach", "item", "index", "fontSize", "div", "textContent", "itemName", "concat", "fontWeight", "color", "transform", "Math", "random", "left", "top", "whiteSpace", "textShadow", "transition", "cursor", "zIndex", "addEventListener", "toString", "tooltip", "toFixed", "backgroundColor", "padding", "borderRadius", "className", "append<PERSON><PERSON><PERSON>", "querySelector", "<PERSON><PERSON><PERSON><PERSON>", "initTopSuppliersChart", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "myChart", "response", "supplierData", "_t", "w", "_context", "n", "reinitChart", "showLoading", "p", "getSupplierList", "dimensionType", "currentDimensionType", "orderType", "selectedOrderType", "v", "data", "Array", "isArray", "dashboardData", "purchaseProviderTotalAmountList", "originalTopSuppliersData", "_toConsumableArray2", "renderAndPaginateTopSuppliers", "hideLoading", "f", "chartInstance", "suppliersDataToRender", "intervalId", "clearInterval", "suppliersData", "ddze", "currentIndex", "groupSize", "updateChart", "setOption", "title", "text", "textStyle", "xAxis", "show", "yAxis", "series", "endIndex", "min", "displayData", "slice", "reverse", "max<PERSON><PERSON><PERSON>", "max", "apply", "map", "parseFloat", "unit", "valueDiv", "fractionDigits", "option", "trigger", "axisPointer", "type", "formatter", "params", "originalValue", "value", "name", "toLocaleString", "undefined", "minimumFractionDigits", "maximumFractionDigits", "grid", "right", "bottom", "containLabel", "axisLabel", "axisLine", "lineStyle", "splitLine", "max<PERSON><PERSON><PERSON>", "suppName", "substring", "interval", "itemStyle", "label", "setInterval", "initSupplierRiskChart", "supplierRiskData", "riskData", "top10RiskSuppliers", "riskNum", "barColor", "suppId", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "borderWidth", "borderColor", "off", "on", "componentType", "seriesType", "currentUrl", "window", "location", "href", "baseUrl", "replace", "dimensionTypeMapping", "mappedDimensionType", "url", "supplierId", "原始dimensionType", "映射后dimensionType", "open", "warn", "bind", "getZr", "pointInPixel", "offsetX", "offsetY", "containPixel", "setCursorStyle", "initPriceTrendChart", "priceAndStoreData", "formatDate", "dateStr", "year", "month", "day", "allDates", "Set", "procurementPriceVoList", "priceGroup", "priceList", "add", "recordDate", "procurementPurchaseAmountVoList", "amountGroup", "amountList", "from", "xAxisData", "legendData", "colorIndex", "priceData", "date", "found", "find", "price", "push", "priceName", "yAxisIndex", "smooth", "symbol", "symbolSize", "connectNulls", "amountData", "amount", "amountName", "priceMin", "priceMax", "amountMin", "amountMax", "priceValues", "filter", "s", "flatMap", "amountValues", "str", "axisValueLabel", "seriesName", "includes", "marker", "legend", "uniqueMonths", "monthsCount", "size", "totalDataPoints", "idealInterval", "floor", "originalDateStr", "parseInt", "initNewPriceTrendChart", "newPriceAndStoreDataList", "newPriceAndStoreData", "materialData", "materialName", "pageButtonItemGap", "pageButtonGap", "pageButtonPosition", "pageFormatter", "pageIconColor", "pageIconInactiveColor", "pageIconSize", "pageTextStyle"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/views/purchaseDashboardPlan/chartMethodsExtended.js"], "sourcesContent": ["import * as echarts from 'echarts'\r\n\r\nexport default {\r\n  methods: {\r\n    // 物料词云\r\n    initMaterialCloud() {\r\n      console.log('initMaterialCloud started with data:', JSON.stringify(this.highFrequencyMaterialData))\r\n\r\n      const chartDom = document.getElementById('materialCloudChart')\r\n      if (!chartDom) {\r\n        console.error('Cannot find materialCloudChart DOM element')\r\n        return\r\n      }\r\n\r\n      chartDom.innerHTML = ''\r\n\r\n      const rawMaterialList = this.highFrequencyMaterialData\r\n      if (!rawMaterialList || rawMaterialList.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">无高频采购物料数据</div>'\r\n        console.log('No high frequency materials to display for materialCloudChart.')\r\n        return\r\n      }\r\n\r\n      // 按入库金额排序，而不是入库数量\r\n      const highFrequencyMaterials = rawMaterialList.sort((a, b) => (b.inAmt || 0) - (a.inAmt || 0))\r\n\r\n      const container = document.createElement('div')\r\n      container.style.width = '100%'\r\n      container.style.height = '100%'\r\n      container.style.position = 'relative'\r\n      container.style.overflow = 'hidden'\r\n\r\n      const colors = [\r\n        '#4fc3f7', '#a5d6a7', '#ffcc80', '#ef9a9a', '#ce93d8',\r\n        '#90caf9', '#80deea', '#c5e1a5', '#fff59d', '#ffab91'\r\n      ]\r\n\r\n      const maxFontSize = 50\r\n      const minFontSize = 12\r\n\r\n      highFrequencyMaterials.forEach((item, index) => {\r\n        let fontSize = maxFontSize - (index * 2)\r\n        if (fontSize < minFontSize) {\r\n          fontSize = minFontSize\r\n        }\r\n\r\n        const div = document.createElement('div')\r\n        div.textContent = item.itemName\r\n        div.style.position = 'absolute'\r\n        div.style.fontSize = `${fontSize}px`\r\n        div.style.fontWeight = 'bold'\r\n        div.style.color = colors[index % colors.length]\r\n        div.style.transform = `rotate(${Math.random() * 50 - 25}deg)`\r\n        div.style.left = `${5 + Math.random() * 70}%`\r\n        div.style.top = `${5 + Math.random() * 70}%`\r\n        div.style.whiteSpace = 'nowrap'\r\n        div.style.textShadow = '1px 1px 3px rgba(0,0,0,0.3)'\r\n        div.style.transition = 'all 0.3s ease'\r\n        div.style.cursor = 'pointer'\r\n        div.style.zIndex = highFrequencyMaterials.length - index\r\n\r\n        div.addEventListener('mouseover', function() {\r\n          this.style.transform = `rotate(0deg) scale(1.3)`\r\n          this.style.zIndex = (highFrequencyMaterials.length * 2).toString()\r\n          this.style.textShadow = '2px 2px 5px rgba(0,0,0,0.5)'\r\n\r\n          const tooltip = document.createElement('div')\r\n          // 显示入库金额而不是入库数量\r\n          tooltip.textContent = `入库金额: ${(item.inAmt || 0).toFixed(2)} 万元`\r\n          tooltip.style.position = 'absolute'\r\n          tooltip.style.backgroundColor = 'rgba(0,0,0,0.7)'\r\n          tooltip.style.color = '#fff'\r\n          tooltip.style.padding = '3px 8px'\r\n          tooltip.style.borderRadius = '4px'\r\n          tooltip.style.fontSize = '12px'\r\n          tooltip.style.top = 'calc(100% + 5px)'\r\n          tooltip.style.left = '50%'\r\n          tooltip.style.transform = 'translateX(-50%)'\r\n          tooltip.style.whiteSpace = 'nowrap'\r\n          tooltip.style.zIndex = (highFrequencyMaterials.length * 2 + 1).toString()\r\n          tooltip.className = 'material-tooltip'\r\n          this.appendChild(tooltip)\r\n        })\r\n\r\n        div.addEventListener('mouseout', function() {\r\n          this.style.transform = `rotate(${Math.random() * 50 - 25}deg) scale(1)`\r\n          this.style.zIndex = (highFrequencyMaterials.length - index).toString()\r\n          this.style.textShadow = '1px 1px 3px rgba(0,0,0,0.3)'\r\n\r\n          const tooltip = this.querySelector('.material-tooltip')\r\n          if (tooltip) {\r\n            this.removeChild(tooltip)\r\n          }\r\n        })\r\n\r\n        container.appendChild(div)\r\n      })\r\n\r\n      chartDom.appendChild(container)\r\n      console.log(`Material cloud chart updated with ${highFrequencyMaterials.length} items.`)\r\n    },\r\n\r\n    // TOP供应商图表\r\n    async initTopSuppliersChart() {\r\n      console.log('initTopSuppliersChart started')\r\n      const myChart = this.reinitChart('topSuppliersChart')\r\n      if (!myChart) return\r\n\r\n      // 使用API获取数据，传递orderType参数\r\n      myChart.showLoading()\r\n      try {\r\n        const response = await this.getSupplierList({\r\n          dimensionType: this.currentDimensionType,\r\n          orderType: this.selectedOrderType\r\n        })\r\n        \r\n        let supplierData = []\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          supplierData = response.data\r\n        } else {\r\n          console.error('从showSuppList API获取的数据无效:', response)\r\n          // 如果API失败，使用仪表板原始数据作为备用\r\n          supplierData = this.dashboardData.purchaseProviderTotalAmountList || []\r\n        }\r\n        \r\n        this.originalTopSuppliersData = [...supplierData]\r\n        this.renderAndPaginateTopSuppliers(myChart, supplierData)\r\n      } catch (error) {\r\n        console.error('初始化TOP供应商图表失败:', error)\r\n        // 如果API失败，使用仪表板原始数据作为备用\r\n        this.originalTopSuppliersData = this.dashboardData.purchaseProviderTotalAmountList \r\n          ? [...this.dashboardData.purchaseProviderTotalAmountList] \r\n          : []\r\n        this.renderAndPaginateTopSuppliers(myChart, this.originalTopSuppliersData)\r\n      } finally {\r\n        myChart.hideLoading()\r\n      }\r\n    },\r\n\r\n    renderAndPaginateTopSuppliers(chartInstance, suppliersDataToRender) {\r\n      if (!chartInstance) return\r\n\r\n      if (chartInstance.intervalId) {\r\n        clearInterval(chartInstance.intervalId)\r\n        chartInstance.intervalId = null\r\n      }\r\n\r\n      const suppliersData = suppliersDataToRender.sort((a, b) => b.ddze - a.ddze)\r\n\r\n      let currentIndex = 0\r\n      const groupSize = 10\r\n\r\n      const updateChart = () => {\r\n        if (suppliersData.length === 0) {\r\n          chartInstance.setOption({\r\n            title: {\r\n              text: '暂无数据',\r\n              left: 'center',\r\n              top: 'center',\r\n              textStyle: { color: 'rgba(255,255,255,0.7)' }\r\n            },\r\n            xAxis: { show: false },\r\n            yAxis: { show: false },\r\n            series: []\r\n          }, true)\r\n          return\r\n        }\r\n\r\n        const endIndex = Math.min(currentIndex + groupSize, suppliersData.length)\r\n        let displayData = suppliersData.slice(currentIndex, endIndex)\r\n        displayData = displayData.reverse()\r\n\r\n        let maxDdze = 0\r\n        if (displayData.length > 0) {\r\n          maxDdze = Math.max(...displayData.map(item => parseFloat(item.ddze) || 0))\r\n        }\r\n\r\n        let unit = '万元'\r\n        let valueDiv = 1\r\n        let fractionDigits = 1\r\n\r\n        if (maxDdze > 100000) {\r\n          unit = '亿元'\r\n          valueDiv = 10000\r\n          fractionDigits = 2\r\n        }\r\n\r\n        const colors = ['#83bff6', '#ea7ccc', '#5fd8b6', '#ffb980', '#e879ed', '#ffdb5c', '#ff9a9e', '#a18cd1', '#fbc2eb', '#84fab0']\r\n\r\n        const option = {\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: {\r\n              type: 'shadow'\r\n            },\r\n            formatter: function(params) {\r\n              const item = params[0]\r\n              const originalValue = parseFloat(item.value)\r\n              return `${item.name}: ${originalValue.toLocaleString(undefined, { minimumFractionDigits: fractionDigits, maximumFractionDigits: fractionDigits })}${unit}`\r\n            }\r\n          },\r\n          grid: {\r\n            left: '3%',\r\n            right: '15%',\r\n            bottom: '3%',\r\n            top: '3%',\r\n            containLabel: true\r\n          },\r\n          xAxis: {\r\n            show: true,\r\n            type: 'value',\r\n            axisLabel: {\r\n              color: '#eee',\r\n              formatter: function(value) {\r\n                return parseFloat(value).toFixed(0) + unit\r\n              }\r\n            },\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#eee'\r\n              }\r\n            },\r\n            splitLine: {\r\n              show: false\r\n            }\r\n          },\r\n          yAxis: {\r\n            show: true,\r\n            type: 'category',\r\n            data: displayData.map(item => {\r\n              const maxLength = 36\r\n              let name = item.suppName\r\n              if (name.length > maxLength) {\r\n                name = name.substring(0, maxLength - 3) + \"...\"\r\n              }\r\n              return name\r\n            }),\r\n            axisLabel: {\r\n              color: '#eee',\r\n              interval: 0\r\n            },\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#eee'\r\n              }\r\n            }\r\n          },\r\n          series: [\r\n            {\r\n              name: '供货金额',\r\n              type: 'bar',\r\n              data: displayData.map((item, index) => {\r\n                return {\r\n                  value: (parseFloat(item.ddze) || 0) / valueDiv,\r\n                  itemStyle: {\r\n                    color: colors[index % colors.length],\r\n                    borderRadius: [0, 5, 5, 0]\r\n                  }\r\n                }\r\n              }),\r\n              label: {\r\n                show: true,\r\n                position: 'right',\r\n                formatter: function(params) {\r\n                  return parseFloat(params.value).toLocaleString(undefined, { minimumFractionDigits: fractionDigits, maximumFractionDigits: fractionDigits }) + ' ' + unit\r\n                },\r\n                color: '#fff'\r\n              }\r\n            }\r\n          ]\r\n        }\r\n        chartInstance.setOption(option, true)\r\n      }\r\n\r\n      updateChart()\r\n\r\n      if (suppliersData.length > groupSize) {\r\n        chartInstance.intervalId = setInterval(() => {\r\n          currentIndex += groupSize\r\n          if (currentIndex >= suppliersData.length) {\r\n            currentIndex = 0\r\n          }\r\n          updateChart()\r\n        }, 15000)\r\n      }\r\n    },\r\n\r\n    // TOP10供应商风险提醒图表\r\n    initSupplierRiskChart() {\r\n      console.log('initSupplierRiskChart started with data:', JSON.stringify(this.supplierRiskData))\r\n      const myChart = this.reinitChart('supplierRiskChart')\r\n      if (!myChart) return\r\n\r\n      const riskData = this.supplierRiskData || []\r\n\r\n      const top10RiskSuppliers = riskData\r\n        .sort((a, b) => b.riskNum - a.riskNum)\r\n        .slice(0, 10)\r\n        .reverse()\r\n\r\n      if (top10RiskSuppliers.length === 0) {\r\n        document.getElementById('supplierRiskChart').innerHTML = '<div class=\"chart-placeholder\">无供应商风险数据</div>'\r\n        return\r\n      }\r\n\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function (params) {\r\n            const item = params[0]\r\n            return `${item.name}: ${item.value}<br/><span style=\"color: #00d4ff;\">点击查看详情</span>`\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '10%',\r\n          bottom: '3%',\r\n          top: '3%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: '{value}'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: top10RiskSuppliers.map(item => item.suppName),\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 0\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '风险数量',\r\n            type: 'bar',\r\n            data: top10RiskSuppliers.map((item, index) => {\r\n              let barColor\r\n              if (index >= 7) {\r\n                barColor = '#FF4136'\r\n              } else if (index >= 3) {\r\n                barColor = '#FF851B'\r\n              } else {\r\n                barColor = '#FFDC00'\r\n              }\r\n\r\n              return {\r\n                value: item.riskNum,\r\n                suppId: item.suppId, // 保存供应商ID用于点击跳转\r\n                itemStyle: {\r\n                  color: barColor,\r\n                  borderRadius: [0, 5, 5, 0]\r\n                },\r\n                emphasis: {\r\n                  itemStyle: {\r\n                    color: barColor,\r\n                    borderRadius: [0, 5, 5, 0],\r\n                    shadowBlur: 10,\r\n                    shadowColor: 'rgba(255, 255, 255, 0.5)',\r\n                    borderWidth: 2,\r\n                    borderColor: '#fff'\r\n                  }\r\n                }\r\n              }\r\n            }),\r\n            label: {\r\n              show: true,\r\n              position: 'right',\r\n              formatter: '{c}',\r\n              color: '#fff'\r\n            }\r\n          }\r\n        ]\r\n      }\r\n      \r\n      // 添加点击事件监听器\r\n      myChart.off('click') // 先移除之前的点击事件\r\n      myChart.on('click', function(params) {\r\n        if (params.componentType === 'series' && params.seriesType === 'bar') {\r\n          const suppId = params.data.suppId\r\n          if (suppId) {\r\n            // 获取当前页面URL并替换路径\r\n            const currentUrl = window.location.href\r\n            const baseUrl = currentUrl.replace('purchaseDashboard', 'supplierCertificate')\r\n            \r\n            // 时间维度映射：采购看板的currentDimensionType → 供应商证书页面的dimensionType\r\n            const dimensionTypeMapping = {\r\n              1: 3,  // 近三个月：看板值1 → 证书页面值3\r\n              2: 6,  // 近六个月：看板值2 → 证书页面值6  \r\n              3: 12  // 近一年：看板值3 → 证书页面值12\r\n            }\r\n            const mappedDimensionType = dimensionTypeMapping[this.currentDimensionType] || 12\r\n            \r\n            // 构建完整的跳转URL，包含supplierId和mappedDimensionType参数\r\n            const url = `${baseUrl}?supplierId=${suppId}&dimensionType=${mappedDimensionType}`\r\n            console.log('跳转到供应商详情页面:', url)\r\n            console.log('跳转参数:', { \r\n              supplierId: suppId, \r\n              原始dimensionType: this.currentDimensionType,\r\n              映射后dimensionType: mappedDimensionType \r\n            })\r\n            \r\n            // 在新窗口中打开页面\r\n            window.open(url, '_blank')\r\n          } else {\r\n            console.warn('供应商ID不存在，无法跳转')\r\n          }\r\n        }\r\n      }.bind(this))\r\n      \r\n      myChart.setOption(option, true)\r\n      \r\n      // 设置鼠标样式，表示可点击\r\n      myChart.getZr().on('mousemove', function(params) {\r\n        const pointInPixel = [params.offsetX, params.offsetY]\r\n        if (myChart.containPixel('grid', pointInPixel)) {\r\n          myChart.getZr().setCursorStyle('pointer')\r\n        } else {\r\n          myChart.getZr().setCursorStyle('default')\r\n        }\r\n      })\r\n    },\r\n\r\n    // 采购价格趋势图（旧版本）\r\n    initPriceTrendChart() {\r\n      const myChart = this.reinitChart('priceTrendChart')\r\n      if (!myChart) return\r\n\r\n      // 使用新的数据结构\r\n      const priceAndStoreData = this.priceAndStoreData\r\n      if (!priceAndStoreData) {\r\n        document.getElementById('priceTrendChart').innerHTML = '<div class=\"chart-placeholder\">暂无价格趋势数据</div>'\r\n        return\r\n      }\r\n\r\n      const formatDate = (dateStr) => {\r\n        const year = dateStr.substring(0, 4)\r\n        const month = dateStr.substring(4, 6)\r\n        const day = dateStr.substring(6, 8)\r\n        return `${year}年${month}月${day}日`\r\n      }\r\n\r\n      // 收集所有日期\r\n      let allDates = new Set()\r\n      \r\n      // 从价格数据中收集日期\r\n      if (priceAndStoreData.procurementPriceVoList) {\r\n        priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {\r\n          if (priceGroup.priceList) {\r\n            priceGroup.priceList.forEach(item => {\r\n              allDates.add(item.recordDate)\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      // 从采购量数据中收集日期\r\n      if (priceAndStoreData.procurementPurchaseAmountVoList) {\r\n        priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {\r\n          if (amountGroup.amountList) {\r\n            amountGroup.amountList.forEach(item => {\r\n              allDates.add(item.recordDate)\r\n            })\r\n          }\r\n        })\r\n      }\r\n\r\n      // 转换为排序的数组\r\n      allDates = Array.from(allDates).sort()\r\n      const xAxisData = allDates.map(formatDate)\r\n\r\n      if (allDates.length === 0) {\r\n        document.getElementById('priceTrendChart').innerHTML = '<div class=\"chart-placeholder\">暂无价格趋势数据</div>'\r\n        return\r\n      }\r\n\r\n      // 构建系列数据\r\n      const series = []\r\n      const legendData = []\r\n      const colors = ['#8fe9ff', '#ff9f7f', '#5fd8b6', '#ffb980', '#e879ed', '#ffdb5c', '#83bff6', '#ea7ccc']\r\n      let colorIndex = 0\r\n\r\n      // 构建价格系列\r\n      if (priceAndStoreData.procurementPriceVoList) {\r\n        priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {\r\n          const priceData = allDates.map(date => {\r\n            const found = priceGroup.priceList.find(item => item.recordDate === date)\r\n            return found ? parseFloat(found.price) : null\r\n          })\r\n\r\n          series.push({\r\n            name: priceGroup.priceName,\r\n            type: 'line',\r\n            yAxisIndex: 0,\r\n            data: priceData,\r\n            smooth: true,\r\n            lineStyle: {\r\n              width: 3,\r\n              color: colors[colorIndex % colors.length]\r\n            },\r\n            itemStyle: {\r\n              color: colors[colorIndex % colors.length]\r\n            },\r\n            symbol: 'circle',\r\n            symbolSize: 6,\r\n            connectNulls: true\r\n          })\r\n          \r\n          legendData.push(priceGroup.priceName)\r\n          colorIndex++\r\n        })\r\n      }\r\n\r\n      // 构建采购量系列\r\n      if (priceAndStoreData.procurementPurchaseAmountVoList) {\r\n        priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {\r\n          const amountData = allDates.map(date => {\r\n            const found = amountGroup.amountList.find(item => item.recordDate === date)\r\n            return found ? parseFloat(found.amount) : null\r\n          })\r\n\r\n          series.push({\r\n            name: amountGroup.amountName,\r\n            type: 'line',\r\n            yAxisIndex: 1,\r\n            data: amountData,\r\n            smooth: true,\r\n            lineStyle: {\r\n              width: 3,\r\n              color: colors[colorIndex % colors.length]\r\n            },\r\n            itemStyle: {\r\n              color: colors[colorIndex % colors.length]\r\n            },\r\n            symbol: 'circle',\r\n            symbolSize: 6,\r\n            connectNulls: true\r\n          })\r\n          \r\n          legendData.push(amountGroup.amountName)\r\n          colorIndex++\r\n        })\r\n      }\r\n\r\n             // 计算Y轴范围\r\n       let priceMin, priceMax, amountMin, amountMax\r\n       \r\n       // 计算价格轴范围（左轴）\r\n       const priceValues = series.filter(s => s.yAxisIndex === 0)\r\n         .flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n       if (priceValues.length > 0) {\r\n         priceMin = Math.min(...priceValues)\r\n         priceMax = Math.max(...priceValues)\r\n       }\r\n       \r\n       // 计算采购量轴范围（右轴）\r\n       const amountValues = series.filter(s => s.yAxisIndex === 1)\r\n         .flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n       if (amountValues.length > 0) {\r\n         amountMin = Math.min(...amountValues)\r\n         amountMax = Math.max(...amountValues)\r\n       }\r\n\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          formatter: function(params) {\r\n            let str = params[0].axisValueLabel + '<br/>'\r\n            params.forEach(item => {\r\n              if (item.value !== null && item.value !== undefined) {\r\n                if (item.seriesName.includes('价格') || item.seriesName.includes('价')) {\r\n                  str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`\r\n                } else {\r\n                  str += `${item.marker}${item.seriesName}: ${parseFloat(item.value).toFixed(2)} 万吨<br/>`\r\n                }\r\n              } else {\r\n                str += `${item.marker}${item.seriesName}: -<br/>`\r\n              }\r\n            })\r\n            return str\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '12%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: function(index, value) {\r\n              // 计算所有月份，确保均匀分布显示标签\r\n              if (index >= allDates.length || !allDates.length) return false\r\n              \r\n              // 获取所有不重复的月份\r\n              const uniqueMonths = new Set()\r\n              allDates.forEach(dateStr => {\r\n                const year = dateStr.substring(0, 4)\r\n                const month = dateStr.substring(4, 6)\r\n                uniqueMonths.add(`${year}${month}`)\r\n              })\r\n              \r\n              const monthsCount = uniqueMonths.size\r\n              if (monthsCount <= 1) return true\r\n              \r\n              // 计算间隔，确保标签均匀分布\r\n              const totalDataPoints = allDates.length\r\n              const idealInterval = Math.floor(totalDataPoints / Math.min(monthsCount, 8)) // 最多显示8个标签\r\n              \r\n              return index % Math.max(idealInterval, 1) === 0\r\n            },\r\n            formatter: function(value, index) {\r\n              // 格式化为 2026.6 的形式（去掉.1）\r\n              if (index >= allDates.length) return ''\r\n              const originalDateStr = allDates[index]\r\n              if (!originalDateStr) return ''\r\n              \r\n              const year = originalDateStr.substring(0, 4)\r\n              const month = parseInt(originalDateStr.substring(4, 6))\r\n              return `${year}.${month}`\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          }\r\n        },\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '价格（元/吨）',\r\n            position: 'left',\r\n            min: priceMin,\r\n            max: priceMax,\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#8fe9ff'\r\n              }\r\n            },\r\n            axisLabel: {\r\n              color: '#eee'\r\n            },\r\n            splitLine: {\r\n              lineStyle: {\r\n                color: 'rgba(255,255,255,0.1)'\r\n              }\r\n            }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '采购量 (万吨)',\r\n            position: 'right',\r\n            min: amountMin,\r\n            max: amountMax,\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#ff9f7f'\r\n              }\r\n            },\r\n            axisLabel: {\r\n              color: '#eee',\r\n              formatter: function(value) {\r\n                return parseFloat(value).toFixed(0)\r\n              }\r\n            },\r\n            splitLine: {\r\n              show: false\r\n            }\r\n          }\r\n        ],\r\n        series: series\r\n      }\r\n\r\n      myChart.setOption(option, true)\r\n    },\r\n\r\n    // 新的采购价格趋势图\r\n    initNewPriceTrendChart() {\r\n      const myChart = this.reinitChart('priceTrendChart')\r\n      if (!myChart) return\r\n\r\n      // 使用新的数据结构\r\n      const newPriceAndStoreDataList = this.newPriceAndStoreData\r\n      if (!newPriceAndStoreDataList || !Array.isArray(newPriceAndStoreDataList) || newPriceAndStoreDataList.length === 0) {\r\n        document.getElementById('priceTrendChart').innerHTML = '<div class=\"chart-placeholder\">暂无价格趋势数据</div>'\r\n        return\r\n      }\r\n\r\n      const formatDate = (dateStr) => {\r\n        const year = dateStr.substring(0, 4)\r\n        const month = dateStr.substring(4, 6)\r\n        const day = dateStr.substring(6, 8)\r\n        return `${year}年${month}月${day}日`\r\n      }\r\n\r\n      // 收集所有日期\r\n      let allDates = new Set()\r\n      \r\n      // 从所有物料的数据中收集日期\r\n      newPriceAndStoreDataList.forEach(materialData => {\r\n        // 从价格数据中收集日期\r\n        if (materialData.procurementPriceVoList) {\r\n          materialData.procurementPriceVoList.forEach(priceGroup => {\r\n            if (priceGroup.priceList) {\r\n              priceGroup.priceList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n\r\n        // 从采购量数据中收集日期\r\n        if (materialData.procurementPurchaseAmountVoList) {\r\n          materialData.procurementPurchaseAmountVoList.forEach(amountGroup => {\r\n            if (amountGroup.amountList) {\r\n              amountGroup.amountList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      // 转换为排序的数组\r\n      allDates = Array.from(allDates).sort()\r\n      const xAxisData = allDates.map(formatDate)\r\n\r\n      if (allDates.length === 0) {\r\n        document.getElementById('priceTrendChart').innerHTML = '<div class=\"chart-placeholder\">暂无价格趋势数据</div>'\r\n        return\r\n      }\r\n\r\n      // 构建系列数据\r\n      const series = []\r\n      const legendData = []\r\n      const colors = ['#8fe9ff', '#ff9f7f', '#5fd8b6', '#ffb980', '#e879ed', '#ffdb5c', '#83bff6', '#ea7ccc', '#a18cd1', '#fbc2eb']\r\n      let colorIndex = 0\r\n\r\n      // 遍历每个物料的数据\r\n      newPriceAndStoreDataList.forEach(materialData => {\r\n        const materialName = materialData.itemName\r\n\r\n        // 构建价格系列\r\n        if (materialData.procurementPriceVoList) {\r\n          materialData.procurementPriceVoList.forEach(priceGroup => {\r\n            const priceData = allDates.map(date => {\r\n              const found = priceGroup.priceList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.price) : null\r\n            })\r\n\r\n            series.push({\r\n              name: `${materialName}-${priceGroup.priceName}`,\r\n              type: 'line',\r\n              yAxisIndex: 0,\r\n              data: priceData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 3,\r\n                color: colors[colorIndex % colors.length]\r\n              },\r\n              itemStyle: {\r\n                color: colors[colorIndex % colors.length]\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 6,\r\n              connectNulls: true\r\n            })\r\n            \r\n            legendData.push(`${materialName}-${priceGroup.priceName}`)\r\n            colorIndex++\r\n          })\r\n        }\r\n\r\n        // 构建采购量系列\r\n        if (materialData.procurementPurchaseAmountVoList) {\r\n          materialData.procurementPurchaseAmountVoList.forEach(amountGroup => {\r\n            const amountData = allDates.map(date => {\r\n              const found = amountGroup.amountList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.amount) : null\r\n            })\r\n\r\n            series.push({\r\n              name: `${materialName}-${amountGroup.amountName}`,\r\n              type: 'line',\r\n              yAxisIndex: 1,\r\n              data: amountData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 3,\r\n                color: colors[colorIndex % colors.length]\r\n              },\r\n              itemStyle: {\r\n                color: colors[colorIndex % colors.length]\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 6,\r\n              connectNulls: true\r\n            })\r\n            \r\n            legendData.push(`${materialName}-${amountGroup.amountName}`)\r\n            colorIndex++\r\n          })\r\n        }\r\n      })\r\n\r\n      // 计算Y轴范围\r\n      let priceMin, priceMax, amountMin, amountMax\r\n      \r\n      // 计算价格轴范围（左轴）\r\n      const priceValues = series.filter(s => s.yAxisIndex === 0)\r\n        .flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n      if (priceValues.length > 0) {\r\n        priceMin = Math.min(...priceValues)\r\n        priceMax = Math.max(...priceValues)\r\n      }\r\n      \r\n      // 计算采购量轴范围（右轴）\r\n      const amountValues = series.filter(s => s.yAxisIndex === 1)\r\n        .flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n      if (amountValues.length > 0) {\r\n        amountMin = Math.min(...amountValues)\r\n        amountMax = Math.max(...amountValues)\r\n      }\r\n\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          formatter: function(params) {\r\n            let str = params[0].axisValueLabel + '<br/>'\r\n            params.forEach(item => {\r\n              if (item.value !== null && item.value !== undefined) {\r\n                if (item.seriesName.includes('价格') || item.seriesName.includes('价')) {\r\n                  str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`\r\n                } else {\r\n                  str += `${item.marker}${item.seriesName}: ${parseFloat(item.value).toFixed(2)} 吨<br/>`\r\n                }\r\n              } else {\r\n                str += `${item.marker}${item.seriesName}: -<br/>`\r\n              }\r\n            })\r\n            return str\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%',\r\n          type: 'scroll',\r\n          pageButtonItemGap: 5,\r\n          pageButtonGap: 30,\r\n          pageButtonPosition: 'end',\r\n          pageFormatter: '{current}/{total}',\r\n          pageIconColor: '#2ec7c9',\r\n          pageIconInactiveColor: '#aaa',\r\n          pageIconSize: 15,\r\n          pageTextStyle: {\r\n            color: '#fff'\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '12%',\r\n          top: '25%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: function(index, value) {\r\n              // 计算所有月份，确保均匀分布显示标签\r\n              if (index >= allDates.length || !allDates.length) return false\r\n              \r\n              // 获取所有不重复的月份\r\n              const uniqueMonths = new Set()\r\n              allDates.forEach(dateStr => {\r\n                const year = dateStr.substring(0, 4)\r\n                const month = dateStr.substring(4, 6)\r\n                uniqueMonths.add(`${year}${month}`)\r\n              })\r\n              \r\n              const monthsCount = uniqueMonths.size\r\n              if (monthsCount <= 1) return true\r\n              \r\n              // 计算间隔，确保标签均匀分布\r\n              const totalDataPoints = allDates.length\r\n              const idealInterval = Math.floor(totalDataPoints / Math.min(monthsCount, 8)) // 最多显示8个标签\r\n              \r\n              return index % Math.max(idealInterval, 1) === 0\r\n            },\r\n            formatter: function(value, index) {\r\n              // 格式化为 2026.6 的形式（去掉.1）\r\n              if (index >= allDates.length) return ''\r\n              const originalDateStr = allDates[index]\r\n              if (!originalDateStr) return ''\r\n              \r\n              const year = originalDateStr.substring(0, 4)\r\n              const month = parseInt(originalDateStr.substring(4, 6))\r\n              return `${year}.${month}`\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          }\r\n        },\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '价格（元/吨）',\r\n            position: 'left',\r\n            min: priceMin,\r\n            max: priceMax,\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#8fe9ff'\r\n              }\r\n            },\r\n            axisLabel: {\r\n              color: '#eee'\r\n            },\r\n            splitLine: {\r\n              lineStyle: {\r\n                color: 'rgba(255,255,255,0.1)'\r\n              }\r\n            }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '采购量 (吨)',\r\n            position: 'right',\r\n            min: amountMin,\r\n            max: amountMax,\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#ff9f7f'\r\n              }\r\n            },\r\n            axisLabel: {\r\n              color: '#eee',\r\n              formatter: function(value) {\r\n                return parseFloat(value).toFixed(0)\r\n              }\r\n            },\r\n            splitLine: {\r\n              show: false\r\n            }\r\n          }\r\n        ],\r\n        series: series\r\n      }\r\n\r\n      myChart.setOption(option, true)\r\n    }\r\n  }\r\n} "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAkC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEnB;EACbC,OAAO,EAAE;IACP;IACAC,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClBC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,yBAAyB,CAAC,CAAC;MAEnG,IAAMC,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC;MAC9D,IAAI,CAACF,QAAQ,EAAE;QACbL,OAAO,CAACQ,KAAK,CAAC,4CAA4C,CAAC;QAC3D;MACF;MAEAH,QAAQ,CAACI,SAAS,GAAG,EAAE;MAEvB,IAAMC,eAAe,GAAG,IAAI,CAACN,yBAAyB;MACtD,IAAI,CAACM,eAAe,IAAIA,eAAe,CAACC,MAAM,KAAK,CAAC,EAAE;QACpDN,QAAQ,CAACI,SAAS,GAAG,gDAAgD;QACrET,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;QAC7E;MACF;;MAEA;MACA,IAAMW,sBAAsB,GAAGF,eAAe,CAACG,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAK,CAACA,CAAC,CAACC,KAAK,IAAI,CAAC,KAAKF,CAAC,CAACE,KAAK,IAAI,CAAC,CAAC;MAAA,EAAC;MAE9F,IAAMC,SAAS,GAAGX,QAAQ,CAACY,aAAa,CAAC,KAAK,CAAC;MAC/CD,SAAS,CAACE,KAAK,CAACC,KAAK,GAAG,MAAM;MAC9BH,SAAS,CAACE,KAAK,CAACE,MAAM,GAAG,MAAM;MAC/BJ,SAAS,CAACE,KAAK,CAACG,QAAQ,GAAG,UAAU;MACrCL,SAAS,CAACE,KAAK,CAACI,QAAQ,GAAG,QAAQ;MAEnC,IAAMC,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CACtD;MAED,IAAMC,WAAW,GAAG,EAAE;MACtB,IAAMC,WAAW,GAAG,EAAE;MAEtBd,sBAAsB,CAACe,OAAO,CAAC,UAACC,IAAI,EAAEC,KAAK,EAAK;QAC9C,IAAIC,QAAQ,GAAGL,WAAW,GAAII,KAAK,GAAG,CAAE;QACxC,IAAIC,QAAQ,GAAGJ,WAAW,EAAE;UAC1BI,QAAQ,GAAGJ,WAAW;QACxB;QAEA,IAAMK,GAAG,GAAGzB,QAAQ,CAACY,aAAa,CAAC,KAAK,CAAC;QACzCa,GAAG,CAACC,WAAW,GAAGJ,IAAI,CAACK,QAAQ;QAC/BF,GAAG,CAACZ,KAAK,CAACG,QAAQ,GAAG,UAAU;QAC/BS,GAAG,CAACZ,KAAK,CAACW,QAAQ,MAAAI,MAAA,CAAMJ,QAAQ,OAAI;QACpCC,GAAG,CAACZ,KAAK,CAACgB,UAAU,GAAG,MAAM;QAC7BJ,GAAG,CAACZ,KAAK,CAACiB,KAAK,GAAGZ,MAAM,CAACK,KAAK,GAAGL,MAAM,CAACb,MAAM,CAAC;QAC/CoB,GAAG,CAACZ,KAAK,CAACkB,SAAS,aAAAH,MAAA,CAAaI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,SAAM;QAC7DR,GAAG,CAACZ,KAAK,CAACqB,IAAI,MAAAN,MAAA,CAAM,CAAC,GAAGI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAG;QAC7CR,GAAG,CAACZ,KAAK,CAACsB,GAAG,MAAAP,MAAA,CAAM,CAAC,GAAGI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAG;QAC5CR,GAAG,CAACZ,KAAK,CAACuB,UAAU,GAAG,QAAQ;QAC/BX,GAAG,CAACZ,KAAK,CAACwB,UAAU,GAAG,6BAA6B;QACpDZ,GAAG,CAACZ,KAAK,CAACyB,UAAU,GAAG,eAAe;QACtCb,GAAG,CAACZ,KAAK,CAAC0B,MAAM,GAAG,SAAS;QAC5Bd,GAAG,CAACZ,KAAK,CAAC2B,MAAM,GAAGlC,sBAAsB,CAACD,MAAM,GAAGkB,KAAK;QAExDE,GAAG,CAACgB,gBAAgB,CAAC,WAAW,EAAE,YAAW;UAC3C,IAAI,CAAC5B,KAAK,CAACkB,SAAS,4BAA4B;UAChD,IAAI,CAAClB,KAAK,CAAC2B,MAAM,GAAG,CAAClC,sBAAsB,CAACD,MAAM,GAAG,CAAC,EAAEqC,QAAQ,CAAC,CAAC;UAClE,IAAI,CAAC7B,KAAK,CAACwB,UAAU,GAAG,6BAA6B;UAErD,IAAMM,OAAO,GAAG3C,QAAQ,CAACY,aAAa,CAAC,KAAK,CAAC;UAC7C;UACA+B,OAAO,CAACjB,WAAW,gCAAAE,MAAA,CAAY,CAACN,IAAI,CAACZ,KAAK,IAAI,CAAC,EAAEkC,OAAO,CAAC,CAAC,CAAC,kBAAK;UAChED,OAAO,CAAC9B,KAAK,CAACG,QAAQ,GAAG,UAAU;UACnC2B,OAAO,CAAC9B,KAAK,CAACgC,eAAe,GAAG,iBAAiB;UACjDF,OAAO,CAAC9B,KAAK,CAACiB,KAAK,GAAG,MAAM;UAC5Ba,OAAO,CAAC9B,KAAK,CAACiC,OAAO,GAAG,SAAS;UACjCH,OAAO,CAAC9B,KAAK,CAACkC,YAAY,GAAG,KAAK;UAClCJ,OAAO,CAAC9B,KAAK,CAACW,QAAQ,GAAG,MAAM;UAC/BmB,OAAO,CAAC9B,KAAK,CAACsB,GAAG,GAAG,kBAAkB;UACtCQ,OAAO,CAAC9B,KAAK,CAACqB,IAAI,GAAG,KAAK;UAC1BS,OAAO,CAAC9B,KAAK,CAACkB,SAAS,GAAG,kBAAkB;UAC5CY,OAAO,CAAC9B,KAAK,CAACuB,UAAU,GAAG,QAAQ;UACnCO,OAAO,CAAC9B,KAAK,CAAC2B,MAAM,GAAG,CAAClC,sBAAsB,CAACD,MAAM,GAAG,CAAC,GAAG,CAAC,EAAEqC,QAAQ,CAAC,CAAC;UACzEC,OAAO,CAACK,SAAS,GAAG,kBAAkB;UACtC,IAAI,CAACC,WAAW,CAACN,OAAO,CAAC;QAC3B,CAAC,CAAC;QAEFlB,GAAG,CAACgB,gBAAgB,CAAC,UAAU,EAAE,YAAW;UAC1C,IAAI,CAAC5B,KAAK,CAACkB,SAAS,aAAAH,MAAA,CAAaI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,kBAAe;UACvE,IAAI,CAACpB,KAAK,CAAC2B,MAAM,GAAG,CAAClC,sBAAsB,CAACD,MAAM,GAAGkB,KAAK,EAAEmB,QAAQ,CAAC,CAAC;UACtE,IAAI,CAAC7B,KAAK,CAACwB,UAAU,GAAG,6BAA6B;UAErD,IAAMM,OAAO,GAAG,IAAI,CAACO,aAAa,CAAC,mBAAmB,CAAC;UACvD,IAAIP,OAAO,EAAE;YACX,IAAI,CAACQ,WAAW,CAACR,OAAO,CAAC;UAC3B;QACF,CAAC,CAAC;QAEFhC,SAAS,CAACsC,WAAW,CAACxB,GAAG,CAAC;MAC5B,CAAC,CAAC;MAEF1B,QAAQ,CAACkD,WAAW,CAACtC,SAAS,CAAC;MAC/BjB,OAAO,CAACC,GAAG,sCAAAiC,MAAA,CAAsCtB,sBAAsB,CAACD,MAAM,YAAS,CAAC;IAC1F,CAAC;IAED;IACM+C,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAA/D,OAAA,mBAAAgE,aAAA,CAAAhE,OAAA,IAAAiE,CAAA,UAAAC,QAAA;QAAA,IAAAC,OAAA,EAAAC,QAAA,EAAAC,YAAA,EAAAC,EAAA;QAAA,WAAAN,aAAA,CAAAhE,OAAA,IAAAuE,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAC5BtE,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;cACtC+D,OAAO,GAAGL,KAAI,CAACY,WAAW,CAAC,mBAAmB,CAAC;cAAA,IAChDP,OAAO;gBAAAK,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,OAAAD,QAAA,CAAAvD,CAAA;YAAA;cAEZ;cACAkD,OAAO,CAACQ,WAAW,CAAC,CAAC;cAAAH,QAAA,CAAAI,CAAA;cAAAJ,QAAA,CAAAC,CAAA;cAAA,OAEIX,KAAI,CAACe,eAAe,CAAC;gBAC1CC,aAAa,EAAEhB,KAAI,CAACiB,oBAAoB;gBACxCC,SAAS,EAAElB,KAAI,CAACmB;cAClB,CAAC,CAAC;YAAA;cAHIb,QAAQ,GAAAI,QAAA,CAAAU,CAAA;cAKVb,YAAY,GAAG,EAAE;cACrB,IAAID,QAAQ,IAAIA,QAAQ,CAACe,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACjB,QAAQ,CAACe,IAAI,CAAC,EAAE;gBAC7Dd,YAAY,GAAGD,QAAQ,CAACe,IAAI;cAC9B,CAAC,MAAM;gBACLhF,OAAO,CAACQ,KAAK,CAAC,2BAA2B,EAAEyD,QAAQ,CAAC;gBACpD;gBACAC,YAAY,GAAGP,KAAI,CAACwB,aAAa,CAACC,+BAA+B,IAAI,EAAE;cACzE;cAEAzB,KAAI,CAAC0B,wBAAwB,OAAAC,mBAAA,CAAAzF,OAAA,EAAOqE,YAAY,CAAC;cACjDP,KAAI,CAAC4B,6BAA6B,CAACvB,OAAO,EAAEE,YAAY,CAAC;cAAAG,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAI,CAAA;cAAAN,EAAA,GAAAE,QAAA,CAAAU,CAAA;cAEzD/E,OAAO,CAACQ,KAAK,CAAC,gBAAgB,EAAA2D,EAAO,CAAC;cACtC;cACAR,KAAI,CAAC0B,wBAAwB,GAAG1B,KAAI,CAACwB,aAAa,CAACC,+BAA+B,OAAAE,mBAAA,CAAAzF,OAAA,EAC1E8D,KAAI,CAACwB,aAAa,CAACC,+BAA+B,IACtD,EAAE;cACNzB,KAAI,CAAC4B,6BAA6B,CAACvB,OAAO,EAAEL,KAAI,CAAC0B,wBAAwB,CAAC;YAAA;cAAAhB,QAAA,CAAAI,CAAA;cAE1ET,OAAO,CAACwB,WAAW,CAAC,CAAC;cAAA,OAAAnB,QAAA,CAAAoB,CAAA;YAAA;cAAA,OAAApB,QAAA,CAAAvD,CAAA;UAAA;QAAA,GAAAiD,OAAA;MAAA;IAEzB,CAAC;IAEDwB,6BAA6B,WAA7BA,6BAA6BA,CAACG,aAAa,EAAEC,qBAAqB,EAAE;MAClE,IAAI,CAACD,aAAa,EAAE;MAEpB,IAAIA,aAAa,CAACE,UAAU,EAAE;QAC5BC,aAAa,CAACH,aAAa,CAACE,UAAU,CAAC;QACvCF,aAAa,CAACE,UAAU,GAAG,IAAI;MACjC;MAEA,IAAME,aAAa,GAAGH,qBAAqB,CAAC9E,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAKA,CAAC,CAACgF,IAAI,GAAGjF,CAAC,CAACiF,IAAI;MAAA,EAAC;MAE3E,IAAIC,YAAY,GAAG,CAAC;MACpB,IAAMC,SAAS,GAAG,EAAE;MAEpB,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;QACxB,IAAIJ,aAAa,CAACnF,MAAM,KAAK,CAAC,EAAE;UAC9B+E,aAAa,CAACS,SAAS,CAAC;YACtBC,KAAK,EAAE;cACLC,IAAI,EAAE,MAAM;cACZ7D,IAAI,EAAE,QAAQ;cACdC,GAAG,EAAE,QAAQ;cACb6D,SAAS,EAAE;gBAAElE,KAAK,EAAE;cAAwB;YAC9C,CAAC;YACDmE,KAAK,EAAE;cAAEC,IAAI,EAAE;YAAM,CAAC;YACtBC,KAAK,EAAE;cAAED,IAAI,EAAE;YAAM,CAAC;YACtBE,MAAM,EAAE;UACV,CAAC,EAAE,IAAI,CAAC;UACR;QACF;QAEA,IAAMC,QAAQ,GAAGrE,IAAI,CAACsE,GAAG,CAACZ,YAAY,GAAGC,SAAS,EAAEH,aAAa,CAACnF,MAAM,CAAC;QACzE,IAAIkG,WAAW,GAAGf,aAAa,CAACgB,KAAK,CAACd,YAAY,EAAEW,QAAQ,CAAC;QAC7DE,WAAW,GAAGA,WAAW,CAACE,OAAO,CAAC,CAAC;QAEnC,IAAIC,OAAO,GAAG,CAAC;QACf,IAAIH,WAAW,CAAClG,MAAM,GAAG,CAAC,EAAE;UAC1BqG,OAAO,GAAG1E,IAAI,CAAC2E,GAAG,CAAAC,KAAA,CAAR5E,IAAI,MAAAgD,mBAAA,CAAAzF,OAAA,EAAQgH,WAAW,CAACM,GAAG,CAAC,UAAAvF,IAAI;YAAA,OAAIwF,UAAU,CAACxF,IAAI,CAACmE,IAAI,CAAC,IAAI,CAAC;UAAA,EAAC,EAAC;QAC5E;QAEA,IAAIsB,IAAI,GAAG,IAAI;QACf,IAAIC,QAAQ,GAAG,CAAC;QAChB,IAAIC,cAAc,GAAG,CAAC;QAEtB,IAAIP,OAAO,GAAG,MAAM,EAAE;UACpBK,IAAI,GAAG,IAAI;UACXC,QAAQ,GAAG,KAAK;UAChBC,cAAc,GAAG,CAAC;QACpB;QAEA,IAAM/F,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;QAE7H,IAAMgG,MAAM,GAAG;UACbvE,OAAO,EAAE;YACPwE,OAAO,EAAE,MAAM;YACfC,WAAW,EAAE;cACXC,IAAI,EAAE;YACR,CAAC;YACDC,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;cAC1B,IAAMjG,IAAI,GAAGiG,MAAM,CAAC,CAAC,CAAC;cACtB,IAAMC,aAAa,GAAGV,UAAU,CAACxF,IAAI,CAACmG,KAAK,CAAC;cAC5C,UAAA7F,MAAA,CAAUN,IAAI,CAACoG,IAAI,QAAA9F,MAAA,CAAK4F,aAAa,CAACG,cAAc,CAACC,SAAS,EAAE;gBAAEC,qBAAqB,EAAEZ,cAAc;gBAAEa,qBAAqB,EAAEb;cAAe,CAAC,CAAC,EAAArF,MAAA,CAAGmF,IAAI;YAC1J;UACF,CAAC;UACDgB,IAAI,EAAE;YACJ7F,IAAI,EAAE,IAAI;YACV8F,KAAK,EAAE,KAAK;YACZC,MAAM,EAAE,IAAI;YACZ9F,GAAG,EAAE,IAAI;YACT+F,YAAY,EAAE;UAChB,CAAC;UACDjC,KAAK,EAAE;YACLC,IAAI,EAAE,IAAI;YACVmB,IAAI,EAAE,OAAO;YACbc,SAAS,EAAE;cACTrG,KAAK,EAAE,MAAM;cACbwF,SAAS,EAAE,SAAXA,SAASA,CAAWG,KAAK,EAAE;gBACzB,OAAOX,UAAU,CAACW,KAAK,CAAC,CAAC7E,OAAO,CAAC,CAAC,CAAC,GAAGmE,IAAI;cAC5C;YACF,CAAC;YACDqB,QAAQ,EAAE;cACRC,SAAS,EAAE;gBACTvG,KAAK,EAAE;cACT;YACF,CAAC;YACDwG,SAAS,EAAE;cACTpC,IAAI,EAAE;YACR;UACF,CAAC;UACDC,KAAK,EAAE;YACLD,IAAI,EAAE,IAAI;YACVmB,IAAI,EAAE,UAAU;YAChB3C,IAAI,EAAE6B,WAAW,CAACM,GAAG,CAAC,UAAAvF,IAAI,EAAI;cAC5B,IAAMiH,SAAS,GAAG,EAAE;cACpB,IAAIb,IAAI,GAAGpG,IAAI,CAACkH,QAAQ;cACxB,IAAId,IAAI,CAACrH,MAAM,GAAGkI,SAAS,EAAE;gBAC3Bb,IAAI,GAAGA,IAAI,CAACe,SAAS,CAAC,CAAC,EAAEF,SAAS,GAAG,CAAC,CAAC,GAAG,KAAK;cACjD;cACA,OAAOb,IAAI;YACb,CAAC,CAAC;YACFS,SAAS,EAAE;cACTrG,KAAK,EAAE,MAAM;cACb4G,QAAQ,EAAE;YACZ,CAAC;YACDN,QAAQ,EAAE;cACRC,SAAS,EAAE;gBACTvG,KAAK,EAAE;cACT;YACF;UACF,CAAC;UACDsE,MAAM,EAAE,CACN;YACEsB,IAAI,EAAE,MAAM;YACZL,IAAI,EAAE,KAAK;YACX3C,IAAI,EAAE6B,WAAW,CAACM,GAAG,CAAC,UAACvF,IAAI,EAAEC,KAAK,EAAK;cACrC,OAAO;gBACLkG,KAAK,EAAE,CAACX,UAAU,CAACxF,IAAI,CAACmE,IAAI,CAAC,IAAI,CAAC,IAAIuB,QAAQ;gBAC9C2B,SAAS,EAAE;kBACT7G,KAAK,EAAEZ,MAAM,CAACK,KAAK,GAAGL,MAAM,CAACb,MAAM,CAAC;kBACpC0C,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC3B;cACF,CAAC;YACH,CAAC,CAAC;YACF6F,KAAK,EAAE;cACL1C,IAAI,EAAE,IAAI;cACVlF,QAAQ,EAAE,OAAO;cACjBsG,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;gBAC1B,OAAOT,UAAU,CAACS,MAAM,CAACE,KAAK,CAAC,CAACE,cAAc,CAACC,SAAS,EAAE;kBAAEC,qBAAqB,EAAEZ,cAAc;kBAAEa,qBAAqB,EAAEb;gBAAe,CAAC,CAAC,GAAG,GAAG,GAAGF,IAAI;cAC1J,CAAC;cACDjF,KAAK,EAAE;YACT;UACF,CAAC;QAEL,CAAC;QACDsD,aAAa,CAACS,SAAS,CAACqB,MAAM,EAAE,IAAI,CAAC;MACvC,CAAC;MAEDtB,WAAW,CAAC,CAAC;MAEb,IAAIJ,aAAa,CAACnF,MAAM,GAAGsF,SAAS,EAAE;QACpCP,aAAa,CAACE,UAAU,GAAGuD,WAAW,CAAC,YAAM;UAC3CnD,YAAY,IAAIC,SAAS;UACzB,IAAID,YAAY,IAAIF,aAAa,CAACnF,MAAM,EAAE;YACxCqF,YAAY,GAAG,CAAC;UAClB;UACAE,WAAW,CAAC,CAAC;QACf,CAAC,EAAE,KAAK,CAAC;MACX;IACF,CAAC;IAED;IACAkD,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;MACtBpJ,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACkJ,gBAAgB,CAAC,CAAC;MAC9F,IAAMrF,OAAO,GAAG,IAAI,CAACO,WAAW,CAAC,mBAAmB,CAAC;MACrD,IAAI,CAACP,OAAO,EAAE;MAEd,IAAMsF,QAAQ,GAAG,IAAI,CAACD,gBAAgB,IAAI,EAAE;MAE5C,IAAME,kBAAkB,GAAGD,QAAQ,CAChCzI,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;QAAA,OAAKA,CAAC,CAACyI,OAAO,GAAG1I,CAAC,CAAC0I,OAAO;MAAA,EAAC,CACrC1C,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACZC,OAAO,CAAC,CAAC;MAEZ,IAAIwC,kBAAkB,CAAC5I,MAAM,KAAK,CAAC,EAAE;QACnCL,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC,CAACE,SAAS,GAAG,+CAA+C;QACxG;MACF;MAEA,IAAM+G,MAAM,GAAG;QACbvE,OAAO,EAAE;UACPwE,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXC,IAAI,EAAE;UACR,CAAC;UACDC,SAAS,EAAE,SAAXA,SAASA,CAAYC,MAAM,EAAE;YAC3B,IAAMjG,IAAI,GAAGiG,MAAM,CAAC,CAAC,CAAC;YACtB,UAAA3F,MAAA,CAAUN,IAAI,CAACoG,IAAI,QAAA9F,MAAA,CAAKN,IAAI,CAACmG,KAAK;UACpC;QACF,CAAC;QACDM,IAAI,EAAE;UACJ7F,IAAI,EAAE,IAAI;UACV8F,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,IAAI;UACZ9F,GAAG,EAAE,IAAI;UACT+F,YAAY,EAAE;QAChB,CAAC;QACDjC,KAAK,EAAE;UACLoB,IAAI,EAAE,OAAO;UACbc,SAAS,EAAE;YACTrG,KAAK,EAAE,MAAM;YACbwF,SAAS,EAAE;UACb,CAAC;UACDc,QAAQ,EAAE;YACRC,SAAS,EAAE;cACTvG,KAAK,EAAE;YACT;UACF,CAAC;UACDwG,SAAS,EAAE;YACTD,SAAS,EAAE;cACTvG,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACDqE,KAAK,EAAE;UACLkB,IAAI,EAAE,UAAU;UAChB3C,IAAI,EAAEuE,kBAAkB,CAACpC,GAAG,CAAC,UAAAvF,IAAI;YAAA,OAAIA,IAAI,CAACkH,QAAQ;UAAA,EAAC;UACnDL,SAAS,EAAE;YACTrG,KAAK,EAAE,MAAM;YACb4G,QAAQ,EAAE;UACZ,CAAC;UACDN,QAAQ,EAAE;YACRC,SAAS,EAAE;cACTvG,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACDsE,MAAM,EAAE,CACN;UACEsB,IAAI,EAAE,MAAM;UACZL,IAAI,EAAE,KAAK;UACX3C,IAAI,EAAEuE,kBAAkB,CAACpC,GAAG,CAAC,UAACvF,IAAI,EAAEC,KAAK,EAAK;YAC5C,IAAI4H,QAAQ;YACZ,IAAI5H,KAAK,IAAI,CAAC,EAAE;cACd4H,QAAQ,GAAG,SAAS;YACtB,CAAC,MAAM,IAAI5H,KAAK,IAAI,CAAC,EAAE;cACrB4H,QAAQ,GAAG,SAAS;YACtB,CAAC,MAAM;cACLA,QAAQ,GAAG,SAAS;YACtB;YAEA,OAAO;cACL1B,KAAK,EAAEnG,IAAI,CAAC4H,OAAO;cACnBE,MAAM,EAAE9H,IAAI,CAAC8H,MAAM;cAAE;cACrBT,SAAS,EAAE;gBACT7G,KAAK,EAAEqH,QAAQ;gBACfpG,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;cAC3B,CAAC;cACDsG,QAAQ,EAAE;gBACRV,SAAS,EAAE;kBACT7G,KAAK,EAAEqH,QAAQ;kBACfpG,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBAC1BuG,UAAU,EAAE,EAAE;kBACdC,WAAW,EAAE,0BAA0B;kBACvCC,WAAW,EAAE,CAAC;kBACdC,WAAW,EAAE;gBACf;cACF;YACF,CAAC;UACH,CAAC,CAAC;UACFb,KAAK,EAAE;YACL1C,IAAI,EAAE,IAAI;YACVlF,QAAQ,EAAE,OAAO;YACjBsG,SAAS,EAAE,KAAK;YAChBxF,KAAK,EAAE;UACT;QACF,CAAC;MAEL,CAAC;;MAED;MACA4B,OAAO,CAACgG,GAAG,CAAC,OAAO,CAAC,EAAC;MACrBhG,OAAO,CAACiG,EAAE,CAAC,OAAO,EAAE,UAASpC,MAAM,EAAE;QACnC,IAAIA,MAAM,CAACqC,aAAa,KAAK,QAAQ,IAAIrC,MAAM,CAACsC,UAAU,KAAK,KAAK,EAAE;UACpE,IAAMT,MAAM,GAAG7B,MAAM,CAAC7C,IAAI,CAAC0E,MAAM;UACjC,IAAIA,MAAM,EAAE;YACV;YACA,IAAMU,UAAU,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;YACvC,IAAMC,OAAO,GAAGJ,UAAU,CAACK,OAAO,CAAC,mBAAmB,EAAE,qBAAqB,CAAC;;YAE9E;YACA,IAAMC,oBAAoB,GAAG;cAC3B,CAAC,EAAE,CAAC;cAAG;cACP,CAAC,EAAE,CAAC;cAAG;cACP,CAAC,EAAE,EAAE,CAAE;YACT,CAAC;YACD,IAAMC,mBAAmB,GAAGD,oBAAoB,CAAC,IAAI,CAAC9F,oBAAoB,CAAC,IAAI,EAAE;;YAEjF;YACA,IAAMgG,GAAG,MAAA1I,MAAA,CAAMsI,OAAO,kBAAAtI,MAAA,CAAewH,MAAM,qBAAAxH,MAAA,CAAkByI,mBAAmB,CAAE;YAClF3K,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE2K,GAAG,CAAC;YAC/B5K,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;cACnB4K,UAAU,EAAEnB,MAAM;cAClBoB,eAAe,EAAE,IAAI,CAAClG,oBAAoB;cAC1CmG,gBAAgB,EAAEJ;YACpB,CAAC,CAAC;;YAEF;YACAN,MAAM,CAACW,IAAI,CAACJ,GAAG,EAAE,QAAQ,CAAC;UAC5B,CAAC,MAAM;YACL5K,OAAO,CAACiL,IAAI,CAAC,eAAe,CAAC;UAC/B;QACF;MACF,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAEblH,OAAO,CAACmC,SAAS,CAACqB,MAAM,EAAE,IAAI,CAAC;;MAE/B;MACAxD,OAAO,CAACmH,KAAK,CAAC,CAAC,CAAClB,EAAE,CAAC,WAAW,EAAE,UAASpC,MAAM,EAAE;QAC/C,IAAMuD,YAAY,GAAG,CAACvD,MAAM,CAACwD,OAAO,EAAExD,MAAM,CAACyD,OAAO,CAAC;QACrD,IAAItH,OAAO,CAACuH,YAAY,CAAC,MAAM,EAAEH,YAAY,CAAC,EAAE;UAC9CpH,OAAO,CAACmH,KAAK,CAAC,CAAC,CAACK,cAAc,CAAC,SAAS,CAAC;QAC3C,CAAC,MAAM;UACLxH,OAAO,CAACmH,KAAK,CAAC,CAAC,CAACK,cAAc,CAAC,SAAS,CAAC;QAC3C;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACAC,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MACpB,IAAMzH,OAAO,GAAG,IAAI,CAACO,WAAW,CAAC,iBAAiB,CAAC;MACnD,IAAI,CAACP,OAAO,EAAE;;MAEd;MACA,IAAM0H,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;MAChD,IAAI,CAACA,iBAAiB,EAAE;QACtBpL,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC,CAACE,SAAS,GAAG,+CAA+C;QACtG;MACF;MAEA,IAAMkL,UAAU,GAAG,SAAbA,UAAUA,CAAIC,OAAO,EAAK;QAC9B,IAAMC,IAAI,GAAGD,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QACpC,IAAM+C,KAAK,GAAGF,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QACrC,IAAMgD,GAAG,GAAGH,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QACnC,UAAA7G,MAAA,CAAU2J,IAAI,YAAA3J,MAAA,CAAI4J,KAAK,YAAA5J,MAAA,CAAI6J,GAAG;MAChC,CAAC;;MAED;MACA,IAAIC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;;MAExB;MACA,IAAIP,iBAAiB,CAACQ,sBAAsB,EAAE;QAC5CR,iBAAiB,CAACQ,sBAAsB,CAACvK,OAAO,CAAC,UAAAwK,UAAU,EAAI;UAC7D,IAAIA,UAAU,CAACC,SAAS,EAAE;YACxBD,UAAU,CAACC,SAAS,CAACzK,OAAO,CAAC,UAAAC,IAAI,EAAI;cACnCoK,QAAQ,CAACK,GAAG,CAACzK,IAAI,CAAC0K,UAAU,CAAC;YAC/B,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIZ,iBAAiB,CAACa,+BAA+B,EAAE;QACrDb,iBAAiB,CAACa,+BAA+B,CAAC5K,OAAO,CAAC,UAAA6K,WAAW,EAAI;UACvE,IAAIA,WAAW,CAACC,UAAU,EAAE;YAC1BD,WAAW,CAACC,UAAU,CAAC9K,OAAO,CAAC,UAAAC,IAAI,EAAI;cACrCoK,QAAQ,CAACK,GAAG,CAACzK,IAAI,CAAC0K,UAAU,CAAC;YAC/B,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;;MAEA;MACAN,QAAQ,GAAG/G,KAAK,CAACyH,IAAI,CAACV,QAAQ,CAAC,CAACnL,IAAI,CAAC,CAAC;MACtC,IAAM8L,SAAS,GAAGX,QAAQ,CAAC7E,GAAG,CAACwE,UAAU,CAAC;MAE1C,IAAIK,QAAQ,CAACrL,MAAM,KAAK,CAAC,EAAE;QACzBL,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC,CAACE,SAAS,GAAG,+CAA+C;QACtG;MACF;;MAEA;MACA,IAAMiG,MAAM,GAAG,EAAE;MACjB,IAAMkG,UAAU,GAAG,EAAE;MACrB,IAAMpL,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACvG,IAAIqL,UAAU,GAAG,CAAC;;MAElB;MACA,IAAInB,iBAAiB,CAACQ,sBAAsB,EAAE;QAC5CR,iBAAiB,CAACQ,sBAAsB,CAACvK,OAAO,CAAC,UAAAwK,UAAU,EAAI;UAC7D,IAAMW,SAAS,GAAGd,QAAQ,CAAC7E,GAAG,CAAC,UAAA4F,IAAI,EAAI;YACrC,IAAMC,KAAK,GAAGb,UAAU,CAACC,SAAS,CAACa,IAAI,CAAC,UAAArL,IAAI;cAAA,OAAIA,IAAI,CAAC0K,UAAU,KAAKS,IAAI;YAAA,EAAC;YACzE,OAAOC,KAAK,GAAG5F,UAAU,CAAC4F,KAAK,CAACE,KAAK,CAAC,GAAG,IAAI;UAC/C,CAAC,CAAC;UAEFxG,MAAM,CAACyG,IAAI,CAAC;YACVnF,IAAI,EAAEmE,UAAU,CAACiB,SAAS;YAC1BzF,IAAI,EAAE,MAAM;YACZ0F,UAAU,EAAE,CAAC;YACbrI,IAAI,EAAE8H,SAAS;YACfQ,MAAM,EAAE,IAAI;YACZ3E,SAAS,EAAE;cACTvH,KAAK,EAAE,CAAC;cACRgB,KAAK,EAAEZ,MAAM,CAACqL,UAAU,GAAGrL,MAAM,CAACb,MAAM;YAC1C,CAAC;YACDsI,SAAS,EAAE;cACT7G,KAAK,EAAEZ,MAAM,CAACqL,UAAU,GAAGrL,MAAM,CAACb,MAAM;YAC1C,CAAC;YACD4M,MAAM,EAAE,QAAQ;YAChBC,UAAU,EAAE,CAAC;YACbC,YAAY,EAAE;UAChB,CAAC,CAAC;UAEFb,UAAU,CAACO,IAAI,CAAChB,UAAU,CAACiB,SAAS,CAAC;UACrCP,UAAU,EAAE;QACd,CAAC,CAAC;MACJ;;MAEA;MACA,IAAInB,iBAAiB,CAACa,+BAA+B,EAAE;QACrDb,iBAAiB,CAACa,+BAA+B,CAAC5K,OAAO,CAAC,UAAA6K,WAAW,EAAI;UACvE,IAAMkB,UAAU,GAAG1B,QAAQ,CAAC7E,GAAG,CAAC,UAAA4F,IAAI,EAAI;YACtC,IAAMC,KAAK,GAAGR,WAAW,CAACC,UAAU,CAACQ,IAAI,CAAC,UAAArL,IAAI;cAAA,OAAIA,IAAI,CAAC0K,UAAU,KAAKS,IAAI;YAAA,EAAC;YAC3E,OAAOC,KAAK,GAAG5F,UAAU,CAAC4F,KAAK,CAACW,MAAM,CAAC,GAAG,IAAI;UAChD,CAAC,CAAC;UAEFjH,MAAM,CAACyG,IAAI,CAAC;YACVnF,IAAI,EAAEwE,WAAW,CAACoB,UAAU;YAC5BjG,IAAI,EAAE,MAAM;YACZ0F,UAAU,EAAE,CAAC;YACbrI,IAAI,EAAE0I,UAAU;YAChBJ,MAAM,EAAE,IAAI;YACZ3E,SAAS,EAAE;cACTvH,KAAK,EAAE,CAAC;cACRgB,KAAK,EAAEZ,MAAM,CAACqL,UAAU,GAAGrL,MAAM,CAACb,MAAM;YAC1C,CAAC;YACDsI,SAAS,EAAE;cACT7G,KAAK,EAAEZ,MAAM,CAACqL,UAAU,GAAGrL,MAAM,CAACb,MAAM;YAC1C,CAAC;YACD4M,MAAM,EAAE,QAAQ;YAChBC,UAAU,EAAE,CAAC;YACbC,YAAY,EAAE;UAChB,CAAC,CAAC;UAEFb,UAAU,CAACO,IAAI,CAACX,WAAW,CAACoB,UAAU,CAAC;UACvCf,UAAU,EAAE;QACd,CAAC,CAAC;MACJ;;MAEO;MACN,IAAIgB,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS;;MAE5C;MACA,IAAMC,WAAW,GAAGvH,MAAM,CAACwH,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAACd,UAAU,KAAK,CAAC;MAAA,EAAC,CACvDe,OAAO,CAAC,UAAAD,CAAC;QAAA,OAAIA,CAAC,CAACnJ,IAAI,CAACkJ,MAAM,CAAC,UAAAnJ,CAAC;UAAA,OAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKmD,SAAS;QAAA,EAAC;MAAA,EAAC;MAClE,IAAI+F,WAAW,CAACtN,MAAM,GAAG,CAAC,EAAE;QAC1BkN,QAAQ,GAAGvL,IAAI,CAACsE,GAAG,CAAAM,KAAA,CAAR5E,IAAI,MAAAgD,mBAAA,CAAAzF,OAAA,EAAQoO,WAAW,EAAC;QACnCH,QAAQ,GAAGxL,IAAI,CAAC2E,GAAG,CAAAC,KAAA,CAAR5E,IAAI,MAAAgD,mBAAA,CAAAzF,OAAA,EAAQoO,WAAW,EAAC;MACrC;;MAEA;MACA,IAAMI,YAAY,GAAG3H,MAAM,CAACwH,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAACd,UAAU,KAAK,CAAC;MAAA,EAAC,CACxDe,OAAO,CAAC,UAAAD,CAAC;QAAA,OAAIA,CAAC,CAACnJ,IAAI,CAACkJ,MAAM,CAAC,UAAAnJ,CAAC;UAAA,OAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKmD,SAAS;QAAA,EAAC;MAAA,EAAC;MAClE,IAAImG,YAAY,CAAC1N,MAAM,GAAG,CAAC,EAAE;QAC3BoN,SAAS,GAAGzL,IAAI,CAACsE,GAAG,CAAAM,KAAA,CAAR5E,IAAI,MAAAgD,mBAAA,CAAAzF,OAAA,EAAQwO,YAAY,EAAC;QACrCL,SAAS,GAAG1L,IAAI,CAAC2E,GAAG,CAAAC,KAAA,CAAR5E,IAAI,MAAAgD,mBAAA,CAAAzF,OAAA,EAAQwO,YAAY,EAAC;MACvC;MAED,IAAM7G,MAAM,GAAG;QACbvE,OAAO,EAAE;UACPwE,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXC,IAAI,EAAE;UACR,CAAC;UACDC,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIyG,GAAG,GAAGzG,MAAM,CAAC,CAAC,CAAC,CAAC0G,cAAc,GAAG,OAAO;YAC5C1G,MAAM,CAAClG,OAAO,CAAC,UAAAC,IAAI,EAAI;cACrB,IAAIA,IAAI,CAACmG,KAAK,KAAK,IAAI,IAAInG,IAAI,CAACmG,KAAK,KAAKG,SAAS,EAAE;gBACnD,IAAItG,IAAI,CAAC4M,UAAU,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAI7M,IAAI,CAAC4M,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;kBACnEH,GAAG,OAAApM,MAAA,CAAON,IAAI,CAAC8M,MAAM,EAAAxM,MAAA,CAAGN,IAAI,CAAC4M,UAAU,QAAAtM,MAAA,CAAKN,IAAI,CAACmG,KAAK,wBAAW;gBACnE,CAAC,MAAM;kBACLuG,GAAG,OAAApM,MAAA,CAAON,IAAI,CAAC8M,MAAM,EAAAxM,MAAA,CAAGN,IAAI,CAAC4M,UAAU,QAAAtM,MAAA,CAAKkF,UAAU,CAACxF,IAAI,CAACmG,KAAK,CAAC,CAAC7E,OAAO,CAAC,CAAC,CAAC,uBAAU;gBACzF;cACF,CAAC,MAAM;gBACLoL,GAAG,OAAApM,MAAA,CAAON,IAAI,CAAC8M,MAAM,EAAAxM,MAAA,CAAGN,IAAI,CAAC4M,UAAU,aAAU;cACnD;YACF,CAAC,CAAC;YACF,OAAOF,GAAG;UACZ;QACF,CAAC;QACDK,MAAM,EAAE;UACN3J,IAAI,EAAE4H,UAAU;UAChBtG,SAAS,EAAE;YACTlE,KAAK,EAAE;UACT,CAAC;UACDK,GAAG,EAAE;QACP,CAAC;QACD4F,IAAI,EAAE;UACJ7F,IAAI,EAAE,IAAI;UACV8F,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACb9F,GAAG,EAAE,KAAK;UACV+F,YAAY,EAAE;QAChB,CAAC;QACDjC,KAAK,EAAE;UACLoB,IAAI,EAAE,UAAU;UAChB3C,IAAI,EAAE2H,SAAS;UACflE,SAAS,EAAE;YACTrG,KAAK,EAAE,MAAM;YACb4G,QAAQ,EAAE,SAAVA,QAAQA,CAAWnH,KAAK,EAAEkG,KAAK,EAAE;cAC/B;cACA,IAAIlG,KAAK,IAAImK,QAAQ,CAACrL,MAAM,IAAI,CAACqL,QAAQ,CAACrL,MAAM,EAAE,OAAO,KAAK;;cAE9D;cACA,IAAMiO,YAAY,GAAG,IAAI3C,GAAG,CAAC,CAAC;cAC9BD,QAAQ,CAACrK,OAAO,CAAC,UAAAiK,OAAO,EAAI;gBAC1B,IAAMC,IAAI,GAAGD,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;gBACpC,IAAM+C,KAAK,GAAGF,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;gBACrC6F,YAAY,CAACvC,GAAG,IAAAnK,MAAA,CAAI2J,IAAI,EAAA3J,MAAA,CAAG4J,KAAK,CAAE,CAAC;cACrC,CAAC,CAAC;cAEF,IAAM+C,WAAW,GAAGD,YAAY,CAACE,IAAI;cACrC,IAAID,WAAW,IAAI,CAAC,EAAE,OAAO,IAAI;;cAEjC;cACA,IAAME,eAAe,GAAG/C,QAAQ,CAACrL,MAAM;cACvC,IAAMqO,aAAa,GAAG1M,IAAI,CAAC2M,KAAK,CAACF,eAAe,GAAGzM,IAAI,CAACsE,GAAG,CAACiI,WAAW,EAAE,CAAC,CAAC,CAAC,EAAC;;cAE7E,OAAOhN,KAAK,GAAGS,IAAI,CAAC2E,GAAG,CAAC+H,aAAa,EAAE,CAAC,CAAC,KAAK,CAAC;YACjD,CAAC;YACDpH,SAAS,EAAE,SAAXA,SAASA,CAAWG,KAAK,EAAElG,KAAK,EAAE;cAChC;cACA,IAAIA,KAAK,IAAImK,QAAQ,CAACrL,MAAM,EAAE,OAAO,EAAE;cACvC,IAAMuO,eAAe,GAAGlD,QAAQ,CAACnK,KAAK,CAAC;cACvC,IAAI,CAACqN,eAAe,EAAE,OAAO,EAAE;cAE/B,IAAMrD,IAAI,GAAGqD,eAAe,CAACnG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;cAC5C,IAAM+C,KAAK,GAAGqD,QAAQ,CAACD,eAAe,CAACnG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACvD,UAAA7G,MAAA,CAAU2J,IAAI,OAAA3J,MAAA,CAAI4J,KAAK;YACzB;UACF,CAAC;UACDpD,QAAQ,EAAE;YACRC,SAAS,EAAE;cACTvG,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACDqE,KAAK,EAAE,CACL;UACEkB,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,SAAS;UACf1G,QAAQ,EAAE,MAAM;UAChBsF,GAAG,EAAEiH,QAAQ;UACb5G,GAAG,EAAE6G,QAAQ;UACbpF,QAAQ,EAAE;YACRC,SAAS,EAAE;cACTvG,KAAK,EAAE;YACT;UACF,CAAC;UACDqG,SAAS,EAAE;YACTrG,KAAK,EAAE;UACT,CAAC;UACDwG,SAAS,EAAE;YACTD,SAAS,EAAE;cACTvG,KAAK,EAAE;YACT;UACF;QACF,CAAC,EACD;UACEuF,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,UAAU;UAChB1G,QAAQ,EAAE,OAAO;UACjBsF,GAAG,EAAEmH,SAAS;UACd9G,GAAG,EAAE+G,SAAS;UACdtF,QAAQ,EAAE;YACRC,SAAS,EAAE;cACTvG,KAAK,EAAE;YACT;UACF,CAAC;UACDqG,SAAS,EAAE;YACTrG,KAAK,EAAE,MAAM;YACbwF,SAAS,EAAE,SAAXA,SAASA,CAAWG,KAAK,EAAE;cACzB,OAAOX,UAAU,CAACW,KAAK,CAAC,CAAC7E,OAAO,CAAC,CAAC,CAAC;YACrC;UACF,CAAC;UACD0F,SAAS,EAAE;YACTpC,IAAI,EAAE;UACR;QACF,CAAC,CACF;QACDE,MAAM,EAAEA;MACV,CAAC;MAED1C,OAAO,CAACmC,SAAS,CAACqB,MAAM,EAAE,IAAI,CAAC;IACjC,CAAC;IAED;IACA4H,sBAAsB,WAAtBA,sBAAsBA,CAAA,EAAG;MACvB,IAAMpL,OAAO,GAAG,IAAI,CAACO,WAAW,CAAC,iBAAiB,CAAC;MACnD,IAAI,CAACP,OAAO,EAAE;;MAEd;MACA,IAAMqL,wBAAwB,GAAG,IAAI,CAACC,oBAAoB;MAC1D,IAAI,CAACD,wBAAwB,IAAI,CAACpK,KAAK,CAACC,OAAO,CAACmK,wBAAwB,CAAC,IAAIA,wBAAwB,CAAC1O,MAAM,KAAK,CAAC,EAAE;QAClHL,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC,CAACE,SAAS,GAAG,+CAA+C;QACtG;MACF;MAEA,IAAMkL,UAAU,GAAG,SAAbA,UAAUA,CAAIC,OAAO,EAAK;QAC9B,IAAMC,IAAI,GAAGD,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QACpC,IAAM+C,KAAK,GAAGF,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QACrC,IAAMgD,GAAG,GAAGH,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QACnC,UAAA7G,MAAA,CAAU2J,IAAI,YAAA3J,MAAA,CAAI4J,KAAK,YAAA5J,MAAA,CAAI6J,GAAG;MAChC,CAAC;;MAED;MACA,IAAIC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;;MAExB;MACAoD,wBAAwB,CAAC1N,OAAO,CAAC,UAAA4N,YAAY,EAAI;QAC/C;QACA,IAAIA,YAAY,CAACrD,sBAAsB,EAAE;UACvCqD,YAAY,CAACrD,sBAAsB,CAACvK,OAAO,CAAC,UAAAwK,UAAU,EAAI;YACxD,IAAIA,UAAU,CAACC,SAAS,EAAE;cACxBD,UAAU,CAACC,SAAS,CAACzK,OAAO,CAAC,UAAAC,IAAI,EAAI;gBACnCoK,QAAQ,CAACK,GAAG,CAACzK,IAAI,CAAC0K,UAAU,CAAC;cAC/B,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;;QAEA;QACA,IAAIiD,YAAY,CAAChD,+BAA+B,EAAE;UAChDgD,YAAY,CAAChD,+BAA+B,CAAC5K,OAAO,CAAC,UAAA6K,WAAW,EAAI;YAClE,IAAIA,WAAW,CAACC,UAAU,EAAE;cAC1BD,WAAW,CAACC,UAAU,CAAC9K,OAAO,CAAC,UAAAC,IAAI,EAAI;gBACrCoK,QAAQ,CAACK,GAAG,CAACzK,IAAI,CAAC0K,UAAU,CAAC;cAC/B,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF;MACAN,QAAQ,GAAG/G,KAAK,CAACyH,IAAI,CAACV,QAAQ,CAAC,CAACnL,IAAI,CAAC,CAAC;MACtC,IAAM8L,SAAS,GAAGX,QAAQ,CAAC7E,GAAG,CAACwE,UAAU,CAAC;MAE1C,IAAIK,QAAQ,CAACrL,MAAM,KAAK,CAAC,EAAE;QACzBL,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC,CAACE,SAAS,GAAG,+CAA+C;QACtG;MACF;;MAEA;MACA,IAAMiG,MAAM,GAAG,EAAE;MACjB,IAAMkG,UAAU,GAAG,EAAE;MACrB,IAAMpL,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAC7H,IAAIqL,UAAU,GAAG,CAAC;;MAElB;MACAwC,wBAAwB,CAAC1N,OAAO,CAAC,UAAA4N,YAAY,EAAI;QAC/C,IAAMC,YAAY,GAAGD,YAAY,CAACtN,QAAQ;;QAE1C;QACA,IAAIsN,YAAY,CAACrD,sBAAsB,EAAE;UACvCqD,YAAY,CAACrD,sBAAsB,CAACvK,OAAO,CAAC,UAAAwK,UAAU,EAAI;YACxD,IAAMW,SAAS,GAAGd,QAAQ,CAAC7E,GAAG,CAAC,UAAA4F,IAAI,EAAI;cACrC,IAAMC,KAAK,GAAGb,UAAU,CAACC,SAAS,CAACa,IAAI,CAAC,UAAArL,IAAI;gBAAA,OAAIA,IAAI,CAAC0K,UAAU,KAAKS,IAAI;cAAA,EAAC;cACzE,OAAOC,KAAK,GAAG5F,UAAU,CAAC4F,KAAK,CAACE,KAAK,CAAC,GAAG,IAAI;YAC/C,CAAC,CAAC;YAEFxG,MAAM,CAACyG,IAAI,CAAC;cACVnF,IAAI,KAAA9F,MAAA,CAAKsN,YAAY,OAAAtN,MAAA,CAAIiK,UAAU,CAACiB,SAAS,CAAE;cAC/CzF,IAAI,EAAE,MAAM;cACZ0F,UAAU,EAAE,CAAC;cACbrI,IAAI,EAAE8H,SAAS;cACfQ,MAAM,EAAE,IAAI;cACZ3E,SAAS,EAAE;gBACTvH,KAAK,EAAE,CAAC;gBACRgB,KAAK,EAAEZ,MAAM,CAACqL,UAAU,GAAGrL,MAAM,CAACb,MAAM;cAC1C,CAAC;cACDsI,SAAS,EAAE;gBACT7G,KAAK,EAAEZ,MAAM,CAACqL,UAAU,GAAGrL,MAAM,CAACb,MAAM;cAC1C,CAAC;cACD4M,MAAM,EAAE,QAAQ;cAChBC,UAAU,EAAE,CAAC;cACbC,YAAY,EAAE;YAChB,CAAC,CAAC;YAEFb,UAAU,CAACO,IAAI,IAAAjL,MAAA,CAAIsN,YAAY,OAAAtN,MAAA,CAAIiK,UAAU,CAACiB,SAAS,CAAE,CAAC;YAC1DP,UAAU,EAAE;UACd,CAAC,CAAC;QACJ;;QAEA;QACA,IAAI0C,YAAY,CAAChD,+BAA+B,EAAE;UAChDgD,YAAY,CAAChD,+BAA+B,CAAC5K,OAAO,CAAC,UAAA6K,WAAW,EAAI;YAClE,IAAMkB,UAAU,GAAG1B,QAAQ,CAAC7E,GAAG,CAAC,UAAA4F,IAAI,EAAI;cACtC,IAAMC,KAAK,GAAGR,WAAW,CAACC,UAAU,CAACQ,IAAI,CAAC,UAAArL,IAAI;gBAAA,OAAIA,IAAI,CAAC0K,UAAU,KAAKS,IAAI;cAAA,EAAC;cAC3E,OAAOC,KAAK,GAAG5F,UAAU,CAAC4F,KAAK,CAACW,MAAM,CAAC,GAAG,IAAI;YAChD,CAAC,CAAC;YAEFjH,MAAM,CAACyG,IAAI,CAAC;cACVnF,IAAI,KAAA9F,MAAA,CAAKsN,YAAY,OAAAtN,MAAA,CAAIsK,WAAW,CAACoB,UAAU,CAAE;cACjDjG,IAAI,EAAE,MAAM;cACZ0F,UAAU,EAAE,CAAC;cACbrI,IAAI,EAAE0I,UAAU;cAChBJ,MAAM,EAAE,IAAI;cACZ3E,SAAS,EAAE;gBACTvH,KAAK,EAAE,CAAC;gBACRgB,KAAK,EAAEZ,MAAM,CAACqL,UAAU,GAAGrL,MAAM,CAACb,MAAM;cAC1C,CAAC;cACDsI,SAAS,EAAE;gBACT7G,KAAK,EAAEZ,MAAM,CAACqL,UAAU,GAAGrL,MAAM,CAACb,MAAM;cAC1C,CAAC;cACD4M,MAAM,EAAE,QAAQ;cAChBC,UAAU,EAAE,CAAC;cACbC,YAAY,EAAE;YAChB,CAAC,CAAC;YAEFb,UAAU,CAACO,IAAI,IAAAjL,MAAA,CAAIsN,YAAY,OAAAtN,MAAA,CAAIsK,WAAW,CAACoB,UAAU,CAAE,CAAC;YAC5Df,UAAU,EAAE;UACd,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF;MACA,IAAIgB,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS;;MAE5C;MACA,IAAMC,WAAW,GAAGvH,MAAM,CAACwH,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAACd,UAAU,KAAK,CAAC;MAAA,EAAC,CACvDe,OAAO,CAAC,UAAAD,CAAC;QAAA,OAAIA,CAAC,CAACnJ,IAAI,CAACkJ,MAAM,CAAC,UAAAnJ,CAAC;UAAA,OAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKmD,SAAS;QAAA,EAAC;MAAA,EAAC;MAClE,IAAI+F,WAAW,CAACtN,MAAM,GAAG,CAAC,EAAE;QAC1BkN,QAAQ,GAAGvL,IAAI,CAACsE,GAAG,CAAAM,KAAA,CAAR5E,IAAI,MAAAgD,mBAAA,CAAAzF,OAAA,EAAQoO,WAAW,EAAC;QACnCH,QAAQ,GAAGxL,IAAI,CAAC2E,GAAG,CAAAC,KAAA,CAAR5E,IAAI,MAAAgD,mBAAA,CAAAzF,OAAA,EAAQoO,WAAW,EAAC;MACrC;;MAEA;MACA,IAAMI,YAAY,GAAG3H,MAAM,CAACwH,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAACd,UAAU,KAAK,CAAC;MAAA,EAAC,CACxDe,OAAO,CAAC,UAAAD,CAAC;QAAA,OAAIA,CAAC,CAACnJ,IAAI,CAACkJ,MAAM,CAAC,UAAAnJ,CAAC;UAAA,OAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKmD,SAAS;QAAA,EAAC;MAAA,EAAC;MAClE,IAAImG,YAAY,CAAC1N,MAAM,GAAG,CAAC,EAAE;QAC3BoN,SAAS,GAAGzL,IAAI,CAACsE,GAAG,CAAAM,KAAA,CAAR5E,IAAI,MAAAgD,mBAAA,CAAAzF,OAAA,EAAQwO,YAAY,EAAC;QACrCL,SAAS,GAAG1L,IAAI,CAAC2E,GAAG,CAAAC,KAAA,CAAR5E,IAAI,MAAAgD,mBAAA,CAAAzF,OAAA,EAAQwO,YAAY,EAAC;MACvC;MAEA,IAAM7G,MAAM,GAAG;QACbvE,OAAO,EAAE;UACPwE,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXC,IAAI,EAAE;UACR,CAAC;UACDC,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIyG,GAAG,GAAGzG,MAAM,CAAC,CAAC,CAAC,CAAC0G,cAAc,GAAG,OAAO;YAC5C1G,MAAM,CAAClG,OAAO,CAAC,UAAAC,IAAI,EAAI;cACrB,IAAIA,IAAI,CAACmG,KAAK,KAAK,IAAI,IAAInG,IAAI,CAACmG,KAAK,KAAKG,SAAS,EAAE;gBACnD,IAAItG,IAAI,CAAC4M,UAAU,CAACC,QAAQ,CAAC,IAAI,CAAC,IAAI7M,IAAI,CAAC4M,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;kBACnEH,GAAG,OAAApM,MAAA,CAAON,IAAI,CAAC8M,MAAM,EAAAxM,MAAA,CAAGN,IAAI,CAAC4M,UAAU,QAAAtM,MAAA,CAAKN,IAAI,CAACmG,KAAK,wBAAW;gBACnE,CAAC,MAAM;kBACLuG,GAAG,OAAApM,MAAA,CAAON,IAAI,CAAC8M,MAAM,EAAAxM,MAAA,CAAGN,IAAI,CAAC4M,UAAU,QAAAtM,MAAA,CAAKkF,UAAU,CAACxF,IAAI,CAACmG,KAAK,CAAC,CAAC7E,OAAO,CAAC,CAAC,CAAC,iBAAS;gBACxF;cACF,CAAC,MAAM;gBACLoL,GAAG,OAAApM,MAAA,CAAON,IAAI,CAAC8M,MAAM,EAAAxM,MAAA,CAAGN,IAAI,CAAC4M,UAAU,aAAU;cACnD;YACF,CAAC,CAAC;YACF,OAAOF,GAAG;UACZ;QACF,CAAC;QACDK,MAAM,EAAE;UACN3J,IAAI,EAAE4H,UAAU;UAChBtG,SAAS,EAAE;YACTlE,KAAK,EAAE;UACT,CAAC;UACDK,GAAG,EAAE,IAAI;UACTkF,IAAI,EAAE,QAAQ;UACd8H,iBAAiB,EAAE,CAAC;UACpBC,aAAa,EAAE,EAAE;UACjBC,kBAAkB,EAAE,KAAK;UACzBC,aAAa,EAAE,mBAAmB;UAClCC,aAAa,EAAE,SAAS;UACxBC,qBAAqB,EAAE,MAAM;UAC7BC,YAAY,EAAE,EAAE;UAChBC,aAAa,EAAE;YACb5N,KAAK,EAAE;UACT;QACF,CAAC;QACDiG,IAAI,EAAE;UACJ7F,IAAI,EAAE,IAAI;UACV8F,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACb9F,GAAG,EAAE,KAAK;UACV+F,YAAY,EAAE;QAChB,CAAC;QACDjC,KAAK,EAAE;UACLoB,IAAI,EAAE,UAAU;UAChB3C,IAAI,EAAE2H,SAAS;UACflE,SAAS,EAAE;YACTrG,KAAK,EAAE,MAAM;YACb4G,QAAQ,EAAE,SAAVA,QAAQA,CAAWnH,KAAK,EAAEkG,KAAK,EAAE;cAC/B;cACA,IAAIlG,KAAK,IAAImK,QAAQ,CAACrL,MAAM,IAAI,CAACqL,QAAQ,CAACrL,MAAM,EAAE,OAAO,KAAK;;cAE9D;cACA,IAAMiO,YAAY,GAAG,IAAI3C,GAAG,CAAC,CAAC;cAC9BD,QAAQ,CAACrK,OAAO,CAAC,UAAAiK,OAAO,EAAI;gBAC1B,IAAMC,IAAI,GAAGD,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;gBACpC,IAAM+C,KAAK,GAAGF,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;gBACrC6F,YAAY,CAACvC,GAAG,IAAAnK,MAAA,CAAI2J,IAAI,EAAA3J,MAAA,CAAG4J,KAAK,CAAE,CAAC;cACrC,CAAC,CAAC;cAEF,IAAM+C,WAAW,GAAGD,YAAY,CAACE,IAAI;cACrC,IAAID,WAAW,IAAI,CAAC,EAAE,OAAO,IAAI;;cAEjC;cACA,IAAME,eAAe,GAAG/C,QAAQ,CAACrL,MAAM;cACvC,IAAMqO,aAAa,GAAG1M,IAAI,CAAC2M,KAAK,CAACF,eAAe,GAAGzM,IAAI,CAACsE,GAAG,CAACiI,WAAW,EAAE,CAAC,CAAC,CAAC,EAAC;;cAE7E,OAAOhN,KAAK,GAAGS,IAAI,CAAC2E,GAAG,CAAC+H,aAAa,EAAE,CAAC,CAAC,KAAK,CAAC;YACjD,CAAC;YACDpH,SAAS,EAAE,SAAXA,SAASA,CAAWG,KAAK,EAAElG,KAAK,EAAE;cAChC;cACA,IAAIA,KAAK,IAAImK,QAAQ,CAACrL,MAAM,EAAE,OAAO,EAAE;cACvC,IAAMuO,eAAe,GAAGlD,QAAQ,CAACnK,KAAK,CAAC;cACvC,IAAI,CAACqN,eAAe,EAAE,OAAO,EAAE;cAE/B,IAAMrD,IAAI,GAAGqD,eAAe,CAACnG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;cAC5C,IAAM+C,KAAK,GAAGqD,QAAQ,CAACD,eAAe,CAACnG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACvD,UAAA7G,MAAA,CAAU2J,IAAI,OAAA3J,MAAA,CAAI4J,KAAK;YACzB;UACF,CAAC;UACDpD,QAAQ,EAAE;YACRC,SAAS,EAAE;cACTvG,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACDqE,KAAK,EAAE,CACL;UACEkB,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,SAAS;UACf1G,QAAQ,EAAE,MAAM;UAChBsF,GAAG,EAAEiH,QAAQ;UACb5G,GAAG,EAAE6G,QAAQ;UACbpF,QAAQ,EAAE;YACRC,SAAS,EAAE;cACTvG,KAAK,EAAE;YACT;UACF,CAAC;UACDqG,SAAS,EAAE;YACTrG,KAAK,EAAE;UACT,CAAC;UACDwG,SAAS,EAAE;YACTD,SAAS,EAAE;cACTvG,KAAK,EAAE;YACT;UACF;QACF,CAAC,EACD;UACEuF,IAAI,EAAE,OAAO;UACbK,IAAI,EAAE,SAAS;UACf1G,QAAQ,EAAE,OAAO;UACjBsF,GAAG,EAAEmH,SAAS;UACd9G,GAAG,EAAE+G,SAAS;UACdtF,QAAQ,EAAE;YACRC,SAAS,EAAE;cACTvG,KAAK,EAAE;YACT;UACF,CAAC;UACDqG,SAAS,EAAE;YACTrG,KAAK,EAAE,MAAM;YACbwF,SAAS,EAAE,SAAXA,SAASA,CAAWG,KAAK,EAAE;cACzB,OAAOX,UAAU,CAACW,KAAK,CAAC,CAAC7E,OAAO,CAAC,CAAC,CAAC;YACrC;UACF,CAAC;UACD0F,SAAS,EAAE;YACTpC,IAAI,EAAE;UACR;QACF,CAAC,CACF;QACDE,MAAM,EAAEA;MACV,CAAC;MAED1C,OAAO,CAACmC,SAAS,CAACqB,MAAM,EAAE,IAAI,CAAC;IACjC;EACF;AACF,CAAC", "ignoreList": []}]}