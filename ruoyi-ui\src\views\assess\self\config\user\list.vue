<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-row>
          <el-form-item label="考核年月" prop="assessDate">
            <el-date-picker
              v-model="queryParams.assessDate"
              type="month"
              value-format="yyyy-M"
              format="yyyy 年 M 月"
              placeholder="选择考核年月"
              :clearable="false">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="工号" prop="workNo">
            <el-input
              v-model="queryParams.workNo"
              placeholder="请输入工号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="部门" prop="deptId">
            <treeselect style="width: 200px;" v-model="queryParams.deptId" :multiple="false" :options="deptOptions" :normalizer="normalizer" :disable-branch-nodes="true" placeholder="请选择部门" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>
  
      <el-row :gutter="10" class="mb8">
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
  
      <el-table v-loading="loading" :data="selfAssessUserList">
        <!-- <el-table-column label="编号" align="center" prop="id" /> -->
        <el-table-column label="工号" align="center" prop="workNo" width="120"/>
        <el-table-column label="姓名" align="center" prop="name" width="120"/>
        <!-- <el-table-column label="身份" align="center" prop="assessRole" width="120">
          <template slot-scope="scope">
            {{ dicts.self_assess_role[scope.row.assessRole]["label"] }}
          </template>
        </el-table-column> -->
        <el-table-column label="部门" align="center">
          <template slot-scope="scope">
            <span v-for="item, index in scope.row.deptList" v-bind:key="index">
              {{ scope.row.deptList.length > 1 && index + 1 != scope.row.deptList.length ? item.deptName + ", " : item.deptName}}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="填写状态" align="center" prop="assessStatus" width="120">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.assessStatus === '0'" type="primary">已保存</el-tag>
            <el-tag v-else-if="scope.row.assessStatus === '1'" type="warning">部门评分</el-tag>
            <el-tag v-else-if="scope.row.assessStatus === '2'" type="warning">事业部评分</el-tag>
            <el-tag v-else-if="scope.row.assessStatus === '3'" type="warning">运改/组织部评分</el-tag>
            <el-tag v-else-if="scope.row.assessStatus === '4' || scope.row.assessStatus === '5'" type="success">已完成</el-tag>
            <el-tag v-else type="info">未填写</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleConfig(scope.row)"
            >指标配置</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleReport(scope.row)"
            >自评填写</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
  
    </div>
  </template>
  
  <script>
  import { getToken } from "@/utils/auth";
  import { listAvailable, listAvailableWithStatus } from "@/api/assess/self/user";
  import { listDept } from "@/api/assess/lateral/dept";
  import Treeselect from "@riophae/vue-treeselect";
  import "@riophae/vue-treeselect/dist/vue-treeselect.css";
  
  export default {
    name: "SelfAssessUserList",
    components: {
      Treeselect
    },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 绩效考核-干部自评人员配置表格数据
        selfAssessUserList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          assessDate: null,
          workNo: null,
          name: null,
          assessRole: null,
          benefitLinkFlag: null,
          averageLinkFlag: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
        },
        // 字典
        dicts:{
          self_assess_role:[],
          sys_yes_no:[]
        },
        // 部门下拉树
        deptOptions:[],
        // 导入参数
        upload: {
          // 是否禁用上传
          isUploading: false,
          // 设置上传的请求头部
          headers: { Authorization: 'Bearer ' + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/web/selfAssessUser/importInfo",
        },
        // 导入结果
        importRes:[],
        openImportRes:false
      };
    },
    created() {
      this.initPageData();
    },

    // 监听路由变化，确保每次进入页面都重新获取数据
    watch: {
      '$route'(to) {
        // 当路由发生变化时，重新初始化页面数据
        if (to.path === '/assess/self/user/list') {
          this.initPageData();
        }
      }
    },

    // 路由更新时的钩子
    beforeRouteUpdate(to, from, next) {
      // 在当前路由改变，但是该组件被复用时调用
      this.initPageData();
      next();
    },
    methods: {
      // 初始化页面数据
      initPageData() {
        // 设置默认考核年月为当前年月
        const now = new Date();
        this.queryParams.assessDate = `${now.getFullYear()}-${now.getMonth() + 1}`;

        // 重置数据
        this.selfAssessUserList = [];
        this.total = 0;

        // 获取数据
        this.getList();
        this.getTreeselect();
        this.getDicts("self_assess_role").then(response => {
          this.dicts.self_assess_role = this.formatterDict(response.data);
        });
        this.getDicts("sys_yes_no").then(response => {
          this.dicts.sys_yes_no = this.formatterDict(response.data);
        });
      },

      formatterDict(dict){
        let result = []
        dict.forEach(dict => {
          result.push({
            label:dict.dictLabel,
            value:dict.dictValue
          })
        });
        return result;
      },
      /** 查询绩效考核-干部自评人员配置列表 */
      getList() {
        this.loading = true;

        // 如果有考核年月，使用批量获取状态的接口
        if (this.queryParams.assessDate) {
          listAvailableWithStatus(this.queryParams).then(response => {
            this.selfAssessUserList = response.data;
            this.loading = false;
          }).catch(error => {
            console.error('获取用户列表失败:', error);
            this.$message.error('获取用户列表失败');
            this.loading = false;
          });
        } else {
          // 没有考核年月时使用原接口
          listAvailable(this.queryParams).then(response => {
            this.selfAssessUserList = response.data;
            this.loading = false;
          }).catch(error => {
            console.error('获取用户列表失败:', error);
            this.$message.error('获取用户列表失败');
            this.loading = false;
          });
        }
      },


      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          id: null,
          workNo: null,
          name: null,
          assessRole: null,
          benefitLinkFlag: null,
          averageLinkFlag: null
        };
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm("queryForm");
        // 重置后重新设置默认考核年月
        const now = new Date();
        this.queryParams.assessDate = `${now.getFullYear()}-${now.getMonth() + 1}`;
        this.handleQuery();
      },
  
      /** 转换横向评价部门数据结构 */
      normalizer(node) {
        if (node.children && !node.children.length) {
          delete node.children;
        }
        return {
          id: node.deptId,
          label: node.deptName,
          children: node.children
        };
      },
        /** 查询横向评价部门下拉树结构 */
      getTreeselect() {
        listDept().then(response => {
          this.deptOptions = this.handleTree(response.data, "deptId", "parentId");
        });
      },
  
      /** 配置点击事件 */
      handleConfig(row){
        this.$router.push({
          path:"/assess/self/user/detail",
          query:{
            userId:row.id
          }
        })
      },
      /** 自评填写点击事件 */
      handleReport(row){
        this.$router.push({
          path:"/assess/self/user/report",
          query:{
            workNo:row.workNo,
            deptId:row.deptList[0].deptId
          }
        })
      }
      
    }
  };
  </script>
  <style>
  .redtext{
    color: red;
  }
  </style>
  