package com.ruoyi.app.qualityCost.controller;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.app.qualityCost.domain.ContractDeviation;
import com.ruoyi.app.qualityCost.domain.QualityObjection;
import com.ruoyi.app.qualityCost.domain.RegradeDetail;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.qualityCost.domain.ScrapDetail;
import com.ruoyi.app.qualityCost.service.IScrapDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 内部损失成本-产品报废损失明细Controller
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@RequestMapping("/qualityCost/scrapDetail")
public class ScrapDetailController extends BaseController
{
    @Autowired
    private IScrapDetailService scrapDetailService;

    /**
     * 查询内部损失成本-产品报废损失明细列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ScrapDetail scrapDetail)
    {
        startPage();
        List<ScrapDetail> list = scrapDetailService.selectScrapDetailList(scrapDetail);
        return getDataTable(list);
    }

    @GetMapping("/listAll")
    public TableDataInfo listAll(ScrapDetail scrapDetail)
    {

        List<ScrapDetail> list = null;
        if (scrapDetail.getSearchMode().equals("模糊搜索")) {
            startPage();
            list = scrapDetailService.selectScrapDetailList(scrapDetail);
        } else if (scrapDetail.getSearchMode().equals("精确搜索")) {
            startPage();
            list = scrapDetailService.selectScrapDetailListByAccurateSearch(scrapDetail);
        }

        return getDataTable(list);
    }

    @GetMapping("/getSum")
    public AjaxResult getSum(ScrapDetail scrapDetail)
    {
        List<ScrapDetail> list = null;
        if (scrapDetail.getSearchMode().equals("模糊搜索")) {
            startPage();
            list = scrapDetailService.selectScrapDetailList(scrapDetail);
        } else if (scrapDetail.getSearchMode().equals("精确搜索")) {
            startPage();
            list = scrapDetailService.selectScrapDetailListByAccurateSearch(scrapDetail);
        }
        BigDecimal sumCostTon = BigDecimal.ZERO;
        BigDecimal sumCostPerTon = BigDecimal.ZERO;
        BigDecimal sumCostEx = BigDecimal.ZERO;
        for (ScrapDetail item : list) {
            if (item.getCostTon() != null) {
                sumCostTon = sumCostTon.add(item.getCostTon());
            }
            if (item.getCostPerTon() != null) {
                sumCostPerTon = sumCostPerTon.add(item.getCostPerTon());
            }
            if (item.getCostEx() != null) {
                sumCostEx = sumCostEx.add(item.getCostEx());
            }
        }
        ContractDeviation sumObj = new ContractDeviation();
        sumObj.setCostTon(sumCostTon);
        sumObj.setCostPerTon(sumCostPerTon);
        sumObj.setCostEx(sumCostEx);

        return AjaxResult.success(sumObj);
    }

    @GetMapping("/getAllSum")
    public AjaxResult getAllSum(ScrapDetail scrapDetail)
    {
        List<ScrapDetail> list = null;
        if (scrapDetail.getSearchMode().equals("模糊搜索")) {
            list = scrapDetailService.selectScrapDetailList(scrapDetail);
        } else if (scrapDetail.getSearchMode().equals("精确搜索")) {
            list = scrapDetailService.selectScrapDetailListByAccurateSearch(scrapDetail);
        }
        BigDecimal sumCostTon = BigDecimal.ZERO;
        BigDecimal sumCostPerTon = BigDecimal.ZERO;
        BigDecimal sumCostEx = BigDecimal.ZERO;
        for (ScrapDetail item : list) {
            if (item.getCostTon() != null) {
                sumCostTon = sumCostTon.add(item.getCostTon());
            }
            if (item.getCostPerTon() != null) {
                sumCostPerTon = sumCostPerTon.add(item.getCostPerTon());
            }
            if (item.getCostEx() != null) {
                sumCostEx = sumCostEx.add(item.getCostEx());
            }
        }
        ContractDeviation sumObj = new ContractDeviation();
        sumObj.setCostTon(sumCostTon);
        sumObj.setCostPerTon(sumCostPerTon);
        sumObj.setCostEx(sumCostEx);

        return AjaxResult.success(sumObj);
    }

    /**
     * 导出内部损失成本-产品报废损失明细列表
     */
    @Log(title = "内部损失成本-产品报废损失明细", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ScrapDetail scrapDetail)
    {
        List<ScrapDetail> list = scrapDetailService.selectScrapDetailList(scrapDetail);
        ExcelUtil<ScrapDetail> util = new ExcelUtil<ScrapDetail>(ScrapDetail.class);
        return util.exportExcel(list, "scrapDetail");
    }

    /**
     * 获取内部损失成本-产品报废损失明细详细信息
     */
    @GetMapping(value = "/{recCreator}")
    public AjaxResult getInfo(@PathVariable("recCreator") String recCreator)
    {
        return AjaxResult.success(scrapDetailService.selectScrapDetailById(recCreator));
    }

    /**
     * 新增内部损失成本-产品报废损失明细
     */
    @Log(title = "内部损失成本-产品报废损失明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScrapDetail scrapDetail)
    {
        return toAjax(scrapDetailService.insertScrapDetail(scrapDetail));
    }

    /**
     * 修改内部损失成本-产品报废损失明细
     */
    @Log(title = "内部损失成本-产品报废损失明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScrapDetail scrapDetail)
    {
        return toAjax(scrapDetailService.updateScrapDetail(scrapDetail));
    }

    /**
     * 删除内部损失成本-产品报废损失明细
     */
    @Log(title = "内部损失成本-产品报废损失明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{recCreators}")
    public AjaxResult remove(@PathVariable String[] recCreators)
    {
        return toAjax(scrapDetailService.deleteScrapDetailByIds(recCreators));
    }

    /**
     * 按成本中心和会计期查询产品报废损失明细
     */
    @GetMapping("/scrapDetailByCenterAndPeriod")
    public AjaxResult getScrapDetailByCenterAndPeriod(@RequestParam String costCenter, @RequestParam String yearMonth) {
        List<ScrapDetail> list = scrapDetailService.selectScrapDetailByCenterAndPeriod(costCenter, yearMonth);
        return AjaxResult.success(list);
    }
}
