package com.ruoyi.app.v1.service.impl;

import java.util.*;

import com.ruoyi.app.v1.domain.HrLateralAssessDept;
import com.ruoyi.app.v1.mapper.HrLateralAssessDeptMapper;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysRoleMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ruoyi.app.v1.mapper.HrSelfAssessUserMapper;
import com.ruoyi.app.v1.domain.HrSelfAssessUser;
import com.ruoyi.app.v1.service.IHrSelfAssessUserService;
import com.ruoyi.common.core.service.BaseSoServiceImpl;
import org.springframework.transaction.annotation.Transactional;

/**
 * 绩效考核-干部自评人员配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
@Service
public class HrSelfAssessUserServiceImpl extends BaseSoServiceImpl<HrSelfAssessUserMapper, HrSelfAssessUser> implements IHrSelfAssessUserService
{
    private static final Logger log = LoggerFactory.getLogger(HrSelfAssessUserServiceImpl.class);
    @Autowired
    private HrSelfAssessUserMapper hrSelfAssessUserMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private HrLateralAssessDeptMapper hrLateralAssessDeptMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;

    /**
     * 获取单条数据
     * @param id 绩效考核-干部自评人员配置id
     * @return 绩效考核-干部自评人员配置
     */
    @Override
    public HrSelfAssessUser get(String id) {
        HrSelfAssessUser dto = super.get(id);
        Long[] deptIds = hrSelfAssessUserMapper.selectHrSelfAssessUserDeptByUserId(dto.getId());
        dto.setDeptIds(deptIds);
        List<HrLateralAssessDept> deptList = hrSelfAssessUserMapper.selectHrSelfAssessUserDeptListByUserId(dto.getId());
        dto.setDeptList(deptList);
        String[] leaders = hrSelfAssessUserMapper.selectHrSelfAssessUserLeaderByUserId(dto.getId());
        dto.setLeaders(leaders);
        return dto;
    }

    /**
     * 获取单条数据
     * @param workNo 绩效考核-干部自评人员配置工号
     * @return 绩效考核-干部自评人员配置
     */
    @Override
    public HrSelfAssessUser getByWorkNo(String workNo) {
        HrSelfAssessUser dto = hrSelfAssessUserMapper.getByWorkNo(workNo);
        if(StringUtils.isNotNull(dto)){
            List<HrLateralAssessDept> deptList = hrSelfAssessUserMapper.selectHrSelfAssessUserDeptListByUserId(dto.getId());
            dto.setDeptList(deptList);
        }
        return dto;
    }

    @Override
    public HrSelfAssessUser getByUserInfo(HrSelfAssessUser hrSelfAssessUser) {
        HrSelfAssessUser dto = hrSelfAssessUserMapper.getByUserInfo(hrSelfAssessUser);
        List<HrLateralAssessDept> deptList = hrSelfAssessUserMapper.selectHrSelfAssessUserDeptListByUserId(dto.getId());
        dto.setDeptList(deptList);
        return null;
    }

    @Override
    public HrSelfAssessUser getByWorkNoDeptId(HrSelfAssessUser hrSelfAssessUser) {
        return hrSelfAssessUserMapper.getByWorkNoDeptId(hrSelfAssessUser);
    }

    @Override
    public List<HrLateralAssessDept> getReportDeptList(String workNo) {
        HrSelfAssessUser user = new HrSelfAssessUser();
        user.setWorkNo(workNo);
        // 干部部门
        user.setAssessRole("0");
        List<HrLateralAssessDept> list1 = hrSelfAssessUserMapper.getDeptListByUser(user);
        // 一把手部门
        user.setAssessRole("1");
        List<HrLateralAssessDept> list2 = hrSelfAssessUserMapper.getDeptListByUser(user);
        list1.addAll(list2);
        return list1;
    }

    @Override
    public List<HrLateralAssessDept> getCheckDeptList(String workNo) {
        HrSelfAssessUser user = new HrSelfAssessUser();
        user.setWorkNo(workNo);
        // 一把手部门
        user.setAssessRole("1");
        List<HrLateralAssessDept> list1 = hrSelfAssessUserMapper.getDeptListByUser(user);
        // 条线领导
        user.setAssessRole("2");
        List<HrLateralAssessDept> list2 = hrSelfAssessUserMapper.getDeptListByUser(user);
        list1.addAll(list2);
        return list1;
    }

    /**
     * 查询绩效考核-干部自评人员配置列表
     * @param hrSelfAssessUser 绩效考核-干部自评人员配置
     * @return 绩效考核-干部自评人员配置
     */
    @Override
    public List<HrSelfAssessUser> findList(HrSelfAssessUser hrSelfAssessUser) {
        List<HrSelfAssessUser> hrSelfAssessUserList = super.findList(hrSelfAssessUser);
        for(HrSelfAssessUser user : hrSelfAssessUserList){
            user.setDeptList(hrSelfAssessUserMapper.selectHrSelfAssessUserDeptListByUserId(user.getId()));
        }
        return hrSelfAssessUserList;
    }


    @Override
    public List<HrSelfAssessUser> listAvailable(HrSelfAssessUser hrSelfAssessUser) {
        List<HrSelfAssessUser> hrSelfAssessUserList = hrSelfAssessUserMapper.listAvailable(hrSelfAssessUser);
        for(HrSelfAssessUser user : hrSelfAssessUserList){
            user.setDeptList(hrSelfAssessUserMapper.selectHrSelfAssessUserDeptListByUserId(user.getId()));
        }
        return hrSelfAssessUserList;
    }

    @Override
    public List<HrSelfAssessUser> listAvailableWithStatus(HrSelfAssessUser hrSelfAssessUser, String assessDate) {
        List<HrSelfAssessUser> hrSelfAssessUserList = hrSelfAssessUserMapper.listAvailable(hrSelfAssessUser);
        for(HrSelfAssessUser user : hrSelfAssessUserList){
            user.setDeptList(hrSelfAssessUserMapper.selectHrSelfAssessUserDeptListByUserId(user.getId()));

            // 批量获取用户状态
            String status = hrSelfAssessUserMapper.getUserAssessStatus(user.getWorkNo(), assessDate);
            if(StringUtils.isNotNull(status)) {
                // 根据后台状态映射前端显示状态
//                if("0".equals(status)) {
//                    user.setAssessStatus("1"); // 已保存（暂存状态）
//                } else if("1".equals(status)) {
//                    user.setAssessStatus("2"); // 已提交（部门领导评分）
//                } else if("2".equals(status) || "3".equals(status) || "4".equals(status) || "5".equals(status)) {
//                    user.setAssessStatus("3"); // 已审核（各级审核状态）
//                } else {
//                    user.setAssessStatus("1"); // 其他状态默认为已保存
//                }
                user.setAssessStatus(status);
            } else {
                user.setAssessStatus("-1"); // 未填写
            }
        }
        return hrSelfAssessUserList;
    }

    /**
     * 插入绩效考核-干部自评人员配置
     * @param hrSelfAssessUser 绩效考核-干部自评人员配置
     * @return 绩效考核-干部自评人员配置
     */
    @Override
    @Transactional
    public boolean insert(HrSelfAssessUser hrSelfAssessUser) {
        hrSelfAssessUserMapper.insert(hrSelfAssessUser);
        this.batchHrSelfAssessUserDept(hrSelfAssessUser);
        if(hrSelfAssessUser.getLeaders().length > 0){
            this.batchHrSelfAssessUserLeader(hrSelfAssessUser);
        }
        return true;
    }

    /**
     * 批量插入绩效考核-干部自评人员配置
     * @param list
     * @return 绩效考核-干部自评人员配置
     */
    @Override
    public boolean batch(List<HrSelfAssessUser> list) {
        return super.batch(list);
    }

    /**
     * 更新绩效考核-干部自评人员配置
     * @param hrSelfAssessUser 绩效考核-干部自评人员配置
     * @return 绩效考核-干部自评人员配置
     */
    @Override
    @Transactional
    public boolean update(HrSelfAssessUser hrSelfAssessUser) {
        hrSelfAssessUserMapper.deleteHrSelfAssessUserDept(hrSelfAssessUser.getId());
        this.batchHrSelfAssessUserDept(hrSelfAssessUser);
        hrSelfAssessUserMapper.deleteHrSelfAssessUserLeader(hrSelfAssessUser.getId());
        if(hrSelfAssessUser.getLeaders().length > 0){
            this.batchHrSelfAssessUserLeader(hrSelfAssessUser);
        }
        return super.update(hrSelfAssessUser);
    }

    /**
     * 删除绩效考核-干部自评人员配置
     * @param hrSelfAssessUser 绩效考核-干部自评人员配置
     * @return 绩效考核-干部自评人员配置
     */
    @Override
    public boolean delete(HrSelfAssessUser hrSelfAssessUser) {
        hrSelfAssessUserMapper.deleteHrSelfAssessUserDept(hrSelfAssessUser.getId());
        return super.delete(hrSelfAssessUser);
    }

    @Override
    @Transactional
    public List<Map> importInfo(List<HrSelfAssessUser> hrSelfAssessUserList) {
        List<Map> resultList = new ArrayList<>();
        for(HrSelfAssessUser user : hrSelfAssessUserList){
            Map resultMap = new HashMap();
            String workNo = user.getWorkNo();
            resultMap.put("workNo",workNo);
            resultMap.put("assessRole",user.getAssessRole());
            resultMap.put("benefitLinkFlag",user.getBenefitLinkFlag());
            resultMap.put("averageLinkFlag",user.getAverageLinkFlag());
            SysUser sysUser = sysUserMapper.selectUserByUserName(workNo);
            if(StringUtils.isNotNull(sysUser)){
                user.setName(sysUser.getNickName());
                // 部门
                String deptNames = user.getDeptNames();
                deptNames = deptNames.replace('，',',');
                String[] deptNameList = deptNames.split(",");
                List<Map> deptList = new ArrayList<>();
                List<Long> deptIdList = new ArrayList<>();
                for(String deptName:deptNameList){
                    Map deptMap = new HashMap();
                    HrLateralAssessDept dept = hrLateralAssessDeptMapper.selectHrLateralAssessDeptByDeptName(deptName);
                    if(StringUtils.isNotNull(dept)){
                        deptIdList.add(dept.getDeptId());
                        deptMap.put("deptName",deptName);
                        deptMap.put("deptStatus",true);
                        deptMap.put("msg","部门配置成功");
                    }else{
                        deptMap.put("deptName",deptName);
                        deptMap.put("deptStatus",false);
                        deptMap.put("msg","未找到部门");
                    }
                    deptList.add(deptMap);
                }
                resultMap.put("deptList",deptList);
                Long[] deptIds = deptIdList.toArray(new Long[deptIdList.size()]);

                // 条线领导
                String leaderNames = user.getLeaderNames();
                leaderNames = leaderNames.replace('，',',');
                String[] leaderNameArray = leaderNames.split(",");
                List<Map> leaderList = new ArrayList<>();
                List<String> leaderNoList = new ArrayList<>();
                for(String leaderName:leaderNameArray){
                    Map leaderMap = new HashMap();
                    HrSelfAssessUser leaderUser = hrSelfAssessUserMapper.getLeaderWorkNoByName(leaderName);
                    if(StringUtils.isNotNull(leaderUser)){
                        leaderNoList.add(leaderUser.getWorkNo());
                        leaderMap.put("leaderName",leaderName);
                        leaderMap.put("leaderStatus",true);
                        leaderMap.put("msg","条线领导配置成功");
                    }else{
                        leaderMap.put("leaderName",leaderName);
                        leaderMap.put("leaderStatus",false);
                        leaderMap.put("msg","未找到条线领导");
                    }
                    leaderList.add(leaderMap);
                }
                resultMap.put("leaderList",leaderList);
                String[] leaders = leaderNoList.toArray(new String[leaderList.size()]);
                // 插入用户部门关联关系
                user.setDeptIds(deptIds);
                user.setLeaders(leaders);
                HrSelfAssessUser dto = hrSelfAssessUserMapper.getByUserInfo(user);
                if(StringUtils.isNotNull(dto)){
                    user.setId(dto.getId());
                    super.update(user);
                    hrSelfAssessUserMapper.deleteHrSelfAssessUserDept(user.getId());
                    this.batchHrSelfAssessUserDept(user);
                    hrSelfAssessUserMapper.deleteHrSelfAssessUserLeader(user.getId());
                    this.batchHrSelfAssessUserLeader(user);
                    resultMap.put("name",user.getName());
                    resultMap.put("isSuccess",true);
                    resultMap.put("msg","更新成功");
                }else{
                    hrSelfAssessUserMapper.insert(user);
                    hrSelfAssessUserMapper.deleteHrSelfAssessUserDept(user.getId());
                    this.batchHrSelfAssessUserDept(user);
                    hrSelfAssessUserMapper.deleteHrSelfAssessUserLeader(user.getId());
                    this.batchHrSelfAssessUserLeader(user);
                    resultMap.put("name",user.getName());
                    resultMap.put("isSuccess",true);
                    resultMap.put("msg","插入成功");
                }
            }else {
                resultMap.put("isSuccess",false);
                resultMap.put("msg","未查找到用户信息");
            }
            resultList.add(resultMap);
        }
        return resultList;
    }

    // 判断用户是否已存在
    @Override
    public boolean checkUserUnique(HrSelfAssessUser hrSelfAssessUser) {
        HrSelfAssessUser user = hrSelfAssessUserMapper.checkUserUnique(hrSelfAssessUser);
        return !StringUtils.isNotNull(user);
    }

    // 批量插入关联关系
    private void batchHrSelfAssessUserDept(HrSelfAssessUser hrSelfAssessUser){
        // 插入用户部门关联关系
        Long[] deptIds = hrSelfAssessUser.getDeptIds();
        if(!StringUtils.isNotNull(deptIds)) return;
        List<HrSelfAssessUser> users = new ArrayList<>();
        for(Long deptId:deptIds){
            HrSelfAssessUser user = new HrSelfAssessUser();
            user.setDeptId(deptId);
            user.setId(hrSelfAssessUser.getId());
            users.add(user);
        }
        hrSelfAssessUserMapper.batchHrSelfAssessUserDept(users);
    }

    // 批量插入关联关系
    private void batchHrSelfAssessUserLeader(HrSelfAssessUser hrSelfAssessUser){
        // 插入用户部门关联关系
        String[] leaders = hrSelfAssessUser.getLeaders();
        if(!StringUtils.isNotNull(leaders)) return;
        List<HrSelfAssessUser> users = new ArrayList<>();
        for(String workNo:leaders){
            HrSelfAssessUser user = new HrSelfAssessUser();
            user.setId(hrSelfAssessUser.getId());
            user.setWorkNo(workNo);
            users.add(user);
        }
        hrSelfAssessUserMapper.batchHrSelfAssessUserLeader(users);
    }


    @Override
    @Transactional
    public boolean permissionRefresh() {
        HrSelfAssessUser param = new HrSelfAssessUser();
        List<HrSelfAssessUser> userList = super.findList(param);
        SysRole reportRole = sysRoleMapper.selectRoleByRoleKey("selfAssessReport");
        SysRole checkRole = sysRoleMapper.selectRoleByRoleKey("selfAssessCheck");
        SysRole leaderRole = sysRoleMapper.selectRoleByRoleKey("selfAssessLeaderCheck");
        sysUserRoleMapper.deleteUserRoleByRoleId(reportRole.getRoleId());
        sysUserRoleMapper.deleteUserRoleByRoleId(checkRole.getRoleId());
        sysUserRoleMapper.deleteUserRoleByRoleId(leaderRole.getRoleId());
        List<SysUserRole> reportRoleList = new ArrayList<>();
        List<SysUserRole> checkRoleList = new ArrayList<>();
        List<SysUserRole> leaderRoleList = new ArrayList<>();
        for(HrSelfAssessUser user : userList){
            SysUser sysUser = sysUserMapper.selectUserByUserName(user.getWorkNo());
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(sysUser.getUserId());
            switch (user.getAssessRole()) {
                case "0":
                    userRole.setRoleId(reportRole.getRoleId());
                    if (!reportRoleList.contains(userRole)) {
                        reportRoleList.add(userRole);
                    }
                    break;
                case "1":
                    SysUserRole role1 = new SysUserRole();
                    role1.setRoleId(reportRole.getRoleId());
                    role1.setUserId(sysUser.getUserId());
                    if (!reportRoleList.contains(role1)) {
                        reportRoleList.add(role1);
                    }
                    SysUserRole role2 = new SysUserRole();
                    role2.setRoleId(checkRole.getRoleId());
                    role2.setUserId(sysUser.getUserId());
                    if (!checkRoleList.contains(role2)) {
                        checkRoleList.add(role2);
                    }
                    break;
                case "2":
                    userRole.setRoleId(leaderRole.getRoleId());
                    if (!leaderRoleList.contains(userRole)) {
                        leaderRoleList.add(userRole);
                    }
                    break;
            }
        }
        if(reportRoleList.size() > 0){
            sysUserRoleMapper.batchUserRole(reportRoleList);
        }
        if(checkRoleList.size() > 0){
            sysUserRoleMapper.batchUserRole(checkRoleList);
        }
        if(leaderRoleList.size() > 0){
            sysUserRoleMapper.batchUserRole(leaderRoleList);
        }
        return true;
    }
}
