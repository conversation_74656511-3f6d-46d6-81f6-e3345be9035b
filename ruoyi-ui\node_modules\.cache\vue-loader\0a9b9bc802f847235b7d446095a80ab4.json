{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\scrapDetail\\index.vue?vue&type=style&index=0&id=c2ea2d10&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\scrapDetail\\index.vue", "mtime": 1756456493917}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnNjcmFwLWRldGFpbC1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi50YWJsZS10aXRsZSB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLnRhYmxlLXRpdGxlIGgyIHsNCiAgbWFyZ2luOiAwOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLnRhYmxlLWhlYWRlci1pbmZvIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBwYWRkaW5nOiAxMHB4IDA7DQogIGdhcDogMjRweDsNCn0NCg0KLmhlYWRlci1pdGVtIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmhlYWRlci1pdGVtIC5sYWJlbCB7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQp9DQoNCi5oZWFkZXItaXRlbSAudmFsdWUgew0KICBjb2xvcjogIzMwMzEzMzsNCn0NCg0KLmhlYWRlci1pdGVtOmZpcnN0LWNoaWxkIC5sYWJlbCB7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi5zZWFyY2gtYmFyLXJvdyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgcGFkZGluZzogMTBweCAwOw0KICBnYXA6IDI0cHg7DQp9DQoNCi5tYWluLXRhYmxlIHsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQp9DQoNCi5zdWJ0b3RhbC1zZWN0aW9uIHsNCiAgbWFyZ2luLXRvcDogLTFweDsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQp9DQoNCi8qIOihqOagvOagt+W8j+WumuWItiAqLw0KLnNjcmFwLWRldGFpbC10YWJsZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLnNjcmFwLWRldGFpbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2hlYWRlci13cmFwcGVyKSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7DQp9DQoNCi5zY3JhcC1kZXRhaWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19oZWFkZXIgdGgpIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBwYWRkaW5nOiAxMnB4IDA7DQp9DQoNCi5zY3JhcC1kZXRhaWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19ib2R5IHRyOm50aC1jaGlsZChvZGQpKSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7DQp9DQoNCi5zY3JhcC1kZXRhaWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19ib2R5IHRyOmhvdmVyKSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmMGY5ZmY7DQp9DQoNCi8qIOWwj+iuoeihqOagvOagt+W8jyAqLw0KLnN1YnRvdGFsLXRhYmxlIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQouc3VidG90YWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19oZWFkZXIpIHsNCiAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50Ow0KfQ0KDQouc3VidG90YWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19oZWFkZXItd3JhcHBlcikgew0KICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7DQp9DQoNCi5zdWJ0b3RhbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2JvZHkgdHIpIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjlmZjsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQoNCi5zdWJ0b3RhbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2JvZHkgdGQpIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2YwZjlmZiAhaW1wb3J0YW50Ow0KICBwYWRkaW5nOiAxMnB4IDA7DQp9DQoNCi8qIOaAu+iuoeihqOagvOagt+W8jyAqLw0KLnRvdGFsLXNlY3Rpb24gew0KICBtYXJnaW4tdG9wOiAtMXB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCn0NCg0KLnRvdGFsLXRhYmxlIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQoudG90YWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19oZWFkZXIpIHsNCiAgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50Ow0KfQ0KDQoudG90YWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19oZWFkZXItd3JhcHBlcikgew0KICBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7DQp9DQoNCi50b3RhbC10YWJsZSA6ZGVlcCguZWwtdGFibGVfX2JvZHkgdHIpIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmNGQ0ZjsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLnRvdGFsLXRhYmxlIDpkZWVwKC5lbC10YWJsZV9fYm9keSB0ZCkgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmY0ZDRmICFpbXBvcnRhbnQ7DQogIHBhZGRpbmc6IDEycHggMDsNCiAgY29sb3I6IHdoaXRlOw0KfQ0KDQovKiDmoIfnrb7moLflvI8gKi8NCi5lbC10YWcgew0KICBmb250LXNpemU6IDEycHg7DQp9DQoNCi8qIOWTjeW6lOW8j+iuvuiuoSAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDEyMDBweCkgew0KICAudGFibGUtaGVhZGVyLWluZm8gew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgZ2FwOiAxMHB4Ow0KICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Ow0KICB9DQoNCiAgLnNlYXJjaC1iYXItcm93IHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIGdhcDogMTBweDsNCiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgfQ0KDQogIC5zY3JhcC1kZXRhaWwtdGFibGUgew0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgfQ0KDQogIC5zY3JhcC1kZXRhaWwtdGFibGUgOmRlZXAoLmVsLXRhYmxlX19ib2R5IHRkKSB7DQogICAgcGFkZGluZzogOHB4IDA7DQogIH0NCn0NCg0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5zY3JhcC1kZXRhaWwtY29udGFpbmVyIHsNCiAgICBwYWRkaW5nOiAxMHB4Ow0KICB9DQoNCiAgLm1haW4tdGFibGUgew0KICAgIG92ZXJmbG93LXg6IGF1dG87DQogIH0NCn0NCg0KLyog5pCc57Si5Yy65Z+f5qC35byPICovDQouaGVhZGVyLWl0ZW0gLmVsLWlucHV0IHsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQp9DQoNCi5oZWFkZXItaXRlbSAuZWwtYnV0dG9uIHsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQp9DQoNCi5oZWFkZXItaXRlbSAuZWwtYnV0dG9uOmxhc3QtY2hpbGQgew0KICBtYXJnaW4tcmlnaHQ6IDA7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqhBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/scrapDetail", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"scrap-detail-container\">\r\n      <!-- 表格标题 -->\r\n      <div class=\"table-title\">\r\n        <h2>兴澄特钢质量成本表-产品报废损失</h2>\r\n      </div>\r\n\r\n      <!-- 表格头部信息 -->\r\n      <div class=\"table-header-info\">\r\n        <!-- <div class=\"header-item\">\r\n          <span class=\"label\">产品报废损失</span>\r\n        </div> -->\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">成本中心名称：</span>\r\n          <el-select v-model=\"costCenter\" placeholder=\"请选择成本中心\" style=\"width: 160px;\" :loading=\"costCenterLoading\">\r\n            <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">会计期：</span>\r\n          <el-date-picker v-model=\"accountingPeriod\" type=\"month\" placeholder=\"2025-06\" format=\"yyyy-MM\"\r\n            value-format=\"yyyy-MM\" style=\"width: 150px;\">\r\n          </el-date-picker>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">是否计划内：</span>\r\n          <el-select v-model=\"planFlag\" placeholder=\"全部\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in planFlagOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 搜索行 -->\r\n      <div class=\"search-bar-row\">\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">钢种：</span>\r\n          <el-input v-model=\"searchParams.sgSign\" placeholder=\"请输入钢种\" style=\"width: 150px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">标准：</span>\r\n          <el-input v-model=\"searchParams.sgStd\" placeholder=\"请输入标准\" style=\"width: 150px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">报废原因：</span>\r\n          <el-input v-model=\"searchParams.reason\" placeholder=\"请输入报废原因\" style=\"width: 150px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">搜索模式：</span>\r\n          <el-select v-model=\"searchParams.searchMode\" placeholder=\"请选择搜索模式\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in searchModeOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <el-button type=\"primary\" @click=\"handleSearch\" size=\"small\">搜索</el-button>\r\n          <el-button @click=\"handleReset\" size=\"small\">重置</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主表格 -->\r\n      <div class=\"main-table\">\r\n        <el-table :data=\"tableData\" border style=\"width: auto;\" class=\"scrap-detail-table\" v-loading=\"tableLoading\"\r\n          element-loading-text=\"加载中...\">\r\n          <el-table-column prop=\"sgSign\" label=\"钢种\" align=\"center\" />\r\n          <el-table-column prop=\"sgStd\" label=\"标准\" align=\"center\" />\r\n          <el-table-column prop=\"crShp\" label=\"截面\" align=\"center\" />\r\n          <el-table-column prop=\"thick\" label=\"厚度\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.thick !== null && scope.row.thick !== undefined\">\r\n                {{ scope.row.thick }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"width\" label=\"宽度\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.width !== null && scope.row.width !== undefined\">\r\n                {{ scope.row.width }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"len\" label=\"长度\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.len !== null && scope.row.len !== undefined\">\r\n                {{ scope.row.len }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"costPerTon\" label=\"损失单价（元/吨）\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costPerTon !== null && scope.row.costPerTon !== undefined\">\r\n                {{ formatNumber(scope.row.costPerTon) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costTon\" label=\"吨位\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costTon !== null && scope.row.costTon !== undefined\">\r\n                {{ formatNumber(scope.row.costTon, 2) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costEx\" label=\"损失金额（元）\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costEx !== null && scope.row.costEx !== undefined\">\r\n                {{ formatNumber(scope.row.costEx) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"reason\" label=\"报废原因\" align=\"center\" />\r\n          <el-table-column prop=\"planFlag\" label=\"是否计划内\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"getPlanFlagTagType(scope.row.planFlag)\">\r\n                {{ getPlanFlagValue(scope.row.planFlag) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 小计行 -->\r\n      <div class=\"subtotal-section\">\r\n        <el-table :data=\"subtotalData\" border style=\"width: auto;\" class=\"subtotal-table\" :show-header=\"false\"\r\n          :span-method=\"subtotalSpanMethod\">\r\n          <el-table-column prop=\"label\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty1\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty2\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty3\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty4\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"totalTonnage\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalTonnage, 2) }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"totalUnitPrice\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalUnitPrice) }}\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"totalAmount\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"empty6\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty7\" label=\"\" align=\"center\" />\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 总计行 -->\r\n      <div class=\"total-section\">\r\n        <el-table :data=\"totalData\" border style=\"width: auto;\" class=\"total-table\" :show-header=\"false\"\r\n          :span-method=\"totalSpanMethod\">\r\n          <el-table-column prop=\"label\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty1\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty2\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty3\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty4\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"totalTonnage\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalTonnage, 2) }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"totalUnitPrice\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalUnitPrice) }}\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"totalAmount\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"empty6\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty7\" label=\"\" align=\"center\" />\r\n        </el-table>\r\n      </div>\r\n      <pagination :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"fetchTableData\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { listAllScrapDetail, getSum,getAllSum } from \"@/api/qualityCost/scrapDetail\";\r\n\r\nexport default {\r\n  name: \"ScrapDetail\",\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      // 成本中心和会计期\r\n      costCenter: '',\r\n      accountingPeriod: this.getDefaultYearMonth(),\r\n      // 新增：是否计划内筛选\r\n      planFlag: '1', // 默认“是”\r\n      planFlagOptions: [\r\n        { label: '全部', value: '' },\r\n        { label: '是', value: '1' },\r\n        { label: '否', value: '0' }\r\n      ],\r\n      // 表格数据\r\n      tableData: [],\r\n      // 表格加载状态\r\n      tableLoading: false,\r\n\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      costCenterLoading: false,\r\n      total: 0,\r\n      sumData: {},\r\n      allSumData: {},\r\n      // 新增：搜索参数\r\n      searchParams: {\r\n        sgSign: '',\r\n        sgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      },\r\n      // 搜索模式选项\r\n      searchModeOptions: [\r\n        { label: '模糊搜索', value: '模糊搜索' },\r\n        { label: '精确搜索', value: '精确搜索' }\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    // 计算小计数据\r\n    subtotalData() {\r\n      if (!this.tableData || this.tableData.length === 0) {\r\n        return [{\r\n          label: \"产品报废损失小计\",\r\n          totalTonnage: 0,\r\n          totalUnitPrice: 0,\r\n          totalAmount: 0\r\n        }];\r\n      }\r\n\r\n      const totalTonnage = this.sumData.costTon;\r\n\r\n      const totalUnitPrice = this.sumData.costPerTon;\r\n\r\n      const totalAmount = this.sumData.costEx;\r\n\r\n      return [{\r\n        label: \"产品报废损失小计\",\r\n        totalTonnage: totalTonnage,\r\n        totalUnitPrice: totalUnitPrice,\r\n        totalAmount: totalAmount\r\n      }];\r\n    },\r\n    // 计算总计数据\r\n    totalData() {\r\n       if (!this.tableData || this.tableData.length === 0) {\r\n        return [{\r\n          label: \"产品报废损失总计\",\r\n          totalTonnage: 0,\r\n          totalUnitPrice: 0,\r\n          totalAmount: 0\r\n        }];\r\n      }\r\n\r\n      const totalTonnage = this.allSumData.costTon;\r\n\r\n      const totalUnitPrice = this.allSumData.costPerTon;\r\n\r\n      const totalAmount = this.allSumData.costEx;\r\n\r\n      return [{\r\n        label: \"产品报废损失总计\",\r\n        totalTonnage: totalTonnage,\r\n        totalUnitPrice: totalUnitPrice,\r\n        totalAmount: totalAmount\r\n      }];\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听成本中心变化\r\n    costCenter: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 监听会计期变化\r\n    accountingPeriod: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 新增：监听是否计划内变化\r\n    planFlag: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCostCenterList();\r\n    this.queryParams.planFlag = this.planFlag;\r\n  },\r\n  methods: {\r\n    /** 获取默认会计期 */\r\n    getDefaultYearMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    },\r\n\r\n    // 获取成本中心列表\r\n    getCostCenterList() {\r\n      this.costCenterLoading = true;\r\n      costCenterlist().then(response => {\r\n        this.costCenterOptions = response.data || [];\r\n        // 如果有数据，设置默认选中第一个\r\n        if (this.costCenterOptions.length > 0) {\r\n          console.log('获取成本中心列表:', this.costCenterOptions);\r\n          this.costCenter = this.costCenterOptions[0].key;\r\n          // 设置默认值后，主动触发一次数据获取\r\n          this.$nextTick(() => {\r\n            this.fetchTableData();\r\n          });\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取成本中心列表失败:', error);\r\n        this.$message.error('获取成本中心列表失败');\r\n      }).finally(() => {\r\n        this.costCenterLoading = false;\r\n      });\r\n    },\r\n    // 获取表格数据\r\n    fetchTableData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod) {\r\n        this.tableData = [];\r\n        return;\r\n      }\r\n\r\n      this.tableLoading = true;\r\n\r\n      // 当选择\"江阴兴澄特种钢铁\"时，查询所有数据（不传costCenter参数）\r\n      let costCenterParam = this.costCenter;\r\n      const selectedOption = this.costCenterOptions.find(item => item.key === this.costCenter);\r\n      if (selectedOption && selectedOption.label === '公司') {\r\n        costCenterParam = ''; // 设置为空字符串，查询所有数据\r\n      }\r\n\r\n      this.queryParams.costCenter = costCenterParam;\r\n      this.queryParams.yearMonth = this.accountingPeriod.replace('-', '');\r\n      this.queryParams.planFlag = this.planFlag;\r\n      this.queryParams.sgSign = this.searchParams.sgSign;\r\n      this.queryParams.sgStd = this.searchParams.sgStd;\r\n      this.queryParams.reason = this.searchParams.reason;\r\n      this.queryParams.searchMode = this.searchParams.searchMode;\r\n\r\n      listAllScrapDetail(this.queryParams).then(response => {\r\n        //this.tableData = (response.rows || []).filter(item => item.costEx !== null && item.costEx !== undefined && item.costEx !== 0);\r\n        this.tableData = response.rows || [];\r\n        console.log('获取报废损失数据:', this.tableData);\r\n        this.total = response.total || 0;\r\n      }).catch(error => {\r\n        console.error('获取报废损失数据失败:', error);\r\n        this.$message.error('获取报废损失数据失败');\r\n        this.tableData = [];\r\n        this.total = 0;\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n\r\n      getSum(this.queryParams).then(response => {\r\n        this.sumData = response.data || [];\r\n      }).catch(error => {\r\n        this.$message.error('获取数据失败');\r\n        this.sumData = [];\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n\r\n      getAllSum(this.queryParams).then(response => {\r\n        this.allSumData = response.data || [];\r\n      }).catch(error => {\r\n        this.$message.error('获取数据失败');\r\n        this.allSumData = [];\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n    },\r\n    // 处理计划内标志值\r\n    getPlanFlagValue(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return '否';\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return '是';\r\n      }\r\n      return '未知'; // 既不是0也不是1时显示未知\r\n    },\r\n    // 获取计划内标志标签类型\r\n    getPlanFlagTagType(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return 'danger'; // 绿色\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return 'success'; // 红色\r\n      }\r\n      return 'warning'; // 黄色（未知状态）\r\n    },\r\n    // 获取当前年月\r\n    getCurrentMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = String(now.getMonth() + 1).padStart(2, '0');\r\n      return `${year}-${month}`;\r\n    },\r\n    // 格式化数字显示\r\n    formatNumber(value, decimals = 2) {\r\n      if (value === null || value === undefined) return '';\r\n      return Number(value).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: decimals,\r\n        maximumFractionDigits: decimals\r\n      });\r\n    },\r\n    subtotalSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 合并前6列为小计标签\r\n      if (columnIndex >= 0 && columnIndex <= 6) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 7\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      if (columnIndex >= 9 && columnIndex <= 10) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      // 其他列保持不变\r\n      return {\r\n        rowspan: 1,\r\n        colspan: 1\r\n      };\r\n    },\r\n    // 总计行合并方法\r\n    totalSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 合并前6列为总计标签\r\n      if (columnIndex >= 0 && columnIndex <= 6) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 7\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      if (columnIndex >= 9 && columnIndex <= 10) {\r\n        if (columnIndex === 9) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      // 其他列保持不变\r\n      return {\r\n        rowspan: 1,\r\n        colspan: 1\r\n      };\r\n    },\r\n    // 搜索按钮点击\r\n    handleSearch() {\r\n      this.queryParams.pageNum = 1;\r\n      this.fetchTableData();\r\n    },\r\n    // 重置按钮点击\r\n    handleReset() {\r\n      this.searchParams = {\r\n        sgSign: '',\r\n        sgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      };\r\n      this.queryParams.pageNum = 1;\r\n      this.fetchTableData();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.scrap-detail-container {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-title {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-title h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.table-header-info {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 24px;\r\n}\r\n\r\n.header-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.header-item .label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .value {\r\n  color: #303133;\r\n}\r\n\r\n.header-item:first-child .label {\r\n  color: #303133;\r\n  font-size: 16px;\r\n}\r\n\r\n.search-bar-row {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 24px;\r\n}\r\n\r\n.main-table {\r\n  margin-bottom: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.subtotal-section {\r\n  margin-top: -1px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n/* 表格样式定制 */\r\n.scrap-detail-table {\r\n  font-size: 14px;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__header-wrapper) {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__header th) {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: bold;\r\n  padding: 12px 0;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__body tr:nth-child(odd)) {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__body tr:hover) {\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n/* 小计表格样式 */\r\n.subtotal-table {\r\n  font-size: 14px;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header-wrapper) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body tr) {\r\n  background-color: #f0f9ff;\r\n  font-weight: bold;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body td) {\r\n  background-color: #f0f9ff !important;\r\n  padding: 12px 0;\r\n}\r\n\r\n/* 总计表格样式 */\r\n.total-section {\r\n  margin-top: -1px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.total-table {\r\n  font-size: 14px;\r\n}\r\n\r\n.total-table :deep(.el-table__header) {\r\n  display: none !important;\r\n}\r\n\r\n.total-table :deep(.el-table__header-wrapper) {\r\n  display: none !important;\r\n}\r\n\r\n.total-table :deep(.el-table__body tr) {\r\n  background-color: #ff4d4f;\r\n  font-weight: bold;\r\n  color: white;\r\n}\r\n\r\n.total-table :deep(.el-table__body td) {\r\n  background-color: #ff4d4f !important;\r\n  padding: 12px 0;\r\n  color: white;\r\n}\r\n\r\n/* 标签样式 */\r\n.el-tag {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .table-header-info {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .search-bar-row {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .scrap-detail-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .scrap-detail-table :deep(.el-table__body td) {\r\n    padding: 8px 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .scrap-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .main-table {\r\n    overflow-x: auto;\r\n  }\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.header-item .el-input {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button:last-child {\r\n  margin-right: 0;\r\n}\r\n</style>\r\n"]}]}