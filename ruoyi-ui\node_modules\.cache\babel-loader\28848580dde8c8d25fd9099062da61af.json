{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\scrapDetail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\scrapDetail\\index.vue", "mtime": 1756456493917}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_qualityCostDetail", "require", "_scrapDetail", "name", "data", "queryParams", "pageNum", "pageSize", "costCenter", "accountingPeriod", "getDefaultYearMonth", "planFlag", "planFlagOptions", "label", "value", "tableData", "tableLoading", "costCenterOptions", "costCenterLoading", "total", "sumData", "allSumData", "searchParams", "sgSign", "sgStd", "reason", "searchMode", "searchModeOptions", "computed", "subtotalData", "length", "totalTonnage", "totalUnitPrice", "totalAmount", "costTon", "costPerTon", "costEx", "totalData", "watch", "handler", "fetchTableData", "mounted", "getCostCenterList", "methods", "now", "Date", "year", "getFullYear", "month", "getMonth", "day", "getDate", "hour", "getHours", "prevMonth", "prevYear", "concat", "String", "padStart", "_this", "costCenterlist", "then", "response", "console", "log", "key", "$nextTick", "catch", "error", "$message", "finally", "_this2", "costCenterParam", "selectedOption", "find", "item", "yearMonth", "replace", "listAllScrapDetail", "rows", "getSum", "getAllSum", "getPlanFlagValue", "getPlanFlagTagType", "getCurrentMonth", "formatNumber", "decimals", "arguments", "undefined", "Number", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "subtotalSpanMethod", "_ref", "row", "column", "rowIndex", "columnIndex", "rowspan", "colspan", "totalSpanMethod", "_ref2", "handleSearch", "handleReset"], "sources": ["src/views/qualityCost/scrapDetail/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"scrap-detail-container\">\r\n      <!-- 表格标题 -->\r\n      <div class=\"table-title\">\r\n        <h2>兴澄特钢质量成本表-产品报废损失</h2>\r\n      </div>\r\n\r\n      <!-- 表格头部信息 -->\r\n      <div class=\"table-header-info\">\r\n        <!-- <div class=\"header-item\">\r\n          <span class=\"label\">产品报废损失</span>\r\n        </div> -->\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">成本中心名称：</span>\r\n          <el-select v-model=\"costCenter\" placeholder=\"请选择成本中心\" style=\"width: 160px;\" :loading=\"costCenterLoading\">\r\n            <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">会计期：</span>\r\n          <el-date-picker v-model=\"accountingPeriod\" type=\"month\" placeholder=\"2025-06\" format=\"yyyy-MM\"\r\n            value-format=\"yyyy-MM\" style=\"width: 150px;\">\r\n          </el-date-picker>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">是否计划内：</span>\r\n          <el-select v-model=\"planFlag\" placeholder=\"全部\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in planFlagOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 搜索行 -->\r\n      <div class=\"search-bar-row\">\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">钢种：</span>\r\n          <el-input v-model=\"searchParams.sgSign\" placeholder=\"请输入钢种\" style=\"width: 150px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">标准：</span>\r\n          <el-input v-model=\"searchParams.sgStd\" placeholder=\"请输入标准\" style=\"width: 150px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">报废原因：</span>\r\n          <el-input v-model=\"searchParams.reason\" placeholder=\"请输入报废原因\" style=\"width: 150px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">搜索模式：</span>\r\n          <el-select v-model=\"searchParams.searchMode\" placeholder=\"请选择搜索模式\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in searchModeOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <el-button type=\"primary\" @click=\"handleSearch\" size=\"small\">搜索</el-button>\r\n          <el-button @click=\"handleReset\" size=\"small\">重置</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主表格 -->\r\n      <div class=\"main-table\">\r\n        <el-table :data=\"tableData\" border style=\"width: auto;\" class=\"scrap-detail-table\" v-loading=\"tableLoading\"\r\n          element-loading-text=\"加载中...\">\r\n          <el-table-column prop=\"sgSign\" label=\"钢种\" align=\"center\" />\r\n          <el-table-column prop=\"sgStd\" label=\"标准\" align=\"center\" />\r\n          <el-table-column prop=\"crShp\" label=\"截面\" align=\"center\" />\r\n          <el-table-column prop=\"thick\" label=\"厚度\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.thick !== null && scope.row.thick !== undefined\">\r\n                {{ scope.row.thick }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"width\" label=\"宽度\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.width !== null && scope.row.width !== undefined\">\r\n                {{ scope.row.width }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"len\" label=\"长度\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.len !== null && scope.row.len !== undefined\">\r\n                {{ scope.row.len }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"costPerTon\" label=\"损失单价（元/吨）\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costPerTon !== null && scope.row.costPerTon !== undefined\">\r\n                {{ formatNumber(scope.row.costPerTon) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costTon\" label=\"吨位\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costTon !== null && scope.row.costTon !== undefined\">\r\n                {{ formatNumber(scope.row.costTon, 2) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costEx\" label=\"损失金额（元）\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costEx !== null && scope.row.costEx !== undefined\">\r\n                {{ formatNumber(scope.row.costEx) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"reason\" label=\"报废原因\" align=\"center\" />\r\n          <el-table-column prop=\"planFlag\" label=\"是否计划内\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"getPlanFlagTagType(scope.row.planFlag)\">\r\n                {{ getPlanFlagValue(scope.row.planFlag) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 小计行 -->\r\n      <div class=\"subtotal-section\">\r\n        <el-table :data=\"subtotalData\" border style=\"width: auto;\" class=\"subtotal-table\" :show-header=\"false\"\r\n          :span-method=\"subtotalSpanMethod\">\r\n          <el-table-column prop=\"label\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty1\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty2\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty3\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty4\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"totalTonnage\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalTonnage, 2) }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"totalUnitPrice\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalUnitPrice) }}\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"totalAmount\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"empty6\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty7\" label=\"\" align=\"center\" />\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 总计行 -->\r\n      <div class=\"total-section\">\r\n        <el-table :data=\"totalData\" border style=\"width: auto;\" class=\"total-table\" :show-header=\"false\"\r\n          :span-method=\"totalSpanMethod\">\r\n          <el-table-column prop=\"label\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty1\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty2\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty3\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty4\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"totalTonnage\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalTonnage, 2) }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"totalUnitPrice\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalUnitPrice) }}\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"totalAmount\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"empty6\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty7\" label=\"\" align=\"center\" />\r\n        </el-table>\r\n      </div>\r\n      <pagination :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"fetchTableData\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { listAllScrapDetail, getSum,getAllSum } from \"@/api/qualityCost/scrapDetail\";\r\n\r\nexport default {\r\n  name: \"ScrapDetail\",\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      // 成本中心和会计期\r\n      costCenter: '',\r\n      accountingPeriod: this.getDefaultYearMonth(),\r\n      // 新增：是否计划内筛选\r\n      planFlag: '1', // 默认“是”\r\n      planFlagOptions: [\r\n        { label: '全部', value: '' },\r\n        { label: '是', value: '1' },\r\n        { label: '否', value: '0' }\r\n      ],\r\n      // 表格数据\r\n      tableData: [],\r\n      // 表格加载状态\r\n      tableLoading: false,\r\n\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      costCenterLoading: false,\r\n      total: 0,\r\n      sumData: {},\r\n      allSumData: {},\r\n      // 新增：搜索参数\r\n      searchParams: {\r\n        sgSign: '',\r\n        sgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      },\r\n      // 搜索模式选项\r\n      searchModeOptions: [\r\n        { label: '模糊搜索', value: '模糊搜索' },\r\n        { label: '精确搜索', value: '精确搜索' }\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    // 计算小计数据\r\n    subtotalData() {\r\n      if (!this.tableData || this.tableData.length === 0) {\r\n        return [{\r\n          label: \"产品报废损失小计\",\r\n          totalTonnage: 0,\r\n          totalUnitPrice: 0,\r\n          totalAmount: 0\r\n        }];\r\n      }\r\n\r\n      const totalTonnage = this.sumData.costTon;\r\n\r\n      const totalUnitPrice = this.sumData.costPerTon;\r\n\r\n      const totalAmount = this.sumData.costEx;\r\n\r\n      return [{\r\n        label: \"产品报废损失小计\",\r\n        totalTonnage: totalTonnage,\r\n        totalUnitPrice: totalUnitPrice,\r\n        totalAmount: totalAmount\r\n      }];\r\n    },\r\n    // 计算总计数据\r\n    totalData() {\r\n       if (!this.tableData || this.tableData.length === 0) {\r\n        return [{\r\n          label: \"产品报废损失总计\",\r\n          totalTonnage: 0,\r\n          totalUnitPrice: 0,\r\n          totalAmount: 0\r\n        }];\r\n      }\r\n\r\n      const totalTonnage = this.allSumData.costTon;\r\n\r\n      const totalUnitPrice = this.allSumData.costPerTon;\r\n\r\n      const totalAmount = this.allSumData.costEx;\r\n\r\n      return [{\r\n        label: \"产品报废损失总计\",\r\n        totalTonnage: totalTonnage,\r\n        totalUnitPrice: totalUnitPrice,\r\n        totalAmount: totalAmount\r\n      }];\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听成本中心变化\r\n    costCenter: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 监听会计期变化\r\n    accountingPeriod: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 新增：监听是否计划内变化\r\n    planFlag: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCostCenterList();\r\n    this.queryParams.planFlag = this.planFlag;\r\n  },\r\n  methods: {\r\n    /** 获取默认会计期 */\r\n    getDefaultYearMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    },\r\n\r\n    // 获取成本中心列表\r\n    getCostCenterList() {\r\n      this.costCenterLoading = true;\r\n      costCenterlist().then(response => {\r\n        this.costCenterOptions = response.data || [];\r\n        // 如果有数据，设置默认选中第一个\r\n        if (this.costCenterOptions.length > 0) {\r\n          console.log('获取成本中心列表:', this.costCenterOptions);\r\n          this.costCenter = this.costCenterOptions[0].key;\r\n          // 设置默认值后，主动触发一次数据获取\r\n          this.$nextTick(() => {\r\n            this.fetchTableData();\r\n          });\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取成本中心列表失败:', error);\r\n        this.$message.error('获取成本中心列表失败');\r\n      }).finally(() => {\r\n        this.costCenterLoading = false;\r\n      });\r\n    },\r\n    // 获取表格数据\r\n    fetchTableData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod) {\r\n        this.tableData = [];\r\n        return;\r\n      }\r\n\r\n      this.tableLoading = true;\r\n\r\n      // 当选择\"江阴兴澄特种钢铁\"时，查询所有数据（不传costCenter参数）\r\n      let costCenterParam = this.costCenter;\r\n      const selectedOption = this.costCenterOptions.find(item => item.key === this.costCenter);\r\n      if (selectedOption && selectedOption.label === '公司') {\r\n        costCenterParam = ''; // 设置为空字符串，查询所有数据\r\n      }\r\n\r\n      this.queryParams.costCenter = costCenterParam;\r\n      this.queryParams.yearMonth = this.accountingPeriod.replace('-', '');\r\n      this.queryParams.planFlag = this.planFlag;\r\n      this.queryParams.sgSign = this.searchParams.sgSign;\r\n      this.queryParams.sgStd = this.searchParams.sgStd;\r\n      this.queryParams.reason = this.searchParams.reason;\r\n      this.queryParams.searchMode = this.searchParams.searchMode;\r\n\r\n      listAllScrapDetail(this.queryParams).then(response => {\r\n        //this.tableData = (response.rows || []).filter(item => item.costEx !== null && item.costEx !== undefined && item.costEx !== 0);\r\n        this.tableData = response.rows || [];\r\n        console.log('获取报废损失数据:', this.tableData);\r\n        this.total = response.total || 0;\r\n      }).catch(error => {\r\n        console.error('获取报废损失数据失败:', error);\r\n        this.$message.error('获取报废损失数据失败');\r\n        this.tableData = [];\r\n        this.total = 0;\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n\r\n      getSum(this.queryParams).then(response => {\r\n        this.sumData = response.data || [];\r\n      }).catch(error => {\r\n        this.$message.error('获取数据失败');\r\n        this.sumData = [];\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n\r\n      getAllSum(this.queryParams).then(response => {\r\n        this.allSumData = response.data || [];\r\n      }).catch(error => {\r\n        this.$message.error('获取数据失败');\r\n        this.allSumData = [];\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n    },\r\n    // 处理计划内标志值\r\n    getPlanFlagValue(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return '否';\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return '是';\r\n      }\r\n      return '未知'; // 既不是0也不是1时显示未知\r\n    },\r\n    // 获取计划内标志标签类型\r\n    getPlanFlagTagType(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return 'danger'; // 绿色\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return 'success'; // 红色\r\n      }\r\n      return 'warning'; // 黄色（未知状态）\r\n    },\r\n    // 获取当前年月\r\n    getCurrentMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = String(now.getMonth() + 1).padStart(2, '0');\r\n      return `${year}-${month}`;\r\n    },\r\n    // 格式化数字显示\r\n    formatNumber(value, decimals = 2) {\r\n      if (value === null || value === undefined) return '';\r\n      return Number(value).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: decimals,\r\n        maximumFractionDigits: decimals\r\n      });\r\n    },\r\n    subtotalSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 合并前6列为小计标签\r\n      if (columnIndex >= 0 && columnIndex <= 6) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 7\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      if (columnIndex >= 9 && columnIndex <= 10) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      // 其他列保持不变\r\n      return {\r\n        rowspan: 1,\r\n        colspan: 1\r\n      };\r\n    },\r\n    // 总计行合并方法\r\n    totalSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 合并前6列为总计标签\r\n      if (columnIndex >= 0 && columnIndex <= 6) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 7\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      if (columnIndex >= 9 && columnIndex <= 10) {\r\n        if (columnIndex === 9) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      // 其他列保持不变\r\n      return {\r\n        rowspan: 1,\r\n        colspan: 1\r\n      };\r\n    },\r\n    // 搜索按钮点击\r\n    handleSearch() {\r\n      this.queryParams.pageNum = 1;\r\n      this.fetchTableData();\r\n    },\r\n    // 重置按钮点击\r\n    handleReset() {\r\n      this.searchParams = {\r\n        sgSign: '',\r\n        sgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      };\r\n      this.queryParams.pageNum = 1;\r\n      this.fetchTableData();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.scrap-detail-container {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-title {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-title h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.table-header-info {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 24px;\r\n}\r\n\r\n.header-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.header-item .label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .value {\r\n  color: #303133;\r\n}\r\n\r\n.header-item:first-child .label {\r\n  color: #303133;\r\n  font-size: 16px;\r\n}\r\n\r\n.search-bar-row {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 24px;\r\n}\r\n\r\n.main-table {\r\n  margin-bottom: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.subtotal-section {\r\n  margin-top: -1px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n/* 表格样式定制 */\r\n.scrap-detail-table {\r\n  font-size: 14px;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__header-wrapper) {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__header th) {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: bold;\r\n  padding: 12px 0;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__body tr:nth-child(odd)) {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__body tr:hover) {\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n/* 小计表格样式 */\r\n.subtotal-table {\r\n  font-size: 14px;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header-wrapper) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body tr) {\r\n  background-color: #f0f9ff;\r\n  font-weight: bold;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body td) {\r\n  background-color: #f0f9ff !important;\r\n  padding: 12px 0;\r\n}\r\n\r\n/* 总计表格样式 */\r\n.total-section {\r\n  margin-top: -1px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.total-table {\r\n  font-size: 14px;\r\n}\r\n\r\n.total-table :deep(.el-table__header) {\r\n  display: none !important;\r\n}\r\n\r\n.total-table :deep(.el-table__header-wrapper) {\r\n  display: none !important;\r\n}\r\n\r\n.total-table :deep(.el-table__body tr) {\r\n  background-color: #ff4d4f;\r\n  font-weight: bold;\r\n  color: white;\r\n}\r\n\r\n.total-table :deep(.el-table__body td) {\r\n  background-color: #ff4d4f !important;\r\n  padding: 12px 0;\r\n  color: white;\r\n}\r\n\r\n/* 标签样式 */\r\n.el-tag {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .table-header-info {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .search-bar-row {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .scrap-detail-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .scrap-detail-table :deep(.el-table__body td) {\r\n    padding: 8px 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .scrap-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .main-table {\r\n    overflow-x: auto;\r\n  }\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.header-item .el-input {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button:last-child {\r\n  margin-right: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AA6LA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA;MACAC,UAAA;MACAC,gBAAA,OAAAC,mBAAA;MACA;MACAC,QAAA;MAAA;MACAC,eAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACA;MACAC,SAAA;MACA;MACAC,YAAA;MAEA;MACAC,iBAAA;MACAC,iBAAA;MACAC,KAAA;MACAC,OAAA;MACAC,UAAA;MACA;MACAC,YAAA;QACAC,MAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACA;MACAC,iBAAA,GACA;QAAAd,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAc,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,UAAAd,SAAA,SAAAA,SAAA,CAAAe,MAAA;QACA;UACAjB,KAAA;UACAkB,YAAA;UACAC,cAAA;UACAC,WAAA;QACA;MACA;MAEA,IAAAF,YAAA,QAAAX,OAAA,CAAAc,OAAA;MAEA,IAAAF,cAAA,QAAAZ,OAAA,CAAAe,UAAA;MAEA,IAAAF,WAAA,QAAAb,OAAA,CAAAgB,MAAA;MAEA;QACAvB,KAAA;QACAkB,YAAA,EAAAA,YAAA;QACAC,cAAA,EAAAA,cAAA;QACAC,WAAA,EAAAA;MACA;IACA;IACA;IACAI,SAAA,WAAAA,UAAA;MACA,UAAAtB,SAAA,SAAAA,SAAA,CAAAe,MAAA;QACA;UACAjB,KAAA;UACAkB,YAAA;UACAC,cAAA;UACAC,WAAA;QACA;MACA;MAEA,IAAAF,YAAA,QAAAV,UAAA,CAAAa,OAAA;MAEA,IAAAF,cAAA,QAAAX,UAAA,CAAAc,UAAA;MAEA,IAAAF,WAAA,QAAAZ,UAAA,CAAAe,MAAA;MAEA;QACAvB,KAAA;QACAkB,YAAA,EAAAA,YAAA;QACAC,cAAA,EAAAA,cAAA;QACAC,WAAA,EAAAA;MACA;IACA;EACA;EACAK,KAAA;IACA;IACA9B,UAAA;MACA+B,OAAA,WAAAA,QAAA;QACA,KAAAlC,WAAA,CAAAC,OAAA;QACA,KAAAkC,cAAA;MACA;IACA;IACA;IACA/B,gBAAA;MACA8B,OAAA,WAAAA,QAAA;QACA,KAAAlC,WAAA,CAAAC,OAAA;QACA,KAAAkC,cAAA;MACA;IACA;IACA;IACA7B,QAAA;MACA4B,OAAA,WAAAA,QAAA;QACA,KAAAlC,WAAA,CAAAC,OAAA;QACA,KAAAkC,cAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,iBAAA;IACA,KAAArC,WAAA,CAAAM,QAAA,QAAAA,QAAA;EACA;EACAgC,OAAA;IACA,cACAjC,mBAAA,WAAAA,oBAAA;MACA,IAAAkC,GAAA,OAAAC,IAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAJ,GAAA,CAAAK,QAAA;MACA,IAAAC,GAAA,GAAAN,GAAA,CAAAO,OAAA;MACA,IAAAC,IAAA,GAAAR,GAAA,CAAAS,QAAA;;MAEA;MACA,IAAAH,GAAA,SAAAA,GAAA,WAAAE,IAAA;QACA;QACA,IAAAE,SAAA,GAAAN,KAAA,cAAAA,KAAA;QACA,IAAAO,QAAA,GAAAP,KAAA,SAAAF,IAAA,OAAAA,IAAA;QACA,UAAAU,MAAA,CAAAD,QAAA,OAAAC,MAAA,CAAAC,MAAA,CAAAH,SAAA,EAAAI,QAAA;MACA;QACA,UAAAF,MAAA,CAAAV,IAAA,OAAAU,MAAA,CAAAC,MAAA,CAAAT,KAAA,EAAAU,QAAA;MACA;IACA;IAEA;IACAhB,iBAAA,WAAAA,kBAAA;MAAA,IAAAiB,KAAA;MACA,KAAAzC,iBAAA;MACA,IAAA0C,iCAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA1C,iBAAA,GAAA6C,QAAA,CAAA1D,IAAA;QACA;QACA,IAAAuD,KAAA,CAAA1C,iBAAA,CAAAa,MAAA;UACAiC,OAAA,CAAAC,GAAA,cAAAL,KAAA,CAAA1C,iBAAA;UACA0C,KAAA,CAAAnD,UAAA,GAAAmD,KAAA,CAAA1C,iBAAA,IAAAgD,GAAA;UACA;UACAN,KAAA,CAAAO,SAAA;YACAP,KAAA,CAAAnB,cAAA;UACA;QACA;MACA,GAAA2B,KAAA,WAAAC,KAAA;QACAL,OAAA,CAAAK,KAAA,gBAAAA,KAAA;QACAT,KAAA,CAAAU,QAAA,CAAAD,KAAA;MACA,GAAAE,OAAA;QACAX,KAAA,CAAAzC,iBAAA;MACA;IACA;IACA;IACAsB,cAAA,WAAAA,eAAA;MAAA,IAAA+B,MAAA;MACA;MACA,UAAA/D,UAAA,UAAAC,gBAAA;QACA,KAAAM,SAAA;QACA;MACA;MAEA,KAAAC,YAAA;;MAEA;MACA,IAAAwD,eAAA,QAAAhE,UAAA;MACA,IAAAiE,cAAA,QAAAxD,iBAAA,CAAAyD,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAV,GAAA,KAAAM,MAAA,CAAA/D,UAAA;MAAA;MACA,IAAAiE,cAAA,IAAAA,cAAA,CAAA5D,KAAA;QACA2D,eAAA;MACA;MAEA,KAAAnE,WAAA,CAAAG,UAAA,GAAAgE,eAAA;MACA,KAAAnE,WAAA,CAAAuE,SAAA,QAAAnE,gBAAA,CAAAoE,OAAA;MACA,KAAAxE,WAAA,CAAAM,QAAA,QAAAA,QAAA;MACA,KAAAN,WAAA,CAAAkB,MAAA,QAAAD,YAAA,CAAAC,MAAA;MACA,KAAAlB,WAAA,CAAAmB,KAAA,QAAAF,YAAA,CAAAE,KAAA;MACA,KAAAnB,WAAA,CAAAoB,MAAA,QAAAH,YAAA,CAAAG,MAAA;MACA,KAAApB,WAAA,CAAAqB,UAAA,QAAAJ,YAAA,CAAAI,UAAA;MAEA,IAAAoD,+BAAA,OAAAzE,WAAA,EAAAwD,IAAA,WAAAC,QAAA;QACA;QACAS,MAAA,CAAAxD,SAAA,GAAA+C,QAAA,CAAAiB,IAAA;QACAhB,OAAA,CAAAC,GAAA,cAAAO,MAAA,CAAAxD,SAAA;QACAwD,MAAA,CAAApD,KAAA,GAAA2C,QAAA,CAAA3C,KAAA;MACA,GAAAgD,KAAA,WAAAC,KAAA;QACAL,OAAA,CAAAK,KAAA,gBAAAA,KAAA;QACAG,MAAA,CAAAF,QAAA,CAAAD,KAAA;QACAG,MAAA,CAAAxD,SAAA;QACAwD,MAAA,CAAApD,KAAA;MACA,GAAAmD,OAAA;QACAC,MAAA,CAAAvD,YAAA;MACA;MAEA,IAAAgE,mBAAA,OAAA3E,WAAA,EAAAwD,IAAA,WAAAC,QAAA;QACAS,MAAA,CAAAnD,OAAA,GAAA0C,QAAA,CAAA1D,IAAA;MACA,GAAA+D,KAAA,WAAAC,KAAA;QACAG,MAAA,CAAAF,QAAA,CAAAD,KAAA;QACAG,MAAA,CAAAnD,OAAA;MACA,GAAAkD,OAAA;QACAC,MAAA,CAAAvD,YAAA;MACA;MAEA,IAAAiE,sBAAA,OAAA5E,WAAA,EAAAwD,IAAA,WAAAC,QAAA;QACAS,MAAA,CAAAlD,UAAA,GAAAyC,QAAA,CAAA1D,IAAA;MACA,GAAA+D,KAAA,WAAAC,KAAA;QACAG,MAAA,CAAAF,QAAA,CAAAD,KAAA;QACAG,MAAA,CAAAlD,UAAA;MACA,GAAAiD,OAAA;QACAC,MAAA,CAAAvD,YAAA;MACA;IACA;IACA;IACAkE,gBAAA,WAAAA,iBAAAvE,QAAA;MACA,IAAAA,QAAA,YAAAA,QAAA;QACA;MACA,WAAAA,QAAA,YAAAA,QAAA;QACA;MACA;MACA;IACA;IACA;IACAwE,kBAAA,WAAAA,mBAAAxE,QAAA;MACA,IAAAA,QAAA,YAAAA,QAAA;QACA;MACA,WAAAA,QAAA,YAAAA,QAAA;QACA;MACA;MACA;IACA;IACA;IACAyE,eAAA,WAAAA,gBAAA;MACA,IAAAxC,GAAA,OAAAC,IAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAS,MAAA,CAAAb,GAAA,CAAAK,QAAA,QAAAS,QAAA;MACA,UAAAF,MAAA,CAAAV,IAAA,OAAAU,MAAA,CAAAR,KAAA;IACA;IACA;IACAqC,YAAA,WAAAA,aAAAvE,KAAA;MAAA,IAAAwE,QAAA,GAAAC,SAAA,CAAAzD,MAAA,QAAAyD,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAzE,KAAA,aAAAA,KAAA,KAAA0E,SAAA;MACA,OAAAC,MAAA,CAAA3E,KAAA,EAAA4E,cAAA;QACAC,qBAAA,EAAAL,QAAA;QACAM,qBAAA,EAAAN;MACA;IACA;IACAO,kBAAA,WAAAA,mBAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAC,MAAA,GAAAF,IAAA,CAAAE,MAAA;QAAAC,QAAA,GAAAH,IAAA,CAAAG,QAAA;QAAAC,WAAA,GAAAJ,IAAA,CAAAI,WAAA;MACA;MACA,IAAAA,WAAA,SAAAA,WAAA;QACA,IAAAA,WAAA;UACA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;UACA;YACAD,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA,IAAAF,WAAA,SAAAA,WAAA;QACA,IAAAA,WAAA;UACA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;UACA;YACAD,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA;MACA;QACAD,OAAA;QACAC,OAAA;MACA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAP,GAAA,GAAAO,KAAA,CAAAP,GAAA;QAAAC,MAAA,GAAAM,KAAA,CAAAN,MAAA;QAAAC,QAAA,GAAAK,KAAA,CAAAL,QAAA;QAAAC,WAAA,GAAAI,KAAA,CAAAJ,WAAA;MACA;MACA,IAAAA,WAAA,SAAAA,WAAA;QACA,IAAAA,WAAA;UACA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;UACA;YACAD,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA,IAAAF,WAAA,SAAAA,WAAA;QACA,IAAAA,WAAA;UACA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;UACA;YACAD,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA;MACA;QACAD,OAAA;QACAC,OAAA;MACA;IACA;IACA;IACAG,YAAA,WAAAA,aAAA;MACA,KAAAlG,WAAA,CAAAC,OAAA;MACA,KAAAkC,cAAA;IACA;IACA;IACAgE,WAAA,WAAAA,YAAA;MACA,KAAAlF,YAAA;QACAC,MAAA;QACAC,KAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACA,KAAArB,WAAA,CAAAC,OAAA;MACA,KAAAkC,cAAA;IACA;EACA;AACA", "ignoreList": []}]}