{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\organizationCheck.vue?vue&type=template&id=a4477ba8&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\organizationCheck.vue", "mtime": 1756456282471}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}