package com.ruoyi.web.controller.supply;

import com.ruoyi.app.supply.domain.*;
import com.ruoyi.app.supply.service.ISupplyInfoService;
import com.ruoyi.app.supply.service.ISupplyUserInfoService;
import com.ruoyi.app.supply.service.ISupplyUserFacService;
import com.ruoyi.app.supply.service.ISupplyUserHealthService;
import com.ruoyi.app.supply.service.ISupplyUserFileService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 供应商专用Controller
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/web/supply/supplier")
public class SupplierController extends BaseController {
    
    @Autowired
    private ISupplyInfoService supplyInfoService;
    
    @Autowired
    private ISupplyUserInfoService supplyUserInfoService;
    
    @Autowired
    private ISupplyUserFacService supplyUserFacService;
    
    @Autowired
    private ISupplyUserHealthService supplyUserHealthService;
    
    @Autowired
    private ISupplyUserFileService supplyUserFileService;

    /**
     * 获取当前登录用户的供应商编号（用户编号前7位）
     */
    private String getCurrentSupplierCode() {
        String username = SecurityUtils.getUsername();
        return username.length() >= 7 ? username.substring(0, 7) : username;
    }

    /**
     * 查询供应商人员列表（只能查看自己的）
     */
    @GetMapping("/list")
    public TableDataInfo list(SupplyUserInfo supplyUserInfo) {
        startPage();
        // 设置供应商代码为当前用户的前7位
        supplyUserInfo.setSupplyCode(getCurrentSupplierCode());
        List<SupplyUserInfo> list = supplyUserInfoService.selectSupplyUserInfoList(supplyUserInfo);
        return getDataTable(list);
    }

    /**
     * 获取供应商信息详细
     */
    @GetMapping(value = "/info/{supplyCode}")
    public AjaxResult getSupplyInfo(@PathVariable("supplyCode") String supplyCode) {
        // 验证是否为当前用户的供应商代码
        if (!getCurrentSupplierCode().equals(supplyCode)) {
            return AjaxResult.error("无权限访问该供应商信息");
        }
        // 调用SupplyInfoController中的方法查询供应商信息
        return AjaxResult.success(supplyInfoService.selectSupplyInfoByCode(Integer.parseInt(supplyCode)));
    }

    /**
     * 修改供应商信息
     */
    @Log(title = "供应商信息", businessType = BusinessType.UPDATE)
    @PutMapping("/info")
    public AjaxResult updateSupplyInfo(@RequestBody SupplyInfo supplyInfo) {
        // 验证是否为当前用户的供应商代码
        if (!getCurrentSupplierCode().equals(String.valueOf(supplyInfo.getSupplyCode()))) {
            return AjaxResult.error("无权限修改该供应商信息");
        }
        return toAjax(supplyInfoService.updateSupplyInfo(supplyInfo));
    }

    /**
     * 获取供应商人员详细信息
     */
    @GetMapping(value = "/user/{id}")
    public AjaxResult getUserInfo(@PathVariable("id") Integer id) {
        SupplyUserInfo userInfo = supplyUserInfoService.selectSupplyUserInfoById(id);
        // 验证是否为当前用户的供应商人员
        if (userInfo == null || !getCurrentSupplierCode().equals(userInfo.getSupplyCode())) {
            return AjaxResult.error("无权限访问该人员信息");
        }
        return AjaxResult.success(userInfo);
    }

    /**
     * 新增供应商人员
     */
    @Log(title = "供应商人员", businessType = BusinessType.INSERT)
    @PostMapping("/user")
    public AjaxResult addUserInfo(@RequestBody SupplyUserInfo supplyUserInfo) {
        // 设置供应商代码为当前用户的前7位
        supplyUserInfo.setSupplyCode(getCurrentSupplierCode());
        return toAjax(supplyUserInfoService.insertSupplyUserInfo(supplyUserInfo));
    }

    /**
     * 修改供应商人员
     */
    @Log(title = "供应商人员", businessType = BusinessType.UPDATE)
    @PutMapping("/user")
    public AjaxResult updateUserInfo(@RequestBody SupplyUserInfo supplyUserInfo) {
        // 验证是否为当前用户的供应商人员
        SupplyUserInfo existingUser = supplyUserInfoService.selectSupplyUserInfoById(supplyUserInfo.getId());
        if (existingUser == null || !getCurrentSupplierCode().equals(existingUser.getSupplyCode())) {
            return AjaxResult.error("无权限修改该人员信息");
        }
        // 确保供应商代码不被修改
        supplyUserInfo.setSupplyCode(getCurrentSupplierCode());
        return toAjax(supplyUserInfoService.updateSupplyUserInfo(supplyUserInfo));
    }

    /**
     * 删除供应商人员
     */
    @Log(title = "供应商人员", businessType = BusinessType.DELETE)
    @DeleteMapping("/user/{id}")
    public AjaxResult removeUserInfo(@PathVariable Integer id) {
        SupplyUserInfo userInfo = supplyUserInfoService.selectSupplyUserInfoById(id);
        // 验证是否为当前用户的供应商人员
        if (userInfo == null || !getCurrentSupplierCode().equals(userInfo.getSupplyCode())) {
            return AjaxResult.error("无权限删除该人员信息");
        }
        return toAjax(supplyUserInfoService.deleteSupplyUserInfoById(id));
    }

    /**
     * 导出供应商人员列表
     */
    @Log(title = "供应商人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(HttpServletResponse response, SupplyUserInfo supplyUserInfo) {
        // 设置供应商代码为当前用户的前7位
        supplyUserInfo.setSupplyCode(getCurrentSupplierCode());
        List<SupplyUserInfo> list = supplyUserInfoService.selectSupplyUserInfoList(supplyUserInfo);
        ExcelUtil<SupplyUserInfo> util = new ExcelUtil<SupplyUserInfo>(SupplyUserInfo.class);
        return util.exportExcel(list, "供应商人员数据");
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<SupplyUserImportDto> util = new ExcelUtil<>(SupplyUserImportDto.class);
        return util.importTemplateExcel("供应商用户信息导入模板");
    }
    

    /**
     * 获取岗位识别卡信息
     */
    @GetMapping("/fac/{userId}")
    public AjaxResult getFac(@PathVariable Integer userId) {
        // 验证用户权限
        SupplyUserInfo userInfo = supplyUserInfoService.selectSupplyUserInfoById(userId);
        if (userInfo == null || !getCurrentSupplierCode().equals(userInfo.getSupplyCode())) {
            return AjaxResult.error("无权限访问该人员信息");
        }
        return AjaxResult.success(supplyUserFacService.selectSupplyUserFacById(userId));
    }

    /**
     * 新增岗位识别卡
     */
    @Log(title = "岗位识别卡", businessType = BusinessType.INSERT)
    @PostMapping("/fac")
    public AjaxResult addFac(@RequestBody SupplyUserFac supplyUserFac) {
        // 验证用户权限
        SupplyUserInfo userInfo = supplyUserInfoService.selectSupplyUserInfoById(supplyUserFac.getUserId());
        if (userInfo == null || !getCurrentSupplierCode().equals(userInfo.getSupplyCode())) {
            return AjaxResult.error("无权限操作该人员信息");
        }
        return toAjax(supplyUserFacService.insertSupplyUserFac(supplyUserFac));
    }

    /**
     * 修改岗位识别卡
     */
    @Log(title = "岗位识别卡", businessType = BusinessType.UPDATE)
    @PutMapping("/fac")
    public AjaxResult updateFac(@RequestBody SupplyUserFac supplyUserFac) {
        // 验证用户权限
        SupplyUserInfo userInfo = supplyUserInfoService.selectSupplyUserInfoById(supplyUserFac.getUserId());
        if (userInfo == null || !getCurrentSupplierCode().equals(userInfo.getSupplyCode())) {
            return AjaxResult.error("无权限操作该人员信息");
        }
        return toAjax(supplyUserFacService.updateSupplyUserFac(supplyUserFac));
    }

    /**
     * 获取健康信息
     */
    @GetMapping("/health/{userId}")
    public AjaxResult getHealth(@PathVariable Integer userId) {
        // 验证用户权限
        SupplyUserInfo userInfo = supplyUserInfoService.selectSupplyUserInfoById(userId);
        if (userInfo == null || !getCurrentSupplierCode().equals(userInfo.getSupplyCode())) {
            return AjaxResult.error("无权限访问该人员信息");
        }
        return AjaxResult.success(supplyUserHealthService.selectSupplyUserHealthById(userId));
    }

    /**
     * 新增健康信息
     */
    @Log(title = "健康信息", businessType = BusinessType.INSERT)
    @PostMapping("/health")
    public AjaxResult addHealth(@RequestBody SupplyUserHealth supplyUserHealth) {
        // 验证用户权限
        SupplyUserInfo userInfo = supplyUserInfoService.selectSupplyUserInfoById(supplyUserHealth.getUserid());
        if (userInfo == null || !getCurrentSupplierCode().equals(userInfo.getSupplyCode())) {
            return AjaxResult.error("无权限操作该人员信息");
        }
        return toAjax(supplyUserHealthService.insertSupplyUserHealth(supplyUserHealth));
    }

    /**
     * 修改健康信息
     */
    @Log(title = "健康信息", businessType = BusinessType.UPDATE)
    @PutMapping("/health")
    public AjaxResult updateHealth(@RequestBody SupplyUserHealth supplyUserHealth) {
        // 验证用户权限
        SupplyUserInfo userInfo = supplyUserInfoService.selectSupplyUserInfoById(supplyUserHealth.getUserid());
        if (userInfo == null || !getCurrentSupplierCode().equals(userInfo.getSupplyCode())) {
            return AjaxResult.error("无权限操作该人员信息");
        }
        return toAjax(supplyUserHealthService.updateSupplyUserHealth(supplyUserHealth));
    }

    /**
     * 查询文件列表
     */
    @GetMapping("/file/list/{userId}")
    public TableDataInfo listFile(@PathVariable Integer userId) {
        // 验证用户权限
        SupplyUserInfo userInfo = supplyUserInfoService.selectSupplyUserInfoById(userId);
        if (userInfo == null || !getCurrentSupplierCode().equals(userInfo.getSupplyCode())) {
            return getDataTable(null);
        }
        startPage();
        SupplyUserFile param = new SupplyUserFile();
        param.setUserid(userId);
        List<SupplyUserFile> list = supplyUserFileService.selectSupplyUserFileList(param);
        return getDataTable(list);
    }

    /**
     * 删除文件
     */
    @Log(title = "供应商文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/file/{id}")
    public AjaxResult removeFile(@PathVariable Integer id) {
        SupplyUserFile file = supplyUserFileService.selectSupplyUserFileById(id);
        if (file != null) {
            // 验证用户权限
            SupplyUserInfo userInfo = supplyUserInfoService.selectSupplyUserInfoById(file.getUserid());
            if (userInfo == null || !getCurrentSupplierCode().equals(userInfo.getSupplyCode())) {
                return AjaxResult.error("无权限删除该文件");
            }
        }
        return toAjax(supplyUserFileService.deleteSupplyUserFileById(id));
    }

}
