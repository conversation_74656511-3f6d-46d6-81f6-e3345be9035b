{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\list.vue", "mtime": 1756456282511}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "list.vue", "sourceRoot": "src/views/assess/self/config/user", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.assessDate\"\r\n              type=\"month\"\r\n              value-format=\"yyyy-M\"\r\n              format=\"yyyy 年 M 月\"\r\n              placeholder=\"选择考核年月\"\r\n              :clearable=\"false\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"工号\" prop=\"workNo\">\r\n            <el-input\r\n              v-model=\"queryParams.workNo\"\r\n              placeholder=\"请输入工号\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入姓名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <treeselect style=\"width: 200px;\" v-model=\"queryParams.deptId\" :multiple=\"false\" :options=\"deptOptions\" :normalizer=\"normalizer\" :disable-branch-nodes=\"true\" placeholder=\"请选择部门\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n  \r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n  \r\n      <el-table v-loading=\"loading\" :data=\"selfAssessUserList\">\r\n        <!-- <el-table-column label=\"编号\" align=\"center\" prop=\"id\" /> -->\r\n        <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n        <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n        <!-- <el-table-column label=\"身份\" align=\"center\" prop=\"assessRole\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ dicts.self_assess_role[scope.row.assessRole][\"label\"] }}\r\n          </template>\r\n        </el-table-column> -->\r\n        <el-table-column label=\"部门\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-for=\"item, index in scope.row.deptList\" v-bind:key=\"index\">\r\n              {{ scope.row.deptList.length > 1 && index + 1 != scope.row.deptList.length ? item.deptName + \", \" : item.deptName}}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"填写状态\" align=\"center\" prop=\"assessStatus\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.assessStatus === '0'\" type=\"primary\">已保存</el-tag>\r\n            <el-tag v-else-if=\"scope.row.assessStatus === '1'\" type=\"warning\">部门评分</el-tag>\r\n            <el-tag v-else-if=\"scope.row.assessStatus === '2'\" type=\"warning\">事业部评分</el-tag>\r\n            <el-tag v-else-if=\"scope.row.assessStatus === '3'\" type=\"warning\">运改/组织部评分</el-tag>\r\n            <el-tag v-else-if=\"scope.row.assessStatus === '4' || scope.row.assessStatus === '5'\" type=\"success\">已完成</el-tag>\r\n            <el-tag v-else type=\"info\">未填写</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleConfig(scope.row)\"\r\n            >指标配置</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleReport(scope.row)\"\r\n            >自评填写</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n  \r\n    </div>\r\n  </template>\r\n  \r\n  <script>\r\n  import { getToken } from \"@/utils/auth\";\r\n  import { listAvailable, listAvailableWithStatus } from \"@/api/assess/self/user\";\r\n  import { listDept } from \"@/api/assess/lateral/dept\";\r\n  import Treeselect from \"@riophae/vue-treeselect\";\r\n  import \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n  \r\n  export default {\r\n    name: \"SelfAssessUserList\",\r\n    components: {\r\n      Treeselect\r\n    },\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 绩效考核-干部自评人员配置表格数据\r\n        selfAssessUserList: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          assessDate: null,\r\n          workNo: null,\r\n          name: null,\r\n          assessRole: null,\r\n          benefitLinkFlag: null,\r\n          averageLinkFlag: null\r\n        },\r\n        // 表单参数\r\n        form: {},\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        // 字典\r\n        dicts:{\r\n          self_assess_role:[],\r\n          sys_yes_no:[]\r\n        },\r\n        // 部门下拉树\r\n        deptOptions:[],\r\n        // 导入参数\r\n        upload: {\r\n          // 是否禁用上传\r\n          isUploading: false,\r\n          // 设置上传的请求头部\r\n          headers: { Authorization: 'Bearer ' + getToken() },\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/web/selfAssessUser/importInfo\",\r\n        },\r\n        // 导入结果\r\n        importRes:[],\r\n        openImportRes:false\r\n      };\r\n    },\r\n    created() {\r\n      this.initPageData();\r\n    },\r\n\r\n    // 监听路由变化，确保每次进入页面都重新获取数据\r\n    watch: {\r\n      '$route'(to) {\r\n        // 当路由发生变化时，重新初始化页面数据\r\n        if (to.path === '/assess/self/user/list') {\r\n          this.initPageData();\r\n        }\r\n      }\r\n    },\r\n\r\n    // 路由更新时的钩子\r\n    beforeRouteUpdate(to, from, next) {\r\n      // 在当前路由改变，但是该组件被复用时调用\r\n      this.initPageData();\r\n      next();\r\n    },\r\n    methods: {\r\n      // 初始化页面数据\r\n      initPageData() {\r\n        // 设置默认考核年月为当前年月\r\n        const now = new Date();\r\n        this.queryParams.assessDate = `${now.getFullYear()}-${now.getMonth() + 1}`;\r\n\r\n        // 重置数据\r\n        this.selfAssessUserList = [];\r\n        this.total = 0;\r\n\r\n        // 获取数据\r\n        this.getList();\r\n        this.getTreeselect();\r\n        this.getDicts(\"self_assess_role\").then(response => {\r\n          this.dicts.self_assess_role = this.formatterDict(response.data);\r\n        });\r\n        this.getDicts(\"sys_yes_no\").then(response => {\r\n          this.dicts.sys_yes_no = this.formatterDict(response.data);\r\n        });\r\n      },\r\n\r\n      formatterDict(dict){\r\n        let result = []\r\n        dict.forEach(dict => {\r\n          result.push({\r\n            label:dict.dictLabel,\r\n            value:dict.dictValue\r\n          })\r\n        });\r\n        return result;\r\n      },\r\n      /** 查询绩效考核-干部自评人员配置列表 */\r\n      getList() {\r\n        this.loading = true;\r\n\r\n        // 如果有考核年月，使用批量获取状态的接口\r\n        if (this.queryParams.assessDate) {\r\n          listAvailableWithStatus(this.queryParams).then(response => {\r\n            this.selfAssessUserList = response.data;\r\n            this.loading = false;\r\n          }).catch(error => {\r\n            console.error('获取用户列表失败:', error);\r\n            this.$message.error('获取用户列表失败');\r\n            this.loading = false;\r\n          });\r\n        } else {\r\n          // 没有考核年月时使用原接口\r\n          listAvailable(this.queryParams).then(response => {\r\n            this.selfAssessUserList = response.data;\r\n            this.loading = false;\r\n          }).catch(error => {\r\n            console.error('获取用户列表失败:', error);\r\n            this.$message.error('获取用户列表失败');\r\n            this.loading = false;\r\n          });\r\n        }\r\n      },\r\n\r\n\r\n      // 取消按钮\r\n      cancel() {\r\n        this.open = false;\r\n        this.reset();\r\n      },\r\n      // 表单重置\r\n      reset() {\r\n        this.form = {\r\n          id: null,\r\n          workNo: null,\r\n          name: null,\r\n          assessRole: null,\r\n          benefitLinkFlag: null,\r\n          averageLinkFlag: null\r\n        };\r\n        this.resetForm(\"form\");\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        // 重置后重新设置默认考核年月\r\n        const now = new Date();\r\n        this.queryParams.assessDate = `${now.getFullYear()}-${now.getMonth() + 1}`;\r\n        this.handleQuery();\r\n      },\r\n  \r\n      /** 转换横向评价部门数据结构 */\r\n      normalizer(node) {\r\n        if (node.children && !node.children.length) {\r\n          delete node.children;\r\n        }\r\n        return {\r\n          id: node.deptId,\r\n          label: node.deptName,\r\n          children: node.children\r\n        };\r\n      },\r\n        /** 查询横向评价部门下拉树结构 */\r\n      getTreeselect() {\r\n        listDept().then(response => {\r\n          this.deptOptions = this.handleTree(response.data, \"deptId\", \"parentId\");\r\n        });\r\n      },\r\n  \r\n      /** 配置点击事件 */\r\n      handleConfig(row){\r\n        this.$router.push({\r\n          path:\"/assess/self/user/detail\",\r\n          query:{\r\n            userId:row.id\r\n          }\r\n        })\r\n      },\r\n      /** 自评填写点击事件 */\r\n      handleReport(row){\r\n        this.$router.push({\r\n          path:\"/assess/self/user/report\",\r\n          query:{\r\n            workNo:row.workNo,\r\n            deptId:row.deptList[0].deptId\r\n          }\r\n        })\r\n      }\r\n      \r\n    }\r\n  };\r\n  </script>\r\n  <style>\r\n  .redtext{\r\n    color: red;\r\n  }\r\n  </style>\r\n  "]}]}