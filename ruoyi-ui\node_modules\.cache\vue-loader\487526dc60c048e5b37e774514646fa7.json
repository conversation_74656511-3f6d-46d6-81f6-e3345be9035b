{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPlan\\index.vue?vue&type=style&index=0&id=066229b5&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPlan\\index.vue", "mtime": 1756456493847}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCiogew0KICBtYXJnaW46IDA7DQogIHBhZGRpbmc6IDA7DQogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7DQogIGZvbnQtZmFtaWx5OiAiTWljcm9zb2Z0IFlhSGVpIiwgc2Fucy1zZXJpZjsNCn0NCg0KLmRhc2hib2FyZC1jb250YWluZXIgew0KICB3aWR0aDogMTAwJTsNCiAgbWluLWhlaWdodDogMTAwdmg7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxOTE5NzAsICM0QjAwODIsICM4MDAwODApOw0KICBjb2xvcjogI2ZmZjsNCiAgb3ZlcmZsb3cteDogaGlkZGVuOw0KICBwYWRkaW5nOiAxMHB4Ow0KfQ0KDQouZGFzaGJvYXJkLWhlYWRlciB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBwYWRkaW5nOiA1cHggMDsNCn0NCg0KLmRhc2hib2FyZC1oZWFkZXIgaDEgew0KICBmb250LXNpemU6IDI0cHg7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICBwYWRkaW5nOiA1cHggNDBweDsNCn0NCg0KLmRhc2hib2FyZC1oZWFkZXI6OmJlZm9yZSwNCi5kYXNoYm9hcmQtaGVhZGVyOjphZnRlciB7DQogIGNvbnRlbnQ6ICIiOw0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogNTAlOw0KICB3aWR0aDogMzAlOw0KICBoZWlnaHQ6IDJweDsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCByZ2JhKDAsMjEyLDI1NSwwKSAwJSwgcmdiYSgwLDIxMiwyNTUsMSkgNTAlLCByZ2JhKDAsMjEyLDI1NSwwKSAxMDAlKTsNCn0NCg0KLmRhc2hib2FyZC1oZWFkZXI6OmJlZm9yZSB7DQogIGxlZnQ6IDA7DQp9DQoNCi5kYXNoYm9hcmQtaGVhZGVyOjphZnRlciB7DQogIHJpZ2h0OiAwOw0KfQ0KDQouZGFzaGJvYXJkLWdyaWQgew0KICBkaXNwbGF5OiBncmlkOw0KICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgxMiwgMWZyKTsNCiAgZ3JpZC10ZW1wbGF0ZS1yb3dzOiBhdXRvIGF1dG8gYXV0byBhdXRvIGF1dG8gYXV0bzsNCiAgZ2FwOiAxMHB4Ow0KICBtaW4taGVpZ2h0OiBjYWxjKDEwMHZoIC0gODBweCk7DQp9DQoNCi5jYXJkIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgzMywgMTAsIDU2LCAwLjcpOw0KICBib3JkZXItcmFkaXVzOiA1cHg7DQogIHBhZGRpbmc6IDEwcHg7DQogIGJveC1zaGFkb3c6IDAgMCAxMHB4IHJnYmEoMCwgMCwgMCwgMC4zKTsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBtaW4taGVpZ2h0OiAzMDBweDsNCn0NCg0KLmNhcmQ6OmJlZm9yZSB7DQogIGNvbnRlbnQ6ICcnOw0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogMDsNCiAgbGVmdDogMDsNCiAgcmlnaHQ6IDA7DQogIGhlaWdodDogMnB4Ow0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHJnYmEoMCwyMTIsMjU1LDApIDAlLCByZ2JhKDAsMjEyLDI1NSwxKSA1MCUsIHJnYmEoMCwyMTIsMjU1LDApIDEwMCUpOw0KfQ0KDQouY2FyZC10aXRsZSB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbWFyZ2luLWJvdHRvbTogNXB4Ow0KICBmb250LXdlaWdodDogbm9ybWFsOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5jaGFydC1maWx0ZXItZHJvcGRvd24tY29udGFpbmVyIHsNCiAgei1pbmRleDogMTA7DQp9DQoNCi5jaGFydC1maWx0ZXItZHJvcGRvd24tY29udGFpbmVyIHNlbGVjdCB7DQogIHBhZGRpbmc6IDRweCA4cHg7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgxMzgsIDQzLCAyMjYsIDAuNyk7DQogIGNvbG9yOiAjZmZmOw0KICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjMpOw0KICBmb250LXNpemU6IDEycHg7DQp9DQoNCi5jaGFydCB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDIwcHgpOw0KICBtaW4taGVpZ2h0OiAyMDBweDsNCiAgZmxleDogMTsNCn0NCg0KLnN0YXQtY2FyZHMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDsNCiAgaGVpZ2h0OiAxMDAlOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQouc3RhdC1jYXJkIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBmbGV4LWdyb3c6IDE7DQp9DQoNCi5zdGF0LXZhbHVlIHsNCiAgZm9udC1zaXplOiAzNHB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgY29sb3I6ICMwMGZmZmY7DQogIG1hcmdpbi1ib3R0b206IDVweDsNCn0NCg0KLnN0YXQtbGFiZWwgew0KICBmb250LXNpemU6IDE4cHg7DQogIG9wYWNpdHk6IDAuODsNCn0NCg0KLmNoYXJ0LXBsYWNlaG9sZGVyIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGNvbG9yOiByZ2JhKDI1NSwyNTUsMjU1LDAuNSk7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLm1hdGVyaWFsLWNoYXJ0LWNhcmQgew0KICBoZWlnaHQ6IGF1dG87DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIG1pbi1oZWlnaHQ6IDMwMHB4Ow0KfQ0KDQoubWF0ZXJpYWwtY2hhcnQtY2FyZCAuY2hhcnQgew0KICBmbGV4LWdyb3c6IDE7DQogIGhlaWdodDogMjUwcHg7DQogIG1pbi1oZWlnaHQ6IDI1MHB4Ow0KfQ0KDQoudGltZS1maWx0ZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDEwcHg7DQp9DQoNCi50aW1lLWZpbHRlci1idG4gew0KICBwYWRkaW5nOiA2cHggMTJweDsNCiAgYm9yZGVyOiBub25lOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDMzLCAxMCwgNTYsIDAuNyk7DQogIGNvbG9yOiAjZWVlOw0KICBib3JkZXItcmFkaXVzOiAyMHB4Ow0KICBmb250LXNpemU6IDEycHg7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyMTIsIDI1NSwgMC4yKTsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KfQ0KDQoudGltZS1maWx0ZXItYnRuOmhvdmVyIHsNCiAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTsNCiAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSgwLCAyMTIsIDI1NSwgMC4zKTsNCn0NCg0KLnRpbWUtZmlsdGVyLWJ0bi5hY3RpdmUgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjIpOw0KICBib3JkZXItY29sb3I6IHJnYmEoMCwgMjEyLCAyNTUsIDAuNyk7DQogIGNvbG9yOiAjMDBmZmZmOw0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouaGVhZGVyLWNvbnRyb2xzIHsNCiAgcG9zaXRpb246IGFic29sdXRlOw0KICByaWdodDogMjBweDsNCiAgdG9wOiA1MCU7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxNXB4Ow0KICB6LWluZGV4OiAxMDAwOw0KfQ0KDQouZnVsbHNjcmVlbi1idG4gew0KICBwYWRkaW5nOiA4cHggMTJweDsNCiAgYm9yZGVyOiBub25lOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDMzLCAxMCwgNTYsIDAuNyk7DQogIGNvbG9yOiAjZWVlOw0KICBib3JkZXItcmFkaXVzOiAyMHB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyMTIsIDI1NSwgMC4yKTsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgd2lkdGg6IDQwcHg7DQogIGhlaWdodDogMzJweDsNCn0NCg0KLmZ1bGxzY3JlZW4tYnRuOmhvdmVyIHsNCiAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTsNCiAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSgwLCAyMTIsIDI1NSwgMC4zKTsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAyMTIsIDI1NSwgMC4yKTsNCiAgYm9yZGVyLWNvbG9yOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjcpOw0KICBjb2xvcjogIzAwZmZmZjsNCn0NCg0KLyogQUnku7fmoLzpooTmtYvljLrln5/moLflvI8gKi8NCi5wcmljZS1wcmVkaWN0aW9uLXNlY3Rpb24gew0KICBtYXJnaW4tdG9wOiAxNXB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBwYWRkaW5nOiAxNXB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDE2LCA3LCAzMywgMC42KTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjIpOw0KfQ0KDQoucHJlZGljdGlvbi1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICBmb250LXNpemU6IDEzcHg7DQp9DQoNCi5wcmVkaWN0aW9uLWhlYWRlciBpIHsNCiAgY29sb3I6ICMwMGZmZmY7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi5tb2RlbC1pbmZvIHsNCiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQoucHJlZGljdGlvbi1jb250ZW50IHsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjIpOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIHBhZGRpbmc6IDE1cHg7DQogIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzAwZmZmZjsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KfQ0KDQoucHJlZGljdGlvbi1wbGFjZWhvbGRlciB7DQogIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNSk7DQogIGZvbnQtc3R5bGU6IGl0YWxpYzsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBmb250LXNpemU6IDEycHg7DQp9DQoNCg0KDQovKiDlpJrkuKrpooTmtYvnu5PmnpznmoTmoLflvI8gKi8NCi5wcmVkaWN0aW9ucy1jb250YWluZXIgew0KICBtYXgtaGVpZ2h0OiA1MDBweDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCiAgcGFkZGluZy1yaWdodDogNXB4Ow0KfQ0KDQoucHJlZGljdGlvbi1pdGVtIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjIpOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzAwZmZmZjsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLnByZWRpY3Rpb24taXRlbS5wcmVkaWN0aW9uLWVycm9yIHsNCiAgYm9yZGVyLWxlZnQtY29sb3I6ICNmZjZiNmI7DQp9DQoNCi5wcmVkaWN0aW9uLW1hdGVyaWFsLXRpdGxlIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAyMTIsIDI1NSwgMC4xKTsNCiAgcGFkZGluZzogOHB4IDEycHg7DQogIGZvbnQtc2l6ZTogMTNweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICMwMGZmZmY7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjIpOw0KfQ0KDQoucHJlZGljdGlvbi1pdGVtLnByZWRpY3Rpb24tZXJyb3IgLnByZWRpY3Rpb24tbWF0ZXJpYWwtdGl0bGUgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMTA3LCAxMDcsIDAuMSk7DQogIGNvbG9yOiAjZmY2YjZiOw0KICBib3JkZXItYm90dG9tLWNvbG9yOiByZ2JhKDI1NSwgMTA3LCAxMDcsIDAuMik7DQp9DQoNCi5wcmVkaWN0aW9uLW1hdGVyaWFsLXRpdGxlIGkgew0KICBtYXJnaW4tcmlnaHQ6IDZweDsNCn0NCg0KLmxvYWRpbmctaW5mbyB7DQogIGNvbG9yOiAjMDBmZmZmOw0KICBmb250LXNpemU6IDEycHg7DQogIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICBmb250LXN0eWxlOiBpdGFsaWM7DQp9DQoNCi8qIOmihOa1i+WuueWZqOa7muWKqOadoeagt+W8jyAqLw0KLnByZWRpY3Rpb25zLWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogNHB4Ow0KfQ0KDQoucHJlZGljdGlvbnMtY29udGFpbmVyOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7DQogIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsNCiAgYm9yZGVyLXJhZGl1czogMnB4Ow0KfQ0KDQoucHJlZGljdGlvbnMtY29udGFpbmVyOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogIGJhY2tncm91bmQ6IHJnYmEoMCwgMjEyLCAyNTUsIDAuNSk7DQogIGJvcmRlci1yYWRpdXM6IDJweDsNCn0NCg0KLnByZWRpY3Rpb25zLWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjgpOw0KfQ0KDQovKiDkuIDpl67kuIDnrZTmoLflvI8gKi8NCi5xYS1zZWN0aW9uIHsNCiAgcGFkZGluZzogMDsNCn0NCg0KLnF1ZXN0aW9uLXNlY3Rpb24sIC5hbnN3ZXItc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQp9DQoNCi5hbnN3ZXItc2VjdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDA7DQp9DQoNCi5xYS1sYWJlbCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgZm9udC1zaXplOiAxM3B4Ow0KfQ0KDQoucXVlc3Rpb24tbGFiZWwgew0KICBjb2xvcjogI2ZmYjk4MDsNCn0NCg0KLmFuc3dlci1sYWJlbCB7DQogIGNvbG9yOiAjMDBmZmZmOw0KfQ0KDQoucWEtbGFiZWwgaSB7DQogIG1hcmdpbi1yaWdodDogNnB4Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5xdWVzdGlvbi10ZXh0LCAuYW5zd2VyLXRleHQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuMyk7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgcGFkZGluZzogMTJweCAxNXB4Ow0KICBsaW5lLWhlaWdodDogMS42Ow0KICBmb250LXNpemU6IDEzcHg7DQogIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOSk7DQogIHdoaXRlLXNwYWNlOiBwcmUtd3JhcDsNCiAgd29yZC13cmFwOiBicmVhay13b3JkOw0KICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7DQp9DQoNCi5xdWVzdGlvbi10ZXh0IHsNCiAgYm9yZGVyLWxlZnQ6IDNweCBzb2xpZCAjZmZiOTgwOw0KfQ0KDQouYW5zd2VyLXRleHQgew0KICBib3JkZXItbGVmdDogM3B4IHNvbGlkICMwMGZmZmY7DQogIG1heC1oZWlnaHQ6IDIwMHB4Ow0KICBvdmVyZmxvdy15OiBhdXRvOw0KICBwYWRkaW5nLXJpZ2h0OiAxOHB4Ow0KfQ0KDQovKiDpl67popjmlofmnKzmoLflvI8gKi8NCi5xdWVzdGlvbi10ZXh0IHsNCiAgZm9udC1zdHlsZTogaXRhbGljOw0KICBjb2xvcjogcmdiYSgyNTUsIDIwMCwgMTUwLCAwLjkpOw0KfQ0KDQovKiDnrZTmoYjmlofmnKzmu5rliqjmnaHmoLflvI8gKi8NCi5hbnN3ZXItdGV4dDo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogNHB4Ow0KfQ0KDQouYW5zd2VyLXRleHQ6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpOw0KICBib3JkZXItcmFkaXVzOiAycHg7DQp9DQoNCi5hbnN3ZXItdGV4dDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjUpOw0KICBib3JkZXItcmFkaXVzOiAycHg7DQp9DQoNCi5hbnN3ZXItdGV4dDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjgpOw0KfQ0KDQovKiDku7fmoLzotovlir/ljaHniYfnibnmrormoLflvI8gKi8NCi5wcmljZS10cmVuZC1jYXJkIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgaGVpZ2h0OiBhdXRvOw0KICBtaW4taGVpZ2h0OiA0MDBweDsNCn0NCg0KLnByaWNlLXRyZW5kLWNhcmQgLmNoYXJ0IHsNCiAgZmxleC1zaHJpbms6IDA7DQogIGhlaWdodDogMzAwcHggIWltcG9ydGFudDsNCiAgbWluLWhlaWdodDogMzAwcHg7DQp9DQoNCi5wcmljZS10cmVuZC1jYXJkIC5wcmljZS1wcmVkaWN0aW9uLXNlY3Rpb24gew0KICBmbGV4LXNocmluazogMDsNCiAgbWFyZ2luLXRvcDogMTVweDsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCn0NCg0KLmludmVudG9yeS10b3RhbCB7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY29sb3I6ICMwMGZmZmY7DQogIGZvbnQtd2VpZ2h0OiBub3JtYWw7DQogIG9wYWNpdHk6IDAuOTsNCn0NCg0KLyog5paw5aKe77ya5Lu35qC86LaL5Yq/5Zu+5o6n5Lu25qC35byPICovDQoucHJpY2UtdHJlbmQtY29udHJvbHMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIHBhZGRpbmc6IDEwcHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTYsIDcsIDMzLCAwLjQpOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjEyLCAyNTUsIDAuMik7DQp9DQoNCi5sZWZ0LWNvbnRyb2xzLCAucmlnaHQtY29udHJvbHMgew0KICBmbGV4OiAxOw0KICBtYXgtd2lkdGg6IDQ1JTsNCn0NCg0KLmN1cnZlLWxhYmVsIHsNCiAgZm9udC1zaXplOiAxM3B4Ow0KICBjb2xvcjogIzAwZmZmZjsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLmxlZnQtY29udHJvbHMgLmN1cnZlLWxhYmVsIHsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCn0NCg0KLnJpZ2h0LWNvbnRyb2xzIC5jdXJ2ZS1sYWJlbCB7DQogIHRleHQtYWxpZ246IHJpZ2h0Ow0KfQ0KDQouZHJvcGRvd24tcm93IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAxMHB4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQoucmlnaHQtY29udHJvbHMgLmRyb3Bkb3duLXJvdyB7DQogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQp9DQoNCi5mZXRjaC1kYXRhLWJ0bi1jb250YWluZXIgew0KICB0ZXh0LWFsaWduOiByaWdodDsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLm1vZGVybi1mZXRjaC1idG4gew0KICBiYWNrZ3JvdW5kOiByZ2JhKDEzOCwgNDMsIDIyNiwgMC43KTsNCiAgYm9yZGVyOiBub25lOw0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBwYWRkaW5nOiAxMHB4IDIwcHg7DQogIGNvbG9yOiAjZmZmOw0KICBmb250LXNpemU6IDE0cHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgYm94LXNoYWRvdzogMCAzcHggMTBweCByZ2JhKDEzOCwgNDMsIDIyNiwgMC4zKTsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQoubW9kZXJuLWZldGNoLWJ0bjpob3Zlcjpub3QoOmRpc2FibGVkKSB7DQogIGJhY2tncm91bmQ6IHJnYmEoMTM4LCA0MywgMjI2LCAwLjkpOw0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQogIGJveC1zaGFkb3c6IDAgNXB4IDE1cHggcmdiYSgxMzgsIDQzLCAyMjYsIDAuNSk7DQp9DQoNCi5tb2Rlcm4tZmV0Y2gtYnRuOmFjdGl2ZSB7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsNCn0NCg0KLm1vZGVybi1mZXRjaC1idG46ZGlzYWJsZWQgew0KICBvcGFjaXR5OiAwLjc7DQogIGN1cnNvcjogbm90LWFsbG93ZWQ7DQogIHRyYW5zZm9ybTogbm9uZTsNCn0NCg0KLm1vZGVybi1mZXRjaC1idG4ubG9hZGluZyB7DQogIGJhY2tncm91bmQ6IHJnYmEoMTM4LCA0MywgMjI2LCAwLjcpOw0KfQ0KDQoubW9kZXJuLWZldGNoLWJ0biBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogIGFuaW1hdGlvbjogcm90YXRlIDFzIGxpbmVhciBpbmZpbml0ZTsNCn0NCg0KQGtleWZyYW1lcyByb3RhdGUgew0KICBmcm9tIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH0NCiAgdG8geyB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOyB9DQp9DQoNCi8qIEVsZW1lbnQgVUkg5LiL5ouJ5qGG5qC35byP6KaG55uWICovDQoucHJpY2UtdHJlbmQtY29udHJvbHMgLmVsLXNlbGVjdCB7DQogIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7DQp9DQoNCi5wcmljZS10cmVuZC1jb250cm9scyAuZWwtc2VsZWN0IC5lbC1pbnB1dF9faW5uZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjNGExYzVhICFpbXBvcnRhbnQ7DQogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMTE2LCA3NSwgMTYyLCAwLjUpICFpbXBvcnRhbnQ7DQogIGNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7DQogIGJvcmRlci1yYWRpdXM6IDhweCAhaW1wb3J0YW50Ow0KICBmb250LXNpemU6IDEzcHggIWltcG9ydGFudDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZSAhaW1wb3J0YW50Ow0KfQ0KDQoucHJpY2UtdHJlbmQtY29udHJvbHMgLmVsLXNlbGVjdCAuZWwtaW5wdXRfX2lubmVyOmhvdmVyIHsNCiAgYm9yZGVyLWNvbG9yOiByZ2JhKDExNiwgNzUsIDE2MiwgMC44KSAhaW1wb3J0YW50Ow0KICBib3gtc2hhZG93OiAwIDAgOHB4IHJnYmEoMTE2LCA3NSwgMTYyLCAwLjMpICFpbXBvcnRhbnQ7DQp9DQoNCi5wcmljZS10cmVuZC1jb250cm9scyAuZWwtc2VsZWN0IC5lbC1pbnB1dF9faW5uZXI6Zm9jdXMgew0KICBib3JkZXItY29sb3I6ICM3NjRiYTIgIWltcG9ydGFudDsNCiAgYm94LXNoYWRvdzogMCAwIDEycHggcmdiYSgxMTYsIDc1LCAxNjIsIDAuNSkgIWltcG9ydGFudDsNCn0NCg0KLnByaWNlLXRyZW5kLWNvbnRyb2xzIC5lbC1zZWxlY3QgLmVsLWlucHV0X19pbm5lcjo6cGxhY2Vob2xkZXIgew0KICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpICFpbXBvcnRhbnQ7DQp9DQoNCi5wcmljZS10cmVuZC1jb250cm9scyAuZWwtc2VsZWN0IC5lbC1pbnB1dF9fc3VmZml4IHsNCiAgY29sb3I6ICNmZmZmZmYgIWltcG9ydGFudDsNCn0NCg0KLnByaWNlLXRyZW5kLWNvbnRyb2xzIC5lbC1zZWxlY3QgLmVsLWlucHV0X19zdWZmaXggaSB7DQogIGNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7DQp9DQoNCi5wcmljZS10cmVuZC1jb250cm9scyAuZWwtdGFnIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgxMTYsIDc1LCAxNjIsIDAuNikgIWltcG9ydGFudDsNCiAgYm9yZGVyLWNvbG9yOiByZ2JhKDExNiwgNzUsIDE2MiwgMC44KSAhaW1wb3J0YW50Ow0KICBjb2xvcjogI2ZmZmZmZiAhaW1wb3J0YW50Ow0KICBib3JkZXItcmFkaXVzOiA2cHggIWltcG9ydGFudDsNCn0NCg0KLnByaWNlLXRyZW5kLWNvbnRyb2xzIC5lbC10YWcgLmVsLXRhZ19fY2xvc2Ugew0KICBjb2xvcjogI2ZmZmZmZiAhaW1wb3J0YW50Ow0KfQ0KDQoucHJpY2UtdHJlbmQtY29udHJvbHMgLmVsLXRhZyAuZWwtdGFnX19jbG9zZTpob3ZlciB7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKSAhaW1wb3J0YW50Ow0KfQ0KDQovKiDnm7jkvLznianmlpnljLrln5/moLflvI8gKi8NCi5zaW1pbGFyLW1hdGVyaWFscy1zZWN0aW9uIHsNCiAgbWFyZ2luOiAyMHB4IDA7DQogIHBhZGRpbmc6IDE1cHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTYsIDcsIDMzLCAwLjYpOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjEyLCAyNTUsIDAuMik7DQp9DQoNCi5zaW1pbGFyLW1hdGVyaWFscy1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5zaW1pbGFyLW1hdGVyaWFscy1oZWFkZXIgaSB7DQogIGNvbG9yOiAjMDBmZmZmOw0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQouc2VjdGlvbi10aXRsZSB7DQogIGNvbG9yOiAjMDBmZmZmOw0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQoubG9hZGluZy1pbmZvIHsNCiAgY29sb3I6ICMwMGZmZmY7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgbWFyZ2luLWxlZnQ6IDEwcHg7DQogIGZvbnQtc3R5bGU6IGl0YWxpYzsNCn0NCg0KLnNpbWlsYXItbWF0ZXJpYWxzLWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMC4yKTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBwYWRkaW5nOiAxMHB4Ow0KICBvdmVyZmxvdy14OiBhdXRvOw0KfQ0KDQoubWF0ZXJpYWxzLXRhYmxlIHsNCiAgd2lkdGg6IDEwMCU7DQogIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7DQogIGZvbnQtc2l6ZTogMTNweDsNCn0NCg0KLm1hdGVyaWFscy10YWJsZSB0aCB7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMjEyLCAyNTUsIDAuMSk7DQogIGNvbG9yOiAjMDBmZmZmOw0KICBwYWRkaW5nOiA4cHggMTJweDsNCiAgdGV4dC1hbGlnbjogbGVmdDsNCiAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkIHJnYmEoMCwgMjEyLCAyNTUsIDAuMyk7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi5tYXRlcmlhbHMtdGFibGUgdGQgew0KICBwYWRkaW5nOiA4cHggMTJweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsNCiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsNCn0NCg0KLm1hdGVyaWFsLXJvdyB7DQogIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4zcyBlYXNlOw0KfQ0KDQoubWF0ZXJpYWwtcm93OmhvdmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAyMTIsIDI1NSwgMC4wNSk7DQp9DQoNCi5yYW5rLWNlbGwgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHdpZHRoOiA2MHB4Ow0KfQ0KDQoucmFuay1iYWRnZSB7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgcGFkZGluZzogNHB4IDhweDsNCiAgYm9yZGVyLXJhZGl1czogNTAlOw0KICBjb2xvcjogI2ZmZjsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgbWluLXdpZHRoOiAyMHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5yYW5rLWZpcnN0IHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmZDcwMCwgI2ZmYjM0Nyk7DQogIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDI1NSwgMjE1LCAwLCAwLjMpOw0KfQ0KDQoucmFuay1zZWNvbmQgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjYzBjMGMwLCAjYThhOGE4KTsNCiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMTkyLCAxOTIsIDE5MiwgMC4zKTsNCn0NCg0KLnJhbmstdGhpcmQgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjY2Q3ZjMyLCAjYjg4NjBiKTsNCiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMjA1LCAxMjcsIDUwLCAwLjMpOw0KfQ0KDQoucmFuay1kZWZhdWx0IHsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgxMzgsIDQzLCAyMjYsIDAuNyk7DQp9DQoNCi5tYXRlcmlhbC1uYW1lLCAuY29tcGFyZS1tYXRlcmlhbC1uYW1lIHsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgY29sb3I6ICNmZmY7DQp9DQoNCi5jb21wYXJlLW1hdGVyaWFsLW5hbWUgew0KICBjb2xvcjogIzAwZmZmZjsNCn0NCg0KLnNjb3JlLWNlbGwgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHdpZHRoOiAxMjBweDsNCiAgbWluLXdpZHRoOiAxMjBweDsNCn0NCg0KLnNjb3JlLXZhbHVlIHsNCiAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICBwYWRkaW5nOiAycHggNnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjIpOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGNvbG9yOiAjMDBmZmZmOw0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLnNjb3JlLWRlc2Mgew0KICBjb2xvcjogI2ZmYjk4MDsNCiAgZm9udC1zdHlsZTogaXRhbGljOw0KfQ0KDQouY2F0ZWdvcnktY2VsbCB7DQogIGNvbG9yOiAjNWZkOGI2Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouc2ltaWxhci1tYXRlcmlhbHMtcGxhY2Vob2xkZXIgew0KICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjUpOw0KICBmb250LXN0eWxlOiBpdGFsaWM7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgcGFkZGluZzogMjBweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQouc2ltaWxhci1tYXRlcmlhbHMtZ3JvdXAgew0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KfQ0KDQouc2ltaWxhci1tYXRlcmlhbHMtZ3JvdXA6bGFzdC1jaGlsZCB7DQogIG1hcmdpbi1ib3R0b206IDA7DQp9DQoNCi5ncm91cC10aXRsZSB7DQogIGNvbG9yOiAjMDBmZmZmOw0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICBwYWRkaW5nOiA4cHggMTJweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAyMTIsIDI1NSwgMC4xKTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBib3JkZXItbGVmdDogM3B4IHNvbGlkICMwMGZmZmY7DQp9DQoNCi5wcmljZS10eXBlLWNlbGwgew0KICBjb2xvcjogI2U4NzllZDsNCiAgZm9udC1zaXplOiAxMXB4Ow0KICBtYXgtd2lkdGg6IDEyMHB4Ow0KICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7DQp9DQoNCi5hbGdvcml0aG0tZGVzYyB7DQogIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNyk7DQogIGZvbnQtc2l6ZTogMTFweDsNCiAgZm9udC1zdHlsZTogaXRhbGljOw0KICBtYXJnaW4tbGVmdDogOHB4Ow0KfQ0KDQouYWN0aW9uLWNlbGwgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHdpZHRoOiAxMDBweDsNCn0NCg0KLnZpZXctY29tcGFyaXNvbi1idG4gew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOw0KICBib3JkZXI6IG5vbmU7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgcGFkZGluZzogOHB4IDE2cHg7DQogIGNvbG9yOiAjZmZmOw0KICBmb250LXNpemU6IDEycHg7DQogIGN1cnNvcjogcG9pbnRlcjsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgZGlzcGxheTogaW5saW5lLWZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogNHB4Ow0KICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICBtaW4td2lkdGg6IDcwcHg7DQp9DQoNCi52aWV3LWNvbXBhcmlzb24tYnRuOmhvdmVyIHsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0xcHgpOw0KICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC40KTsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzc2NGJhMiAwJSwgIzY2N2VlYSAxMDAlKTsNCn0NCg0KLnZpZXctY29tcGFyaXNvbi1idG46YWN0aXZlIHsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApOw0KfQ0KDQoudmlldy1jb21wYXJpc29uLWJ0biBpIHsNCiAgZm9udC1zaXplOiAxM3B4Ow0KfQ0KDQovKiDlr7nmr5TlvLnmoYbmoLflvI8gKi8NCi5jb21wYXJpc29uLWRpYWxvZyAuZWwtZGlhbG9nIHsNCiAgYm9yZGVyLXJhZGl1czogMTJweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzE5MTk3MCwgIzRCMDA4MiwgIzgwMDA4MCk7DQogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjEyLCAyNTUsIDAuMyk7DQp9DQoNCi5jb21wYXJpc29uLWRpYWxvZyAuZWwtZGlhbG9nX19oZWFkZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDMzLCAxMCwgNTYsIDAuOSksIHJnYmEoMCwgMjEyLCAyNTUsIDAuMikpOw0KICBwYWRkaW5nOiAyMHB4IDI0cHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjMpOw0KfQ0KDQouY29tcGFyaXNvbi1kaWFsb2cgLmVsLWRpYWxvZ19fdGl0bGUgew0KICBjb2xvcjogIzAwZmZmZjsNCiAgZm9udC1zaXplOiAxOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICB0ZXh0LXNoYWRvdzogMXB4IDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjUpOw0KfQ0KDQouY29tcGFyaXNvbi1kaWFsb2cgLmVsLWRpYWxvZ19faGVhZGVyYnRuIC5lbC1kaWFsb2dfX2Nsb3NlIHsNCiAgY29sb3I6ICMwMGZmZmY7DQogIGZvbnQtc2l6ZTogMjBweDsNCiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCn0NCg0KLmNvbXBhcmlzb24tZGlhbG9nIC5lbC1kaWFsb2dfX2hlYWRlcmJ0biAuZWwtZGlhbG9nX19jbG9zZTpob3ZlciB7DQogIGNvbG9yOiAjZmZmOw0KICB0ZXh0LXNoYWRvdzogMCAwIDEwcHggcmdiYSgwLCAyMTIsIDI1NSwgMC44KTsNCn0NCg0KLmNvbXBhcmlzb24tZGlhbG9nIC5lbC1kaWFsb2dfX2JvZHkgew0KICBwYWRkaW5nOiAwOw0KICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDsNCn0NCg0KLmNvbXBhcmlzb24tY29udGVudCB7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50Ow0KfQ0KDQouY29tcGFyaXNvbi1oZWFkZXIgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQouY29tcGFyaXNvbi10aXRsZSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBnYXA6IDE1cHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLmJhc2UtbWF0ZXJpYWwgew0KICBjb2xvcjogIzAwZmZmZjsNCiAgcGFkZGluZzogOHB4IDE2cHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMjEyLCAyNTUsIDAuMik7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyMTIsIDI1NSwgMC41KTsNCiAgdGV4dC1zaGFkb3c6IDFweCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC41KTsNCn0NCg0KLnZzLXRleHQgew0KICBjb2xvcjogI2ZmZjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBmb250LXdlaWdodDogbm9ybWFsOw0KICB0ZXh0LXNoYWRvdzogMXB4IDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjUpOw0KfQ0KDQouY29tcGFyZS1tYXRlcmlhbCB7DQogIGNvbG9yOiAjZWE3Y2NjOw0KICBwYWRkaW5nOiA4cHggMTZweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyMzQsIDEyNCwgMjA0LCAwLjIpOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjM0LCAxMjQsIDIwNCwgMC41KTsNCiAgdGV4dC1zaGFkb3c6IDFweCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC41KTsNCn0NCg0KLnNpbWlsYXJpdHktaW5mbyB7DQogIGNvbG9yOiAjZmZiOTgwOw0KICBmb250LXNpemU6IDE0cHg7DQogIGZvbnQtd2VpZ2h0OiBub3JtYWw7DQogIHBhZGRpbmc6IDRweCAxMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMTg1LCAxMjgsIDAuMik7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDE4NSwgMTI4LCAwLjQpOw0KICB0ZXh0LXNoYWRvdzogMXB4IDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjUpOw0KfQ0KDQouY29tcGFyaXNvbi1jaGFydC1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDMzLCAxMCwgNTYsIDAuNyk7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyMTIsIDI1NSwgMC4zKTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgYm94LXNoYWRvdzogMCAwIDIwcHggcmdiYSgwLCAyMTIsIDI1NSwgMC4yKTsNCn0NCg0KLmNvbXBhcmlzb24tY2hhcnQgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiA1MDBweDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk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file": "index.vue", "sourceRoot": "src/views/purchaseDashboardPlan", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <div class=\"dashboard-header\">\r\n      <h1>采购计划看板</h1>\r\n      <div class=\"header-controls\">\r\n        <div class=\"fullscreen-btn\" @click=\"toggleFullscreen\" :title=\"isFullscreen ? '退出全屏' : '进入全屏'\">\r\n          <i :class=\"isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'\"></i>\r\n        </div>\r\n        <div class=\"time-filter\">\r\n          <button\r\n            v-for=\"filter in timeFilters\"\r\n            :key=\"filter.id\"\r\n            :class=\"['time-filter-btn', { active: filter.id === activeFilter }]\"\r\n            @click=\"handleTimeFilterChange(filter.id, filter.value)\"\r\n          >\r\n            {{ filter.label }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"dashboard-grid\">\r\n      <!-- 第一行：订单至入库天数 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          订单至入库天数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedOrderFactoryDep\"\r\n              @change=\"handleOrderFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedOrderMaterialType\"\r\n              @change=\"handleOrderMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"monthlyInventoryChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第一行：商务部接收至挂单天数 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          商务部接收至挂单天数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedFactoryDep\"\r\n              @change=\"handleFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedFactoryMaterialType\"\r\n              @change=\"handleFactoryMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"factoryStockChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 入库至领用天数 -->\r\n      <div class=\"card material-chart-card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          入库至领用天数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedMaterialFactoryDep\"\r\n              @change=\"handleMaterialFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedMaterialMaterialType\"\r\n              @change=\"handleMaterialMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"materialStatisticsChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第二行：实时超期数 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          实时超期数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedOverdueFactoryDep\"\r\n              @change=\"handleOverdueFactoryDepChange\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"overdueChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      \r\n\r\n    </div>\r\n\r\n    \r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport chartMethods from './chartMethods'\r\nimport extendedChartMethods from './chartMethodsExtended'\r\nimport screenfull from 'screenfull'\r\nimport { showYearlyAmount, showRealTimeAmount, showCokingCoalAmount, showKeyIndicators, showItemTypeList, showMaterialList, showData, showSuppList, showPurchasePlanList, showHighFrequencyMaterialList, showPurchaseSuppRisk, getMaterialFuturePrice, getMaterialNameList, getPurchasePriceAndStore, getMaterialNameListFromNewTables, getPurchasePriceAndStoreFromNewTables } from '@/api/purchaseDashboard/purchaseDashboard'\r\nimport { listSimilarByItemNames } from '@/api/purchase/similar'\r\nimport { getDepNameList, getListMonthly } from '@/api/purchase/purdchaseFactoryStock'\r\n\r\nexport default {\r\n  name: 'PurchaseDashboard',\r\n  mixins: [chartMethods, extendedChartMethods],\r\n  data() {\r\n    return {\r\n     \r\n\r\n      // 数据\r\n      dashboardData: {},\r\n      purchaseStats: {},\r\n\r\n      // 下拉选项\r\n      topSuppliersOptions: [],\r\n\r\n      // 选中的过滤器值\r\n      selectedTopSuppliersFilter: '',\r\n      selectedOrderType: 'TOP', // 排序类型，默认为TOP\r\n\r\n      // 图表实例\r\n      chartInstances: {},\r\n\r\n      // 原始数据备份\r\n      originalTopSuppliersData: [],\r\n\r\n      // 订单至入库天数相关\r\n      selectedOrderFactoryDep: '', // 选中的分厂\r\n      selectedOrderMaterialType: '', // 选中的物料类型\r\n      orderToReceiptData: [], // 订单至入库天数数据\r\n\r\n      // 第二个订单至入库天数模块相关\r\n      selectedCokingCoalFactoryDep: '', // 选中的分厂\r\n      selectedCokingCoalMaterialType: '', // 选中的物料类型\r\n\r\n      // 第三个订单至入库天数模块相关\r\n      selectedMaterialFactoryDep: '', // 选中的分厂\r\n      selectedMaterialMaterialType: '', // 选中的物料类型\r\n      realTimeInventoryData: [],\r\n      cokingCoalInventoryData: [],\r\n\r\n      // 矿焦煤库存图表相关\r\n      selectedCokingCoalType: '', // 选中的矿焦煤类型，默认为空（全部）\r\n\r\n      // 物料入库统计相关\r\n      selectedMaterialCategory: '1',\r\n      selectedMaterialItem: '',\r\n      materialItemOptions: [],\r\n      materialStatisticsData: [],\r\n\r\n      // 高频采购物料相关\r\n      selectedCodeType: 'ALL',\r\n      selectedItemType: 'CLASS3',\r\n      highFrequencyMaterialData: [],\r\n\r\n      // 供应商风险数据\r\n      supplierRiskData: [],\r\n\r\n      // AI价格预测相关\r\n      pricePredictions: [], // 改为数组，支持多个物料的预测\r\n      predictionLoading: false,\r\n\r\n      // 物料价格趋势图相关\r\n      materialNameOptions: [],\r\n      selectedMaterial: 'PB块',\r\n      selectedMaterialCategory: '1', // 默认选择矿石\r\n      priceAndStoreData: null,\r\n\r\n      // 新的价格趋势图相关属性\r\n      // 采购量曲线\r\n      purchaseAmountCategories: [99], // 默认选择全部\r\n      selectedPurchaseAmountMaterials: [],\r\n      purchaseAmountMaterialOptions: [],\r\n\r\n      // 市场价曲线\r\n      marketPriceCategories: [99], // 默认选择全部\r\n      selectedMarketPriceMaterials: [],\r\n      marketPriceMaterialOptions: [],\r\n\r\n      // 获取数据状态\r\n      fetchingPriceData: false,\r\n      newPriceAndStoreData: null,\r\n\r\n      // 初始化标志\r\n      hasInitializedPriceChart: false,\r\n\r\n      // 相似物料数据\r\n      similarMaterialsData: [],\r\n      similarMaterialsLoading: false,\r\n\r\n      // 对比弹框相关\r\n      comparisonDialogVisible: false,\r\n      comparisonChartLoading: false,\r\n      currentComparison: {},\r\n      comparisonChartInstance: null,\r\n      comparisonPriceData: null,\r\n\r\n      // 机旁库当前库存相关\r\n      selectedFactoryDep: '', // 选中的分厂\r\n      selectedFactoryMaterialType: '', // 选中的物料类型\r\n      factoryDepOptions: [], // 分厂选项列表\r\n      factoryStockData: [], // 机旁库存数据\r\n\r\n      // 实时超期数相关\r\n      selectedOverdueFactoryDep: '', // 选中的分厂\r\n      overdueData: [] // 超期数据\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isFullscreen() {\r\n      return this.$store.state.app.isFullscreenMode\r\n    },\r\n\r\n    // 按itemName、category、priceType联合索引分组相似物料数据\r\n    groupedSimilarMaterials() {\r\n      const grouped = {}\r\n      this.similarMaterialsData.forEach(item => {\r\n        // 创建联合索引key\r\n        const groupKey = `${item.itemName}_${item.category}_${item.priceType}`\r\n        const displayKey = `${item.itemName} (${this.getCategoryName(item.category)} - ${this.getPriceTypeName(item.priceType)})`\r\n\r\n        if (!grouped[displayKey]) {\r\n          grouped[displayKey] = {\r\n            groupKey: groupKey,\r\n            items: []\r\n          }\r\n        }\r\n        grouped[displayKey].items.push(item)\r\n      })\r\n\r\n      // 对每个组内的数据按排名排序\r\n      Object.keys(grouped).forEach(key => {\r\n        grouped[key].items.sort((a, b) => a.rank - b.rank)\r\n      })\r\n\r\n      return grouped\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.checkEchartsAvailability()\r\n    this.fetchDashboardData(3)\r\n\r\n    this.fetchRealTimeInventoryData()\r\n    this.fetchCokingCoalInventoryData()\r\n    // 初始化物料入库统计的下拉框选项和数据\r\n    this.updateMaterialItemOptions().then(() => {\r\n      this.fetchMaterialStatisticsData()\r\n    })\r\n    // 初始化高频采购物料数据\r\n    this.fetchHighFrequencyMaterialData()\r\n    // 初始化供应商风险数据\r\n    this.fetchSupplierRiskData()\r\n\r\n    // 初始化新的物料名称列表（会自动触发默认选中PB块和数据获取）\r\n    this.fetchPurchaseAmountMaterialList()\r\n    this.fetchMarketPriceMaterialList()\r\n\r\n    // 初始化机旁库存数据\r\n    this.fetchFactoryDepOptions()\r\n\r\n    // 初始化订单至入库天数数据\r\n    this.fetchOrderToReceiptData()\r\n\r\n    // 初始化超期数据\r\n    this.fetchOverdueData()\r\n\r\n    this.setupResizeObserver()\r\n    this.initFullscreenListener()\r\n\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.resizeAllCharts)\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 清理定时器和事件监听器\r\n    this.clearAllIntervals()\r\n    this.removeFullscreenListener()\r\n    window.removeEventListener('resize', this.resizeAllCharts)\r\n\r\n    // 确保退出全屏模式\r\n    this.$store.dispatch('app/setFullscreenMode', false)\r\n  },\r\n\r\n  methods: {\r\n    // 初始化全屏监听器\r\n    initFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.on('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 移除全屏监听器\r\n    removeFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.off('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 处理全屏状态变化\r\n    handleFullscreenChange() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        const isFullscreen = screenfull.isFullscreen\r\n        this.$store.dispatch('app/setFullscreenMode', isFullscreen)\r\n\r\n        // 全屏状态变化后，重新调整图表大小\r\n        this.$nextTick(() => {\r\n          setTimeout(() => {\r\n            this.resizeAllCharts()\r\n          }, 300) // 给布局变化一些时间\r\n        })\r\n      }\r\n    },\r\n\r\n    // API调用方法\r\n    async getDashboardData(dimensionType) {\r\n      return await showData({ dimensionType: dimensionType })\r\n    },\r\n\r\n    async getItemTypeList(itemType) {\r\n      return await showItemTypeList({ itemType: itemType })\r\n    },\r\n\r\n    async getMaterialList(params) {\r\n      return await showMaterialList(params)\r\n    },\r\n\r\n    async getSupplierList(params) {\r\n      return await showSuppList(params)\r\n    },\r\n\r\n    async getYearlyAmount(params) {\r\n      return await showYearlyAmount(params)\r\n    },\r\n\r\n    async getRealTimeAmount() {\r\n      return await showRealTimeAmount()\r\n    },\r\n\r\n    async getCokingCoalAmount() {\r\n      return await showCokingCoalAmount()\r\n    },\r\n\r\n    async getKeyIndicators(params) {\r\n      return await showKeyIndicators(params)\r\n    },\r\n\r\n    async getHighFrequencyMaterialList(params) {\r\n      return await showHighFrequencyMaterialList(params)\r\n    },\r\n\r\n    async getPurchaseSuppRisk(params) {\r\n      return await showPurchaseSuppRisk(params)\r\n    },\r\n\r\n    // 根据dimensionType获取timeFlag\r\n    getTimeFlagByDimensionType(dimensionType) {\r\n      switch(dimensionType) {\r\n        case 1: return '03' // 近三个月\r\n        case 2: return '06' // 近六个月\r\n        case 3: return '12' // 近一年\r\n        default: return '03'\r\n      }\r\n    },\r\n\r\n    // 检查ECharts可用性\r\n    checkEchartsAvailability() {\r\n      if (!echarts) {\r\n        console.error('ECharts库未能加载，使用备用显示方式')\r\n        document.querySelectorAll('.chart').forEach(el => {\r\n          el.innerHTML = '<div class=\"chart-placeholder\">图表加载失败</div>'\r\n        })\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n\r\n    // 获取仪表板数据\r\n    async fetchDashboardData(dimensionTypeParam = 1) {\r\n      this.currentDimensionType = dimensionTypeParam\r\n\r\n      // 清除所有定时器\r\n      this.clearAllIntervals()\r\n\r\n      try {\r\n        // 并行获取仪表板数据和关键指标数据\r\n        const [dashboardResponse, keyIndicatorsResponse] = await Promise.all([\r\n          this.getDashboardData(dimensionTypeParam),\r\n          this.getKeyIndicators({ dimensionType: dimensionTypeParam })\r\n        ])\r\n\r\n        // 处理仪表板数据\r\n        if (dashboardResponse && dashboardResponse.data) {\r\n          this.dashboardData = dashboardResponse.data\r\n          console.log('获取仪表板数据成功:', this.dashboardData)\r\n        } else {\r\n          console.error('API数据格式不正确或缺少data字段', dashboardResponse)\r\n          this.showErrorMessage('API数据格式不正确或缺少data字段')\r\n        }\r\n\r\n        // 处理关键指标数据\r\n        if (keyIndicatorsResponse && keyIndicatorsResponse.data) {\r\n          this.purchaseStats = keyIndicatorsResponse.data || {}\r\n          console.log('获取关键指标数据成功:', this.purchaseStats)\r\n        } else {\r\n          console.error('获取关键指标数据失败', keyIndicatorsResponse)\r\n          this.purchaseStats = {}\r\n        }\r\n\r\n        this.initAllCharts()\r\n      } catch (error) {\r\n        console.error('API请求或数据处理失败', error)\r\n        this.showErrorMessage('数据加载失败: ' + error.message)\r\n      }\r\n    },\r\n\r\n    // 显示错误信息\r\n    showErrorMessage(message) {\r\n      document.querySelectorAll('.chart').forEach(chart => {\r\n        chart.innerHTML = `<div class=\"chart-placeholder\">${message}</div>`\r\n      })\r\n    },\r\n\r\n    // 时间过滤器变化处理\r\n    handleTimeFilterChange(filterId, dimensionType) {\r\n      this.activeFilter = filterId\r\n      this.currentDimensionType = dimensionType\r\n      console.log('选择的时间范围:', filterId, '维度:', dimensionType)\r\n\r\n      this.clearAllIntervals()\r\n      this.fetchDashboardData(dimensionType)\r\n      // 同时更新高频物料数据\r\n      this.fetchHighFrequencyMaterialData()\r\n      // 同时更新供应商风险数据\r\n      this.fetchSupplierRiskData()\r\n      // 同时更新物料入库统计数据\r\n      this.fetchMaterialStatisticsData()\r\n      // 注意：价格趋势数据只在用户主动点击按钮时获取，不在时间过滤器变化时自动获取\r\n\r\n      // 同时更新新的物料列表（用于下拉框选项），但不会自动触发数据获取\r\n      this.fetchPurchaseAmountMaterialList()\r\n      this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 清除所有定时器\r\n    clearAllIntervals() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance && instance.intervalId) {\r\n          clearInterval(instance.intervalId)\r\n          instance.intervalId = null\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重新调整所有图表大小\r\n    resizeAllCharts() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance) {\r\n          try {\r\n            instance.resize()\r\n          } catch(err) {\r\n            console.error('图表大小调整失败:', err)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 初始化所有图表\r\n    initAllCharts() {\r\n      const timestamp = new Date().toLocaleTimeString()\r\n      console.log(`initAllCharts started [${timestamp}]`)\r\n      try {\r\n        // 注意：实时库存图表和矿焦煤库存图表会在各自数据获取完成后单独初始化\r\n        // 注意：第一个订单至入库天数图表使用真实数据，会在fetchOrderToReceiptData完成后单独初始化\r\n        // 注意：物料入库统计图表会在fetchMaterialStatisticsData完成后单独初始化\r\n        // 注意：机旁库存图表会在fetchFactoryStockData完成后单独初始化\r\n\r\n        // 初始化其他订单至入库天数图表（第一个会在数据获取完成后单独初始化）\r\n        this.initFactoryStockChart()\r\n        this.initCokingCoalLineChart()\r\n        this.initMaterialStatisticsChart()\r\n\r\n        // 初始化物料词云图\r\n        this.initMaterialCloud()\r\n\r\n        // 初始化TOP供应商图\r\n        this.initTopSuppliersChart()\r\n        this.populateItemDropdown('topSuppliersFilter', 1, 'topSuppliersOptions')\r\n\r\n        // 注意：供应商风险图表会在fetchSupplierRiskData完成后单独初始化\r\n\r\n        // 注意：实时超期数图表会在fetchOverdueData完成后单独初始化\r\n\r\n        // 注意：采购价格趋势图会在fetchPriceAndStoreData完成后单独初始化\r\n\r\n        console.log('所有图表初始化完成')\r\n      } catch (err) {\r\n        console.error('图表初始化主流程失败:', err)\r\n        this.showErrorMessage('图表初始化失败: ' + err.message)\r\n      }\r\n    },\r\n\r\n    // 填充物料类型下拉框\r\n    async populateItemDropdown(selectElementId, itemType, dataPropertyName) {\r\n      try {\r\n        const response = await this.getItemTypeList(itemType)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this[dataPropertyName] = response.data\r\n        } else {\r\n          console.error(`Invalid data format from showItemTypeList for itemType ${itemType}:`, response)\r\n          this[dataPropertyName] = []\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error fetching item types for ${selectElementId}:`, error)\r\n        this[dataPropertyName] = []\r\n      }\r\n    },\r\n\r\n    // 下拉框变化处理方法\r\n    async handleTopSuppliersFilterChange() {\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async handleOrderTypeChange() {\r\n      console.log('排序类型变化:', this.selectedOrderType)\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async refreshTopSuppliersChart() {\r\n      console.log(`Top supplier filter selected item ID: ${this.selectedTopSuppliersFilter}, orderType: ${this.selectedOrderType}`)\r\n      const myChart = this.chartInstances.topSuppliersChart\r\n      if (!myChart) {\r\n        console.error(\"TOP10供应商图表实例未找到\")\r\n        return\r\n      }\r\n\r\n      if (myChart.intervalId) {\r\n        clearInterval(myChart.intervalId)\r\n        myChart.intervalId = null\r\n      }\r\n\r\n      if (!this.selectedTopSuppliersFilter || this.selectedTopSuppliersFilter === \"\") {\r\n        // 使用原始数据，但需要根据orderType重新获取\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n            newSupplierData = this.originalTopSuppliersData\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          this.renderAndPaginateTopSuppliers(myChart, this.originalTopSuppliersData)\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      } else {\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            itemId: this.selectedTopSuppliersFilter,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          document.getElementById('topSuppliersChart').innerHTML = '<div class=\"chart-placeholder\">供应商数据加载失败</div>'\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      }\r\n    },\r\n\r\n    // 设置大小调整观察器\r\n    setupResizeObserver() {\r\n      const resizeObserver = new ResizeObserver(entries => {\r\n        for (let entry of entries) {\r\n          const charts = entry.target.querySelectorAll('.chart')\r\n          charts.forEach(chart => {\r\n            if (chart.id) {\r\n              const instance = echarts.getInstanceByDom(document.getElementById(chart.id))\r\n              if (instance) {\r\n                instance.resize()\r\n              }\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      document.querySelectorAll('.card').forEach(card => {\r\n        resizeObserver.observe(card)\r\n      })\r\n    },\r\n\r\n    toggleFullscreen() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.toggle()\r\n      } else {\r\n        this.$message({\r\n          message: '您的浏览器不支持全屏功能',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n\r\n    async handleOrderFactoryDepChange() {\r\n      console.log('订单至入库天数 - 分厂变化:', this.selectedOrderFactoryDep)\r\n      // 获取新数据并更新图表\r\n      await this.fetchOrderToReceiptData()\r\n    },\r\n\r\n    async handleOrderMaterialTypeChange() {\r\n      console.log('订单至入库天数 - 物料类型变化:', this.selectedOrderMaterialType)\r\n      // 获取新数据并更新图表\r\n      await this.fetchOrderToReceiptData()\r\n    },\r\n\r\n    // 获取订单至入库天数数据\r\n    async fetchOrderToReceiptData() {\r\n      try {\r\n        console.log('fetchOrderToReceiptData - 开始获取数据')\r\n        console.log('fetchOrderToReceiptData - 调用API: showPurchasePlanList')\r\n\r\n        const response = await showPurchasePlanList({\r\n          dimensionType: 3 // 使用固定的维度类型\r\n        })\r\n        console.log('fetchOrderToReceiptData - API调用成功')\r\n        console.log('fetchOrderToReceiptData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          console.log('fetchOrderToReceiptData - 原始数据长度:', response.data.length)\r\n          console.log('fetchOrderToReceiptData - 原始数据前3条:', response.data.slice(0, 3))\r\n\r\n          // 检查数据结构\r\n          console.log('fetchOrderToReceiptData - 检查数据结构')\r\n\r\n          // 检查是否有countType字段\r\n          const hasCountType = response.data.some(item => item.countType !== undefined)\r\n          console.log('fetchOrderToReceiptData - 是否有countType字段:', hasCountType)\r\n\r\n          let dataToProcess = response.data\r\n\r\n          if (hasCountType) {\r\n            // 检查所有可能的countType值\r\n            const countTypes = [...new Set(response.data.map(item => item.countType))]\r\n            console.log('fetchOrderToReceiptData - 所有countType值:', countTypes)\r\n\r\n            // 筛选countType=\"B\"的数据\r\n            const filteredData = response.data.filter(item => item.countType === 'B')\r\n            console.log('fetchOrderToReceiptData - 筛选后的数据:', filteredData)\r\n            console.log('fetchOrderToReceiptData - 筛选后数据长度:', filteredData.length)\r\n\r\n            if (filteredData.length === 0) {\r\n              console.warn('fetchOrderToReceiptData - 没有找到countType=\"B\"的数据，使用所有数据')\r\n              dataToProcess = response.data\r\n            } else {\r\n              dataToProcess = filteredData\r\n            }\r\n          } else {\r\n            console.log('fetchOrderToReceiptData - 没有countType字段，使用所有数据')\r\n          }\r\n\r\n          if (dataToProcess.length === 0) {\r\n            console.warn('fetchOrderToReceiptData - 没有可用数据，不显示图表')\r\n            this.orderToReceiptData = []\r\n          } else {\r\n            // 检查第一条数据的所有字段\r\n            console.log('fetchOrderToReceiptData - 第一条数据的所有字段:', Object.keys(dataToProcess[0]))\r\n            console.log('fetchOrderToReceiptData - 第一条数据内容:', dataToProcess[0])\r\n            console.log('fetchOrderToReceiptData - 第一条数据的midDays字段:', dataToProcess[0].midDays)\r\n\r\n            // 按period排序（从小到大）\r\n            const sortedData = dataToProcess.sort((a, b) => {\r\n              const periodA = parseInt(a.period) || 0\r\n              const periodB = parseInt(b.period) || 0\r\n              return periodA - periodB\r\n            })\r\n\r\n            // 转换数据格式，尝试多个可能的字段名\r\n            this.orderToReceiptData = sortedData.map(item => {\r\n              // 尝试多个可能的平均天数字段名\r\n              let avgDays = 0\r\n              if (item.avgDays !== undefined) {\r\n                avgDays = parseFloat(item.avgDays) || 0\r\n              } else if (item.avgDay !== undefined) {\r\n                avgDays = parseFloat(item.avgDay) || 0\r\n              } else if (item.averageDays !== undefined) {\r\n                avgDays = parseFloat(item.averageDays) || 0\r\n              } else if (item.days !== undefined) {\r\n                avgDays = parseFloat(item.days) || 0\r\n              } else if (item.dayCount !== undefined) {\r\n                avgDays = parseFloat(item.dayCount) || 0\r\n              }\r\n\r\n              // 提取中位数字段\r\n              let midDays = 0\r\n              if (item.midDays !== undefined) {\r\n                midDays = parseFloat(item.midDays) || 0\r\n              } else if (item.midDay !== undefined) {\r\n                midDays = parseFloat(item.midDay) || 0\r\n              } else if (item.medianDays !== undefined) {\r\n                midDays = parseFloat(item.medianDays) || 0\r\n              } else if (item.median !== undefined) {\r\n                midDays = parseFloat(item.median) || 0\r\n              }\r\n\r\n              console.log(`处理数据项 ${item.period}: 平均天数 = ${avgDays}, 中位数 = ${midDays}`)\r\n\r\n              return {\r\n                period: this.formatPeriod(item.period),\r\n                avgDays: avgDays,\r\n                midDays: midDays,\r\n                // 添加原始数据用于调试\r\n                originalData: item\r\n              }\r\n            })\r\n\r\n            console.log('fetchOrderToReceiptData - 处理后的数据:', this.orderToReceiptData)\r\n          }\r\n        } else {\r\n          console.error('获取订单至入库天数数据失败，不显示图表', response)\r\n          this.orderToReceiptData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取订单至入库天数数据失败:', error)\r\n        console.error('错误详情:', error.message)\r\n        this.orderToReceiptData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMonthlyInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 格式化期间（202508 -> 2025.8）\r\n    formatPeriod(period) {\r\n      if (!period) return ''\r\n      const periodStr = period.toString()\r\n      if (periodStr.length === 6) {\r\n        const year = periodStr.substring(0, 4)\r\n        const month = parseInt(periodStr.substring(4, 6))\r\n        return `${year}.${month}`\r\n      }\r\n      return periodStr\r\n    },\r\n\r\n\r\n\r\n    // 第二个订单至入库天数模块的事件处理\r\n    async handleCokingCoalFactoryDepChange() {\r\n      console.log('第二个订单至入库天数 - 分厂变化:', this.selectedCokingCoalFactoryDep)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initCokingCoalLineChart()\r\n    },\r\n\r\n    async handleCokingCoalMaterialTypeChange() {\r\n      console.log('第二个订单至入库天数 - 物料类型变化:', this.selectedCokingCoalMaterialType)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initCokingCoalLineChart()\r\n    },\r\n\r\n    // 第三个订单至入库天数模块的事件处理\r\n    async handleMaterialFactoryDepChange() {\r\n      console.log('第三个订单至入库天数 - 分厂变化:', this.selectedMaterialFactoryDep)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initMaterialStatisticsChart()\r\n    },\r\n\r\n    async handleMaterialMaterialTypeChange() {\r\n      console.log('第三个订单至入库天数 - 物料类型变化:', this.selectedMaterialMaterialType)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initMaterialStatisticsChart()\r\n    },\r\n\r\n    // 实时超期数相关方法\r\n    async handleOverdueFactoryDepChange() {\r\n      console.log('实时超期数 - 分厂变化:', this.selectedOverdueFactoryDep)\r\n      await this.fetchOverdueData()\r\n    },\r\n\r\n    async fetchOverdueData() {\r\n      try {\r\n        // 这里可以根据selectedOverdueFactoryDep获取真实数据\r\n        // 暂时使用模拟数据\r\n        this.overdueData = this.getMockOverdueData()\r\n\r\n        // 数据获取完成后重新初始化图表\r\n        this.$nextTick(() => {\r\n          this.initOverdueChart()\r\n        })\r\n      } catch (error) {\r\n        console.error('获取超期数据失败:', error)\r\n        this.overdueData = this.getMockOverdueData()\r\n        this.$nextTick(() => {\r\n          this.initOverdueChart()\r\n        })\r\n      }\r\n    },\r\n\r\n    // 生成模拟超期数据\r\n    getMockOverdueData() {\r\n      return [\r\n        { materialType: '原材料', overdueNotReceived: 25, overdueNotUsed: 18 },\r\n        { materialType: '辅耐材', overdueNotReceived: 12, overdueNotUsed: 8 },\r\n        { materialType: '材料类', overdueNotReceived: 35, overdueNotUsed: 22 },\r\n        { materialType: '通用备件', overdueNotReceived: 18, overdueNotUsed: 15 },\r\n        { materialType: '专用备件', overdueNotReceived: 28, overdueNotUsed: 20 },\r\n        { materialType: '办公', overdueNotReceived: 5, overdueNotUsed: 3 }\r\n      ]\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    async fetchRealTimeInventoryData() {\r\n      try {\r\n        const response = await this.getRealTimeAmount()\r\n        console.log('fetchRealTimeInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.realTimeInventoryData = response.data || []\r\n          console.log('fetchRealTimeInventoryData - 设置的数据:', this.realTimeInventoryData)\r\n        } else {\r\n          console.error('获取实时库存数据失败，使用模拟数据', response)\r\n          // 使用模拟数据\r\n          this.realTimeInventoryData = this.getMockRealTimeData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取实时库存数据失败，使用模拟数据:', error)\r\n        // 使用模拟数据\r\n        this.realTimeInventoryData = this.getMockRealTimeData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initRealTimeInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟实时库存数据\r\n    getMockRealTimeData() {\r\n      return [\r\n        {\r\n          materialType: 'A',\r\n          materialName: '通用备件',\r\n          centerInventoryAmount: 1250.30,\r\n          machineSideInventoryAmount: 380.50,\r\n          totalInventoryAmount: 1630.80\r\n        },\r\n        {\r\n          materialType: 'B',\r\n          materialName: '专用备件',\r\n          centerInventoryAmount: 980.75,\r\n          machineSideInventoryAmount: 420.25,\r\n          totalInventoryAmount: 1401.00\r\n        },\r\n        {\r\n          materialType: 'C',\r\n          materialName: '材料类',\r\n          centerInventoryAmount: 2150.60,\r\n          machineSideInventoryAmount: 650.40,\r\n          totalInventoryAmount: 2801.00\r\n        },\r\n        {\r\n          materialType: 'D',\r\n          materialName: '原材料',\r\n          centerInventoryAmount: 3200.90,\r\n          machineSideInventoryAmount: 890.10,\r\n          totalInventoryAmount: 4091.00\r\n        },\r\n        {\r\n          materialType: 'E',\r\n          materialName: '辅耐材',\r\n          centerInventoryAmount: 1580.40,\r\n          machineSideInventoryAmount: 320.60,\r\n          totalInventoryAmount: 1901.00\r\n        },\r\n        {\r\n          materialType: 'G',\r\n          materialName: '办公',\r\n          centerInventoryAmount: 150.20,\r\n          machineSideInventoryAmount: 50.80,\r\n          totalInventoryAmount: 201.00\r\n        }\r\n      ]\r\n    },\r\n\r\n    async fetchCokingCoalInventoryData() {\r\n      try {\r\n        const response = await this.getCokingCoalAmount()\r\n        console.log('fetchCokingCoalInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.cokingCoalInventoryData = response.data || []\r\n          console.log('fetchCokingCoalInventoryData - 设置的数据:', this.cokingCoalInventoryData)\r\n        } else {\r\n          console.error('获取矿焦煤库存数据失败', response)\r\n          this.cokingCoalInventoryData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取矿焦煤库存数据失败:', error)\r\n        this.cokingCoalInventoryData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 物料入库统计相关方法\r\n    async handleMaterialCategoryChange() {\r\n      console.log('物料类别变化:', this.selectedMaterialCategory)\r\n      this.selectedMaterialItem = '' // 重置第二个下拉框\r\n      await this.updateMaterialItemOptions()\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async handleMaterialItemChange() {\r\n      console.log('物料项目变化:', this.selectedMaterialItem)\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async updateMaterialItemOptions() {\r\n      if (this.selectedMaterialCategory === '1') {\r\n        // 大类：只有全部选项\r\n        this.materialItemOptions = []\r\n      } else {\r\n        // 中类、细类、叶类：获取对应的选项\r\n        const itemType = parseInt(this.selectedMaterialCategory) - 1 // 1->0, 2->1, 3->2, 4->3\r\n        try {\r\n          const response = await this.getItemTypeList(itemType)\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            this.materialItemOptions = response.data\r\n          } else {\r\n            this.materialItemOptions = []\r\n          }\r\n        } catch (error) {\r\n          console.error('获取物料项目选项失败:', error)\r\n          this.materialItemOptions = []\r\n        }\r\n      }\r\n    },\r\n\r\n    async fetchMaterialStatisticsData() {\r\n      try {\r\n        const params = {\r\n          itemType: parseInt(this.selectedMaterialCategory),\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        // 如果选择了具体物料项目，添加itemId参数\r\n        if (this.selectedMaterialItem && this.selectedMaterialItem !== '') {\r\n          params.itemId = this.selectedMaterialItem\r\n        }\r\n\r\n        console.log('fetchMaterialStatisticsData - 请求参数:', params)\r\n        const response = await this.getMaterialList(params)\r\n        console.log('fetchMaterialStatisticsData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.materialStatisticsData = response.data || []\r\n          console.log('fetchMaterialStatisticsData - 设置的数据:', this.materialStatisticsData)\r\n        } else {\r\n          console.error('获取物料统计数据失败，使用模拟数据', response)\r\n          this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料统计数据失败，使用模拟数据:', error)\r\n        this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialStatisticsChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟物料统计数据\r\n    getMockMaterialStatisticsData() {\r\n      return [\r\n        { itemName: '通用备件', inAmt: 1250.30, arriveRate: 85.5 },\r\n        { itemName: '专用备件', inAmt: 980.75, arriveRate: 78.2 },\r\n        { itemName: '材料类', inAmt: 2150.60, arriveRate: 92.1 },\r\n        { itemName: '原材料', inAmt: 3200.90, arriveRate: 88.7 },\r\n        { itemName: '辅耐材', inAmt: 1580.40, arriveRate: 91.3 },\r\n        { itemName: '办公', inAmt: 150.20, arriveRate: 95.0 }\r\n      ]\r\n    },\r\n\r\n    async fetchHighFrequencyMaterialData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          codeType: this.selectedCodeType,\r\n          itemType: this.selectedItemType\r\n        }\r\n\r\n        console.log('fetchHighFrequencyMaterialData - 请求参数:', params)\r\n        const response = await this.getHighFrequencyMaterialList(params)\r\n        console.log('fetchHighFrequencyMaterialData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.highFrequencyMaterialData = response.data || []\r\n          console.log('fetchHighFrequencyMaterialData - 设置的数据:', this.highFrequencyMaterialData)\r\n        } else {\r\n          console.error('获取高频物料数据失败，使用模拟数据', response)\r\n          this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取高频物料数据失败，使用模拟数据:', error)\r\n        this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialCloud()\r\n      })\r\n    },\r\n\r\n    // 生成模拟高频物料数据\r\n    getMockHighFrequencyData() {\r\n      return [\r\n        { itemName: '粗粉', inAmt: 392467.2, inNum: 5421293 },\r\n        { itemName: '精粉', inAmt: 280350.5, inNum: 4250180 },\r\n        { itemName: '球团', inAmt: 195200.8, inNum: 3180970 },\r\n        { itemName: '烧结', inAmt: 150420.3, inNum: 2890540 },\r\n        { itemName: '焦炭', inAmt: 125680.7, inNum: 2350210 },\r\n        { itemName: '煤炭', inAmt: 98750.2, inNum: 1980760 }\r\n      ]\r\n    },\r\n\r\n    async handleCodeTypeChange() {\r\n      console.log('大类类型变化:', this.selectedCodeType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    async handleItemTypeChange() {\r\n      console.log('维度变化:', this.selectedItemType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    // 获取供应商风险数据\r\n    async fetchSupplierRiskData() {\r\n      try {\r\n        const params = {\r\n          timeFlag: this.getTimeFlagByDimensionType(this.currentDimensionType)\r\n        }\r\n\r\n        console.log('fetchSupplierRiskData - 请求参数:', params)\r\n        const response = await this.getPurchaseSuppRisk(params)\r\n        console.log('fetchSupplierRiskData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.supplierRiskData = response.data || []\r\n          console.log('fetchSupplierRiskData - 设置的数据:', this.supplierRiskData)\r\n        } else {\r\n          console.error('获取供应商风险数据失败', response)\r\n          this.supplierRiskData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取供应商风险数据失败:', error)\r\n        this.supplierRiskData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initSupplierRiskChart()\r\n      })\r\n    },\r\n\r\n    // 获取多个物料的AI价格预测\r\n    async fetchMultiplePricePredictions(materialNames) {\r\n      this.predictionLoading = true\r\n      this.pricePredictions = [] // 清空之前的预测结果\r\n\r\n      try {\r\n        // 并行调用所有物料的预测接口\r\n        const predictionPromises = materialNames.map(async (materialName) => {\r\n          try {\r\n            const params = {\r\n              materialName: materialName,\r\n              materialType: '1' // 默认使用矿石类型，可以根据需要调整\r\n            }\r\n\r\n            console.log(`fetchPricePrediction - ${materialName} 请求参数:`, params)\r\n            const response = await getMaterialFuturePrice(params)\r\n            console.log(`fetchPricePrediction - ${materialName} 完整响应:`, response)\r\n\r\n            if (response && response.code && response.code === 200 && response.data) {\r\n              return {\r\n                materialName: materialName,\r\n                question: response.data.question || `关于${materialName}的价格预测`,\r\n                prediction: response.data.answer || response.data.prediction || response.msg,\r\n                success: response.data.success !== false\r\n              }\r\n            } else {\r\n              console.error(`获取${materialName}价格预测数据失败`, response)\r\n              return {\r\n                materialName: materialName,\r\n                question: `关于${materialName}的价格预测`,\r\n                prediction: `获取${materialName}价格预测失败`,\r\n                success: false\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error(`获取${materialName}价格预测数据失败:`, error)\r\n            return {\r\n              materialName: materialName,\r\n              question: `关于${materialName}的价格预测`,\r\n              prediction: `获取${materialName}价格预测失败：${error.message}`,\r\n              success: false\r\n            }\r\n          }\r\n        })\r\n\r\n        // 等待所有预测结果\r\n        const results = await Promise.all(predictionPromises)\r\n        this.pricePredictions = results\r\n        console.log('fetchMultiplePricePredictions - 设置的预测数据:', this.pricePredictions)\r\n\r\n        const successCount = results.filter(r => r.success).length\r\n        const totalCount = results.length\r\n\r\n        if (successCount > 0) {\r\n          this.$message.success(`成功获取${successCount}/${totalCount}个物料的价格预测`)\r\n        } else {\r\n          this.$message.error('所有物料的价格预测获取失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('批量获取价格预测数据失败:', error)\r\n        this.$message.error('批量获取价格预测失败：' + error.message)\r\n      } finally {\r\n        this.predictionLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取物料名称列表\r\n    async fetchMaterialNameList() {\r\n      try {\r\n        const params = {\r\n          category: parseInt(this.selectedMaterialCategory)\r\n        }\r\n\r\n        const response = await getMaterialNameList(params)\r\n        console.log('fetchMaterialNameList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.materialNameOptions = response.data\r\n          console.log('fetchMaterialNameList - 设置的数据:', this.materialNameOptions)\r\n\r\n          // 设置默认选中PB块，如果存在的话\r\n          const pbMaterial = this.materialNameOptions.find(item => item.itemName === 'PB块')\r\n          if (pbMaterial) {\r\n            this.selectedMaterial = 'PB块'\r\n          } else if (this.materialNameOptions.length > 0) {\r\n            // 如果没有PB块，选择第一个\r\n            this.selectedMaterial = this.materialNameOptions[0].itemName\r\n          }\r\n\r\n          // 获取价格数据\r\n          this.fetchPriceAndStoreData()\r\n        } else {\r\n          console.error('获取物料名称列表失败', response)\r\n          this.materialNameOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料名称列表失败:', error)\r\n        this.materialNameOptions = []\r\n      }\r\n    },\r\n\r\n    // 获取物料价格和采购量数据\r\n    async fetchPriceAndStoreData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemName: this.selectedMaterial\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStore(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n          this.priceAndStoreData = response.data[0] // 取第一个元素\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.priceAndStoreData)\r\n        } else {\r\n          console.error('获取价格和采购量数据失败', response)\r\n          this.priceAndStoreData = null\r\n        }\r\n      } catch (error) {\r\n        console.error('获取价格和采购量数据失败:', error)\r\n        this.priceAndStoreData = null\r\n      }\r\n\r\n      // 数据获取完成后重新初始化价格趋势图\r\n      this.$nextTick(() => {\r\n        this.initPriceTrendChart()\r\n      })\r\n    },\r\n\r\n    // 处理物资类型切换\r\n    async handleMaterialCategoryTypeChange() {\r\n      console.log('物资类型变化:', this.selectedMaterialCategory)\r\n      // 重新获取物料名称列表\r\n      await this.fetchMaterialNameList()\r\n    },\r\n\r\n    // 处理物料选择变化\r\n    async handleMaterialChange() {\r\n      console.log('物料选择变化:', this.selectedMaterial)\r\n      await this.fetchPriceAndStoreData()\r\n      // 不再自动触发AI预测，等用户点击按钮后再触发\r\n    },\r\n\r\n    calculateRealTimeInventoryTotal() {\r\n      let total = 0\r\n      if (this.realTimeInventoryData && this.realTimeInventoryData.length > 0) {\r\n        this.realTimeInventoryData.forEach(item => {\r\n          total += parseFloat(item.totalInventoryAmount) || 0\r\n        })\r\n      }\r\n      return total.toFixed(2)\r\n    },\r\n\r\n    calculateCokingCoalTotal() {\r\n      let total = 0\r\n      if (this.cokingCoalInventoryData && this.cokingCoalInventoryData.length > 0) {\r\n        // 找到所有数据中的最新日期\r\n        let latestDate = ''\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            item.purchaseCokingDailyDetailList.forEach(detail => {\r\n              if (detail.instockDate > latestDate) {\r\n                latestDate = detail.instockDate\r\n              }\r\n            })\r\n          }\r\n        })\r\n\r\n        // 计算最新日期各个物料的库存量合计\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)\r\n            if (latestDetail) {\r\n              total += parseFloat(latestDetail.invQty) || 0\r\n            }\r\n          }\r\n        })\r\n      }\r\n      return (total / 10000).toFixed(2) // 转换为万吨\r\n    },\r\n\r\n    // 处理矿焦煤类型下拉框变化\r\n    async handleCokingCoalTypeChange() {\r\n      console.log('矿焦煤类型变化:', this.selectedCokingCoalType)\r\n      // 重新初始化图表以应用过滤\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 机旁库存相关方法\r\n    // 获取分厂选项列表\r\n    async fetchFactoryDepOptions() {\r\n      try {\r\n        const response = await getDepNameList()\r\n        console.log('fetchFactoryDepOptions - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryDepOptions = response.data\r\n          console.log('fetchFactoryDepOptions - 设置的数据:', this.factoryDepOptions)\r\n        } else {\r\n          console.error('获取分厂选项列表失败', response)\r\n          this.factoryDepOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取分厂选项列表失败:', error)\r\n        this.factoryDepOptions = []\r\n      }\r\n\r\n      // 分厂选项加载完成后，图表会在initAllCharts中初始化\r\n    },\r\n\r\n    // 处理分厂选择变化\r\n    async handleFactoryDepChange() {\r\n      console.log('订单至入库天数 - 分厂选择变化:', this.selectedFactoryDep)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initFactoryStockChart()\r\n    },\r\n\r\n    // 处理物料类型选择变化\r\n    async handleFactoryMaterialTypeChange() {\r\n      console.log('订单至入库天数 - 物料类型选择变化:', this.selectedFactoryMaterialType)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initFactoryStockChart()\r\n    },\r\n\r\n    // 获取机旁库存数据\r\n    async fetchFactoryStockData() {\r\n      try {\r\n        const depName = this.selectedFactoryDep || '' // 空字符串表示全部\r\n        console.log('fetchFactoryStockData - 请求参数:', depName)\r\n\r\n        const response = await getListMonthly(depName)\r\n        console.log('fetchFactoryStockData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryStockData = response.data\r\n          console.log('fetchFactoryStockData - 设置的数据:', this.factoryStockData)\r\n        } else {\r\n          console.error('获取机旁库存数据失败', response)\r\n          this.factoryStockData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取机旁库存数据失败:', error)\r\n        this.factoryStockData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initFactoryStockChart()\r\n      })\r\n    },\r\n\r\n    // 新增方法：处理采购量曲线物料类型变化\r\n    async handlePurchaseAmountCategoriesChange() {\r\n      console.log('采购量曲线物料类型变化:', this.purchaseAmountCategories)\r\n      this.selectedPurchaseAmountMaterials = [] // 重置选中的物料\r\n      await this.fetchPurchaseAmountMaterialList()\r\n    },\r\n\r\n    // 新增方法：处理市场价曲线物料类型变化\r\n    async handleMarketPriceCategoriesChange() {\r\n      console.log('市场价曲线物料类型变化:', this.marketPriceCategories)\r\n      this.selectedMarketPriceMaterials = [] // 重置选中的物料\r\n      await this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 新增方法：获取采购量曲线物料列表\r\n    async fetchPurchaseAmountMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.purchaseAmountCategories,\r\n          curveType: 2, // 采购量曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchPurchaseAmountMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchPurchaseAmountMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.purchaseAmountMaterialOptions = response.data\r\n          console.log('fetchPurchaseAmountMaterialList - 设置的数据:', this.purchaseAmountMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedPurchaseAmountMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.purchaseAmountMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedPurchaseAmountMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 采购量曲线')\r\n\r\n              // 检查市场价曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取采购量曲线物料列表失败', response)\r\n          this.purchaseAmountMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取采购量曲线物料列表失败:', error)\r\n        this.purchaseAmountMaterialOptions = []\r\n      }\r\n    },\r\n\r\n    // 新增方法：获取市场价曲线物料列表\r\n    async fetchMarketPriceMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.marketPriceCategories,\r\n          curveType: 1, // 价格曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchMarketPriceMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchMarketPriceMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.marketPriceMaterialOptions = response.data\r\n          console.log('fetchMarketPriceMaterialList - 设置的数据:', this.marketPriceMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedMarketPriceMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.marketPriceMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedMarketPriceMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 市场价曲线')\r\n\r\n              // 检查采购量曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取市场价曲线物料列表失败', response)\r\n          this.marketPriceMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取市场价曲线物料列表失败:', error)\r\n        this.marketPriceMaterialOptions = []\r\n      }\r\n    },\r\n\r\n\r\n\r\n    // 新增方法：获取物料采购价格数据（用于新的价格趋势图）\r\n    async fetchPriceAndStoreDataForNewChart() {\r\n      if (this.selectedPurchaseAmountMaterials.length === 0 && this.selectedMarketPriceMaterials.length === 0) {\r\n        this.$message.warning('请至少选择一个物料')\r\n        return\r\n      }\r\n\r\n      this.fetchingPriceData = true\r\n      try {\r\n        // 构建itemList\r\n        const itemList = []\r\n\r\n        // 添加采购量曲线的物料\r\n        this.selectedPurchaseAmountMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 2, // 采购量曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        // 添加市场价曲线的物料\r\n        this.selectedMarketPriceMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 1, // 价格曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.newPriceAndStoreData = response.data\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.newPriceAndStoreData)\r\n\r\n          // 重新渲染图表\r\n          this.$nextTick(() => {\r\n            this.initNewPriceTrendChart()\r\n          })\r\n\r\n          // 获取所有选中物料的去重列表\r\n          const allSelectedMaterials = [...new Set([\r\n            ...this.selectedPurchaseAmountMaterials,\r\n            ...this.selectedMarketPriceMaterials\r\n          ])]\r\n\r\n          // 为每个物料调用AI预测接口\r\n          if (allSelectedMaterials.length > 0) {\r\n            this.fetchMultiplePricePredictions(allSelectedMaterials)\r\n          }\r\n\r\n          // 如果市场价曲线有选中物料，获取相似物料信息\r\n          if (this.selectedMarketPriceMaterials.length > 0) {\r\n            this.fetchSimilarMaterials(this.selectedMarketPriceMaterials)\r\n          } else {\r\n            // 清空相似物料数据\r\n            this.similarMaterialsData = []\r\n          }\r\n\r\n          this.$message.success('数据获取成功')\r\n        } else {\r\n          console.error('获取物料采购价格数据失败', response)\r\n          this.$message.error('获取数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料采购价格数据失败:', error)\r\n        this.$message.error('获取数据失败：' + error.message)\r\n      } finally {\r\n        this.fetchingPriceData = false\r\n      }\r\n    },\r\n\r\n    // 获取相似物料信息\r\n    async fetchSimilarMaterials(itemNames) {\r\n      this.similarMaterialsLoading = true\r\n      try {\r\n        const params = {\r\n          itemNames: itemNames\r\n        }\r\n\r\n        console.log('fetchSimilarMaterials - 请求参数:', params)\r\n        const response = await listSimilarByItemNames(params)\r\n        console.log('fetchSimilarMaterials - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.similarMaterialsData = response.data\r\n          console.log('fetchSimilarMaterials - 设置的数据:', this.similarMaterialsData)\r\n        } else {\r\n          console.error('获取相似物料数据失败', response)\r\n          this.similarMaterialsData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取相似物料数据失败:', error)\r\n        this.similarMaterialsData = []\r\n      } finally {\r\n        this.similarMaterialsLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取排名样式类\r\n    getRankClass(rank) {\r\n      if (rank === 1) return 'rank-first'\r\n      if (rank === 2) return 'rank-second'\r\n      if (rank === 3) return 'rank-third'\r\n      return 'rank-default'\r\n    },\r\n\r\n    // 获取商品分类名称\r\n    getCategoryName(category) {\r\n      const categoryMap = {\r\n        1: '矿石',\r\n        2: '煤炭',\r\n        3: '合金',\r\n        4: '废钢'\r\n      }\r\n      return categoryMap[category] || '未知'\r\n    },\r\n\r\n    // 获取价格类型名称\r\n    getPriceTypeName(priceType) {\r\n      const priceTypeMap = {\r\n        1: '现货价',\r\n        2: '市场采购到厂价',\r\n        3: '兴澄废钢收购价(车运)',\r\n        4: '兴澄废钢收购价(船运)',\r\n        5: '沙钢废钢收购价(车运)',\r\n        6: '沙钢废钢收购价(船运)'\r\n      }\r\n      return priceTypeMap[priceType] || '未知'\r\n    },\r\n\r\n    // 打开对比弹框\r\n    openComparisonDialog(item) {\r\n      console.log('openComparisonDialog - 传入的item数据:', item)\r\n      this.currentComparison = { ...item }\r\n      console.log('openComparisonDialog - 设置的currentComparison:', this.currentComparison)\r\n      this.comparisonDialogVisible = true\r\n\r\n      // 弹框打开后获取对比数据\r\n      this.$nextTick(() => {\r\n        this.fetchComparisonData()\r\n      })\r\n    },\r\n\r\n    // 关闭对比弹框\r\n    closeComparisonDialog() {\r\n      this.comparisonDialogVisible = false\r\n      this.currentComparison = {}\r\n      this.comparisonPriceData = null\r\n\r\n      // 清理图表实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n          this.comparisonChartInstance = null\r\n        } catch (err) {\r\n          console.error('清理对比图表实例失败:', err)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 获取对比数据（独立实现，不耦合现有趋势图）\r\n    async fetchComparisonData() {\r\n      this.comparisonChartLoading = true\r\n      try {\r\n        // 构建两个物料的对比请求，只获取价格曲线\r\n        const itemList = [\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.itemName\r\n          },\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.compareItemName\r\n          }\r\n        ]\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchComparisonData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchComparisonData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          // 对返回的数据进行筛选，确保基准物料和相似物料的指定价格类型都能被提取\r\n          const filteredData = []\r\n\r\n          // 获取基准物料和相似物料的目标价格类型名称\r\n          const basePriceTypeName = this.getPriceTypeName(this.currentComparison.priceType)\r\n          const comparePriceTypeName = this.getPriceTypeName(this.currentComparison.comparePriceType)\r\n\r\n          console.log('筛选条件:', {\r\n            baseItemName: this.currentComparison.itemName,\r\n            basePriceTypeName: basePriceTypeName,\r\n            compareItemName: this.currentComparison.compareItemName,\r\n            comparePriceTypeName: comparePriceTypeName\r\n          })\r\n\r\n          response.data.forEach(materialData => {\r\n            const filteredMaterialData = { ...materialData }\r\n\r\n            if (filteredMaterialData.procurementPriceVoList) {\r\n              // 只保留匹配的价格类型\r\n              filteredMaterialData.procurementPriceVoList = filteredMaterialData.procurementPriceVoList.filter(priceGroup => {\r\n                let isMatch = false\r\n                // 基准物料：匹配物料名称和基准价格类型\r\n                if (materialData.itemName === this.currentComparison.itemName) {\r\n                  isMatch = priceGroup.priceName === basePriceTypeName\r\n                  console.log(`基准物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${basePriceTypeName}] 匹配:${isMatch}`)\r\n                }\r\n\r\n                if(isMatch){\r\n                  return isMatch\r\n                }else{\r\n                  if (materialData.itemName === this.currentComparison.compareItemName) {\r\n                    const isMatch = priceGroup.priceName === comparePriceTypeName\r\n                    console.log(`相似物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${comparePriceTypeName}] 匹配:${isMatch}`)\r\n                    return isMatch\r\n                  }\r\n                }\r\n\r\n\r\n                return false\r\n              })\r\n\r\n              console.log(111111111)\r\n              console.log(filteredMaterialData.procurementPriceVoList)\r\n\r\n              // 只有当该物料有匹配的价格类型时才加入结果\r\n              if (filteredMaterialData.procurementPriceVoList.length > 0) {\r\n                filteredData.push(filteredMaterialData)\r\n                console.log(`添加物料[${materialData.itemName}]，包含${filteredMaterialData.procurementPriceVoList.length}个价格组`)\r\n              }\r\n            }\r\n          })\r\n\r\n          this.comparisonPriceData = filteredData\r\n          console.log('fetchComparisonData - 筛选后的数据:', this.comparisonPriceData)\r\n          console.log('筛选结果统计:', {\r\n            totalMaterials: filteredData.length,\r\n            materials: filteredData.map(m => ({\r\n              name: m.itemName,\r\n              priceGroupCount: m.procurementPriceVoList?.length || 0,\r\n              priceGroups: m.procurementPriceVoList?.map(p => p.priceName) || []\r\n            }))\r\n          })\r\n\r\n          // 渲染对比图表\r\n          this.$nextTick(() => {\r\n            this.renderComparisonChart()\r\n          })\r\n        } else {\r\n          console.error('获取对比数据失败', response)\r\n          this.$message.error('获取对比数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取对比数据失败:', error)\r\n        this.$message.error('获取对比数据失败：' + error.message)\r\n      } finally {\r\n        this.comparisonChartLoading = false\r\n      }\r\n    },\r\n\r\n    // 渲染对比图表（独立实现，不耦合现有趋势图）\r\n    renderComparisonChart() {\r\n      const chartDom = document.getElementById('comparisonChart')\r\n      if (!chartDom) {\r\n        console.error('找不到对比图表DOM元素')\r\n        return\r\n      }\r\n\r\n      // 清理现有实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n        } catch (err) {\r\n          console.error('清理现有对比图表实例失败:', err)\r\n        }\r\n      }\r\n\r\n      // 创建新的图表实例\r\n      try {\r\n        this.comparisonChartInstance = echarts.init(chartDom)\r\n      } catch (err) {\r\n        console.error('创建对比图表实例失败:', err)\r\n        return\r\n      }\r\n\r\n      if (!this.comparisonPriceData || this.comparisonPriceData.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      const formatDate = (dateStr) => {\r\n        const year = dateStr.substring(0, 4)\r\n        const month = dateStr.substring(4, 6)\r\n        const day = dateStr.substring(6, 8)\r\n        return `${year}年${month}月${day}日`\r\n      }\r\n\r\n      // 收集所有日期\r\n      let allDates = new Set()\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        if (materialData.procurementPriceVoList) {\r\n          materialData.procurementPriceVoList.forEach(priceGroup => {\r\n            if (priceGroup.priceList) {\r\n              priceGroup.priceList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      allDates = Array.from(allDates).sort()\r\n      const xAxisData = allDates.map(formatDate)\r\n\r\n      if (allDates.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      // 构建系列数据\r\n      const series = []\r\n      const legendData = []\r\n      const colors = ['#8fe9ff', '#ff9f7f', '#5fd8b6', '#ffb980']\r\n      let colorIndex = 0\r\n\r\n      console.log('=== 开始处理对比数据 ===')\r\n      console.log('对比数据总览:', {\r\n        materialCount: this.comparisonPriceData.length,\r\n        baseMaterial: this.currentComparison.itemName,\r\n        compareMaterial: this.currentComparison.compareItemName\r\n      })\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        const materialName = materialData.itemName\r\n        console.log(`\\n处理物料: ${materialName}`)\r\n\r\n        if (materialData.procurementPriceVoList) {\r\n          console.log(`  该物料有 ${materialData.procurementPriceVoList.length} 个价格组`)\r\n          materialData.procurementPriceVoList.forEach((priceGroup, index) => {\r\n            console.log(`  价格组 ${index + 1}: ${priceGroup.priceName}，数据点数量: ${priceGroup.priceList?.length || 0}`)\r\n          })\r\n\r\n          // 数据已经在fetchComparisonData中预先筛选过，这里直接处理所有匹配的价格组\r\n          materialData.procurementPriceVoList.forEach((priceGroup, groupIndex) => {\r\n            const priceData = allDates.map(date => {\r\n              const found = priceGroup.priceList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.price) : null\r\n            })\r\n\r\n            // 统计有效数据点\r\n            const validDataCount = priceData.filter(v => v !== null && v !== undefined).length\r\n            console.log(`    处理价格组[${priceGroup.priceName}]，有效数据点: ${validDataCount}/${priceData.length}`)\r\n\r\n            // 确保每条曲线都有唯一的名称和颜色，即使数据相同\r\n            const uniqueName = `${materialName}-${priceGroup.priceName}`\r\n            const lineColor = colors[colorIndex % colors.length]\r\n\r\n            // 检查是否已经有相同的数据，如果有则添加轻微偏移\r\n            const dataStr = JSON.stringify(priceData)\r\n            const existingSeries = series.find(s => JSON.stringify(s.data) === dataStr)\r\n            let adjustedData = priceData\r\n\r\n            if (existingSeries && priceData.some(v => v !== null)) {\r\n              // 为重复数据添加极小的偏移量（0.01），确保两条线都能显示\r\n              adjustedData = priceData.map(value => value !== null ? value + 0.01 : null)\r\n              console.log(`    检测到重复数据，为 ${uniqueName} 添加偏移`)\r\n            }\r\n\r\n            series.push({\r\n              name: uniqueName,\r\n              type: 'line',\r\n              data: adjustedData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 3,\r\n                color: lineColor,\r\n                // 如果是偏移的数据，使用虚线样式区分\r\n                type: adjustedData !== priceData ? 'dashed' : 'solid'\r\n              },\r\n              itemStyle: {\r\n                color: lineColor\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 6,\r\n              connectNulls: true,\r\n              // 添加z-index确保两条线都能显示\r\n              z: colorIndex + 1\r\n            })\r\n\r\n            legendData.push(uniqueName)\r\n            colorIndex++\r\n            console.log(`    ✓ 添加曲线: ${uniqueName}，颜色: ${lineColor}，有效数据: ${validDataCount}`)\r\n          })\r\n        }\r\n      })\r\n\r\n      console.log(`\\n=== 图表数据处理完成 ===`)\r\n      console.log(`总计添加 ${series.length} 条曲线:`)\r\n      series.forEach((s, i) => {\r\n        const validCount = s.data.filter(v => v !== null && v !== undefined).length\r\n        console.log(`  ${i + 1}. ${s.name} (有效数据: ${validCount})`)\r\n      })\r\n\r\n      // 计算Y轴范围\r\n      let priceMin, priceMax\r\n      const priceValues = series.flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n      if (priceValues.length > 0) {\r\n        priceMin = Math.min(...priceValues)\r\n        priceMax = Math.max(...priceValues)\r\n      }\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          formatter: function(params) {\r\n            let str = params[0].axisValueLabel + '<br/>'\r\n            params.forEach(item => {\r\n              if (item.value !== null && item.value !== undefined) {\r\n                str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`\r\n              } else {\r\n                str += `${item.marker}${item.seriesName}: -<br/>`\r\n              }\r\n            })\r\n            return str\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '12%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: function(index, value) {\r\n              if (index >= allDates.length || !allDates.length) return false\r\n\r\n              const uniqueMonths = new Set()\r\n              allDates.forEach(dateStr => {\r\n                const year = dateStr.substring(0, 4)\r\n                const month = dateStr.substring(4, 6)\r\n                uniqueMonths.add(`${year}${month}`)\r\n              })\r\n\r\n              const monthsCount = uniqueMonths.size\r\n              if (monthsCount <= 1) return true\r\n\r\n              const totalDataPoints = allDates.length\r\n              const idealInterval = Math.floor(totalDataPoints / Math.min(monthsCount, 8))\r\n\r\n              return index % Math.max(idealInterval, 1) === 0\r\n            },\r\n            formatter: function(value, index) {\r\n              if (index >= allDates.length) return ''\r\n              const originalDateStr = allDates[index]\r\n              if (!originalDateStr) return ''\r\n\r\n              const year = originalDateStr.substring(0, 4)\r\n              const month = parseInt(originalDateStr.substring(4, 6))\r\n              return `${year}.${month}`\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '价格（元/吨）',\r\n          min: priceMin,\r\n          max: priceMax,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee'\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: series\r\n      }\r\n\r\n      this.comparisonChartInstance.setOption(option, true)\r\n    },\r\n\r\n    // 检查两个曲线是否都已设置默认值，如果是则触发初始数据获取\r\n    checkAndTriggerInitialDataFetch() {\r\n      // 检查两个曲线是否都已经设置了默认的PB块\r\n      if (this.selectedPurchaseAmountMaterials.includes('PB块') &&\r\n        this.selectedMarketPriceMaterials.includes('PB块') &&\r\n        !this.hasInitializedPriceChart) {\r\n\r\n        this.hasInitializedPriceChart = true // 标记已经初始化过\r\n        console.log('两个曲线都已设置默认值，自动触发数据获取')\r\n\r\n        // 自动触发数据获取\r\n        this.$nextTick(() => {\r\n          this.fetchPriceAndStoreDataForNewChart()\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n  font-family: \"Microsoft YaHei\", sans-serif;\r\n}\r\n\r\n.dashboard-container {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  color: #fff;\r\n  overflow-x: hidden;\r\n  padding: 10px;\r\n}\r\n\r\n.dashboard-header {\r\n  text-align: center;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n  padding: 5px 0;\r\n}\r\n\r\n.dashboard-header h1 {\r\n  font-size: 24px;\r\n  position: relative;\r\n  display: inline-block;\r\n  padding: 5px 40px;\r\n}\r\n\r\n.dashboard-header::before,\r\n.dashboard-header::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 50%;\r\n  width: 30%;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.dashboard-header::before {\r\n  left: 0;\r\n}\r\n\r\n.dashboard-header::after {\r\n  right: 0;\r\n}\r\n\r\n.dashboard-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(12, 1fr);\r\n  grid-template-rows: auto auto auto auto auto auto;\r\n  gap: 10px;\r\n  min-height: calc(100vh - 80px);\r\n}\r\n\r\n.card {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 5px;\r\n  padding: 10px;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.card-title {\r\n  font-size: 14px;\r\n  margin-bottom: 5px;\r\n  font-weight: normal;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.chart-filter-dropdown-container {\r\n  z-index: 10;\r\n}\r\n\r\n.chart-filter-dropdown-container select {\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n  color: #fff;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  font-size: 12px;\r\n}\r\n\r\n.chart {\r\n  width: 100%;\r\n  height: calc(100% - 20px);\r\n  min-height: 200px;\r\n  flex: 1;\r\n}\r\n\r\n.stat-cards {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  height: 100%;\r\n  align-items: center;\r\n}\r\n\r\n.stat-card {\r\n  text-align: center;\r\n  flex-grow: 1;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 34px;\r\n  font-weight: bold;\r\n  color: #00ffff;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 18px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.chart-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: rgba(255,255,255,0.5);\r\n  font-size: 14px;\r\n}\r\n\r\n.material-chart-card {\r\n  height: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.material-chart-card .chart {\r\n  flex-grow: 1;\r\n  height: 250px;\r\n  min-height: 250px;\r\n}\r\n\r\n.time-filter {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.time-filter-btn {\r\n  padding: 6px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n}\r\n\r\n.time-filter-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.time-filter-btn.active {\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n  font-weight: 500;\r\n}\r\n\r\n.header-controls {\r\n  position: absolute;\r\n  right: 20px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  z-index: 1000;\r\n}\r\n\r\n.fullscreen-btn {\r\n  padding: 8px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 40px;\r\n  height: 32px;\r\n}\r\n\r\n.fullscreen-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n}\r\n\r\n/* AI价格预测区域样式 */\r\n.price-prediction-section {\r\n  margin-top: 15px;\r\n  margin-bottom: 15px;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-size: 13px;\r\n}\r\n\r\n.prediction-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.model-info {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 12px;\r\n}\r\n\r\n.prediction-content {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  border-left: 3px solid #00ffff;\r\n  position: relative;\r\n}\r\n\r\n.prediction-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n\r\n\r\n/* 多个预测结果的样式 */\r\n.predictions-container {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding-right: 5px;\r\n}\r\n\r\n.prediction-item {\r\n  margin-bottom: 15px;\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n  overflow: hidden;\r\n}\r\n\r\n.prediction-item.prediction-error {\r\n  border-left-color: #ff6b6b;\r\n}\r\n\r\n.prediction-material-title {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  padding: 8px 12px;\r\n  font-size: 13px;\r\n  font-weight: 600;\r\n  color: #00ffff;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-item.prediction-error .prediction-material-title {\r\n  background-color: rgba(255, 107, 107, 0.1);\r\n  color: #ff6b6b;\r\n  border-bottom-color: rgba(255, 107, 107, 0.2);\r\n}\r\n\r\n.prediction-material-title i {\r\n  margin-right: 6px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n/* 预测容器滚动条样式 */\r\n.predictions-container::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 一问一答样式 */\r\n.qa-section {\r\n  padding: 0;\r\n}\r\n\r\n.question-section, .answer-section {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.answer-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.qa-label {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  font-weight: 600;\r\n  font-size: 13px;\r\n}\r\n\r\n.question-label {\r\n  color: #ffb980;\r\n}\r\n\r\n.answer-label {\r\n  color: #00ffff;\r\n}\r\n\r\n.qa-label i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n.question-text, .answer-text {\r\n  background-color: rgba(0, 0, 0, 0.3);\r\n  border-radius: 8px;\r\n  padding: 12px 15px;\r\n  line-height: 1.6;\r\n  font-size: 13px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  white-space: pre-wrap;\r\n  word-wrap: break-word;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.question-text {\r\n  border-left: 3px solid #ffb980;\r\n}\r\n\r\n.answer-text {\r\n  border-left: 3px solid #00ffff;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding-right: 18px;\r\n}\r\n\r\n/* 问题文本样式 */\r\n.question-text {\r\n  font-style: italic;\r\n  color: rgba(255, 200, 150, 0.9);\r\n}\r\n\r\n/* 答案文本滚动条样式 */\r\n.answer-text::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 价格趋势卡片特殊样式 */\r\n.price-trend-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: auto;\r\n  min-height: 400px;\r\n}\r\n\r\n.price-trend-card .chart {\r\n  flex-shrink: 0;\r\n  height: 300px !important;\r\n  min-height: 300px;\r\n}\r\n\r\n.price-trend-card .price-prediction-section {\r\n  flex-shrink: 0;\r\n  margin-top: 15px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.inventory-total {\r\n  font-size: 12px;\r\n  color: #00ffff;\r\n  font-weight: normal;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 新增：价格趋势图控件样式 */\r\n.price-trend-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: rgba(16, 7, 33, 0.4);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.left-controls, .right-controls {\r\n  flex: 1;\r\n  max-width: 45%;\r\n}\r\n\r\n.curve-label {\r\n  font-size: 13px;\r\n  color: #00ffff;\r\n  margin-bottom: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n.left-controls .curve-label {\r\n  text-align: left;\r\n}\r\n\r\n.right-controls .curve-label {\r\n  text-align: right;\r\n}\r\n\r\n.dropdown-row {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.right-controls .dropdown-row {\r\n  justify-content: flex-end;\r\n}\r\n\r\n.fetch-data-btn-container {\r\n  text-align: right;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.modern-fetch-btn {\r\n  background: rgba(138, 43, 226, 0.7);\r\n  border: none;\r\n  border-radius: 12px;\r\n  padding: 10px 20px;\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 3px 10px rgba(138, 43, 226, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-fetch-btn:hover:not(:disabled) {\r\n  background: rgba(138, 43, 226, 0.9);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.5);\r\n}\r\n\r\n.modern-fetch-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.modern-fetch-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.modern-fetch-btn.loading {\r\n  background: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.modern-fetch-btn i {\r\n  margin-right: 8px;\r\n  animation: rotate 1s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n/* Element UI 下拉框样式覆盖 */\r\n.price-trend-controls .el-select {\r\n  background-color: transparent !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner {\r\n  background-color: #4a1c5a !important;\r\n  border: 1px solid rgba(116, 75, 162, 0.5) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 8px !important;\r\n  font-size: 13px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:hover {\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  box-shadow: 0 0 8px rgba(116, 75, 162, 0.3) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:focus {\r\n  border-color: #764ba2 !important;\r\n  box-shadow: 0 0 12px rgba(116, 75, 162, 0.5) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner::placeholder {\r\n  color: rgba(255, 255, 255, 0.7) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix i {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag {\r\n  background-color: rgba(116, 75, 162, 0.6) !important;\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 6px !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close:hover {\r\n  background-color: rgba(255, 255, 255, 0.2) !important;\r\n}\r\n\r\n/* 相似物料区域样式 */\r\n.similar-materials-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.similar-materials-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  font-size: 14px;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.similar-materials-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.section-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n.similar-materials-container {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 10px;\r\n  overflow-x: auto;\r\n}\r\n\r\n.materials-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  font-size: 13px;\r\n}\r\n\r\n.materials-table th {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  color: #00ffff;\r\n  padding: 8px 12px;\r\n  text-align: left;\r\n  border-bottom: 2px solid rgba(0, 212, 255, 0.3);\r\n  font-weight: 600;\r\n}\r\n\r\n.materials-table td {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.material-row {\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.material-row:hover {\r\n  background-color: rgba(0, 212, 255, 0.05);\r\n}\r\n\r\n.rank-cell {\r\n  text-align: center;\r\n  width: 60px;\r\n}\r\n\r\n.rank-badge {\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 50%;\r\n  color: #fff;\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n  min-width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.rank-first {\r\n  background: linear-gradient(135deg, #ffd700, #ffb347);\r\n  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.rank-second {\r\n  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);\r\n  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);\r\n}\r\n\r\n.rank-third {\r\n  background: linear-gradient(135deg, #cd7f32, #b8860b);\r\n  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);\r\n}\r\n\r\n.rank-default {\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.material-name, .compare-material-name {\r\n  font-weight: 500;\r\n  color: #fff;\r\n}\r\n\r\n.compare-material-name {\r\n  color: #00ffff;\r\n}\r\n\r\n.score-cell {\r\n  text-align: center;\r\n  width: 120px;\r\n  min-width: 120px;\r\n}\r\n\r\n.score-value {\r\n  display: inline-block;\r\n  padding: 2px 6px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 4px;\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n}\r\n\r\n.score-desc {\r\n  color: #ffb980;\r\n  font-style: italic;\r\n}\r\n\r\n.category-cell {\r\n  color: #5fd8b6;\r\n  font-weight: 500;\r\n}\r\n\r\n.similar-materials-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n.similar-materials-group {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.similar-materials-group:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.group-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n}\r\n\r\n.price-type-cell {\r\n  color: #e879ed;\r\n  font-size: 11px;\r\n  max-width: 120px;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.algorithm-desc {\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 11px;\r\n  font-style: italic;\r\n  margin-left: 8px;\r\n}\r\n\r\n.action-cell {\r\n  text-align: center;\r\n  width: 100px;\r\n}\r\n\r\n.view-comparison-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  color: #fff;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  white-space: nowrap;\r\n  min-width: 70px;\r\n}\r\n\r\n.view-comparison-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\r\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\r\n}\r\n\r\n.view-comparison-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.view-comparison-btn i {\r\n  font-size: 13px;\r\n}\r\n\r\n/* 对比弹框样式 */\r\n.comparison-dialog .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__header {\r\n  background: linear-gradient(135deg, rgba(33, 10, 56, 0.9), rgba(0, 212, 255, 0.2));\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__title {\r\n  color: #00ffff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close {\r\n  color: #00ffff;\r\n  font-size: 20px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close:hover {\r\n  color: #fff;\r\n  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n.comparison-dialog .el-dialog__body {\r\n  padding: 0;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-content {\r\n  padding: 20px;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.comparison-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 15px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.base-material {\r\n  color: #00ffff;\r\n  padding: 8px 16px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.vs-text {\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.compare-material {\r\n  color: #ea7ccc;\r\n  padding: 8px 16px;\r\n  background-color: rgba(234, 124, 204, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(234, 124, 204, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.similarity-info {\r\n  color: #ffb980;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  padding: 4px 12px;\r\n  background-color: rgba(255, 185, 128, 0.2);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 185, 128, 0.4);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-chart-container {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  overflow: hidden;\r\n  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.comparison-chart {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n</style>\r\n"]}]}