{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardStock\\index.vue?vue&type=template&id=04110bc0&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardStock\\index.vue", "mtime": 1756456493859}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}