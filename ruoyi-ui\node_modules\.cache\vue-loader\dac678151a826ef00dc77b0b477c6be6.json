{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardStock\\index.vue?vue&type=style&index=0&id=04110bc0&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardStock\\index.vue", "mtime": 1756456493859}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KKiB7DQogIG1hcmdpbjogMDsNCiAgcGFkZGluZzogMDsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgZm9udC1mYW1pbHk6ICJNaWNyb3NvZnQgWWFIZWkiLCBzYW5zLXNlcmlmOw0KfQ0KDQouZGFzaGJvYXJkLWNvbnRhaW5lciB7DQogIHdpZHRoOiAxMDAlOw0KICBtaW4taGVpZ2h0OiAxMDB2aDsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzE5MTk3MCwgIzRCMDA4MiwgIzgwMDA4MCk7DQogIGNvbG9yOiAjZmZmOw0KICBvdmVyZmxvdy14OiBoaWRkZW47DQogIHBhZGRpbmc6IDEwcHg7DQp9DQoNCi5kYXNoYm9hcmQtaGVhZGVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIHBhZGRpbmc6IDVweCAwOw0KfQ0KDQouZGFzaGJvYXJkLWhlYWRlciBoMSB7DQogIGZvbnQtc2l6ZTogMjRweDsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogIHBhZGRpbmc6IDVweCA0MHB4Ow0KfQ0KDQouZGFzaGJvYXJkLWhlYWRlcjo6YmVmb3JlLA0KLmRhc2hib2FyZC1oZWFkZXI6OmFmdGVyIHsNCiAgY29udGVudDogIiI7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgdG9wOiA1MCU7DQogIHdpZHRoOiAzMCU7DQogIGhlaWdodDogMnB4Ow0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHJnYmEoMCwyMTIsMjU1LDApIDAlLCByZ2JhKDAsMjEyLDI1NSwxKSA1MCUsIHJnYmEoMCwyMTIsMjU1LDApIDEwMCUpOw0KfQ0KDQouZGFzaGJvYXJkLWhlYWRlcjo6YmVmb3JlIHsNCiAgbGVmdDogMDsNCn0NCg0KLmRhc2hib2FyZC1oZWFkZXI6OmFmdGVyIHsNCiAgcmlnaHQ6IDA7DQp9DQoNCi5kYXNoYm9hcmQtZ3JpZCB7DQogIGRpc3BsYXk6IGdyaWQ7DQogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDEyLCAxZnIpOw0KICBncmlkLXRlbXBsYXRlLXJvd3M6IGF1dG8gYXV0byBhdXRvIGF1dG8gYXV0byBhdXRvOw0KICBnYXA6IDEwcHg7DQogIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA4MHB4KTsNCn0NCg0KLmNhcmQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDMzLCAxMCwgNTYsIDAuNyk7DQogIGJvcmRlci1yYWRpdXM6IDVweDsNCiAgcGFkZGluZzogMTBweDsNCiAgYm94LXNoYWRvdzogMCAwIDEwcHggcmdiYSgwLCAwLCAwLCAwLjMpOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIG1pbi1oZWlnaHQ6IDMwMHB4Ow0KfQ0KDQouY2FyZDo6YmVmb3JlIHsNCiAgY29udGVudDogJyc7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgdG9wOiAwOw0KICBsZWZ0OiAwOw0KICByaWdodDogMDsNCiAgaGVpZ2h0OiAycHg7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgcmdiYSgwLDIxMiwyNTUsMCkgMCUsIHJnYmEoMCwyMTIsMjU1LDEpIDUwJSwgcmdiYSgwLDIxMiwyNTUsMCkgMTAwJSk7DQp9DQoNCi5jYXJkLXRpdGxlIHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBtYXJnaW4tYm90dG9tOiA1cHg7DQogIGZvbnQtd2VpZ2h0OiBub3JtYWw7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLmNoYXJ0LWZpbHRlci1kcm9wZG93bi1jb250YWluZXIgew0KICB6LWluZGV4OiAxMDsNCn0NCg0KLmNoYXJ0LWZpbHRlci1kcm9wZG93bi1jb250YWluZXIgc2VsZWN0IHsNCiAgcGFkZGluZzogNHB4IDhweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDEzOCwgNDMsIDIyNiwgMC43KTsNCiAgY29sb3I6ICNmZmY7DQogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjEyLCAyNTUsIDAuMyk7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KLmNoYXJ0IHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogY2FsYygxMDAlIC0gMjBweCk7DQogIG1pbi1oZWlnaHQ6IDIwMHB4Ow0KICBmbGV4OiAxOw0KfQ0KDQouc3RhdC1jYXJkcyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYXJvdW5kOw0KICBoZWlnaHQ6IDEwMCU7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5zdGF0LWNhcmQgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGZsZXgtZ3JvdzogMTsNCn0NCg0KLnN0YXQtdmFsdWUgew0KICBmb250LXNpemU6IDM0cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzAwZmZmZjsNCiAgbWFyZ2luLWJvdHRvbTogNXB4Ow0KfQ0KDQouc3RhdC1sYWJlbCB7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgb3BhY2l0eTogMC44Ow0KfQ0KDQouY2hhcnQtcGxhY2Vob2xkZXIgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDAlOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgY29sb3I6IHJnYmEoMjU1LDI1NSwyNTUsMC41KTsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQoubWF0ZXJpYWwtY2hhcnQtY2FyZCB7DQogIGhlaWdodDogYXV0bzsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgbWluLWhlaWdodDogMzAwcHg7DQp9DQoNCi5tYXRlcmlhbC1jaGFydC1jYXJkIC5jaGFydCB7DQogIGZsZXgtZ3JvdzogMTsNCiAgaGVpZ2h0OiAyNTBweDsNCiAgbWluLWhlaWdodDogMjUwcHg7DQp9DQoNCi50aW1lLWZpbHRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMTBweDsNCn0NCg0KLnRpbWUtZmlsdGVyLWJ0biB7DQogIHBhZGRpbmc6IDZweCAxMnB4Ow0KICBib3JkZXI6IG5vbmU7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMzMsIDEwLCA1NiwgMC43KTsNCiAgY29sb3I6ICNlZWU7DQogIGJvcmRlci1yYWRpdXM6IDIwcHg7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjIpOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQp9DQoNCi50aW1lLWZpbHRlci1idG46aG92ZXIgew0KICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpOw0KICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDAsIDIxMiwgMjU1LCAwLjMpOw0KfQ0KDQoudGltZS1maWx0ZXItYnRuLmFjdGl2ZSB7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMjEyLCAyNTUsIDAuMik7DQogIGJvcmRlci1jb2xvcjogcmdiYSgwLCAyMTIsIDI1NSwgMC43KTsNCiAgY29sb3I6ICMwMGZmZmY7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5oZWFkZXItY29udHJvbHMgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHJpZ2h0OiAyMHB4Ow0KICB0b3A6IDUwJTsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDE1cHg7DQogIHotaW5kZXg6IDEwMDA7DQp9DQoNCi5mdWxsc2NyZWVuLWJ0biB7DQogIHBhZGRpbmc6IDhweCAxMnB4Ow0KICBib3JkZXI6IG5vbmU7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMzMsIDEwLCA1NiwgMC43KTsNCiAgY29sb3I6ICNlZWU7DQogIGJvcmRlci1yYWRpdXM6IDIwcHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjIpOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICB3aWR0aDogNDBweDsNCiAgaGVpZ2h0OiAzMnB4Ow0KfQ0KDQouZnVsbHNjcmVlbi1idG46aG92ZXIgew0KICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpOw0KICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDAsIDIxMiwgMjU1LCAwLjMpOw0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjIpOw0KICBib3JkZXItY29sb3I6IHJnYmEoMCwgMjEyLCAyNTUsIDAuNyk7DQogIGNvbG9yOiAjMDBmZmZmOw0KfQ0KDQovKiBBSeS7t+agvOmihOa1i+WMuuWfn+agt+W8jyAqLw0KLnByaWNlLXByZWRpY3Rpb24tc2VjdGlvbiB7DQogIG1hcmdpbi10b3A6IDE1cHg7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIHBhZGRpbmc6IDE1cHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTYsIDcsIDMzLCAwLjYpOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMCwgMjEyLCAyNTUsIDAuMik7DQp9DQoNCi5wcmVkaWN0aW9uLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDEycHg7DQogIGZvbnQtc2l6ZTogMTNweDsNCn0NCg0KLnByZWRpY3Rpb24taGVhZGVyIGkgew0KICBjb2xvcjogIzAwZmZmZjsNCiAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLm1vZGVsLWluZm8gew0KICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpOw0KICBmb250LXNpemU6IDEycHg7DQp9DQoNCi5wcmVkaWN0aW9uLWNvbnRlbnQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuMik7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgcGFkZGluZzogMTVweDsNCiAgYm9yZGVyLWxlZnQ6IDNweCBzb2xpZCAjMDBmZmZmOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQp9DQoNCi5wcmVkaWN0aW9uLXBsYWNlaG9sZGVyIHsNCiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC41KTsNCiAgZm9udC1zdHlsZTogaXRhbGljOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDIwcHg7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KDQoNCi8qIOWkmuS4qumihOa1i+e7k+aenOeahOagt+W8jyAqLw0KLnByZWRpY3Rpb25zLWNvbnRhaW5lciB7DQogIG1heC1oZWlnaHQ6IDUwMHB4Ow0KICBvdmVyZmxvdy15OiBhdXRvOw0KICBwYWRkaW5nLXJpZ2h0OiA1cHg7DQp9DQoNCi5wcmVkaWN0aW9uLWl0ZW0gew0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDAsIDAsIDAuMik7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgYm9yZGVyLWxlZnQ6IDNweCBzb2xpZCAjMDBmZmZmOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQoucHJlZGljdGlvbi1pdGVtLnByZWRpY3Rpb24tZXJyb3Igew0KICBib3JkZXItbGVmdC1jb2xvcjogI2ZmNmI2YjsNCn0NCg0KLnByZWRpY3Rpb24tbWF0ZXJpYWwtdGl0bGUgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjEpOw0KICBwYWRkaW5nOiA4cHggMTJweDsNCiAgZm9udC1zaXplOiAxM3B4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBjb2xvcjogIzAwZmZmZjsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHJnYmEoMCwgMjEyLCAyNTUsIDAuMik7DQp9DQoNCi5wcmVkaWN0aW9uLWl0ZW0ucHJlZGljdGlvbi1lcnJvciAucHJlZGljdGlvbi1tYXRlcmlhbC10aXRsZSB7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAxMDcsIDEwNywgMC4xKTsNCiAgY29sb3I6ICNmZjZiNmI7DQogIGJvcmRlci1ib3R0b20tY29sb3I6IHJnYmEoMjU1LCAxMDcsIDEwNywgMC4yKTsNCn0NCg0KLnByZWRpY3Rpb24tbWF0ZXJpYWwtdGl0bGUgaSB7DQogIG1hcmdpbi1yaWdodDogNnB4Ow0KfQ0KDQoubG9hZGluZy1pbmZvIHsNCiAgY29sb3I6ICMwMGZmZmY7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgbWFyZ2luLWxlZnQ6IDEwcHg7DQogIGZvbnQtc3R5bGU6IGl0YWxpYzsNCn0NCg0KLyog6aKE5rWL5a655Zmo5rua5Yqo5p2h5qC35byPICovDQoucHJlZGljdGlvbnMtY29udGFpbmVyOjotd2Via2l0LXNjcm9sbGJhciB7DQogIHdpZHRoOiA0cHg7DQp9DQoNCi5wcmVkaWN0aW9ucy1jb250YWluZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpOw0KICBib3JkZXItcmFkaXVzOiAycHg7DQp9DQoNCi5wcmVkaWN0aW9ucy1jb250YWluZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCiAgYmFja2dyb3VuZDogcmdiYSgwLCAyMTIsIDI1NSwgMC41KTsNCiAgYm9yZGVyLXJhZGl1czogMnB4Ow0KfQ0KDQoucHJlZGljdGlvbnMtY29udGFpbmVyOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7DQogIGJhY2tncm91bmQ6IHJnYmEoMCwgMjEyLCAyNTUsIDAuOCk7DQp9DQoNCi8qIOS4gOmXruS4gOetlOagt+W8jyAqLw0KLnFhLXNlY3Rpb24gew0KICBwYWRkaW5nOiAwOw0KfQ0KDQoucXVlc3Rpb24tc2VjdGlvbiwgLmFuc3dlci1zZWN0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLmFuc3dlci1zZWN0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCn0NCg0KLnFhLWxhYmVsIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KICBmb250LXNpemU6IDEzcHg7DQp9DQoNCi5xdWVzdGlvbi1sYWJlbCB7DQogIGNvbG9yOiAjZmZiOTgwOw0KfQ0KDQouYW5zd2VyLWxhYmVsIHsNCiAgY29sb3I6ICMwMGZmZmY7DQp9DQoNCi5xYS1sYWJlbCBpIHsNCiAgbWFyZ2luLXJpZ2h0OiA2cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLnF1ZXN0aW9uLXRleHQsIC5hbnN3ZXItdGV4dCB7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMC4zKTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAxMnB4IDE1cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjY7DQogIGZvbnQtc2l6ZTogMTNweDsNCiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTsNCiAgd2hpdGUtc3BhY2U6IHByZS13cmFwOw0KICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7DQogIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsNCn0NCg0KLnF1ZXN0aW9uLXRleHQgew0KICBib3JkZXItbGVmdDogM3B4IHNvbGlkICNmZmI5ODA7DQp9DQoNCi5hbnN3ZXItdGV4dCB7DQogIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzAwZmZmZjsNCiAgbWF4LWhlaWdodDogMjAwcHg7DQogIG92ZXJmbG93LXk6IGF1dG87DQogIHBhZGRpbmctcmlnaHQ6IDE4cHg7DQp9DQoNCi8qIOmXrumimOaWh+acrOagt+W8jyAqLw0KLnF1ZXN0aW9uLXRleHQgew0KICBmb250LXN0eWxlOiBpdGFsaWM7DQogIGNvbG9yOiByZ2JhKDI1NSwgMjAwLCAxNTAsIDAuOSk7DQp9DQoNCi8qIOetlOahiOaWh+acrOa7muWKqOadoeagt+W8jyAqLw0KLmFuc3dlci10ZXh0Ojotd2Via2l0LXNjcm9sbGJhciB7DQogIHdpZHRoOiA0cHg7DQp9DQoNCi5hbnN3ZXItdGV4dDo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7DQogIGJvcmRlci1yYWRpdXM6IDJweDsNCn0NCg0KLmFuc3dlci10ZXh0Ojotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogIGJhY2tncm91bmQ6IHJnYmEoMCwgMjEyLCAyNTUsIDAuNSk7DQogIGJvcmRlci1yYWRpdXM6IDJweDsNCn0NCg0KLmFuc3dlci10ZXh0Ojotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7DQogIGJhY2tncm91bmQ6IHJnYmEoMCwgMjEyLCAyNTUsIDAuOCk7DQp9DQoNCi8qIOS7t+agvOi2i+WKv+WNoeeJh+eJueauiuagt+W8jyAqLw0KLnByaWNlLXRyZW5kLWNhcmQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBoZWlnaHQ6IGF1dG87DQogIG1pbi1oZWlnaHQ6IDQwMHB4Ow0KfQ0KDQoucHJpY2UtdHJlbmQtY2FyZCAuY2hhcnQgew0KICBmbGV4LXNocmluazogMDsNCiAgaGVpZ2h0OiAzMDBweCAhaW1wb3J0YW50Ow0KICBtaW4taGVpZ2h0OiAzMDBweDsNCn0NCg0KLnByaWNlLXRyZW5kLWNhcmQgLnByaWNlLXByZWRpY3Rpb24tc2VjdGlvbiB7DQogIGZsZXgtc2hyaW5rOiAwOw0KICBtYXJnaW4tdG9wOiAxNXB4Ow0KICBtYXJnaW4tYm90dG9tOiAwOw0KfQ0KDQouaW52ZW50b3J5LXRvdGFsIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBjb2xvcjogIzAwZmZmZjsNCiAgZm9udC13ZWlnaHQ6IG5vcm1hbDsNCiAgb3BhY2l0eTogMC45Ow0KfQ0KDQovKiDmlrDlop7vvJrku7fmoLzotovlir/lm77mjqfku7bmoLflvI8gKi8NCi5wcmljZS10cmVuZC1jb250cm9scyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgcGFkZGluZzogMTBweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgxNiwgNywgMzMsIDAuNCk7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyMTIsIDI1NSwgMC4yKTsNCn0NCg0KLmxlZnQtY29udHJvbHMsIC5yaWdodC1jb250cm9scyB7DQogIGZsZXg6IDE7DQogIG1heC13aWR0aDogNDUlOw0KfQ0KDQouY3VydmUtbGFiZWwgew0KICBmb250LXNpemU6IDEzcHg7DQogIGNvbG9yOiAjMDBmZmZmOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KfQ0KDQoubGVmdC1jb250cm9scyAuY3VydmUtbGFiZWwgew0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KfQ0KDQoucmlnaHQtY29udHJvbHMgLmN1cnZlLWxhYmVsIHsNCiAgdGV4dC1hbGlnbjogcmlnaHQ7DQp9DQoNCi5kcm9wZG93bi1yb3cgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDEwcHg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5yaWdodC1jb250cm9scyAuZHJvcGRvd24tcm93IHsNCiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCn0NCg0KLmZldGNoLWRhdGEtYnRuLWNvbnRhaW5lciB7DQogIHRleHQtYWxpZ246IHJpZ2h0Ow0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQoubW9kZXJuLWZldGNoLWJ0biB7DQogIGJhY2tncm91bmQ6IHJnYmEoMTM4LCA0MywgMjI2LCAwLjcpOw0KICBib3JkZXI6IG5vbmU7DQogIGJvcmRlci1yYWRpdXM6IDEycHg7DQogIHBhZGRpbmc6IDEwcHggMjBweDsNCiAgY29sb3I6ICNmZmY7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICBib3gtc2hhZG93OiAwIDNweCAxMHB4IHJnYmEoMTM4LCA0MywgMjI2LCAwLjMpOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5tb2Rlcm4tZmV0Y2gtYnRuOmhvdmVyOm5vdCg6ZGlzYWJsZWQpIHsNCiAgYmFja2dyb3VuZDogcmdiYSgxMzgsIDQzLCAyMjYsIDAuOSk7DQogIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsNCiAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDEzOCwgNDMsIDIyNiwgMC41KTsNCn0NCg0KLm1vZGVybi1mZXRjaC1idG46YWN0aXZlIHsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApOw0KfQ0KDQoubW9kZXJuLWZldGNoLWJ0bjpkaXNhYmxlZCB7DQogIG9wYWNpdHk6IDAuNzsNCiAgY3Vyc29yOiBub3QtYWxsb3dlZDsNCiAgdHJhbnNmb3JtOiBub25lOw0KfQ0KDQoubW9kZXJuLWZldGNoLWJ0bi5sb2FkaW5nIHsNCiAgYmFja2dyb3VuZDogcmdiYSgxMzgsIDQzLCAyMjYsIDAuNyk7DQp9DQoNCi5tb2Rlcm4tZmV0Y2gtYnRuIGkgew0KICBtYXJnaW4tcmlnaHQ6IDhweDsNCiAgYW5pbWF0aW9uOiByb3RhdGUgMXMgbGluZWFyIGluZmluaXRlOw0KfQ0KDQpAa2V5ZnJhbWVzIHJvdGF0ZSB7DQogIGZyb20geyB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsgfQ0KICB0byB7IHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7IH0NCn0NCg0KLyogRWxlbWVudCBVSSDkuIvmi4nmoYbmoLflvI/opobnm5YgKi8NCi5wcmljZS10cmVuZC1jb250cm9scyAuZWwtc2VsZWN0IHsNCiAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQgIWltcG9ydGFudDsNCn0NCg0KLnByaWNlLXRyZW5kLWNvbnRyb2xzIC5lbC1zZWxlY3QgLmVsLWlucHV0X19pbm5lciB7DQogIGJhY2tncm91bmQtY29sb3I6ICM0YTFjNWEgIWltcG9ydGFudDsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxMTYsIDc1LCAxNjIsIDAuNSkgIWltcG9ydGFudDsNCiAgY29sb3I6ICNmZmZmZmYgIWltcG9ydGFudDsNCiAgYm9yZGVyLXJhZGl1czogOHB4ICFpbXBvcnRhbnQ7DQogIGZvbnQtc2l6ZTogMTNweCAhaW1wb3J0YW50Ow0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlICFpbXBvcnRhbnQ7DQp9DQoNCi5wcmljZS10cmVuZC1jb250cm9scyAuZWwtc2VsZWN0IC5lbC1pbnB1dF9faW5uZXI6aG92ZXIgew0KICBib3JkZXItY29sb3I6IHJnYmEoMTE2LCA3NSwgMTYyLCAwLjgpICFpbXBvcnRhbnQ7DQogIGJveC1zaGFkb3c6IDAgMCA4cHggcmdiYSgxMTYsIDc1LCAxNjIsIDAuMykgIWltcG9ydGFudDsNCn0NCg0KLnByaWNlLXRyZW5kLWNvbnRyb2xzIC5lbC1zZWxlY3QgLmVsLWlucHV0X19pbm5lcjpmb2N1cyB7DQogIGJvcmRlci1jb2xvcjogIzc2NGJhMiAhaW1wb3J0YW50Ow0KICBib3gtc2hhZG93OiAwIDAgMTJweCByZ2JhKDExNiwgNzUsIDE2MiwgMC41KSAhaW1wb3J0YW50Ow0KfQ0KDQoucHJpY2UtdHJlbmQtY29udHJvbHMgLmVsLXNlbGVjdCAuZWwtaW5wdXRfX2lubmVyOjpwbGFjZWhvbGRlciB7DQogIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNykgIWltcG9ydGFudDsNCn0NCg0KLnByaWNlLXRyZW5kLWNvbnRyb2xzIC5lbC1zZWxlY3QgLmVsLWlucHV0X19zdWZmaXggew0KICBjb2xvcjogI2ZmZmZmZiAhaW1wb3J0YW50Ow0KfQ0KDQoucHJpY2UtdHJlbmQtY29udHJvbHMgLmVsLXNlbGVjdCAuZWwtaW5wdXRfX3N1ZmZpeCBpIHsNCiAgY29sb3I6ICNmZmZmZmYgIWltcG9ydGFudDsNCn0NCg0KLnByaWNlLXRyZW5kLWNvbnRyb2xzIC5lbC10YWcgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDExNiwgNzUsIDE2MiwgMC42KSAhaW1wb3J0YW50Ow0KICBib3JkZXItY29sb3I6IHJnYmEoMTE2LCA3NSwgMTYyLCAwLjgpICFpbXBvcnRhbnQ7DQogIGNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7DQogIGJvcmRlci1yYWRpdXM6IDZweCAhaW1wb3J0YW50Ow0KfQ0KDQoucHJpY2UtdHJlbmQtY29udHJvbHMgLmVsLXRhZyAuZWwtdGFnX19jbG9zZSB7DQogIGNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7DQp9DQoNCi5wcmljZS10cmVuZC1jb250cm9scyAuZWwtdGFnIC5lbC10YWdfX2Nsb3NlOmhvdmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOebuOS8vOeJqeaWmeWMuuWfn+agt+W8jyAqLw0KLnNpbWlsYXItbWF0ZXJpYWxzLXNlY3Rpb24gew0KICBtYXJnaW46IDIwcHggMDsNCiAgcGFkZGluZzogMTVweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgxNiwgNywgMzMsIDAuNik7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyMTIsIDI1NSwgMC4yKTsNCn0NCg0KLnNpbWlsYXItbWF0ZXJpYWxzLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgZmxleC13cmFwOiB3cmFwOw0KICBnYXA6IDhweDsNCn0NCg0KLnNpbWlsYXItbWF0ZXJpYWxzLWhlYWRlciBpIHsNCiAgY29sb3I6ICMwMGZmZmY7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi5zZWN0aW9uLXRpdGxlIHsNCiAgY29sb3I6ICMwMGZmZmY7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5sb2FkaW5nLWluZm8gew0KICBjb2xvcjogIzAwZmZmZjsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBtYXJnaW4tbGVmdDogMTBweDsNCiAgZm9udC1zdHlsZTogaXRhbGljOw0KfQ0KDQouc2ltaWxhci1tYXRlcmlhbHMtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjIpOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIHBhZGRpbmc6IDEwcHg7DQogIG92ZXJmbG93LXg6IGF1dG87DQp9DQoNCi5tYXRlcmlhbHMtdGFibGUgew0KICB3aWR0aDogMTAwJTsNCiAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsNCiAgZm9udC1zaXplOiAxM3B4Ow0KfQ0KDQoubWF0ZXJpYWxzLXRhYmxlIHRoIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAyMTIsIDI1NSwgMC4xKTsNCiAgY29sb3I6ICMwMGZmZmY7DQogIHBhZGRpbmc6IDhweCAxMnB4Ow0KICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICBib3JkZXItYm90dG9tOiAycHggc29saWQgcmdiYSgwLCAyMTIsIDI1NSwgMC4zKTsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCn0NCg0KLm1hdGVyaWFscy10YWJsZSB0ZCB7DQogIHBhZGRpbmc6IDhweCAxMnB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpOw0KICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpOw0KfQ0KDQoubWF0ZXJpYWwtcm93IHsNCiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjNzIGVhc2U7DQp9DQoNCi5tYXRlcmlhbC1yb3c6aG92ZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjA1KTsNCn0NCg0KLnJhbmstY2VsbCB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgd2lkdGg6IDYwcHg7DQp9DQoNCi5yYW5rLWJhZGdlIHsNCiAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICBwYWRkaW5nOiA0cHggOHB4Ow0KICBib3JkZXItcmFkaXVzOiA1MCU7DQogIGNvbG9yOiAjZmZmOw0KICBmb250LXdlaWdodDogYm9sZDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBtaW4td2lkdGg6IDIwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLnJhbmstZmlyc3Qgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmZkNzAwLCAjZmZiMzQ3KTsNCiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMjU1LCAyMTUsIDAsIDAuMyk7DQp9DQoNCi5yYW5rLXNlY29uZCB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNjMGMwYzAsICNhOGE4YTgpOw0KICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgxOTIsIDE5MiwgMTkyLCAwLjMpOw0KfQ0KDQoucmFuay10aGlyZCB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNjZDdmMzIsICNiODg2MGIpOw0KICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgyMDUsIDEyNywgNTAsIDAuMyk7DQp9DQoNCi5yYW5rLWRlZmF1bHQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDEzOCwgNDMsIDIyNiwgMC43KTsNCn0NCg0KLm1hdGVyaWFsLW5hbWUsIC5jb21wYXJlLW1hdGVyaWFsLW5hbWUgew0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogI2ZmZjsNCn0NCg0KLmNvbXBhcmUtbWF0ZXJpYWwtbmFtZSB7DQogIGNvbG9yOiAjMDBmZmZmOw0KfQ0KDQouc2NvcmUtY2VsbCB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgd2lkdGg6IDEyMHB4Ow0KICBtaW4td2lkdGg6IDEyMHB4Ow0KfQ0KDQouc2NvcmUtdmFsdWUgew0KICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogIHBhZGRpbmc6IDJweCA2cHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMjEyLCAyNTUsIDAuMik7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgY29sb3I6ICMwMGZmZmY7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KfQ0KDQouc2NvcmUtZGVzYyB7DQogIGNvbG9yOiAjZmZiOTgwOw0KICBmb250LXN0eWxlOiBpdGFsaWM7DQp9DQoNCi5jYXRlZ29yeS1jZWxsIHsNCiAgY29sb3I6ICM1ZmQ4YjY7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5zaW1pbGFyLW1hdGVyaWFscy1wbGFjZWhvbGRlciB7DQogIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNSk7DQogIGZvbnQtc3R5bGU6IGl0YWxpYzsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBmb250LXNpemU6IDEycHg7DQp9DQoNCi5zaW1pbGFyLW1hdGVyaWFscy1ncm91cCB7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi5zaW1pbGFyLW1hdGVyaWFscy1ncm91cDpsYXN0LWNoaWxkIHsNCiAgbWFyZ2luLWJvdHRvbTogMDsNCn0NCg0KLmdyb3VwLXRpdGxlIHsNCiAgY29sb3I6ICMwMGZmZmY7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBmb250LXNpemU6IDE0cHg7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQogIHBhZGRpbmc6IDhweCAxMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDAsIDIxMiwgMjU1LCAwLjEpOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzAwZmZmZjsNCn0NCg0KLnByaWNlLXR5cGUtY2VsbCB7DQogIGNvbG9yOiAjZTg3OWVkOw0KICBmb250LXNpemU6IDExcHg7DQogIG1heC13aWR0aDogMTIwcHg7DQogIHdvcmQtd3JhcDogYnJlYWstd29yZDsNCn0NCg0KLmFsZ29yaXRobS1kZXNjIHsNCiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC43KTsNCiAgZm9udC1zaXplOiAxMXB4Ow0KICBmb250LXN0eWxlOiBpdGFsaWM7DQogIG1hcmdpbi1sZWZ0OiA4cHg7DQp9DQoNCi5hY3Rpb24tY2VsbCB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgd2lkdGg6IDEwMHB4Ow0KfQ0KDQoudmlldy1jb21wYXJpc29uLWJ0biB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7DQogIGJvcmRlcjogbm9uZTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBwYWRkaW5nOiA4cHggMTZweDsNCiAgY29sb3I6ICNmZmY7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KICBkaXNwbGF5OiBpbmxpbmUtZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiA0cHg7DQogIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogIG1pbi13aWR0aDogNzBweDsNCn0NCg0KLnZpZXctY29tcGFyaXNvbi1idG46aG92ZXIgew0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTFweCk7DQogIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjQpOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNzY0YmEyIDAlLCAjNjY3ZWVhIDEwMCUpOw0KfQ0KDQoudmlldy1jb21wYXJpc29uLWJ0bjphY3RpdmUgew0KICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7DQp9DQoNCi52aWV3LWNvbXBhcmlzb24tYnRuIGkgew0KICBmb250LXNpemU6IDEzcHg7DQp9DQoNCi8qIOWvueavlOW8ueahhuagt+W8jyAqLw0KLmNvbXBhcmlzb24tZGlhbG9nIC5lbC1kaWFsb2cgew0KICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMTkxOTcwLCAjNEIwMDgyLCAjODAwMDgwKTsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgwLCAyMTIsIDI1NSwgMC4zKTsNCn0NCg0KLmNvbXBhcmlzb24tZGlhbG9nIC5lbC1kaWFsb2dfX2hlYWRlciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMzMsIDEwLCA1NiwgMC45KSwgcmdiYSgwLCAyMTIsIDI1NSwgMC4yKSk7DQogIHBhZGRpbmc6IDIwcHggMjRweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHJnYmEoMCwgMjEyLCAyNTUsIDAuMyk7DQp9DQoNCi5jb21wYXJpc29uLWRpYWxvZyAuZWwtZGlhbG9nX190aXRsZSB7DQogIGNvbG9yOiAjMDBmZmZmOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIHRleHQtc2hhZG93OiAxcHggMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuNSk7DQp9DQoNCi5jb21wYXJpc29uLWRpYWxvZyAuZWwtZGlhbG9nX19oZWFkZXJidG4gLmVsLWRpYWxvZ19fY2xvc2Ugew0KICBjb2xvcjogIzAwZmZmZjsNCiAgZm9udC1zaXplOiAyMHB4Ow0KICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOw0KfQ0KDQouY29tcGFyaXNvbi1kaWFsb2cgLmVsLWRpYWxvZ19faGVhZGVyYnRuIC5lbC1kaWFsb2dfX2Nsb3NlOmhvdmVyIHsNCiAgY29sb3I6ICNmZmY7DQogIHRleHQtc2hhZG93OiAwIDAgMTBweCByZ2JhKDAsIDIxMiwgMjU1LCAwLjgpOw0KfQ0KDQouY29tcGFyaXNvbi1kaWFsb2cgLmVsLWRpYWxvZ19fYm9keSB7DQogIHBhZGRpbmc6IDA7DQogIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50Ow0KfQ0KDQouY29tcGFyaXNvbi1jb250ZW50IHsNCiAgcGFkZGluZzogMjBweDsNCiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7DQp9DQoNCi5jb21wYXJpc29uLWhlYWRlciB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5jb21wYXJpc29uLXRpdGxlIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGdhcDogMTVweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNjAwOw0KfQ0KDQouYmFzZS1tYXRlcmlhbCB7DQogIGNvbG9yOiAjMDBmZmZmOw0KICBwYWRkaW5nOiA4cHggMTZweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAyMTIsIDI1NSwgMC4yKTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjUpOw0KICB0ZXh0LXNoYWRvdzogMXB4IDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjUpOw0KfQ0KDQoudnMtdGV4dCB7DQogIGNvbG9yOiAjZmZmOw0KICBmb250LXNpemU6IDE0cHg7DQogIGZvbnQtd2VpZ2h0OiBub3JtYWw7DQogIHRleHQtc2hhZG93OiAxcHggMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuNSk7DQp9DQoNCi5jb21wYXJlLW1hdGVyaWFsIHsNCiAgY29sb3I6ICNlYTdjY2M7DQogIHBhZGRpbmc6IDhweCAxNnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDIzNCwgMTI0LCAyMDQsIDAuMik7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyMzQsIDEyNCwgMjA0LCAwLjUpOw0KICB0ZXh0LXNoYWRvdzogMXB4IDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjUpOw0KfQ0KDQouc2ltaWxhcml0eS1pbmZvIHsNCiAgY29sb3I6ICNmZmI5ODA7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgZm9udC13ZWlnaHQ6IG5vcm1hbDsNCiAgcGFkZGluZzogNHB4IDEycHg7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMjU1LCAxODUsIDEyOCwgMC4yKTsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMTg1LCAxMjgsIDAuNCk7DQogIHRleHQtc2hhZG93OiAxcHggMXB4IDJweCByZ2JhKDAsIDAsIDAsIDAuNSk7DQp9DQoNCi5jb21wYXJpc29uLWNoYXJ0LWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMzMsIDEwLCA1NiwgMC43KTsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjMpOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBib3gtc2hhZG93OiAwIDAgMjBweCByZ2JhKDAsIDIxMiwgMjU1LCAwLjIpOw0KfQ0KDQouY29tcGFyaXNvbi1jaGFydCB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDUwMHB4Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi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file": "index.vue", "sourceRoot": "src/views/purchaseDashboardStock", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <div class=\"dashboard-header\">\r\n      <h1>采购库存看板</h1>\r\n      <div class=\"header-controls\">\r\n        <div class=\"fullscreen-btn\" @click=\"toggleFullscreen\" :title=\"isFullscreen ? '退出全屏' : '进入全屏'\">\r\n          <i :class=\"isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'\"></i>\r\n        </div>\r\n        <div class=\"time-filter\">\r\n          <button\r\n            v-for=\"filter in timeFilters\"\r\n            :key=\"filter.id\"\r\n            :class=\"['time-filter-btn', { active: filter.id === activeFilter }]\"\r\n            @click=\"handleTimeFilterChange(filter.id, filter.value)\"\r\n          >\r\n            {{ filter.label }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"dashboard-grid\">\r\n      <!-- 第一行：中心仓库月度库存金额 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          中心仓库月度库存金额\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedYear\"\r\n              @change=\"handleYearChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部年份</option>\r\n              <option v-for=\"year in availableYears\" :key=\"year\" :value=\"year\">\r\n                {{ year }}年\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedMaterialType\"\r\n              @change=\"handleMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">总和</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"monthlyInventoryChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第一行：机旁库当前库存 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          机旁库当前库存\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedFactoryDep\"\r\n              @change=\"handleFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedFactoryMaterialType\"\r\n              @change=\"handleFactoryMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"factoryStockChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第二行：矿焦煤实时库存 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          <div style=\"display: flex; align-items: center; justify-content: space-between; width: 100%;\">\r\n            <div style=\"display: flex; align-items: center; gap: 15px;\">\r\n              <span>矿焦煤实时库存</span>\r\n              <span class=\"inventory-total\">\r\n                合计: {{ calculateCokingCoalTotal() }}万吨\r\n              </span>\r\n            </div>\r\n            <div class=\"chart-filter-dropdown-container\">\r\n              <select\r\n                v-model=\"selectedCokingCoalType\"\r\n                @change=\"handleCokingCoalTypeChange\"\r\n              >\r\n                <option value=\"\">全部</option>\r\n                <option value=\"矿料类\">矿料类</option>\r\n                <option value=\"焦炭\">焦炭</option>\r\n                <option value=\"煤焦类\">煤焦类</option>\r\n                <option value=\"合金类\">合金类</option>\r\n                <option value=\"辅助类/电极\">辅助类/电极</option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n        </h2>\r\n        <div class=\"chart\" style=\"display: flex; height: 100%;\">\r\n          <div id=\"cokingCoalPieChart\" style=\"width: 25%; height: 100%;\"></div>\r\n          <div id=\"cokingCoalLineChart\" style=\"width: 75%; height: 100%;\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 物料入库统计 -->\r\n      <div class=\"card material-chart-card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          物料入库统计\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedMaterialCategory\"\r\n              @change=\"handleMaterialCategoryChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"1\">大类</option>\r\n              <option value=\"2\">中类</option>\r\n              <option value=\"3\">细类</option>\r\n              <option value=\"4\">叶类</option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedMaterialItem\"\r\n              @change=\"handleMaterialItemChange\"\r\n            >\r\n              <option value=\"\">全部</option>\r\n              <option v-for=\"item in materialItemOptions\" :key=\"item.itemId\" :value=\"item.itemId\">\r\n                {{ item.itemName }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"materialStatisticsChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- 物料价格对比弹框 -->\r\n    <el-dialog\r\n      title=\"物料价格趋势对比分析\"\r\n      :visible.sync=\"comparisonDialogVisible\"\r\n      width=\"90%\"\r\n      :before-close=\"closeComparisonDialog\"\r\n      custom-class=\"comparison-dialog\"\r\n    >\r\n      <div class=\"comparison-content\">\r\n        <div class=\"comparison-header\">\r\n          <div class=\"comparison-title\">\r\n            <span class=\"base-material\">{{ currentComparison.itemName }}</span>\r\n            <span class=\"vs-text\">VS</span>\r\n            <span class=\"compare-material\">{{ currentComparison.compareItemName }}</span>\r\n            <span class=\"similarity-info\">相似度：{{ currentComparison.score }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"comparison-chart-container\">\r\n          <div\r\n            id=\"comparisonChart\"\r\n            class=\"comparison-chart\"\r\n            v-loading=\"comparisonChartLoading\"\r\n            element-loading-text=\"正在加载对比数据...\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport chartMethods from './chartMethods'\r\nimport extendedChartMethods from './chartMethodsExtended'\r\nimport screenfull from 'screenfull'\r\nimport { showYearlyAmount, showRealTimeAmount, showCokingCoalAmount, showKeyIndicators, showItemTypeList, showMaterialList, showData, showSuppList, showHighFrequencyMaterialList, showPurchaseSuppRisk, getMaterialFuturePrice, getMaterialNameList, getPurchasePriceAndStore, getMaterialNameListFromNewTables, getPurchasePriceAndStoreFromNewTables } from '@/api/purchaseDashboard/purchaseDashboard'\r\nimport { listSimilarByItemNames } from '@/api/purchase/similar'\r\nimport { getDepNameList, getListMonthly } from '@/api/purchase/purdchaseFactoryStock'\r\n\r\nexport default {\r\n  name: 'PurchaseDashboard',\r\n  mixins: [chartMethods, extendedChartMethods],\r\n  data() {\r\n    return {\r\n      // 时间过滤器选项\r\n      timeFilters: [\r\n        { id: 'filter-3m', label: '近三个月', value: 1 },\r\n        { id: 'filter-6m', label: '近六个月', value: 2 },\r\n        { id: 'filter-1y', label: '近一年', value: 3 }\r\n      ],\r\n      activeFilter: 'filter-1y',\r\n      currentDimensionType: 3,\r\n\r\n      // 数据\r\n      dashboardData: {},\r\n      purchaseStats: {},\r\n\r\n      // 下拉选项\r\n      topSuppliersOptions: [],\r\n\r\n      // 选中的过滤器值\r\n      selectedTopSuppliersFilter: '',\r\n      selectedOrderType: 'TOP', // 排序类型，默认为TOP\r\n\r\n      // 图表实例\r\n      chartInstances: {},\r\n\r\n      // 原始数据备份\r\n      originalTopSuppliersData: [],\r\n\r\n      // 库存图表相关\r\n      selectedYear: '',\r\n      selectedMaterialType: '',\r\n      availableYears: (() => {\r\n        const currentYear = new Date().getFullYear()\r\n        const years = []\r\n        for (let year = 2020; year <= currentYear; year++) {\r\n          years.push(year.toString())\r\n        }\r\n        return years\r\n      })(),\r\n      yearlyInventoryData: [],\r\n      realTimeInventoryData: [],\r\n      cokingCoalInventoryData: [],\r\n\r\n      // 矿焦煤库存图表相关\r\n      selectedCokingCoalType: '', // 选中的矿焦煤类型，默认为空（全部）\r\n\r\n      // 物料入库统计相关\r\n      selectedMaterialCategory: '1',\r\n      selectedMaterialItem: '',\r\n      materialItemOptions: [],\r\n      materialStatisticsData: [],\r\n\r\n      // 高频采购物料相关\r\n      selectedCodeType: 'ALL',\r\n      selectedItemType: 'CLASS3',\r\n      highFrequencyMaterialData: [],\r\n\r\n      // 供应商风险数据\r\n      supplierRiskData: [],\r\n\r\n      // AI价格预测相关\r\n      pricePredictions: [], // 改为数组，支持多个物料的预测\r\n      predictionLoading: false,\r\n\r\n      // 物料价格趋势图相关\r\n      materialNameOptions: [],\r\n      selectedMaterial: 'PB块',\r\n      selectedMaterialCategory: '1', // 默认选择矿石\r\n      priceAndStoreData: null,\r\n\r\n      // 新的价格趋势图相关属性\r\n      // 采购量曲线\r\n      purchaseAmountCategories: [99], // 默认选择全部\r\n      selectedPurchaseAmountMaterials: [],\r\n      purchaseAmountMaterialOptions: [],\r\n\r\n      // 市场价曲线\r\n      marketPriceCategories: [99], // 默认选择全部\r\n      selectedMarketPriceMaterials: [],\r\n      marketPriceMaterialOptions: [],\r\n\r\n      // 获取数据状态\r\n      fetchingPriceData: false,\r\n      newPriceAndStoreData: null,\r\n\r\n      // 初始化标志\r\n      hasInitializedPriceChart: false,\r\n\r\n      // 相似物料数据\r\n      similarMaterialsData: [],\r\n      similarMaterialsLoading: false,\r\n\r\n      // 对比弹框相关\r\n      comparisonDialogVisible: false,\r\n      comparisonChartLoading: false,\r\n      currentComparison: {},\r\n      comparisonChartInstance: null,\r\n      comparisonPriceData: null,\r\n\r\n      // 机旁库当前库存相关\r\n      selectedFactoryDep: '', // 选中的分厂\r\n      selectedFactoryMaterialType: '', // 选中的物料类型\r\n      factoryDepOptions: [], // 分厂选项列表\r\n      factoryStockData: [] // 机旁库存数据\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isFullscreen() {\r\n      return this.$store.state.app.isFullscreenMode\r\n    },\r\n\r\n    // 按itemName、category、priceType联合索引分组相似物料数据\r\n    groupedSimilarMaterials() {\r\n      const grouped = {}\r\n      this.similarMaterialsData.forEach(item => {\r\n        // 创建联合索引key\r\n        const groupKey = `${item.itemName}_${item.category}_${item.priceType}`\r\n        const displayKey = `${item.itemName} (${this.getCategoryName(item.category)} - ${this.getPriceTypeName(item.priceType)})`\r\n\r\n        if (!grouped[displayKey]) {\r\n          grouped[displayKey] = {\r\n            groupKey: groupKey,\r\n            items: []\r\n          }\r\n        }\r\n        grouped[displayKey].items.push(item)\r\n      })\r\n\r\n      // 对每个组内的数据按排名排序\r\n      Object.keys(grouped).forEach(key => {\r\n        grouped[key].items.sort((a, b) => a.rank - b.rank)\r\n      })\r\n\r\n      return grouped\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.checkEchartsAvailability()\r\n    this.fetchDashboardData(3)\r\n    this.fetchYearlyInventoryData()\r\n    this.fetchRealTimeInventoryData()\r\n    this.fetchCokingCoalInventoryData()\r\n    // 初始化物料入库统计的下拉框选项和数据\r\n    this.updateMaterialItemOptions().then(() => {\r\n      this.fetchMaterialStatisticsData()\r\n    })\r\n    // 初始化高频采购物料数据\r\n    this.fetchHighFrequencyMaterialData()\r\n    // 初始化供应商风险数据\r\n    this.fetchSupplierRiskData()\r\n\r\n    // 初始化新的物料名称列表（会自动触发默认选中PB块和数据获取）\r\n    this.fetchPurchaseAmountMaterialList()\r\n    this.fetchMarketPriceMaterialList()\r\n\r\n    // 初始化机旁库存数据\r\n    this.fetchFactoryDepOptions()\r\n\r\n    this.setupResizeObserver()\r\n    this.initFullscreenListener()\r\n\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.resizeAllCharts)\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 清理定时器和事件监听器\r\n    this.clearAllIntervals()\r\n    this.removeFullscreenListener()\r\n    window.removeEventListener('resize', this.resizeAllCharts)\r\n\r\n    // 确保退出全屏模式\r\n    this.$store.dispatch('app/setFullscreenMode', false)\r\n  },\r\n\r\n  methods: {\r\n    // 初始化全屏监听器\r\n    initFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.on('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 移除全屏监听器\r\n    removeFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.off('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 处理全屏状态变化\r\n    handleFullscreenChange() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        const isFullscreen = screenfull.isFullscreen\r\n        this.$store.dispatch('app/setFullscreenMode', isFullscreen)\r\n\r\n        // 全屏状态变化后，重新调整图表大小\r\n        this.$nextTick(() => {\r\n          setTimeout(() => {\r\n            this.resizeAllCharts()\r\n          }, 300) // 给布局变化一些时间\r\n        })\r\n      }\r\n    },\r\n\r\n    // API调用方法\r\n    async getDashboardData(dimensionType) {\r\n      return await showData({ dimensionType: dimensionType })\r\n    },\r\n\r\n    async getItemTypeList(itemType) {\r\n      return await showItemTypeList({ itemType: itemType })\r\n    },\r\n\r\n    async getMaterialList(params) {\r\n      return await showMaterialList(params)\r\n    },\r\n\r\n    async getSupplierList(params) {\r\n      return await showSuppList(params)\r\n    },\r\n\r\n    async getYearlyAmount(params) {\r\n      return await showYearlyAmount(params)\r\n    },\r\n\r\n    async getRealTimeAmount() {\r\n      return await showRealTimeAmount()\r\n    },\r\n\r\n    async getCokingCoalAmount() {\r\n      return await showCokingCoalAmount()\r\n    },\r\n\r\n    async getKeyIndicators(params) {\r\n      return await showKeyIndicators(params)\r\n    },\r\n\r\n    async getHighFrequencyMaterialList(params) {\r\n      return await showHighFrequencyMaterialList(params)\r\n    },\r\n\r\n    async getPurchaseSuppRisk(params) {\r\n      return await showPurchaseSuppRisk(params)\r\n    },\r\n\r\n    // 根据dimensionType获取timeFlag\r\n    getTimeFlagByDimensionType(dimensionType) {\r\n      switch(dimensionType) {\r\n        case 1: return '03' // 近三个月\r\n        case 2: return '06' // 近六个月\r\n        case 3: return '12' // 近一年\r\n        default: return '03'\r\n      }\r\n    },\r\n\r\n    // 检查ECharts可用性\r\n    checkEchartsAvailability() {\r\n      if (!echarts) {\r\n        console.error('ECharts库未能加载，使用备用显示方式')\r\n        document.querySelectorAll('.chart').forEach(el => {\r\n          el.innerHTML = '<div class=\"chart-placeholder\">图表加载失败</div>'\r\n        })\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n\r\n    // 获取仪表板数据\r\n    async fetchDashboardData(dimensionTypeParam = 1) {\r\n      this.currentDimensionType = dimensionTypeParam\r\n\r\n      // 清除所有定时器\r\n      this.clearAllIntervals()\r\n\r\n      try {\r\n        // 并行获取仪表板数据和关键指标数据\r\n        const [dashboardResponse, keyIndicatorsResponse] = await Promise.all([\r\n          this.getDashboardData(dimensionTypeParam),\r\n          this.getKeyIndicators({ dimensionType: dimensionTypeParam })\r\n        ])\r\n\r\n        // 处理仪表板数据\r\n        if (dashboardResponse && dashboardResponse.data) {\r\n          this.dashboardData = dashboardResponse.data\r\n          console.log('获取仪表板数据成功:', this.dashboardData)\r\n        } else {\r\n          console.error('API数据格式不正确或缺少data字段', dashboardResponse)\r\n          this.showErrorMessage('API数据格式不正确或缺少data字段')\r\n        }\r\n\r\n        // 处理关键指标数据\r\n        if (keyIndicatorsResponse && keyIndicatorsResponse.data) {\r\n          this.purchaseStats = keyIndicatorsResponse.data || {}\r\n          console.log('获取关键指标数据成功:', this.purchaseStats)\r\n        } else {\r\n          console.error('获取关键指标数据失败', keyIndicatorsResponse)\r\n          this.purchaseStats = {}\r\n        }\r\n\r\n        this.initAllCharts()\r\n      } catch (error) {\r\n        console.error('API请求或数据处理失败', error)\r\n        this.showErrorMessage('数据加载失败: ' + error.message)\r\n      }\r\n    },\r\n\r\n    // 显示错误信息\r\n    showErrorMessage(message) {\r\n      document.querySelectorAll('.chart').forEach(chart => {\r\n        chart.innerHTML = `<div class=\"chart-placeholder\">${message}</div>`\r\n      })\r\n    },\r\n\r\n    // 时间过滤器变化处理\r\n    handleTimeFilterChange(filterId, dimensionType) {\r\n      this.activeFilter = filterId\r\n      this.currentDimensionType = dimensionType\r\n      console.log('选择的时间范围:', filterId, '维度:', dimensionType)\r\n\r\n      this.clearAllIntervals()\r\n      this.fetchDashboardData(dimensionType)\r\n      // 同时更新高频物料数据\r\n      this.fetchHighFrequencyMaterialData()\r\n      // 同时更新供应商风险数据\r\n      this.fetchSupplierRiskData()\r\n      // 同时更新物料入库统计数据\r\n      this.fetchMaterialStatisticsData()\r\n      // 注意：价格趋势数据只在用户主动点击按钮时获取，不在时间过滤器变化时自动获取\r\n\r\n      // 同时更新新的物料列表（用于下拉框选项），但不会自动触发数据获取\r\n      this.fetchPurchaseAmountMaterialList()\r\n      this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 清除所有定时器\r\n    clearAllIntervals() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance && instance.intervalId) {\r\n          clearInterval(instance.intervalId)\r\n          instance.intervalId = null\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重新调整所有图表大小\r\n    resizeAllCharts() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance) {\r\n          try {\r\n            instance.resize()\r\n          } catch(err) {\r\n            console.error('图表大小调整失败:', err)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 初始化所有图表\r\n    initAllCharts() {\r\n      console.log('initAllCharts started')\r\n      try {\r\n        // 注意：实时库存图表和矿焦煤库存图表会在各自数据获取完成后单独初始化\r\n        // 注意：月度库存金额图表会在fetchYearlyInventoryData完成后单独初始化\r\n        // 注意：物料入库统计图表会在fetchMaterialStatisticsData完成后单独初始化\r\n        // 注意：机旁库存图表会在fetchFactoryStockData完成后单独初始化\r\n\r\n        // 初始化物料词云图\r\n        this.initMaterialCloud()\r\n\r\n        // 初始化TOP供应商图\r\n        this.initTopSuppliersChart()\r\n        this.populateItemDropdown('topSuppliersFilter', 1, 'topSuppliersOptions')\r\n\r\n        // 注意：供应商风险图表会在fetchSupplierRiskData完成后单独初始化\r\n\r\n        // 注意：采购价格趋势图会在fetchPriceAndStoreData完成后单独初始化\r\n\r\n        console.log('所有图表初始化完成')\r\n      } catch (err) {\r\n        console.error('图表初始化主流程失败:', err)\r\n        this.showErrorMessage('图表初始化失败: ' + err.message)\r\n      }\r\n    },\r\n\r\n    // 填充物料类型下拉框\r\n    async populateItemDropdown(selectElementId, itemType, dataPropertyName) {\r\n      try {\r\n        const response = await this.getItemTypeList(itemType)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this[dataPropertyName] = response.data\r\n        } else {\r\n          console.error(`Invalid data format from showItemTypeList for itemType ${itemType}:`, response)\r\n          this[dataPropertyName] = []\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error fetching item types for ${selectElementId}:`, error)\r\n        this[dataPropertyName] = []\r\n      }\r\n    },\r\n\r\n    // 下拉框变化处理方法\r\n    async handleTopSuppliersFilterChange() {\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async handleOrderTypeChange() {\r\n      console.log('排序类型变化:', this.selectedOrderType)\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async refreshTopSuppliersChart() {\r\n      console.log(`Top supplier filter selected item ID: ${this.selectedTopSuppliersFilter}, orderType: ${this.selectedOrderType}`)\r\n      const myChart = this.chartInstances.topSuppliersChart\r\n      if (!myChart) {\r\n        console.error(\"TOP10供应商图表实例未找到\")\r\n        return\r\n      }\r\n\r\n      if (myChart.intervalId) {\r\n        clearInterval(myChart.intervalId)\r\n        myChart.intervalId = null\r\n      }\r\n\r\n      if (!this.selectedTopSuppliersFilter || this.selectedTopSuppliersFilter === \"\") {\r\n        // 使用原始数据，但需要根据orderType重新获取\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n            newSupplierData = this.originalTopSuppliersData\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          this.renderAndPaginateTopSuppliers(myChart, this.originalTopSuppliersData)\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      } else {\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            itemId: this.selectedTopSuppliersFilter,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          document.getElementById('topSuppliersChart').innerHTML = '<div class=\"chart-placeholder\">供应商数据加载失败</div>'\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      }\r\n    },\r\n\r\n    // 设置大小调整观察器\r\n    setupResizeObserver() {\r\n      const resizeObserver = new ResizeObserver(entries => {\r\n        for (let entry of entries) {\r\n          const charts = entry.target.querySelectorAll('.chart')\r\n          charts.forEach(chart => {\r\n            if (chart.id) {\r\n              const instance = echarts.getInstanceByDom(document.getElementById(chart.id))\r\n              if (instance) {\r\n                instance.resize()\r\n              }\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      document.querySelectorAll('.card').forEach(card => {\r\n        resizeObserver.observe(card)\r\n      })\r\n    },\r\n\r\n    toggleFullscreen() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.toggle()\r\n      } else {\r\n        this.$message({\r\n          message: '您的浏览器不支持全屏功能',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n\r\n    async handleYearChange() {\r\n      console.log('年份变化:', this.selectedYear)\r\n      await this.fetchYearlyInventoryData()\r\n    },\r\n\r\n    async handleMaterialTypeChange() {\r\n      console.log('物料类型变化:', this.selectedMaterialType)\r\n      await this.fetchYearlyInventoryData()\r\n    },\r\n\r\n    // 获取年度库存数据\r\n    async fetchYearlyInventoryData() {\r\n      try {\r\n        const params = {}\r\n\r\n        // 只有当materialType不为空时才传递该参数\r\n        if (this.selectedMaterialType && this.selectedMaterialType !== '') {\r\n          params.materialType = this.selectedMaterialType\r\n        }\r\n\r\n        // 如果选择了具体年份，只查询该年份，否则查询所有年份\r\n        if (this.selectedYear) {\r\n          params.yearList = [this.selectedYear]\r\n        } else {\r\n          params.yearList = this.availableYears\r\n        }\r\n\r\n        console.log('fetchYearlyInventoryData - 请求参数:', params)\r\n        const response = await this.getYearlyAmount(params)\r\n        console.log('fetchYearlyInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.yearlyInventoryData = response.data || []\r\n          console.log('fetchYearlyInventoryData - 设置的数据:', this.yearlyInventoryData)\r\n        } else {\r\n          // 使用模拟数据\r\n          this.yearlyInventoryData = this.getMockYearlyData()\r\n          console.log('fetchYearlyInventoryData - 使用模拟数据:', this.yearlyInventoryData)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取年度库存数据失败，使用模拟数据:', error)\r\n        // 使用模拟数据\r\n        this.yearlyInventoryData = this.getMockYearlyData()\r\n      }\r\n\r\n      // 重新初始化图表\r\n      this.initMonthlyInventoryChart()\r\n    },\r\n\r\n    // 生成模拟数据\r\n    getMockYearlyData() {\r\n      return [\r\n        {\r\n          year: '2023',\r\n          monthlyResultVoList: [\r\n            { monthIndex: 1, amount: 1200.50 },\r\n            { monthIndex: 2, amount: 1350.75 },\r\n            { monthIndex: 3, amount: 1180.20 },\r\n            { monthIndex: 4, amount: 1420.30 },\r\n            { monthIndex: 5, amount: 1380.90 },\r\n            { monthIndex: 6, amount: 1520.40 },\r\n            { monthIndex: 7, amount: 1650.60 },\r\n            { monthIndex: 8, amount: 1480.85 },\r\n            { monthIndex: 9, amount: 1390.25 },\r\n            { monthIndex: 10, amount: 1610.70 },\r\n            { monthIndex: 11, amount: 1580.35 },\r\n            { monthIndex: 12, amount: 1720.95 }\r\n          ]\r\n        },\r\n        {\r\n          year: '2024',\r\n          monthlyResultVoList: [\r\n            { monthIndex: 1, amount: 1320.80 },\r\n            { monthIndex: 2, amount: 1450.60 },\r\n            { monthIndex: 3, amount: 1280.40 },\r\n            { monthIndex: 4, amount: 1540.70 },\r\n            { monthIndex: 5, amount: 1480.20 },\r\n            { monthIndex: 6, amount: 1620.50 },\r\n            { monthIndex: 7, amount: 1750.30 },\r\n            { monthIndex: 8, amount: 1580.90 },\r\n            { monthIndex: 9, amount: 1490.60 },\r\n            { monthIndex: 10, amount: 1710.40 },\r\n            { monthIndex: 11, amount: 1680.80 },\r\n            { monthIndex: 12, amount: 1820.20 }\r\n          ]\r\n        }\r\n      ]\r\n    },\r\n\r\n    async fetchRealTimeInventoryData() {\r\n      try {\r\n        const response = await this.getRealTimeAmount()\r\n        console.log('fetchRealTimeInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.realTimeInventoryData = response.data || []\r\n          console.log('fetchRealTimeInventoryData - 设置的数据:', this.realTimeInventoryData)\r\n        } else {\r\n          console.error('获取实时库存数据失败，使用模拟数据', response)\r\n          // 使用模拟数据\r\n          this.realTimeInventoryData = this.getMockRealTimeData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取实时库存数据失败，使用模拟数据:', error)\r\n        // 使用模拟数据\r\n        this.realTimeInventoryData = this.getMockRealTimeData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initRealTimeInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟实时库存数据\r\n    getMockRealTimeData() {\r\n      return [\r\n        {\r\n          materialType: 'A',\r\n          materialName: '通用备件',\r\n          centerInventoryAmount: 1250.30,\r\n          machineSideInventoryAmount: 380.50,\r\n          totalInventoryAmount: 1630.80\r\n        },\r\n        {\r\n          materialType: 'B',\r\n          materialName: '专用备件',\r\n          centerInventoryAmount: 980.75,\r\n          machineSideInventoryAmount: 420.25,\r\n          totalInventoryAmount: 1401.00\r\n        },\r\n        {\r\n          materialType: 'C',\r\n          materialName: '材料类',\r\n          centerInventoryAmount: 2150.60,\r\n          machineSideInventoryAmount: 650.40,\r\n          totalInventoryAmount: 2801.00\r\n        },\r\n        {\r\n          materialType: 'D',\r\n          materialName: '原材料',\r\n          centerInventoryAmount: 3200.90,\r\n          machineSideInventoryAmount: 890.10,\r\n          totalInventoryAmount: 4091.00\r\n        },\r\n        {\r\n          materialType: 'E',\r\n          materialName: '辅耐材',\r\n          centerInventoryAmount: 1580.40,\r\n          machineSideInventoryAmount: 320.60,\r\n          totalInventoryAmount: 1901.00\r\n        },\r\n        {\r\n          materialType: 'G',\r\n          materialName: '办公',\r\n          centerInventoryAmount: 150.20,\r\n          machineSideInventoryAmount: 50.80,\r\n          totalInventoryAmount: 201.00\r\n        }\r\n      ]\r\n    },\r\n\r\n    async fetchCokingCoalInventoryData() {\r\n      try {\r\n        const response = await this.getCokingCoalAmount()\r\n        console.log('fetchCokingCoalInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.cokingCoalInventoryData = response.data || []\r\n          console.log('fetchCokingCoalInventoryData - 设置的数据:', this.cokingCoalInventoryData)\r\n        } else {\r\n          console.error('获取矿焦煤库存数据失败', response)\r\n          this.cokingCoalInventoryData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取矿焦煤库存数据失败:', error)\r\n        this.cokingCoalInventoryData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 物料入库统计相关方法\r\n    async handleMaterialCategoryChange() {\r\n      console.log('物料类别变化:', this.selectedMaterialCategory)\r\n      this.selectedMaterialItem = '' // 重置第二个下拉框\r\n      await this.updateMaterialItemOptions()\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async handleMaterialItemChange() {\r\n      console.log('物料项目变化:', this.selectedMaterialItem)\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async updateMaterialItemOptions() {\r\n      if (this.selectedMaterialCategory === '1') {\r\n        // 大类：只有全部选项\r\n        this.materialItemOptions = []\r\n      } else {\r\n        // 中类、细类、叶类：获取对应的选项\r\n        const itemType = parseInt(this.selectedMaterialCategory) - 1 // 1->0, 2->1, 3->2, 4->3\r\n        try {\r\n          const response = await this.getItemTypeList(itemType)\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            this.materialItemOptions = response.data\r\n          } else {\r\n            this.materialItemOptions = []\r\n          }\r\n        } catch (error) {\r\n          console.error('获取物料项目选项失败:', error)\r\n          this.materialItemOptions = []\r\n        }\r\n      }\r\n    },\r\n\r\n    async fetchMaterialStatisticsData() {\r\n      try {\r\n        const params = {\r\n          itemType: parseInt(this.selectedMaterialCategory),\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        // 如果选择了具体物料项目，添加itemId参数\r\n        if (this.selectedMaterialItem && this.selectedMaterialItem !== '') {\r\n          params.itemId = this.selectedMaterialItem\r\n        }\r\n\r\n        console.log('fetchMaterialStatisticsData - 请求参数:', params)\r\n        const response = await this.getMaterialList(params)\r\n        console.log('fetchMaterialStatisticsData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.materialStatisticsData = response.data || []\r\n          console.log('fetchMaterialStatisticsData - 设置的数据:', this.materialStatisticsData)\r\n        } else {\r\n          console.error('获取物料统计数据失败，使用模拟数据', response)\r\n          this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料统计数据失败，使用模拟数据:', error)\r\n        this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialStatisticsChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟物料统计数据\r\n    getMockMaterialStatisticsData() {\r\n      return [\r\n        { itemName: '通用备件', inAmt: 1250.30, arriveRate: 85.5 },\r\n        { itemName: '专用备件', inAmt: 980.75, arriveRate: 78.2 },\r\n        { itemName: '材料类', inAmt: 2150.60, arriveRate: 92.1 },\r\n        { itemName: '原材料', inAmt: 3200.90, arriveRate: 88.7 },\r\n        { itemName: '辅耐材', inAmt: 1580.40, arriveRate: 91.3 },\r\n        { itemName: '办公', inAmt: 150.20, arriveRate: 95.0 }\r\n      ]\r\n    },\r\n\r\n    async fetchHighFrequencyMaterialData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          codeType: this.selectedCodeType,\r\n          itemType: this.selectedItemType\r\n        }\r\n\r\n        console.log('fetchHighFrequencyMaterialData - 请求参数:', params)\r\n        const response = await this.getHighFrequencyMaterialList(params)\r\n        console.log('fetchHighFrequencyMaterialData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.highFrequencyMaterialData = response.data || []\r\n          console.log('fetchHighFrequencyMaterialData - 设置的数据:', this.highFrequencyMaterialData)\r\n        } else {\r\n          console.error('获取高频物料数据失败，使用模拟数据', response)\r\n          this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取高频物料数据失败，使用模拟数据:', error)\r\n        this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialCloud()\r\n      })\r\n    },\r\n\r\n    // 生成模拟高频物料数据\r\n    getMockHighFrequencyData() {\r\n      return [\r\n        { itemName: '粗粉', inAmt: 392467.2, inNum: 5421293 },\r\n        { itemName: '精粉', inAmt: 280350.5, inNum: 4250180 },\r\n        { itemName: '球团', inAmt: 195200.8, inNum: 3180970 },\r\n        { itemName: '烧结', inAmt: 150420.3, inNum: 2890540 },\r\n        { itemName: '焦炭', inAmt: 125680.7, inNum: 2350210 },\r\n        { itemName: '煤炭', inAmt: 98750.2, inNum: 1980760 }\r\n      ]\r\n    },\r\n\r\n    async handleCodeTypeChange() {\r\n      console.log('大类类型变化:', this.selectedCodeType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    async handleItemTypeChange() {\r\n      console.log('维度变化:', this.selectedItemType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    // 获取供应商风险数据\r\n    async fetchSupplierRiskData() {\r\n      try {\r\n        const params = {\r\n          timeFlag: this.getTimeFlagByDimensionType(this.currentDimensionType)\r\n        }\r\n\r\n        console.log('fetchSupplierRiskData - 请求参数:', params)\r\n        const response = await this.getPurchaseSuppRisk(params)\r\n        console.log('fetchSupplierRiskData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.supplierRiskData = response.data || []\r\n          console.log('fetchSupplierRiskData - 设置的数据:', this.supplierRiskData)\r\n        } else {\r\n          console.error('获取供应商风险数据失败', response)\r\n          this.supplierRiskData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取供应商风险数据失败:', error)\r\n        this.supplierRiskData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initSupplierRiskChart()\r\n      })\r\n    },\r\n\r\n    // 获取多个物料的AI价格预测\r\n    async fetchMultiplePricePredictions(materialNames) {\r\n      this.predictionLoading = true\r\n      this.pricePredictions = [] // 清空之前的预测结果\r\n\r\n      try {\r\n        // 并行调用所有物料的预测接口\r\n        const predictionPromises = materialNames.map(async (materialName) => {\r\n          try {\r\n            const params = {\r\n              materialName: materialName,\r\n              materialType: '1' // 默认使用矿石类型，可以根据需要调整\r\n            }\r\n\r\n            console.log(`fetchPricePrediction - ${materialName} 请求参数:`, params)\r\n            const response = await getMaterialFuturePrice(params)\r\n            console.log(`fetchPricePrediction - ${materialName} 完整响应:`, response)\r\n\r\n            if (response && response.code && response.code === 200 && response.data) {\r\n              return {\r\n                materialName: materialName,\r\n                question: response.data.question || `关于${materialName}的价格预测`,\r\n                prediction: response.data.answer || response.data.prediction || response.msg,\r\n                success: response.data.success !== false\r\n              }\r\n            } else {\r\n              console.error(`获取${materialName}价格预测数据失败`, response)\r\n              return {\r\n                materialName: materialName,\r\n                question: `关于${materialName}的价格预测`,\r\n                prediction: `获取${materialName}价格预测失败`,\r\n                success: false\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error(`获取${materialName}价格预测数据失败:`, error)\r\n            return {\r\n              materialName: materialName,\r\n              question: `关于${materialName}的价格预测`,\r\n              prediction: `获取${materialName}价格预测失败：${error.message}`,\r\n              success: false\r\n            }\r\n          }\r\n        })\r\n\r\n        // 等待所有预测结果\r\n        const results = await Promise.all(predictionPromises)\r\n        this.pricePredictions = results\r\n        console.log('fetchMultiplePricePredictions - 设置的预测数据:', this.pricePredictions)\r\n\r\n        const successCount = results.filter(r => r.success).length\r\n        const totalCount = results.length\r\n\r\n        if (successCount > 0) {\r\n          this.$message.success(`成功获取${successCount}/${totalCount}个物料的价格预测`)\r\n        } else {\r\n          this.$message.error('所有物料的价格预测获取失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('批量获取价格预测数据失败:', error)\r\n        this.$message.error('批量获取价格预测失败：' + error.message)\r\n      } finally {\r\n        this.predictionLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取物料名称列表\r\n    async fetchMaterialNameList() {\r\n      try {\r\n        const params = {\r\n          category: parseInt(this.selectedMaterialCategory)\r\n        }\r\n\r\n        const response = await getMaterialNameList(params)\r\n        console.log('fetchMaterialNameList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.materialNameOptions = response.data\r\n          console.log('fetchMaterialNameList - 设置的数据:', this.materialNameOptions)\r\n\r\n          // 设置默认选中PB块，如果存在的话\r\n          const pbMaterial = this.materialNameOptions.find(item => item.itemName === 'PB块')\r\n          if (pbMaterial) {\r\n            this.selectedMaterial = 'PB块'\r\n          } else if (this.materialNameOptions.length > 0) {\r\n            // 如果没有PB块，选择第一个\r\n            this.selectedMaterial = this.materialNameOptions[0].itemName\r\n          }\r\n\r\n          // 获取价格数据\r\n          this.fetchPriceAndStoreData()\r\n        } else {\r\n          console.error('获取物料名称列表失败', response)\r\n          this.materialNameOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料名称列表失败:', error)\r\n        this.materialNameOptions = []\r\n      }\r\n    },\r\n\r\n    // 获取物料价格和采购量数据\r\n    async fetchPriceAndStoreData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemName: this.selectedMaterial\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStore(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n          this.priceAndStoreData = response.data[0] // 取第一个元素\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.priceAndStoreData)\r\n        } else {\r\n          console.error('获取价格和采购量数据失败', response)\r\n          this.priceAndStoreData = null\r\n        }\r\n      } catch (error) {\r\n        console.error('获取价格和采购量数据失败:', error)\r\n        this.priceAndStoreData = null\r\n      }\r\n\r\n      // 数据获取完成后重新初始化价格趋势图\r\n      this.$nextTick(() => {\r\n        this.initPriceTrendChart()\r\n      })\r\n    },\r\n\r\n    // 处理物资类型切换\r\n    async handleMaterialCategoryTypeChange() {\r\n      console.log('物资类型变化:', this.selectedMaterialCategory)\r\n      // 重新获取物料名称列表\r\n      await this.fetchMaterialNameList()\r\n    },\r\n\r\n    // 处理物料选择变化\r\n    async handleMaterialChange() {\r\n      console.log('物料选择变化:', this.selectedMaterial)\r\n      await this.fetchPriceAndStoreData()\r\n      // 不再自动触发AI预测，等用户点击按钮后再触发\r\n    },\r\n\r\n    calculateRealTimeInventoryTotal() {\r\n      let total = 0\r\n      if (this.realTimeInventoryData && this.realTimeInventoryData.length > 0) {\r\n        this.realTimeInventoryData.forEach(item => {\r\n          total += parseFloat(item.totalInventoryAmount) || 0\r\n        })\r\n      }\r\n      return total.toFixed(2)\r\n    },\r\n\r\n    calculateCokingCoalTotal() {\r\n      let total = 0\r\n      if (this.cokingCoalInventoryData && this.cokingCoalInventoryData.length > 0) {\r\n        // 找到所有数据中的最新日期\r\n        let latestDate = ''\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            item.purchaseCokingDailyDetailList.forEach(detail => {\r\n              if (detail.instockDate > latestDate) {\r\n                latestDate = detail.instockDate\r\n              }\r\n            })\r\n          }\r\n        })\r\n\r\n        // 计算最新日期各个物料的库存量合计\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)\r\n            if (latestDetail) {\r\n              total += parseFloat(latestDetail.invQty) || 0\r\n            }\r\n          }\r\n        })\r\n      }\r\n      return (total / 10000).toFixed(2) // 转换为万吨\r\n    },\r\n\r\n    // 处理矿焦煤类型下拉框变化\r\n    async handleCokingCoalTypeChange() {\r\n      console.log('矿焦煤类型变化:', this.selectedCokingCoalType)\r\n      // 重新初始化图表以应用过滤\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 机旁库存相关方法\r\n    // 获取分厂选项列表\r\n    async fetchFactoryDepOptions() {\r\n      try {\r\n        const response = await getDepNameList()\r\n        console.log('fetchFactoryDepOptions - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryDepOptions = response.data\r\n          console.log('fetchFactoryDepOptions - 设置的数据:', this.factoryDepOptions)\r\n        } else {\r\n          console.error('获取分厂选项列表失败', response)\r\n          this.factoryDepOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取分厂选项列表失败:', error)\r\n        this.factoryDepOptions = []\r\n      }\r\n\r\n      // 获取默认数据（全部）\r\n      this.fetchFactoryStockData()\r\n    },\r\n\r\n    // 处理分厂选择变化\r\n    async handleFactoryDepChange() {\r\n      console.log('分厂选择变化:', this.selectedFactoryDep)\r\n      await this.fetchFactoryStockData()\r\n    },\r\n\r\n    // 处理物料类型选择变化\r\n    async handleFactoryMaterialTypeChange() {\r\n      console.log('物料类型选择变化:', this.selectedFactoryMaterialType)\r\n      // 重新初始化图表以应用筛选\r\n      this.$nextTick(() => {\r\n        this.initFactoryStockChart()\r\n      })\r\n    },\r\n\r\n    // 获取机旁库存数据\r\n    async fetchFactoryStockData() {\r\n      try {\r\n        const depName = this.selectedFactoryDep || '' // 空字符串表示全部\r\n        console.log('fetchFactoryStockData - 请求参数:', depName)\r\n\r\n        const response = await getListMonthly(depName)\r\n        console.log('fetchFactoryStockData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryStockData = response.data\r\n          console.log('fetchFactoryStockData - 设置的数据:', this.factoryStockData)\r\n        } else {\r\n          console.error('获取机旁库存数据失败', response)\r\n          this.factoryStockData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取机旁库存数据失败:', error)\r\n        this.factoryStockData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initFactoryStockChart()\r\n      })\r\n    },\r\n\r\n    // 新增方法：处理采购量曲线物料类型变化\r\n    async handlePurchaseAmountCategoriesChange() {\r\n      console.log('采购量曲线物料类型变化:', this.purchaseAmountCategories)\r\n      this.selectedPurchaseAmountMaterials = [] // 重置选中的物料\r\n      await this.fetchPurchaseAmountMaterialList()\r\n    },\r\n\r\n    // 新增方法：处理市场价曲线物料类型变化\r\n    async handleMarketPriceCategoriesChange() {\r\n      console.log('市场价曲线物料类型变化:', this.marketPriceCategories)\r\n      this.selectedMarketPriceMaterials = [] // 重置选中的物料\r\n      await this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 新增方法：获取采购量曲线物料列表\r\n    async fetchPurchaseAmountMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.purchaseAmountCategories,\r\n          curveType: 2, // 采购量曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchPurchaseAmountMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchPurchaseAmountMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.purchaseAmountMaterialOptions = response.data\r\n          console.log('fetchPurchaseAmountMaterialList - 设置的数据:', this.purchaseAmountMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedPurchaseAmountMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.purchaseAmountMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedPurchaseAmountMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 采购量曲线')\r\n\r\n              // 检查市场价曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取采购量曲线物料列表失败', response)\r\n          this.purchaseAmountMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取采购量曲线物料列表失败:', error)\r\n        this.purchaseAmountMaterialOptions = []\r\n      }\r\n    },\r\n\r\n    // 新增方法：获取市场价曲线物料列表\r\n    async fetchMarketPriceMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.marketPriceCategories,\r\n          curveType: 1, // 价格曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchMarketPriceMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchMarketPriceMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.marketPriceMaterialOptions = response.data\r\n          console.log('fetchMarketPriceMaterialList - 设置的数据:', this.marketPriceMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedMarketPriceMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.marketPriceMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedMarketPriceMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 市场价曲线')\r\n\r\n              // 检查采购量曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取市场价曲线物料列表失败', response)\r\n          this.marketPriceMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取市场价曲线物料列表失败:', error)\r\n        this.marketPriceMaterialOptions = []\r\n      }\r\n    },\r\n\r\n\r\n\r\n    // 新增方法：获取物料采购价格数据（用于新的价格趋势图）\r\n    async fetchPriceAndStoreDataForNewChart() {\r\n      if (this.selectedPurchaseAmountMaterials.length === 0 && this.selectedMarketPriceMaterials.length === 0) {\r\n        this.$message.warning('请至少选择一个物料')\r\n        return\r\n      }\r\n\r\n      this.fetchingPriceData = true\r\n      try {\r\n        // 构建itemList\r\n        const itemList = []\r\n\r\n        // 添加采购量曲线的物料\r\n        this.selectedPurchaseAmountMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 2, // 采购量曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        // 添加市场价曲线的物料\r\n        this.selectedMarketPriceMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 1, // 价格曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.newPriceAndStoreData = response.data\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.newPriceAndStoreData)\r\n\r\n          // 重新渲染图表\r\n          this.$nextTick(() => {\r\n            this.initNewPriceTrendChart()\r\n          })\r\n\r\n          // 获取所有选中物料的去重列表\r\n          const allSelectedMaterials = [...new Set([\r\n            ...this.selectedPurchaseAmountMaterials,\r\n            ...this.selectedMarketPriceMaterials\r\n          ])]\r\n\r\n          // 为每个物料调用AI预测接口\r\n          if (allSelectedMaterials.length > 0) {\r\n            this.fetchMultiplePricePredictions(allSelectedMaterials)\r\n          }\r\n\r\n          // 如果市场价曲线有选中物料，获取相似物料信息\r\n          if (this.selectedMarketPriceMaterials.length > 0) {\r\n            this.fetchSimilarMaterials(this.selectedMarketPriceMaterials)\r\n          } else {\r\n            // 清空相似物料数据\r\n            this.similarMaterialsData = []\r\n          }\r\n\r\n          this.$message.success('数据获取成功')\r\n        } else {\r\n          console.error('获取物料采购价格数据失败', response)\r\n          this.$message.error('获取数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料采购价格数据失败:', error)\r\n        this.$message.error('获取数据失败：' + error.message)\r\n      } finally {\r\n        this.fetchingPriceData = false\r\n      }\r\n    },\r\n\r\n    // 获取相似物料信息\r\n    async fetchSimilarMaterials(itemNames) {\r\n      this.similarMaterialsLoading = true\r\n      try {\r\n        const params = {\r\n          itemNames: itemNames\r\n        }\r\n\r\n        console.log('fetchSimilarMaterials - 请求参数:', params)\r\n        const response = await listSimilarByItemNames(params)\r\n        console.log('fetchSimilarMaterials - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.similarMaterialsData = response.data\r\n          console.log('fetchSimilarMaterials - 设置的数据:', this.similarMaterialsData)\r\n        } else {\r\n          console.error('获取相似物料数据失败', response)\r\n          this.similarMaterialsData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取相似物料数据失败:', error)\r\n        this.similarMaterialsData = []\r\n      } finally {\r\n        this.similarMaterialsLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取排名样式类\r\n    getRankClass(rank) {\r\n      if (rank === 1) return 'rank-first'\r\n      if (rank === 2) return 'rank-second'\r\n      if (rank === 3) return 'rank-third'\r\n      return 'rank-default'\r\n    },\r\n\r\n    // 获取商品分类名称\r\n    getCategoryName(category) {\r\n      const categoryMap = {\r\n        1: '矿石',\r\n        2: '煤炭',\r\n        3: '合金',\r\n        4: '废钢'\r\n      }\r\n      return categoryMap[category] || '未知'\r\n    },\r\n\r\n    // 获取价格类型名称\r\n    getPriceTypeName(priceType) {\r\n      const priceTypeMap = {\r\n        1: '现货价',\r\n        2: '市场采购到厂价',\r\n        3: '兴澄废钢收购价(车运)',\r\n        4: '兴澄废钢收购价(船运)',\r\n        5: '沙钢废钢收购价(车运)',\r\n        6: '沙钢废钢收购价(船运)'\r\n      }\r\n      return priceTypeMap[priceType] || '未知'\r\n    },\r\n\r\n    // 打开对比弹框\r\n    openComparisonDialog(item) {\r\n      console.log('openComparisonDialog - 传入的item数据:', item)\r\n      this.currentComparison = { ...item }\r\n      console.log('openComparisonDialog - 设置的currentComparison:', this.currentComparison)\r\n      this.comparisonDialogVisible = true\r\n\r\n      // 弹框打开后获取对比数据\r\n      this.$nextTick(() => {\r\n        this.fetchComparisonData()\r\n      })\r\n    },\r\n\r\n    // 关闭对比弹框\r\n    closeComparisonDialog() {\r\n      this.comparisonDialogVisible = false\r\n      this.currentComparison = {}\r\n      this.comparisonPriceData = null\r\n\r\n      // 清理图表实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n          this.comparisonChartInstance = null\r\n        } catch (err) {\r\n          console.error('清理对比图表实例失败:', err)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 获取对比数据（独立实现，不耦合现有趋势图）\r\n    async fetchComparisonData() {\r\n      this.comparisonChartLoading = true\r\n      try {\r\n        // 构建两个物料的对比请求，只获取价格曲线\r\n        const itemList = [\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.itemName\r\n          },\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.compareItemName\r\n          }\r\n        ]\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchComparisonData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchComparisonData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          // 对返回的数据进行筛选，确保基准物料和相似物料的指定价格类型都能被提取\r\n          const filteredData = []\r\n\r\n          // 获取基准物料和相似物料的目标价格类型名称\r\n          const basePriceTypeName = this.getPriceTypeName(this.currentComparison.priceType)\r\n          const comparePriceTypeName = this.getPriceTypeName(this.currentComparison.comparePriceType)\r\n\r\n          console.log('筛选条件:', {\r\n            baseItemName: this.currentComparison.itemName,\r\n            basePriceTypeName: basePriceTypeName,\r\n            compareItemName: this.currentComparison.compareItemName,\r\n            comparePriceTypeName: comparePriceTypeName\r\n          })\r\n\r\n          response.data.forEach(materialData => {\r\n            const filteredMaterialData = { ...materialData }\r\n\r\n            if (filteredMaterialData.procurementPriceVoList) {\r\n              // 只保留匹配的价格类型\r\n              filteredMaterialData.procurementPriceVoList = filteredMaterialData.procurementPriceVoList.filter(priceGroup => {\r\n                let isMatch = false\r\n                // 基准物料：匹配物料名称和基准价格类型\r\n                if (materialData.itemName === this.currentComparison.itemName) {\r\n                  isMatch = priceGroup.priceName === basePriceTypeName\r\n                  console.log(`基准物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${basePriceTypeName}] 匹配:${isMatch}`)\r\n                }\r\n\r\n                if(isMatch){\r\n                  return isMatch\r\n                }else{\r\n                  if (materialData.itemName === this.currentComparison.compareItemName) {\r\n                    const isMatch = priceGroup.priceName === comparePriceTypeName\r\n                    console.log(`相似物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${comparePriceTypeName}] 匹配:${isMatch}`)\r\n                    return isMatch\r\n                  }\r\n                }\r\n\r\n\r\n                return false\r\n              })\r\n\r\n              console.log(111111111)\r\n              console.log(filteredMaterialData.procurementPriceVoList)\r\n\r\n              // 只有当该物料有匹配的价格类型时才加入结果\r\n              if (filteredMaterialData.procurementPriceVoList.length > 0) {\r\n                filteredData.push(filteredMaterialData)\r\n                console.log(`添加物料[${materialData.itemName}]，包含${filteredMaterialData.procurementPriceVoList.length}个价格组`)\r\n              }\r\n            }\r\n          })\r\n\r\n          this.comparisonPriceData = filteredData\r\n          console.log('fetchComparisonData - 筛选后的数据:', this.comparisonPriceData)\r\n          console.log('筛选结果统计:', {\r\n            totalMaterials: filteredData.length,\r\n            materials: filteredData.map(m => ({\r\n              name: m.itemName,\r\n              priceGroupCount: m.procurementPriceVoList?.length || 0,\r\n              priceGroups: m.procurementPriceVoList?.map(p => p.priceName) || []\r\n            }))\r\n          })\r\n\r\n          // 渲染对比图表\r\n          this.$nextTick(() => {\r\n            this.renderComparisonChart()\r\n          })\r\n        } else {\r\n          console.error('获取对比数据失败', response)\r\n          this.$message.error('获取对比数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取对比数据失败:', error)\r\n        this.$message.error('获取对比数据失败：' + error.message)\r\n      } finally {\r\n        this.comparisonChartLoading = false\r\n      }\r\n    },\r\n\r\n    // 渲染对比图表（独立实现，不耦合现有趋势图）\r\n    renderComparisonChart() {\r\n      const chartDom = document.getElementById('comparisonChart')\r\n      if (!chartDom) {\r\n        console.error('找不到对比图表DOM元素')\r\n        return\r\n      }\r\n\r\n      // 清理现有实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n        } catch (err) {\r\n          console.error('清理现有对比图表实例失败:', err)\r\n        }\r\n      }\r\n\r\n      // 创建新的图表实例\r\n      try {\r\n        this.comparisonChartInstance = echarts.init(chartDom)\r\n      } catch (err) {\r\n        console.error('创建对比图表实例失败:', err)\r\n        return\r\n      }\r\n\r\n      if (!this.comparisonPriceData || this.comparisonPriceData.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      const formatDate = (dateStr) => {\r\n        const year = dateStr.substring(0, 4)\r\n        const month = dateStr.substring(4, 6)\r\n        const day = dateStr.substring(6, 8)\r\n        return `${year}年${month}月${day}日`\r\n      }\r\n\r\n      // 收集所有日期\r\n      let allDates = new Set()\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        if (materialData.procurementPriceVoList) {\r\n          materialData.procurementPriceVoList.forEach(priceGroup => {\r\n            if (priceGroup.priceList) {\r\n              priceGroup.priceList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      allDates = Array.from(allDates).sort()\r\n      const xAxisData = allDates.map(formatDate)\r\n\r\n      if (allDates.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      // 构建系列数据\r\n      const series = []\r\n      const legendData = []\r\n      const colors = ['#8fe9ff', '#ff9f7f', '#5fd8b6', '#ffb980']\r\n      let colorIndex = 0\r\n\r\n      console.log('=== 开始处理对比数据 ===')\r\n      console.log('对比数据总览:', {\r\n        materialCount: this.comparisonPriceData.length,\r\n        baseMaterial: this.currentComparison.itemName,\r\n        compareMaterial: this.currentComparison.compareItemName\r\n      })\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        const materialName = materialData.itemName\r\n        console.log(`\\n处理物料: ${materialName}`)\r\n\r\n        if (materialData.procurementPriceVoList) {\r\n          console.log(`  该物料有 ${materialData.procurementPriceVoList.length} 个价格组`)\r\n          materialData.procurementPriceVoList.forEach((priceGroup, index) => {\r\n            console.log(`  价格组 ${index + 1}: ${priceGroup.priceName}，数据点数量: ${priceGroup.priceList?.length || 0}`)\r\n          })\r\n\r\n          // 数据已经在fetchComparisonData中预先筛选过，这里直接处理所有匹配的价格组\r\n          materialData.procurementPriceVoList.forEach((priceGroup, groupIndex) => {\r\n            const priceData = allDates.map(date => {\r\n              const found = priceGroup.priceList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.price) : null\r\n            })\r\n\r\n            // 统计有效数据点\r\n            const validDataCount = priceData.filter(v => v !== null && v !== undefined).length\r\n            console.log(`    处理价格组[${priceGroup.priceName}]，有效数据点: ${validDataCount}/${priceData.length}`)\r\n\r\n            // 确保每条曲线都有唯一的名称和颜色，即使数据相同\r\n            const uniqueName = `${materialName}-${priceGroup.priceName}`\r\n            const lineColor = colors[colorIndex % colors.length]\r\n\r\n            // 检查是否已经有相同的数据，如果有则添加轻微偏移\r\n            const dataStr = JSON.stringify(priceData)\r\n            const existingSeries = series.find(s => JSON.stringify(s.data) === dataStr)\r\n            let adjustedData = priceData\r\n\r\n            if (existingSeries && priceData.some(v => v !== null)) {\r\n              // 为重复数据添加极小的偏移量（0.01），确保两条线都能显示\r\n              adjustedData = priceData.map(value => value !== null ? value + 0.01 : null)\r\n              console.log(`    检测到重复数据，为 ${uniqueName} 添加偏移`)\r\n            }\r\n\r\n            series.push({\r\n              name: uniqueName,\r\n              type: 'line',\r\n              data: adjustedData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 3,\r\n                color: lineColor,\r\n                // 如果是偏移的数据，使用虚线样式区分\r\n                type: adjustedData !== priceData ? 'dashed' : 'solid'\r\n              },\r\n              itemStyle: {\r\n                color: lineColor\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 6,\r\n              connectNulls: true,\r\n              // 添加z-index确保两条线都能显示\r\n              z: colorIndex + 1\r\n            })\r\n\r\n            legendData.push(uniqueName)\r\n            colorIndex++\r\n            console.log(`    ✓ 添加曲线: ${uniqueName}，颜色: ${lineColor}，有效数据: ${validDataCount}`)\r\n          })\r\n        }\r\n      })\r\n\r\n      console.log(`\\n=== 图表数据处理完成 ===`)\r\n      console.log(`总计添加 ${series.length} 条曲线:`)\r\n      series.forEach((s, i) => {\r\n        const validCount = s.data.filter(v => v !== null && v !== undefined).length\r\n        console.log(`  ${i + 1}. ${s.name} (有效数据: ${validCount})`)\r\n      })\r\n\r\n      // 计算Y轴范围\r\n      let priceMin, priceMax\r\n      const priceValues = series.flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n      if (priceValues.length > 0) {\r\n        priceMin = Math.min(...priceValues)\r\n        priceMax = Math.max(...priceValues)\r\n      }\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          formatter: function(params) {\r\n            let str = params[0].axisValueLabel + '<br/>'\r\n            params.forEach(item => {\r\n              if (item.value !== null && item.value !== undefined) {\r\n                str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`\r\n              } else {\r\n                str += `${item.marker}${item.seriesName}: -<br/>`\r\n              }\r\n            })\r\n            return str\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '12%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: function(index, value) {\r\n              if (index >= allDates.length || !allDates.length) return false\r\n\r\n              const uniqueMonths = new Set()\r\n              allDates.forEach(dateStr => {\r\n                const year = dateStr.substring(0, 4)\r\n                const month = dateStr.substring(4, 6)\r\n                uniqueMonths.add(`${year}${month}`)\r\n              })\r\n\r\n              const monthsCount = uniqueMonths.size\r\n              if (monthsCount <= 1) return true\r\n\r\n              const totalDataPoints = allDates.length\r\n              const idealInterval = Math.floor(totalDataPoints / Math.min(monthsCount, 8))\r\n\r\n              return index % Math.max(idealInterval, 1) === 0\r\n            },\r\n            formatter: function(value, index) {\r\n              if (index >= allDates.length) return ''\r\n              const originalDateStr = allDates[index]\r\n              if (!originalDateStr) return ''\r\n\r\n              const year = originalDateStr.substring(0, 4)\r\n              const month = parseInt(originalDateStr.substring(4, 6))\r\n              return `${year}.${month}`\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '价格（元/吨）',\r\n          min: priceMin,\r\n          max: priceMax,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee'\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: series\r\n      }\r\n\r\n      this.comparisonChartInstance.setOption(option, true)\r\n    },\r\n\r\n    // 检查两个曲线是否都已设置默认值，如果是则触发初始数据获取\r\n    checkAndTriggerInitialDataFetch() {\r\n      // 检查两个曲线是否都已经设置了默认的PB块\r\n      if (this.selectedPurchaseAmountMaterials.includes('PB块') &&\r\n        this.selectedMarketPriceMaterials.includes('PB块') &&\r\n        !this.hasInitializedPriceChart) {\r\n\r\n        this.hasInitializedPriceChart = true // 标记已经初始化过\r\n        console.log('两个曲线都已设置默认值，自动触发数据获取')\r\n\r\n        // 自动触发数据获取\r\n        this.$nextTick(() => {\r\n          this.fetchPriceAndStoreDataForNewChart()\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n  font-family: \"Microsoft YaHei\", sans-serif;\r\n}\r\n\r\n.dashboard-container {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  color: #fff;\r\n  overflow-x: hidden;\r\n  padding: 10px;\r\n}\r\n\r\n.dashboard-header {\r\n  text-align: center;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n  padding: 5px 0;\r\n}\r\n\r\n.dashboard-header h1 {\r\n  font-size: 24px;\r\n  position: relative;\r\n  display: inline-block;\r\n  padding: 5px 40px;\r\n}\r\n\r\n.dashboard-header::before,\r\n.dashboard-header::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 50%;\r\n  width: 30%;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.dashboard-header::before {\r\n  left: 0;\r\n}\r\n\r\n.dashboard-header::after {\r\n  right: 0;\r\n}\r\n\r\n.dashboard-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(12, 1fr);\r\n  grid-template-rows: auto auto auto auto auto auto;\r\n  gap: 10px;\r\n  min-height: calc(100vh - 80px);\r\n}\r\n\r\n.card {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 5px;\r\n  padding: 10px;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.card-title {\r\n  font-size: 14px;\r\n  margin-bottom: 5px;\r\n  font-weight: normal;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.chart-filter-dropdown-container {\r\n  z-index: 10;\r\n}\r\n\r\n.chart-filter-dropdown-container select {\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n  color: #fff;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  font-size: 12px;\r\n}\r\n\r\n.chart {\r\n  width: 100%;\r\n  height: calc(100% - 20px);\r\n  min-height: 200px;\r\n  flex: 1;\r\n}\r\n\r\n.stat-cards {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  height: 100%;\r\n  align-items: center;\r\n}\r\n\r\n.stat-card {\r\n  text-align: center;\r\n  flex-grow: 1;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 34px;\r\n  font-weight: bold;\r\n  color: #00ffff;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 18px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.chart-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: rgba(255,255,255,0.5);\r\n  font-size: 14px;\r\n}\r\n\r\n.material-chart-card {\r\n  height: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.material-chart-card .chart {\r\n  flex-grow: 1;\r\n  height: 250px;\r\n  min-height: 250px;\r\n}\r\n\r\n.time-filter {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.time-filter-btn {\r\n  padding: 6px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n}\r\n\r\n.time-filter-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.time-filter-btn.active {\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n  font-weight: 500;\r\n}\r\n\r\n.header-controls {\r\n  position: absolute;\r\n  right: 20px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  z-index: 1000;\r\n}\r\n\r\n.fullscreen-btn {\r\n  padding: 8px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 40px;\r\n  height: 32px;\r\n}\r\n\r\n.fullscreen-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n}\r\n\r\n/* AI价格预测区域样式 */\r\n.price-prediction-section {\r\n  margin-top: 15px;\r\n  margin-bottom: 15px;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-size: 13px;\r\n}\r\n\r\n.prediction-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.model-info {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 12px;\r\n}\r\n\r\n.prediction-content {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  border-left: 3px solid #00ffff;\r\n  position: relative;\r\n}\r\n\r\n.prediction-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n\r\n\r\n/* 多个预测结果的样式 */\r\n.predictions-container {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding-right: 5px;\r\n}\r\n\r\n.prediction-item {\r\n  margin-bottom: 15px;\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n  overflow: hidden;\r\n}\r\n\r\n.prediction-item.prediction-error {\r\n  border-left-color: #ff6b6b;\r\n}\r\n\r\n.prediction-material-title {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  padding: 8px 12px;\r\n  font-size: 13px;\r\n  font-weight: 600;\r\n  color: #00ffff;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-item.prediction-error .prediction-material-title {\r\n  background-color: rgba(255, 107, 107, 0.1);\r\n  color: #ff6b6b;\r\n  border-bottom-color: rgba(255, 107, 107, 0.2);\r\n}\r\n\r\n.prediction-material-title i {\r\n  margin-right: 6px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n/* 预测容器滚动条样式 */\r\n.predictions-container::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 一问一答样式 */\r\n.qa-section {\r\n  padding: 0;\r\n}\r\n\r\n.question-section, .answer-section {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.answer-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.qa-label {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  font-weight: 600;\r\n  font-size: 13px;\r\n}\r\n\r\n.question-label {\r\n  color: #ffb980;\r\n}\r\n\r\n.answer-label {\r\n  color: #00ffff;\r\n}\r\n\r\n.qa-label i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n.question-text, .answer-text {\r\n  background-color: rgba(0, 0, 0, 0.3);\r\n  border-radius: 8px;\r\n  padding: 12px 15px;\r\n  line-height: 1.6;\r\n  font-size: 13px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  white-space: pre-wrap;\r\n  word-wrap: break-word;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.question-text {\r\n  border-left: 3px solid #ffb980;\r\n}\r\n\r\n.answer-text {\r\n  border-left: 3px solid #00ffff;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding-right: 18px;\r\n}\r\n\r\n/* 问题文本样式 */\r\n.question-text {\r\n  font-style: italic;\r\n  color: rgba(255, 200, 150, 0.9);\r\n}\r\n\r\n/* 答案文本滚动条样式 */\r\n.answer-text::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 价格趋势卡片特殊样式 */\r\n.price-trend-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: auto;\r\n  min-height: 400px;\r\n}\r\n\r\n.price-trend-card .chart {\r\n  flex-shrink: 0;\r\n  height: 300px !important;\r\n  min-height: 300px;\r\n}\r\n\r\n.price-trend-card .price-prediction-section {\r\n  flex-shrink: 0;\r\n  margin-top: 15px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.inventory-total {\r\n  font-size: 12px;\r\n  color: #00ffff;\r\n  font-weight: normal;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 新增：价格趋势图控件样式 */\r\n.price-trend-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: rgba(16, 7, 33, 0.4);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.left-controls, .right-controls {\r\n  flex: 1;\r\n  max-width: 45%;\r\n}\r\n\r\n.curve-label {\r\n  font-size: 13px;\r\n  color: #00ffff;\r\n  margin-bottom: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n.left-controls .curve-label {\r\n  text-align: left;\r\n}\r\n\r\n.right-controls .curve-label {\r\n  text-align: right;\r\n}\r\n\r\n.dropdown-row {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.right-controls .dropdown-row {\r\n  justify-content: flex-end;\r\n}\r\n\r\n.fetch-data-btn-container {\r\n  text-align: right;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.modern-fetch-btn {\r\n  background: rgba(138, 43, 226, 0.7);\r\n  border: none;\r\n  border-radius: 12px;\r\n  padding: 10px 20px;\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 3px 10px rgba(138, 43, 226, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-fetch-btn:hover:not(:disabled) {\r\n  background: rgba(138, 43, 226, 0.9);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.5);\r\n}\r\n\r\n.modern-fetch-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.modern-fetch-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.modern-fetch-btn.loading {\r\n  background: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.modern-fetch-btn i {\r\n  margin-right: 8px;\r\n  animation: rotate 1s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n/* Element UI 下拉框样式覆盖 */\r\n.price-trend-controls .el-select {\r\n  background-color: transparent !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner {\r\n  background-color: #4a1c5a !important;\r\n  border: 1px solid rgba(116, 75, 162, 0.5) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 8px !important;\r\n  font-size: 13px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:hover {\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  box-shadow: 0 0 8px rgba(116, 75, 162, 0.3) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:focus {\r\n  border-color: #764ba2 !important;\r\n  box-shadow: 0 0 12px rgba(116, 75, 162, 0.5) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner::placeholder {\r\n  color: rgba(255, 255, 255, 0.7) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix i {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag {\r\n  background-color: rgba(116, 75, 162, 0.6) !important;\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 6px !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close:hover {\r\n  background-color: rgba(255, 255, 255, 0.2) !important;\r\n}\r\n\r\n/* 相似物料区域样式 */\r\n.similar-materials-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.similar-materials-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  font-size: 14px;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.similar-materials-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.section-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n.similar-materials-container {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 10px;\r\n  overflow-x: auto;\r\n}\r\n\r\n.materials-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  font-size: 13px;\r\n}\r\n\r\n.materials-table th {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  color: #00ffff;\r\n  padding: 8px 12px;\r\n  text-align: left;\r\n  border-bottom: 2px solid rgba(0, 212, 255, 0.3);\r\n  font-weight: 600;\r\n}\r\n\r\n.materials-table td {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.material-row {\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.material-row:hover {\r\n  background-color: rgba(0, 212, 255, 0.05);\r\n}\r\n\r\n.rank-cell {\r\n  text-align: center;\r\n  width: 60px;\r\n}\r\n\r\n.rank-badge {\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 50%;\r\n  color: #fff;\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n  min-width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.rank-first {\r\n  background: linear-gradient(135deg, #ffd700, #ffb347);\r\n  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.rank-second {\r\n  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);\r\n  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);\r\n}\r\n\r\n.rank-third {\r\n  background: linear-gradient(135deg, #cd7f32, #b8860b);\r\n  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);\r\n}\r\n\r\n.rank-default {\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.material-name, .compare-material-name {\r\n  font-weight: 500;\r\n  color: #fff;\r\n}\r\n\r\n.compare-material-name {\r\n  color: #00ffff;\r\n}\r\n\r\n.score-cell {\r\n  text-align: center;\r\n  width: 120px;\r\n  min-width: 120px;\r\n}\r\n\r\n.score-value {\r\n  display: inline-block;\r\n  padding: 2px 6px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 4px;\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n}\r\n\r\n.score-desc {\r\n  color: #ffb980;\r\n  font-style: italic;\r\n}\r\n\r\n.category-cell {\r\n  color: #5fd8b6;\r\n  font-weight: 500;\r\n}\r\n\r\n.similar-materials-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n.similar-materials-group {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.similar-materials-group:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.group-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n}\r\n\r\n.price-type-cell {\r\n  color: #e879ed;\r\n  font-size: 11px;\r\n  max-width: 120px;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.algorithm-desc {\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 11px;\r\n  font-style: italic;\r\n  margin-left: 8px;\r\n}\r\n\r\n.action-cell {\r\n  text-align: center;\r\n  width: 100px;\r\n}\r\n\r\n.view-comparison-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  color: #fff;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  white-space: nowrap;\r\n  min-width: 70px;\r\n}\r\n\r\n.view-comparison-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\r\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\r\n}\r\n\r\n.view-comparison-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.view-comparison-btn i {\r\n  font-size: 13px;\r\n}\r\n\r\n/* 对比弹框样式 */\r\n.comparison-dialog .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__header {\r\n  background: linear-gradient(135deg, rgba(33, 10, 56, 0.9), rgba(0, 212, 255, 0.2));\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__title {\r\n  color: #00ffff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close {\r\n  color: #00ffff;\r\n  font-size: 20px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close:hover {\r\n  color: #fff;\r\n  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n.comparison-dialog .el-dialog__body {\r\n  padding: 0;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-content {\r\n  padding: 20px;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.comparison-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 15px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.base-material {\r\n  color: #00ffff;\r\n  padding: 8px 16px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.vs-text {\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.compare-material {\r\n  color: #ea7ccc;\r\n  padding: 8px 16px;\r\n  background-color: rgba(234, 124, 204, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(234, 124, 204, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.similarity-info {\r\n  color: #ffb980;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  padding: 4px 12px;\r\n  background-color: rgba(255, 185, 128, 0.2);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 185, 128, 0.4);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-chart-container {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  overflow: hidden;\r\n  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.comparison-chart {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n</style>\r\n"]}]}