{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue", "mtime": 1756456493919}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJQdW5pc2htZW50QmFzaXNEaWFsb2ciLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIHZpc2libGU6IGZhbHNlLA0KICAgICAgLy8g6YCJ5Lit55qE5L6d5o2u57G75Z6L77yI5aSa6YCJ77yJDQogICAgICBzZWxlY3RlZEJhc2lzVHlwZXM6IFtdLA0KICAgICAgLy8g6LSo6YeP5byC6K6u5Y2V5Y+3DQogICAgICBxdWFsaXR5TnVtYmVyOiAnJywNCiAgICAgIC8vIOWItuW6puWQjeensA0KICAgICAgc3lzdGVtTmFtZTogJycsDQogICAgICAvLyDmiqXlkYrlkI3np7ANCiAgICAgIHJlcG9ydE5hbWU6ICcnLA0KICAgICAgLy8g5beh5qOA5aSE572a5Y2V5Y+3DQogICAgICBpbnNwZWN0aW9uTnVtYmVyOiAnJywNCiAgICAgIC8vIOWuieeuoeWkhOe9muWNleWPtw0KICAgICAgc2FmZXR5TnVtYmVyOiAnJywNCiAgICAgIC8vIOS+neaNruWGheWuuQ0KICAgICAgYmFzaXNDb250ZW50OiAnJywNCiAgICAgIC8vIOmihOiniOaWh+acrA0KICAgICAgcHJldmlld1RleHQ6ICcnDQogICAgfTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmmL7npLrlvLnnqpcgKi8NCiAgICBzaG93KGN1cnJlbnRWYWx1ZSA9ICcnKSB7DQogICAgICB0aGlzLnZpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5wYXJzZUN1cnJlbnRWYWx1ZShjdXJyZW50VmFsdWUpOw0KICAgICAgdGhpcy51cGRhdGVQcmV2aWV3KCk7DQogICAgICAvLyDnoa7kv53lvLnnqpflrozlhajmiZPlvIDlkI7lho3ov5vooYzlhbbku5bmk43kvZwNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ+W8ueeql+W3suaYvuekuu+8jOW9k+WJjeaVsOaNru+8micsIHsNCiAgICAgICAgICBzZWxlY3RlZEJhc2lzVHlwZXM6IHRoaXMuc2VsZWN0ZWRCYXNpc1R5cGVzLA0KICAgICAgICAgIHF1YWxpdHlOdW1iZXI6IHRoaXMucXVhbGl0eU51bWJlciwNCiAgICAgICAgICBzeXN0ZW1OYW1lOiB0aGlzLnN5c3RlbU5hbWUsDQogICAgICAgICAgcmVwb3J0TmFtZTogdGhpcy5yZXBvcnROYW1lLA0KICAgICAgICAgIGluc3BlY3Rpb25OdW1iZXI6IHRoaXMuaW5zcGVjdGlvbk51bWJlciwNCiAgICAgICAgICBzYWZldHlOdW1iZXI6IHRoaXMuc2FmZXR5TnVtYmVyLA0KICAgICAgICAgIGJhc2lzQ29udGVudDogdGhpcy5iYXNpc0NvbnRlbnQNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIA0KICAgIC8qKiDpmpDol4/lvLnnqpcgKi8NCiAgICBoaWRlKCkgew0KICAgICAgdGhpcy52aXNpYmxlID0gZmFsc2U7DQogICAgfSwNCiAgICANCiAgICAvKiog5YWz6Zet5by556qXICovDQogICAgaGFuZGxlQ2xvc2UoKSB7DQogICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIA0KICAgIC8qKiDph43nva7mlbDmja4gKi8NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRCYXNpc1R5cGVzID0gW107DQogICAgICB0aGlzLnF1YWxpdHlOdW1iZXIgPSAnJzsNCiAgICAgIHRoaXMuc3lzdGVtTmFtZSA9ICcnOw0KICAgICAgdGhpcy5yZXBvcnROYW1lID0gJyc7DQogICAgICB0aGlzLmluc3BlY3Rpb25OdW1iZXIgPSAnJzsNCiAgICAgIHRoaXMuc2FmZXR5TnVtYmVyID0gJyc7DQogICAgICB0aGlzLmJhc2lzQ29udGVudCA9ICcnOw0KICAgICAgdGhpcy5wcmV2aWV3VGV4dCA9ICcnOw0KICAgIH0sDQogICAgDQogICAgLyoqIOino+aekOW9k+WJjeWAvCAqLw0KICAgIHBhcnNlQ3VycmVudFZhbHVlKHZhbHVlKSB7DQogICAgICBpZiAoIXZhbHVlKSB7DQogICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDph43nva7pgInkuK3nmoTnsbvlnovmlbDnu4QNCiAgICAgIHRoaXMuc2VsZWN0ZWRCYXNpc1R5cGVzID0gW107DQoNCiAgICAgIC8vIOWwneivleino+aekOeOsOacieeahOS+neaNruWGheWuue+8iOaUr+aMgeWkmuS4quexu+Wei++8iQ0KICAgICAgaWYgKHZhbHVlLmluY2x1ZGVzKCfotKjph4/lvILorq7ljZXlj7fvvJonKSkgew0KICAgICAgICB0aGlzLnNlbGVjdGVkQmFzaXNUeXBlcy5wdXNoKCdxdWFsaXR5Jyk7DQogICAgICAgIGNvbnN0IG1hdGNoID0gdmFsdWUubWF0Y2goL+i0qOmHj+W8guiuruWNleWPt++8mihbXu+8m1xuXSopLyk7DQogICAgICAgIGlmIChtYXRjaCkgew0KICAgICAgICAgIHRoaXMucXVhbGl0eU51bWJlciA9IG1hdGNoWzFdLnRyaW0oKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICBpZiAodmFsdWUuaW5jbHVkZXMoJ+WItuW6puWQjeensO+8micpKSB7DQogICAgICAgIHRoaXMuc2VsZWN0ZWRCYXNpc1R5cGVzLnB1c2goJ3N5c3RlbScpOw0KICAgICAgICBjb25zdCBtYXRjaCA9IHZhbHVlLm1hdGNoKC/liLbluqblkI3np7DvvJooW17vvJtcbl0qKS8pOw0KICAgICAgICBpZiAobWF0Y2gpIHsNCiAgICAgICAgICB0aGlzLnN5c3RlbU5hbWUgPSBtYXRjaFsxXS50cmltKCk7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgaWYgKHZhbHVlLmluY2x1ZGVzKCfmiqXlkYrvvJonKSkgew0KICAgICAgICB0aGlzLnNlbGVjdGVkQmFzaXNUeXBlcy5wdXNoKCdyZXBvcnQnKTsNCiAgICAgICAgY29uc3QgbWF0Y2ggPSB2YWx1ZS5tYXRjaCgv5oql5ZGK77yaKFte77ybXG5dKikvKTsNCiAgICAgICAgaWYgKG1hdGNoKSB7DQogICAgICAgICAgdGhpcy5yZXBvcnROYW1lID0gbWF0Y2hbMV0udHJpbSgpOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIGlmICh2YWx1ZS5pbmNsdWRlcygn5beh5qOA5aSE572a5Y2V5Y+377yaJykpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RlZEJhc2lzVHlwZXMucHVzaCgnaW5zcGVjdGlvbicpOw0KICAgICAgICBjb25zdCBtYXRjaCA9IHZhbHVlLm1hdGNoKC/lt6Hmo4DlpITnvZrljZXlj7fvvJooW17vvJtcbl0qKS8pOw0KICAgICAgICBpZiAobWF0Y2gpIHsNCiAgICAgICAgICB0aGlzLmluc3BlY3Rpb25OdW1iZXIgPSBtYXRjaFsxXS50cmltKCk7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgaWYgKHZhbHVlLmluY2x1ZGVzKCflronnrqHlpITnvZrljZXlj7fvvJonKSkgew0KICAgICAgICB0aGlzLnNlbGVjdGVkQmFzaXNUeXBlcy5wdXNoKCdzYWZldHknKTsNCiAgICAgICAgY29uc3QgbWF0Y2ggPSB2YWx1ZS5tYXRjaCgv5a6J566h5aSE572a5Y2V5Y+377yaKFte77ybXG5dKikvKTsNCiAgICAgICAgaWYgKG1hdGNoKSB7DQogICAgICAgICAgdGhpcy5zYWZldHlOdW1iZXIgPSBtYXRjaFsxXS50cmltKCk7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g6Kej5p6Q5L6d5o2u5YaF5a65DQogICAgICBjb25zdCBjb250ZW50TWF0Y2ggPSB2YWx1ZS5tYXRjaCgv5L6d5o2u5YaF5a6577yaKFteXSopLyk7DQogICAgICBpZiAoY29udGVudE1hdGNoKSB7DQogICAgICAgIHRoaXMuYmFzaXNDb250ZW50ID0gY29udGVudE1hdGNoWzFdLnRyaW0oKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWmguaenOayoeacieaJvuWIsOS+neaNruWGheWuueagh+ivhu+8jOWwhuaVtOS4quWGheWuueS9nOS4uuS+neaNruWGheWuuQ0KICAgICAgICB0aGlzLmJhc2lzQ29udGVudCA9IHZhbHVlOw0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgLyoqIOS+nea<PERSON>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"}, {"version": 3, "sources": ["punishmentBasis-module.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "punishmentBasis-module.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"处罚依据选择\"\r\n    :visible.sync=\"visible\"\r\n    width=\"500px\"\r\n    top=\"5vh\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n    :close-on-click-modal=\"false\"\r\n    custom-class=\"basis-dialog\"\r\n  >\r\n    <div class=\"basis-dialog-content\">\r\n      <!-- 处罚依据选项 -->\r\n      <div class=\"basis-options\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>选择依据类型：</h4>\r\n        <el-checkbox-group v-model=\"selectedBasisTypes\" @change=\"handleBasisTypeChange\">\r\n          <!-- 质量异议单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"checkbox-wrapper\">\r\n                <el-checkbox label=\"quality\" @change=\"handleBasisTypeChange\">质量异议单号</el-checkbox>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"qualityNumber\"\r\n                  placeholder=\"请输入质量异议单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  :disabled=\"!selectedBasisTypes.includes('quality')\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 文件报批单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"checkbox-wrapper\">\r\n                <el-checkbox label=\"report\" @change=\"handleBasisTypeChange\">文件报批单号</el-checkbox>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"reportName\"\r\n                  placeholder=\"请输入文件报批单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  :disabled=\"!selectedBasisTypes.includes('report')\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 巡检处罚单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"checkbox-wrapper\">\r\n                <el-checkbox label=\"inspection\" @change=\"handleBasisTypeChange\">巡检处罚单号</el-checkbox>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"inspectionNumber\"\r\n                  placeholder=\"请输入巡检处罚单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  :disabled=\"!selectedBasisTypes.includes('inspection')\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 安管处罚单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"checkbox-wrapper\">\r\n                <el-checkbox label=\"safety\" @change=\"handleBasisTypeChange\">安管处罚单号</el-checkbox>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"safetyNumber\"\r\n                  placeholder=\"请输入安管处罚单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  :disabled=\"!selectedBasisTypes.includes('safety')\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <!-- 制度名称 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"checkbox-wrapper\">\r\n                <el-checkbox label=\"system\" @change=\"handleBasisTypeChange\">制度名称</el-checkbox>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"systemName\"\r\n                  placeholder=\"请输入制度名称\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  :disabled=\"!selectedBasisTypes.includes('system')\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-checkbox-group>\r\n      </div>\r\n\r\n      <!-- 依据内容 -->\r\n      <div class=\"basis-content\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>依据内容：</h4>\r\n        <div class=\"content-wrapper\">\r\n          <el-input\r\n            v-model=\"basisContent\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入依据内容\"\r\n            @input=\"updateBasisText\"\r\n            class=\"content-textarea\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <div class=\"footer-buttons\">\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确 定</el-button>\r\n        <el-button @click=\"handleClose\">取 消</el-button>\r\n      </div>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PunishmentBasisDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 选中的依据类型（多选）\r\n      selectedBasisTypes: [],\r\n      // 质量异议单号\r\n      qualityNumber: '',\r\n      // 制度名称\r\n      systemName: '',\r\n      // 报告名称\r\n      reportName: '',\r\n      // 巡检处罚单号\r\n      inspectionNumber: '',\r\n      // 安管处罚单号\r\n      safetyNumber: '',\r\n      // 依据内容\r\n      basisContent: '',\r\n      // 预览文本\r\n      previewText: ''\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show(currentValue = '') {\r\n      this.visible = true;\r\n      this.parseCurrentValue(currentValue);\r\n      this.updatePreview();\r\n      // 确保弹窗完全打开后再进行其他操作\r\n      this.$nextTick(() => {\r\n        console.log('弹窗已显示，当前数据：', {\r\n          selectedBasisTypes: this.selectedBasisTypes,\r\n          qualityNumber: this.qualityNumber,\r\n          systemName: this.systemName,\r\n          reportName: this.reportName,\r\n          inspectionNumber: this.inspectionNumber,\r\n          safetyNumber: this.safetyNumber,\r\n          basisContent: this.basisContent\r\n        });\r\n      });\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.selectedBasisTypes = [];\r\n      this.qualityNumber = '';\r\n      this.systemName = '';\r\n      this.reportName = '';\r\n      this.inspectionNumber = '';\r\n      this.safetyNumber = '';\r\n      this.basisContent = '';\r\n      this.previewText = '';\r\n    },\r\n    \r\n    /** 解析当前值 */\r\n    parseCurrentValue(value) {\r\n      if (!value) {\r\n        this.reset();\r\n        return;\r\n      }\r\n\r\n      // 重置选中的类型数组\r\n      this.selectedBasisTypes = [];\r\n\r\n      // 尝试解析现有的依据内容（支持多个类型）\r\n      if (value.includes('质量异议单号：')) {\r\n        this.selectedBasisTypes.push('quality');\r\n        const match = value.match(/质量异议单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.qualityNumber = match[1].trim();\r\n        }\r\n      }\r\n\r\n      if (value.includes('制度名称：')) {\r\n        this.selectedBasisTypes.push('system');\r\n        const match = value.match(/制度名称：([^；\\n]*)/);\r\n        if (match) {\r\n          this.systemName = match[1].trim();\r\n        }\r\n      }\r\n\r\n      if (value.includes('报告：')) {\r\n        this.selectedBasisTypes.push('report');\r\n        const match = value.match(/报告：([^；\\n]*)/);\r\n        if (match) {\r\n          this.reportName = match[1].trim();\r\n        }\r\n      }\r\n\r\n      if (value.includes('巡检处罚单号：')) {\r\n        this.selectedBasisTypes.push('inspection');\r\n        const match = value.match(/巡检处罚单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.inspectionNumber = match[1].trim();\r\n        }\r\n      }\r\n\r\n      if (value.includes('安管处罚单号：')) {\r\n        this.selectedBasisTypes.push('safety');\r\n        const match = value.match(/安管处罚单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.safetyNumber = match[1].trim();\r\n        }\r\n      }\r\n\r\n      // 解析依据内容\r\n      const contentMatch = value.match(/依据内容：([^]*)/);\r\n      if (contentMatch) {\r\n        this.basisContent = contentMatch[1].trim();\r\n      } else {\r\n        // 如果没有找到依据内容标识，将整个内容作为依据内容\r\n        this.basisContent = value;\r\n      }\r\n    },\r\n    \r\n    /** 依据类型变化 */\r\n    handleBasisTypeChange() {\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 更新依据文本 */\r\n    updateBasisText() {\r\n      this.updatePreview();\r\n    },\r\n\r\n\r\n    \r\n    /** 更新预览 */\r\n    updatePreview() {\r\n      const parts = [];\r\n\r\n      // 添加选中的依据类型信息（支持多选）\r\n      if (this.selectedBasisTypes.includes('quality') && this.qualityNumber) {\r\n        parts.push(`质量异议单号：${this.qualityNumber}`);\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('system') && this.systemName) {\r\n        parts.push(`制度名称：${this.systemName}`);\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('report') && this.reportName) {\r\n        parts.push(`报告：${this.reportName}`);\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('inspection') && this.inspectionNumber) {\r\n        parts.push(`巡检处罚单号：${this.inspectionNumber}`);\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('safety') && this.safetyNumber) {\r\n        parts.push(`安管处罚单号：${this.safetyNumber}`);\r\n      }\r\n\r\n      // 添加依据内容\r\n      if (this.basisContent) {\r\n        parts.push(`依据内容：${this.basisContent}`);\r\n      }\r\n\r\n      this.previewText = parts.join('；');\r\n\r\n      console.log('预览更新：', {\r\n        selectedBasisTypes: this.selectedBasisTypes,\r\n        qualityNumber: this.qualityNumber,\r\n        systemName: this.systemName,\r\n        reportName: this.reportName,\r\n        inspectionNumber: this.inspectionNumber,\r\n        safetyNumber: this.safetyNumber,\r\n        basisContent: this.basisContent,\r\n        previewText: this.previewText\r\n      });\r\n    },\r\n    \r\n    /** 确认选择 */\r\n    handleConfirm() {\r\n      this.updatePreview();\r\n\r\n      // 验证是否填写了必要信息\r\n      if (this.selectedBasisTypes.length === 0) {\r\n        this.$message.warning('请至少选择一个依据类型');\r\n        return;\r\n      }\r\n\r\n      // 验证选中的每个类型是否都填写了对应的内容\r\n      if (this.selectedBasisTypes.includes('quality') && !this.qualityNumber) {\r\n        this.$message.warning('请输入质量异议单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('system') && !this.systemName) {\r\n        this.$message.warning('请输入制度名称');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('report') && !this.reportName) {\r\n        this.$message.warning('请输入文件报批单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('inspection') && !this.inspectionNumber) {\r\n        this.$message.warning('请输入巡检处罚单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('safety') && !this.safetyNumber) {\r\n        this.$message.warning('请输入安管处罚单号');\r\n        return;\r\n      }\r\n\r\n      if (!this.basisContent) {\r\n        this.$message.warning('请输入依据内容');\r\n        return;\r\n      }\r\n\r\n      this.$emit('select', this.previewText);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 弹窗内容容器 */\r\n.basis-dialog-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 章节标题样式 */\r\n.section-title {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  margin: 0 0 15px 0;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 顶级标题样式（选择依据类型） */\r\n.basis-options .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 处罚依据选项样式 */\r\n.basis-options {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-item {\r\n  margin-bottom: 15px;\r\n  padding: 0;\r\n}\r\n\r\n/* 新的行布局 */\r\n.basis-row {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  min-height: 36px;\r\n}\r\n\r\n.checkbox-wrapper {\r\n  width: 120px;\r\n  flex-shrink: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 36px;\r\n}\r\n\r\n.input-wrapper {\r\n  width: calc(100% - 135px);\r\n  margin-left: 14px;\r\n}\r\n\r\n.aligned-input {\r\n  width: 100%;\r\n}\r\n\r\n.aligned-input ::v-deep .el-input__inner {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 多选框对齐样式 */\r\n.checkbox-wrapper ::v-deep .el-checkbox {\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin: 0;\r\n}\r\n\r\n.checkbox-wrapper ::v-deep .el-checkbox__input {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 8px;\r\n}\r\n\r\n.checkbox-wrapper ::v-deep .el-checkbox__inner {\r\n  width: 16px;\r\n  height: 16px;\r\n}\r\n\r\n.checkbox-wrapper ::v-deep .el-checkbox__label {\r\n  font-size: 14px;\r\n  line-height: 36px;\r\n  padding-left: 8px;\r\n  color: #606266;\r\n}\r\n\r\n/* 依据内容区域样式 */\r\n.basis-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-content .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.content-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.content-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.content-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 预览区域样式 */\r\n.preview-area {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.preview-area .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.preview-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.preview-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.preview-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  background-color: #ffffff;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 专门的弹窗样式 */\r\n::v-deep .basis-dialog {\r\n  margin-top: 5vh !important;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__body {\r\n  padding: 25px;\r\n  max-height: 75vh;\r\n  overflow-y: auto;\r\n  background-color: #ffffff;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__header {\r\n  padding: 20px 25px 15px;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__footer {\r\n  padding: 15px 25px 20px;\r\n  text-align: right;\r\n}\r\n\r\n/* 底部按钮样式 */\r\n.footer-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n}\r\n\r\n/* 多选框组样式 */\r\n::v-deep .el-checkbox-group {\r\n  width: 100%;\r\n}\r\n\r\n::v-deep .el-checkbox {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n::v-deep .el-checkbox__label {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 必填标识符样式 */\r\n.required-mark {\r\n  color: #F56C6C;\r\n  margin-right: 4px;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"]}]}