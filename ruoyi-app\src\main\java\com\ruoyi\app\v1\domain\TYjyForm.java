package com.ruoyi.app.v1.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
/**
 * TYjyForm对象 t_yjy_form
 * 
 * <AUTHOR>
 * @date 2024-10-21
 */
public class TYjyForm extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;
    private List<Long> ids;
    private Long answerId;
    /** 纬度id */
    @Excel(name = "纬度id")
    private Long dimensionalityId;

    private String dimensionalityPath;

    private List<Long> dimensionalityIds;

    @Excel(name = "纬度名称集合")
    private String dimensionalityNames;


    @Excel(name = "问题名称")
    private String formQuestion;

    /** $column.columnComment */
    @Excel(name = "纬度id")
    private String frequency;


    /** 类型 */
    @Excel(name = "类型")
    private String formType;

    /** $column.columnComment */
    private String delFlag;

    /** $column.columnComment */
    @Excel(name = "值")
    private String creatorDeptCode;
    @Excel(name = "值")
    private String creatorDeptName;



    //审核人列表
    private String checkerList;
    private String deptCode;
    private String deptName;
    private String fcDate;


    private List<String> deptCodes=new ArrayList<>();

    private List<TYjyAnswer> answerHistory=new ArrayList<>();

    private String deptNames;

    /** $column.columnComment */
    private Date deleteTime;

    private String workNo;


    private String distributeDept;

    List<JSONObject> checkerJsonList;

    private String formNote;
    private String formNote1;
    private String noteDept;


    private Double maximum;
    private Double minimum;

    private String unit;
    private String status;
    private String formValue;
    private String formFile;
    private String creatorNo;
    private String creatorName;
    private String deadlineSwitch;
    private String deadlineDate;
    private String mailSwitch;
    private String mailDate;

    private String submitDate;

    private String checkWorkNo;
    private String checkUserName;


    private String reason;
    private String measure;

    private String assessment;

    //设置邮件通知时间
    private Integer countdown;


    private String deptShow;

    private String dimensionalityNameExport;

    private String formQuestionExport;

    private Integer formQuestionSort;

    private Integer mailNum;

    private String mailTime;

    private Long version;

    private String checkHistory;

    private String connectType;

    private Long connectId;

    private String ruleType;

    private String hiddenFlag;
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public String getDimensionalityNames() {
        return dimensionalityNames;
    }

    public void setDimensionalityNames(String dimensionalityNames) {
        this.dimensionalityNames = dimensionalityNames;
    }

    public void setDimensionalityId(Long dimensionalityId)
    {
        this.dimensionalityId = dimensionalityId;
    }

    public Long getDimensionalityId() 
    {
        return dimensionalityId;
    }
    public void setFrequency(String frequency) 
    {
        this.frequency = frequency;
    }

    public String getFrequency() 
    {
        return frequency;
    }
    public void setFormType(String formType) 
    {
        this.formType = formType;
    }

    public String getFormType() 
    {
        return formType;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }
    public void setDeleteTime(Date deleteTime) 
    {
        this.deleteTime = deleteTime;
    }

    public Date getDeleteTime() 
    {
        return deleteTime;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getFormQuestion() {
        return formQuestion;
    }

    public void setFormQuestion(String formQuestion) {
        this.formQuestion = formQuestion;
    }

    public List<Long> getDimensionalityIds() {
        return dimensionalityIds;
    }

    public void setDimensionalityIds(List<Long> dimensionalityIds) {
        this.dimensionalityIds = dimensionalityIds;
    }

    public List<String> getDeptCodes() {
        return deptCodes;
    }

    public void setDeptCodes(List<String> deptCodes) {
        this.deptCodes = deptCodes;
    }

    public String getDeptNames() {
        return deptNames;
    }

    public void setDeptNames(String deptNames) {
        this.deptNames = deptNames;
    }

    public String getCreatorDeptCode() {
        return creatorDeptCode;
    }

    public void setCreatorDeptCode(String creatorDeptCode) {
        this.creatorDeptCode = creatorDeptCode;
    }

    public String getCreatorDeptName() {
        return creatorDeptName;
    }

    public void setCreatorDeptName(String creatorDeptName) {
        this.creatorDeptName = creatorDeptName;
    }

    public List<TYjyAnswer> getAnswerHistory() {
        return answerHistory;
    }

    public void setAnswerHistory(List<TYjyAnswer> answerHistory) {
        this.answerHistory = answerHistory;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getDistributeDept() {
        return distributeDept;
    }

    public void setDistributeDept(String distributeDept) {
        this.distributeDept = distributeDept;
    }


    public String getCheckerList() {
        return checkerList;
    }

    public void setCheckerList(String checkerList) {
        this.checkerList = checkerList;
    }

    public List<JSONObject> getCheckerJsonList() {
        return checkerJsonList;
    }

    public void setCheckerJsonList(List<JSONObject> checkerJsonList) {
        this.checkerJsonList = checkerJsonList;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getFcDate() {
        return fcDate;
    }

    public void setFcDate(String fcDate) {
        this.fcDate = fcDate;
    }

    public String getDimensionalityPath() {
        return dimensionalityPath;
    }

    public void setDimensionalityPath(String dimensionalityPath) {
        this.dimensionalityPath = dimensionalityPath;
    }

    public String getFormNote() {
        return formNote;
    }

    public void setFormNote(String formNote) {
        this.formNote = formNote;
    }

    public String getNoteDept() {
        return noteDept;
    }

    public void setNoteDept(String noteDept) {
        this.noteDept = noteDept;
    }

    public Double getMaximum() {
        return maximum;
    }

    public void setMaximum(Double maximum) {
        this.maximum = maximum;
    }

    public Double getMinimum() {
        return minimum;
    }

    public void setMinimum(Double minimum) {
        this.minimum = minimum;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getFormNote1() {
        return formNote1;
    }

    public void setFormNote1(String formNote1) {
        this.formNote1 = formNote1;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFormValue() {
        return formValue;
    }

    public void setFormValue(String formValue) {
        this.formValue = formValue;
    }

    public String getCreatorNo() {
        return creatorNo;
    }

    public void setCreatorNo(String creatorNo) {
        this.creatorNo = creatorNo;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getDeadlineSwitch() {
        return deadlineSwitch;
    }

    public void setDeadlineSwitch(String deadlineSwitch) {
        this.deadlineSwitch = deadlineSwitch;
    }

    public String getDeadlineDate() {
        return deadlineDate;
    }

    public void setDeadlineDate(String deadlineDate) {
        this.deadlineDate = deadlineDate;
    }

    public String getMailSwitch() {
        return mailSwitch;
    }

    public void setMailSwitch(String mailSwitch) {
        this.mailSwitch = mailSwitch;
    }

    public String getCheckWorkNo() {
        return checkWorkNo;
    }

    public void setCheckWorkNo(String checkWorkNo) {
        this.checkWorkNo = checkWorkNo;
    }

    public String getCheckUserName() {
        return checkUserName;
    }

    public void setCheckUserName(String checkUserName) {
        this.checkUserName = checkUserName;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getMeasure() {
        return measure;
    }

    public void setMeasure(String measure) {
        this.measure = measure;
    }

    public String getMailDate() {
        return mailDate;
    }

    public void setMailDate(String mailDate) {
        this.mailDate = mailDate;
    }

    public String getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(String submitDate) {
        this.submitDate = submitDate;
    }

    public String getAssessment() {
        return assessment;
    }

    public void setAssessment(String assessment) {
        this.assessment = assessment;
    }

    public Integer getCountdown() {
        return countdown;
    }

    public void setCountdown(Integer countdown) {
        this.countdown = countdown;
    }

    public String getDeptShow() {
        return deptShow;
    }

    public void setDeptShow(String deptShow) {
        this.deptShow = deptShow;
    }

    public String getDimensionalityNameExport() {
        return dimensionalityNameExport;
    }

    public void setDimensionalityNameExport(String dimensionalityNameExport) {
        this.dimensionalityNameExport = dimensionalityNameExport;
    }

    public String getFormQuestionExport() {
        return formQuestionExport;
    }

    public void setFormQuestionExport(String formQuestionExport) {
        this.formQuestionExport = formQuestionExport;
    }

    public Integer getMailNum() {
        return mailNum;
    }

    public void setMailNum(Integer mailNum) {
        this.mailNum = mailNum;
    }

    public String getMailTime() {
        return mailTime;
    }

    public void setMailTime(String mailTime) {
        this.mailTime = mailTime;
    }

    public Long getAnswerId() {
        return answerId;
    }

    public void setAnswerId(Long answerId) {
        this.answerId = answerId;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getCheckHistory() {
        return checkHistory;
    }

    public void setCheckHistory(String checkHistory) {
        this.checkHistory = checkHistory;
    }

    public String getConnectType() {
        return connectType;
    }

    public void setConnectType(String connectType) {
        this.connectType = connectType;
    }

    public Long getConnectId() {
        return connectId;
    }

    public void setConnectId(Long connectId) {
        this.connectId = connectId;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getFormQuestionSort() {
        return formQuestionSort;
    }

    public void setFormQuestionSort(Integer formQuestionSort) {
        this.formQuestionSort = formQuestionSort;
    }

    public String getFormFile() {
        return formFile;
    }

    public void setFormFile(String formFile) {
        this.formFile = formFile;
    }

    public String getHiddenFlag() {
        return hiddenFlag;
    }

    public void setHiddenFlag(String hiddenFlag) {
        this.hiddenFlag = hiddenFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dimensionalityId", getDimensionalityId())
            .append("formQuestion", getFormQuestion())
            .append("dimensionalityNames", getDimensionalityNames())
            .append("frequency", getFrequency())
            .append("formType", getFormType())
            .append("createTime", getCreateTime())
            .append("creatorDeptName", getCreatorDeptName())
            .append("creatorDeptCode", getCreatorDeptCode())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("deleteTime", getDeleteTime())
            .toString();
    }
}
