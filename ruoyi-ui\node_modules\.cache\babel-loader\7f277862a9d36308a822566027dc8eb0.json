{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\dgcb\\driver\\driver.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\dgcb\\driver\\driver.js", "mtime": 1756456282383}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDriver", "query", "request", "url", "method", "params", "listAllDriver", "getXctgDriverUserList", "getXctgDriverCarList", "getAllListByCompanyId", "getDriver", "id", "addDriver", "data", "updateDriver", "delDriver", "exportDriver"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/api/dgcb/driver/driver.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询吨钢承包司机信息列表\r\nexport function listDriver(query) {\r\n  return request({\r\n    url: '/web/driver/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询吨钢承包司机所有信息列表\r\nexport function listAllDriver(query) {\r\n  return request({\r\n    url: '/web/driver/getAllList',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getXctgDriverUserList(query) {\r\n  return request({\r\n    url: '/web/driver/getXctgDriverUserList',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getXctgDriverCarList(query) {\r\n  return request({\r\n    url: '/web/driver/getXctgDriverCarList',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询指定公司下所有的司主信息\r\nexport function getAllListByCompanyId(query) {\r\n  return request({\r\n    url: '/web/driver/getAllListByCompanyId',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询吨钢承包司机信息详细\r\nexport function getDriver(id) {\r\n  return request({\r\n    url: '/web/driver/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增吨钢承包司机信息\r\nexport function addDriver(data) {\r\n  return request({\r\n    url: '/web/driver',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改吨钢承包司机信息\r\nexport function updateDriver(data) {\r\n  return request({\r\n    url: '/web/driver',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除吨钢承包司机信息\r\nexport function delDriver(id) {\r\n  return request({\r\n    url: '/web/driver/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 导出吨钢承包司机信息\r\nexport function exportDriver(query) {\r\n  return request({\r\n    url: '/web/driver/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACL,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASM,qBAAqBA,CAACN,KAAK,EAAE;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASO,oBAAoBA,CAACP,KAAK,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,qBAAqBA,CAACR,KAAK,EAAE;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,SAASA,CAACC,EAAE,EAAE;EAC5B,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc,GAAGQ,EAAE;IACxBP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,MAAM;IACdS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAE,KAAK;IACbS,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACJ,EAAE,EAAE;EAC5B,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc,GAAGQ,EAAE;IACxBP,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,YAAYA,CAACf,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}