<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-row>
        <el-form-item label="工号" prop="workNo">
          <el-input
            v-model="queryParams.workNo"
            placeholder="请输入工号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入姓名"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="身份" prop="assessRole">
          <el-select v-model="queryParams.assessRole" placeholder="请选择身份" clearable>
            <el-option
              v-for="dict in dicts.self_assess_role"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门" prop="deptId">
          <treeselect style="width: 200px;" v-model="queryParams.deptId" :multiple="false" :options="deptOptions" :normalizer="normalizer" :disable-branch-nodes="true" placeholder="请选择部门" />
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="是否100%挂钩公司效益" prop="benefitLinkFlag" label-width="200px">
          <el-select v-model="queryParams.benefitLinkFlag" placeholder="请选择" clearable>
            <el-option
              v-for="dict in dicts.sys_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否挂勾钢铁轧平均分" prop="averageLinkFlag" label-width="192px">
          <el-select v-model="queryParams.averageLinkFlag" placeholder="请选择" clearable>
            <el-option
              v-for="dict in dicts.sys_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-row>
      
      
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="small"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="small"
          @click="handleExport"
        >导出</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-upload
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :disabled="upload.isUploading"
        :action="upload.url"
        :show-file-list="false"
        :multiple="false"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess">
            <el-button size="small" type="warning" plain icon="el-icon-download">导入</el-button>
        </el-upload>
      </el-col>
      <el-col :span="1.5">
          <el-button size="small" type="info" plain icon="el-icon-link" @click="downloadTemplate">导入模板下载</el-button>
      </el-col>
      <el-col :span="1.5">
          <el-button size="small" type="info" plain icon="el-icon-link" @click="permissionRefresh">权限刷新</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="selfAssessUserList">
      <!-- <el-table-column label="编号" align="center" prop="id" /> -->
      <el-table-column label="工号" align="center" prop="workNo" width="120"/>
      <el-table-column label="姓名" align="center" prop="name" width="120"/>
      <el-table-column label="身份" align="center" prop="assessRole" width="120">
        <template slot-scope="scope">
          {{ dicts.self_assess_role[scope.row.assessRole]["label"] }}
        </template>
      </el-table-column>
      <el-table-column label="部门" align="center">
        <template slot-scope="scope">
          <span v-for="item, index in scope.row.deptList" v-bind:key="index">
            {{ scope.row.deptList.length > 1 && index + 1 != scope.row.deptList.length ? item.deptName + ", " : item.deptName}}  
          </span>
        </template>
      </el-table-column>
      <el-table-column label="职务" align="center" prop="job" width="120"/>
      <el-table-column label="岗位类型" align="center" prop="postType" width="120">
        <template slot-scope="scope">
          {{ dicts.self_assess_post_type[scope.row.postType]["label"] }}
        </template>
      </el-table-column>
      <el-table-column label="是否100%挂钩公司效益" align="center" prop="benefitLinkFlag" width="200">
        <template slot-scope="scope">
          <el-tag :type="scope.row.benefitLinkFlag == 'Y' ? 'success' : 'info'">{{ scope.row.benefitLinkFlag == "Y" ? "是" : " 否" }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否挂勾钢铁轧平均分" align="center" prop="averageLinkFlag" width="200">
        <template slot-scope="scope">
          <el-tag :type="scope.row.averageLinkFlag == 'Y' ? 'success' : 'info'">{{ scope.row.averageLinkFlag == "Y" ? "是" : " 否" }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleConfig(scope.row)"
          >配置</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改绩效考核-干部自评人员配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" label-position="top">
          <el-row :gutter="20">
            <el-col :span="12">
                <el-form-item label="工号" prop="workNo">
                  <el-input v-model="form.workNo" placeholder="请输入工号" />
                </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="姓名" prop="name">
                <el-input v-model="form.name" placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="部门" prop="deptIds">
                <treeselect v-model="form.deptIds" @input="deptChange" :multiple="deptMultiple" :options="deptOptions" :normalizer="normalizer" :disable-branch-nodes="true" placeholder="请选择部门" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份" prop="assessRole">
                <el-radio-group v-model="form.assessRole">
                  <el-radio
                    v-for="dict in dicts.self_assess_role"
                    @change="roleChange"
                    :key="dict.value"
                    :label="dict.value"
                  >{{dict.label}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="职务" prop="job">
                <el-input v-model="form.job" placeholder="请输入职务" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="交叉评分领导" prop="postType">
                <el-select v-model="form.leaders" multiple placeholder="请选择">
                  <el-option
                    v-for="item in leaderOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="岗位类型" prop="postType">
                <el-radio-group v-model="form.postType">
                  <el-radio
                    v-for="dict in dicts.self_assess_post_type"
                    :key="dict.value"
                    :label="dict.value"
                  >{{dict.label}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="是否100%挂钩公司效益" prop="benefitLinkFlag">
                <el-radio-group v-model="form.benefitLinkFlag">
                  <el-radio
                    v-for="dict in dicts.sys_yes_no"
                    :key="dict.value"
                    :label="dict.value"
                  >{{dict.label}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否挂勾钢铁轧平均分" prop="averageLinkFlag">
                <el-radio-group v-model="form.averageLinkFlag">
                  <el-radio
                    v-for="dict in dicts.sys_yes_no"
                    :key="dict.value"
                    :label="dict.value"
                  >{{dict.label}}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入结果对话框 -->
    <el-dialog title="导入结果" :visible.sync="openImportRes" width="1000px" append-to-body>
      <el-table :data="importRes">
        <el-table-column label="工号" align="center" prop="workNo"/>
        <!-- <el-table-column label="姓名" align="center" prop="name"/> -->
        <el-table-column label="身份" align="center" prop="assessRole">
          <template slot-scope="scope">
            {{ dicts.self_assess_role[scope.row.assessRole]["label"] }}
          </template>
        </el-table-column>
        <el-table-column label="部门" align="center">
          <template slot-scope="scope">
            <span v-for="item, index in scope.row.deptList" v-bind:key="index" :class="item.deptStatus ? '':'redtext'">
              {{ scope.row.deptList.length > 1 && index + 1 != scope.row.deptList.length ? item.deptName + ", " : item.deptName}}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="是否100%挂钩公司效益" align="center" prop="benefitLinkFlag">
          <template slot-scope="scope">
            <el-tag :type="scope.row.averageLinkFlag == 'Y' ? 'success' : 'info'">{{ scope.row.averageLinkFlag == "Y" ? "是" : " 否" }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="是否挂勾钢铁轧平均分" align="center" prop="averageLinkFlag">
          <template slot-scope="scope">
            <el-tag :type="scope.row.averageLinkFlag == 'Y' ? 'success' : 'info'">{{ scope.row.averageLinkFlag == "Y" ? "是" : " 否" }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="导入结果" align="center" prop="msg" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { getTemplateFile } from "@/api/templateFile/list";
import { listSelfAssessUser, getSelfAssessUser, delSelfAssessUser, addSelfAssessUser, updateSelfAssessUser, permissionRefresh, listSelfAssessUserAll } from "@/api/assess/self/user";
import { listDept } from "@/api/assess/lateral/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "SelfAssessUser",
  components: {
    Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 绩效考核-干部自评人员配置表格数据
      selfAssessUserList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        workNo: null,
        name: null,
        deptId: null,
        assessRole: null,
        benefitLinkFlag: null,
        averageLinkFlag: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deptIds:[{required:true, message:"请选择部门"}],
        workNo:[{required:true, message:"请填写工号"}],
        name:[{required:true, message:"请输入姓名"}],
        assessRole:[{required:true, message:"请选择身份"}]
      },
      // 字典
      dicts:{
        self_assess_role:[],
        sys_yes_no:[],
        self_assess_post_type:[]
      },
      // 部门下拉树
      deptOptions:[],
      // 领导下拉框
      leaderOptions:[],
      // 导入参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/web/selfAssessUser/importInfo",
      },
      // 导入结果
      importRes:[],
      openImportRes:false,
      // 部门是否可多选
      deptMultiple:false,
    };
  },
  created() {
    this.initPageData();
  },

  // 监听路由变化，确保每次进入页面都重新获取数据
  watch: {
    '$route'(to) {
      // 当路由发生变化时，重新初始化页面数据
      if (to.path === '/assess/self/config') {
        this.initPageData();
      }
    }
  },

  // 路由更新时的钩子
  beforeRouteUpdate(to, from, next) {
    // 在当前路由改变，但是该组件被复用时调用
    this.initPageData();
    next();
  },
  methods: {
    // 初始化页面数据
    initPageData() {
      // 重置数据
      this.selfAssessUserList = [];
      this.total = 0;
      this.deptOptions = [];
      this.leaderOptions = [];

      // 获取数据
      this.getList();
      this.getTreeselect();
      this.getDicts("self_assess_role").then(response => {
        this.dicts.self_assess_role = this.formatterDict(response.data);
      });
      this.getDicts("sys_yes_no").then(response => {
        this.dicts.sys_yes_no = this.formatterDict(response.data);
      });
      this.getDicts("self_assess_post_type").then(response => {
        this.dicts.self_assess_post_type = this.formatterDict(response.data);
      });
      this.getLeaderList();
    },

    formatterDict(dict){
      let result = []
      dict.forEach(dict => {
        result.push({
          label:dict.dictLabel,
          value:dict.dictValue
        })
      });
      return result;
    },

    /**获取条线线领导列表 */
    getLeaderList(){
      listSelfAssessUserAll({assessRole:"2"}).then(res => {
        console.log(res)
        if(res.code == 200){
          let leaderOptions = [];
          res.data.forEach(item => {
            leaderOptions.push({
              label:item.name,
              value:item.workNo
            })
          })
          this.leaderOptions = leaderOptions;
        }
      })
    },
    /** 查询绩效考核-干部自评人员配置列表 */
    getList() {
      this.loading = true;
      listSelfAssessUser(this.queryParams).then(response => {
        this.selfAssessUserList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        workNo: null,
        name: null,
        assessRole: '0',
        benefitLinkFlag: 'N',
        averageLinkFlag: 'N',
        postType:'0',
        leaders:[]
      };
      this.deptMultiple = false;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加自评人员配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id
      getSelfAssessUser({id:id}).then(response => {
        this.form = response.data;
        if(this.form.assessRole == "2"){
          this.deptMultiple = true;
        }else{
          this.deptMultiple = false;
        }
        this.open = true;
        this.title = "修改自评人员配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      console.log(this.form)
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(!Array.isArray(this.form.deptIds)){
            this.form.deptIds = [this.form.deptIds]
          }
          if (this.form.id != null) {
            updateSelfAssessUser(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSelfAssessUser(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id ;
      this.$modal.confirm('是否确认删除编号为"' + id + '"的数据项？').then(function() {
        return delSelfAssessUser({id:id});
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('selfAssessUser/selfAssessUser/export', {
        ...this.queryParams
      }, `selfAssessUser_${new Date().getTime()}.xlsx`)
    },
    /** 转换横向评价部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      };
    },
	  /** 查询横向评价部门下拉树结构 */
    getTreeselect() {
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, "deptId", "parentId");
      });
    },

    /** 配置点击事件 */
    handleConfig(row){
      this.$router.push({
        path:"/assess/self/user/detail",
        query:{
          userId:row.id
        }
      })
    },
    // 身份改变事件
    roleChange(value){
      console.log(value)
      if(value == '2'){
        if(this.form.deptIds){
          if(!Array.isArray(this.form.deptIds)){
            this.form.deptIds = [this.form.deptIds]
          }
        }
        this.deptMultiple = true;
      }else{
        if(this.form.deptIds){
          this.form.deptIds = this.form.deptIds[0]
        }
        this.deptMultiple = false;
      }
    },
    // 部门改变事件
    deptChange(value){
      console.log(value)
    },
    
    handleFileUploadProgress(){
        this.upload.isUploading = true;
    },
    handleFileSuccess(response){
        this.upload.isUploading = false;
        console.log(response)
        this.handleQuery();
        this.importRes = response.data;
        this.openImportRes = true;
    },

    // 模板下载
    downloadTemplate(){
      getTemplateFile({id:"41"}).then(res => {
          if(res.code == 200){
            let localUrl = window.location.host;
            if(localUrl === "************:8099"){
              res.data.url = res.data.url.replace("ydxt.citicsteel.com:8099","************:8099");
            }
            let url = res.data.url;
            window.open(url);
          }
      })
    },

    permissionRefresh(){
      permissionRefresh().then(res => {
        if(res.code == 200){
          this.$modal.msgSuccess("刷新成功");
        }
      })
    },
  }
};
</script>
<style>
.redtext{
  color: red;
}
</style>
