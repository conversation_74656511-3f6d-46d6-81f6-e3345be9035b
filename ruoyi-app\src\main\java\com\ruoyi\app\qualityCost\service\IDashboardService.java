package com.ruoyi.app.qualityCost.service;

import com.ruoyi.app.qualityCost.domain.*;

public interface IDashboardService {


    PieChart getPieChartData(QualityCostDetail qualityCostDetail);

    MultiLineChart getMultiLineChartData(QualityCostDetail qualityCostDetail);

    QualityCostDetail getQualityCostDetail(QualityCostDetail qualityCostDetail);

    ExternalCostDetailChart getExternalCostDetail(QualityCostDetail qualityCostDetail);

    InternalCostDetailChart getInternalCostDetail(QualityCostDetail qualityCostDetail);

    ComboChart getComboChartDetail(QualityCostDetail qualityCostDetail);

    WaterfallChart getWaterfallChartDetail(QualityCostDetail qualityCostDetail);

    ScrapLossChart getScrapLossChartDetailsDetail(QualityCostDetail qualityCostDetail);

    QualityObjectionLossChart getQualityObjectionLossDetail(QualityCostDetail qualityCostDetail);

    FactoryRejectionChart getFactoryRejectionChartDetail(QualityCostDetail qualityCostDetail);

    FactoryScrapChart getFactoryScrapChartDetail(QualityCostDetail qualityCostDetail);

    FactoryContractChart getFactoryContractChartDetail(QualityCostDetail qualityCostDetail);

    FactoryReturnChart getFactoryReturnChartDetail(QualityCostDetail qualityCostDetail);

}
