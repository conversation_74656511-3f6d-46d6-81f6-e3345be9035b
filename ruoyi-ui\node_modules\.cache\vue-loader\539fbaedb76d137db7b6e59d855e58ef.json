{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\supplier\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\supplier\\index.vue", "mtime": 1756456282822}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgcmVxdWVzdCBmcm9tICdAL3V0aWxzL3JlcXVlc3QnDQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gJ0AvdXRpbHMvYXV0aCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnU3VwcGxpZXJJbmZvJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g5L6b5bqU5ZWG5L+h5oGvDQogICAgICBzdXBwbGllckluZm86IHt9LA0KICAgICAgc3VwcGxpZXJDb2RlOiAnJywNCiAgICAgIC8vIOS6uuWRmOWIl+ihqA0KICAgICAgdXNlckxpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHN1cHBseUNvZGU6IG51bGwsDQogICAgICAgIHVzZXJOYW1lOiBudWxsLA0KICAgICAgICBpZGNhcmQ6IG51bGwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICB1c2VyTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLflp5PlkI3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBpZGNhcmQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6Lqr5Lu96K+B5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlspfkvY3or4bliKvljaENCiAgICAgIGZhY0RpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgZmFjRm9ybToge30sDQogICAgICBmYWNGb3JtSXRlbXM6IFsNCiAgICAgICAgeyBmaWVsZDogJ3VzZXJQb3N0JywgdGl0bGU6ICflspfkvY3lkI3np7AnLCBzcGFuOiAyNCwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlVGV4dGFyZWEnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeWyl+S9jeWQjeensCcsIHJvd3M6IDIgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICd1c2VyRmFjQ2xhc3MnLCB0aXRsZTogJ+Wyl+S9jeePree7hCcsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5bKX5L2N54+t57uEJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ3VzZXJEZXB0TmFtZScsIHRpdGxlOiAn5omA5bGe6YOo6ZeoJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXmiYDlsZ7pg6jpl6gnIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAndXNlckZhY1dvcmsnLCB0aXRsZTogJ+Wyl+S9jeaPj+i/sCcsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5bKX5L2N5o+P6L+wJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ3VzZXJUaW1lQmVnaW4nLCB0aXRsZTogJ+WFpeWOguaXtumXtCcsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHR5cGU6ICdkYXRlJywgcGxhY2Vob2xkZXI6ICfpgInmi6nml6XmnJ8nIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAndXNlclRpbWVFbmQnLCB0aXRsZTogJ+emu+WOguaXtumXtCcsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHR5cGU6ICdkYXRlJywgcGxhY2Vob2xkZXI6ICfpgInmi6nml6XmnJ8nIH0gfSB9LA0KICAgICAgICB7DQogICAgICAgICAgZmllbGQ6ICdzdGF0ZScsDQogICAgICAgICAgdGl0bGU6ICfnirbmgIEnLA0KICAgICAgICAgIHNwYW46IDI0LA0KICAgICAgICAgIGl0ZW1SZW5kZXI6IHsNCiAgICAgICAgICAgIG5hbWU6ICdWeGVTZWxlY3QnLA0KICAgICAgICAgICAgb3B0aW9uczogWw0KICAgICAgICAgICAgICB7IGxhYmVsOiAn6LW36I2JJywgdmFsdWU6IDAgfSwNCiAgICAgICAgICAgICAgeyBsYWJlbDogJ+WIhuWOguWuoeaguOS6uicsIHZhbHVlOiAxIH0sDQogICAgICAgICAgICAgIHsgbGFiZWw6ICfkurrlipvotYTmupDpg6gnLCB2YWx1ZTogMiB9LA0KICAgICAgICAgICAgICB7IGxhYmVsOiAn6YCA5ZueJywgdmFsdWU6IC0xIH0sDQogICAgICAgICAgICAgIHsgbGFiZWw6ICfnpoHnlKgnLCB2YWx1ZTogMTAxIH0sDQogICAgICAgICAgICAgIHsgbGFiZWw6ICflrqHmoLjpgJrov4cnLCB2YWx1ZTogOTkgfSwNCiAgICAgICAgICAgICAgeyBsYWJlbDogJ+WIoOmZpCcsIHZhbHVlOiAxMDIgfQ0KICAgICAgICAgICAgXSwNCiAgICAgICAgICAgIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36YCJ5oupJyB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgLy8g5YGl5bq35L+h5oGvDQogICAgICBoZWFsdGhEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGhlYWx0aEZvcm06IHt9LA0KICAgICAgaGVhbHRoRm9ybUl0ZW1zOiBbDQogICAgICAgIHsgZmllbGQ6ICdoZWFsZGF0ZScsIHRpdGxlOiAn5L2T5qOA5pel5pyfJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgdHlwZTogJ2RhdGUnLCBwbGFjZWhvbGRlcjogJ+mAieaLqeaXpeacnycgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdob3MnLCB0aXRsZTogJ+WMu+mZoicsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5Yy76ZmiJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWx0eicsIHRpdGxlOiAn5L2T6YeNJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXkvZPph40nIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbHR6enMnLCB0aXRsZTogJ+S9k+mHjeaMh+aVsCcsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5L2T6YeN5oyH5pWwJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxwdHQnLCB0aXRsZTogJ+ihgOezlicsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl6KGA57OWJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxzc3knLCB0aXRsZTogJ+aUtue8qeWOiycsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5pS257yp5Y6LJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxzenknLCB0aXRsZTogJ+iIkuW8oOWOiycsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl6IiS5byg5Y6LJyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWx6ZGdjJywgdGl0bGU6ICfmgLvog4blm7rphocnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeaAu+iDhuWbuumGhycgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFsZ3lzeicsIHRpdGxlOiAn55SY5rK55LiJ6YWvJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXnlJjmsrnkuInpha8nIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbGdhJywgdGl0bGU6ICfosLfmsKjphbDovazogr3phbYnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeiwt+awqOmFsOi9rOiCvemFticgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFsZ2InLCB0aXRsZTogJ+iwt+S4mei9rOawqOmFticsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl6LC35LiZ6L2s5rCo6YW2JyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWxnYycsIHRpdGxlOiAn6LC36I2J6L2s5rCo6YW2Jywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXosLfojYnovazmsKjphbYnIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbG5zZCcsIHRpdGxlOiAn5bC/57Sg5rCuJywgc3BhbjogMTIsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZUlucHV0JywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXlsL/ntKDmsK4nIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbGpnJywgdGl0bGU6ICfogozphZAnLCBzcGFuOiAxMiwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlSW5wdXQnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeiCjOmFkCcgfSB9IH0sDQogICAgICAgIHsgZmllbGQ6ICdoZWFseGQnLCB0aXRsZTogJ+W/g+eUteWbvicsIHNwYW46IDEyLCBpdGVtUmVuZGVyOiB7IG5hbWU6ICdWeGVJbnB1dCcsIHByb3BzOiB7IHBsYWNlaG9sZGVyOiAn6K+36L6T5YWl5b+D55S15Zu+JyB9IH0gfSwNCiAgICAgICAgeyBmaWVsZDogJ2hlYWx4aicsIHRpdGxlOiAn5bCP57uTJywgc3BhbjogMjQsIGl0ZW1SZW5kZXI6IHsgbmFtZTogJ1Z4ZVRleHRhcmVhJywgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fovpPlhaXlsI/nu5MnLCByb3dzOiAyIH0gfSB9LA0KICAgICAgICB7IGZpZWxkOiAnaGVhbGp5JywgdGl0bGU6ICflu7rorq4nLCBzcGFuOiAyNCwgaXRlbVJlbmRlcjogeyBuYW1lOiAnVnhlVGV4dGFyZWEnLCBwcm9wczogeyBwbGFjZWhvbGRlcjogJ+ivt+i+k+WFpeW7uuiuricsIHJvd3M6IDIgfSB9IH0sDQogICAgICAgIHsNCiAgICAgICAgICBmaWVsZDogJ3N0YXRlJywNCiAgICAgICAgICB0aXRsZTogJ+eKtuaAgScsDQogICAgICAgICAgc3BhbjogMTIsDQogICAgICAgICAgaXRlbVJlbmRlcjogew0KICAgICAgICAgICAgbmFtZTogJ1Z4ZVNlbGVjdCcsDQogICAgICAgICAgICBvcHRpb25zOiBbDQogICAgICAgICAgICAgIHsgbGFiZWw6ICfmraPluLgnLCB2YWx1ZTogMSB9LA0KICAgICAgICAgICAgICB7IGxhYmVsOiAn5Yig6ZmkJywgdmFsdWU6IDEwMSB9DQogICAgICAgICAgICBdLA0KICAgICAgICAgICAgcHJvcHM6IHsgcGxhY2Vob2xkZXI6ICfor7fpgInmi6knIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICAvLyDpmYTku7bnrqHnkIYNCiAgICAgIGZpbGVEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGZpbGVMaXN0OiBbXSwNCiAgICAgIHVwbG9hZFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICcvd2ViL3N1cHBseS91c2VyZmlsZS91cGxvYWQnLA0KICAgICAgY3VycmVudFVzZXJJZDogbnVsbCwNCiAgICAgIGN1cnJlbnRVc2VySW5mbzoge30sDQogICAgICAvLyDlr7zlhaXlj4LmlbANCiAgICAgIHVwbG9hZDogew0KICAgICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjlr7zlhaXvvIkNCiAgICAgICAgb3BlbjogZmFsc2UsDQogICAgICAgIC8vIOW8ueWHuuWxguagh+mimO+8iOWvvOWFpe+8iQ0KICAgICAgICB0aXRsZTogIiIsDQogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oA0KICAgICAgICBpc1VwbG9hZGluZzogZmFsc2UsDQogICAgICAgIC8vIOaYr+WQpuabtOaWsOW3sue7j+WtmOWcqOeahOeUqOaIt+aVsOaNrg0KICAgICAgICB1cGRhdGVTdXBwb3J0OiAwLA0KICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gNCiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0sDQogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgA0KICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL3dlYi9zdXBwbHkvc3VwcGxpZXIvaW1wb3J0Ig0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgdXBsb2FkRGF0YSgpIHsNCiAgICAgIHJldHVybiB7DQogICAgICAgIHVzZXJpZDogdGhpcy5jdXJyZW50VXNlcklkDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuaW5pdFN1cHBsaWVyQ29kZSgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOWIneWni+WMluS+m+W6lOWVhuS7o+eggSAqLw0KICAgIGluaXRTdXBwbGllckNvZGUoKSB7DQogICAgICAvLyDojrflj5blvZPliY3nmbvlvZXnlKjmiLfkv6Hmga8NCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCdHZXRJbmZvJykudGhlbihyZXMgPT4gew0KICAgICAgICBjb25zdCB1c2VybmFtZSA9IHJlcy51c2VyLnVzZXJOYW1lOw0KICAgICAgICAvLyDlvZPliY3nlKjmiLfnvJblj7fnmoTliY035L2N5Li65L6b5bqU5ZWG57yW5Y+3DQogICAgICAgIHRoaXMuc3VwcGxpZXJDb2RlID0gdXNlcm5hbWUuc3Vic3RyaW5nKDAsIDcpOw0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN1cHBseUNvZGUgPSB0aGlzLnN1cHBsaWVyQ29kZTsNCg0KICAgICAgICAvLyDlnKjojrflj5bliLDkvpvlupTllYbku6PnoIHlkI7vvIzlho3osIPnlKjlhbbku5bliJ3lp4vljJbmlrnms5UNCiAgICAgICAgdGhpcy5nZXRTdXBwbGllckluZm8oKTsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUqOaIt+S/oeaBr+Wksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6I635Y+W5L6b5bqU5ZWG5L+h5oGvICovDQogICAgZ2V0U3VwcGxpZXJJbmZvKCkgew0KICAgICAgaWYgKHRoaXMuc3VwcGxpZXJDb2RlKSB7DQogICAgICAgIC8vIOiwg+eUqFN1cHBseUluZm9Db250cm9sbGVy5Lit55qE5pa55rOV5p+l6K+i5L6b5bqU5ZWG5L+h5oGvDQogICAgICAgIHJlcXVlc3QuZ2V0KGAvd2ViL3N1cHBseS9pbmZvL2dldEJ5Q29kZS8ke3RoaXMuc3VwcGxpZXJDb2RlfWApLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgICAgdGhpcy5zdXBwbGllckluZm8gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDlpoLmnpzkvpvlupTllYbkv6Hmga/kuI3lrZjlnKjvvIzmmL7npLrpu5jorqTkv6Hmga8NCiAgICAgICAgICAgIHRoaXMuc3VwcGxpZXJJbmZvID0gew0KICAgICAgICAgICAgICBzdXBwbHlDb2RlOiB0aGlzLnN1cHBsaWVyQ29kZSwNCiAgICAgICAgICAgICAgc3VwcGx5TmFtZTogJycsDQogICAgICAgICAgICAgIGNvbnRhY3RQZXJzb246ICcnLA0KICAgICAgICAgICAgICBjb250YWN0UGhvbmU6ICcnLA0KICAgICAgICAgICAgICBhZGRyZXNzOiAnJywNCiAgICAgICAgICAgICAgc3RhdHVzOiAnMScNCiAgICAgICAgICAgIH07DQogICAgICAgICAgfQ0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgLy8g5aaC5p6c5L6b5bqU5ZWG5L+h5oGv5LiN5a2Y5Zyo77yM5pi+56S66buY6K6k5L+h5oGvDQogICAgICAgICAgdGhpcy5zdXBwbGllckluZm8gPSB7DQogICAgICAgICAgICBzdXBwbHlDb2RlOiB0aGlzLnN1cHBsaWVyQ29kZSwNCiAgICAgICAgICAgIHN1cHBseU5hbWU6ICcnLA0KICAgICAgICAgICAgY29udGFjdFBlcnNvbjogJycsDQogICAgICAgICAgICBjb250YWN0UGhvbmU6ICcnLA0KICAgICAgICAgICAgYWRkcmVzczogJycsDQogICAgICAgICAgICBzdGF0dXM6ICcxJw0KICAgICAgICAgIH07DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOafpeivouS6uuWRmOWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICAvLyDosIPnlKjkvpvlupTllYbkuJPnlKjmjqXlj6Pmn6Xor6LkurrlkZjliJfooagNCiAgICAgIHJlcXVlc3QuZ2V0KCcvd2ViL3N1cHBseS9zdXBwbGllci9saXN0Jywgew0KICAgICAgICBwYXJhbXM6IHRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3MgfHwgW107DQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsIHx8IDA7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+afpeivouWksei0pScpOw0KICAgICAgICAgIHRoaXMudXNlckxpc3QgPSBbXTsNCiAgICAgICAgICB0aGlzLnRvdGFsID0gMDsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmn6Xor6LlpLHotKU6ICcgKyBlcnJvci5tZXNzYWdlKTsNCiAgICAgICAgdGhpcy51c2VyTGlzdCA9IFtdOw0KICAgICAgICB0aGlzLnRvdGFsID0gMDsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHN1cHBseUNvZGU6IHRoaXMuc3VwcGxpZXJDb2RlLA0KICAgICAgICB1c2VyTmFtZTogbnVsbCwNCiAgICAgICAgaWRjYXJkOiBudWxsDQogICAgICB9Ow0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Lq65ZGYIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkczsNCiAgICAgIC8vIOiwg+eUqOS+m+W6lOWVhuS4k+eUqOaOpeWPo+iOt+WPluS6uuWRmOS/oeaBrw0KICAgICAgcmVxdWVzdC5nZXQoYC93ZWIvc3VwcGx5L3N1cHBsaWVyL3VzZXIvJHtpZH1gKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueS6uuWRmCI7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+iOt+WPluS6uuWRmOS/oeaBr+Wksei0pScpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluS6uuWRmOS/oeaBr+Wksei0pTogJyArIGVycm9yLm1lc3NhZ2UpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgLy8g6LCD55So5L6b5bqU5ZWG5LiT55So5L+u5pS55o6l5Y+jDQogICAgICAgICAgICByZXF1ZXN0LnB1dCgnL3dlYi9zdXBwbHkvc3VwcGxpZXIvdXNlcicsIHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyB8fCAn5L+u5pS55aSx6LSlJyk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5L+u5pS55aSx6LSlOiAnICsgZXJyb3IubWVzc2FnZSk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g6LCD55So5L6b5bqU5ZWG5LiT55So5paw5aKe5o6l5Y+jDQogICAgICAgICAgICByZXF1ZXN0LnBvc3QoJy93ZWIvc3VwcGx5L3N1cHBsaWVyL3VzZXInLCB0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+aWsOWinuWksei0pScpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aWsOWinuWksei0pTogJyArIGVycm9yLm1lc3NhZ2UpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTkurrlkZjnvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbigoKSA9PiB7DQogICAgICAgIC8vIOiwg+eUqOS+m+W6lOWVhuS4k+eUqOWIoOmZpOaOpeWPow0KICAgICAgICByZXR1cm4gcmVxdWVzdC5kZWxldGUoYC93ZWIvc3VwcGx5L3N1cHBsaWVyL3VzZXIvJHtpZHN9YCk7DQogICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfliKDpmaTlpLHotKUnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBpZiAoZXJyb3IgIT09ICdjYW5jZWwnKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yig6Zmk5aSx6LSlOiAnICsgZXJyb3IubWVzc2FnZSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ3dlYi9zdXBwbHkvc3VwcGxpZXIvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgc3VwcGxpZXJfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICAvKiog5a+85YWl5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlSW1wb3J0KCkgew0KICAgICAgdGhpcy51cGxvYWQudGl0bGUgPSAi5Lq65ZGY5a+85YWlIjsNCiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOS4i+i9veaooeadv+aTjeS9nCAqLw0KICAgIGltcG9ydFRlbXBsYXRlKCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnd2ViL3N1cHBseS9zdXBwbGllci9pbXBvcnRUZW1wbGF0ZScsIHt9LCBgc3VwcGxpZXJfdGVtcGxhdGVfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICAvKiog5paH5Lu25LiK5Lyg5Lit5aSE55CGICovDQogICAgaGFuZGxlRmlsZVVwbG9hZFByb2dyZXNzKGV2ZW50LCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOaWh+S7tuS4iuS8oOaIkOWKn+WkhOeQhiAqLw0KICAgIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy51cGxvYWQub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsNCiAgICAgIHRoaXMuJGFsZXJ0KCI8ZGl2IHN0eWxlPSdvdmVyZmxvdzogYXV0bztvdmVyZmxvdy14OiBoaWRkZW47bWF4LWhlaWdodDogNzB2aDtwYWRkaW5nOiAxMHB4IDIwcHggMDsnPiIgKyByZXNwb25zZS5tc2cgKyAiPC9kaXY+IiwgIuWvvOWFpee7k+aenCIsIHsgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiB0cnVlIH0pOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog5Y+W5raI5oyJ6ZKuICovDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvKiog6KGo5Y2V6YeN572uICovDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBzdXBwbHlDb2RlOiB0aGlzLnN1cHBsaWVyQ29kZSwNCiAgICAgICAgdXNlck5hbWU6IG51bGwsDQogICAgICAgIGlkY2FyZDogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCg0KICAgIC8qKiDmiZPlvIDlspfkvY3or4bliKvljaHlr7nor53moYYgKi8NCiAgICBvcGVuRmFjRGlhbG9nKHJvdykgew0KICAgICAgLy8g6LCD55So5L6b5bqU5ZWG5LiT55So5o6l5Y+j6I635Y+W5bKX5L2N6K+G5Yir5Y2h5L+h5oGvDQogICAgICByZXF1ZXN0LmdldChgL3dlYi9zdXBwbHkvc3VwcGxpZXIvZmFjLyR7cm93LmlkfWApLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5mYWNGb3JtID0gcmVzcG9uc2UuZGF0YSB8fCB7IHVzZXJJZDogcm93LmlkIH07DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5mYWNGb3JtID0geyB1c2VySWQ6IHJvdy5pZCB9Ow0KICAgICAgICB9DQogICAgICAgIHRoaXMuZmFjRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIHRoaXMuZmFjRm9ybSA9IHsgdXNlcklkOiByb3cuaWQgfTsNCiAgICAgICAgdGhpcy5mYWNEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOWyl+S9jeivhuWIq+WNoSAqLw0KICAgIHN1Ym1pdEZhYygpIHsNCiAgICAgIGNvbnN0IHVybCA9IHRoaXMuZmFjRm9ybS5pZCA/ICcvd2ViL3N1cHBseS9zdXBwbGllci9mYWMnIDogJy93ZWIvc3VwcGx5L3N1cHBsaWVyL2ZhYyc7DQogICAgICBjb25zdCBtZXRob2QgPSB0aGlzLmZhY0Zvcm0uaWQgPyAncHV0JyA6ICdwb3N0JzsNCg0KICAgICAgcmVxdWVzdFttZXRob2RdKHVybCwgdGhpcy5mYWNGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5L+d5a2Y5oiQ5YqfJyk7DQogICAgICAgICAgdGhpcy5mYWNEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgJ+S/neWtmOWksei0pScpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S/neWtmOWksei0pTogJyArIGVycm9yLm1lc3NhZ2UpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5omT5byA5YGl5bq35L+h5oGv5a+56K+d5qGGICovDQogICAgb3BlbkhlYWx0aERpYWxvZyhyb3cpIHsNCiAgICAgIC8vIOiwg+eUqOS+m+W6lOWVhuS4k+eUqOaOpeWPo+iOt+WPluWBpeW6t+S/oeaBrw0KICAgICAgcmVxdWVzdC5nZXQoYC93ZWIvc3VwcGx5L3N1cHBsaWVyL2hlYWx0aC8ke3Jvdy5pZH1gKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuaGVhbHRoRm9ybSA9IHJlc3BvbnNlLmRhdGEgfHwgeyB1c2VyaWQ6IHJvdy5pZCB9Ow0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuaGVhbHRoRm9ybSA9IHsgdXNlcmlkOiByb3cuaWQgfTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmhlYWx0aERpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmhlYWx0aEZvcm0gPSB7IHVzZXJpZDogcm93LmlkIH07DQogICAgICAgIHRoaXMuaGVhbHRoRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTlgaXlurfkv6Hmga8gKi8NCiAgICBzdWJtaXRIZWFsdGgoKSB7DQogICAgICBjb25zdCB1cmwgPSB0aGlzLmhlYWx0aEZvcm0uaWQgPyAnL3dlYi9zdXBwbHkvc3VwcGxpZXIvaGVhbHRoJyA6ICcvd2ViL3N1cHBseS9zdXBwbGllci9oZWFsdGgnOw0KICAgICAgY29uc3QgbWV0aG9kID0gdGhpcy5oZWFsdGhGb3JtLmlkID8gJ3B1dCcgOiAncG9zdCc7DQoNCiAgICAgIHJlcXVlc3RbbWV0aG9kXSh1cmwsIHRoaXMuaGVhbHRoRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKnycpOw0KICAgICAgICAgIHRoaXMuaGVhbHRoRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfkv53lrZjlpLHotKUnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkv53lrZjlpLHotKU6ICcgKyBlcnJvci5tZXNzYWdlKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaJk+W8gOaWh+S7tueuoeeQhuWvueivneahhiAqLw0KICAgIG9wZW5GaWxlRGlhbG9nKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50VXNlcklkID0gcm93LmlkOw0KICAgICAgdGhpcy5jdXJyZW50VXNlckluZm8gPSByb3c7DQogICAgICB0aGlzLmdldEZpbGVMaXN0KHJvdy5pZCk7DQogICAgICB0aGlzLmZpbGVEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDojrflj5bmlofku7bliJfooaggKi8NCiAgICBnZXRGaWxlTGlzdCh1c2VyaWQpIHsNCiAgICAgIC8vIOiwg+eUqOS+m+W6lOWVhuS4k+eUqOaOpeWPo+iOt+WPluaWh+S7tuWIl+ihqA0KICAgICAgcmVxdWVzdC5nZXQoYC93ZWIvc3VwcGx5L3N1cHBsaWVyL2ZpbGUvbGlzdC8ke3VzZXJpZH1gKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZmlsZUxpc3QgPSByZXNwb25zZS5yb3dzIHx8IFtdOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuZmlsZUxpc3QgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmZpbGVMaXN0ID0gW107DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmlofku7bkuIrkvKDmiJDlip/lpITnkIYgKi8NCiAgICBoYW5kbGVGaWxlVXBsb2FkU3VjY2VzcyhyZXNwb25zZSkgew0KICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aWh+S7tuS4iuS8oOaIkOWKnycpOw0KICAgICAgICB0aGlzLmdldEZpbGVMaXN0KHRoaXMuY3VycmVudFVzZXJJZCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyB8fCAn5paH5Lu25LiK5Lyg5aSx6LSlJyk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5paH5Lu25LiK5Lyg6ZSZ6K+v5aSE55CGICovDQogICAgaGFuZGxlRmlsZVVwbG9hZEVycm9yKGVycikgew0KICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5paH5Lu25LiK5Lyg5aSx6LSlOiAnICsgKGVyci5tZXNzYWdlIHx8ICfmnKrnn6XplJnor68nKSk7DQogICAgfSwNCiAgICAvKiog5paH5Lu25LiK5Lyg5YmN5qOA5p+lICovDQogICAgYmVmb3JlRmlsZVVwbG9hZChmaWxlKSB7DQogICAgICBjb25zdCBpc1BERiA9IGZpbGUudHlwZSA9PT0gJ2FwcGxpY2F0aW9uL3BkZicgfHwgZmlsZS5uYW1lLnRvTG93ZXJDYXNlKCkuZW5kc1dpdGgoJy5wZGYnKTsNCiAgICAgIGlmICghaXNQREYpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Y+q6IO95LiK5LygUERG5qC85byP5paH5Lu277yBJyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgaXNMdDUwTSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgNTA7DQogICAgICBpZiAoIWlzTHQ1ME0pIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5LiK5Lyg5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDUwTUIhJyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHRydWU7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5paH5Lu2ICovDQogICAgZGVsZXRlRmlsZShyb3cpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuWIoOmZpOivpemZhOS7tuWQl++8nycsICfmj5DnpLonLCB7IHR5cGU6ICd3YXJuaW5nJyB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgLy8g6LCD55So5L6b5bqU5ZWG5LiT55So5Yig6Zmk5paH5Lu25o6l5Y+jDQogICAgICAgIHJlcXVlc3QuZGVsZXRlKGAvd2ViL3N1cHBseS9zdXBwbGllci9maWxlLyR7cm93LmlkfWApLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJyk7DQogICAgICAgICAgICB0aGlzLmdldEZpbGVMaXN0KHRoaXMuY3VycmVudFVzZXJJZCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfliKDpmaTlpLHotKUnKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliKDpmaTlpLHotKU6ICcgKyBlcnJvci5tZXNzYWdlKTsNCiAgICAgICAgfSk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5LiL6L295Y2V5Liq5paH5Lu2ICovDQogICAgZG93bmxvYWRGaWxlSXRlbShyb3cpIHsNCiAgICAgIC8vIOiwg+eUqOS4i+i9veaOpeWPo+iOt+WPluaWh+S7tlVSTA0KICAgICAgcmVxdWVzdC5nZXQoYC93ZWIvc3VwcGx5L3VzZXJmaWxlL2Rvd25sb2FkLyR7cm93LmlkfWApLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgY29uc3QgZmlsZVVybCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgd2luZG93Lm9wZW4oZmlsZVVybCwgJ19ibGFuaycpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzcG9uc2UubXNnIHx8ICfkuIvovb3lpLHotKUnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIvovb3lpLHotKU6ICcgKyBlcnJvci5tZXNzYWdlKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOS4i+i9veaWh+S7tiAqLw0KICAgIGRvd25sb2FkRmlsZShyb3cpIHsNCiAgICAgIC8vIOiwg+eUqOS4i+i9veaOpeWPo+iOt+WPluaWh+S7tlVSTA0KICAgICAgcmVxdWVzdC5nZXQoYC93ZWIvc3VwcGx5L3VzZXJmaWxlL2Rvd25sb2FkLyR7cm93LmlkfWApLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgLy8g6I635Y+W5Yiw5paH5Lu2VVJM5ZCO77yM5Zyo5paw56qX5Y+j5Lit5omT5byA5LiL6L29DQogICAgICAgICAgY29uc3QgZmlsZVVybCA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICB3aW5kb3cub3BlbihmaWxlVXJsLCAnX2JsYW5rJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyB8fCAn5LiL6L295aSx6LSlJykNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkuIvovb3lpLHotKU6ICcgKyBlcnJvci5tZXNzYWdlKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTkuIrkvKDmlofku7YgKi8NCiAgICBzdWJtaXRGaWxlRm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAq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file": "index.vue", "sourceRoot": "src/views/supply/supplier", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 供应商信息展示 -->\r\n    <div class=\"supplier-info-bar\">\r\n      <div class=\"supplier-info-content\">\r\n        <div class=\"supplier-basic\">\r\n          <span class=\"supplier-code\">{{ supplierCode }}</span>\r\n          <span class=\"supplier-name\">{{ supplierInfo.supplyName || '未设置供应商名称' }}</span>\r\n          <el-tag v-if=\"supplierInfo.status\" :type=\"supplierInfo.status === '1' ? 'success' : 'danger'\" size=\"mini\">\r\n            {{ supplierInfo.status === '1' ? '正常' : '停用' }}\r\n          </el-tag>\r\n        </div>\r\n        <div class=\"supplier-contact\" v-if=\"supplierInfo.contactPerson || supplierInfo.contactPhone\">\r\n          <span v-if=\"supplierInfo.contactPerson\">联系人：{{ supplierInfo.contactPerson }}</span>\r\n          <span v-if=\"supplierInfo.contactPhone\">电话：{{ supplierInfo.contactPhone }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 查询表单 -->\r\n    <el-form :inline=\"true\" :model=\"queryParams\" class=\"demo-form-inline\" @submit.native.prevent>\r\n      <el-form-item label=\"用户姓名\">\r\n        <el-input v-model=\"queryParams.userName\" placeholder=\"请输入用户姓名\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"身份证\">\r\n        <el-input v-model=\"queryParams.idcard\" placeholder=\"请输入身份证\" clearable />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">查询</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 工具栏 -->\r\n    <div style=\"margin-bottom: 10px;\">\r\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增人员</el-button>\r\n      <el-button type=\"success\" icon=\"el-icon-upload\" @click=\"handleImport\">导入</el-button>\r\n      <el-button type=\"warning\" icon=\"el-icon-download\" @click=\"handleExport\">导出</el-button>\r\n    </div>\r\n\r\n    <!-- 人员列表 -->\r\n    <el-table :data=\"userList\" border stripe style=\"width: 100%\">\r\n      <el-table-column prop=\"userName\" label=\"用户姓名\" min-width=\"100\" />\r\n      <el-table-column prop=\"idcard\" label=\"身份证\" min-width=\"160\" />\r\n      <el-table-column label=\"岗位识别卡\" min-width=\"110\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFacDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"健康信息\" min-width=\"100\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openHealthDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"附件\" min-width=\"80\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFileDialog(scope.row)\">管理</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" min-width=\"160\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"operation-buttons\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadFile(scope.row)\"\r\n              title=\"下载\">\r\n              下载\r\n            </el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              title=\"编辑\">\r\n              编辑\r\n            </el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              title=\"删除\">\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 新增/编辑人员对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"用户姓名\" prop=\"userName\">\r\n          <el-input v-model=\"form.userName\" placeholder=\"请输入用户姓名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证\" prop=\"idcard\">\r\n          <el-input v-model=\"form.idcard\" placeholder=\"请输入身份证\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    <!-- 岗位识别卡弹窗 -->\r\n    <vxe-modal v-model=\"facDialogVisible\" title=\"岗位识别卡\" width=\"700\" show-footer>\r\n      <vxe-form\r\n        :data=\"facForm\"\r\n        :items=\"facFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"facDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitFac\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 健康信息弹窗 -->\r\n    <vxe-modal v-model=\"healthDialogVisible\" title=\"健康信息\" width=\"800\" show-footer>\r\n      <vxe-form\r\n        :data=\"healthForm\"\r\n        :items=\"healthFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"healthDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitHealth\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 附件管理弹窗 -->\r\n    <el-dialog\r\n      :visible.sync=\"fileDialogVisible\"\r\n      title=\"附件管理\"\r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n    >\r\n      <!-- 上传区域 -->\r\n      <div class=\"upload-section\">\r\n        <el-upload\r\n          ref=\"fileUpload\"\r\n          :action=\"uploadUrl\"\r\n          :headers=\"upload.headers\"\r\n          :data=\"uploadData\"\r\n          :on-success=\"handleFileUploadSuccess\"\r\n          :on-error=\"handleFileUploadError\"\r\n          :before-upload=\"beforeFileUpload\"\r\n          :show-file-list=\"false\"\r\n          accept=\".pdf\"\r\n          drag\r\n          class=\"upload-dragger\"\r\n        >\r\n          <i class=\"el-icon-upload\"></i>\r\n          <div class=\"el-upload__text\">将PDF文件拖到此处，或<em>点击上传</em></div>\r\n          <div class=\"el-upload__tip\">仅支持PDF格式，单个文件不超过50MB</div>\r\n        </el-upload>\r\n      </div>\r\n\r\n      <!-- 文件列表 -->\r\n      <div class=\"file-list-section\">\r\n        <div class=\"file-list-header\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span class=\"file-list-title\">已上传文件</span>\r\n          <span class=\"file-count\">(共 {{fileList.length}} 个文件)</span>\r\n        </div>\r\n        <div class=\"file-list-content\">\r\n          <el-table\r\n            :data=\"fileList\"\r\n            style=\"width: 100%\"\r\n            :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n          >\r\n            <el-table-column prop=\"filename\" label=\"文件名\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"file-info\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span class=\"file-name\">{{scope.row.filename}}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"format\" label=\"格式\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{scope.row.format}}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"state\" label=\"状态\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag\r\n                  size=\"mini\"\r\n                  :type=\"scope.row.state === 1 ? 'success' : 'danger'\"\r\n                >\r\n                  {{scope.row.state === 1 ? '正常' : '异常'}}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"operation-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-download\"\r\n                    @click=\"downloadFileItem(scope.row)\"\r\n                  >\r\n                    下载\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"danger\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"deleteFile(scope.row)\"\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 人员导入对话框 -->\r\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :limit=\"1\"\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :action=\"upload.url\"\r\n        :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\"\r\n        :auto-upload=\"false\"\r\n        drag\r\n      >\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\r\n          <div class=\"el-upload__tip\" slot=\"tip\">\r\n            <el-checkbox v-model=\"upload.updateSupport\" /> 是否更新已经存在的用户数据\r\n          </div>\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n          <el-link type=\"primary\" :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" @click=\"importTemplate\">下载模板</el-link>\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" :loading=\"upload.isUploading\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  name: 'SupplierInfo',\r\n  data() {\r\n    return {\r\n      // 供应商信息\r\n      supplierInfo: {},\r\n      supplierCode: '',\r\n      // 人员列表\r\n      userList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        supplyCode: null,\r\n        userName: null,\r\n        idcard: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userName: [\r\n          { required: true, message: \"用户姓名不能为空\", trigger: \"blur\" }\r\n        ],\r\n        idcard: [\r\n          { required: true, message: \"身份证不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 总条数\r\n      total: 0,\r\n      // 岗位识别卡\r\n      facDialogVisible: false,\r\n      facForm: {},\r\n      facFormItems: [\r\n        { field: 'userPost', title: '岗位名称', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入岗位名称', rows: 2 } } },\r\n        { field: 'userFacClass', title: '岗位班组', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位班组' } } },\r\n        { field: 'userDeptName', title: '所属部门', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入所属部门' } } },\r\n        { field: 'userFacWork', title: '岗位描述', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位描述' } } },\r\n        { field: 'userTimeBegin', title: '入厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'userTimeEnd', title: '离厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 24,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '起草', value: 0 },\r\n              { label: '分厂审核人', value: 1 },\r\n              { label: '人力资源部', value: 2 },\r\n              { label: '退回', value: -1 },\r\n              { label: '禁用', value: 101 },\r\n              { label: '审核通过', value: 99 },\r\n              { label: '删除', value: 102 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 健康信息\r\n      healthDialogVisible: false,\r\n      healthForm: {},\r\n      healthFormItems: [\r\n        { field: 'healdate', title: '体检日期', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'hos', title: '医院', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入医院' } } },\r\n        { field: 'healtz', title: '体重', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重' } } },\r\n        { field: 'healtzzs', title: '体重指数', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重指数' } } },\r\n        { field: 'healptt', title: '血糖', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入血糖' } } },\r\n        { field: 'healssy', title: '收缩压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入收缩压' } } },\r\n        { field: 'healszy', title: '舒张压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入舒张压' } } },\r\n        { field: 'healzdgc', title: '总胆固醇', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入总胆固醇' } } },\r\n        { field: 'healgysz', title: '甘油三酯', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入甘油三酯' } } },\r\n        { field: 'healga', title: '谷氨酰转肽酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷氨酰转肽酶' } } },\r\n        { field: 'healgb', title: '谷丙转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷丙转氨酶' } } },\r\n        { field: 'healgc', title: '谷草转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷草转氨酶' } } },\r\n        { field: 'healnsd', title: '尿素氮', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入尿素氮' } } },\r\n        { field: 'healjg', title: '肌酐', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入肌酐' } } },\r\n        { field: 'healxd', title: '心电图', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入心电图' } } },\r\n        { field: 'healxj', title: '小结', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入小结', rows: 2 } } },\r\n        { field: 'healjy', title: '建议', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入建议', rows: 2 } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 12,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '正常', value: 1 },\r\n              { label: '删除', value: 101 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 附件管理\r\n      fileDialogVisible: false,\r\n      fileList: [],\r\n      uploadUrl: process.env.VUE_APP_BASE_API + '/web/supply/userfile/upload',\r\n      currentUserId: null,\r\n      currentUserInfo: {},\r\n      // 导入参数\r\n      upload: {\r\n        // 是否显示弹出层（导入）\r\n        open: false,\r\n        // 弹出层标题（导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 是否更新已经存在的用户数据\r\n        updateSupport: 0,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/web/supply/supplier/import\"\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    uploadData() {\r\n      return {\r\n        userid: this.currentUserId\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.initSupplierCode();\r\n  },\r\n  methods: {\r\n    /** 初始化供应商代码 */\r\n    initSupplierCode() {\r\n      // 获取当前登录用户信息\r\n      this.$store.dispatch('GetInfo').then(res => {\r\n        const username = res.user.userName;\r\n        // 当前用户编号的前7位为供应商编号\r\n        this.supplierCode = username.substring(0, 7);\r\n        this.queryParams.supplyCode = this.supplierCode;\r\n\r\n        // 在获取到供应商代码后，再调用其他初始化方法\r\n        this.getSupplierInfo();\r\n        this.getList();\r\n      }).catch(() => {\r\n        this.$message.error('获取用户信息失败');\r\n      });\r\n    },\r\n    /** 获取供应商信息 */\r\n    getSupplierInfo() {\r\n      if (this.supplierCode) {\r\n        // 调用SupplyInfoController中的方法查询供应商信息\r\n        request.get(`/web/supply/info/getByCode/${this.supplierCode}`).then(response => {\r\n          if (response.code === 200 && response.data) {\r\n            this.supplierInfo = response.data;\r\n          } else {\r\n            // 如果供应商信息不存在，显示默认信息\r\n            this.supplierInfo = {\r\n              supplyCode: this.supplierCode,\r\n              supplyName: '',\r\n              contactPerson: '',\r\n              contactPhone: '',\r\n              address: '',\r\n              status: '1'\r\n            };\r\n          }\r\n        }).catch(() => {\r\n          // 如果供应商信息不存在，显示默认信息\r\n          this.supplierInfo = {\r\n            supplyCode: this.supplierCode,\r\n            supplyName: '',\r\n            contactPerson: '',\r\n            contactPhone: '',\r\n            address: '',\r\n            status: '1'\r\n          };\r\n        });\r\n      }\r\n    },\r\n    /** 查询人员列表 */\r\n    getList() {\r\n      // 调用供应商专用接口查询人员列表\r\n      request.get('/web/supply/supplier/list', {\r\n        params: this.queryParams\r\n      }).then(response => {\r\n        if (response.code === 200) {\r\n          this.userList = response.rows || [];\r\n          this.total = response.total || 0;\r\n        } else {\r\n          this.$message.error(response.msg || '查询失败');\r\n          this.userList = [];\r\n          this.total = 0;\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('查询失败: ' + error.message);\r\n        this.userList = [];\r\n        this.total = 0;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        supplyCode: this.supplierCode,\r\n        userName: null,\r\n        idcard: null\r\n      };\r\n      this.getList();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加人员\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      // 调用供应商专用接口获取人员信息\r\n      request.get(`/web/supply/supplier/user/${id}`).then(response => {\r\n        if (response.code === 200) {\r\n          this.form = response.data;\r\n          this.open = true;\r\n          this.title = \"修改人员\";\r\n        } else {\r\n          this.$message.error(response.msg || '获取人员信息失败');\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取人员信息失败: ' + error.message);\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            // 调用供应商专用修改接口\r\n            request.put('/web/supply/supplier/user', this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.$modal.msgSuccess(\"修改成功\");\r\n                this.open = false;\r\n                this.getList();\r\n              } else {\r\n                this.$message.error(response.msg || '修改失败');\r\n              }\r\n            }).catch(error => {\r\n              this.$message.error('修改失败: ' + error.message);\r\n            });\r\n          } else {\r\n            // 调用供应商专用新增接口\r\n            request.post('/web/supply/supplier/user', this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.$modal.msgSuccess(\"新增成功\");\r\n                this.open = false;\r\n                this.getList();\r\n              } else {\r\n                this.$message.error(response.msg || '新增失败');\r\n              }\r\n            }).catch(error => {\r\n              this.$message.error('新增失败: ' + error.message);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('是否确认删除人员编号为\"' + ids + '\"的数据项？').then(() => {\r\n        // 调用供应商专用删除接口\r\n        return request.delete(`/web/supply/supplier/user/${ids}`);\r\n      }).then(response => {\r\n        if (response.code === 200) {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        } else {\r\n          this.$message.error(response.msg || '删除失败');\r\n        }\r\n      }).catch(error => {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除失败: ' + error.message);\r\n        }\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('web/supply/supplier/export', {\r\n        ...this.queryParams\r\n      }, `supplier_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 导入按钮操作 */\r\n    handleImport() {\r\n      this.upload.title = \"人员导入\";\r\n      this.upload.open = true;\r\n    },\r\n    /** 下载模板操作 */\r\n    importTemplate() {\r\n      this.download('web/supply/supplier/importTemplate', {}, `supplier_template_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 文件上传中处理 */\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n    /** 文件上传成功处理 */\r\n    handleFileSuccess(response, file, fileList) {\r\n      this.upload.open = false;\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.$alert(\"<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>\" + response.msg + \"</div>\", \"导入结果\", { dangerouslyUseHTMLString: true });\r\n      this.getList();\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        supplyCode: this.supplierCode,\r\n        userName: null,\r\n        idcard: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n\r\n    /** 打开岗位识别卡对话框 */\r\n    openFacDialog(row) {\r\n      // 调用供应商专用接口获取岗位识别卡信息\r\n      request.get(`/web/supply/supplier/fac/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          this.facForm = response.data || { userId: row.id };\r\n        } else {\r\n          this.facForm = { userId: row.id };\r\n        }\r\n        this.facDialogVisible = true;\r\n      }).catch(() => {\r\n        this.facForm = { userId: row.id };\r\n        this.facDialogVisible = true;\r\n      });\r\n    },\r\n    /** 提交岗位识别卡 */\r\n    submitFac() {\r\n      const url = this.facForm.id ? '/web/supply/supplier/fac' : '/web/supply/supplier/fac';\r\n      const method = this.facForm.id ? 'put' : 'post';\r\n\r\n      request[method](url, this.facForm).then(response => {\r\n        if (response.code === 200) {\r\n          this.$message.success('保存成功');\r\n          this.facDialogVisible = false;\r\n          this.getList();\r\n        } else {\r\n          this.$message.error(response.msg || '保存失败');\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('保存失败: ' + error.message);\r\n      });\r\n    },\r\n    /** 打开健康信息对话框 */\r\n    openHealthDialog(row) {\r\n      // 调用供应商专用接口获取健康信息\r\n      request.get(`/web/supply/supplier/health/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          this.healthForm = response.data || { userid: row.id };\r\n        } else {\r\n          this.healthForm = { userid: row.id };\r\n        }\r\n        this.healthDialogVisible = true;\r\n      }).catch(() => {\r\n        this.healthForm = { userid: row.id };\r\n        this.healthDialogVisible = true;\r\n      });\r\n    },\r\n    /** 提交健康信息 */\r\n    submitHealth() {\r\n      const url = this.healthForm.id ? '/web/supply/supplier/health' : '/web/supply/supplier/health';\r\n      const method = this.healthForm.id ? 'put' : 'post';\r\n\r\n      request[method](url, this.healthForm).then(response => {\r\n        if (response.code === 200) {\r\n          this.$message.success('保存成功');\r\n          this.healthDialogVisible = false;\r\n          this.getList();\r\n        } else {\r\n          this.$message.error(response.msg || '保存失败');\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('保存失败: ' + error.message);\r\n      });\r\n    },\r\n    /** 打开文件管理对话框 */\r\n    openFileDialog(row) {\r\n      this.currentUserId = row.id;\r\n      this.currentUserInfo = row;\r\n      this.getFileList(row.id);\r\n      this.fileDialogVisible = true;\r\n    },\r\n    /** 获取文件列表 */\r\n    getFileList(userid) {\r\n      // 调用供应商专用接口获取文件列表\r\n      request.get(`/web/supply/supplier/file/list/${userid}`).then(response => {\r\n        if (response.code === 200) {\r\n          this.fileList = response.rows || [];\r\n        } else {\r\n          this.fileList = [];\r\n        }\r\n      }).catch(() => {\r\n        this.fileList = [];\r\n      });\r\n    },\r\n    /** 文件上传成功处理 */\r\n    handleFileUploadSuccess(response) {\r\n      if (response.code === 200) {\r\n        this.$message.success('文件上传成功');\r\n        this.getFileList(this.currentUserId);\r\n      } else {\r\n        this.$message.error(response.msg || '文件上传失败');\r\n      }\r\n    },\r\n    /** 文件上传错误处理 */\r\n    handleFileUploadError(err) {\r\n      this.$message.error('文件上传失败: ' + (err.message || '未知错误'));\r\n    },\r\n    /** 文件上传前检查 */\r\n    beforeFileUpload(file) {\r\n      const isPDF = file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');\r\n      if (!isPDF) {\r\n        this.$message.error('只能上传PDF格式文件！');\r\n        return false;\r\n      }\r\n\r\n      const isLt50M = file.size / 1024 / 1024 < 50;\r\n      if (!isLt50M) {\r\n        this.$message.error('上传文件大小不能超过 50MB!');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n    /** 删除文件 */\r\n    deleteFile(row) {\r\n      this.$confirm('确定删除该附件吗？', '提示', { type: 'warning' }).then(() => {\r\n        // 调用供应商专用删除文件接口\r\n        request.delete(`/web/supply/supplier/file/${row.id}`).then(response => {\r\n          if (response.code === 200) {\r\n            this.$message.success('删除成功');\r\n            this.getFileList(this.currentUserId);\r\n          } else {\r\n            this.$message.error(response.msg || '删除失败');\r\n          }\r\n        }).catch(error => {\r\n          this.$message.error('删除失败: ' + error.message);\r\n        });\r\n      }).catch(() => {});\r\n    },\r\n    /** 下载单个文件 */\r\n    downloadFileItem(row) {\r\n      // 调用下载接口获取文件URL\r\n      request.get(`/web/supply/userfile/download/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          const fileUrl = response.data;\r\n          window.open(fileUrl, '_blank');\r\n        } else {\r\n          this.$message.error(response.msg || '下载失败');\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('下载失败: ' + error.message);\r\n      });\r\n    },\r\n    /** 下载文件 */\r\n    downloadFile(row) {\r\n      // 调用下载接口获取文件URL\r\n      request.get(`/web/supply/userfile/download/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          // 获取到文件URL后，在新窗口中打开下载\r\n          const fileUrl = response.data\r\n          window.open(fileUrl, '_blank')\r\n        } else {\r\n          this.$message.error(response.msg || '下载失败')\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('下载失败: ' + error.message)\r\n      })\r\n    },\r\n    /** 提交上传文件 */\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.supplier-info-bar {\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  padding: 16px 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.supplier-info-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.supplier-basic {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.supplier-code {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #409EFF;\r\n  background: rgba(64, 158, 255, 0.1);\r\n  padding: 4px 12px;\r\n  border-radius: 4px;\r\n  border: 1px solid rgba(64, 158, 255, 0.2);\r\n}\r\n\r\n.supplier-name {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.supplier-contact {\r\n  display: flex;\r\n  gap: 20px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.supplier-contact span {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.operation-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.operation-buttons .el-button {\r\n  margin: 0;\r\n}\r\n\r\n/* 附件管理样式 */\r\n.upload-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.upload-dragger {\r\n  width: 100%;\r\n}\r\n\r\n.upload-dragger .el-upload-dragger {\r\n  width: 100%;\r\n  height: 120px;\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  background-color: #fafafa;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-dragger .el-upload-dragger:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.upload-dragger .el-icon-upload {\r\n  font-size: 28px;\r\n  color: #c0c4cc;\r\n  margin: 20px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-dragger .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-dragger .el-upload__text em {\r\n  color: #409EFF;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-dragger .el-upload__tip {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  line-height: 1.5;\r\n  margin-top: 8px;\r\n}\r\n\r\n.file-list-section {\r\n  background-color: #f5f7fa;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.file-list-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  color: #606266;\r\n}\r\n\r\n.file-list-title {\r\n  margin-left: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-count {\r\n  margin-left: 10px;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.file-list-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  padding: 10px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-name {\r\n  margin-left: 8px;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  max-width: 150px;\r\n}\r\n</style>\r\n"]}]}