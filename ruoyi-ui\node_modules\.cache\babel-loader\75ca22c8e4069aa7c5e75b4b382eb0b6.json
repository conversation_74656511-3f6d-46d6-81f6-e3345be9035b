{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerForm.vue", "mtime": 1756456493810}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_form", "require", "_input", "_interopRequireDefault", "_answer", "_dimensionality", "_auth", "_axios", "xlsx", "_interopRequireWildcard", "_default", "exports", "default", "name", "components", "answerInput", "data", "pickerOptions", "disabledDate", "time", "getTime", "Date", "now", "frequencyOptions", "queryParams", "_defineProperty2", "formQuestion", "undefined", "fcDate", "dimensionalityId", "formType", "dimensionalityNames", "drawerShow", "stickyTop", "loading", "showSearch", "answerList", "formData", "row", "rootList", "userInfo", "datesave", "pathsave", "count", "deptName", "dimensionalityName", "dateValue", "specialFcDate", "queryImport", "startDate", "endDate", "rootId", "importOpen", "SpecialImportOpen", "upload", "isUploading", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "uploadSpecial", "excelHtml", "searchopen", "excelData", "exceltitle", "customBlobContent", "mounted", "JSON", "parse", "stringify", "$store", "state", "user", "created", "$route", "query", "initData", "methods", "onDateChange", "console", "log", "clickNode", "$event", "node", "target", "parentElement", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "click", "changeEvent", "params", "$form", "$refs", "formRef", "updateStatus", "inputChange", "val", "formValue", "handleScroll", "isSticky", "window", "scrollY", "_this", "getAllRootListForAnswer", "then", "res", "value", "deptCode", "label", "i", "length", "getList", "initData1", "_this2", "containsSubstring", "substring", "string", "includes", "aloneList", "_this3", "formFrequency", "answerListPlusAll", "list", "num", "replace", "formId", "dimensionalityPath", "map", "x", "key", "originalName", "sortKey", "sort", "a", "b", "localeCompare", "item", "uniqueDimensionalityNames", "_toConsumableArray2", "Set", "for<PERSON>ach", "title", "group", "filter", "push", "$forceUpdate", "handleQuery", "handlePreview", "_this4", "type", "downloadXlsx", "_objectSpread2", "blob", "reader", "FileReader", "readAsA<PERSON>y<PERSON><PERSON>er", "onload", "evt", "result", "ints", "Uint8Array", "slice", "size", "workBook", "read", "sheetNames", "SheetNames", "sheetName", "workSheet", "Sheets", "excelTable", "utils", "sheet_to_json", "tableThead", "Array", "from", "Object", "keys", "handleDateChange", "reset<PERSON><PERSON>y", "handleUpload", "_ref", "file", "FormData", "append", "axios", "post", "handleSubmit", "_this5", "processedLists", "reduce", "acc", "current", "concat", "status", "parseFloat", "allLists", "datestr", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "format1", "format2", "$confirm", "confirmButtonText", "cancelButtonText", "newAdd", "msgSuccess", "catch", "handleFileUploadProgress", "handleFileSuccess", "response", "code", "$modal", "msgError", "downloadTemplate", "$notify", "error", "message", "downloadFile", "downloadTemplateSpecial"], "sources": ["src/views/dataReport/answer/answerForm.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n    >\r\n      <el-row :gutter=\"20\" style=\"margin: 20px\">\r\n        <el-form-item label=\"报表名称\" prop=\"dimensionalityId\">\r\n          <el-select\r\n            v-model=\"queryParams.dimensionalityId\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n            @change=\"handleQuery\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in rootList\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '1'\"  >\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '0'\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-01\"\r\n            type=\"month\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n\r\n        <el-form-item label=\"填报问题\" prop=\"formQuestion\">\r\n          <el-input\r\n            v-model=\"queryParams.formQuestion\"\r\n            placeholder=\"请输入填报问题\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"cyan\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuery\"\r\n            >搜索</el-button\r\n          >\r\n        </el-form-item>\r\n        </el-row>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-form-item>\r\n          <el-button  v-if=\" !containsSubstring('安全责任工资考核表',dimensionalityName) \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"importOpen = true\"\r\n            >数据导入导出</el-button\r\n          >\r\n          \r\n          <el-button v-if=\" aloneList(dimensionalityName) || containsSubstring('安全责任工资考核表',dimensionalityName)\"\r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"SpecialImportOpen = true\"\r\n            >单周期报表导入导出</el-button>\r\n\r\n          <el-button v-if=\"aloneList(dimensionalityName)  || containsSubstring('安全责任工资考核表',dimensionalityName) \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handlePreview\"\r\n            >数据预览</el-button\r\n          >\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">报表名称：{{dimensionalityName}}</el-col>\r\n        \r\n        <right-toolbar\r\n          :showSearch.sync=\"showSearch\"\r\n          @queryTable=\"getList\"\r\n        ></right-toolbar>\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">管理部门：{{deptName}}</el-col>\r\n        <right-toolbar\r\n          :showSearch.sync=\"showSearch\"\r\n          @queryTable=\"getList\"\r\n        ></right-toolbar>\r\n      </el-row>\r\n    </el-form>\r\n    <vxe-form\r\n      ref=\"formRef\"\r\n      :data=\"formData\"\r\n      @submit=\"handleSubmit\"\r\n      border\r\n      title-background\r\n      vertical-align=\"center\"\r\n      title-width=\"300\"\r\n      title-bold\r\n    >\r\n      <vxe-form-group\r\n        v-for=\"(group, index) in answerList\"\r\n        :key=\"index\"\r\n        span=\"24\"\r\n        :title=\"group.title\"\r\n        title-bold\r\n        vertical\r\n      >\r\n        <vxe-form-item\r\n          v-for=\"(question, qIndex) in group.list\"\r\n          :key=\"qIndex\"\r\n          :title=\"question.formQuestion\"\r\n          :field=\"answerList[index].list[qIndex].formValue\"\r\n          :span=\"question.formType == '3' ? 24 : 12\"\r\n          :item-render=\"{}\"\r\n        >\r\n          <template #default=\"params\">\r\n            <vxe-tag\r\n              v-if=\"question.status == '0'\"\r\n              status=\"primary\"\r\n              content=\"主要颜色\"\r\n              >待审核 （审核人姓名：{{question.checkWorkNo}}  审核人工号：{{question.checkUserName}}）</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '1'\"\r\n              status=\"warning\"\r\n              content=\"信息颜色\"\r\n              >审核中</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '2'\"\r\n              status=\"success\"\r\n              content=\"信息颜色\"\r\n              >审核完成</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '3'\"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >驳回理由: {{ question.assessment }}</vxe-tag\r\n            >\r\n\r\n            <vxe-input\r\n              v-if=\"question.formType == '0'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"integer\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '1'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"'float'\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '2'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"text\"\r\n            ></vxe-input>\r\n            <vxe-textarea\r\n              v-if=\"question.formType == '3'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              :placeholder=\"question.formQuestion\"\r\n            ></vxe-textarea>\r\n            <vxe-text v-if=\"question.formNote != null\" status=\"warning\"\r\n              >指标:{{ question.formNote }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formNote1 != null\" status=\"warning\"\r\n              >备注:{{ question.formNote1 }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.maximum != null\" status=\"primary\"\r\n              >最大值:{{ question.maximum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.minimum != null\" status=\"primary\"\r\n              >最小值:{{ question.minimum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formUnit != null\" status=\"primary\"\r\n              >单位:{{ question.formUnit }}<br\r\n            /></vxe-text>\r\n            <vxe-tag\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >输入值超出预计范围，请输入原因和改进措施</vxe-tag\r\n            >\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].reason\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请填写原因\"\r\n            ></vxe-textarea>\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].measure\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请输入改进措施\"\r\n            ></vxe-textarea>\r\n          </template>\r\n        </vxe-form-item>\r\n      </vxe-form-group>\r\n\r\n      <vxe-form-item align=\"center\" span=\"24\" :item-render=\"{}\">\r\n        <template #default>\r\n          <vxe-button\r\n            type=\"submit\"\r\n            status=\"primary\"\r\n            content=\"提交\"\r\n          ></vxe-button>\r\n        </template>\r\n      </vxe-form-item>\r\n    </vxe-form>\r\n\r\n    <el-dialog\r\n      title=\"选择导出范围\"\r\n      :visible.sync=\"importOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplate\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"upload.headers\"\r\n            :disabled=\"upload.isUploading\"\r\n            :action=\"upload.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    <el-dialog\r\n      title=\"单位时间报表导出\"\r\n      :visible.sync=\"SpecialImportOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplateSpecial\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"uploadSpecial.headers\"\r\n            :disabled=\"uploadSpecial.isUploading\"\r\n            :action=\"uploadSpecial.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"SpecialImportOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog  title=\"文件预览\"  :visible.sync=\"searchopen\" width=\"1800px\" >\r\n        <div class=\"test\">\r\n          <vue-office-excel\r\n              :src=\"customBlobContent\"\r\n              style=\"height: 100vh;\"\r\n          />\r\n        </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport { answerListPlusAll,formFrequency } from \"@/api/tYjy/form\";\r\nimport answerInput from \"./input\";\r\nimport { newAdd, addAlone } from \"@/api/tYjy/answer\";\r\nimport { getAllRootListForAnswer } from \"@/api/tYjy/dimensionality\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport axios from \"axios\";\r\nimport * as xlsx from 'xlsx';\r\n\r\n//引入相关样式\r\n\r\nexport default {\r\n  name: \"Answer\",\r\n  components: { answerInput },\r\n  data() {\r\n    return {\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() > Date.now();\r\n        },\r\n      },\r\n      frequencyOptions: [],\r\n\r\n      queryParams: {\r\n        formQuestion: undefined,\r\n        fcDate: undefined,\r\n        dimensionalityId: undefined,\r\n        formQuestion: undefined,\r\n      },\r\n\r\n      formType: null,\r\n      dimensionalityNames: null,\r\n      drawerShow: false,\r\n      stickyTop: 0,\r\n      loading: false,\r\n      showSearch: true,\r\n      answerList: [],\r\n      formData: {},\r\n      row: {},\r\n      rootList: [],\r\n      userInfo: {},\r\n      datesave:{},\r\n      pathsave:{},\r\n      count:1,\r\n      deptName:null,\r\n      dimensionalityName:null,\r\n      dateValue: null,\r\n      specialFcDate:null,\r\n      queryImport: {\r\n        startDate: null,\r\n        endDate: null,\r\n        rootId: null,\r\n      },\r\n      importOpen:false,\r\n      SpecialImportOpen:false,\r\n      // 导入参数\r\n      upload: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importData\",\r\n      },\r\n\r\n\r\n      uploadSpecial: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importDataSpecial\",\r\n\r\n      },\r\n      excelHtml:\"\",\r\n      searchopen:false,\r\n      excelData: [], // 存储 Excel 数据\r\n      exceltitle: [],\r\n      customBlobContent:\"\"\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userInfo = JSON.parse(JSON.stringify(this.$store.state.user));\r\n  },\r\n\r\n  created() {\r\n    const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    const fcDate = this.$route.query && this.$route.query.fcDate;\r\n    this.queryParams.dimensionalityId=dimensionalityId\r\n    this.queryParams.fcDate=fcDate\r\n    this.initData();\r\n\r\n\r\n    // if(this.$route.query)\r\n    // {\r\n    //   const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    //   // const dimensionalityName = this.$route.query && this.$route.query.dimensionalityName;\r\n    //   const fcDate = this.$route.query && this.$route.query.fcDate;\r\n    //   this.queryParams.dimensionalityId=dimensionalityId\r\n    //   // this.queryParams.dimensionalityName=dimensionalityName\r\n    //   this.queryParams.fcDate=fcDate\r\n    //   this.initData1();\r\n    // }\r\n    // else\r\n    // {\r\n    //   this.initData();\r\n    // }\r\n  },\r\n  methods: {\r\n    onDateChange() {\r\n      console.log(this.dateValue);\r\n      if (this.dateValue != null && this.dateValue != \"\") {\r\n        this.queryImport.startDate = this.dateValue[0];\r\n        this.queryImport.endDate = this.dateValue[1];\r\n      } else {\r\n        this.queryImport.startDate = \"\";\r\n        this.queryImport.endDate = \"\";\r\n      }\r\n    },\r\n    clickNode($event, node) {\r\n      $event.target.parentElement.parentElement.firstElementChild.click();\r\n    },\r\n    changeEvent(params) {\r\n      const $form = this.$refs.formRef;\r\n      if ($form) {\r\n        $form.updateStatus(params);\r\n      }\r\n    },\r\n    disabledDate(time) {\r\n      return time.getTime() < Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天\r\n    },\r\n    inputChange(val, row) {\r\n      row.formValue = val;\r\n    },\r\n    handleScroll() {\r\n      this.isSticky = window.scrollY >= this.stickyTop;\r\n    },\r\n    initData() {\r\n      getAllRootListForAnswer().then((res) => {\r\n        this.rootList = res.data;\r\n        if(this.queryParams.dimensionalityId==null)\r\n        {\r\n          this.queryParams.dimensionalityId = this.rootList[0].value;\r\n          this.deptName= this.rootList[0].deptName;\r\n          this.deptCode= this.rootList[0].deptCode;\r\n          this.dimensionalityName=this.rootList[0].label\r\n        }\r\n        else\r\n        {\r\n          // this.queryParams.dimensionalityId = this.queryParams.dimensionalityId;\r\n          for(let i=0;i<this.rootList.length;i++)\r\n          {\r\n            if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n            {\r\n              this.queryParams.dimensionalityId = this.rootList[i].value;\r\n              this.deptName= this.rootList[i].deptName;\r\n              this.deptCode= this.rootList[i].deptCode;\r\n              this.dimensionalityName=this.rootList[i].label\r\n            }\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n    initData1() {\r\n      getAllRootListForAnswer().then((res) => {\r\n        this.rootList = res.data;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n          {\r\n            this.deptName= this.rootList[0].deptName;\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n    containsSubstring(substring, string) {\r\n      return string.includes(substring);\r\n    },\r\n    aloneList(string) {\r\n      if(string== '气体结算月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '高炉、转炉煤气月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '天然气消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '蒸汽消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '电量月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '2025年经济责任制考核表（特板事业部）')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '水处理水量报表')\r\n      {\r\n        return true;\r\n      }\r\n      \r\n      // if(string== '研究院目标指标一览')\r\n      // {\r\n      //   return true;\r\n      // }\r\n      return false;\r\n    },\r\n\r\n    /** 查询TYjyAnswer列表 */\r\n    getList() {\r\n      formFrequency({dimensionalityId: this.queryParams.dimensionalityId}).then((res) => {\r\n           if(this.count!=res.data)\r\n           {\r\n            this.queryParams.fcDate=undefined\r\n           }\r\n           this.count=res.data\r\n      });\r\n\r\n      this.answerList = [];\r\n      answerListPlusAll({\r\n        fcDate: this.queryParams.fcDate,\r\n        dimensionalityId: this.queryParams.dimensionalityId,\r\n        formQuestion: this.queryParams.formQuestion,\r\n      }).then((res) => {\r\n        let answerList = [];\r\n        let list = res.data;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n          {\r\n            this.queryParams.dimensionalityId = this.rootList[i].value;\r\n            this.deptName= this.rootList[i].deptName;\r\n            this.deptCode= this.rootList[i].deptCode;\r\n            this.dimensionalityName=this.rootList[i].label\r\n          }\r\n        }\r\n        if(this.containsSubstring('安全责任工资考核表',this.dimensionalityName))\r\n        {\r\n          console.log(\"test1\")\r\n          let num=0\r\n          for(let i=0;i<list.length;i++)\r\n          {\r\n            if(list[i].formQuestion!='自评总分' && list[i].formQuestion!='厂长评分')\r\n            {\r\n              num=1;\r\n              break;\r\n            }\r\n          }\r\n          if(num==0)\r\n          {\r\n            for(let i=0;i<list.length;i++)\r\n            {\r\n              list[i].dimensionalityName=list[i].dimensionalityName.replace('/七、考核评分', '')\r\n            }\r\n            console.log(\"test3\",list)\r\n          }\r\n        }\r\n        \r\n        for(let i=0;i<list.length;i++)\r\n        {\r\n            this.datesave[list[i].formId]=list[i].formValue\r\n            this.pathsave[list[i].dimensionalityName]=list[i].dimensionalityPath\r\n        }\r\n        // 使用 map 提取 dimensionalityName 属性到一个数组\r\n        let dimensionalityNames = list.map((x) => x.dimensionalityName);\r\n\r\n        // 提取 / 后的前三位字符，并与原字符串配对\r\n        dimensionalityNames = dimensionalityNames.map((name) => {\r\n          // let key = name.includes(\"/\") ? name.split(\"/\")[1].slice(0, 3) : \"\";\r\n          let key = this.pathsave[name];\r\n          return { originalName: name, sortKey: key };\r\n        });\r\n\r\n        // 按照提取出的前三字符排序\r\n        dimensionalityNames.sort((a, b) => a.sortKey.localeCompare(b.sortKey));\r\n        // console.log(\"test0\",dimensionalityNames)\r\n        // 如果需要，可以提取排序后的原始名字\r\n        dimensionalityNames = dimensionalityNames.map(\r\n          (item) => item.originalName\r\n        );\r\n\r\n        // 使用 Set 去重\r\n        let uniqueDimensionalityNames = [...new Set(dimensionalityNames)];\r\n\r\n        uniqueDimensionalityNames.forEach((title) => {\r\n          let group = {\r\n            title: \"\",\r\n            list: [],\r\n          };\r\n          group.title = title;\r\n          group.list = list.filter((item) => item.dimensionalityName === title);\r\n          // 假设你有一个数组来存储所有的组\r\n          answerList.push(group); // 将生成的组添加到groups数组中\r\n        });\r\n        this.answerList = answerList;\r\n        // console.log(\"test111\",answerList)\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    handleQuery() {\r\n      for(let i=0;i<this.rootList.length;i++)\r\n      {\r\n        if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n        {\r\n          this.deptName= this.rootList[i].deptName;\r\n          this.deptCode= this.rootList[i].deptCode;\r\n          this.dimensionalityName=this.rootList[i].label\r\n        }\r\n      }\r\n      this.getList();\r\n    },\r\n    handlePreview() {\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.queryParams.fcDate\r\n      queryImport.type=\"0\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportWithTemplate\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n      else\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n    },\r\n    \r\n    handleDateChange() {\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.fcDate = undefined;\r\n      this.queryParams.formQuestion = undefined;\r\n      this.queryParams.dimensionalityId = undefined;\r\n      this.getList();\r\n    },\r\n    handleUpload({ file }) {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n      return axios\r\n        .post(\r\n          process.env.VUE_APP_BASE_API + \"/common/uploadMinioDataReport\",\r\n          formData\r\n        )\r\n        .then((res) => {\r\n          return {\r\n            ...res.data,\r\n          };\r\n        });\r\n    },\r\n    /** 提交按钮 */\r\n    handleSubmit() {\r\n      console.log(this.answerList);\r\n      console.log(\"test1\",this.datesave);\r\n      // 首先对 answerList 进行处理：合并、过滤和转换\r\n      let processedLists = this.answerList\r\n        .reduce((acc, current) => {\r\n          return acc.concat(current.list);\r\n        }, [])\r\n        .filter((x) => {\r\n          // 过滤条件\r\n          console.log(\"test1\",x.status);\r\n          console.log(this.datesave[x.formId]);\r\n          console.log(x.formValue);\r\n          return (\r\n            x.formValue != null &&\r\n            x.formValue != \"\" &&\r\n            ((![\"1\"].includes(x.status))&&\r\n            (\r\n              ([\"0\",\"2\",\"3\"].includes(x.status) && this.datesave[x.formId]!=x.formValue))\r\n              ||([\"4\"].includes(x.status))\r\n            )\r\n          );\r\n        });\r\n\r\n      // 对符合条件的元素进行 formValue 的转换\r\n      processedLists.forEach((x) => {\r\n        if ([\"0\", \"1\"].includes(x.formType)) {\r\n          x.formValue = parseFloat(x.formValue);\r\n        }\r\n        x.fcDate = this.queryParams.fcDate;\r\n      });\r\n\r\n      // 最后进行深拷贝\r\n      let allLists = JSON.parse(JSON.stringify(processedLists));\r\n\r\n      // console.log(\"allLists:\", allLists);\r\n      const now = new Date();\r\n      \r\n      let datestr=\"请确定是否要提交数据\"\r\n      if(this.queryParams.fcDate==null)\r\n      {\r\n        // 获取年月日\r\n        const year = now.getFullYear();\r\n        const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要+1\r\n        const day = String(now.getDate()).padStart(2, '0');\r\n\r\n        // 格式化为 yyyy年MM月dd日\r\n        const format1 = `${year}年${month}月${day}日`;\r\n\r\n        // 格式化为 yyyy年MM月\r\n        const format2 = `${year}年${month}月`;\r\n        \r\n        if(this.count=='1')\r\n        {\r\n          datestr='您未选择时间,请确定是否要提交'+format1+'的数据?'\r\n        }\r\n        else\r\n        {\r\n          datestr='您未选择时间,请确定是否要提交'+format2+'的数据?'\r\n        }\r\n      }\r\n      this.$confirm(\r\n        datestr,\r\n        {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n\r\n        }\r\n      ).then(()=>{\r\n        newAdd(allLists).then((res) => {\r\n        this.getList();\r\n        this.msgSuccess(\"保存成功\");\r\n      });\r\n      }).catch(()=>{});\r\n\r\n      // newAdd(allLists).then((res) => {\r\n      //   this.getList();\r\n      //   this.msgSuccess(\"保存成功\");\r\n      // });\r\n    },\r\n    \r\n    handleFileUploadProgress() {\r\n      this.upload.isUploading = true;\r\n    },\r\n    handleFileSuccess(response) {\r\n      console.log(response)\r\n      if (response.code == 200) {\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n        this.getList();\r\n        this.importOpen = false;\r\n        this.SpecialImportOpen = false;\r\n      }\r\n      else {\r\n        this.$modal.msgError(\"上传失败\")\r\n      }\r\n      this.upload.isUploading = false;\r\n    },\r\n    // 模板下载\r\n    downloadTemplate(){\r\n    \r\n      if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportTemplate\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        \"(\" +\r\n          this.queryImport.startDate +\r\n          \"-\" +\r\n          this.queryImport.endDate +\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n    \r\n    },\r\n\r\n        // 模板下载\r\n    downloadTemplateSpecial(){\r\n      if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (\r\n      //   this.specialFcDate == null \r\n      // ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"导出前请先输入开始结束时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"0\"\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n  \r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n.test{\r\n  height: 100vh;\r\n}\r\n\r\n.excel-preview {\r\n  margin-top: 20px;\r\n  overflow: auto;\r\n  max-height: 500px;\r\n  border: 1px solid #ddd;\r\n  padding: 10px;\r\n}\r\n\r\n/* 设置滚动条的样式 */\r\n::-webkit-scrollbar {\r\n  width: 10px;\r\n  /* 竖向滚动条宽度 */\r\n  height: 15px;\r\n  /* 横向滚动条宽度 */\r\n  background-color: #ffffff;\r\n}\r\n\r\n/* 滚动槽 */\r\n::-webkit-scrollbar-track {\r\n  /* 其实直接在  ::-webkit-scrollbar 中设置也能达到同样的视觉效果*/\r\n  /* -webkit-box-shadow: inset 0 0 6px rgba(177, 223, 117, 0.7); */\r\n  background-color: #e4e4e4;\r\n  border-radius: 10px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 5px;\r\n  -webkit-box-shadow: inset 0 0 6px rgba(158, 156, 156, 0.616);\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(139, 138, 138, 0.616);\r\n  -webkit-box-shadow: unset;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:window-inactive {\r\n  /* 容器不被激活时的样式 */\r\n  background: #bdbdbd66;\r\n}\r\n\r\n::-webkit-scrollbar-corner {\r\n  /* 两个滚动条交汇处边角的样式 */\r\n  background-color: #cacaca66;\r\n}\r\n\r\n.sticky {\r\n  padding: 20px;\r\n  position: -webkit-sticky;\r\n  position: sticky;\r\n  top: 0; /* 粘性定位的起始位置 */\r\n  z-index: 100; /* 确保按钮在卡片之上 */\r\n}\r\n.el-tabs--card {\r\n  height: calc(100vh - 110px);\r\n}\r\n.el-tab-pane {\r\n  height: calc(100vh - 110px);\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n  "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwXA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AACA,IAAAI,eAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,IAAA,GAAAC,uBAAA,CAAAR,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AAAA,IAAAS,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,UAAA;IAAAC,WAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;QACA;MACA;MACAC,gBAAA;MAEAC,WAAA,MAAAC,gBAAA,CAAAb,OAAA;QACAc,YAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD,SAAA;QACAE,gBAAA,EAAAF;MAAA,mBACAA,SAAA,CACA;MAEAG,QAAA;MACAC,mBAAA;MACAC,UAAA;MACAC,SAAA;MACAC,OAAA;MACAC,UAAA;MACAC,UAAA;MACAC,QAAA;MACAC,GAAA;MACAC,QAAA;MACAC,QAAA;MACAC,QAAA;MACAC,QAAA;MACAC,KAAA;MACAC,QAAA;MACAC,kBAAA;MACAC,SAAA;MACAC,aAAA;MACAC,WAAA;QACAC,SAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAC,UAAA;MACAC,iBAAA;MACA;MACAC,MAAA;QACA;QACAC,WAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EACAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GACA;MACA;MAGAC,aAAA;QACA;QACAR,WAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EACAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA,GACA;MAEA;MACAE,SAAA;MACAC,UAAA;MACAC,SAAA;MAAA;MACAC,UAAA;MACAC,iBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAA7B,QAAA,GAAA8B,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA;EACA;EAEAC,OAAA,WAAAA,QAAA;IACA,IAAA/C,gBAAA,QAAAgD,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAjD,gBAAA;IACA,IAAAD,MAAA,QAAAiD,MAAA,CAAAC,KAAA,SAAAD,MAAA,CAAAC,KAAA,CAAAlD,MAAA;IACA,KAAAJ,WAAA,CAAAK,gBAAA,GAAAA,gBAAA;IACA,KAAAL,WAAA,CAAAI,MAAA,GAAAA,MAAA;IACA,KAAAmD,QAAA;;IAGA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAA;MACAC,OAAA,CAAAC,GAAA,MAAArC,SAAA;MACA,SAAAA,SAAA,iBAAAA,SAAA;QACA,KAAAE,WAAA,CAAAC,SAAA,QAAAH,SAAA;QACA,KAAAE,WAAA,CAAAE,OAAA,QAAAJ,SAAA;MACA;QACA,KAAAE,WAAA,CAAAC,SAAA;QACA,KAAAD,WAAA,CAAAE,OAAA;MACA;IACA;IACAkC,SAAA,WAAAA,UAAAC,MAAA,EAAAC,IAAA;MACAD,MAAA,CAAAE,MAAA,CAAAC,aAAA,CAAAA,aAAA,CAAAC,iBAAA,CAAAC,KAAA;IACA;IACAC,WAAA,WAAAA,YAAAC,MAAA;MACA,IAAAC,KAAA,QAAAC,KAAA,CAAAC,OAAA;MACA,IAAAF,KAAA;QACAA,KAAA,CAAAG,YAAA,CAAAJ,MAAA;MACA;IACA;IACA1E,YAAA,WAAAA,aAAAC,IAAA;MACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;IACA;IACA2E,WAAA,WAAAA,YAAAC,GAAA,EAAA5D,GAAA;MACAA,GAAA,CAAA6D,SAAA,GAAAD,GAAA;IACA;IACAE,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,GAAAC,MAAA,CAAAC,OAAA,SAAAtE,SAAA;IACA;IACA8C,QAAA,WAAAA,SAAA;MAAA,IAAAyB,KAAA;MACA,IAAAC,uCAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAjE,QAAA,GAAAoE,GAAA,CAAA3F,IAAA;QACA,IAAAwF,KAAA,CAAAhF,WAAA,CAAAK,gBAAA,UACA;UACA2E,KAAA,CAAAhF,WAAA,CAAAK,gBAAA,GAAA2E,KAAA,CAAAjE,QAAA,IAAAqE,KAAA;UACAJ,KAAA,CAAA5D,QAAA,GAAA4D,KAAA,CAAAjE,QAAA,IAAAK,QAAA;UACA4D,KAAA,CAAAK,QAAA,GAAAL,KAAA,CAAAjE,QAAA,IAAAsE,QAAA;UACAL,KAAA,CAAA3D,kBAAA,GAAA2D,KAAA,CAAAjE,QAAA,IAAAuE,KAAA;QACA,OAEA;UACA;UACA,SAAAC,CAAA,MAAAA,CAAA,GAAAP,KAAA,CAAAjE,QAAA,CAAAyE,MAAA,EAAAD,CAAA,IACA;YACA,IAAAP,KAAA,CAAAhF,WAAA,CAAAK,gBAAA,IAAA2E,KAAA,CAAAjE,QAAA,CAAAwE,CAAA,EAAAH,KAAA,EACA;cACAJ,KAAA,CAAAhF,WAAA,CAAAK,gBAAA,GAAA2E,KAAA,CAAAjE,QAAA,CAAAwE,CAAA,EAAAH,KAAA;cACAJ,KAAA,CAAA5D,QAAA,GAAA4D,KAAA,CAAAjE,QAAA,CAAAwE,CAAA,EAAAnE,QAAA;cACA4D,KAAA,CAAAK,QAAA,GAAAL,KAAA,CAAAjE,QAAA,CAAAwE,CAAA,EAAAF,QAAA;cACAL,KAAA,CAAA3D,kBAAA,GAAA2D,KAAA,CAAAjE,QAAA,CAAAwE,CAAA,EAAAD,KAAA;YACA;UACA;QACA;QACAN,KAAA,CAAAS,OAAA;MACA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAV,uCAAA,IAAAC,IAAA,WAAAC,GAAA;QACAQ,MAAA,CAAA5E,QAAA,GAAAoE,GAAA,CAAA3F,IAAA;QACA,SAAA+F,CAAA,MAAAA,CAAA,GAAAI,MAAA,CAAA5E,QAAA,CAAAyE,MAAA,EAAAD,CAAA,IACA;UACA,IAAAI,MAAA,CAAA3F,WAAA,CAAAK,gBAAA,IAAAsF,MAAA,CAAA5E,QAAA,CAAAwE,CAAA,EAAAH,KAAA,EACA;YACAO,MAAA,CAAAvE,QAAA,GAAAuE,MAAA,CAAA5E,QAAA,IAAAK,QAAA;UACA;QACA;QACAuE,MAAA,CAAAF,OAAA;MACA;IACA;IACAG,iBAAA,WAAAA,kBAAAC,SAAA,EAAAC,MAAA;MACA,OAAAA,MAAA,CAAAC,QAAA,CAAAF,SAAA;IACA;IACAG,SAAA,WAAAA,UAAAF,MAAA;MACA,IAAAA,MAAA,cACA;QACA;MACA;MACA,IAAAA,MAAA,kBACA;QACA;MACA;MACA,IAAAA,MAAA,gBACA;QACA;MACA;MACA,IAAAA,MAAA,eACA;QACA;MACA;MACA,IAAAA,MAAA,aACA;QACA;MACA;MACA,IAAAA,MAAA,4BACA;QACA;MACA;MACA,IAAAA,MAAA,eACA;QACA;MACA;;MAEA;MACA;MACA;MACA;MACA;IACA;IAEA,qBACAL,OAAA,WAAAA,QAAA;MAAA,IAAAQ,MAAA;MACA,IAAAC,mBAAA;QAAA7F,gBAAA,OAAAL,WAAA,CAAAK;MAAA,GAAA6E,IAAA,WAAAC,GAAA;QACA,IAAAc,MAAA,CAAA9E,KAAA,IAAAgE,GAAA,CAAA3F,IAAA,EACA;UACAyG,MAAA,CAAAjG,WAAA,CAAAI,MAAA,GAAAD,SAAA;QACA;QACA8F,MAAA,CAAA9E,KAAA,GAAAgE,GAAA,CAAA3F,IAAA;MACA;MAEA,KAAAoB,UAAA;MACA,IAAAuF,uBAAA;QACA/F,MAAA,OAAAJ,WAAA,CAAAI,MAAA;QACAC,gBAAA,OAAAL,WAAA,CAAAK,gBAAA;QACAH,YAAA,OAAAF,WAAA,CAAAE;MACA,GAAAgF,IAAA,WAAAC,GAAA;QACA,IAAAvE,UAAA;QACA,IAAAwF,IAAA,GAAAjB,GAAA,CAAA3F,IAAA;QACA,SAAA+F,CAAA,MAAAA,CAAA,GAAAU,MAAA,CAAAlF,QAAA,CAAAyE,MAAA,EAAAD,CAAA,IACA;UACA,IAAAU,MAAA,CAAAjG,WAAA,CAAAK,gBAAA,IAAA4F,MAAA,CAAAlF,QAAA,CAAAwE,CAAA,EAAAH,KAAA,EACA;YACAa,MAAA,CAAAjG,WAAA,CAAAK,gBAAA,GAAA4F,MAAA,CAAAlF,QAAA,CAAAwE,CAAA,EAAAH,KAAA;YACAa,MAAA,CAAA7E,QAAA,GAAA6E,MAAA,CAAAlF,QAAA,CAAAwE,CAAA,EAAAnE,QAAA;YACA6E,MAAA,CAAAZ,QAAA,GAAAY,MAAA,CAAAlF,QAAA,CAAAwE,CAAA,EAAAF,QAAA;YACAY,MAAA,CAAA5E,kBAAA,GAAA4E,MAAA,CAAAlF,QAAA,CAAAwE,CAAA,EAAAD,KAAA;UACA;QACA;QACA,IAAAW,MAAA,CAAAL,iBAAA,cAAAK,MAAA,CAAA5E,kBAAA,GACA;UACAqC,OAAA,CAAAC,GAAA;UACA,IAAA0C,GAAA;UACA,SAAAd,EAAA,MAAAA,EAAA,GAAAa,IAAA,CAAAZ,MAAA,EAAAD,EAAA,IACA;YACA,IAAAa,IAAA,CAAAb,EAAA,EAAArF,YAAA,cAAAkG,IAAA,CAAAb,EAAA,EAAArF,YAAA,YACA;cACAmG,GAAA;cACA;YACA;UACA;UACA,IAAAA,GAAA,OACA;YACA,SAAAd,GAAA,MAAAA,GAAA,GAAAa,IAAA,CAAAZ,MAAA,EAAAD,GAAA,IACA;cACAa,IAAA,CAAAb,GAAA,EAAAlE,kBAAA,GAAA+E,IAAA,CAAAb,GAAA,EAAAlE,kBAAA,CAAAiF,OAAA;YACA;YACA5C,OAAA,CAAAC,GAAA,UAAAyC,IAAA;UACA;QACA;QAEA,SAAAb,GAAA,MAAAA,GAAA,GAAAa,IAAA,CAAAZ,MAAA,EAAAD,GAAA,IACA;UACAU,MAAA,CAAAhF,QAAA,CAAAmF,IAAA,CAAAb,GAAA,EAAAgB,MAAA,IAAAH,IAAA,CAAAb,GAAA,EAAAZ,SAAA;UACAsB,MAAA,CAAA/E,QAAA,CAAAkF,IAAA,CAAAb,GAAA,EAAAlE,kBAAA,IAAA+E,IAAA,CAAAb,GAAA,EAAAiB,kBAAA;QACA;QACA;QACA,IAAAjG,mBAAA,GAAA6F,IAAA,CAAAK,GAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAArF,kBAAA;QAAA;;QAEA;QACAd,mBAAA,GAAAA,mBAAA,CAAAkG,GAAA,WAAApH,IAAA;UACA;UACA,IAAAsH,GAAA,GAAAV,MAAA,CAAA/E,QAAA,CAAA7B,IAAA;UACA;YAAAuH,YAAA,EAAAvH,IAAA;YAAAwH,OAAA,EAAAF;UAAA;QACA;;QAEA;QACApG,mBAAA,CAAAuG,IAAA,WAAAC,CAAA,EAAAC,CAAA;UAAA,OAAAD,CAAA,CAAAF,OAAA,CAAAI,aAAA,CAAAD,CAAA,CAAAH,OAAA;QAAA;QACA;QACA;QACAtG,mBAAA,GAAAA,mBAAA,CAAAkG,GAAA,CACA,UAAAS,IAAA;UAAA,OAAAA,IAAA,CAAAN,YAAA;QAAA,CACA;;QAEA;QACA,IAAAO,yBAAA,OAAAC,mBAAA,CAAAhI,OAAA,MAAAiI,GAAA,CAAA9G,mBAAA;QAEA4G,yBAAA,CAAAG,OAAA,WAAAC,KAAA;UACA,IAAAC,KAAA;YACAD,KAAA;YACAnB,IAAA;UACA;UACAoB,KAAA,CAAAD,KAAA,GAAAA,KAAA;UACAC,KAAA,CAAApB,IAAA,GAAAA,IAAA,CAAAqB,MAAA,WAAAP,IAAA;YAAA,OAAAA,IAAA,CAAA7F,kBAAA,KAAAkG,KAAA;UAAA;UACA;UACA3G,UAAA,CAAA8G,IAAA,CAAAF,KAAA;QACA;QACAvB,MAAA,CAAArF,UAAA,GAAAA,UAAA;QACA;QACAqF,MAAA,CAAA0B,YAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,SAAArC,CAAA,MAAAA,CAAA,QAAAxE,QAAA,CAAAyE,MAAA,EAAAD,CAAA,IACA;QACA,SAAAvF,WAAA,CAAAK,gBAAA,SAAAU,QAAA,CAAAwE,CAAA,EAAAH,KAAA,EACA;UACA,KAAAhE,QAAA,QAAAL,QAAA,CAAAwE,CAAA,EAAAnE,QAAA;UACA,KAAAiE,QAAA,QAAAtE,QAAA,CAAAwE,CAAA,EAAAF,QAAA;UACA,KAAAhE,kBAAA,QAAAN,QAAA,CAAAwE,CAAA,EAAAD,KAAA;QACA;MACA;MACA,KAAAG,OAAA;IACA;IACAoC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAtG,WAAA;MACAA,WAAA,CAAAG,MAAA,QAAA3B,WAAA,CAAAK,gBAAA;MACAmB,WAAA,CAAApB,MAAA,QAAAJ,WAAA,CAAAI,MAAA;MACAoB,WAAA,CAAAuG,IAAA;MACA,SAAA1G,kBAAA,iBACA;QACA,KAAA2G,YAAA,CACA,2CAAAC,cAAA,CAAA7I,OAAA,MAEAoC,WAAA,GAEA,KAAAH,kBAAA,cAAAE,aAAA,GACA,yBAEA,EAAA2D,IAAA,WAAAgD,IAAA;UACA,IAAAC,MAAA,OAAAC,UAAA;UACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;UACAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;YACAT,MAAA,CAAAlF,iBAAA,GAAAuF,MAAA,CAAAK,MAAA;YACA,IAAAC,IAAA,OAAAC,UAAA,CAAAH,GAAA,CAAAxE,MAAA,CAAAyE,MAAA;YACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAT,IAAA,CAAAU,IAAA;YACA,IAAAC,QAAA,GAAA7J,IAAA,CAAA8J,IAAA,CAAAL,IAAA;cAAAV,IAAA;YAAA;YACA,IAAAgB,UAAA,GAAAF,QAAA,CAAAG,UAAA;YACA,IAAAC,SAAA,GAAAF,UAAA;YACA,IAAAG,SAAA,GAAAL,QAAA,CAAAM,MAAA,CAAAF,SAAA;YACA;YACA,IAAAG,UAAA,GAAApK,IAAA,CAAAqK,KAAA,CAAAC,aAAA,CAAAJ,SAAA;YACA;YACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAA3C,GAAA,CACA,UAAAS,IAAA;cACA,OAAAA,IAAA;YACA,CACA;YACAY,MAAA,CAAApF,SAAA,GAAA0G,UAAA;YACAtB,MAAA,CAAAnF,UAAA,GAAA4G,UAAA;YACAzB,MAAA,CAAAtF,SAAA,GAAA4G,UAAA;YACAtB,MAAA,CAAArF,UAAA;UACA;QACA;MACA,OAEA;QACA,KAAAuF,YAAA,CACA,8CAAAC,cAAA,CAAA7I,OAAA,MAEAoC,WAAA,GAEA,KAAAH,kBAAA,cAAAE,aAAA,GACA,yBAEA,EAAA2D,IAAA,WAAAgD,IAAA;UACA,IAAAC,MAAA,OAAAC,UAAA;UACAD,MAAA,CAAAE,iBAAA,CAAAH,IAAA;UACAC,MAAA,CAAAG,MAAA,aAAAC,GAAA;YACAT,MAAA,CAAAlF,iBAAA,GAAAuF,MAAA,CAAAK,MAAA;YACA,IAAAC,IAAA,OAAAC,UAAA,CAAAH,GAAA,CAAAxE,MAAA,CAAAyE,MAAA;YACAC,IAAA,GAAAA,IAAA,CAAAE,KAAA,IAAAT,IAAA,CAAAU,IAAA;YACA,IAAAC,QAAA,GAAA7J,IAAA,CAAA8J,IAAA,CAAAL,IAAA;cAAAV,IAAA;YAAA;YACA,IAAAgB,UAAA,GAAAF,QAAA,CAAAG,UAAA;YACA,IAAAC,SAAA,GAAAF,UAAA;YACA,IAAAG,SAAA,GAAAL,QAAA,CAAAM,MAAA,CAAAF,SAAA;YACA;YACA,IAAAG,UAAA,GAAApK,IAAA,CAAAqK,KAAA,CAAAC,aAAA,CAAAJ,SAAA;YACA;YACA,IAAAK,UAAA,GAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAP,UAAA,MAAA3C,GAAA,CACA,UAAAS,IAAA;cACA,OAAAA,IAAA;YACA,CACA;YACAY,MAAA,CAAApF,SAAA,GAAA0G,UAAA;YACAtB,MAAA,CAAAnF,UAAA,GAAA4G,UAAA;YACAzB,MAAA,CAAAtF,SAAA,GAAA4G,UAAA;YACAtB,MAAA,CAAArF,UAAA;UACA;QACA;MACA;IACA;IAEAmH,gBAAA,WAAAA,iBAAA;MACA,KAAAnE,OAAA;IACA;IACAoE,UAAA,WAAAA,WAAA;MACA,KAAA7J,WAAA,CAAAI,MAAA,GAAAD,SAAA;MACA,KAAAH,WAAA,CAAAE,YAAA,GAAAC,SAAA;MACA,KAAAH,WAAA,CAAAK,gBAAA,GAAAF,SAAA;MACA,KAAAsF,OAAA;IACA;IACAqE,YAAA,WAAAA,aAAAC,IAAA;MAAA,IAAAC,IAAA,GAAAD,IAAA,CAAAC,IAAA;MACA,IAAAnJ,QAAA,OAAAoJ,QAAA;MACApJ,QAAA,CAAAqJ,MAAA,SAAAF,IAAA;MACA,OAAAG,cAAA,CACAC,IAAA,CACAhI,OAAA,CAAAC,GAAA,CAAAC,gBAAA,oCACAzB,QACA,EACAqE,IAAA,WAAAC,GAAA;QACA,WAAA8C,cAAA,CAAA7I,OAAA,MACA+F,GAAA,CAAA3F,IAAA;MAEA;IACA;IACA,WACA6K,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA5G,OAAA,CAAAC,GAAA,MAAA/C,UAAA;MACA8C,OAAA,CAAAC,GAAA,eAAA1C,QAAA;MACA;MACA,IAAAsJ,cAAA,QAAA3J,UAAA,CACA4J,MAAA,WAAAC,GAAA,EAAAC,OAAA;QACA,OAAAD,GAAA,CAAAE,MAAA,CAAAD,OAAA,CAAAtE,IAAA;MACA,OACAqB,MAAA,WAAAf,CAAA;QACA;QACAhD,OAAA,CAAAC,GAAA,UAAA+C,CAAA,CAAAkE,MAAA;QACAlH,OAAA,CAAAC,GAAA,CAAA2G,MAAA,CAAArJ,QAAA,CAAAyF,CAAA,CAAAH,MAAA;QACA7C,OAAA,CAAAC,GAAA,CAAA+C,CAAA,CAAA/B,SAAA;QACA,OACA+B,CAAA,CAAA/B,SAAA,YACA+B,CAAA,CAAA/B,SAAA,WACA,OAAAoB,QAAA,CAAAW,CAAA,CAAAkE,MAAA,KAEA,gBAAA7E,QAAA,CAAAW,CAAA,CAAAkE,MAAA,KAAAN,MAAA,CAAArJ,QAAA,CAAAyF,CAAA,CAAAH,MAAA,KAAAG,CAAA,CAAA/B,SAAA,IACA,MAAAoB,QAAA,CAAAW,CAAA,CAAAkE,MAAA,EACA;MAEA;;MAEA;MACAL,cAAA,CAAAjD,OAAA,WAAAZ,CAAA;QACA,eAAAX,QAAA,CAAAW,CAAA,CAAApG,QAAA;UACAoG,CAAA,CAAA/B,SAAA,GAAAkG,UAAA,CAAAnE,CAAA,CAAA/B,SAAA;QACA;QACA+B,CAAA,CAAAtG,MAAA,GAAAkK,MAAA,CAAAtK,WAAA,CAAAI,MAAA;MACA;;MAEA;MACA,IAAA0K,QAAA,GAAAhI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAuH,cAAA;;MAEA;MACA,IAAAzK,GAAA,OAAAD,IAAA;MAEA,IAAAkL,OAAA;MACA,SAAA/K,WAAA,CAAAI,MAAA,UACA;QACA;QACA,IAAA4K,IAAA,GAAAlL,GAAA,CAAAmL,WAAA;QACA,IAAAC,KAAA,GAAAC,MAAA,CAAArL,GAAA,CAAAsL,QAAA,QAAAC,QAAA;QACA,IAAAC,GAAA,GAAAH,MAAA,CAAArL,GAAA,CAAAyL,OAAA,IAAAF,QAAA;;QAEA;QACA,IAAAG,OAAA,MAAAb,MAAA,CAAAK,IAAA,YAAAL,MAAA,CAAAO,KAAA,YAAAP,MAAA,CAAAW,GAAA;;QAEA;QACA,IAAAG,OAAA,MAAAd,MAAA,CAAAK,IAAA,YAAAL,MAAA,CAAAO,KAAA;QAEA,SAAA/J,KAAA,SACA;UACA4J,OAAA,uBAAAS,OAAA;QACA,OAEA;UACAT,OAAA,uBAAAU,OAAA;QACA;MACA;MACA,KAAAC,QAAA,CACAX,OAAA,EACA;QACAY,iBAAA;QACAC,gBAAA;QACA7D,IAAA;MAEA,CACA,EAAA7C,IAAA;QACA,IAAA2G,cAAA,EAAAf,QAAA,EAAA5F,IAAA,WAAAC,GAAA;UACAmF,MAAA,CAAA7E,OAAA;UACA6E,MAAA,CAAAwB,UAAA;QACA;MACA,GAAAC,KAAA;;MAEA;MACA;MACA;MACA;IACA;IAEAC,wBAAA,WAAAA,yBAAA;MACA,KAAAlK,MAAA,CAAAC,WAAA;IACA;IACAkK,iBAAA,WAAAA,kBAAAC,QAAA;MACAxI,OAAA,CAAAC,GAAA,CAAAuI,QAAA;MACA,IAAAA,QAAA,CAAAC,IAAA;QACA,KAAAC,MAAA,CAAAN,UAAA;QACA,KAAArG,OAAA;QACA,KAAA7D,UAAA;QACA,KAAAC,iBAAA;MACA,OACA;QACA,KAAAuK,MAAA,CAAAC,QAAA;MACA;MACA,KAAAvK,MAAA,CAAAC,WAAA;IACA;IACA;IACAuK,gBAAA,WAAAA,iBAAA;MAEA,IACA,KAAA9K,WAAA,CAAAC,SAAA,YACA,KAAAD,WAAA,CAAAC,SAAA,UACA,KAAAD,WAAA,CAAAE,OAAA,YACA,KAAAF,WAAA,CAAAE,OAAA,QACA;QACA,KAAA6K,OAAA,CAAAC,KAAA;UACAjF,KAAA;UACAkF,OAAA;QACA;QACA;MACA;MACA,KAAAjL,WAAA,CAAAG,MAAA,QAAA3B,WAAA,CAAAK,gBAAA;MACA,KAAAqM,YAAA,CACA,uCAAAzE,cAAA,CAAA7I,OAAA,MAEA,KAAAoC,WAAA,GAEA,MACA,KAAAA,WAAA,CAAAC,SAAA,GACA,MACA,KAAAD,WAAA,CAAAE,OAAA,GACA,yBAEA;IAEA;IAEA;IACAiL,uBAAA,WAAAA,wBAAA;MACA,SAAApL,aAAA;QACA,KAAAA,aAAA,QAAAvB,WAAA,CAAAI,MAAA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAAoB,WAAA;MACAA,WAAA,CAAAG,MAAA,QAAA3B,WAAA,CAAAK,gBAAA;MACAmB,WAAA,CAAApB,MAAA,QAAAmB,aAAA;MACAC,WAAA,CAAAuG,IAAA;MACA,KAAA2E,YAAA,CACA,8CAAAzE,cAAA,CAAA7I,OAAA,MAEAoC,WAAA,GAEA,KAAAH,kBAAA,cAAAE,aAAA,GACA,yBAEA;IAEA;EAEA;AACA", "ignoreList": []}]}