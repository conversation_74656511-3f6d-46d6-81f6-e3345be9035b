package com.ruoyi.app.v1.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.app.v1.domain.HrLateralAssessDept;
import com.ruoyi.app.v1.domain.HrSelfAssessUser;
import com.ruoyi.common.core.service.BaseSoService;

/**
 * 绩效考核-干部自评人员配置Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface IHrSelfAssessUserService extends BaseSoService<HrSelfAssessUser>
{
    // 根据工号查询用户配置信息
    HrSelfAssessUser getByWorkNo(String workNo);
    // 批量导入用户配置信息
    List<Map> importInfo(List<HrSelfAssessUser> hrSelfAssessUserList);

    boolean checkUserUnique(HrSelfAssessUser hrSelfAssessUser);
    // 钶配置人员列表
    List<HrSelfAssessUser> listAvailable(HrSelfAssessUser hrSelfAssessUser);

    // 批量获取用户填写状态
    List<HrSelfAssessUser> listAvailableWithStatus(HrSelfAssessUser hrSelfAssessUser, String assessDate);

    // 根据工号部门编号获取自评填报用户信息
    HrSelfAssessUser getByUserInfo(HrSelfAssessUser hrSelfAssessUser);

    // 获取用户自评填报部门
    List<HrLateralAssessDept> getReportDeptList(String workNo);
    // 获取用户自评审核部门
    List<HrLateralAssessDept> getCheckDeptList(String workNo);
    // 根据用户信息查询（工号、部门id）
    HrSelfAssessUser getByWorkNoDeptId(HrSelfAssessUser hrSelfAssessUser);
    // 权限刷新
    boolean permissionRefresh();
}
