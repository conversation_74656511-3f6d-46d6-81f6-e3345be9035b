package com.ruoyi.app.v1.service.impl;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.Normalizer;
import java.util.function.Function;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;


import com.ruoyi.app.jw.domain.LeaderBusiness;
import com.ruoyi.app.jw.domain.LeaderInfo;
import com.ruoyi.app.jw.domain.LeaderSteelRelative;
import com.ruoyi.app.templateFile.domain.TemplateFile;
import com.ruoyi.app.templateFile.service.ITemplateFileService;
import com.ruoyi.app.v1.domain.*;
import com.ruoyi.app.v1.enums.dataReportFrequency;
import com.ruoyi.app.v1.mapper.TYjyAnswerMapper;
import com.ruoyi.app.v1.mapper.TYjyDeptMapper;
import com.ruoyi.app.v1.mapper.TYjyFormMapper;
import com.ruoyi.app.v1.service.ITYjyAnswerService;
import com.ruoyi.app.v1.service.ITYjyFormService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.EasyExcel.ExportExcelByTemplateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.app.v1.mapper.TYjyDimensionalityMapper;
import com.ruoyi.app.v1.service.ITYjyDimensionalityService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

/**
 * TYjyDimensionalityService业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@Service
public class TYjyDimensionalityServiceImpl implements ITYjyDimensionalityService {
    @Autowired
    private TYjyDimensionalityMapper tYjyDimensionalityMapper;
    @Autowired
    private TYjyFormMapper tYjyFormMapper;
    @Autowired
    private ITYjyFormService tYjyFormService;

    @Autowired
    private ITYjyAnswerService tYjyAnswerService;

    @Autowired
    private TYjyDeptMapper tYjyDeptMapper;

//    @Autowired
//    private ITYjyDimensionalityService tYjyDimensionalityService;
    /**
     * 查询TYjyDimensionality
     *
     * @param id TYjyDimensionalityID
     * @return TYjyDimensionality
     */
    @Override
    public TYjyDimensionality selectTYjyDimensionalityById(Long id) {
        return tYjyDimensionalityMapper.selectTYjyDimensionalityById(id);
    }

    /**
     * 查询TYjyDimensionality列表
     *
     * @param tYjyDimensionality TYjyDimensionality
     * @return TYjyDimensionality
     */
    @Override
    public List<TYjyDimensionality> selectTYjyDimensionalityList(TYjyDimensionality tYjyDimensionality) {
        return tYjyDimensionalityMapper.selectTYjyDimensionalityList(tYjyDimensionality);
    }

    @Override
    public List<TYjyDimensionality>  selectTYjyDimensionalityListIsExit(TYjyDimensionality tYjyDimensionality)
    {
        return tYjyDimensionalityMapper.selectTYjyDimensionalityListIsExit(tYjyDimensionality);
    }
    @Override
    public List<TYjyDimensionality> selectRootList(TYjyDimensionality tYjyDimensionality) {
        return tYjyDimensionalityMapper.selectRootList(tYjyDimensionality);
    }

    //改造接口进行筛选操作
    @Override
    public List<TYjyDimensionality> getALLRootForAnswer(TYjyDimensionality tYjyDimensionality)
    {
        List<TYjyDimensionality> begin=tYjyDimensionalityMapper.getALLRootForAnswer(tYjyDimensionality);
        List<TYjyDimensionality> search=tYjyDimensionalityMapper.getALLRootForAnswerDeal(tYjyDimensionality);
        List<String> pathList=new ArrayList<>();
        for (TYjyDimensionality item:search)
        {
            pathList.add(item.getPath());
        }
        List<TYjyDimensionality> re= new ArrayList<>();
        for (TYjyDimensionality item:begin)
        {
            int num=0;
            for(String strItem:pathList)
            {
                if(strItem.contains(item.getPath()))
                {
                    re.add(item);
                    num=1;
                    break;
                }
            }
            if(num==1)
            {
                continue;
            }
        }
        return re;
    }

    @Override
    public List<TYjyDimensionality> getALLRootFordistribute(TYjyDimensionality tYjyDimensionality)
    {
        List<TYjyDimensionality> begin=tYjyDimensionalityMapper.getALLRootFordistribute(tYjyDimensionality);
        List<TYjyDimensionality> search=tYjyDimensionalityMapper.getALLRootFordistributeDeal(tYjyDimensionality);
        List<String> pathList=new ArrayList<>();
        for (TYjyDimensionality item:search)
        {
            pathList.add(item.getPath());
        }
        List<TYjyDimensionality> re= new ArrayList<>();
        for (TYjyDimensionality item:begin)
        {
            int num=0;
            for(String strItem:pathList)
            {
                if(strItem.contains(item.getPath()))
                {
                    re.add(item);
                    num=1;
                    break;
                }
            }
            if(num==1)
            {
                continue;
            }
        }
        return re;
//        return tYjyDimensionalityMapper.getALLRootFordistribute(tYjyDimensionality);
    }

    @Override
    public List<TYjyDimensionality> getALLparentRootFordistribute(TYjyDimensionality tYjyDimensionality)
    {
        return tYjyDimensionalityMapper.getALLparentRootFordistribute(tYjyDimensionality);
    }


    @Override
    public List<TYjyDimensionality> getALLparentRootFordistributePlus(TYjyDimensionality tYjyDimensionality)
    {
        TYjyFormAnswer tYjyFormAnswer=new TYjyFormAnswer();
        String fcDate=tYjyDimensionality.getFcDate();
        tYjyFormAnswer.setWorkNo(tYjyDimensionality.getCreateBy());
        tYjyFormAnswer.setId(tYjyDimensionality.getId());
        tYjyFormAnswer.setDimensionalityName(tYjyDimensionality.getDimensionalityName());
        tYjyFormAnswer.setDeptCode(tYjyDimensionality.getDeptCode());
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        return tYjyDimensionalityMapper.getALLparentRootFordistributePlus(tYjyFormAnswer);
    }


    @Override
    public List<TYjyDimensionality> getByRootId(TYjyDimensionality tYjyDimensionality) {
        return tYjyDimensionalityMapper.getByRootId(tYjyDimensionality);
    }

    @Override
    public List<TYjyDimensionality> getByRootIdForUser(TYjyDimensionality tYjyDimensionality) {
        return tYjyDimensionalityMapper.getByRootIdForUser(tYjyDimensionality);
    }


    @Override
    public List<TYjyDimensionality> getByRootIdwithParent(TYjyDimensionality tYjyDimensionality) {
        return tYjyDimensionalityMapper.getByRootIdwithParent(tYjyDimensionality);
    }

    @Override
    public List<TYjyDimensionality> getChildremById(TYjyDimensionality tYjyDimensionality) {
        return tYjyDimensionalityMapper.getChildremById(tYjyDimensionality);
    }

    @Override
    public List<TYjyDimensionality> selectRootListWithDept(TYjyDimensionality tYjyDimensionality) {
        return tYjyDimensionalityMapper.selectRootListWithDept(tYjyDimensionality);
    }


    @Override
    public List<TYjyDimensionality> selectRootListWithDeptPlus(TYjyDimensionality tYjyDimensionality) {
        TYjyFormAnswer tYjyFormAnswer=new TYjyFormAnswer();
        String fcDate=tYjyDimensionality.getFcDate();
        tYjyFormAnswer.setWorkNo(tYjyDimensionality.getCreateBy());
        tYjyFormAnswer.setId(tYjyDimensionality.getId());
        tYjyFormAnswer.setDimensionalityName(tYjyDimensionality.getDimensionalityName());
        tYjyFormAnswer.setDeptCode(tYjyDimensionality.getDeptCode());
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        return tYjyDimensionalityMapper.selectRootListWithDeptPlus(tYjyFormAnswer);
    }


    @Override
    public List<TYjyDimensionality> selectRootListWithDeptSuper(TYjyDimensionality tYjyDimensionality) {
        TYjyFormAnswer tYjyFormAnswer=new TYjyFormAnswer();
        String fcDate=tYjyDimensionality.getFcDate();
        tYjyFormAnswer.setWorkNo(tYjyDimensionality.getCreateBy());
        tYjyFormAnswer.setId(tYjyDimensionality.getId());
        tYjyFormAnswer.setDimensionalityName(tYjyDimensionality.getDimensionalityName());
        tYjyFormAnswer.setDeptCode(tYjyDimensionality.getDeptCode());
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        return tYjyDimensionalityMapper.selectRootListWithDeptSuper(tYjyFormAnswer);
    }

    /**
     * 新增TYjyDimensionality
     *
     * @param tYjyDimensionality TYjyDimensionality
     * @return 结果
     */
    @Override
    public int insertTYjyDimensionality(TYjyDimensionality tYjyDimensionality) {
        tYjyDimensionality.setCreateTime(DateUtils.getNowDate());
        return tYjyDimensionalityMapper.insertTYjyDimensionality(tYjyDimensionality);
    }

    /**
     * 修改TYjyDimensionality
     *
     * @param tYjyDimensionality TYjyDimensionality
     * @return 结果
     */
    @Override
    public int updateTYjyDimensionality(TYjyDimensionality tYjyDimensionality) {
        tYjyDimensionality.setUpdateTime(DateUtils.getNowDate());
        return tYjyDimensionalityMapper.updateTYjyDimensionality(tYjyDimensionality);
    }

    /**
     * 批量删除TYjyDimensionality
     *
     * @param ids 需要删除的TYjyDimensionalityID
     * @return 结果
     */
    @Override
    public int deleteTYjyDimensionalityByIds(Long[] ids) {
        return tYjyDimensionalityMapper.deleteTYjyDimensionalityByIds(ids);
    }

    /**
     * 删除TYjyDimensionality信息
     *
     * @param id TYjyDimensionalityID
     * @return 结果
     */
    @Override
    public int deleteTYjyDimensionalityById(Long id) {
        return tYjyDimensionalityMapper.deleteTYjyDimensionalityById(id);
    }

    @Override
    public TYjyDimensionalityRoot getTree(TYjyDimensionalityRoot tYjyDimensionalityRoot, Map<Long, List<TYjyDimensionalityRoot>> map) {
        List<TYjyDimensionalityRoot> list = map.get(tYjyDimensionalityRoot.getValue());
        if (list != null) {
            for (TYjyDimensionalityRoot item : list) {
                item = getTree(item, map);
            }
            tYjyDimensionalityRoot.children = list;
        }
        else
        {
            tYjyDimensionalityRoot.children=null;
        }
        return tYjyDimensionalityRoot;
    }

    @Override
    public TYjyDimensionality getALlTree(TYjyDimensionality tYjyDimensionality, Map<Long, List<TYjyDimensionality>> map) {
        List<TYjyDimensionality> list = map.get(tYjyDimensionality.getId());
        if (list != null) {
            for (TYjyDimensionality item : list) {
                item = getALlTree(item, map);
            }
            tYjyDimensionality.children = list;
        }
        return tYjyDimensionality;
    }


    @Override
    public List<DeptInfoList> getDeptInfoList() {
        return tYjyDimensionalityMapper.getDeptInfoList();
    }


    @Autowired
    private ITYjyAnswerService answerService;

    private static final Logger log = LoggerFactory.getLogger(TYjyDimensionalityServiceImpl.class);

    @Override
    public void export(HttpServletResponse response, String startDate, String endDate, Long rootId,boolean noteShow) throws IOException {

//        TYjyDimensionalityRoot dimensionalityRoot = this.getTreeById(rootId);

//        TYjyDimensionalityRoot dimensionalityRoot = this.getTreeByIdwithParent(rootId);

        TYjyDimensionalityRoot dimensionalityRoot = this.getTreeByIdwithParentPlus(rootId);
        List<TYjyDimensionalityRoot> allLeaves = this.getAllLeaves(dimensionalityRoot);

        List<Long> leafIds = allLeaves.stream().map(x -> x.getValue()).collect(Collectors.toList());
        List<TYjyExport> forms = answerService.selectExportFormList(leafIds);
        List<TYjyExport> exports = answerService.selectExportList(leafIds, startDate, endDate);

        forms.forEach(item ->{
            TYjyDimensionalityRoot rootD = allLeaves.stream().filter(x -> x.getValue().equals(item.getDimensionalityId())).findFirst().orElse(null);
            if(Objects.nonNull(rootD))item.setLabels(rootD.getLabels());
            else item.setLabels(new ArrayList<>());
        });
        exports.forEach(item ->{
            item.setDateDesc(dataReportFrequency.getDateTemp(item.getFrequency(),item.getFcDate()));
            TYjyExport tYjyExport = forms.stream().filter(x -> x.getFormId().equals(item.getFormId())).findFirst().orElse(null);
            if(Objects.nonNull(tYjyExport)) item.setLabels(tYjyExport.getLabels());
            else item.setLabels(new ArrayList<>());
        });
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        ServletOutputStream out = response.getOutputStream();
        XSSFWorkbook wb = new XSSFWorkbook();
        try {
            this.exportExcel(wb, forms,exports,startDate,endDate,false,noteShow);
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
            IOUtils.closeQuietly(out);
        }
    }

    @Override
    public void exportTemplate(HttpServletResponse response, String startDate, String endDate, Long rootId) throws IOException {
        TYjyDimensionalityRoot dimensionalityRoot = this.getTreeByIdwithParent(rootId);
//        TYjyDimensionalityRoot dimensionalityRoot = this.getTreeByIdwithParentPlus(rootId);
        List<TYjyDimensionalityRoot> allLeaves = this.getAllLeaves(dimensionalityRoot);

        TYjyForm tYjyForm =new TYjyForm();
        tYjyForm.setDimensionalityId(rootId);
        tYjyForm.setWorkNo(SecurityUtils.getUsername());
        List<Long> ids = SpringUtils.getBean(TYjyFormMapper.class).selectTYjyFormListForAnswer(tYjyForm).stream().map(x->x.getId()).collect(Collectors.toList());
        List<TYjyExport> forms = answerService.selectExportFormListByFormId(ids);
        List<TYjyExport> exports = answerService.selectExportListByFormId(ids, startDate, endDate);

        forms.forEach(item ->{
            TYjyDimensionalityRoot rootD = allLeaves.stream().filter(x -> x.getValue().equals(item.getDimensionalityId())).findFirst().orElse(null);
            if(Objects.nonNull(rootD))item.setLabels(rootD.getLabels());
            else item.setLabels(new ArrayList<>());
        });
        exports.forEach(item ->{
            item.setDateDesc(dataReportFrequency.getDateTemp(item.getFrequency(),item.getFcDate()));
            TYjyExport tYjyExport = forms.stream().filter(x -> x.getFormId().equals(item.getFormId())).findFirst().orElse(null);
            if(Objects.nonNull(tYjyExport)) item.setLabels(tYjyExport.getLabels());
            else item.setLabels(new ArrayList<>());
        });
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        ServletOutputStream out = response.getOutputStream();
        XSSFWorkbook wb = new XSSFWorkbook();
        try {
            this.exportExcel(wb,forms,exports,startDate,endDate,true,false);
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
            IOUtils.closeQuietly(out);
        }

    }
    public static String processNumberString(String str) {
        if (!isNumeric(str)) {
            return str;
        }

        boolean hasDecimal = str.contains(".");
        if (!hasDecimal) {
            return str;
        }

        String[] parts = str.split("\\.");
        String integerPart = parts[0];
        String decimalPart = parts.length > 1 ? parts[1] : "";

        // 处理整数部分为空的情况，例如 ".5000" → "0.5000"
        if (integerPart.isEmpty()) {
            integerPart = "0";
        }

        // 处理小数部分的末尾零
        String trimmedDecimal = decimalPart.replaceAll("0+$", "");

        if (trimmedDecimal.isEmpty()) {
            // 处理 "-0" 的情况
            if (integerPart.equals("-0")) {
                return "0";
            }
            return integerPart;
        } else {
            return integerPart + "." + trimmedDecimal;
        }
    }

    private static boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        // 处理以点开头或结尾的情况，例如 "123." 或 ".45"
        String s = str;
        // 检查是否以点开头或结尾，但允许这些情况通过解析
        try {
            Double.parseDouble(s);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    @Override
    public Map<String, List<JSONObject>> readExcelBySheetNames(MultipartFile file, Set<String> targetSheets) throws IOException {
        Map<String, List<JSONObject>> result = new HashMap<>();
        try (InputStream is = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(is)) { // 自动识别 .xls/.xlsx[3,7](@ref)
            Map<Long,TYjyForm> dealmap=new HashMap<>();
            // 遍历所有 Sheet
            for (Sheet sheet : workbook) {
                String sheetName = sheet.getSheetName();
                if (!targetSheets.contains(sheetName)) continue;

                List<JSONObject> sheetData = new ArrayList<>();
                Iterator<Row> rowIterator = sheet.iterator();

                // 读取列头
                Row headerRow = rowIterator.next();
                List<String> headers = getHeaders(headerRow);
                int dId = headers.indexOf("单位");
                int yId = headers.indexOf("原因");

                // 遍历数据行
                while (rowIterator.hasNext()) {
                    Row currentRow = rowIterator.next();

                    List<JSONObject> rowData = parseRow(currentRow, headers,dId,yId,sheetName,dealmap);
                    sheetData.addAll(rowData);
                }
                result.put(sheetName, sheetData);
            }
        } catch (IOException | EncryptedDocumentException e) {
            throw new RuntimeException("Excel 解析失败：" + e.getMessage());
        }
        return result;
    }



    private List<String> getHeaders(Row headerRow) {
        List<String> headers = new ArrayList<>();
        Iterator<Cell> cellIterator = headerRow.cellIterator();
        while (cellIterator.hasNext()) {
            Cell cell = cellIterator.next();
            headers.add(cell.getStringCellValue().trim());
        }
        return headers;
    }

    private List<JSONObject> parseRow(Row row, List<String> headers,int unitIndex,int reasonIndex,String sheetName,Map<Long,TYjyForm> dealmap) {
        List<JSONObject> rowData = new ArrayList<>();
        DataFormatter formatter = new DataFormatter(); // 统一格式化工具[6](@ref)
        int index = unitIndex + 1;
        int size = headers.size() - unitIndex - 1;
        if(reasonIndex >= 0) {
            size = size - 4;
            index = index + 2;
        }
        int count=0;
        TYjyForm tYjyForm=null;
        for (int i = 0; i < size; i++) {
//            if(count==0)
//            {
//                tYjyForm=tYjyFormMapper.selectTYjyFormById(Long.valueOf(formatter.formatCellValue(row.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).trim()));
//                count=count+1;
//            }
            Long newid=Long.valueOf(formatter.formatCellValue(row.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).trim());
            if(!dealmap.containsKey(newid))
            {
                tYjyForm=tYjyFormMapper.selectTYjyFormById(Long.valueOf(formatter.formatCellValue(row.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).trim()));
                TYjyForm searchTYjyForm=new TYjyForm();
                searchTYjyForm.setDimensionalityPath(tYjyForm.getDimensionalityPath().split(",")[0]+',');
                List<TYjyForm> formList=tYjyFormMapper.selectTYjyFormList(searchTYjyForm);
                for(TYjyForm item:formList)
                {
                    dealmap.put(item.getId(),item);
                }
            }
            else
            {
                tYjyForm=dealmap.get(newid);
            }
            String value = formatter.formatCellValue(row.getCell(index + i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).trim();
            if(StringUtils.isNotBlank(value)){
                JSONObject item = new JSONObject();
                item.put("formId",formatter.formatCellValue(row.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).trim());
                item.put("fcDate",dataReportFrequency.parsePeriodToStartDate(sheetName,headers.get(index + i)));
                if(tYjyForm.getFormType().equals("1"))
                {
                    String deal=formatter.formatCellValue(row.getCell(index + i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK));
                    if(deal.trim().equals(""))
                    {
                        item.put("formValue",processNumberString(value));
                    }
                    else
                    {
                          //另外一条路线
//                        DataFormatter df = new DataFormatter();
//                        item.put("formValue",getNumericCellValue(row.getCell(index + i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK),df));
                        BigDecimal decimalValue = BigDecimal.valueOf(row.getCell(index + i).getNumericCellValue());
                        item.put("formValue",decimalValue.stripTrailingZeros().toPlainString());
                    }
                }
                else
                {
                    item.put("formValue",processNumberString(value));
                }
                item.put("creatorDept","");
                if(reasonIndex >= 0){
                    item.put("reason",formatter.formatCellValue(row.getCell(reasonIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).trim());
                    item.put("measure",formatter.formatCellValue(row.getCell(reasonIndex + 1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)).trim());
                }
                rowData.add(item);
            }

        }
        return rowData;
    }

    //尝试解决小数进度获取异常的问题，还需要进一步测试有效性
    @Override
    public String getNumericCellValue(Cell cell, DataFormatter df) {
        String valueStr = df.formatCellValue(cell);// 格式化后的数字，比如会保留后面的0
        if (StringUtils.isNotBlank(valueStr)) {
            CellStyle cellStyle = cell.getCellStyle();
            if (cellStyle != null) {
                String formatString = cellStyle.getDataFormatString();
                if ("General".equals(formatString)) {// 常规，则直接返回格式化后的值
                    return valueStr;
                } else {
                    // 如果不是常规，比较准确数字和格式化后的数字小数位数是否一致
                    String valueString = String.valueOf(cell.getNumericCellValue());// 准确的数字
                    if (!valueString.endsWith("0")) {// 如果不是以0结尾，就获取字符串，否则可能不准确
//                        cell.setCellType(Cell.CELL_TYPE_STRING);
                        String valueStringStr = cell.getStringCellValue();// 准确的数字字符串
                        if (!valueString.equals(valueStringStr)) {
//                            logger.warn("数字与字符串不一致：" + valueString + "，" + valueStringStr);
                            valueString = valueStringStr;
                        }
                    }
                    int pointNumString = getDecimalPlace(valueString);// 准确数字的小数位数
                    int pointNumStr = getDecimalPlace(valueStr);// 格式化后数字的小数位数
                    // 如果一致，或者格式化后的数字小数位数比准确数字多，则直接返回格式化后的值
                    if (pointNumString <= pointNumStr) {
                        return valueStr;
                    } else {// 否则设置保留的小数位数
                        return new BigDecimal(valueString).setScale(pointNumStr, BigDecimal.ROUND_HALF_UP).toPlainString();
                    }
                }
            }
        }
        return valueStr;
    }

    public static int getDecimalPlace(String str) {
        if (StringUtils.isEmpty(str)) {
            return 0;
        }
        int index = str.indexOf(".");
        if (index == -1 || index == str.length() - 1) {
            return 0;
        }
        return str.length() - 1 - index;
    }


    private void exportExcel(XSSFWorkbook workbook, List<TYjyExport> forms, List<TYjyExport> exports,String startDate,String endDate,boolean isId,boolean noteShow) throws Exception{

        // 1. 公共数据预处理
        List<TYjyExport> reasonsList = exports.stream()
                .filter(this::isReason)
                .collect(Collectors.toList());

        // 2. 分频率处理（参考网页5的模板化思想）
        processFrequencySheets(workbook, forms, exports, startDate, endDate,isId,noteShow);

        // 3. 原因措施处理（复用主逻辑）
        if (!reasonsList.isEmpty()) {
            processReasonSheet(workbook, reasonsList, forms);
        }
    }


    private void processFrequencySheets(XSSFWorkbook workbook, List<TYjyExport> forms,
                                        List<TYjyExport> exports, String startDate, String endDate,boolean isId,boolean noteShow) {
        List<String> frequencies = forms.stream()
                .map(TYjyExport::getFrequency)
                .distinct().collect(Collectors.toList());

        frequencies.forEach(frequency -> {
            XSSFSheet sheet = workbook.createSheet(dataReportFrequency.getByCode(frequency).getInfo() + "填报");
            List<TYjyExport> filteredData = filterByFrequency(exports, frequency);
            List<TYjyExport> formData = filterByFrequency(forms, frequency);

            // 维度处理
            int maxDim = getMaxDimensionLength(formData);
            padDimensionList(formData, maxDim);
            mergeDimensionCells(sheet, formData, maxDim ,isId?1:0);
            boolean isHave = isHaveMaxMin(formData);
            List<String> headers = getHeaderTitles(frequency,maxDim, startDate, endDate,isHave,isId);
            //不需要展示当前note变化历史
            if(noteShow==false)
            {

                createHeaderRow(sheet, headers);
                // 动态字段填充
                fillDynamicFields(sheet, filteredData, formData, headers,maxDim,dataReportFrequency.getPeriodDateList(frequency, startDate, endDate),isHave,isId);
            }
            else
            {
                Map<Long,Map<String,String>> noteMap=new HashedMap();
                List<String> timeList=new ArrayList<>();
                for(TYjyExport item:forms)
                {
                    if(item.getNoteList()==null)
                    {
                        noteMap.put(item.getFormId(),new HashedMap());
                    }
                    else
                    {
                        Map insertMap=new HashedMap();
                        List<JSONObject> jsonList=JSONArray.parseArray(item.getNoteList(), JSONObject.class);
                        for(JSONObject jsonItem:jsonList)
                        {
                            insertMap.put(jsonItem.get("date").toString(),jsonItem.get("formNote").toString());
                            if(!timeList.contains(jsonItem.get("date").toString()))
                            {
                                timeList.add(jsonItem.get("date").toString());
                            }
                        }
                        noteMap.put(item.getFormId(),insertMap);
                    }
                }
                timeList.sort(null);
                completeNoteMap(timeList,noteMap);
                //此处需要基于行的情况进行改造
                createHeaderRowWithNote(sheet, headers,timeList);

                //此处需要基于行的情况进行改造
                // 动态字段填充
                fillDynamicFieldsWithNote(sheet, filteredData, formData, headers,maxDim,dataReportFrequency.getPeriodDateList(frequency, startDate, endDate),isHave,isId,timeList,noteMap);
            }

        });
    }
    private boolean isReason(TYjyExport export) {
        // 获取最小值、最大值和表单值
        Double minimum = export.getMinimum();
        Double maximum = export.getMaximum();
        String formValue = export.getFormValue();

        // 如果最小值或最大值不为空，且表单值不为空
        if ((minimum != null || maximum != null) && StringUtils.isNotBlank(formValue)) {
            try {
                double number = Double.parseDouble(formValue);
                // 检查是否超出范围
                return (minimum != null && number < minimum) || (maximum != null && number > maximum);
            } catch (NumberFormatException e) {
                // 如果表单值无法解析为数字，返回 false
                return false;
            }
        }
        return false;
    }
    private boolean isHaveMaxMin(List<TYjyExport> forms) {
        if(forms.stream().filter(x->Objects.nonNull(x.getMaximum())||Objects.nonNull(x.getMinimum())).collect(Collectors.toList()).size()>0) return true;
        return false;
    }
    // 通用维度列合并方法（适用于主表和原因措施表）
    private void mergeDimensionCells(XSSFSheet sheet, List<? extends TYjyExport> dataList, int maxLength,int colIndex) {
        if (dataList == null || dataList.isEmpty()) return;
        if (dataList.get(0).getLabels().size() < maxLength) return;

        for (int col = 0; col < maxLength; col++) {
            int rowStart = 0;
            String prevValue = dataList.get(0).getLabels().get(col);

            List<CellRangeAddress> mergeRegions = new ArrayList<>(); // 预存合并区域

            for (int i = 1; i < dataList.size(); i++) {
                String currentValue = dataList.get(i).getLabels().get(col);
                if (!prevValue.equals(currentValue)) {
                    if (rowStart < i - 1) {
                        mergeRegions.add(new CellRangeAddress(rowStart + 1, i, col + colIndex, col + colIndex));
                    }
                    prevValue = currentValue;
                    rowStart = i;
                }
            }

            // 处理最后一组连续区域
            if (rowStart < dataList.size() - 1) {
                mergeRegions.add(new CellRangeAddress(rowStart + 1, dataList.size(), col + colIndex, col + colIndex));
            }
            // 批量合并（减少锁竞争）
            mergeRegions.forEach(sheet::addMergedRegion);
        }
    }

    // 单周期导出 合并维度
    private void mergeAlone(XSSFSheet sheet, List<List<String>> dataList, int maxLength,int colIndex,int beginRow) {
        if (dataList == null || dataList.isEmpty() || dataList.size()<2) return;
        List<CellRangeAddress> mergeRegions = new ArrayList<>(); // 预存合并区域
        int num=0;
        for(int col = 0; col < colIndex; col++)
        {
            for(int row = 0; row < maxLength-1; row++)
            {
                String prevValue=dataList.get(row).get(col);
                if(prevValue==null || prevValue.equals(""))
                {
                    continue;
                }
                num=1;
                while(dataList.get(row).get(col).equals(dataList.get(row+num).get(col)))
                {
                    if(col==0)
                    {
                        num=num+1;
                        if(row+num==maxLength)
                        {
                            break;
                        }
                    }
                    else
                    {
                        int deepcol=col-1;
                        int isSame=0;
                        while(deepcol>=0)
                        {
                            if(!dataList.get(row).get(deepcol).equals(dataList.get(row+num).get(deepcol)))
                            {
                                isSame=1;
                                break;
                            }
                            deepcol=deepcol-1;
                        }
                        if(isSame==0)
                        {
                            num=num+1;
                            if(row+num==maxLength)
                            {
                                break;
                            }
                        }
                        else
                        {
                             break;
                        }
                    }
                }
                if(num>1)
                {
                    mergeRegions.add(new CellRangeAddress(row+beginRow, row+beginRow+num-1, col, col));
                    row=row+num-1;
                }
            }

        }
        mergeRegions.forEach(sheet::addMergedRegion);
    }

    // 单周期导出 合并维度
    private void mergeOne(XSSFSheet sheet,  int row0,int row1,int col0,int col1) {

        List<CellRangeAddress> mergeRegions = new ArrayList<>(); // 预存合并区域
        mergeRegions.add(new CellRangeAddress(row0, row1, col0, col1));
        mergeRegions.forEach(sheet::addMergedRegion);
    }

    private void mergeHen(XSSFSheet sheet, int mouth,int length,int begincol,int beginRow)
    {
        List<CellRangeAddress> mergeRegions = new ArrayList<>(); // 预存合并区域
        for(int i=0;i<mouth;i++)
        {
            mergeRegions.add(new CellRangeAddress(beginRow, beginRow, begincol+i*length, begincol+i*length+length-1));
        }
        mergeRegions.forEach(sheet::addMergedRegion);
    }
    // 获取最大维度长度
    private int getMaxDimensionLength(Collection<TYjyExport> dataList) {
        return dataList.stream()
                .mapToInt(obj -> obj.getLabels().size())
                .max()
                .orElse(0);
    }

    // 生成维度标题列表
    private List<String> generateDimensionHeaders(int maxLength) {
        return IntStream.rangeClosed(1, maxLength)
                .mapToObj(i -> "维度" + i)
                .collect(Collectors.toList());
    }
    public static List<TYjyExport> filterByFrequency(List<TYjyExport> dataList, String targetFrequency) {
        return dataList.stream()
                .filter(item -> targetFrequency.equalsIgnoreCase(item.getFrequency()))
                .collect(Collectors.toList());
    }

    // 补全维度列表空值（参考网页6的代码拆分原则）
    private void padDimensionList(List<TYjyExport> dataList, int targetLength) {
        dataList.forEach(item -> {
            List<String> labels = item.getLabels();
            labels.addAll(Collections.nCopies(targetLength - labels.size(), ""));
        });
    }

    private List<String> getHeaderTitles(String frequency,int maxLength, String startDate, String endDate,boolean isHave,boolean isId){
        List<String> res = new ArrayList<>();
        if(isId) res.add("id");
        res.addAll(generateDimensionHeaders(maxLength));
        res.addAll(Arrays.asList("问题", "目标", "单位"));
        if(isHave) res.addAll(Arrays.asList("最小值", "最大值"));
        List<String> periodDateList = dataReportFrequency.getPeriodDateList(frequency, startDate, endDate);//获取时间的逻辑
        res.addAll(periodDateList);
        if(isHave && periodDateList.size() <= 1){
            res.addAll(Arrays.asList("原因", "措施"));
        }
        return res;
    }

    private List<String> getHeaderTitlesWithConnect(String frequency, String startDate, String endDate){
        List<String> res = new ArrayList<>();
        List<String> periodDateList = dataReportFrequency.getPeriodDateListByQcy(frequency, startDate, endDate);//获取时间的逻辑
        res.addAll(periodDateList);
        return res;
    }

    private void completeNoteMap(List<String> timeList,Map<Long,Map<String,String>> noteMap)
    {
        for(Long item:noteMap.keySet())
        {
            List<String> haslist = noteMap.get(item).keySet().stream().collect(Collectors.toList());
            for(String date:timeList)
            {
                haslist.sort(null);
                //如果没有前序指标，则添加空指标，有前序指标则拷贝前序指标
                if(noteMap.get(item).get(date)==null)
                {
                     if(haslist.get(0).compareTo(date)>0)
                     {
                         noteMap.get(item).put(date,"");
                         haslist.add(date);
                     }
                     else
                     {
                         int dateNum=0;
                         while(dateNum<noteMap.get(item).size()-1)
                         {
                             if(haslist.get(dateNum+1).compareTo(date)>0)
                             {
                                 break;
                             }
                             dateNum=dateNum+1;
                         }
                         noteMap.get(item).put(date,noteMap.get(item).get(haslist.get(dateNum+1)));
                         haslist.add(date);
                     }
                }
            }
        }
    }

    // 创建带合并样式的表头行
    private void createHeaderRow(XSSFSheet sheet, List<String> headers ) {
        XSSFCellStyle headerStyle = createHeaderStyle(sheet.getWorkbook());
        XSSFRow headerRow = sheet.createRow(0);

        // 填充基础表头
        IntStream.range(0, headers.size()).forEach(i -> {
            XSSFCell cell = headerRow.createCell(i);
            cell.setCellValue(headers.get(i));
            cell.setCellStyle(headerStyle);
        });
        for (int i = 0; i < headers.size(); i++) {
            if(headers.get(i).contains("维度")||headers.get(i).contains("原因")||headers.get(i).contains("措施")||headers.get(i).contains("问题")) sheet.setColumnWidth(i, 30 * 256);
            else sheet.setColumnWidth(i,  10 * 256);
        }
    }

    private void createHeaderRowWithNote(XSSFSheet sheet, List<String> headers,List<String> timeList) {
        XSSFCellStyle headerStyle = createHeaderStyle(sheet.getWorkbook());
        XSSFRow headerRow = sheet.createRow(0);

        // 填充基础表头
        int j=0;
        for(int i=0;i< headers.size();i++)
        {
            if(j<timeList.size())
            {
                if(timeList.get(j).equals(headers.get(i)))
                {
                    XSSFCell cell = headerRow.createCell(i+j);
                    cell.setCellValue("指标修改");
                    cell.setCellStyle(headerStyle);
                    j=j+1;
                }
            }

            XSSFCell cell = headerRow.createCell(i+j);
            cell.setCellValue(headers.get(i));
            cell.setCellStyle(headerStyle);
        }
//        IntStream.range(0, headers.size()).forEach(i -> {
//            XSSFCell cell = headerRow.createCell(i);
//            cell.setCellValue(headers.get(i));
//            cell.setCellStyle(headerStyle);
//        });
        for (int i = 0; i < headers.size(); i++) {
            if(headers.get(i).contains("维度")||headers.get(i).contains("原因")||headers.get(i).contains("措施")||headers.get(i).contains("问题")) sheet.setColumnWidth(i, 30 * 256);
            else sheet.setColumnWidth(i,  10 * 256);
        }
        for (int i = headers.size(); i < headers.size()+j; i++) {
            sheet.setColumnWidth(i,  10 * 256);
        }
    }

    // 表头样式创建
    private XSSFCellStyle createHeaderStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.MEDIUM);
        return style;
    }
    private XSSFCellStyle createDataStyle(XSSFWorkbook workbook) {
        // 创建基础单元格样式
        XSSFCellStyle style = workbook.createCellStyle();

        // 字体配置（网页6的字体设置最佳实践）
        XSSFFont font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 12);
        font.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(font);

        // 对齐方式（网页7推荐的通用数据对齐方案）
        style.setAlignment(HorizontalAlignment.CENTER);    // 水平居中
        style.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

        // 边框设置（网页8的边框优化方案）
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());

        // 数据格式（网页7的日期/数值处理规范）
        style.setDataFormat(
                workbook.createDataFormat().getFormat("0.00")
        );
        //支持自动换行
        style.setWrapText(true);

        style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    // 动态填充时间序列数据
    private void fillDynamicFields(XSSFSheet sheet, List<TYjyExport> data,
                                   List<TYjyExport> formData, List<String> headers,int maxDin,List<String> periodList,boolean isHave,boolean isId) {
        XSSFCellStyle dataStyle = createDataStyle(sheet.getWorkbook());
        // 逐行填充数据
        IntStream.range(0, formData.size()).forEach(rowIdx -> {
            XSSFRow row = sheet.createRow(rowIdx + 1); // 跳过表头行
            TYjyExport form = formData.get(rowIdx);
            int currentCol = 0;
            if(isId)  setCellValue(row, currentCol++, form.getFormId().toString(), dataStyle);
            for(int i=0;i<maxDin;i++){
                setCellValue(row, currentCol++, form.getLabels().get(i), dataStyle);
            }
            // 固定字段填充
            setCellValue(row, currentCol++, Objects.nonNull(form.getQuestion())?form.getQuestion().toString():"", dataStyle);
            setCellValueWithType(row, currentCol++, Objects.nonNull(form.getNote())?form.getNote().toString():"", dataStyle);
            setCellValue(row, currentCol++, Objects.nonNull(form.getUnit())?form.getUnit().toString():"", dataStyle);
            if(isHave){
                setCellValue(row, currentCol++, Objects.nonNull(form.getMinimum())?form.getMinimum().toString():"", dataStyle);
                setCellValue(row, currentCol++, Objects.nonNull(form.getMaximum())?form.getMaximum().toString():"", dataStyle);
            }
            //动态字段从这里开始考虑，此处就是修改填充的的地方
            for(String period:periodList){
                // 动态时间字段填充
                TYjyExport yjyExport = data.stream().filter(export -> (export.getFormId().equals(form.getFormId()) && export.getDateDesc().equals(period))).findFirst().orElse(null);
                setCellValueWithType(row, currentCol++, Objects.nonNull(yjyExport)?yjyExport.getFormValue():"", dataStyle);
            }
            if(isHave && periodList.size() <= 1){
                setCellValue(row, currentCol++, Objects.nonNull(form.getReason())?form.getReason().toString():"", dataStyle);
                setCellValue(row, currentCol++, Objects.nonNull(form.getMeasure())?form.getMeasure().toString():"", dataStyle);
            }
        });
    }

    // 动态填充时间序列数据
    private void fillDynamicFieldsWithNote(XSSFSheet sheet, List<TYjyExport> data,
                                   List<TYjyExport> formData, List<String> headers,int maxDin,List<String> periodList,boolean isHave,boolean isId,List<String> timeList,Map<Long,Map<String,String>> noteMap) {
        XSSFCellStyle dataStyle = createDataStyle(sheet.getWorkbook());
        // 逐行填充数据
        IntStream.range(0, formData.size()).forEach(rowIdx -> {
            XSSFRow row = sheet.createRow(rowIdx + 1); // 跳过表头行
            TYjyExport form = formData.get(rowIdx);
            int currentCol = 0;
            if(isId)  setCellValue(row, currentCol++, form.getFormId().toString(), dataStyle);
            for(int i=0;i<maxDin;i++){
                setCellValue(row, currentCol++, form.getLabels().get(i), dataStyle);
            }
            // 固定字段填充
            setCellValue(row, currentCol++, Objects.nonNull(form.getQuestion())?form.getQuestion().toString():"", dataStyle);
            setCellValueWithType(row, currentCol++, Objects.nonNull(form.getNote())?form.getNote().toString():"", dataStyle);
            setCellValue(row, currentCol++, Objects.nonNull(form.getUnit())?form.getUnit().toString():"", dataStyle);
            if(isHave){
                setCellValue(row, currentCol++, Objects.nonNull(form.getMinimum())?form.getMinimum().toString():"", dataStyle);
                setCellValue(row, currentCol++, Objects.nonNull(form.getMaximum())?form.getMaximum().toString():"", dataStyle);
            }
            //动态字段从这里开始考虑，此处就是修改填充的的地方
            int j=0;
            for(int i=0;i<periodList.size();i++)
            {   String period =periodList.get(i);
                TYjyExport yjyExport = data.stream().filter(export -> (export.getFormId().equals(form.getFormId()) && export.getDateDesc().equals(period))).findFirst().orElse(null);
                if(j<timeList.size())
                {
                    if(periodList.get(i).equals(timeList.get(j)))
                    {
                        setCellValueWithType(row, currentCol+j, noteMap.get(form.getFormId()).get(timeList.get(j)), dataStyle);
                        j=j+1;
                    }
                }
                setCellValueWithType(row, currentCol+j, Objects.nonNull(yjyExport)?yjyExport.getFormValue():"", dataStyle);
                currentCol=currentCol+1;
            }
//            for(String period:periodList){
//                // 动态时间字段填充
//                TYjyExport yjyExport = data.stream().filter(export -> (export.getFormId().equals(form.getFormId()) && export.getDateDesc().equals(period))).findFirst().orElse(null);
//                setCellValue(row, currentCol++, Objects.nonNull(yjyExport)?yjyExport.getFormValue():"", dataStyle);
//            }
            currentCol=currentCol+j;
            if(isHave && periodList.size() <= 1){
                setCellValue(row, currentCol++, Objects.nonNull(form.getReason())?form.getReason().toString():"", dataStyle);
                setCellValue(row, currentCol++, Objects.nonNull(form.getMeasure())?form.getMeasure().toString():"", dataStyle);
            }
        });
    }


    // 单元格值设置辅助方法
    private void setCellValue(XSSFRow row, int col, String value, XSSFCellStyle style) {
        XSSFCell cell = row.getCell(col, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
        cell.setCellValue(value);
        cell.setCellStyle(style);
    }

    // 单元格值设置辅助方法
    private void setCellValueWithType(XSSFRow row, int col, String value, XSSFCellStyle style) {
        XSSFCell cell = row.getCell(col, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
        if (value != null && value.matches("^[+-]?(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?$"))
        {
            // 检查是否全为数字
            double numericValue = Double.parseDouble(value); // 转换为数字
            cell.setCellValue(numericValue); // 设置新的数值
            cell.setCellType(CellType.NUMERIC); // 确保类型为数值
        }
        else
        {
            cell.setCellValue(value);
        }

        cell.setCellStyle(style);
    }

    // 处理原因措施专用工作表
    private void processReasonSheet(XSSFWorkbook workbook, List<TYjyExport> reasonsList,
                                    List<TYjyExport> forms) {
        XSSFSheet sheet = workbook.createSheet("原因措施");


        // 维度处理（复用主表逻辑）
        int maxDim = getMaxDimensionLength(reasonsList);
        padDimensionList(reasonsList, maxDim);
        mergeDimensionCells(sheet, reasonsList, maxDim, 0);

        // 生成专用表头
        List<String> reasonHeaders = generateReasonHeaders(maxDim);
        createHeaderRow(sheet, reasonHeaders);

        // 填充原因措施数据
        fillReasonData(sheet, reasonsList, maxDim);
    }

    // 生成原因措施专用表头
    private List<String> generateReasonHeaders(int dimensionCount) {
        List<String> headers = new ArrayList<>();
        headers.addAll(generateDimensionHeaders(dimensionCount));
        headers.addAll(Arrays.asList("问题", "目标", "单位", "填报时间", "最小值", "最大值", "异常值", "原因", "措施"));
        return headers;
    }

    // 原因数据填充
    // 原因数据填充
    private void fillReasonData(XSSFSheet sheet, List<TYjyExport> reasons, int dimCount) {
        XSSFCellStyle dataStyle = createDataStyle(sheet.getWorkbook());

        IntStream.range(0, reasons.size()).forEach(rowIdx -> {
            TYjyExport item = reasons.get(rowIdx);
            XSSFRow row = sheet.createRow(rowIdx + 1); // 跳过表头

            // 维度列填充
            IntStream.range(0, dimCount).forEach(col ->
                    setCellValue(row, col, Objects.nonNull(item.getLabels().get(col))?item.getLabels().get(col):"", dataStyle));

            // 专用字段填充
            setCellValue(row, dimCount, Objects.nonNull(item.getQuestion())?item.getQuestion():"", dataStyle);
            setCellValue(row, dimCount+1,  Objects.nonNull(item.getNote())?item.getNote():"", dataStyle);
            setCellValue(row, dimCount+2,  Objects.nonNull(item.getUnit())?item.getUnit():"", dataStyle);
            setCellValue(row, dimCount+3,  Objects.nonNull(item.getDateDesc())?item.getDateDesc():"", dataStyle);
            setCellValue(row, dimCount+4,  Objects.nonNull(item.getMinimum())?item.getMinimum().toString():"", dataStyle);
            setCellValue(row, dimCount+5,  Objects.nonNull(item.getMaximum())?item.getMaximum().toString():"", dataStyle);
            setCellValueWithType(row, dimCount+6,  Objects.nonNull(item.getFormValue())?item.getFormValue():"", dataStyle);
            setCellValue(row, dimCount+7,  Objects.nonNull(item.getReason())?item.getReason():"", dataStyle);
            setCellValue(row, dimCount+8,  Objects.nonNull(item.getMeasure())?item.getMeasure():"", dataStyle);
        });
    }

    // 范围格式化辅助方法
    private String formatRange(TYjyExport item) {
        return String.format("%.2f ~ %.2f",
                item.getMinimum() != null ? item.getMinimum() : Double.MIN_VALUE,
                item.getMaximum() != null ? item.getMaximum() : Double.MAX_VALUE);
    }

    public TYjyDimensionalityRoot getTreeById(Long id) {
        TYjyDimensionality tYjyDimensionality = this.selectTYjyDimensionalityById(id);
        if (Objects.isNull(tYjyDimensionality)) return new TYjyDimensionalityRoot();
        List<TYjyDimensionality> list = this.getByRootId(tYjyDimensionality);
        TYjyDimensionalityRoot root = new TYjyDimensionalityRoot();
        root.setValue(tYjyDimensionality.getId());
        root.setLabel(tYjyDimensionality.getDimensionalityName());
        root.setIsUse(tYjyDimensionality.getIsUse());
        Map<Long, List<TYjyDimensionalityRoot>> map = new HashMap<>();
        for (TYjyDimensionality item : list) {
            TYjyDimensionalityRoot add = new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            if (map.containsKey(item.getParentId())) {
                map.get(item.getParentId()).add(add);
            } else {
                List<TYjyDimensionalityRoot> insert = new ArrayList<>();
                insert.add(add);
                map.put(item.getParentId(), insert);
            }
        }
        return this.getTree(root, map);
    }


    public TYjyDimensionalityRoot getTreeByIdwithParent(Long id) {
        TYjyDimensionality tYjyDimensionality = this.selectTYjyDimensionalityById(id);
        if (Objects.isNull(tYjyDimensionality)) return new TYjyDimensionalityRoot();
        List<TYjyDimensionality> list = this.getByRootIdwithParent(tYjyDimensionality);
        tYjyDimensionality = this.selectTYjyDimensionalityById(Long.valueOf(tYjyDimensionality.getPath().split(",")[0]));
        TYjyDimensionalityRoot root = new TYjyDimensionalityRoot();
        root.setValue(tYjyDimensionality.getId());
        root.setLabel(tYjyDimensionality.getDimensionalityName());
        root.setIsUse(tYjyDimensionality.getIsUse());
        Map<Long, List<TYjyDimensionalityRoot>> map = new HashMap<>();
        for (TYjyDimensionality item : list) {
            TYjyDimensionalityRoot add = new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            if (map.containsKey(item.getParentId())) {
                map.get(item.getParentId()).add(add);
            } else {
                List<TYjyDimensionalityRoot> insert = new ArrayList<>();
                insert.add(add);
                map.put(item.getParentId(), insert);
            }
        }
        return this.getTree(root, map);
    }

    public TYjyDimensionalityRoot getTreeByIdwithParentPlus(Long id) {
        TYjyDimensionality tYjyDimensionality = this.selectTYjyDimensionalityById(id);
        tYjyDimensionality = this.selectTYjyDimensionalityById(Long.valueOf(tYjyDimensionality.getPath().split(",")[0]));
        if (Objects.isNull(tYjyDimensionality)) return new TYjyDimensionalityRoot();
        tYjyDimensionality.setCreateBy(SecurityUtils.getUsername());
        List<TYjyDimensionality> list = this.getByRootIdForUser(tYjyDimensionality);
        TYjyDimensionalityRoot root = new TYjyDimensionalityRoot();
        root.setValue(tYjyDimensionality.getId());
        root.setLabel(tYjyDimensionality.getDimensionalityName());
        root.setIsUse(tYjyDimensionality.getIsUse());
        Map<Long, List<TYjyDimensionalityRoot>> map = new HashMap<>();
        for (TYjyDimensionality item : list) {
            TYjyDimensionalityRoot add = new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            if (map.containsKey(item.getParentId())) {
                map.get(item.getParentId()).add(add);
            } else {
                List<TYjyDimensionalityRoot> insert = new ArrayList<>();
                insert.add(add);
                map.put(item.getParentId(), insert);
            }
        }
        return this.getTree(root, map);
    }
    //        TYjyDimensionality tYjyDimensionality=tYjyDimensionalityService.selectTYjyDimensionalityById(id);
//        tYjyDimensionality.setCreateBy(SecurityUtils.getUsername());
//        List<TYjyDimensionality> list = tYjyDimensionalityService.getByRootIdForUser(tYjyDimensionality);
//        TYjyDimensionalityRoot root=new TYjyDimensionalityRoot();
//        root.setValue(tYjyDimensionality.getId());
//        root.setLabel(tYjyDimensionality.getDimensionalityName());
//        root.setIsUse(tYjyDimensionality.getIsUse());
//        root.setRuleType(ruleType);
//        Map<Long,List<TYjyDimensionalityRoot>> map=new HashMap<>();
//        for(TYjyDimensionality item:list)
//        {
//            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
//            add.setValue(item.getId());
//            add.setLabel(item.getDimensionalityName());
//            add.setIsUse(item.getIsUse());
//            add.setRuleType(item.getRuleType());
//            if(map.containsKey(item.getParentId()))
//            {
//                map.get(item.getParentId()).add(add);
//            }
//            else
//            {
//                List<TYjyDimensionalityRoot> insert=new ArrayList<>();
//                insert.add(add);
//                map.put(item.getParentId(),insert);
//            }
//        }

    public List<TYjyDimensionalityRoot> getAllLeaves(TYjyDimensionalityRoot root) {
        if (root == null) {
            return new ArrayList<>();
        }
        List<TYjyDimensionalityRoot> leaves = new ArrayList<>();
        traverseAndSetLabels(root, new ArrayList<>(), leaves);
        return leaves;
    }

    private static void traverseAndSetLabels(TYjyDimensionalityRoot node, List<String> ancestorLabels, List<TYjyDimensionalityRoot> leaves) {
        if (node == null) {
            return;
        }

        // 将当前节点的label添加到ancestorLabels中
        List<String> currentLabels = new ArrayList<>(ancestorLabels);
        currentLabels.add(node.getLabel());

        // 设置当前节点的labels
        node.setLabels(currentLabels);

        // 如果是叶子节点，添加到leaves列表中
        if (Objects.isNull(node.getChildren())||node.getChildren().isEmpty()) {
            leaves.add(node);
        }
        if(Objects.nonNull(node.getChildren())){
            // 递归遍历子节点
            for (TYjyDimensionalityRoot child : node.getChildren()) {
                traverseAndSetLabels(child, currentLabels, leaves);
            }
        }

    }

    @Override
    public List<JSONObject> dealLsitWithStatus(List<TYjyDimensionality> list,String fcDate)
    {
        List<JSONObject> array=new ArrayList<>();
        for(TYjyDimensionality item:list)
        {
            JSONObject jsonResult=new JSONObject();
            jsonResult.put("id",item.getId());
            jsonResult.put("dimensionalityName",item.getDimensionalityName());
            jsonResult.put("path",item.getPath());
            jsonResult.put("deptCode",item.getDeptCode());
            jsonResult.put("deptName",item.getDeptName());
            jsonResult.put("shouldCount",0);//每日填报
            jsonResult.put("count",0);//每日填报
            jsonResult.put("ruleType",item.getRuleType());//每日填报
            dealJSONObject(item,jsonResult,null,fcDate);
//            List<JSONObject> add=new ArrayList<>();
//            //此处可以考虑优化，但是暂时先这样凑活着吧
//            add.add(dealJSONObject(item,jsonResult,"0",fcDate));
//            add.add(dealJSONObject(item,jsonResult,"1",fcDate));
//            add.add(dealJSONObject(item,jsonResult,"2",fcDate));
//            add.add(dealJSONObject(item,jsonResult,"3",fcDate));
//            add.add(dealJSONObject(item,jsonResult,"4",fcDate));
//            add.add(dealJSONObject(item,jsonResult,"5",fcDate));
//            jsonResult.put("countList",add);
            jsonResult.put("notCount",Integer.valueOf(jsonResult.get("shouldCount").toString())-Integer.valueOf(jsonResult.get("count").toString()));//每日填报
            if(jsonResult.get("shouldCount").toString().equals("0"))
            {
                jsonResult.put("countRate",Float.valueOf("0"));
            }
            else
            {
                BigDecimal decimal = new BigDecimal((100*(Float.valueOf(jsonResult.get("count").toString())/Float.valueOf(jsonResult.get("shouldCount").toString()))));
                BigDecimal roundedDecimal = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
                jsonResult.put("countRate",Float.valueOf(roundedDecimal.floatValue()).toString()+"%");
            }
//            dealJSONObject(item,jsonResult,"1",fcDate);
//            dealJSONObject(item,jsonResult,"2",fcDate);
//            dealJSONObject(item,jsonResult,"3",fcDate);
//            dealJSONObject(item,jsonResult,"4",fcDate);
//            dealJSONObject(item,jsonResult,"5",fcDate);
            array.add(jsonResult);
        }
        return array;
    }
    @Override
    public List<JSONObject> dealLsitWithStatusPlus(List<TYjyDimensionality> list,String fcDate)
    {

        List<JSONObject> array=new ArrayList<>();
        for(TYjyDimensionality item:list)
        {
            JSONObject jsonResult=new JSONObject();
            jsonResult.put("id",item.getId());
            jsonResult.put("dimensionalityName",item.getDimensionalityName());
            jsonResult.put("path",item.getPath());
            jsonResult.put("deptCode",item.getDeptCode());
            jsonResult.put("deptName",item.getDeptName());
            jsonResult.put("shouldCount",item.getSumNum());//每日填报
            jsonResult.put("count",item.getFinishNum());//每日填报
            jsonResult.put("ruleType",item.getRuleType());//每日填报

//            dealJSONObject(item,jsonResult,null,fcDate);//取消了查询

//            List<JSONObject> add=new ArrayList<>();
//            //此处可以考虑优化，但是暂时先这样凑活着吧
//            add.add(dealJSONObject(item,jsonResult,"0",fcDate));
//            add.add(dealJSONObject(item,jsonResult,"1",fcDate));
//            add.add(dealJSONObject(item,jsonResult,"2",fcDate));
//            add.add(dealJSONObject(item,jsonResult,"3",fcDate));
//            add.add(dealJSONObject(item,jsonResult,"4",fcDate));
//            add.add(dealJSONObject(item,jsonResult,"5",fcDate));
//            jsonResult.put("countList",add);
            jsonResult.put("notCount",Integer.valueOf(jsonResult.get("shouldCount").toString())-Integer.valueOf(jsonResult.get("count").toString()));//每日填报
            if(jsonResult.get("shouldCount").toString().equals("0"))
            {
                jsonResult.put("countRate",Float.valueOf("0"));
            }
            else
            {
                BigDecimal decimal = new BigDecimal((100*(Float.valueOf(jsonResult.get("count").toString())/Float.valueOf(jsonResult.get("shouldCount").toString()))));
                BigDecimal roundedDecimal = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
                jsonResult.put("countRate",Float.valueOf(roundedDecimal.floatValue()).toString()+"%");
            }
//            dealJSONObject(item,jsonResult,"1",fcDate);
//            dealJSONObject(item,jsonResult,"2",fcDate);
//            dealJSONObject(item,jsonResult,"3",fcDate);
//            dealJSONObject(item,jsonResult,"4",fcDate);
//            dealJSONObject(item,jsonResult,"5",fcDate);
            array.add(jsonResult);
        }
        return array;

//        List<JSONObject> array=new ArrayList<>();
//        for(TYjyDimensionality item:list)
//        {
//            JSONObject jsonResult=new JSONObject();
//            jsonResult.put("id",item.getId());
//            jsonResult.put("dimensionalityName",item.getDimensionalityName());
//            jsonResult.put("path",item.getPath());
//            jsonResult.put("deptCode",item.getDeptCode());
//            jsonResult.put("deptName",item.getDeptName());
//            jsonResult.put("shouldCount",0);//每日填报
//            jsonResult.put("count",0);//每日填报
//            jsonResult.put("ruleType",item.getRuleType());//每日填报
//            dealJSONObject(item,jsonResult,null,fcDate);
////            List<JSONObject> add=new ArrayList<>();
////            //此处可以考虑优化，但是暂时先这样凑活着吧
////            add.add(dealJSONObject(item,jsonResult,"0",fcDate));
////            add.add(dealJSONObject(item,jsonResult,"1",fcDate));
////            add.add(dealJSONObject(item,jsonResult,"2",fcDate));
////            add.add(dealJSONObject(item,jsonResult,"3",fcDate));
////            add.add(dealJSONObject(item,jsonResult,"4",fcDate));
////            add.add(dealJSONObject(item,jsonResult,"5",fcDate));
////            jsonResult.put("countList",add);
//            jsonResult.put("notCount",Integer.valueOf(jsonResult.get("shouldCount").toString())-Integer.valueOf(jsonResult.get("count").toString()));//每日填报
//            if(jsonResult.get("shouldCount").toString().equals("0"))
//            {
//                jsonResult.put("countRate",Float.valueOf("0"));
//            }
//            else
//            {
//                BigDecimal decimal = new BigDecimal((100*(Float.valueOf(jsonResult.get("count").toString())/Float.valueOf(jsonResult.get("shouldCount").toString()))));
//                BigDecimal roundedDecimal = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
//                jsonResult.put("countRate",Float.valueOf(roundedDecimal.floatValue()).toString()+"%");
//            }
////            dealJSONObject(item,jsonResult,"1",fcDate);
////            dealJSONObject(item,jsonResult,"2",fcDate);
////            dealJSONObject(item,jsonResult,"3",fcDate);
////            dealJSONObject(item,jsonResult,"4",fcDate);
////            dealJSONObject(item,jsonResult,"5",fcDate);
//            array.add(jsonResult);
//        }
//        return array;
    }

    public JSONObject dealJSONObject(TYjyDimensionality item,JSONObject jsonResult,String frequency,String fcDate)
    {
        TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
        tYjyFormAnswer.setDimensionalityPath(item.getPath());
//        tYjyForm.setFcDate(dealfcdate(fcDate,frequency));
        tYjyFormAnswer.setFrequency(frequency);
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        List<TYjyForm> searchList=tYjyFormMapper.selectFormListForAdmin(tYjyFormAnswer);
//        jsonResult.put("shouldCount"+frequency,searchList.size());//每日填报
        jsonResult.put("shouldCount",Integer.valueOf(jsonResult.get("shouldCount").toString())+searchList.size());//每日填报
        int count=0;
        for(TYjyForm item1:searchList)
        {
            if(item1.getStatus()!=null)
            {
                if(item1.getStatus().equals("2"))
                {
                    count=count+1;
                }
            }
        }
//        jsonResult.put("count"+frequency,count);//每日填报
        jsonResult.put("count",Integer.valueOf(jsonResult.get("count").toString())+count);//每日填报
        JSONObject add=new JSONObject();
        add.put("frequency",frequency);
        add.put("shouldCount",Integer.valueOf(jsonResult.get("shouldCount").toString())+searchList.size());
        add.put("count",count);
        return add;
    }




    @Override
    public List<TYjyForm> getFormStatusListWithadmin(String path,String fcDate,String frequency,String status)
    {
        TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
        tYjyFormAnswer.setDimensionalityPath(path);
        tYjyFormAnswer.setFrequency(frequency);
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        tYjyFormAnswer.setStatus(status);
        List<TYjyForm> searchList=tYjyFormMapper.selectFormListForAdmin(tYjyFormAnswer);
        return searchList;
    }


    @Override
    public List<TYjyForm> getformstatus(String path,String fcDate,String formQuestion)
    {
        TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
        tYjyFormAnswer.setDimensionalityPath(path);
        tYjyFormAnswer.setFormQuestion(formQuestion);
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        List<TYjyForm> searchList=tYjyFormMapper.selectFormListForAdminNoCount(tYjyFormAnswer);
        return searchList;
    }
    @Override
    public List<TYjyForm> getformstatusSubmit(String path,String fcDate,String formQuestion,String WorkNo)
    {
        TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
        tYjyFormAnswer.setDimensionalityPath(path);
        tYjyFormAnswer.setFormQuestion(formQuestion);
        tYjyFormAnswer.setWorkNo(WorkNo);
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        List<TYjyForm> searchList=tYjyFormMapper.selectFormListForSubmit(tYjyFormAnswer);
        return searchList;
    }
    @Override
    public List<JSONObject> dealLsitWithsubmit(List<TYjyDimensionality> list,String fcDate,String workNo)
    {
        List<JSONObject> array=new ArrayList<>();
        for(TYjyDimensionality item:list)
        {
            JSONObject jsonResult=new JSONObject();
            jsonResult.put("id",item.getId());
            jsonResult.put("dimensionalityName",item.getDimensionalityName());
            jsonResult.put("path",item.getPath());
            jsonResult.put("deptCode",item.getDeptCode());
            jsonResult.put("deptName",item.getDeptName());
            jsonResult.put("shouldCount",0);//每日填报
            jsonResult.put("count",0);//每日填报
            jsonResult.put("ruleType",item.getRuleType());//每日填报
            dealJSONObjectSubmit(item,jsonResult,null,fcDate,workNo);//可以结合可获取报表的方法来实现查询，需要进一步优化报表信息
            jsonResult.put("notCount",Integer.valueOf(jsonResult.get("shouldCount").toString())-Integer.valueOf(jsonResult.get("count").toString()));//每日填报
            if(jsonResult.get("shouldCount").toString().equals("0"))
            {
                jsonResult.put("countRate",Float.valueOf("0"));
            }
            else
            {
                BigDecimal decimal = new BigDecimal((100*(Float.valueOf(jsonResult.get("count").toString())/Float.valueOf(jsonResult.get("shouldCount").toString()))));
                BigDecimal roundedDecimal = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
                jsonResult.put("countRate",Float.valueOf(roundedDecimal.floatValue()).toString()+"%");
            }
            array.add(jsonResult);
        }
        return array;
    }


    @Override
    public List<JSONObject> dealLsitWithsubmitPlus(List<TYjyDimensionality> list,String fcDate,String workNo)
    {
        List<JSONObject> array=new ArrayList<>();
        for(TYjyDimensionality item:list)
        {
            JSONObject jsonResult=new JSONObject();
            jsonResult.put("id",item.getId());
            jsonResult.put("dimensionalityName",item.getDimensionalityName());
            jsonResult.put("path",item.getPath());
            jsonResult.put("deptCode",item.getDeptCode());
            jsonResult.put("deptName",item.getDeptName());
            jsonResult.put("shouldCount",item.getSumNum());//每日填报
            jsonResult.put("count",item.getFinishNum());//每日填报
            jsonResult.put("ruleType",item.getRuleType());//每日填报
//            dealJSONObjectSubmit(item,jsonResult,null,fcDate,workNo);//可以结合可获取报表的方法来实现查询，需要进一步优化报表信息
            jsonResult.put("notCount",Integer.valueOf(jsonResult.get("shouldCount").toString())-Integer.valueOf(jsonResult.get("count").toString()));//每日填报
            if(jsonResult.get("shouldCount").toString().equals("0"))
            {
                jsonResult.put("countRate",Float.valueOf("0"));
            }
            else
            {
                BigDecimal decimal = new BigDecimal((100*(Float.valueOf(jsonResult.get("count").toString())/Float.valueOf(jsonResult.get("shouldCount").toString()))));
                BigDecimal roundedDecimal = decimal.setScale(2, BigDecimal.ROUND_HALF_UP);
                jsonResult.put("countRate",Float.valueOf(roundedDecimal.floatValue()).toString()+"%");
            }
            array.add(jsonResult);
        }
        return array;
    }


    public JSONObject dealJSONObjectSubmit(TYjyDimensionality item,JSONObject jsonResult,String frequency,String fcDate,String workNo)
    {
        TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
        tYjyFormAnswer.setDimensionalityPath(item.getPath());
//        tYjyForm.setFcDate(dealfcdate(fcDate,frequency));
        tYjyFormAnswer.setFrequency(frequency);
        tYjyFormAnswer.setWorkNo(workNo);
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        List<TYjyForm> searchList=tYjyFormMapper.selectFormListForSubmit(tYjyFormAnswer);
//        jsonResult.put("shouldCount"+frequency,searchList.size());//每日填报
        jsonResult.put("shouldCount",Integer.valueOf(jsonResult.get("shouldCount").toString())+searchList.size());//每日填报
        int count=0;
        for(TYjyForm item1:searchList)
        {
            if(item1.getStatus()!=null)
            {
                if(item1.getStatus().equals("2"))
                {
                    count=count+1;
                }
            }
        }
//        jsonResult.put("count"+frequency,count);//每日填报
        jsonResult.put("count",Integer.valueOf(jsonResult.get("count").toString())+count);//每日填报
        JSONObject add=new JSONObject();
        add.put("frequency",frequency);
        add.put("shouldCount",Integer.valueOf(jsonResult.get("shouldCount").toString())+searchList.size());
        add.put("count",count);
        return add;
    }


    @Override
    public List<TYjyForm> getFormStatusListWithsumbit(String path,String fcDate,String frequency,String status,String workNo)
    {
        TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
        tYjyFormAnswer.setDimensionalityPath(path);
        tYjyFormAnswer.setFrequency(frequency);
        tYjyFormAnswer.setWorkNo(workNo);
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        tYjyFormAnswer.setStatus(status);
        List<TYjyForm> searchList=tYjyFormMapper.selectFormListForSubmit(tYjyFormAnswer);
        return searchList;
    }


    @Override
    public String dealfcdate(String fcDate,String frequency)
    {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date currentDate = new Date();
        if(fcDate!=null) {
            try {
                currentDate = dateFormat.parse(fcDate);
                calendar.setTime(currentDate);
            } catch (ParseException e) {
                // 异常处理代码
                e.printStackTrace();
                // 或者其他错误处理逻辑
            }
        }
        else
        {
            calendar.setTime(currentDate);
        }
        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
        String nowtime="";

        if(frequency.equals("0"))//
        {
            nowtime=currentYear+"";
            if(currentMonth<9)
            {
                nowtime=nowtime+"-0"+(currentMonth+1);
            }
            else
            {
                nowtime=nowtime+"-"+(currentMonth+1);
            }
            if(currentDay<10)
            {
                nowtime=nowtime+"-0"+currentDay;
            }
            else
            {
                nowtime=nowtime+"-"+currentDay;
            }
        }else if(frequency.equals("1"))
        {
            if(currentMonth<9)
            {
                nowtime=currentYear+"-0"+(currentMonth+1)+"-01";
            }
            else
            {
                nowtime=currentYear+"-"+(currentMonth+1)+"-01";
            }
        }else if(frequency.equals("2"))
        {
            if(currentMonth<=2)
            {
                nowtime=currentYear+"-01-01";
            }else if(currentMonth<=5)
            {
                nowtime=currentYear+"-04-01";
            }else if(currentMonth<=8)
            {
                nowtime=currentYear+"-07-01";
            }else if(currentMonth<=11)
            {
                nowtime=currentYear+"-10-01";
            }
        }else if(frequency.equals("3"))
        {
            if(currentMonth<=5)
            {
                nowtime=currentYear+"-01-01";
            }
            else
            {
                nowtime=currentYear+"-07-01";
            }
        }else if(frequency.equals("4"))
        {
            nowtime=currentYear+"-01-01";
        }else if(frequency.equals("5"))
        {
            // 计算本周的星期一
            calendar.add(Calendar.DAY_OF_YEAR,-1);
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            // 格式化输出为 yyyy-MM-dd
            nowtime=dateFormat.format(calendar.getTime());
        }
        return nowtime;
    }



    @Override
    public String dealfcdatebefore(String fcDate,String frequency,int num)
    {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date currentDate = new Date();
        if(fcDate!=null) {
            try {
                currentDate = dateFormat.parse(fcDate);
                calendar.setTime(currentDate);
            } catch (ParseException e) {
                // 异常处理代码
                e.printStackTrace();
                // 或者其他错误处理逻辑
            }
        }
        else
        {
            calendar.setTime(currentDate);
        }
        if(frequency.equals("0"))
        {
            calendar.add(Calendar.DAY_OF_YEAR,-1*num);
        }
        if(frequency.equals("1"))
        {
            calendar.add(Calendar.MONTH,-1*num);
        }
        if(frequency.equals("2"))
        {
            calendar.add(Calendar.MONTH,-3*num);
        }
        if(frequency.equals("3"))
        {
            calendar.add(Calendar.MONTH,-6*num);
        }
        if(frequency.equals("4"))
        {
            calendar.add(Calendar.MONTH,-12*num);
        }
        if(frequency.equals("5"))
        {
            calendar.add(Calendar.DAY_OF_YEAR,-7*num);
        }
        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
        String nowtime="";

        if(frequency.equals("0"))//
        {
            nowtime=currentYear+"";
            if(currentMonth<9)
            {
                nowtime=nowtime+"-0"+(currentMonth+1);
            }
            else
            {
                nowtime=nowtime+"-"+(currentMonth+1);
            }
            if(currentDay<10)
            {
                nowtime=nowtime+"-0"+currentDay;
            }
            else
            {
                nowtime=nowtime+"-"+currentDay;
            }
        }else if(frequency.equals("1"))
        {
            if(currentMonth<9)
            {
                nowtime=currentYear+"-0"+(currentMonth+1)+"-01";
            }
            else
            {
                nowtime=currentYear+"-"+(currentMonth+1)+"-01";
            }
        }else if(frequency.equals("2"))
        {
            if(currentMonth<=2)
            {
                nowtime=currentYear+"-01-01";
            }else if(currentMonth<=5)
            {
                nowtime=currentYear+"-04-01";
            }else if(currentMonth<=8)
            {
                nowtime=currentYear+"-07-01";
            }else if(currentMonth<=11)
            {
                nowtime=currentYear+"-10-01";
            }

        }else if(frequency.equals("3"))
        {
            if(currentMonth<=5)
            {
                nowtime=currentYear+"-01-01";
            }
            else
            {
                nowtime=currentYear+"-07-01";
            }
        }else if(frequency.equals("4"))
        {
            nowtime=currentYear+"-01-01";
        }else if(frequency.equals("5"))
        {
            // 计算本周的星期一
            calendar.add(Calendar.DAY_OF_YEAR,-1);
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            // 格式化输出为 yyyy-MM-dd
            nowtime=dateFormat.format(calendar.getTime());
//            Calendar weekcalendar = Calendar.getInstance();
//            int test=calendar.get(Calendar.DAY_OF_WEEK);
//            if(calendar.get(Calendar.DAY_OF_WEEK)<=1)
//            {
//                weekcalendar.setWeekDate(calendar.getWeekYear(), calendar.get(Calendar.WEEK_OF_MONTH)-1, 2);
//            }
//            else
//            {
//                weekcalendar.setWeekDate(calendar.getWeekYear(), calendar.get(Calendar.WEEK_OF_MONTH), 2);
//            }
//            nowtime=dateFormat.format(weekcalendar.getTime());
        }
        return nowtime;
    }

    @Override
    public void exportOnce(HttpServletResponse response, String fcDate, Long rootId,String type) throws IOException {


//        TYjyDimensionalityRoot dimensionalityRoot = this.getTreeByIdwithParent(rootId);
        //获取所有维度信息，只有有修改权限的人可以这么处理
        TYjyDimensionality tYjyDimensionality=new TYjyDimensionality();
        List<TYjyDimensionality> list=new ArrayList<>();
        String workNo=SecurityUtils.getUsername();
        if(type.equals("0"))
        {
            tYjyDimensionality.setCreateBy(workNo);
            tYjyDimensionality.setPath(rootId.toString()+",");
            list = getALLRootForAnswer(tYjyDimensionality);
        }
        if(type.equals("1"))
        {
            tYjyDimensionality=selectTYjyDimensionalityById(rootId);
            list = getByRootId(tYjyDimensionality);
        }

        List<TYjyDimensionalityRoot> dimensionalityRootList=new ArrayList<>();
        Map<Long,TYjyDimensionalityRoot> root=new HashMap<>();
        Map<Long,Map<Long,List<TYjyDimensionalityRoot>>> map=new HashMap<>();
        for(TYjyDimensionality item:list)
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            add.setDeptName(item.getDeptName());
            add.setDeptCode(item.getDeptCode());
            if(item.getParentId()==null)
            {
                root.put(item.getId(),add);
                Map<Long,List<TYjyDimensionalityRoot>> rootList=new HashMap<>();
                map.put(item.getId(),rootList);
            }
        }
        for(TYjyDimensionality item:list)
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            add.setDeptName(item.getDeptName());
            add.setDeptCode(item.getDeptCode());
            if(item.getParentId()!=null)
            {
                if(map.get(Long.valueOf(item.getPath().split(",")[0])).containsKey(item.getParentId()))
                {
                    map.get(Long.valueOf(item.getPath().split(",")[0])).get(item.getParentId()).add(add);
                }
                else
                {
                    List<TYjyDimensionalityRoot> insert=new ArrayList<>();
                    insert.add(add);
                    map.get(Long.valueOf(item.getPath().split(",")[0])).put(item.getParentId(),insert);
                }
            }
        }
        for(Long item:root.keySet())
        {
            dimensionalityRootList.add(getTree(root.get(item),map.get(item)));
        }
        //获取所有的form信息及其值 可以简单粗暴些
        TYjyDimensionalityRoot finalroot=dimensionalityRootList.get(0);

        TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
        tYjyFormAnswer.setDimensionalityPath(rootId.toString()+",");
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        List<TYjyForm> formList=new ArrayList<>();
        if(type.equals("0"))
        {
            tYjyFormAnswer.setWorkNo(workNo);
            formList=tYjyFormMapper.selectFormListForSubmit(tYjyFormAnswer);
        }
        if(type.equals("1"))
        {
            formList=tYjyFormMapper.selectFormListForAdminNoCount(tYjyFormAnswer);
        }
        Map<Long,List<TYjyForm>> dealformroot=new HashMap<>();
        List<String> titleList=new ArrayList<>();
        List<Integer> sortList=new ArrayList<>();
        Map<Integer,String> titleMap=new HashedMap();
        for(TYjyForm item:formList)//此处修改
        {
            if(dealformroot.containsKey(item.getDimensionalityId()))
            {
                dealformroot.get(item.getDimensionalityId()).add(item);
            }
            else
            {
                List<TYjyForm> newlist=new ArrayList<>();
                newlist.add(item);
                dealformroot.put(item.getDimensionalityId(),newlist);
            }
//            if(!titleList.contains(item.getFormQuestion().trim().replace(")","").replace("("," ").split(" ")[1]))
//            {
//                //formQuestion 需要进一步的规范化，让世界感受痛苦吧//这里也要考虑显示的效果啊
//                titleList.add(item.getFormQuestion().trim().replace(")","").replace("("," ").split(" ")[1]);
//            }

            if(item.getFormQuestionSort()!=null)
            {
                if(!titleMap.containsValue(item.getFormQuestionExport()))
                {
                    sortList.add(item.getFormQuestionSort());
                    titleMap.put(item.getFormQuestionSort(),item.getFormQuestionExport());
                }
            }
            else
            {
                if(!titleList.contains(item.getFormQuestionExport()))
                {
                    titleList.add(item.getFormQuestionExport());
                }
            }

        }
        Collections.sort(sortList);
        for(Integer item:sortList)
        {
            titleList.add(titleMap.get(item));
        }
        //该环节主要是把维度和信息单元关联起来，此处还是有必要的，不然容易翻车啊，但是数据结构可能还存在着各种问题，需要思考一下情况。
        dealexportroot(finalroot,dealformroot,"",0);
        List<TYjyDimensionalityRoot> exportlist=new ArrayList<>();
        dealexportlist(finalroot,exportlist);
//        List<TYjyDimensionalityRoot> allLeaves = this.getAllLeaves(dimensionalityRoot);
//        TYjyForm tYjyForm =new TYjyForm();
//        tYjyForm.setDimensionalityId(rootId);
//        tYjyForm.setWorkNo(SecurityUtils.getUsername());
//        List<Long> ids = SpringUtils.getBean(TYjyFormMapper.class).selectTYjyFormListForAnswer(tYjyForm).stream().map(x->x.getId()).collect(Collectors.toList());


//        List<TYjyExport> forms = answerService.selectExportFormListByFormId(ids);
//        List<TYjyExport> exports = answerService.selectExportListByFormId(ids, startDate, endDate);
//
//        forms.forEach(item ->{
//            TYjyDimensionalityRoot rootD = allLeaves.stream().filter(x -> x.getValue().equals(item.getDimensionalityId())).findFirst().orElse(null);
//            if(Objects.nonNull(rootD))item.setLabels(rootD.getLabels());
//            else item.setLabels(new ArrayList<>());
//        });
//        exports.forEach(item ->{
//            item.setDateDesc(dataReportFrequency.getDateTemp(item.getFrequency(),item.getFcDate()));
//            TYjyExport tYjyExport = forms.stream().filter(x -> x.getFormId().equals(item.getFormId())).findFirst().orElse(null);
//            if(Objects.nonNull(tYjyExport)) item.setLabels(tYjyExport.getLabels());
//            else item.setLabels(new ArrayList<>());
//        });


        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        ServletOutputStream out = response.getOutputStream();
        XSSFWorkbook wb = new XSSFWorkbook();
        try {

            this.specialSheetsExcel( wb, exportlist, titleList,fcDate,rootId.toString()+",");
            //此处也需要新方法了
//            if(rootId.toString().equals("1059"))
//            {
//                this.specialSheetsExcelWithNote( wb, exportlist, titleList,fcDate,rootId.toString()+",");
//            }
//            else
//            {
//                this.specialSheetsExcel( wb, exportlist, titleList,fcDate,rootId.toString()+",");
//            }
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
            IOUtils.closeQuietly(out);
        }

    }

    public void dealexportroot(TYjyDimensionalityRoot finalroot,Map<Long,List<TYjyForm>> dealformroot,String label,Integer deepCount)
    {
        if(finalroot.getChildren()!=null)
        {
            for(TYjyDimensionalityRoot item:finalroot.getChildren())
            {
                if(dealformroot.containsKey(item.getValue()))
                {
                    item.setFormList(dealformroot.get(item.getValue()));
                }
                if(deepCount==0)
                {
                    item.setLabel(item.getLabel());
                }
                else
                {
                    item.setLabel((label+"//"+item.getLabel()));
                }

                item.setDeepCount(deepCount);
                dealexportroot(item,dealformroot,item.getLabel(),deepCount+1);
            }
        }
        return;
    }
    public void dealexportlist(TYjyDimensionalityRoot finalroot,List<TYjyDimensionalityRoot> exportlist)
    {
        if(finalroot.getFormList()!=null)
        {
            if(finalroot.getFormList().size()>0)
            {
                exportlist.add(finalroot);
            }
        }
        if(finalroot.getChildren()!=null)
        {
            for(TYjyDimensionalityRoot item:finalroot.getChildren())
            {
                dealexportlist(item,exportlist);
            }
        }
        return;
    }


//    private void specialexportExcel(XSSFWorkbook workbook, List<TYjyExport> forms, List<TYjyExport> exports,String startDate,String endDate,boolean isId) throws Exception{
//
//        // 1. 公共数据预处理
//        List<TYjyExport> reasonsList = exports.stream()
//                .filter(this::isReason)
//                .collect(Collectors.toList());
//
//        // 2. 分频率处理（参考网页5的模板化思想）
//        specialSheetsExcel(workbook, forms, exports, startDate, endDate,isId);
//
//        // 3. 原因措施处理（复用主逻辑）
//        if (!reasonsList.isEmpty()) {
//            processReasonSheet(workbook, reasonsList, forms);
//        }
//    }

    private void specialSheetsExcel(XSSFWorkbook workbook, List<TYjyDimensionalityRoot> exportlist, List<String> titleList, String fcDate,String path) {

        //开始处理excel
        XSSFSheet sheet = workbook.createSheet("数据");

        //标题使用的文本配置
        XSSFCellStyle headerStyle = createHeaderStyle(sheet.getWorkbook());
        headerStyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        headerStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        headerStyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        headerStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        headerStyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        headerStyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        headerStyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        headerStyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        headerStyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        headerStyle.setWrapText(true);//允许自动换行
        //文本使用的数据配置
        CellStyle wordstyle = workbook.createCellStyle();
        wordstyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        wordstyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        wordstyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        wordstyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        wordstyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        wordstyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        wordstyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        wordstyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        wordstyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        wordstyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        wordstyle.setWrapText(true);//允许自动换行
        //数据使用的数据配置
        CellStyle numberstyle = workbook.createCellStyle();

        DataFormat dataFormat = workbook.createDataFormat();
        numberstyle.setDataFormat(dataFormat.getFormat("General"));
        numberstyle.setDataFormat(
                workbook.createDataFormat().getFormat("0.00")
        );
        numberstyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        numberstyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        numberstyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        numberstyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        numberstyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        numberstyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        numberstyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        numberstyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        numberstyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        numberstyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        numberstyle.setWrapText(true);//允许自动换行


        //填充基础表头
        XSSFRow headerRow = sheet.createRow(0);
        XSSFCell cell = headerRow.createCell(0);
        cell.setCellValue(fcDate);
        cell.setCellStyle(headerStyle);
        int max=1;
        for(TYjyDimensionalityRoot item:exportlist)
        {
            String[] dList = item.getLabel().split("//");
            if (max < dList.length)
            {
                max = dList.length;
            }
        }
        int headtitle=1;
        while(headtitle<max)
        {
            cell = headerRow.createCell(headtitle);
            cell.setCellStyle(headerStyle);
            headtitle=headtitle+1;
        }
        int count=max;
        for(String item:titleList)
        {
            cell=headerRow.createCell(count);
            cell.setCellStyle(headerStyle);
            cell.setCellValue(item);
            count=count+1;
        }
//        sheet.autoSizeColumn(0);
        // 具体内容
        int begin=1;
        List<List<String>> dataList=new ArrayList<>();
        for(TYjyDimensionalityRoot item:exportlist)
        {
            List<String> newDataList=new ArrayList<>();

            headerRow = sheet.createRow(begin);
            int morecount=max;
            String[] dList=item.getLabel().split("//");
            //新版数据处理
            for(int di=0;di<morecount;di++)
            {
                cell=headerRow.createCell(di);
                if(di<dList.length)
                {
                    cell.setCellValue(dList[di]);
                    newDataList.add(dList[di]);
                }
                else
                {
                    newDataList.add("");
                }
                cell.setCellStyle(wordstyle);
            }
            for(String itemCount:titleList)
            {
                for(TYjyForm formitem:item.getFormList())
                {
                    if(itemCount.equals(formitem.getFormQuestionExport()))
                    {
                        cell=headerRow.createCell(morecount);

                        if(formitem.getFormType().equals("0")||formitem.getFormType().equals("1"))
                        {
                            cell.setCellStyle(numberstyle);
                            if(formitem.getFormValue()!=null)
                            {
                                if(!formitem.getFormValue().equals(""))
                                {
                                    cell.setCellValue(Double.valueOf(formitem.getFormValue()));
                                    cell.setCellType(CellType.NUMERIC); // 确保类型为数值
                                }
                                else
                                {
                                    cell.setCellValue(formitem.getFormValue());
                                }

                            }
                            else
                            {
                                cell.setCellValue(formitem.getFormValue());
                            }
                        }
                        else
                        {
                            cell.setCellStyle(wordstyle);
                            cell.setCellValue(formitem.getFormValue());
                        }
                        break;
                    }
                    else
                    {
                        cell=headerRow.createCell(morecount);
                        cell.setCellStyle(wordstyle);
                    }
                }
                morecount=morecount+1;
            }
            dataList.add(newDataList);
//            sheet.autoSizeColumn(begin);
            begin=begin+1;



        }

        mergeAlone(sheet,dataList,dataList.size(),max,1);
        autoSizeColumns(sheet,max+titleList.size(),true);
        //特殊的合并规则
        if(path.equals("3872,"))
        {
            mergeOne(sheet,4,5,2,4);
            mergeOne(sheet,5,6,2,4);
            mergeOne(sheet,6,7,2,8);
            mergeOne(sheet,7,8,2,8);
        }


        sheet = workbook.createSheet("编号");
        headerStyle = createHeaderStyle(sheet.getWorkbook());
        //填充基础表头
        headerRow = sheet.createRow(0);
        cell = headerRow.createCell(0);
        cell.setCellValue(fcDate);
        cell.setCellStyle(headerStyle);
        count=max;
        for(String item:titleList)
        {
            cell=headerRow.createCell(count);
            cell.setCellValue(item);
            cell.setCellStyle(headerStyle);
            count=count+1;
        }
        // 具体内容
        begin=1;
        for(TYjyDimensionalityRoot item:exportlist)
        {
            headerRow = sheet.createRow(begin);
            int morecount=max;
            String[] dList=item.getLabel().split("//");
            if(max<dList.length)
            {
                max=dList.length;
            }
            for(int di=0;di<dList.length;di++)
            {
                cell=headerRow.createCell(di);
                cell.setCellValue(dList[di]);
            }
            for(String itemCount:titleList)
            {
                for(TYjyForm formitem:item.getFormList())
                {
                    if(itemCount.equals(formitem.getFormQuestionExport()))
                    {
                        cell=headerRow.createCell(morecount);
                        cell.setCellValue(formitem.getId());
                    }
                }
                morecount=morecount+1;
            }
            begin=begin+1;
        }

        sheet = workbook.createSheet("配置");
        headerStyle = createHeaderStyle(sheet.getWorkbook());
        //填充基础表头
        headerRow = sheet.createRow(0);
        cell = headerRow.createCell(0);
        cell.setCellValue(max);
        cell.setCellStyle(headerStyle);
        cell = headerRow.createCell(1);
        cell.setCellValue(titleList.size());
        cell.setCellStyle(headerStyle);
        cell = headerRow.createCell(2);
        cell.setCellValue(path);
        cell.setCellStyle(headerStyle);

    }

    private void specialSheetsExcelWithNote(XSSFWorkbook workbook, List<TYjyDimensionalityRoot> exportlist, List<String> titleList, String fcDate,String path) {

        //开始处理excel
        XSSFSheet sheet = workbook.createSheet("数据");

        //标题使用的文本配置
        XSSFCellStyle headerStyle = createHeaderStyle(sheet.getWorkbook());
        headerStyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        headerStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        headerStyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        headerStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        headerStyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        headerStyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        headerStyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        headerStyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        headerStyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        headerStyle.setWrapText(true);//允许自动换行
        //文本使用的数据配置
        CellStyle wordstyle = workbook.createCellStyle();
        wordstyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        wordstyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        wordstyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        wordstyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        wordstyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        wordstyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        wordstyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        wordstyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        wordstyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        wordstyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        wordstyle.setWrapText(true);//允许自动换行
        //数据使用的数据配置
        CellStyle numberstyle = workbook.createCellStyle();

        DataFormat dataFormat = workbook.createDataFormat();
//        numberstyle.setDataFormat(dataFormat.getFormat("General"));
        numberstyle.setDataFormat(
                workbook.createDataFormat().getFormat("0.00")
        );
        numberstyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        numberstyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        numberstyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        numberstyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        numberstyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        numberstyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        numberstyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        numberstyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        numberstyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        numberstyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        numberstyle.setWrapText(true);//允许自动换行



        //填充基础表头
        XSSFRow headerRow = sheet.createRow(0);
        XSSFCell cell = headerRow.createCell(0);
        cell.setCellValue(fcDate);
        cell.setCellStyle(headerStyle);
        int max=1;
        for(TYjyDimensionalityRoot item:exportlist)
        {
            String[] dList = item.getLabel().split("//");
            if (max < dList.length)
            {
                max = dList.length;
            }
        }
        int headtitle=1;
        while(headtitle<max)
        {
            cell = headerRow.createCell(headtitle);
            cell.setCellStyle(headerStyle);
            headtitle=headtitle+1;
        }
        cell=headerRow.createCell(max);
        cell.setCellStyle(headerStyle);
        cell.setCellValue("指标");

        int count=max+1;
        for(String item:titleList)
        {
            cell=headerRow.createCell(count);
            cell.setCellStyle(headerStyle);
            cell.setCellValue(item);
            count=count+1;
        }
//        sheet.autoSizeColumn(0);
        // 具体内容
        int begin=1;
        for(TYjyDimensionalityRoot item:exportlist)
        {
            headerRow = sheet.createRow(begin);
            int morecount=max+1;
            String[] dList=item.getLabel().split("//");
            //新版数据处理
            for(int di=0;di<morecount;di++)
            {
                cell=headerRow.createCell(di);
                if(di<dList.length)
                {
                    cell.setCellValue(dList[di]);
                }
                cell.setCellStyle(wordstyle);
            }

            int breakNum=0;
            for(String itemCount:titleList)
            {
                for(TYjyForm formitem:item.getFormList())
                {
                    if(breakNum==0)
                    {
                        cell=headerRow.createCell(max);
                        cell.setCellValue(formitem.getFormNote());
                        cell.setCellStyle(wordstyle);
                        breakNum=breakNum+1;
                    }
                    if(itemCount.equals(formitem.getFormQuestionExport()))
                    {
                        cell=headerRow.createCell(morecount);

                        if(formitem.getFormType().equals("0")||formitem.getFormType().equals("1"))
                        {
                            cell.setCellStyle(numberstyle);
                            if(formitem.getFormValue()!=null)
                            {
                                if(!formitem.getFormValue().equals(""))
                                {
                                    cell.setCellValue(Double.valueOf(formitem.getFormValue()));
                                }
                                else
                                {
                                    cell.setCellValue(formitem.getFormValue());
                                }

                            }
                            else
                            {
                                cell.setCellValue(formitem.getFormValue());
                            }
                        }
                        else
                        {
                            cell.setCellStyle(wordstyle);
                            cell.setCellValue(formitem.getFormValue());
                        }
                        break;
                    }
                    else
                    {
                        cell=headerRow.createCell(morecount);
                        cell.setCellStyle(wordstyle);
                    }
                }
                morecount=morecount+1;
            }
//            sheet.autoSizeColumn(begin);
            begin=begin+1;
        }
        autoSizeColumns(sheet,max+1+titleList.size(),true);
        sheet = workbook.createSheet("编号");
        headerStyle = createHeaderStyle(sheet.getWorkbook());
        //填充基础表头
        headerRow = sheet.createRow(0);
        cell = headerRow.createCell(0);
        cell.setCellValue(fcDate);
        cell.setCellStyle(headerStyle);
        count=max+1;
        for(String item:titleList)
        {
            cell=headerRow.createCell(count);
            cell.setCellValue(item);
            cell.setCellStyle(headerStyle);
            count=count+1;
        }
        // 具体内容
        begin=1;
        for(TYjyDimensionalityRoot item:exportlist)
        {
            headerRow = sheet.createRow(begin);
            int morecount=max+1;
            String[] dList=item.getLabel().split("//");
            if(max<dList.length)
            {
                max=dList.length;
            }
            for(int di=0;di<dList.length;di++)
            {
                cell=headerRow.createCell(di);
                cell.setCellValue(dList[di]);
            }
            for(String itemCount:titleList)
            {
                for(TYjyForm formitem:item.getFormList())
                {
                    if(itemCount.equals(formitem.getFormQuestionExport()))
                    {
                        cell=headerRow.createCell(morecount);
                        cell.setCellValue(formitem.getId());
                    }
                }
                morecount=morecount+1;
            }
            begin=begin+1;
        }

        sheet = workbook.createSheet("配置");
        headerStyle = createHeaderStyle(sheet.getWorkbook());
        //填充基础表头
        headerRow = sheet.createRow(0);
        cell = headerRow.createCell(0);
        cell.setCellValue(max+1);
        cell.setCellStyle(headerStyle);
        cell = headerRow.createCell(1);
        cell.setCellValue(titleList.size());
        cell.setCellStyle(headerStyle);
        cell = headerRow.createCell(2);
        cell.setCellValue(path);
        cell.setCellStyle(headerStyle);
    }






    //暂时先不考虑导入模块，考虑问题目标和单位展示，肯定只能记录每行第一个的单位和目标了
    private void specialSheetsExcelWithConnect(XSSFWorkbook workbook, List<TYjyDimensionalityRoot> exportlist, List<String> titleList,Map<String,Map<Long,TYjyForm>> fcDateFormList, Map<Long,TYjyForm> idFormList, String startDate, String endDate,String path) {

        String timeStr="每月";
        String frequency="1";
        //开始处理excel
        XSSFSheet sheet = workbook.createSheet(timeStr+"数据");
        //
        XSSFSheet sheet1 = workbook.createSheet(timeStr+"编号");


        //标题使用的文本配置
        XSSFCellStyle headerStyle = createHeaderStyle(sheet.getWorkbook());

        headerStyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        headerStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        headerStyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        headerStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        headerStyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        headerStyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        headerStyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        headerStyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        headerStyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        headerStyle.setWrapText(true);//允许自动换行
        //文本使用的数据配置
        CellStyle wordstyle = workbook.createCellStyle();
        wordstyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        wordstyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        wordstyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        wordstyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        wordstyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        wordstyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        wordstyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        wordstyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        wordstyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        wordstyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        wordstyle.setWrapText(true);//允许自动换行
        //数据使用的数据配置
        CellStyle numberstyle = workbook.createCellStyle();

        DataFormat dataFormat = workbook.createDataFormat();
//        numberstyle.setDataFormat(dataFormat.getFormat("General"));
        numberstyle.setDataFormat(
                workbook.createDataFormat().getFormat("0.00")
        );
        numberstyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        numberstyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        numberstyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        numberstyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        numberstyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        numberstyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        numberstyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        numberstyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        numberstyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        numberstyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        numberstyle.setWrapText(true);//允许自动换行



        //数据填充基础表头
        XSSFRow headerRow = sheet.createRow(0);
        XSSFCell cell = headerRow.createCell(0);
        cell.setCellValue("");
        cell.setCellStyle(headerStyle);
        //编号
//        headerStyle = createHeaderStyle(sheet1.getWorkbook());
        //编号填充基础表头
//        XSSFRow headerRow1 = sheet1.createRow(0);
//        XSSFCell cell1 = headerRow1.createCell(0);
//        cell1.setCellValue("");
//        cell1.setCellStyle(headerStyle);
        //确定指标和单位是否要显示
        int NoteCount=0;
        int UnitCount=0;
        for(TYjyForm item:idFormList.values())
        {
            if(item.getFormNote()!=null)
            {
                NoteCount=1;
            }
            if(item.getUnit()!=null)
            {
                UnitCount=1;
            }
            if(NoteCount!=0&&UnitCount!=0)
            {
                break;
            }
        }
        //
        int max=1;
        String lastdate="";
        for(TYjyDimensionalityRoot item:exportlist)
        {
            String[] dList = item.getLabel().split("//");
            if (max < dList.length)
            {
                max = dList.length;
            }
        }
        int headtitle=1;
        while(headtitle<max)
        {
            cell = headerRow.createCell(headtitle);
            cell.setCellStyle(headerStyle);
            headtitle=headtitle+1;
        }


        cell=headerRow.createCell(max);
        cell.setCellStyle(headerStyle);
        cell.setCellValue("问题");

        int maxadd=1;

        if(NoteCount>0)
        {
            cell=headerRow.createCell(max+maxadd);
            cell.setCellStyle(headerStyle);
            cell.setCellValue("指标");
            maxadd=maxadd+1;
        }

        if(UnitCount>0)
        {
            cell=headerRow.createCell(max+maxadd);
            cell.setCellStyle(headerStyle);
            cell.setCellValue("单位");
            maxadd=maxadd+1;
        }

        int count=max+maxadd;//此处之后为需要处理的数据 max+1 为维度加基础原因
        //获取时间段函数
        List<String> TimeList= getHeaderTitlesWithConnect(frequency,startDate,endDate);
        //开始的地方
        for(String item:TimeList)
        {
            lastdate=item;
            cell=headerRow.createCell(count);
            cell.setCellStyle(headerStyle);
            cell.setCellValue(item);
            count=count+1;
        }
        //不需要考虑时间的地方，以最后的时间微赚
        int timecount=count;

        for(String item:titleList)
        {
            cell=headerRow.createCell(count);
            cell.setCellStyle(headerStyle);
            cell.setCellValue(item);
            count=count+1;
        }
//        sheet.autoSizeColumn(0);
        // 具体内容
        int begin=1;//从第二行开始
        List<List<String>> dataList=new ArrayList<>();
        for(TYjyDimensionalityRoot item:exportlist)
        {
            int morecount=max+maxadd;
//            String[] dList=item.getLabel().split("//");
//            headerRow = sheet.createRow(begin);
//            //新版数据处理
//            List<String> newDataList=new ArrayList<>();
//            for(int di=0;di<morecount;di++)
//            {
//                cell=headerRow.createCell(di);
//                if(di<dList.length)
//                {
//                    cell.setCellValue(dList[di]);
//                    newDataList.add(dList[di]);
//                }
//                else
//                {
//                    newDataList.add("");
//                }
//                cell.setCellStyle(wordstyle);
//            }
//            dataList.add(newDataList);
//            int dealcount=0;
            //此处发生变化，需要一次性将一行全部输入进去
            String[] dList=item.getLabel().split("//");
            for(TYjyForm formitem:item.getFormList())
            {
                headerRow = sheet.createRow(begin);
                //新版数据处理
                List<String> newDataList=new ArrayList<>();
                for(int di=0;di<morecount;di++)
                {
                    cell=headerRow.createCell(di);
                    if(di<dList.length)
                    {
                        cell.setCellValue(dList[di]);
                        newDataList.add(dList[di]);
                    }
                    else
                    {
                        newDataList.add("");
                    }
                    cell.setCellStyle(wordstyle);
                }
                dataList.add(newDataList);
//                if(dealcount==1)
//                {
//                    for(int di=0;di<morecount;di++)
//                    {
//                        cell=headerRow.createCell(di);
//                        cell.setCellStyle(wordstyle);
//                    }
//                }
//                else
//                {
//                    dealcount=1;
//                }
                //先填入note
                cell=headerRow.createCell(max);
                cell.setCellValue(formitem.getFormQuestion());
                cell.setCellStyle(wordstyle);
                if(NoteCount>0)
                {
                    cell=headerRow.createCell(max+NoteCount);
                    cell.setCellValue(formitem.getFormNote());
                    cell.setCellStyle(wordstyle);
                }
                if(UnitCount>0)
                {
                    cell=headerRow.createCell(max+NoteCount+UnitCount);
                    cell.setCellValue(formitem.getUnit());
                    cell.setCellStyle(wordstyle);
                }

                //再按照时间填入数据
                for(String timeItem:TimeList)
                {
                    cell=headerRow.createCell(morecount);
                    TYjyForm insertItem=fcDateFormList.get(timeItem).get(formitem.getId());
                    if(insertItem!=null)
                    {
                        if(insertItem.getFormType().equals("0")||insertItem.getFormType().equals("1"))
                        {
                            cell.setCellStyle(numberstyle);
                            if(insertItem.getFormValue()!=null)
                            {
                                if(!insertItem.getFormValue().equals(""))
                                {
                                    cell.setCellValue(Double.valueOf(insertItem.getFormValue()));
                                }
                                else
                                {
                                    cell.setCellValue(insertItem.getFormValue());
                                }

                            }
                            else
                            {
                                cell.setCellValue(insertItem.getFormValue());
                            }
                        }
                        else
                        {
                            cell.setCellStyle(wordstyle);
                            cell.setCellValue(insertItem.getFormValue());
                        }
                    }
                    else
                    {
                        cell=headerRow.createCell(morecount);
                        cell.setCellStyle(wordstyle);
                    }
                    morecount=morecount+1;
                }
                //最后填入收尾的数据

                if(formitem.getConnectId()!=null)
                {
                    TYjyForm Lastshow=idFormList.get(formitem.getConnectId());
                    //此处注入新的内容
                    while(Lastshow!=null)
                    {
                        int pathCount=0;
                        for(String itemCount:titleList)
                        {
                            cell=headerRow.createCell(morecount+pathCount);
                            if(Lastshow!=null)
                            {
                                if(itemCount.equals(Lastshow.getFormQuestionExport()))
                                {

                                    TYjyForm insertItem=fcDateFormList.get(lastdate).get(Lastshow.getId());
                                    if(insertItem!=null)
                                    {
                                        if(insertItem.getFormType().equals("0")||insertItem.getFormType().equals("1"))
                                        {
                                            cell.setCellStyle(numberstyle);
                                            if(insertItem.getFormValue()!=null)
                                            {
                                                if(!insertItem.getFormValue().equals(""))
                                                {
                                                    cell.setCellValue(Double.valueOf(insertItem.getFormValue()));
                                                }
                                                else
                                                {
                                                    cell.setCellValue(insertItem.getFormValue());
                                                }

                                            }
                                            else
                                            {
                                                cell.setCellValue(insertItem.getFormValue());
                                            }
                                        }
                                        else
                                        {
                                            cell.setCellStyle(wordstyle);
                                            cell.setCellValue(insertItem.getFormValue());
                                        }
                                    }
                                    else
                                    {
//                                        cell=headerRow.createCell(morecount);
                                        cell.setCellStyle(wordstyle);
                                    }

                                    if(Lastshow.getConnectId()!=null)
                                    {
                                        Lastshow=idFormList.get(Lastshow.getConnectId());
                                    }
                                    else
                                    {
                                        Lastshow=null;
                                    }
                                }
                                else
                                {
//                                    cell=headerRow.createCell(morecount);
                                    cell.setCellStyle(wordstyle);
                                }
                            }
                            else
                            {
//                                cell=headerRow.createCell(morecount);
                                cell.setCellStyle(wordstyle);
                            }
                            pathCount=pathCount+1;
                        }
                    }
                }
                else
                {
                    int pathCount=0;
                    for(String itemCount:titleList)
                    {
                        cell=headerRow.createCell(morecount+pathCount);
                        cell.setCellStyle(wordstyle);
                        pathCount=pathCount+1;
                    }
                }
                begin=begin+1;
                morecount=max+maxadd;
//                headerRow = sheet.createRow(begin);
//                newDataList=new ArrayList<>();
//                for(int di=0;di<morecount;di++)
//                {
//                    cell=headerRow.createCell(di);
//                    if(di<dList.length)
//                    {
//                        cell.setCellValue(dList[di]);
//                        newDataList.add(dList[di]);
//                    }
//                    else
//                    {
//                        newDataList.add("");
//                    }
//                    cell.setCellStyle(wordstyle);
//                }
//                dataList.add(newDataList);
                //新版数据处理
            }

//            int breakNum=0;
//            for(String itemCount:titleList)
//            {
//                for(TYjyForm formitem:item.getFormList())
//                {
//                    if(breakNum==0)
//                    {
//                        cell=headerRow.createCell(max);
//                        cell.setCellValue(formitem.getFormNote());
//                        cell.setCellStyle(wordstyle);
//                        breakNum=breakNum+1;
//                    }
//                    if(itemCount.equals(formitem.getFormQuestionExport()))
//                    {
//                        cell=headerRow.createCell(morecount);
//                        if(formitem.getFormType().equals("0")||formitem.getFormType().equals("1"))
//                        {
//                            cell.setCellStyle(numberstyle);
//                            if(formitem.getFormValue()!=null)
//                            {
//                                if(!formitem.getFormValue().equals(""))
//                                {
//                                    cell.setCellValue(Double.valueOf(formitem.getFormValue()));
//                                }
//                                else
//                                {
//                                    cell.setCellValue(formitem.getFormValue());
//                                }
//
//                            }
//                            else
//                            {
//                                cell.setCellValue(formitem.getFormValue());
//                            }
//                        }
//                        else
//                        {
//                            cell.setCellStyle(wordstyle);
//                            cell.setCellValue(formitem.getFormValue());
//                        }
//                        break;
//                    }
//                    else
//                    {
//                        cell=headerRow.createCell(morecount);
//                        cell.setCellStyle(wordstyle);
//                    }
//                }
//                morecount=morecount+1;
//            }
//            begin=begin+1;
        }
        mergeAlone(sheet,dataList,dataList.size(),max,1);
        autoSizeColumns(sheet,max+maxadd+TimeList.size()+titleList.size(),true);



//        count=max+1;
//        for(String item:titleList)
//        {
//            cell=headerRow.createCell(count);
//            cell.setCellValue(item);
//            cell.setCellStyle(headerStyle);
//            count=count+1;
//        }
//        // 具体内容
//        begin=1;
//        for(TYjyDimensionalityRoot item:exportlist)
//        {
//            headerRow = sheet.createRow(begin);
//            int morecount=max+1;
//            String[] dList=item.getLabel().split("//");
//            if(max<dList.length)
//            {
//                max=dList.length;
//            }
//            for(int di=0;di<dList.length;di++)
//            {
//                cell=headerRow.createCell(di);
//                cell.setCellValue(dList[di]);
//            }
//            for(String itemCount:titleList)
//            {
//                for(TYjyForm formitem:item.getFormList())
//                {
//                    if(itemCount.equals(formitem.getFormQuestionExport()))
//                    {
//                        cell=headerRow.createCell(morecount);
//                        cell.setCellValue(formitem.getId());
//                    }
//                }
//                morecount=morecount+1;
//            }
//            begin=begin+1;
//        }

        sheet = workbook.createSheet(timeStr+"配置");
        headerStyle = createHeaderStyle(sheet.getWorkbook());
        //填充基础表头
        headerRow = sheet.createRow(0);
        cell = headerRow.createCell(0);
        cell.setCellValue(max+1);
        cell.setCellStyle(headerStyle);
        cell = headerRow.createCell(1);
        cell.setCellValue(titleList.size());
        cell.setCellStyle(headerStyle);
        cell = headerRow.createCell(2);
        cell.setCellValue(path);
        cell.setCellStyle(headerStyle);
    }

    //暂时先不考虑导入模块，考虑问题目标和单位展示，肯定只能记录每行第一个的单位和目标了
    private void specialSheetsExcelWithMouth(XSSFWorkbook workbook, List<TYjyDimensionalityRoot> exportlist, List<String> mouthTitleList, List<String> titleList,Map<String,Map<Long,TYjyForm>> fcDateFormList, Map<Long,TYjyForm> idFormList, String startDate, String endDate,String path) {

        String timeStr="每月";
        String frequency="1";
        //开始处理excel
        XSSFSheet sheet = workbook.createSheet(timeStr+"数据");
        //
//        XSSFSheet sheet1 = workbook.createSheet(timeStr+"编号");


        //标题使用的文本配置
        XSSFCellStyle headerStyle = createHeaderStyle(sheet.getWorkbook());

        headerStyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        headerStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        headerStyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        headerStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        headerStyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        headerStyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        headerStyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        headerStyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        headerStyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        headerStyle.setWrapText(true);//允许自动换行
        //文本使用的数据配置
        CellStyle wordstyle = workbook.createCellStyle();
        wordstyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        wordstyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        wordstyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        wordstyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        wordstyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        wordstyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        wordstyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        wordstyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        wordstyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        wordstyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        wordstyle.setWrapText(true);//允许自动换行
        //数据使用的数据配置
        CellStyle numberstyle = workbook.createCellStyle();

        DataFormat dataFormat = workbook.createDataFormat();
//        numberstyle.setDataFormat(dataFormat.getFormat("General"));
        numberstyle.setDataFormat(
                workbook.createDataFormat().getFormat("0.00")
        );
        numberstyle.setBorderBottom(BorderStyle.THIN); // 下边框粗线
        numberstyle.setBottomBorderColor(IndexedColors.BLACK.getIndex()); // 下边框颜色为黑色
        numberstyle.setBorderLeft(BorderStyle.THIN); // 左边框粗线
        numberstyle.setLeftBorderColor(IndexedColors.BLACK.getIndex()); // 左边框颜色为黑色
        numberstyle.setBorderRight(BorderStyle.THIN); // 右边框粗线
        numberstyle.setRightBorderColor(IndexedColors.BLACK.getIndex()); // 右边框颜色为黑色
        numberstyle.setBorderTop(BorderStyle.THIN); // 上边框粗线
        numberstyle.setTopBorderColor(IndexedColors.BLACK.getIndex()); // 上边框颜色为黑色
        numberstyle.setAlignment(HorizontalAlignment.CENTER); // 水平对齐方式为左对齐
        numberstyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直对齐方式为居中
        numberstyle.setWrapText(true);//允许自动换行



        //数据填充基础表头
        XSSFRow headerRow = sheet.createRow(0);
        XSSFCell cell = headerRow.createCell(0);
        cell.setCellValue("");
        cell.setCellStyle(headerStyle);
        //编号
//        headerStyle = createHeaderStyle(sheet1.getWorkbook());
        //编号填充基础表头
//        XSSFRow headerRow1 = sheet1.createRow(0);
//        XSSFCell cell1 = headerRow1.createCell(0);
//        cell1.setCellValue("");
//        cell1.setCellStyle(headerStyle);
        //确定指标和单位是否要显示
//        int NoteCount=0;
//        int UnitCount=0;
//        for(TYjyForm item:idFormList.values())
//        {
//            if(item.getFormNote()!=null)
//            {
//                NoteCount=1;
//            }
//            if(item.getUnit()!=null)
//            {
//                UnitCount=1;
//            }
//            if(NoteCount!=0&&UnitCount!=0)
//            {
//                break;
//            }
//        }
        //
        int max=1;
        String lastdate="";
        for(TYjyDimensionalityRoot item:exportlist)
        {
            String[] dList = item.getLabel().split("//");
            if (max < dList.length)
            {
                max = dList.length;
            }
        }
        //此处插入标准格式内容
        int headtitle=1;
        while(headtitle<max)
        {
            cell = headerRow.createCell(headtitle);
            cell.setCellStyle(headerStyle);
            headtitle=headtitle+1;
        }


//        cell=headerRow.createCell(max);
//        cell.setCellStyle(headerStyle);
//        cell.setCellValue("问题");
//
//        int maxadd=1;
//
//        if(NoteCount>0)
//        {
//            cell=headerRow.createCell(max+maxadd);
//            cell.setCellStyle(headerStyle);
//            cell.setCellValue("指标");
//            maxadd=maxadd+1;
//        }
//
//        if(UnitCount>0)
//        {
//            cell=headerRow.createCell(max+maxadd);
//            cell.setCellStyle(headerStyle);
//            cell.setCellValue("单位");
//            maxadd=maxadd+1;
//        }

//        int count=max+maxadd;//此处之后为需要处理的数据 max+1 为维度加基础原因
        int count=max;//此处之后为需要处理的数据 max+1 为维度加基础原因
        //获取时间段函数
        List<String> TimeList= getHeaderTitlesWithConnect(frequency,startDate,endDate);
        //开始的地方
        //改成标题
        for(String item:TimeList)
        {
            for(int i=0;i<mouthTitleList.size();i++)
            {
                lastdate=item;
                cell=headerRow.createCell(count+i);
                cell.setCellStyle(headerStyle);
                cell.setCellValue(item);
            }
            count=count+mouthTitleList.size();
        }

        for(String item:titleList)
        {
            cell=headerRow.createCell(count);
            cell.setCellStyle(headerStyle);
            cell.setCellValue(item);
            count=count+1;
        }
//        sheet.autoSizeColumn(0);
        // 第二行加入重复的列表示
        int dealtitle=1;
        int dealcount=max;
        headerRow = sheet.createRow(dealtitle);
        for(int i=0;i<max;i++)
        {
            cell=headerRow.createCell(i);
            cell.setCellStyle(headerStyle);
        }
        for(String item:TimeList)
        {
            for(String insertItem:mouthTitleList)
            {
                cell=headerRow.createCell(dealcount);
                cell.setCellStyle(headerStyle);
                cell.setCellValue(insertItem);
                dealcount=dealcount+1;
            }
        }
        //前面一行需要插入更多的
        int begin=2;//从第二行开始
        List<List<String>> dataList=new ArrayList<>();
        for(TYjyDimensionalityRoot item:exportlist)
        {
            int morecount = max;
            //此处发生变化，需要一次性将一行全部输入进去
            String[] dList = item.getLabel().split("//");
            for (TYjyForm formitem : item.getFormList())
            {
                headerRow = sheet.createRow(begin);
                //新版数据处理
                List<String> newDataList = new ArrayList<>();
                for (int di = 0; di < morecount; di++)
                {
                    cell = headerRow.createCell(di);
                    if (di < dList.length)
                    {
                        cell.setCellValue(dList[di]);
                        newDataList.add(dList[di]);
                    }
                    else
                    {
                        newDataList.add("");
                    }
                    cell.setCellStyle(wordstyle);
                }
                dataList.add(newDataList);
                //再按照时间填入数据
                for (String timeItem : TimeList)
                {
                    TYjyForm insertItem =null;
                    if( fcDateFormList.get(timeItem)!=null)
                    {
                         insertItem = fcDateFormList.get(timeItem).get(formitem.getId());
                    }
//                    int mouth = 0;
                    for (String mouthItem : mouthTitleList)
                    {
                        cell = headerRow.createCell(morecount);
                        if (insertItem == null)
                        {
                            cell.setCellStyle(numberstyle);
                        }
                        else
                        {
                            if (insertItem.getFormQuestionExport().equals(mouthItem))
                            {
                                if (insertItem.getFormType().equals("0") || insertItem.getFormType().equals("1"))
                                {
                                    cell.setCellStyle(numberstyle);
                                    if (insertItem.getFormValue() != null)
                                    {
                                        if (!insertItem.getFormValue().equals(""))
                                        {
                                            cell.setCellValue(Double.valueOf(insertItem.getFormValue()));
                                        }
                                        else
                                        {
                                            cell.setCellValue(insertItem.getFormValue());
                                        }

                                    }
                                    else
                                    {
                                        cell.setCellValue(insertItem.getFormValue());
                                    }
                                }
                                else
                                {
                                    cell.setCellStyle(wordstyle);
                                    cell.setCellValue(insertItem.getFormValue());
                                }
                                if (insertItem.getConnectId() != null)
                                {
                                    insertItem = fcDateFormList.get(timeItem).get(insertItem.getConnectId());
                                }
                                else
                                {
                                    insertItem = null;
                                }
                            }
                            else
                            {
                                cell.setCellStyle(numberstyle);
                            }

                        }
//                        mouth = mouth + 1;
                        morecount = morecount + 1;
                    }
                    //最后填入收尾的数据
                    if (insertItem != null && insertItem.getConnectType().equals("2")) {
                        TYjyForm Lastshow = idFormList.get(formitem.getConnectId());
                        //此处注入新的内容
                        while (Lastshow != null) {
                            int pathCount = 0;
                            for (String itemCount : titleList) {
                                cell = headerRow.createCell(morecount + pathCount);
                                if (Lastshow != null) {
                                    if (itemCount.equals(Lastshow.getFormQuestionExport())) {
                                        TYjyForm insertItem1 = fcDateFormList.get(lastdate).get(Lastshow.getId());
                                        if (insertItem1 != null) {
                                            if (insertItem1.getFormType().equals("0") || insertItem1.getFormType().equals("1")) {
                                                cell.setCellStyle(numberstyle);
                                                if (insertItem1.getFormValue() != null) {
                                                    if (!insertItem1.getFormValue().equals("")) {
                                                        cell.setCellValue(Double.valueOf(insertItem1.getFormValue()));
                                                    } else {
                                                        cell.setCellValue(insertItem1.getFormValue());
                                                    }

                                                } else {
                                                    cell.setCellValue(insertItem1.getFormValue());
                                                }
                                            } else {
                                                cell.setCellStyle(wordstyle);
                                                cell.setCellValue(insertItem1.getFormValue());
                                            }
                                        } else {
                                            cell.setCellStyle(wordstyle);
                                        }

                                        if (Lastshow.getConnectId() != null) {
                                            Lastshow = idFormList.get(Lastshow.getConnectId());
                                        } else {
                                            Lastshow = null;
                                        }
                                    } else {
                                        cell.setCellStyle(wordstyle);
                                    }
                                } else {
                                    cell.setCellStyle(wordstyle);
                                }
                                pathCount = pathCount + 1;
                            }
                        }
                    }
                    else
                    {
                        int pathCount = 0;
                        for (String itemCount : titleList)
                        {
                            cell = headerRow.createCell(morecount + pathCount);
                            cell.setCellStyle(wordstyle);
                            pathCount = pathCount + 1;
                        }
                    }
                }
                begin = begin + 1;
                morecount = max;
            }
        }
        mergeAlone(sheet,dataList,dataList.size(),max,2);
        autoSizeColumns(sheet,max+TimeList.size()*mouthTitleList.size()+titleList.size(),true);
        mergeHen(sheet,TimeList.size(),mouthTitleList.size(),max,0);

        sheet = workbook.createSheet(timeStr+"配置");
        headerStyle = createHeaderStyle(sheet.getWorkbook());
        //填充基础表头
        headerRow = sheet.createRow(0);
        cell = headerRow.createCell(0);
        cell.setCellValue(max+1);
        cell.setCellStyle(headerStyle);
        cell = headerRow.createCell(1);
        cell.setCellValue(titleList.size());
        cell.setCellStyle(headerStyle);
        cell = headerRow.createCell(2);
        cell.setCellValue(path);
        cell.setCellStyle(headerStyle);
    }

    public static void autoSizeColumns(XSSFSheet sheet, int numOfColumns, boolean includeHeader) {
        // 为每列计算最大宽度
        int[] maxColumnWidth = new int[numOfColumns];

        // 检查标题行
        if (includeHeader) {
            Row headerRow = sheet.getRow(0);
            if (headerRow != null) {
                for (int col = 0; col < numOfColumns; col++) {

                    Cell cell = headerRow.getCell(col);
                    if (cell != null) {
                        String value = cell.getStringCellValue();
                        int length = value.length();
                        if (length > maxColumnWidth[col]) {
                            if(col==0)
                            {
                                maxColumnWidth[col] = length;
                            }
                            else
                            {
                                maxColumnWidth[col] = ((length*4)/2);
                            }

                        }
                    }
                }
            }
        }

        // 检查数据行
        for (int rowNum = includeHeader ? 1 : 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
            Row row = sheet.getRow(rowNum);
            if (row != null) {
                for (int col = 0; col < numOfColumns; col++) {
                    Cell cell = row.getCell(col);
                    int length = 0;
                    if (cell != null) {
                        String value;
                        switch (cell.getCellType()) {
                            case STRING:
                                value = cell.getStringCellValue();
//                                length = (value.length()*2);
                                length = calculateSpecialLength(value).intValue();
                                break;
                            case NUMERIC:
                                if (DateUtil.isCellDateFormatted(cell)) {
                                    value = cell.getDateCellValue().toString();
                                    length = value.length();
                                } else {
                                    value = String.valueOf(cell.getNumericCellValue());
                                    length = value.length();
                                }
                                break;
                            case BOOLEAN:
                                value = String.valueOf(cell.getBooleanCellValue());
                                length = value.length();
                                break;
                            case FORMULA:
                                value = cell.getCellFormula();
                                length = value.length();
                                break;
                            default:
//                                value = "";
//                                length = value.length();
                                DataFormatter df = new DataFormatter();
                                value = df.formatCellValue(cell);
                                length = calculateSpecialLength(value).intValue();
                        }
                        if (length > maxColumnWidth[col]) {
//                            if(length>255)
//                            {
//                                length=255;
//                            }
                            maxColumnWidth[col] = length;

                        }
                    }
                }
            }
        }

        // 应用列宽设置（添加额外空间）
        for (int col = 0; col < numOfColumns; col++) {
            // 计算列宽（每个字符约256单位，添加额外20%空间）
//            int columnWidth = (int) Math.min(maxColumnWidth[col] * 256 * 1.3, 50 * 256);
            int columnWidth=12;
            if(maxColumnWidth[col]>12)
            {
                if(maxColumnWidth[col]>24)
                {
                    if(maxColumnWidth[col]>36)
                    {
                        columnWidth=48;
                    }
                    else
                    {
                        columnWidth=36;
                    }
                }
                else
                {
                    columnWidth=24;
                }
            }
            sheet.setColumnWidth(col, columnWidth* 256);
        }
    }

    public static Double calculateSpecialLength(String text) {
        String normalizedText = Normalizer.normalize(text, Normalizer.Form.NFC); // NFC标准化形式
        double totalLength = 0;
        for (char ch : normalizedText.toCharArray()) {
            if ((ch >= 0x4e00 && ch <= 0x9fa5) || (ch >= 0x3400 && ch <= 0x4DBF)) {
                totalLength += 1;
            } else if ((ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') ||
                    (ch >= '0' && ch <= '9') || "-,!@#$%^&*()".indexOf(ch) != -1) {
                totalLength += 0.5;
            }else
            {
                totalLength += 1;// 其他字符可以根据需要处理或忽略不计入长度
            }
        }
        return totalLength;
    }
    public void readExcelBySheetSpecial(MultipartFile file) throws IOException{
        Map<String, List<JSONObject>> result = new HashMap<>();


        try (InputStream is = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(is)) { // 自动识别 .xls/.xlsx[3,7](@ref)
            DataFormatter formatter = new DataFormatter();
            Sheet date=null;
            Sheet id=null;
            Sheet config=null;
            int count=1;
            int numcount=1;
            String fcDate="";
            String path="";
            // 遍历所有 Sheet 直接拆了完事
            for (Sheet sheet : workbook)
            {
                String sheetName = sheet.getSheetName();
                if(sheetName.equals("数据"))
                {
                    date=sheet;
                    Iterator<Row> rowIterator = sheet.iterator();
                    // 读取列头
                    Row headerRow = rowIterator.next();//获取日期和距离
                    fcDate=formatter.formatCellValue(headerRow.getCell(0));
                }
                if(sheetName.equals("编号"))
                {
                    id=sheet;
                }
                if(sheetName.equals("配置"))
                {
                    config=sheet;
                    Iterator<Row> rowIterator = sheet.iterator();
                    Row headerRow = rowIterator.next();//获取日期和距离
                    count=Integer.valueOf(formatter.formatCellValue(headerRow.getCell(0)));
                    numcount=Integer.valueOf(formatter.formatCellValue(headerRow.getCell(1)));
                    path=formatter.formatCellValue(headerRow.getCell(2));
                }
            }

            TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
            tYjyFormAnswer.setWorkNo(SecurityUtils.getUsername());
            tYjyFormAnswer.setDimensionalityPath(path);
            tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
            tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
            tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
            tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
            tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
            tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
            List<TYjyForm> formList=tYjyFormMapper.selectFormListForSubmit(tYjyFormAnswer);

            TYjyDept searchdept=new TYjyDept();
            List<TYjyDept> deptList=tYjyDeptMapper.selectTYjyDeptList(searchdept);
            Map<String,String> deptListmap=new HashMap<>();
            for(TYjyDept deptitem:deptList)
            {
                deptListmap.put(deptitem.getPath(),deptitem.getDeptName());
            }

            Map<Long,TYjyForm> dealmap=new HashMap<>();
            for(TYjyForm item:formList)
            {
                dealmap.put(item.getId(),item);
            }
            Iterator<Row> daterow = date.iterator();
            Iterator<Row> idrow = id.iterator();
            Row dateBegin = daterow.next();//获取日期和距离
            Row idBegin = idrow.next();//获取日期和距离
            TYjyForm checkitem=null;
            SysUser user= SecurityUtils.getLoginUser().getUser();

            while (daterow.hasNext()) {
                dateBegin=daterow.next();
                idBegin=idrow.next();
                for(int i=0;i<numcount;i++)
                {
                    TYjyAnswer newadd=new TYjyAnswer();
                    String strFormId=formatter.formatCellValue(idBegin.getCell(count+i));
                    if(strFormId==null||strFormId.equals(""))
                    {
                        continue;
                    }
                    newadd.setFormId(Long.valueOf(formatter.formatCellValue(idBegin.getCell(count+i))));
                    newadd.setFcDate(fcDate);
                    if(dealmap.containsKey(newadd.getFormId()))
                    {
                        checkitem=dealmap.get(newadd.getFormId());
                    }
                    else
                    {
                        continue;
                    }

                    if(checkitem.getRuleType()!=null)
                    {
                        if(checkitem.getRuleType().equals("11"))
                        {
                            continue;
                        }
                    }
                    if(checkitem.getFormType().equals("1"))
                    {
                        String deal=formatter.formatCellValue(dateBegin.getCell(count+i));
                        if(deal.trim().equals(""))
                        {
                            newadd.setFormValue(deal);
                        }
                        else
                        {
//                            DataFormatter df = new DataFormatter();
//                            //此处需要重新测试
//                            newadd.setFormValue(getNumericCellValue(dateBegin.getCell(count+i),df));
                            BigDecimal decimalValue = BigDecimal.valueOf(dateBegin.getCell(count+i).getNumericCellValue());
                            newadd.setFormValue(decimalValue.stripTrailingZeros().toPlainString());
                        }

                    }
                    else
                    {
                        newadd.setFormValue(formatter.formatCellValue(dateBegin.getCell(count+i)));
                    }
                    if(newadd.getFormValue().trim().equals(""))
                    {
                        if(checkitem.getFormValue()!=null && newadd.getFormValue().trim().equals(""))
                        {
                            //此处应对旧数据进行删除，此处是新加的功能，还有数据累计的问题需要考虑如何处理
                            tYjyAnswerService.deleteTYjyAnswerById(checkitem.getAnswerId());
                        }
                        continue;
                    }
                    else
                    {
                        if(checkitem.getFormValue()!=null)
                        {
                            if(checkitem.getStatus().equals("2"))
                            {
                                if(checkitem.getFormValue().equals(newadd.getFormValue()))
                                {
                                    continue;
                                }
                            }
                        }
                        tYjyAnswerService.branceInsert(newadd,checkitem,user.getUserName(),user.getNickName(),fcDate,deptListmap);
//                        else
//                        {
//                              //需要修正的数值太多了，建议重新进行整合处理，暂时先放弃，再重新搞一遍数据
//                            tYjyAnswerService.branceInsert(newadd,checkitem,user.getUserName(),user.getNickName(),fcDate,deptListmap);
////                            tYjyAnswerService.finalinsert(newadd,checkitem,user,fcDate);
////                            tYjyAnswerService.finalinsert(newadd,checkitem,user,fcDate);
////                            tYjyAnswerService.adddeal(newadd,user,checkitem);
//                            //tYjyAnswerService.dealaddalone(newadd);//此处应该考虑如何批量的进行导入,感觉又要大规模的重写数据了，让人无语啊！！！！
//                        }
                    }
                }
            }

        } catch (IOException | EncryptedDocumentException e) {
            throw new RuntimeException("Excel 解析失败：" + e.getMessage());
        }
        return ;
    }

    //导出格式固定，且分情况展示的列表,可以展示多个月的情况
    @Override
    public void exportTwice(HttpServletResponse response, String startDate, String endDate, Long rootId,String type) throws IOException {

        TYjyDimensionality tYjyDimensionality=new TYjyDimensionality();
        List<TYjyDimensionality> list=new ArrayList<>();
        String workNo=SecurityUtils.getUsername();
        if(type.equals("0"))
        {
            tYjyDimensionality.setCreateBy(workNo);
            tYjyDimensionality.setPath(rootId.toString()+",");
            list = getALLRootForAnswer(tYjyDimensionality);
        }
        if(type.equals("1"))
        {
            tYjyDimensionality=selectTYjyDimensionalityById(rootId);
            list = getByRootId(tYjyDimensionality);
        }

        List<TYjyDimensionalityRoot> dimensionalityRootList=new ArrayList<>();
        Map<Long,TYjyDimensionalityRoot> root=new HashMap<>();
        Map<Long,Map<Long,List<TYjyDimensionalityRoot>>> map=new HashMap<>();
        //对维度进行排序
        for(TYjyDimensionality item:list)
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            add.setDeptName(item.getDeptName());
            add.setDeptCode(item.getDeptCode());
            if(item.getParentId()==null)
            {
                root.put(item.getId(),add);
                Map<Long,List<TYjyDimensionalityRoot>> rootList=new HashMap<>();
                map.put(item.getId(),rootList);
            }
        }
        for(TYjyDimensionality item:list)//维度整理方法保持不变
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            add.setDeptName(item.getDeptName());
            add.setDeptCode(item.getDeptCode());
            if(item.getParentId()!=null)
            {
                if(map.get(Long.valueOf(item.getPath().split(",")[0])).containsKey(item.getParentId()))
                {
                    map.get(Long.valueOf(item.getPath().split(",")[0])).get(item.getParentId()).add(add);
                }
                else
                {
                    List<TYjyDimensionalityRoot> insert=new ArrayList<>();
                    insert.add(add);
                    map.get(Long.valueOf(item.getPath().split(",")[0])).put(item.getParentId(),insert);
                }
            }
        }
        for(Long item:root.keySet())
        {
            dimensionalityRootList.add(getTree(root.get(item),map.get(item)));
        }
        //获取所有的form信息及其值 可以简单粗暴些
        TYjyDimensionalityRoot finalroot=dimensionalityRootList.get(0);

        TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
        tYjyFormAnswer.setDimensionalityPath(rootId.toString()+",");
        tYjyFormAnswer.setFcDate0(dealfcdate(startDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(startDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(startDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(startDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(startDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(startDate,"5"));
        tYjyFormAnswer.setFcDate00(dealfcdate(endDate,"0"));
        tYjyFormAnswer.setFcDate11(dealfcdate(endDate,"1"));
        tYjyFormAnswer.setFcDate22(dealfcdate(endDate,"2"));
        tYjyFormAnswer.setFcDate33(dealfcdate(endDate,"3"));
        tYjyFormAnswer.setFcDate44(dealfcdate(endDate,"4"));
        tYjyFormAnswer.setFcDate55(dealfcdate(endDate,"5"));
        List<TYjyForm> formList=new ArrayList<>();

        if(type.equals("0"))
        {
            tYjyFormAnswer.setWorkNo(workNo);
            formList=tYjyFormMapper.selectExportListForSubmit(tYjyFormAnswer);
        }
        if(type.equals("1"))
        {
            formList=tYjyFormMapper.selectExportListForAdmin(tYjyFormAnswer);
        }
        //根据时间进行 保存
        //根据dimensionalityId 和连接状态进行保存
        //根据 挂靠情况进行保存
        //根据 form_id进行保存
        Map<String,Map<Long,TYjyForm>> fcDateFormList=new HashMap<>();//按照天数进行保存

        Map<Long,TYjyForm> idFormList=new HashMap<>();//按照id进行保存，只用保存一次

        Map<Long,List<TYjyForm>> dealformroot=new HashMap<>();//维度结构辅助处理，只用保存一次

        List<String> titleList=new ArrayList<>();//后缀部分标题，只用保存一次


        //此处需要调整
        for(TYjyForm item:formList)//此处修改
        {
            if(fcDateFormList.containsKey(item.getFcDate()))
            {
                fcDateFormList.get(item.getFcDate()).put(item.getId(),item);
            }
            else
            {
                Map<Long,TYjyForm> newMap=new HashMap<>();
                newMap.put(item.getId(),item);
                fcDateFormList.put(item.getFcDate(),newMap);
            }

            if(!idFormList.containsKey(item.getId()))
            {
                idFormList.put(item.getId(),item);
                if(item.getConnectType().equals("0"))
                {
                    if(dealformroot.containsKey(item.getDimensionalityId()))
                    {
                        dealformroot.get(item.getDimensionalityId()).add(item);
                    }
                    else
                    {
                        List<TYjyForm> newlist=new ArrayList<>();
                        newlist.add(item);
                        dealformroot.put(item.getDimensionalityId(),newlist);
                    }
                }
                else
                {
                    if(item.getConnectType().equals("2"))
                    {
                        if(!titleList.contains(item.getFormQuestionExport()))
                        {
                            titleList.add(item.getFormQuestionExport());
                        }
                    }
                }
            }

//            if(!titleList.contains(item.getFormQuestion().trim().replace(")","").replace("("," ").split(" ")[1]))
//            {
//                //formQuestion 需要进一步的规范化，让世界感受痛苦吧//这里也要考虑显示的效果啊
//                titleList.add(item.getFormQuestion().trim().replace(")","").replace("("," ").split(" ")[1]);
//            }

        }
        dealexportroot(finalroot,dealformroot,"",0);
        List<TYjyDimensionalityRoot> exportlist=new ArrayList<>();
        dealexportlist(finalroot,exportlist);
//        int test=1;
//        List<TYjyDimensionalityRoot> allLeaves = this.getAllLeaves(dimensionalityRoot);
//        TYjyForm tYjyForm =new TYjyForm();
//        tYjyForm.setDimensionalityId(rootId);
//        tYjyForm.setWorkNo(SecurityUtils.getUsername());
//        List<Long> ids = SpringUtils.getBean(TYjyFormMapper.class).selectTYjyFormListForAnswer(tYjyForm).stream().map(x->x.getId()).collect(Collectors.toList());


//        List<TYjyExport> forms = answerService.selectExportFormListByFormId(ids);
//        List<TYjyExport> exports = answerService.selectExportListByFormId(ids, startDate, endDate);
//
//        forms.forEach(item ->{
//            TYjyDimensionalityRoot rootD = allLeaves.stream().filter(x -> x.getValue().equals(item.getDimensionalityId())).findFirst().orElse(null);
//            if(Objects.nonNull(rootD))item.setLabels(rootD.getLabels());
//            else item.setLabels(new ArrayList<>());
//        });
//        exports.forEach(item ->{
//            item.setDateDesc(dataReportFrequency.getDateTemp(item.getFrequency(),item.getFcDate()));
//            TYjyExport tYjyExport = forms.stream().filter(x -> x.getFormId().equals(item.getFormId())).findFirst().orElse(null);
//            if(Objects.nonNull(tYjyExport)) item.setLabels(tYjyExport.getLabels());
//            else item.setLabels(new ArrayList<>());
//        });


        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        ServletOutputStream out = response.getOutputStream();
        XSSFWorkbook wb = new XSSFWorkbook();
        try {


            this.specialSheetsExcelWithConnect( wb, exportlist,titleList,fcDateFormList,idFormList,startDate,endDate,rootId.toString()+",");

//            if(rootId.toString().equals("1059"))
//            {
//                this.specialSheetsExcelWithNote( wb, exportlist, titleList,fcDate,rootId.toString()+",");
//            }
//            else
//            {
//                this.specialSheetsExcel( wb, exportlist, titleList,fcDate,rootId.toString()+",");
//            }
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
            IOUtils.closeQuietly(out);
        }

    }

    //导出格式固定，且分情况展示的列表,可以展示多个月的情况
    @Override
    public void exportThird(HttpServletResponse response, String startDate, String endDate, Long rootId,String type) throws IOException {

        TYjyDimensionality tYjyDimensionality=new TYjyDimensionality();
        List<TYjyDimensionality> list=new ArrayList<>();
        String workNo=SecurityUtils.getUsername();
        if(type.equals("0"))
        {
            tYjyDimensionality.setCreateBy(workNo);
            tYjyDimensionality.setPath(rootId.toString()+",");
            list = getALLRootForAnswer(tYjyDimensionality);
        }
        if(type.equals("1"))
        {
            tYjyDimensionality=selectTYjyDimensionalityById(rootId);
            list = getByRootId(tYjyDimensionality);
        }

        List<TYjyDimensionalityRoot> dimensionalityRootList=new ArrayList<>();
        Map<Long,TYjyDimensionalityRoot> root=new HashMap<>();
        Map<Long,Map<Long,List<TYjyDimensionalityRoot>>> map=new HashMap<>();
        //对维度进行排序
        for(TYjyDimensionality item:list)
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            add.setDeptName(item.getDeptName());
            add.setDeptCode(item.getDeptCode());
            if(item.getParentId()==null)
            {
                root.put(item.getId(),add);
                Map<Long,List<TYjyDimensionalityRoot>> rootList=new HashMap<>();
                map.put(item.getId(),rootList);
            }
        }
        for(TYjyDimensionality item:list)//维度整理方法保持不变
        {
            TYjyDimensionalityRoot add=new TYjyDimensionalityRoot();
            add.setValue(item.getId());
            add.setLabel(item.getDimensionalityName());
            add.setIsUse(item.getIsUse());
            add.setDeptName(item.getDeptName());
            add.setDeptCode(item.getDeptCode());
            if(item.getParentId()!=null)
            {
                if(map.get(Long.valueOf(item.getPath().split(",")[0])).containsKey(item.getParentId()))
                {
                    map.get(Long.valueOf(item.getPath().split(",")[0])).get(item.getParentId()).add(add);
                }
                else
                {
                    List<TYjyDimensionalityRoot> insert=new ArrayList<>();
                    insert.add(add);
                    map.get(Long.valueOf(item.getPath().split(",")[0])).put(item.getParentId(),insert);
                }
            }
        }
        for(Long item:root.keySet())
        {
            dimensionalityRootList.add(getTree(root.get(item),map.get(item)));
        }
        //获取所有的form信息及其值 可以简单粗暴些
        TYjyDimensionalityRoot finalroot=dimensionalityRootList.get(0);
        TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
        tYjyFormAnswer.setDimensionalityPath(rootId.toString()+",");
        tYjyFormAnswer.setFcDate0(dealfcdate(startDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(startDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(startDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(startDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(startDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(startDate,"5"));
        tYjyFormAnswer.setFcDate00(dealfcdate(endDate,"0"));
        tYjyFormAnswer.setFcDate11(dealfcdate(endDate,"1"));
        tYjyFormAnswer.setFcDate22(dealfcdate(endDate,"2"));
        tYjyFormAnswer.setFcDate33(dealfcdate(endDate,"3"));
        tYjyFormAnswer.setFcDate44(dealfcdate(endDate,"4"));
        tYjyFormAnswer.setFcDate55(dealfcdate(endDate,"5"));
        List<TYjyForm> formList=new ArrayList<>();
        if(type.equals("0"))
        {
            tYjyFormAnswer.setWorkNo(workNo);
            formList=tYjyFormMapper.selectExportListForSubmit(tYjyFormAnswer);
        }
        if(type.equals("1"))
        {
            formList=tYjyFormMapper.selectExportListForAdmin(tYjyFormAnswer);
        }
        Map<String,Map<Long,TYjyForm>> fcDateFormList=new HashMap<>();//按照天数进行保存
        Map<Long,TYjyForm> idFormList=new HashMap<>();//按照id进行保存，只用保存一次
        Map<Long,List<TYjyForm>> dealformroot=new HashMap<>();//维度结构辅助处理，只用保存一次
        List<String> titleList=new ArrayList<>();//后缀部分标题，只用保存一次

        List<String> mouthTitleList=new ArrayList<>();//每个月都要展示的内容，需要进行深入的思考
        //此处需要调整
        for(TYjyForm item:formList)//此处修改
        {
            if(fcDateFormList.containsKey(item.getFcDate()))
            {
                fcDateFormList.get(item.getFcDate()).put(item.getId(),item);
            }
            else
            {
                Map<Long,TYjyForm> newMap=new HashMap<>();
                newMap.put(item.getId(),item);
                fcDateFormList.put(item.getFcDate(),newMap);
            }
            if(!idFormList.containsKey(item.getId()))
            {
                idFormList.put(item.getId(),item);
                if(item.getConnectType().equals("0"))
                {
                    if(dealformroot.containsKey(item.getDimensionalityId()))
                    {
                        dealformroot.get(item.getDimensionalityId()).add(item);
                    }
                    else
                    {
                        List<TYjyForm> newlist=new ArrayList<>();
                        newlist.add(item);
                        dealformroot.put(item.getDimensionalityId(),newlist);
                    }
                    if(!mouthTitleList.contains(item.getFormQuestionExport()))
                    {
                        mouthTitleList.add(item.getFormQuestionExport());
                    }

                }
                else
                {
                    if(item.getConnectType().equals("1"))
                    {
                        if(!mouthTitleList.contains(item.getFormQuestionExport()))
                        {
                            mouthTitleList.add(item.getFormQuestionExport());
                        }
                    }
                    if(item.getConnectType().equals("2"))
                    {
                        if(!titleList.contains(item.getFormQuestionExport()))
                        {
                            titleList.add(item.getFormQuestionExport());
                        }
                    }
                }
            }
        }
        dealexportroot(finalroot,dealformroot,"",0);
        List<TYjyDimensionalityRoot> exportlist=new ArrayList<>();
        dealexportlist(finalroot,exportlist);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        ServletOutputStream out = response.getOutputStream();
        XSSFWorkbook wb = new XSSFWorkbook();
        try {
            this.specialSheetsExcelWithMouth( wb, exportlist,mouthTitleList,titleList,fcDateFormList,idFormList,startDate,endDate,rootId.toString()+",");
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
            IOUtils.closeQuietly(out);
        }

    }

    @Override
    public List<TYjyDimensionality> selectDeadlineForDimensionality(TYjyDimensionality tYjyDimensionality)
    {
        return tYjyDimensionalityMapper.selectDeadlineForDimensionality(tYjyDimensionality);
    }

    @Override
    public List<TYjyDimensionality> selectDeadlineForDimensionalityForAdmin(TYjyDimensionality tYjyDimensionality)
    {
        return tYjyDimensionalityMapper.selectDeadlineForDimensionalityForAdmin(tYjyDimensionality);
    }

    @Override
    public List<TYjyDimensionality> selectDeadlineForDimensionalityForUser(TYjyDimensionality tYjyDimensionality)
    {
        return tYjyDimensionalityMapper.selectDeadlineForDimensionalityForUser(tYjyDimensionality);
    }

    @Autowired
    private ITemplateFileService templateFileService;

    @Override
    public void exportTemplate(HttpServletResponse response, String fcDate, Long rootId,String type,String isUpdate) throws IOException {

        String workNo=SecurityUtils.getUsername();
        TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
        tYjyFormAnswer.setDimensionalityPath(rootId.toString()+",");
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        List<TYjyForm> formList=new ArrayList<>();
        if(type.equals("0"))
        {
            tYjyFormAnswer.setWorkNo(workNo);
            formList=tYjyFormMapper.selectFormListForSubmit(tYjyFormAnswer);
        }
        if(type.equals("1"))
        {
            formList=tYjyFormMapper.selectFormListForAdminNoCount(tYjyFormAnswer);
        }
        Map<String,TYjyForm> formMap=new HashedMap();
        for(TYjyForm item:formList)//此处修改
        {
            formMap.put(item.getId().toString(),item);
        }

        tYjyFormAnswer  = new TYjyFormAnswer();
        tYjyFormAnswer.setId(Long.valueOf("2504"));
        tYjyFormAnswer.setDimensionalityPath(rootId.toString()+",");
        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
        tYjyFormAnswer.setFcDate00(dealfcdate(fcDate,"0"));
        tYjyFormAnswer.setFcDate11(dealfcdate(fcDate,"1"));
        tYjyFormAnswer.setFcDate22(dealfcdate(fcDate,"2"));
        tYjyFormAnswer.setFcDate33(dealfcdate(fcDate,"3"));
        tYjyFormAnswer.setFcDate44(dealfcdate(fcDate,"4"));
        tYjyFormAnswer.setFcDate55(dealfcdate(fcDate,"5"));
        List<TYjyForm> sheetList=new ArrayList<>();
        if(type.equals("0"))
        {
            tYjyFormAnswer.setWorkNo(workNo);
            sheetList=tYjyFormMapper.selectExportListForSubmit(tYjyFormAnswer);
        }
        if(type.equals("1"))
        {
            sheetList=tYjyFormMapper.selectExportListForAdmin(tYjyFormAnswer);
        }
        Map<String,TYjyForm> sheetMap=new HashedMap();
        for(TYjyForm item:sheetList)//此处修改
        {
            sheetMap.put(item.getFcDate(),item);
        }

        TemplateFile template = templateFileService.selectTemplateFileById(124);
        ServletOutputStream out = response.getOutputStream();
        Workbook wb=null;
        try {
            wb=ExportFileTemplate(formMap,sheetMap,template,dealfcdate(fcDate,"1"));
            if(isUpdate!=null)
            {
                if(isUpdate.equals("1"))
                {
                    calculateFormulas(wb);
                }
            }
            wb.setForceFormulaRecalculation(true);
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
            IOUtils.closeQuietly(out);
        }

    }

    @Override
    public void exportTemplate1(HttpServletResponse response, String startDate, String endDate, Long rootId,String type,String isUpdate) throws IOException {

        String workNo=SecurityUtils.getUsername();
        TYjyFormAnswer tYjyFormAnswer  =new TYjyFormAnswer();
        tYjyFormAnswer.setDimensionalityPath(rootId.toString()+",");
        tYjyFormAnswer.setFcDate0(dealfcdate(startDate,"0"));
        tYjyFormAnswer.setFcDate1(dealfcdate(startDate,"1"));
        tYjyFormAnswer.setFcDate2(dealfcdate(startDate,"2"));
        tYjyFormAnswer.setFcDate3(dealfcdate(startDate,"3"));
        tYjyFormAnswer.setFcDate4(dealfcdate(startDate,"4"));
        tYjyFormAnswer.setFcDate5(dealfcdate(startDate,"5"));
        tYjyFormAnswer.setFcDate00(dealfcdate(endDate,"0"));
        tYjyFormAnswer.setFcDate11(dealfcdate(endDate,"1"));
        tYjyFormAnswer.setFcDate22(dealfcdate(endDate,"2"));
        tYjyFormAnswer.setFcDate33(dealfcdate(endDate,"3"));
        tYjyFormAnswer.setFcDate44(dealfcdate(endDate,"4"));
        tYjyFormAnswer.setFcDate55(dealfcdate(endDate,"5"));
        List<TYjyForm> formList=new ArrayList<>();
        Map<Long,String> MaxMonth=new HashMap<>();
        if(type.equals("0"))
        {
            tYjyFormAnswer.setWorkNo(workNo);
            formList=tYjyFormMapper.selectExportListForSubmit(tYjyFormAnswer);
        }
        if(type.equals("1"))
        {
            formList=tYjyFormMapper.selectExportListForAdmin(tYjyFormAnswer);
        }
        Map<String,TYjyForm> formMap=new HashedMap();
        for(TYjyForm item:formList)//
        {
            if(item.getFcDate()==null)
            {
                continue;
            }
            formMap.put(item.getId().toString()+"."+item.getFcDate(),item);
            //此处仍然需要思考，思考要不要进一步的优化处理
            if(MaxMonth.containsKey(item.getId()))
            {
                if(item.getFcDate().compareTo(MaxMonth.get(item.getId()))>0)
                {
                    formMap.put(item.getId().toString(),item);
                    MaxMonth.put(item.getId(),item.getFcDate());
                }
            }
            else
            {
                formMap.put(item.getId().toString(),item);
                MaxMonth.put(item.getId(),item.getFcDate());
            }
//            if(item.getFcDate().equals(dealfcdate(endDate,"1")))
//            {
//                formMap.put(item.getId().toString(),item);
//            }
        }
//        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
//        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"1"));
//        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
//        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
//        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
//        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
//        List<TYjyForm> formList=new ArrayList<>();
//        if(type.equals("0"))
//        {
//            tYjyFormAnswer.setWorkNo(workNo);
//            formList=tYjyFormMapper.selectFormListForSubmit(tYjyFormAnswer);
//        }
//        if(type.equals("1"))
//        {
//            formList=tYjyFormMapper.selectFormListForAdminNoCount(tYjyFormAnswer);
//        }
//        Map<String,TYjyForm> formMap=new HashedMap();
//        for(TYjyForm item:formList)//此处修改
//        {
//            formMap.put(item.getId().toString(),item);
//        }
//        Map<String,TYjyForm> sheetMap=new HashedMap();

//        Map<String,TYjyForm> sheetMap=new HashedMap();
//        tYjyFormAnswer  = new TYjyFormAnswer();
//        tYjyFormAnswer.setId(Long.valueOf("2504"));
//        tYjyFormAnswer.setDimensionalityPath(rootId.toString()+",");
//        tYjyFormAnswer.setFcDate0(dealfcdate(fcDate,"0"));
//        tYjyFormAnswer.setFcDate1(dealfcdate(fcDate,"4"));
//        tYjyFormAnswer.setFcDate2(dealfcdate(fcDate,"2"));
//        tYjyFormAnswer.setFcDate3(dealfcdate(fcDate,"3"));
//        tYjyFormAnswer.setFcDate4(dealfcdate(fcDate,"4"));
//        tYjyFormAnswer.setFcDate5(dealfcdate(fcDate,"5"));
//        tYjyFormAnswer.setFcDate00(dealfcdate(fcDate,"0"));
//        tYjyFormAnswer.setFcDate11(dealfcdate(fcDate,"1"));
//        tYjyFormAnswer.setFcDate22(dealfcdate(fcDate,"2"));
//        tYjyFormAnswer.setFcDate33(dealfcdate(fcDate,"3"));
//        tYjyFormAnswer.setFcDate44(dealfcdate(fcDate,"4"));
//        tYjyFormAnswer.setFcDate55(dealfcdate(fcDate,"5"));
//        List<TYjyForm> sheetList=new ArrayList<>();
//        if(type.equals("0"))
//        {
//            tYjyFormAnswer.setWorkNo(workNo);
//            sheetList=tYjyFormMapper.selectExportListForSubmit(tYjyFormAnswer);
//        }
//        if(type.equals("1"))
//        {
//            sheetList=tYjyFormMapper.selectExportListForAdmin(tYjyFormAnswer);
//        }
//
//        for(TYjyForm item:sheetList)//此处修改
//        {
//            sheetMap.put(item.getFcDate(),item);
//        }

        //此处也是需要修改的地方，之后最好全部都是配置成在数据库进行配置的
        TemplateFile template = templateFileService.selectTemplateFileById(126);
        ServletOutputStream out = response.getOutputStream();
        Workbook wb=null;
        try {
            wb=ExportFileTemplate1(formMap,template,dealfcdate(endDate,"1"));
            if(isUpdate!=null)
            {
                if(isUpdate.equals("1"))
                {
                    calculateFormulas(wb);
                }
            }
            wb.setForceFormulaRecalculation(true);
            wb.write(out);
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(wb);
            IOUtils.closeQuietly(out);
        }

    }

    public XSSFWorkbook ExportFileTemplate(Map<String,TYjyForm> formMap,Map<String,TYjyForm> sheetMap,TemplateFile template,String fcDate) throws Exception {
        List<String> TimeList= getHeaderTitlesWithConnect("1",dealfcdate(fcDate,"4"),dealfcdate(fcDate,"1"));
        Map<String,Object> dataMap = new HashMap<>();
        Map<String,JSONObject> jsonMap = new HashMap<>();
        List<String> list=new ArrayList<>();
        dataMap.put("fcDate",fcDate);
        for(String item:formMap.keySet())
        {
            if(formMap.get(item).getFormValue()==null)
            {
                dataMap.put(item,"");
            }
            else
            {
                dataMap.put(item,formMap.get(item).getFormValue());
            }

            JSONObject json=new JSONObject();
            json.put("formType",formMap.get(item).getFormType());
            jsonMap.put(item,json);
        }

        XSSFWorkbook workbook=ExportExcelByTemplateUtils.doExportExcelOneListByTemplateWithSignByQcy(template.getUrl(),dataMap,jsonMap,"steel",list);
        //此处将附件逐个拷贝过去
        Integer mouthNum=1;
        for(String item:TimeList)
        {
            TYjyForm fileItem=sheetMap.get(item);
            if(fileItem==null)
            {
                continue;
            }
            if(fileItem.getFormFile()==null)
            {
                mouthNum=mouthNum+1;
                continue;
            }
            if(fileItem.getFormFile().equals("[]"))
            {
                mouthNum=mouthNum+1;
                continue;
            }
            List<JSONObject> jsonlist = JSONArray.parseArray(fileItem.getFormFile(), JSONObject.class);
            String fileurl=jsonlist.get(0).get("url").toString();
            File fileCopy=ExportExcelByTemplateUtils.doenloadFileByQcy(fileurl);
            InputStream inputStream = new FileInputStream(fileCopy);
            XSSFWorkbook workbookSource = new XSSFWorkbook(inputStream);
            copySheet(workbookSource,workbook,mouthNum.toString()+"月");
            mouthNum=mouthNum+1;
        }

        return workbook;
    }

    public XSSFWorkbook ExportFileTemplate1(Map<String,TYjyForm> formMap,TemplateFile template,String fcDate) throws Exception {
//        List<String> TimeList= getHeaderTitlesWithConnect("1",dealfcdate(fcDate,"4"),dealfcdate(fcDate,"1"));
        Map<String,Object> dataMap = new HashMap<>();
        Map<String,JSONObject> jsonMap = new HashMap<>();
        List<String> list=new ArrayList<>();
        dataMap.put("fcDate",fcDate);
        for(String item:formMap.keySet())
        {
            if(formMap.get(item).getFormValue()==null)
            {
                dataMap.put(item,"");
            }
            else
            {
                dataMap.put(item,formMap.get(item).getFormValue());
            }
            JSONObject json=new JSONObject();
            json.put("formType",formMap.get(item).getFormType());
            jsonMap.put(item,json);
//            if(item.contains("."))
//            {
//                jsonMap.put(item.split(".")[0],json);
//            }
//            else
//            {
//                jsonMap.put(item,json);
//            }


        }
        XSSFWorkbook workbook=ExportExcelByTemplateUtils.doExportExcelOneListByTemplateWithSignByQcy(template.getUrl(),dataMap,jsonMap,"steel",list);

        XSSFSheet sheet = workbook.getSheetAt(0);
        int num=12;
        if(fcDate.length()>=7)
        {
            if(fcDate.substring(5,6).equals("0"))
            {
                num=Integer.valueOf(fcDate.substring(6,7));
            }
            else
            {
                num=Integer.valueOf(fcDate.substring(5,7));
            }
        }
        for(int i=num+3;i<15;i++)
        {
            Row hiderow=sheet.getRow(i);
            hiderow.setZeroHeight(true);
        }


        //此处将附件逐个拷贝过去
//        Integer mouthNum=1;
//        for(String item:TimeList)
//        {
//            TYjyForm fileItem=sheetMap.get(item);
//            if(fileItem==null)
//            {
//                continue;
//            }
//            if(fileItem.getFormFile()==null)
//            {
//                mouthNum=mouthNum+1;
//                continue;
//            }
//            if(fileItem.getFormFile().equals("[]"))
//            {
//                mouthNum=mouthNum+1;
//                continue;
//            }
//            List<JSONObject> jsonlist = JSONArray.parseArray(fileItem.getFormFile(), JSONObject.class);
//            String fileurl=jsonlist.get(0).get("url").toString();
//            File fileCopy=ExportExcelByTemplateUtils.doenloadFileByQcy(fileurl);
//            InputStream inputStream = new FileInputStream(fileCopy);
//            XSSFWorkbook workbookSource = new XSSFWorkbook(inputStream);
//            copySheet(workbookSource,workbook,mouthNum.toString()+"月");
//            mouthNum=mouthNum+1;
//        }

        return workbook;
    }

    public static void copySheet(XSSFWorkbook sourceWorkbook, XSSFWorkbook targetWorkbook,String newSheetName) throws IOException {
            // 3. 获取源Sheet
            XSSFSheet sourceSheet = sourceWorkbook.getSheetAt(0);
            // 4. 在目标工作簿中创建新Sheet
            XSSFSheet newSheet = targetWorkbook.createSheet(newSheetName);
            // 5. 复制单元格内容
            copySheetContent(sourceSheet, newSheet);
    }
    private static void copySheetContent(Sheet sourceSheet, Sheet targetSheet) {
        // 复制列宽
        copyColumnWidths(sourceSheet, targetSheet);

        // 复制行和单元格
        for (int i = 0; i <= sourceSheet.getLastRowNum(); i++) {
            Row sourceRow = sourceSheet.getRow(i);
            if (sourceRow == null) continue;

            Row newRow = targetSheet.createRow(i);
            newRow.setHeight(sourceRow.getHeight());

            // 复制单元格
            for (int j = 0; j < sourceRow.getLastCellNum(); j++) {
                Cell sourceCell = sourceRow.getCell(j);
                if (sourceCell == null) continue;

                Cell newCell = newRow.createCell(j);
                copyCell(sourceCell, newCell);
            }
        }

        // 复制合并区域
        copyMergedRegions(sourceSheet, targetSheet);
    }

    private static void copyCell(Cell sourceCell, Cell targetCell) {
        // 复制单元格样式
        CellStyle newStyle = targetCell.getSheet().getWorkbook().createCellStyle();
        newStyle.cloneStyleFrom(sourceCell.getCellStyle());
        targetCell.setCellStyle(newStyle);

        // 复制单元格值
        switch (sourceCell.getCellType()) {
            case STRING:
                targetCell.setCellValue(sourceCell.getStringCellValue());
                break;
            case NUMERIC:
                targetCell.setCellValue(sourceCell.getNumericCellValue());
                break;
            case BOOLEAN:
                targetCell.setCellValue(sourceCell.getBooleanCellValue());
                break;
            case FORMULA:
                targetCell.setCellFormula(sourceCell.getCellFormula());
                break;
            case BLANK:
                targetCell.setBlank();
                break;
            default:
                targetCell.setCellValue("");
        }
    }

    private static void copyColumnWidths(Sheet sourceSheet, Sheet targetSheet) {
        for (int i = 0; i < sourceSheet.getRow(0).getLastCellNum(); i++) {
            int width = sourceSheet.getColumnWidth(i);
            targetSheet.setColumnWidth(i, width);
        }
    }

    private static void copyMergedRegions(Sheet sourceSheet, Sheet targetSheet) {
        for (int i = 0; i < sourceSheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = sourceSheet.getMergedRegion(i);
            targetSheet.addMergedRegion(mergedRegion);
        }
    }


    public static void calculateFormulas(Workbook workbook) throws IOException {

        // 创建公式计算器
        FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();

        // 遍历所有工作表
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);

            // 遍历工作表中的所有行
            for (Row row : sheet) {
                // 遍历行中的所有单元格
                for (Cell cell : row) {
                    // 如果单元格包含公式，则计算公式并将结果设置为单元格值
                    if (cell.getCellType() == CellType.FORMULA) {
                        CellStyle savestyle=cell.getCellStyle();
//                        evaluator.evaluateFormulaCell(cell);

                        // 获取公式计算结果
                        CellValue cellValue = evaluator.evaluate(cell);
                        short dataFormatIndex = savestyle.getDataFormat();
                        String dataFormatString = workbook.createDataFormat().getFormat(dataFormatIndex);

                        // 根据计算结果类型设置单元格值
                        switch (cellValue.getCellType()) {
                            case NUMERIC:
                                if(dataFormatString.contains("0")||dataFormatString.contains("#"))
                                {
                                    cell.setCellStyle(savestyle);
                                    double one = cellValue.getNumberValue();
                                    if(dataFormatString.contains("%"))
                                    {
                                        one=one*100;
                                    }
                                    BigDecimal two = new BigDecimal(one);
                                    double three = two.setScale(getDecimalPlacesFromFormat(dataFormatString),BigDecimal.ROUND_HALF_UP).doubleValue();
                                    cell.setCellValue(three);
                                }
                                else
                                {
                                    cell.setCellStyle(savestyle);
                                    cell.setCellValue(cellValue.getNumberValue());
                                }

//                                cell.setCellType(evaluator.evaluateFormulaCell(cell));
//                              cell.setCellType(CellType.NUMERIC);
                                break;
                            case STRING:
                                cell.setCellStyle(savestyle);
                                cell.setCellValue(cellValue.getStringValue());
//                                cell.setCellType(CellType.STRING);
                                break;
                            case BOOLEAN:
                                cell.setCellStyle(savestyle);
                                cell.setCellValue(cellValue.getBooleanValue());
//                                cell.setCellType(CellType.BOOLEAN);
                                break;
                            case ERROR:
                                cell.setCellStyle(savestyle);
                                cell.setCellValue(CellType.ERROR.toString());
//                                cell.setCellType(CellType.BOOLEAN);
                                break;
                            default:
                                cell.setCellValue("");
                        }
//                        cell.setCellStyle(savestyle);
                    }
                }
            }
        }
    }
    public static int getDecimalPlacesFromFormat(String formatString) {
        if (formatString == null || formatString.isEmpty()) {
            return 0;
        }

        // 1. 处理条件格式（通常由分号分隔，我们取第一部分，即正数的格式）
        String primaryFormat;
        int semicolonIndex = formatString.indexOf(';');
        if (semicolonIndex != -1) {
            primaryFormat = formatString.substring(0, semicolonIndex);
        } else {
            primaryFormat = formatString;
        }

        // 2. 找到小数点的位置
        int decimalPointIndex = primaryFormat.indexOf('.');
        if (decimalPointIndex == -1) {
            // 格式字符串中没有小数点，说明没有小数部分
            return 0;
        }

        // 3. 获取小数点后面的子字符串
        String decimalPart = primaryFormat.substring(decimalPointIndex + 1);

        // 4. 计算小数部分中 '0' 和 '#' 的个数
        int decimalPlaces = 0;
        for (int i = 0; i < decimalPart.length(); i++) {
            char c = decimalPart.charAt(i);
            if (c == '0' || c == '#') {
                decimalPlaces++;
            } else if (c == '[' || c == ']' || c == '(' || c == ')') {
                // 遇到颜色、条件等特殊符号，停止计算。例如 [Red]、($0.00)
                break;
            }
            // 其他字符如逗号、百分号、日期符号等，忽略它们，因为它们不属于小数部分
            // 但对于简单的数值格式，通常不会遇到
        }
        return decimalPlaces;
    }
}
