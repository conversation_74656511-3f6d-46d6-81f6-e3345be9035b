{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\detail.vue?vue&type=style&index=0&id=45132b4c&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\detail.vue", "mtime": 1756456282508}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoudGFibGUtc3RyaXBlZHsNCiAgbWFyZ2luLXRvcDogMTBweDsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgd2lkdGg6IDEwMCU7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgYm9yZGVyOiAxcHggIzg4ODsNCiAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsNCn0NCi50YWJsZS1zdHJpcGVkIHRoew0KICBoZWlnaHQ6IDMycHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICM4ODg7DQogIGJhY2tncm91bmQtY29sb3I6ICNkZWRlZGU7DQp9DQoudGFibGUtc3RyaXBlZCB0ZHsNCiAgbWluLWhlaWdodDogMzJweDsNCiAgYm9yZGVyOiAxcHggc29saWQgIzg4ODsNCn0NCi50YWJsZS1pbnB1dCAuZWwtdGV4dGFyZWFfX2lubmVyew0KICBib3JkZXI6IDAgIWltcG9ydGFudDsNCiAgcmVzaXplOiBub25lICFpbXBvcnRhbnQ7DQp9DQo="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0hBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/assess/self/config/user", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-descriptions class=\"margin-top\" title=\"用户信息\" :column=\"3\" border>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          姓名\r\n        </template>\r\n        {{ userInfo.name }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          工号\r\n        </template>\r\n        {{ userInfo.workNo }}\r\n      </el-descriptions-item>\r\n      <!-- <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          身份\r\n        </template>\r\n        <span v-if=\"userInfo.assessRole == '0'\">干部</span>\r\n        <span v-if=\"userInfo.assessRole == '1'\">一把手</span>\r\n        <span v-if=\"userInfo.assessRole == '2'\">条线领导</span>\r\n      </el-descriptions-item> -->\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          部门\r\n        </template>\r\n        <span v-for=\"item,index in userInfo.deptList\" v-bind:key=\"index\">{{ item.deptName }}</span>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          职务\r\n        </template>\r\n        <span>{{ userInfo.job }}</span>\r\n      </el-descriptions-item>\r\n    </el-descriptions>\r\n\r\n    <h4>指标配置</h4>\r\n    <el-row :gutter=\"10\" class=\"mb8\" style=\"margin-top: 10px;\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          @click=\"handleEdit\"\r\n          size=\"small\"\r\n        >编辑</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-upload\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :disabled=\"upload.isUploading\"\r\n        :action=\"upload.url\"\r\n        :show-file-list=\"false\"\r\n        :multiple=\"false\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\">\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\">导入</el-button>\r\n        </el-upload>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n          <el-button size=\"small\" type=\"info\" plain icon=\"el-icon-link\" @click=\"downloadTemplate\">导入模板下载</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"targetList\" \r\n      :span-method=\"objectSpanMethod\" border>\r\n      <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"200\"/>\r\n      <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"200\"/>\r\n      <el-table-column label=\"目标\" align=\"center\" prop=\"target\" />\r\n      <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n      <!-- <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column> -->\r\n    </el-table>\r\n    \r\n    <!-- 导入绩效考核-自评指标配置对话框 -->\r\n    <el-dialog :title=\"editTitle\" :visible.sync=\"openEdit\" width=\"1000px\" append-to-body>\r\n      <div style=\"color: red;\">\r\n        注：提交前可对内容进行修改; 鼠标按住行,拖动可变换排列顺序；确认提交后将覆盖原有配置信息。\r\n      </div>\r\n      <table class=\"table-striped\">\r\n        <thead class=\"thead-dark\">\r\n          <tr>\r\n            <th scope=\"col\">序号</th>\r\n            <th scope=\"col\">类型</th>\r\n            <th scope=\"col\">指标</th>\r\n            <th scope=\"col\">目标</th>\r\n            <th scope=\"col\">评分标准</th>\r\n            <th scope=\"col\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <draggable v-model=\"editData\" tag=\"tbody\" item-key=\"name\">\r\n          <tr v-for=\"element,index in editData\" v-bind:key=\"index\">\r\n            <td scope=\"row\">{{ index + 1 }}</td>\r\n            <td>\r\n              <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"element.item\" placeholder=\"请输入类型\"></el-input>\r\n            </td>\r\n            <td>\r\n              <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"element.category\" placeholder=\"请输入指标\"></el-input>\r\n            </td>\r\n            <td>\r\n              <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"element.target\" placeholder=\"请输入目标\"></el-input>\r\n            </td>\r\n            <td>\r\n              <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"element.standard\" placeholder=\"请输入评分标准\"></el-input>\r\n            </td>\r\n            <td>\r\n              <div>\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  @click=\"handleEditDelete(index)\"\r\n                >删除</el-button>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </draggable>\r\n      </table>\r\n      <div>\r\n        <el-button type=\"primary\" \r\n        icon=\"el-icon-plus\" size=\"mini\" @click=\"addRow\">添加行</el-button>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitEdit\">确 定</el-button>\r\n        <el-button @click=\"cancelEdit\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { getTemplateFile } from \"@/api/templateFile/list\";\r\nimport { batchTarget, listTargetAll } from \"@/api/assess/self/target\";\r\nimport { getSelfAssessUser } from \"@/api/assess/self/user\";\r\nimport draggable from 'vuedraggable'\r\n\r\nexport default {\r\n  name: \"SelfAssessTarget\",\r\n  components: {\r\n    draggable\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 绩效考核-自评指标配置表格数据\r\n      targetList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        workNo: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      },\r\n      // 用户信息\r\n      userInfo:{},\r\n      // 编辑弹出框显示\r\n      openEdit:false,\r\n      // 导入参数\r\n      upload: {\r\n          // 是否禁用上传\r\n          isUploading: false,\r\n          // 设置上传的请求头部\r\n          headers: { Authorization: 'Bearer ' + getToken() },\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/web/selfAssess/target/handleImport\",\r\n      },\r\n      editTitle:\"\",\r\n      // 预览/编辑配置数据\r\n      editData:[],\r\n      // 合并单元格信息\r\n      spanList:{\r\n        itemList:[],\r\n        standardList:[]\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.initPageData();\r\n  },\r\n\r\n  // 监听路由变化，确保每次进入页面都重新获取数据\r\n  watch: {\r\n    '$route'(to) {\r\n      // 当路由发生变化时，重新初始化页面数据\r\n      if (to.path === '/assess/self/user/detail') {\r\n        this.initPageData();\r\n      }\r\n    }\r\n  },\r\n\r\n  // 路由更新时的钩子\r\n  beforeRouteUpdate(to, from, next) {\r\n    // 在当前路由改变，但是该组件被复用时调用\r\n    this.queryParams.userId = to.query.userId;\r\n    this.getSelfAssessUser();\r\n    this.getList();\r\n    next();\r\n  },\r\n  methods: {\r\n    // 初始化页面数据\r\n    initPageData() {\r\n      // 获取路径参数\r\n      this.queryParams.userId = this.$route.query.userId;\r\n\r\n      // 如果没有userId参数，返回上一页或首页\r\n      if (!this.queryParams.userId) {\r\n        this.$message.error('缺少必要参数');\r\n        this.$router.go(-1);\r\n        return;\r\n      }\r\n\r\n      // 重置数据\r\n      this.userInfo = {};\r\n      this.targetList = [];\r\n      this.editData = [];\r\n\r\n      // 获取用户信息和指标配置\r\n      this.getSelfAssessUser();\r\n      this.getList();\r\n    },\r\n\r\n    getSelfAssessUser(){\r\n      if (!this.queryParams.userId) {\r\n        return;\r\n      }\r\n      getSelfAssessUser({id:this.queryParams.userId}).then(res => {\r\n        this.userInfo = res.data\r\n      }).catch(error => {\r\n        console.error('获取用户信息失败:', error);\r\n        this.$message.error('获取用户信息失败');\r\n      })\r\n    },\r\n    /** 查询绩效考核-自评指标配置列表 */\r\n    getList() {\r\n      if (!this.queryParams.userId) {\r\n        this.loading = false;\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n      listTargetAll(this.queryParams).then(response => {\r\n        this.handleSpanList(response.data);\r\n        this.targetList = response.data;\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('获取指标配置失败:', error);\r\n        this.$message.error('获取指标配置失败');\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    // 处理列表\r\n    handleSpanList(data){\r\n      let itemList = [];\r\n      let standardList = [];\r\n      let itemFlag = 0;\r\n      let standardFlag = 0;\r\n      for(let i = 0; i < data.length; i++){\r\n        // 相同考核项、评分标准合并\r\n        if(i == 0){\r\n          itemList.push({\r\n            rowspan: 1,\r\n            colspan: 1\r\n          })\r\n          standardList.push({\r\n            rowspan: 1,\r\n            colspan: 1\r\n          })\r\n        }else{\r\n          // 考核项\r\n          if(data[i - 1].item == data[i].item){\r\n            itemList.push({\r\n              rowspan: 0,\r\n              colspan: 0\r\n            })\r\n            itemList[itemFlag].rowspan += 1;\r\n          }else{\r\n            itemList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            itemFlag = i;\r\n          }\r\n          // 评分标准\r\n          if(data[i - 1].standard == data[i].standard){\r\n            standardList.push({\r\n              rowspan: 0,\r\n              colspan: 0\r\n            })\r\n            standardList[standardFlag].rowspan += 1;\r\n          }else{\r\n            standardList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            standardFlag = i;\r\n          }\r\n        }\r\n      }\r\n      this.spanList.itemList = itemList;\r\n      this.spanList.standardList = standardList;\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        workNo: null,\r\n        sort: null,\r\n        item: null,\r\n        category: null,\r\n        target: null,\r\n        standard: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    // 编辑按钮点击事件\r\n    handleEdit(){\r\n      this.editData = this.handleToEditInfo(JSON.parse(JSON.stringify(this.targetList)));\r\n      this.editTitle = \"配置编辑\"\r\n      this.openEdit = true;\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    // handleExport() {\r\n    //   this.download('selfAssess/target/export', {\r\n    //     ...this.queryParams\r\n    //   }, `target_${new Date().getTime()}.xlsx`)\r\n    // },\r\n\r\n    cancelEdit(){\r\n      this.openEdit = false;\r\n    },\r\n\r\n    // 确认编辑、导入\r\n    submitEdit(){\r\n      console.log(this.editData)\r\n      if(!this.verifyEdit()){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '信息未填写完整'\r\n          });\r\n          return;\r\n      };\r\n      this.$confirm('确认后将覆盖原有数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.batchData();\r\n      }).catch(() => {\r\n          \r\n      });\r\n    },\r\n\r\n    // 提交数据验证\r\n    verifyEdit(){\r\n      for(let i = 0; i < this.editData.length; i++){\r\n        if(!this.editData[i].item) return false;\r\n        // if(!this.editData[i].category) return false;\r\n        if(!this.editData[i].target) return false;\r\n        if(!this.editData[i].standard) return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    // 新增数据\r\n    batchData(){\r\n      let data = this.handleEditData(this.editData);\r\n      batchTarget(data).then(res => {\r\n        if(res.code == 200){\r\n          this.openEdit = false;\r\n          this.editData = [];\r\n          this.getList();\r\n          this.$message({\r\n            type: 'success',\r\n            message: '提交成功!'\r\n          });\r\n        }\r\n      })\r\n    },\r\n\r\n    // 处理提交数据\r\n    handleEditData(data){\r\n      for(let i = 0; i < data.length; i++){\r\n        data[i].sort = i + 1;\r\n        data[i].userId = this.queryParams.userId;\r\n      }\r\n      return data\r\n    },\r\n\r\n    // 处理导入内容\r\n    handleToEditInfo(data){\r\n        for(let i = 0; i < data.length; i++){\r\n          if(data[i].id){\r\n            data[i].id = null;\r\n          }\r\n          // 没有考核项取上一行值\r\n          if(!data[i].item){\r\n            data[i].item = data[i-1].item\r\n          }\r\n          // 没有标准取上一行值\r\n          if(!data[i].standard){\r\n            data[i].standard = data[i-1].standard\r\n          }\r\n          // 没有类别 有目标 类别取上一行内容\r\n          if(!data[i].category && data[i].target){\r\n            // 没有类别且没有目标，\r\n            data[i].category = data[i-1].category\r\n          }\r\n          // 有类别 没有目标 目标取类别内容 类别空\r\n          if(data[i].category && !data[i].target){\r\n            // 没有类别且没有目标，\r\n            data[i].target = data[i].category;\r\n            data[i].category = \"\"\r\n          }\r\n        }\r\n        return data;\r\n    },\r\n\r\n\r\n    handleFileUploadProgress(){\r\n        this.upload.isUploading = true\r\n    },\r\n    handleFileSuccess(response){\r\n        console.log(response)\r\n        this.upload.isUploading = false\r\n        this.editData = this.handleToEditInfo(response.data);\r\n        this.editTitle = \"导入预览\";\r\n        this.openEdit = true;\r\n    },\r\n\r\n    // 模板下载\r\n    downloadTemplate(){\r\n      getTemplateFile({id:\"42\"}).then(res => {\r\n          if(res.code == 200){\r\n            let localUrl = window.location.host;\r\n            if(localUrl === \"************:8099\"){\r\n              res.data.url = res.data.url.replace(\"ydxt.citicsteel.com:8099\",\"************:8099\");\r\n            }\r\n            let url = res.data.url;\r\n            window.open(url);\r\n          }\r\n      })\r\n    },\r\n\r\n    // 编辑行删除\r\n    handleEditDelete(index){\r\n      this.editData.splice(index,1)\r\n    },\r\n\r\n    // 添加行\r\n    addRow(){\r\n      this.editData.push({\r\n        item: null,\r\n        category: null,\r\n        target: null,\r\n        standard: null,\r\n      })\r\n    },\r\n\r\n    // 合并单元格方法\r\n    objectSpanMethod({ row, rowIndex, columnIndex }) {\r\n      // 第一列相同项合并\r\n      if (columnIndex === 0) {\r\n        return this.spanList.itemList[rowIndex];\r\n      }\r\n      // 评分标准相同合并\r\n      if(columnIndex === 3){\r\n        return this.spanList.standardList[rowIndex];\r\n      }\r\n      // 类别无内容 合并\r\n      if(columnIndex === 1){\r\n        if(!row.category){\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          }\r\n        }\r\n      }\r\n      if(columnIndex === 2){\r\n        if(!row.category){\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.table-striped{\r\n  margin-top: 10px;\r\n  margin-bottom: 10px;\r\n  width: 100%;\r\n  text-align: center;\r\n  border: 1px #888;\r\n  border-collapse: collapse;\r\n}\r\n.table-striped th{\r\n  height: 32px;\r\n  border: 1px solid #888;\r\n  background-color: #dedede;\r\n}\r\n.table-striped td{\r\n  min-height: 32px;\r\n  border: 1px solid #888;\r\n}\r\n.table-input .el-textarea__inner{\r\n  border: 0 !important;\r\n  resize: none !important;\r\n}\r\n</style>\r\n"]}]}