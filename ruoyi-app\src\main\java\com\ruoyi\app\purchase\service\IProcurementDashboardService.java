package com.ruoyi.app.purchase.service;

import com.ruoyi.app.purchase.domain.*;
import com.ruoyi.app.purchase.dto.*;
import com.ruoyi.app.purchase.vo.ProcurementYearlyDetailResultVo;
import com.ruoyi.app.purchase.vo.PurchaseDashboardVO;
import com.ruoyi.app.purchase.vo.PurchaseRealTimeInventoryResultVo;
import com.ruoyi.app.purchase.vo.PurchaseCokingCoalInventoryDetailVo;
import com.ruoyi.app.purchase.vo.PurchaseCokingVo;
import com.ruoyi.app.purchase.vo.ProcurementPriceAndPurchaseResultVo;
import com.ruoyi.app.purchase.vo.MaterialNameVo;
import com.ruoyi.app.purchase.vo.MaterialFuturePriceResultVo;

import java.util.List;

public interface IProcurementDashboardService {

    /**
     * 看板数据
     */
    PurchaseDashboardVO showData(ProcurementDto query);

    /**
     * 物资类型列表
     */
    List<PurchaseMaterial> showItemTypeList(ProcurementOptionDto query);

    /**
     * 物资列表
     */
    List<PurchaseMaterial> showMaterialList(ProcurementDetailDto query);


    /**
     * 高频物资列表
     */
    List<PurchaseHighFrequencyMaterial> showHighFrequencyMaterialList(ProcurementFrequencyDto query);



    /**
     * 供应商风险
     * @param purchaseSuppRisk
     * @return
     */
    List<PurchaseSuppRisk> showPurchaseSuppRisk(PurchaseSuppRisk purchaseSuppRisk);

    /**
     * 供应商列表
     *
     */
    List<PurchaseProviderTotalAmount> showSuppList(ProcurementSuppDto procurementSuppDto);

    /**
     * 采购关键指标
     *
     * @param
     * @return
     */
    PurchaseStatistic showKeyIndicators(ProcurementDto query);

    /**
     * 获取库存金额年度统计
     *
     * @param query 查询参数
     * @return 年度库存金额统计结果
     */
    List<ProcurementYearlyDetailResultVo> showYearlyAmount(ProcurementYearlyQueryDto query);

    /**
     * 获取实时库存
     *
     * @return 实时库存计结果
     */
    List<PurchaseRealTimeInventoryResultVo> showRealTimeAmount();

    /**
     * 获取矿焦煤实时库存
     *
     * @return 矿焦煤实时库存结果
     */
    List<PurchaseCokingVo> showCokingCoalAmount();

    /**
     * 获取物资价格和入库数量
     * 支持查询多个物资的价格数据或采购量数据：
     * - 每个物资可以指定独立的曲线类型（价格曲线或采购量曲线）
     * - 物资名称为必填项
     * - 曲线类型：1-价格曲线 2-采购量曲线
     *
     * @param query 查询参数，包含多个物资的查询条件列表
     * @return 物资价格和入库数量结果
     */
    List<ProcurementPriceAndPurchaseResultVo> getPurchasePriceAndStore(ProcurementPriceAndStoreQueryDto query);

    /**
     * 获取物资名称列表
     * 支持多选物资类型筛选：
     * - categories：物资类型列表，支持多选：1-矿石 2-煤炭 3-合金 4-废钢 99-全部
     * - curveType：曲线类型：1-价格曲线 2-采购量曲线
     * - dimensionType：时间维度：1-近三个月 2-近半年 3-近一年
     * 
     * 根据曲线类型筛选：
     * - 价格曲线：只返回有价格数据的物资
     * - 采购量曲线：只返回有采购量数据的物资
     * - 未指定曲线类型：返回所有物资
     *
     * @param query 查询参数
     * @return 带序号的物资名称列表
     */
    List<MaterialNameVo> getMaterialNameList(MaterialNameQueryDto query);

    /**
     * 调用大模型获取物料未来的价格走势
     */
    MaterialFuturePriceResultVo getMaterialFuturePrice(ProcurementFuturePriceDto query);

    /**
     * 获取物资价格和入库数量（使用新数据表）
     * 价格数据从procurement_material_price表获取
     * 采购量数据从procurement_material_amount表获取
     * 
     * @param query 查询参数，包含多个物资的查询条件列表
     * @return 物资价格和入库数量结果
     */
    List<ProcurementPriceAndPurchaseResultVo> getPurchasePriceAndStoreFromNewTables(ProcurementPriceAndStoreQueryDto query);

    /**
     * 获取物资名称列表（使用新数据表）
     * 支持多选物资类型筛选：
     * - categories：物资类型列表，支持多选：1-矿石 2-煤炭 3-合金 4-废钢 99-全部
     * - curveType：曲线类型：1-价格曲线 2-采购量曲线
     * - dimensionType：时间维度：1-近三个月 2-近半年 3-近一年
     * 
     * 根据曲线类型筛选：
     * - 价格曲线：只返回有价格数据的物资（从procurement_material_price表）
     * - 采购量曲线：只返回有采购量数据的物资（从procurement_material_amount表）
     * - 未指定曲线类型：返回所有物资
     *
     * @param query 查询参数
     * @return 带序号的物资名称列表
     */
    List<MaterialNameVo> getMaterialNameListFromNewTables(MaterialNameQueryDto query);

    /**
     * 驾驶舱：资金管理
     * @return
     */
    List<PurchaseTotalAmount> showAmtManage();

    /**
     * 采购计划看板
     * @return
     */
    List<PurchasePlan> showPurchasePlanList();
}
