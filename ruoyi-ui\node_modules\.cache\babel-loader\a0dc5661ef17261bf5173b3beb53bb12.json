{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\permission.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\permission.js", "mtime": 1756456282407}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_router", "_interopRequireDefault", "require", "_store", "_elementUi", "_nprogress", "_auth", "_ip", "NProgress", "configure", "showSpinner", "whiteList", "router", "beforeEach", "to", "from", "next", "start", "getToken", "path", "done", "store", "getters", "roles", "length", "dispatch", "then", "res", "accessRoutes", "addRoutes", "_objectSpread2", "default", "replace", "catch", "err", "Message", "error", "indexOf", "concat", "fullPath", "after<PERSON>ach"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/permission.js"], "sourcesContent": ["import router from './router'\r\nimport store from './store'\r\nimport { Message } from 'element-ui'\r\nimport NProgress from 'nprogress'\r\nimport 'nprogress/nprogress.css'\r\nimport { getToken } from '@/utils/auth'\r\nimport { getUserIP } from '@/utils/ip'\r\n\r\nNProgress.configure({ showSpinner: false })\r\n\r\nconst whiteList = ['/login', '/auth-redirect', '/bind', '/register']\r\nrouter.beforeEach((to, from, next) => {\r\n  NProgress.start()\r\n  if (getToken()) {\r\n    /* has token*/\r\n    if (to.path === '/login') {\r\n      next({ path: '/' })\r\n      NProgress.done()\r\n    } else {\r\n      if (store.getters.roles.length === 0) {\r\n        // 判断当前用户是否已拉取完user_info信息\r\n        store.dispatch('GetInfo').then(res => {\r\n          // 拉取user_info\r\n          const roles = res.roles\r\n          store.dispatch('GenerateRoutes', { roles }).then(accessRoutes => {\r\n          // 测试 默认静态页面\r\n          // store.dispatch('permission/generateRoutes', { roles }).then(accessRoutes => {\r\n            // 根据roles权限生成可访问的路由表\r\n            router.addRoutes(accessRoutes) // 动态添加可访问路由表\r\n            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成\r\n          })\r\n        })\r\n          .catch(err => {\r\n            store.dispatch('FedLogOut').then(() => {\r\n              Message.error(err)\r\n              next({ path: '/' })\r\n            })\r\n          })\r\n      } else {\r\n        next()\r\n        // 没有动态改变权限的需求可直接next() 删除下方权限判断 ↓\r\n        // if (hasPermission(store.getters.roles, to.meta.roles)) {\r\n        //   next()\r\n        // } else {\r\n        //   next({ path: '/401', replace: true, query: { noGoBack: true }})\r\n        // }\r\n        // 可删 ↑\r\n      }\r\n    }\r\n  } else {\r\n    // 没有token\r\n    if (whiteList.indexOf(to.path) !== -1 || to.path.indexOf('bill') !== -1 || to.path.indexOf('webView') !== -1|| to.path.indexOf('tdgcb04') !== -1|| to.path.indexOf('codeLogin') !== -1|| to.path.indexOf('bigScreenCommon') !== -1) {\r\n      // 在免登录白名单，直接进入\r\n      next()\r\n    } \r\n    else {\r\n      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页\r\n      NProgress.done()\r\n    }\r\n  }\r\n})\r\n\r\nrouter.afterEach(() => {\r\n  NProgress.done()\r\n});\r\n\r\n\r\n\r\n"], "mappings": ";;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACAA,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,GAAA,GAAAL,OAAA;AAEAM,kBAAS,CAACC,SAAS,CAAC;EAAEC,WAAW,EAAE;AAAM,CAAC,CAAC;AAE3C,IAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,WAAW,CAAC;AACpEC,eAAM,CAACC,UAAU,CAAC,UAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAK;EACpCR,kBAAS,CAACS,KAAK,CAAC,CAAC;EACjB,IAAI,IAAAC,cAAQ,EAAC,CAAC,EAAE;IACd;IACA,IAAIJ,EAAE,CAACK,IAAI,KAAK,QAAQ,EAAE;MACxBH,IAAI,CAAC;QAAEG,IAAI,EAAE;MAAI,CAAC,CAAC;MACnBX,kBAAS,CAACY,IAAI,CAAC,CAAC;IAClB,CAAC,MAAM;MACL,IAAIC,cAAK,CAACC,OAAO,CAACC,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC;QACAH,cAAK,CAACI,QAAQ,CAAC,SAAS,CAAC,CAACC,IAAI,CAAC,UAAAC,GAAG,EAAI;UACpC;UACA,IAAMJ,KAAK,GAAGI,GAAG,CAACJ,KAAK;UACvBF,cAAK,CAACI,QAAQ,CAAC,gBAAgB,EAAE;YAAEF,KAAK,EAALA;UAAM,CAAC,CAAC,CAACG,IAAI,CAAC,UAAAE,YAAY,EAAI;YACjE;YACA;YACE;YACAhB,eAAM,CAACiB,SAAS,CAACD,YAAY,CAAC,EAAC;YAC/BZ,IAAI,KAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAMjB,EAAE;cAAEkB,OAAO,EAAE;YAAI,EAAE,CAAC,EAAC;UACjC,CAAC,CAAC;QACJ,CAAC,CAAC,CACCC,KAAK,CAAC,UAAAC,GAAG,EAAI;UACZb,cAAK,CAACI,QAAQ,CAAC,WAAW,CAAC,CAACC,IAAI,CAAC,YAAM;YACrCS,kBAAO,CAACC,KAAK,CAACF,GAAG,CAAC;YAClBlB,IAAI,CAAC;cAAEG,IAAI,EAAE;YAAI,CAAC,CAAC;UACrB,CAAC,CAAC;QACJ,CAAC,CAAC;MACN,CAAC,MAAM;QACLH,IAAI,CAAC,CAAC;QACN;QACA;QACA;QACA;QACA;QACA;QACA;MACF;IACF;EACF,CAAC,MAAM;IACL;IACA,IAAIL,SAAS,CAAC0B,OAAO,CAACvB,EAAE,CAACK,IAAI,CAAC,KAAK,CAAC,CAAC,IAAIL,EAAE,CAACK,IAAI,CAACkB,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAIvB,EAAE,CAACK,IAAI,CAACkB,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAGvB,EAAE,CAACK,IAAI,CAACkB,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAGvB,EAAE,CAACK,IAAI,CAACkB,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAGvB,EAAE,CAACK,IAAI,CAACkB,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE;MAClO;MACArB,IAAI,CAAC,CAAC;IACR,CAAC,MACI;MACHA,IAAI,oBAAAsB,MAAA,CAAoBxB,EAAE,CAACyB,QAAQ,CAAE,CAAC,EAAC;MACvC/B,kBAAS,CAACY,IAAI,CAAC,CAAC;IAClB;EACF;AACF,CAAC,CAAC;AAEFR,eAAM,CAAC4B,SAAS,CAAC,YAAM;EACrBhC,kBAAS,CAACY,IAAI,CAAC,CAAC;AAClB,CAAC,CAAC", "ignoreList": []}]}