{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\utils\\ip.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\utils\\ip.js", "mtime": 1756456282412}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmdldFVzZXJJUCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcubWF0Y2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZvci1lYWNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIik7CnZhciBnZXRVc2VySVAgPSBleHBvcnRzLmdldFVzZXJJUCA9IGZ1bmN0aW9uIGdldFVzZXJJUChvbk5ld0lQKSB7CiAgdmFyIE15UGVlckNvbm5lY3Rpb24gPSB3aW5kb3cuUlRDUGVlckNvbm5lY3Rpb24gfHwgd2luZG93Lm1velJUQ1BlZXJDb25uZWN0aW9uIHx8IHdpbmRvdy53ZWJraXRSVENQZWVyQ29ubmVjdGlvbjsKICB2YXIgcGMgPSBuZXcgTXlQZWVyQ29ubmVjdGlvbih7CiAgICBpY2VTZXJ2ZXJzOiBbXQogIH0pOwogIHZhciBub29wID0gZnVuY3Rpb24gbm9vcCgpIHt9OwogIHZhciBsb2NhbElQcyA9IHt9OwogIHZhciBpcFJlZ2V4ID0gLyhbMC05XXsxLDN9KFwuWzAtOV17MSwzfSl7M318W2EtZjAtOV17MSw0fSg6W2EtZjAtOV17MSw0fSl7N30pL2c7CiAgdmFyIGl0ZXJhdGVJUCA9IGZ1bmN0aW9uIGl0ZXJhdGVJUChpcCkgewogICAgaWYgKCFsb2NhbElQc1tpcF0pIG9uTmV3SVAoaXApOwogICAgbG9jYWxJUHNbaXBdID0gdHJ1ZTsKICB9OwogIHBjLmNyZWF0ZURhdGFDaGFubmVsKCcnKTsKICBwYy5jcmVhdGVPZmZlcigpLnRoZW4oZnVuY3Rpb24gKHNkcCkgewogICAgc2RwLnNkcC5zcGxpdCgnXG4nKS5mb3JFYWNoKGZ1bmN0aW9uIChsaW5lKSB7CiAgICAgIGlmIChsaW5lLmluZGV4T2YoJ2NhbmRpZGF0ZScpIDwgMCkgcmV0dXJuOwogICAgICBsaW5lLm1hdGNoKGlwUmVnZXgpLmZvckVhY2goaXRlcmF0ZUlQKTsKICAgIH0pOwogICAgcGMuc2V0TG9jYWxEZXNjcmlwdGlvbihzZHAsIG5vb3AsIG5vb3ApOwogIH0pLmNhdGNoKGZ1bmN0aW9uIChyZWFzb24pIHt9KTsKICBwYy5vbmljZWNhbmRpZGF0ZSA9IGZ1bmN0aW9uIChpY2UpIHsKICAgIGlmICghaWNlIHx8ICFpY2UuY2FuZGlkYXRlIHx8ICFpY2UuY2FuZGlkYXRlLmNhbmRpZGF0ZSB8fCAhaWNlLmNhbmRpZGF0ZS5jYW5kaWRhdGUubWF0Y2goaXBSZWdleCkpIHJldHVybjsKICAgIGljZS5jYW5kaWRhdGUuY2FuZGlkYXRlLm1hdGNoKGlwUmVnZXgpLmZvckVhY2goaXRlcmF0ZUlQKTsKICB9Owp9Ow=="}, {"version": 3, "names": ["getUserIP", "exports", "onNewIP", "MyPeerConnection", "window", "RTCPeerConnection", "mozRTCPeerConnection", "webkitRTCPeerConnection", "pc", "iceServers", "noop", "localIPs", "ipRegex", "iterateIP", "ip", "createDataChannel", "createOffer", "then", "sdp", "split", "for<PERSON>ach", "line", "indexOf", "match", "setLocalDescription", "catch", "reason", "onicecandidate", "ice", "candidate"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/utils/ip.js"], "sourcesContent": ["export const getUserIP = (onNewIP) =>{\r\n  let MyPeerConnection = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection;\r\n  let pc = new MyPeerConnection({\r\n    iceServers: []\r\n  });\r\n  let noop = () => {\r\n  };\r\n  let localIPs = {};\r\n  let ipRegex = /([0-9]{1,3}(\\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/g;\r\n  let iterateIP = (ip) => {\r\n    if (!localIPs[ip]) onNewIP(ip);\r\n    localIPs[ip] = true;\r\n  };\r\n  pc.createDataChannel('');\r\n  pc.createOffer().then((sdp) => {\r\n    sdp.sdp.split('\\n').forEach(function (line) {\r\n      if (line.indexOf('candidate') < 0) return;\r\n      line.match(ipRegex).forEach(iterateIP);\r\n    });\r\n    pc.setLocalDescription(sdp, noop, noop);\r\n  }).catch((reason) => {\r\n  });\r\n  pc.onicecandidate = (ice) => {\r\n    if (!ice || !ice.candidate || !ice.candidate.candidate || !ice.candidate.candidate.match(ipRegex)) return;\r\n    ice.candidate.candidate.match(ipRegex).forEach(iterateIP);\r\n  };\r\n\r\n}\r\n\r\n"], "mappings": ";;;;;;;;;;;;AAAO,IAAMA,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG,SAAZA,SAASA,CAAIE,OAAO,EAAI;EACnC,IAAIC,gBAAgB,GAAGC,MAAM,CAACC,iBAAiB,IAAID,MAAM,CAACE,oBAAoB,IAAIF,MAAM,CAACG,uBAAuB;EAChH,IAAIC,EAAE,GAAG,IAAIL,gBAAgB,CAAC;IAC5BM,UAAU,EAAE;EACd,CAAC,CAAC;EACF,IAAIC,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS,CACjB,CAAC;EACD,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIC,OAAO,GAAG,iEAAiE;EAC/E,IAAIC,SAAS,GAAG,SAAZA,SAASA,CAAIC,EAAE,EAAK;IACtB,IAAI,CAACH,QAAQ,CAACG,EAAE,CAAC,EAAEZ,OAAO,CAACY,EAAE,CAAC;IAC9BH,QAAQ,CAACG,EAAE,CAAC,GAAG,IAAI;EACrB,CAAC;EACDN,EAAE,CAACO,iBAAiB,CAAC,EAAE,CAAC;EACxBP,EAAE,CAACQ,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,UAACC,GAAG,EAAK;IAC7BA,GAAG,CAACA,GAAG,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC1C,IAAIA,IAAI,CAACC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;MACnCD,IAAI,CAACE,KAAK,CAACX,OAAO,CAAC,CAACQ,OAAO,CAACP,SAAS,CAAC;IACxC,CAAC,CAAC;IACFL,EAAE,CAACgB,mBAAmB,CAACN,GAAG,EAAER,IAAI,EAAEA,IAAI,CAAC;EACzC,CAAC,CAAC,CAACe,KAAK,CAAC,UAACC,MAAM,EAAK,CACrB,CAAC,CAAC;EACFlB,EAAE,CAACmB,cAAc,GAAG,UAACC,GAAG,EAAK;IAC3B,IAAI,CAACA,GAAG,IAAI,CAACA,GAAG,CAACC,SAAS,IAAI,CAACD,GAAG,CAACC,SAAS,CAACA,SAAS,IAAI,CAACD,GAAG,CAACC,SAAS,CAACA,SAAS,CAACN,KAAK,CAACX,OAAO,CAAC,EAAE;IACnGgB,GAAG,CAACC,SAAS,CAACA,SAAS,CAACN,KAAK,CAACX,OAAO,CAAC,CAACQ,OAAO,CAACP,SAAS,CAAC;EAC3D,CAAC;AAEH,CAAC", "ignoreList": []}]}