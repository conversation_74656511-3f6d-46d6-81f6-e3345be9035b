{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardMain\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardMain\\index.vue", "mtime": 1756456493834}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_screenfull", "_interopRequireDefault", "_purchaseDashboardMain", "_purchaseDashboard", "name", "data", "timeFilters", "id", "label", "value", "activeFilter", "currentDimensionType", "charts", "resizeTimer", "highFrequencyMaterialList", "priceAndStoreData", "selectedCodeType", "selectedItemType", "selectedMaterial", "cokingCoalInventoryData", "selectedCokingCoalType", "realTimeStats", "sales", "orders", "productAnalysisData", "percentage", "planExecutionData", "bottomStats", "waterLevelConfig", "shape", "waveNum", "waveHeight", "waveOpacity", "colors", "supplierData", "color", "position", "top", "left", "right", "bottom", "centerData", "amount", "count", "qualityIssueCount", "supplierStats", "admission", "elimination", "suspension", "warningInfo", "certificateExpiry", "contractExpiry", "purchaseStats", "arriveRate", "fundManagement", "nextMonth", "twoMonthsLater", "threeMonthsLater", "contractData", "computed", "isFullscreen", "$store", "state", "app", "isFullscreenMode", "mounted", "initFullscreenListener", "initDashboard", "window", "addEventListener", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "Object", "values", "for<PERSON>ach", "chart", "dispose", "removeFullscreenListener", "dispatch", "methods", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "_t", "w", "_context", "n", "p", "loadData", "$nextTick", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v", "console", "error", "a", "_this2", "_callee2", "_t2", "_context2", "Promise", "all", "fetchWarningData", "fetchFundManagementData", "log", "_this3", "_callee3", "response", "nextMonthData", "twoMonthsData", "threeMonthsData", "_t3", "_context3", "getAmtManage", "Array", "isArray", "find", "item", "reserve1", "reserve4", "updateFundManagementChart", "resetFundManagementData", "_this4", "_callee4", "_t4", "_context4", "showKeyIndicators", "dimensionType", "suppRiskNum", "bpoExpireNum", "updatePlanManagementData", "updatePlanExecutionData", "planData", "planTotalNum", "rejectNum1", "rejectNum2", "resetPlanManagementData", "undefined", "toString", "Math", "min", "max", "planCompletionRate", "inTransitOrderNum", "formatNumber", "pendingAuditNum", "urgentPurchaseNum", "supplierResponseRate", "inventoryTurnoverRate", "costSavingRate", "qualityPassRate", "num", "replace", "getPlanIcon", "index", "icons", "getExecutionIcon", "initFundManagementChart", "initSupplierManagementChart", "initPersonalConsumptionChart", "initPurchaseAnalysisChart", "initContractChart", "initTrendChart", "initSupplierChart", "fetchHighFrequencyData", "fetchCokingCoalInventoryData", "chartDom", "document", "getElementById", "init", "option", "backgroundColor", "grid", "containLabel", "xAxis", "type", "axisLine", "lineStyle", "axisLabel", "fontSize", "yAxis", "nameTextStyle", "align", "interval", "formatter", "splitLine", "series", "parseFloat", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "x", "y", "x2", "y2", "colorStops", "offset", "global", "borderRadius", "show", "params", "emphasis", "tooltip", "trigger", "borderColor", "textStyle", "param", "concat", "setOption", "_this5", "supplierManagement", "radius", "center", "dataIndex", "length", "newData", "personalConsumption", "purchaseAnalysis", "smooth", "width", "areaStyle", "contract", "axisPointer", "formattedValue", "toFixed", "map", "rotate", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "borderWidth", "trend", "_this6", "supplier", "_this7", "_callee5", "_t5", "_context5", "fetchHighFrequencyMaterialData", "fetchPriceAndStoreData", "initHighFrequencyMaterialCloud", "initHighFrequencyPriceTrendChart", "_this8", "_callee6", "_t6", "_context6", "codeType", "itemType", "showHighFrequencyMaterialList", "slice", "getMockHighFrequencyData", "_this9", "innerHTML", "rawMaterialList", "highFrequencyMaterials", "sort", "b", "inAmt", "container", "createElement", "style", "height", "overflow", "maxFontSize", "minFontSize", "div", "textContent", "itemName", "fontWeight", "transform", "random", "whiteSpace", "textShadow", "transition", "cursor", "zIndex", "self", "className", "formatAmount", "background", "padding", "append<PERSON><PERSON><PERSON>", "querySelector", "<PERSON><PERSON><PERSON><PERSON>", "inNum", "_this0", "_callee7", "_t7", "_context7", "getPurchasePriceAndStore", "getMockPriceAndStoreData", "procurementPriceVoList", "priceName", "priceList", "recordDate", "price", "procurementPurchaseAmountVoList", "amountName", "amountList", "_this1", "highFrequencyPriceTrend", "allDates", "Set", "priceGroup", "add", "amountGroup", "from", "legendData", "priceData", "date", "found", "push", "yAxisIndex", "symbol", "symbolSize", "connectNulls", "amountData", "priceMin", "priceMax", "priceValues", "filter", "s", "flatMap", "apply", "_toConsumableArray2", "crossStyle", "str", "axisValueLabel", "seriesName", "includes", "marker", "valueInWan", "legend", "itemGap", "orient", "year", "substring", "month", "parseInt", "axisTick", "nameLocation", "nameGap", "nameRotate", "_this10", "clearTimeout", "setTimeout", "resize", "screenfull", "isEnabled", "on", "handleFullscreenChange", "off", "_this11", "toggleFullscreen", "toggle", "$message", "message", "handleTimeFilterChange", "filterId", "goToSupplierPenalty", "routeUrl", "$router", "resolve", "open", "href", "goToStockDashboard", "goToHighFrequencyDashboard", "goToPlanDashboard", "_this12", "_callee8", "_t8", "_context8", "showCokingCoalAmount", "initCokingCoalLineChart", "calculateCokingCoalTotal", "total", "latestDate", "purchaseCokingDailyDetailList", "detail", "instockDate", "latestDetail", "invQty", "handleCokingCoalTypeChange", "_this13", "_callee9", "_context9", "getCokingCoalMaterialColorMap", "baseColors", "allMaterialTypes", "inventoryData", "materialName", "class2Name", "colorMap", "_this14", "cokingCoalLineChart", "myChart", "title", "text", "filteredData", "sortedDates", "formattedDates", "dateStr", "day", "seriesData", "typeName", "lineData", "_item$purchaseCokingD", "d", "materialColor", "seriesItem", "tooltipText", "getWarningPercentage", "numValue", "certificateValue", "contractValue", "maxValue"], "sources": ["src/views/purchaseDashboardMain/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"purchase-dashboard-main\" :class=\"{ 'fullscreen-mode': isFullscreen }\">\r\n    <div class=\"dashboard-container\">\r\n      <!-- 头部标题 -->\r\n      <div class=\"dashboard-header\">\r\n        <h1>采购管理全景视图</h1>\r\n        <div class=\"header-controls\">\r\n          <div class=\"fullscreen-btn\" @click=\"toggleFullscreen\" :title=\"isFullscreen ? '退出全屏' : '进入全屏'\">\r\n            <i :class=\"isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'\"></i>\r\n          </div>\r\n          <div class=\"time-filter\">\r\n            <button\r\n              v-for=\"filter in timeFilters\"\r\n              :key=\"filter.id\"\r\n              :class=\"['time-filter-btn', { active: filter.id === activeFilter }]\"\r\n              @click=\"handleTimeFilterChange(filter.id, filter.value)\"\r\n            >\r\n              {{ filter.label }}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"dashboard-content\">\r\n        <!-- 左侧面板 -->\r\n        <div class=\"left-panel\" :style=\"isFullscreen ? { width: '320px', minWidth: '320px', maxWidth: '320px', gap: '12px' } : {}\">\r\n          <!-- 左侧第一个：资金管理 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">资金管理</h2>\r\n            <div class=\"chart\" id=\"fundManagementChart\"></div>\r\n          </div>\r\n\r\n          <!-- 左侧第二个：供应商信息全景 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">供应商信息全景</h2>\r\n            <div class=\"supplier-circles\">\r\n              <!-- 中心圆形：考核情况 -->\r\n              <div class=\"circle-item center-circle center\" @click=\"goToSupplierPenalty\">\r\n                <div class=\"circle clickable\" :style=\"{ borderColor: centerData.color, backgroundColor: centerData.color }\">\r\n                  <div class=\"circle-number\">\r\n                    <div>金额: {{ centerData.amount }}</div>\r\n                    <div>次数: {{ centerData.count }}</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"circle-label\">{{ centerData.label }}</div>\r\n              </div>\r\n\r\n              <!-- 周围四个圆形 - 随机位置分布 -->\r\n              <div\r\n                class=\"circle-item random-position\"\r\n                v-for=\"item in supplierData\"\r\n                :key=\"item.id\"\r\n                :style=\"item.position\"\r\n              >\r\n                <div class=\"circle\" :style=\"{ borderColor: item.color, backgroundColor: item.color }\">\r\n                  <span class=\"circle-number\">{{ item.value }}</span>\r\n                </div>\r\n                <div class=\"circle-label\">{{ item.label }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 左侧第三个：合同管理（暂时隐藏） -->\r\n          <!-- <div class=\"card\">\r\n            <h2 class=\"card-title\">合同管理</h2>\r\n            <div id=\"contractChart\" class=\"chart\"></div>\r\n          </div> -->\r\n\r\n          <!-- 左侧第三个：单一来源 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">单一来源</h2>\r\n            <div id=\"supplierChart\" class=\"chart\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 中间面板 -->\r\n        <div class=\"center-panel\" :style=\"isFullscreen ? { gap: '10px', flex: '1', minWidth: '400px' } : {}\">\r\n          <!-- 中间第一行 -->\r\n          <div class=\"center-row center-row-first\" :style=\"isFullscreen ? { maxHeight: 'none', flex: '1' } : {}\">\r\n            <!-- 中间第一个：计划管理 -->\r\n            <div class=\"card clickable-card plan-management-card\" @click=\"goToPlanDashboard\">\r\n              <h2 class=\"card-title\">计划管理</h2>\r\n              <div class=\"plan-grid\" :style=\"isFullscreen ? { gap: '8px', padding: '10px 0' } : {}\">\r\n                <div class=\"plan-item\" v-for=\"(item, index) in productAnalysisData\" :key=\"item.name\" :style=\"isFullscreen ? { padding: '10px 40px' } : {}\">\r\n                  <i :class=\"getPlanIcon(index)\" class=\"plan-icon\" :style=\"isFullscreen ? { fontSize: '18px', marginRight: '8px', width: '20px' } : {}\"></i>\r\n                  <div class=\"plan-text\">\r\n                    <div class=\"plan-value\" :style=\"isFullscreen ? { marginBottom: '4px', fontSize: '15px' } : {}\">{{ item.value }}</div>\r\n                    <div class=\"plan-label\" :style=\"isFullscreen ? { fontSize: '12px', lineHeight: '1.3' } : {}\">{{ item.name }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 中间第二个：库存管理 -->\r\n            <div class=\"card clickable-card\" @click=\"goToStockDashboard\">\r\n              <h2 class=\"card-title\">\r\n                <div style=\"display: flex; align-items: center; justify-content: space-between; width: 100%;\">\r\n                  <div style=\"display: flex; align-items: center; gap: 15px;\">\r\n                    <span>库存管理</span>\r\n                    <span class=\"inventory-total\">\r\n                      合计: {{ calculateCokingCoalTotal() }}万吨\r\n                    </span>\r\n                  </div>\r\n                  <div class=\"chart-filter-dropdown-container\">\r\n                    <select\r\n                      v-model=\"selectedCokingCoalType\"\r\n                      @change=\"handleCokingCoalTypeChange\"\r\n                      @click.stop\r\n                    >\r\n                      <option value=\"\">全部</option>\r\n                      <option value=\"矿料类\">矿料类</option>\r\n                      <option value=\"焦炭\">焦炭</option>\r\n                      <option value=\"煤焦类\">煤焦类</option>\r\n                      <option value=\"合金类\">合金类</option>\r\n                      <option value=\"辅助类/电极\">辅助类/电极</option>\r\n                    </select>\r\n                  </div>\r\n                </div>\r\n              </h2>\r\n              <div id=\"cokingCoalLineChart\" class=\"chart\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 新增：中间插入行 - 计划执行状态 -->\r\n          <div class=\"center-row-full\">\r\n            <div class=\"card plan-execution-card\" :style=\"isFullscreen ? { maxHeight: 'none', minHeight: '80px' } : {}\">\r\n              <!-- <h2 class=\"card-title\">计划执行状态</h2> -->\r\n              <div class=\"plan-execution-grid\">\r\n                <div class=\"execution-item\" v-for=\"(item, index) in planExecutionData\" :key=\"item.name\">\r\n                  <i :class=\"getExecutionIcon(index)\" class=\"execution-icon\"></i>\r\n                  <div class=\"execution-text\">\r\n                    <div class=\"execution-value\">{{ item.value }}</div>\r\n                    <div class=\"execution-label\">{{ item.name }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 中间第二行 -->\r\n          <div class=\"center-row center-row-second\" :style=\"isFullscreen ? { maxHeight: 'none', flex: '1' } : {}\">\r\n            <!-- 中间第三个：供方管理 -->\r\n            <div class=\"card supplier-management-card\">\r\n              <h2 class=\"card-title\">供方管理</h2>\r\n              <!-- 上半部分：饼图 -->\r\n              <div id=\"supplierManagementChart\" class=\"chart\" style=\"height: 40px;\"></div>\r\n              <!-- 下半部分：三个数字显示区域 -->\r\n              <div class=\"supplier-stats\">\r\n                <div class=\"supplier-stat-item\">\r\n                  <div class=\"stat-number\">{{ supplierStats.admission }}</div>\r\n                  <div class=\"stat-label\">准入</div>\r\n                </div>\r\n                <div class=\"supplier-stat-item\">\r\n                  <div class=\"stat-number\">{{ supplierStats.elimination }}</div>\r\n                  <div class=\"stat-label\">淘汰</div>\r\n                </div>\r\n                <div class=\"supplier-stat-item\">\r\n                  <div class=\"stat-number\">{{ supplierStats.suspension }}</div>\r\n                  <div class=\"stat-label\">暂缓</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 中间第四个：高频物资 -->\r\n            <div class=\"card clickable-card\" @click=\"goToHighFrequencyDashboard\">\r\n              <h2 class=\"card-title\">高频物资</h2>\r\n              <div class=\"high-frequency-content\">\r\n                <!-- 上半部分：高频采购物料词云 -->\r\n                <div class=\"high-frequency-materials\">\r\n                  <h3 class=\"section-title\">高频采购物料 TOP10</h3>\r\n                  <div id=\"highFrequencyMaterialCloud\" class=\"material-cloud\"></div>\r\n                </div>\r\n\r\n                <!-- 下半部分：物料采购价格和采购量趋势图 -->\r\n                <div class=\"price-trend-section\">\r\n                  <h3 class=\"section-title\">PB块价格及采购量趋势</h3>\r\n                  <div id=\"highFrequencyPriceTrendChart\" class=\"mini-chart\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n           \r\n          </div>\r\n        </div>\r\n\r\n        <!-- 右侧面板 -->\r\n        <div class=\"right-panel\" :style=\"isFullscreen ? { width: '320px', minWidth: '320px', maxWidth: '320px', gap: '12px' } : {}\">\r\n          <!-- 右侧第一个：预警信息 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">预警信息</h2>\r\n            <div class=\"warning-analysis\">\r\n              <div class=\"warning-item\">\r\n                <span class=\"warning-name\">证书过期</span>\r\n                <div class=\"warning-bar\">\r\n                  <div class=\"bar-bg\">\r\n                    <div class=\"bar-fill\" :style=\"{ width: getWarningPercentage(warningInfo.certificateExpiry) + '%' }\"></div>\r\n                  </div>\r\n                  <span class=\"warning-value\">{{ warningInfo.certificateExpiry }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"warning-item\">\r\n                <span class=\"warning-name\">合同过期</span>\r\n                <div class=\"warning-bar\">\r\n                  <div class=\"bar-bg\">\r\n                    <div class=\"bar-fill\" :style=\"{ width: getWarningPercentage(warningInfo.contractExpiry) + '%' }\"></div>\r\n                  </div>\r\n                  <span class=\"warning-value\">{{ warningInfo.contractExpiry }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 右侧第二个：质量异议管理 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">质量异议管理</h2>\r\n            <div class=\"simple-display\">\r\n              <div class=\"display-number\">{{ qualityIssueCount }}</div>\r\n              <div class=\"display-label\">质量异议总数</div>\r\n            </div>\r\n          </div>\r\n\r\n           <!-- 右侧第三个：异常管理 -->\r\n            <div class=\"card\">\r\n              <h2 class=\"card-title\">异常管理</h2>\r\n              <div class=\"funnel-data\">                \r\n              <div class=\"funnel-item\">\r\n                <span class=\"funnel-label\">到货完成度</span>\r\n                <span class=\"funnel-value\">{{ (purchaseStats.arriveRate || 0) + '%' }}</span>\r\n              </div>\r\n              <div class=\"funnel-item\">\r\n                <span class=\"funnel-label\">采购职责不符合数量</span>\r\n                <span class=\"funnel-value\">9000</span>\r\n              </div>\r\n              <div class=\"funnel-item\">\r\n                <span class=\"funnel-label\">计划被驳回数量</span>\r\n                <span class=\"funnel-value\">9000</span>\r\n              </div>\r\n            </div>\r\n            </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport screenfull from 'screenfull'\r\nimport {\r\n  getDashboardData,\r\n  getPersonalConsumption,\r\n  getPurchaseAnalysis,\r\n  getProductAnalysis,\r\n  getMapData,\r\n  getRealTimeStats,\r\n  getSalesFunnel,\r\n  getPurchaseTrend,\r\n  getSupplierAnalysis,\r\n  getAmtManage\r\n} from '@/api/purchaseDashboardMain'\r\nimport {\r\n  showKeyIndicators,\r\n  showHighFrequencyMaterialList,\r\n  getPurchasePriceAndStore,\r\n  showCokingCoalAmount\r\n} from '@/api/purchaseDashboard/purchaseDashboard'\r\n\r\nexport default {\r\n  name: 'PurchaseDashboardMain',\r\n  data() {\r\n    return {\r\n      // 时间过滤器选项\r\n      timeFilters: [\r\n        { id: 'filter-3m', label: '近三个月', value: 1 },\r\n        { id: 'filter-6m', label: '近六个月', value: 2 },\r\n        { id: 'filter-1y', label: '近一年', value: 3 }\r\n      ],\r\n      activeFilter: 'filter-1y',\r\n      currentDimensionType: 3,\r\n\r\n      // 图表实例\r\n      charts: {},\r\n      // 窗口大小变化定时器\r\n      resizeTimer: null,\r\n\r\n      // 高频物资相关数据\r\n      highFrequencyMaterialList: [],\r\n      priceAndStoreData: null,\r\n      selectedCodeType: 'ALL',\r\n      selectedItemType: 'CLASS3',\r\n      selectedMaterial: 'PB块',\r\n\r\n      // 矿焦煤库存相关数据\r\n      cokingCoalInventoryData: [],\r\n      selectedCokingCoalType: '', // 选中的矿焦煤类型，默认为空（全部）\r\n\r\n      // 实时数据\r\n      realTimeStats: {\r\n        sales: '343567',\r\n        orders: '734245'\r\n      },\r\n      // 计划管理数据\r\n      productAnalysisData: [\r\n        { name: '计划总条数', value: '0', percentage: 0 },\r\n        { name: '审核驳回', value: '0%', percentage: 0 },\r\n        { name: '商务部驳回', value: '0%', percentage: 0 },\r\n        { name: '订单至入库平均天数', value: '30000', percentage: 30 },\r\n        { name: '入库至领用平均天数', value: '30000', percentage: 30 },\r\n        { name: '接收至挂单平均天数', value: '40000', percentage: 40 },\r\n        { name: '超期未入库数', value: '40000', percentage: 40 },\r\n        { name: '超期未领用数', value: '40000', percentage: 40 }\r\n\r\n      ],\r\n      // 计划执行状态数据\r\n      planExecutionData: [\r\n        { name: '计划完成率', value: '85%' },\r\n        { name: '在途订单数', value: '1,234' },\r\n        { name: '待审核计划', value: '56' },\r\n        { name: '紧急采购', value: '12' },\r\n        // { name: '供应商响应率', value: '92%' },\r\n        // { name: '库存周转率', value: '3.2' },\r\n        // { name: '采购成本节约', value: '8.5%' },\r\n        // { name: '质量合格率', value: '98.7%' }\r\n      ],\r\n      // 底部统计数据\r\n      bottomStats: [\r\n        { label: '供应商信息全景', value: '34554' },\r\n        { label: '订单量', value: '34554' },\r\n        { label: '客户数', value: '34554' }\r\n      ],\r\n      // 水位图配置\r\n      waterLevelConfig: {\r\n        data: [50],\r\n        shape: 'circle',\r\n        waveNum: 3,\r\n        waveHeight: 40,\r\n        waveOpacity: 0.4,\r\n        colors: ['#00BAFF', '#3DE7C9']\r\n      },\r\n      // 供应商数据（周围四个圆）\r\n      supplierData: [\r\n        { id: 1, value: '8,092', label: '参标次数', color: '#FF6B6B', position: { top: '10%', left: '10%' } },\r\n        { id: 2, value: '1,245', label: '中标次数', color: '#4ECDC4', position: { top: '10%', right: '10%' } },\r\n        { id: 3, value: '89', label: '质量异议次数', color: '#45B7D1', position: { bottom: '10%', left: '10%' } },\r\n        { id: 4, value: '156', label: '合作年限', color: '#96CEB4', position: { bottom: '10%', right: '10%' } }\r\n      ],\r\n      // 中心圆数据（考核情况）\r\n      centerData: {\r\n        label: '考核情况',\r\n        amount: '567万',\r\n        count: '23次',\r\n        color: '#00BAFF'\r\n      },\r\n      // 质量异议总数\r\n      qualityIssueCount: '156',\r\n      // 供方管理统计数据\r\n      supplierStats: {\r\n        admission: '1,245',    // 准入\r\n        elimination: '89',     // 淘汰\r\n        suspension: '156'      // 暂缓\r\n      },\r\n      // 预警信息数据\r\n      warningInfo: {\r\n        certificateExpiry: 0, // 证书过期数量（供应商风险提醒）\r\n        contractExpiry: 0     // 合同过期数量（合同到期提醒）\r\n      },\r\n      // 采购关键指标数据\r\n      purchaseStats: {\r\n        arriveRate: 0  // 到货完成度\r\n      },\r\n      // 资金管理数据\r\n      fundManagement: {\r\n        nextMonth: '0',    // 下月拟入库总金额\r\n        twoMonthsLater: '0', // 2月后拟入库总金额\r\n        threeMonthsLater: '0' // 3月后拟入库总金额\r\n      },\r\n      // 合同管理数据\r\n      contractData: [\r\n        { name: '原材料', count: 12095282 },\r\n        { name: '辅耐材', count: 8340154 },\r\n        { name: '材料类', count: 33344517 },\r\n        { name: '通用备件', count: 76374451 },\r\n        { name: '专用备件', count: 4353921 },\r\n        { name: '办公', count: 23515 }\r\n      ]\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isFullscreen() {\r\n      return this.$store.state.app.isFullscreenMode\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.initFullscreenListener()\r\n    this.initDashboard()\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize)\r\n  },\r\n  beforeDestroy() {\r\n    // 移除事件监听\r\n    window.removeEventListener('resize', this.handleResize)\r\n    // 销毁所有图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      if (chart && chart.dispose) {\r\n        chart.dispose()\r\n      }\r\n    })\r\n    // 移除全屏监听器\r\n    this.removeFullscreenListener()\r\n    // 确保退出全屏模式\r\n    this.$store.dispatch('app/setFullscreenMode', false)\r\n  },\r\n  methods: {\r\n    async initDashboard() {\r\n      try {\r\n        await this.loadData()\r\n        this.$nextTick(() => {\r\n          this.initCharts()\r\n        })\r\n      } catch (error) {\r\n        console.error('初始化驾驶舱失败:', error)\r\n      }\r\n    },\r\n    \r\n    async loadData() {\r\n      try {\r\n        // 并行获取预警信息数据和资金管理数据\r\n        await Promise.all([\r\n          this.fetchWarningData(),\r\n          this.fetchFundManagementData()\r\n        ])\r\n\r\n        // 这里可以并行加载其他数据\r\n        // const [dashboardData, personalData, purchaseData] = await Promise.all([\r\n        //   getDashboardData(),\r\n        //   getPersonalConsumption(),\r\n        //   getPurchaseAnalysis()\r\n        // ])\r\n\r\n        console.log('数据加载完成')\r\n      } catch (error) {\r\n        console.error('加载数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 获取资金管理数据\r\n    async fetchFundManagementData() {\r\n      try {\r\n        const response = await getAmtManage()\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          const data = response.data\r\n\r\n          // 根据reserve1字段找到对应的数据\r\n          const nextMonthData = data.find(item => item.reserve1 === '01')\r\n          const twoMonthsData = data.find(item => item.reserve1 === '02')\r\n          const threeMonthsData = data.find(item => item.reserve1 === '03')\r\n\r\n          // 更新资金管理数据\r\n          this.fundManagement.nextMonth = nextMonthData ? (nextMonthData.reserve4 || '0') : '0'\r\n          this.fundManagement.twoMonthsLater = twoMonthsData ? (twoMonthsData.reserve4 || '0') : '0'\r\n          this.fundManagement.threeMonthsLater = threeMonthsData ? (threeMonthsData.reserve4 || '0') : '0'\r\n\r\n          // 更新图表\r\n          this.updateFundManagementChart()\r\n\r\n          console.log('资金管理数据获取成功:', this.fundManagement)\r\n        } else {\r\n          console.error('资金管理数据格式不正确:', response)\r\n          this.resetFundManagementData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取资金管理数据失败:', error)\r\n        this.resetFundManagementData()\r\n      }\r\n    },\r\n\r\n    // 重置资金管理数据为默认值\r\n    resetFundManagementData() {\r\n      this.fundManagement.nextMonth = '0'\r\n      this.fundManagement.twoMonthsLater = '0'\r\n      this.fundManagement.threeMonthsLater = '0'\r\n      // 更新图表\r\n      this.updateFundManagementChart()\r\n    },\r\n\r\n    // 获取预警信息和计划管理数据\r\n    async fetchWarningData() {\r\n      try {\r\n        // 调用采购全景看板的关键指标接口\r\n        const response = await showKeyIndicators({ dimensionType: this.currentDimensionType })\r\n\r\n        if (response && response.data) {\r\n          const data = response.data\r\n\r\n          // 更新预警信息数据\r\n          // 证书过期数据来自供应商风险提醒\r\n          this.warningInfo.certificateExpiry = data.suppRiskNum || 0\r\n          // 合同过期数据来自合同到期提醒\r\n          this.warningInfo.contractExpiry = data.bpoExpireNum || 0\r\n\r\n          // 更新计划管理数据\r\n          this.updatePlanManagementData(data)\r\n\r\n          // 更新计划执行状态数据\r\n          this.updatePlanExecutionData(data)\r\n\r\n          // 更新采购关键指标数据\r\n          this.purchaseStats.arriveRate = data.arriveRate || 0\r\n\r\n          console.log('预警信息和计划管理数据获取成功:', {\r\n            warningInfo: this.warningInfo,\r\n            planData: {\r\n              planTotalNum: data.planTotalNum,\r\n              rejectNum1: data.rejectNum1,\r\n              rejectNum2: data.rejectNum2\r\n            },\r\n            purchaseStats: this.purchaseStats\r\n          })\r\n        } else {\r\n          console.error('数据获取失败:', response)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取数据失败:', error)\r\n        // 使用默认值\r\n        this.warningInfo.certificateExpiry = 0\r\n        this.warningInfo.contractExpiry = 0\r\n        this.purchaseStats.arriveRate = 0\r\n        this.resetPlanManagementData()\r\n      }\r\n    },\r\n\r\n    // 更新计划管理数据\r\n    updatePlanManagementData(data) {\r\n      // 计划总条数\r\n      if (data.planTotalNum !== undefined) {\r\n        this.productAnalysisData[0].value = data.planTotalNum.toString()\r\n        this.productAnalysisData[0].percentage = Math.min(100, Math.max(0, (data.planTotalNum / 100000) * 100))\r\n      }\r\n\r\n      // 审核驳回（百分比）\r\n      if (data.rejectNum1 !== undefined) {\r\n        this.productAnalysisData[1].value = data.rejectNum1 + '%'\r\n        this.productAnalysisData[1].percentage = Math.min(100, Math.max(0, data.rejectNum1))\r\n      }\r\n\r\n      // 商务部驳回（百分比）\r\n      if (data.rejectNum2 !== undefined) {\r\n        this.productAnalysisData[2].value = data.rejectNum2 + '%'\r\n        this.productAnalysisData[2].percentage = Math.min(100, Math.max(0, data.rejectNum2))\r\n      }\r\n\r\n      // 订单至入库平均天数（绑定到reserve4字段）\r\n      if (data.reserve4 !== undefined) {\r\n        this.productAnalysisData[3].value = data.reserve4.toString() \r\n        this.productAnalysisData[3].percentage = Math.min(100, Math.max(0, (data.reserve4 / 30) * 100)) // 假设30天为100%\r\n      }\r\n    },\r\n\r\n    // 重置计划管理数据为默认值\r\n    resetPlanManagementData() {\r\n      this.productAnalysisData[0].value = '0'\r\n      this.productAnalysisData[0].percentage = 0\r\n      this.productAnalysisData[1].value = '0%'\r\n      this.productAnalysisData[1].percentage = 0\r\n      this.productAnalysisData[2].value = '0%'\r\n      this.productAnalysisData[2].percentage = 0\r\n      this.productAnalysisData[3].value = '0天'\r\n      this.productAnalysisData[3].percentage = 0\r\n    },\r\n\r\n    // 更新计划执行状态数据\r\n    updatePlanExecutionData(data) {\r\n      if (data) {\r\n        // 计划完成率\r\n        if (data.planCompletionRate !== undefined) {\r\n          this.planExecutionData[0].value = data.planCompletionRate + '%'\r\n        }\r\n        // 在途订单数\r\n        if (data.inTransitOrderNum !== undefined) {\r\n          this.planExecutionData[1].value = this.formatNumber(data.inTransitOrderNum)\r\n        }\r\n        // 待审核计划\r\n        if (data.pendingAuditNum !== undefined) {\r\n          this.planExecutionData[2].value = data.pendingAuditNum.toString()\r\n        }\r\n        // 紧急采购\r\n        if (data.urgentPurchaseNum !== undefined) {\r\n          this.planExecutionData[3].value = data.urgentPurchaseNum.toString()\r\n        }\r\n        // 供应商响应率\r\n        if (data.supplierResponseRate !== undefined) {\r\n          this.planExecutionData[4].value = data.supplierResponseRate + '%'\r\n        }\r\n        // 库存周转率\r\n        if (data.inventoryTurnoverRate !== undefined) {\r\n          this.planExecutionData[5].value = data.inventoryTurnoverRate.toString()\r\n        }\r\n        // 采购成本节约\r\n        if (data.costSavingRate !== undefined) {\r\n          this.planExecutionData[6].value = data.costSavingRate + '%'\r\n        }\r\n        // 质量合格率\r\n        if (data.qualityPassRate !== undefined) {\r\n          this.planExecutionData[7].value = data.qualityPassRate + '%'\r\n        }\r\n        console.log('计划执行状态数据更新完成:', this.planExecutionData)\r\n      }\r\n    },\r\n\r\n    // 格式化数字显示（添加千分位分隔符）\r\n    formatNumber(num) {\r\n      if (num === undefined || num === null) return '0'\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\r\n    },\r\n\r\n    // 获取计划管理图标\r\n    getPlanIcon(index) {\r\n      const icons = [\r\n        'el-icon-document', // 计划总条数\r\n        'el-icon-close', // 审核驳回\r\n        'el-icon-error', // 商务部驳回\r\n        'el-icon-time', // 订单至入库平均天数\r\n        'el-icon-truck', // 入库至领用平均天数\r\n        'el-icon-message', // 接收至挂单平均天数\r\n        'el-icon-warning-outline', // 超期未入库数\r\n        'el-icon-warning-outline' // 超期未领用数\r\n      ]\r\n      return icons[index] || 'el-icon-info'\r\n    },\r\n\r\n    // 获取计划执行状态图标\r\n    getExecutionIcon(index) {\r\n      const icons = [\r\n        'el-icon-success', // 计划完成率\r\n        'el-icon-goods', // 在途订单数\r\n        'el-icon-edit-outline', // 待审核计划\r\n        'el-icon-warning', // 紧急采购\r\n        'el-icon-phone', // 供应商响应率\r\n        'el-icon-refresh', // 库存周转率\r\n        'el-icon-coin', // 采购成本节约\r\n        'el-icon-circle-check' // 质量合格率\r\n      ]\r\n      return icons[index] || 'el-icon-info'\r\n    },\r\n\r\n    initCharts() {\r\n      this.initFundManagementChart()\r\n      this.initSupplierManagementChart()\r\n      this.initPersonalConsumptionChart()\r\n      this.initPurchaseAnalysisChart()\r\n      this.initContractChart()\r\n      this.initTrendChart()\r\n      this.initSupplierChart()\r\n      this.fetchHighFrequencyData()\r\n      this.fetchCokingCoalInventoryData()\r\n    },\r\n\r\n    // 资金管理柱状图\r\n    initFundManagementChart() {\r\n      const chartDom = document.getElementById('fundManagementChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.fundManagement = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        grid: {\r\n          left: '15%',\r\n          right: '10%',\r\n          top: '25%',\r\n          bottom: '25%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['下月', '2月后', '3月后'],\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00BAFF'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff',\r\n            fontSize: 12\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '拟入库金额',\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            align: 'right',\r\n          },\r\n          min: 0,\r\n          max: 8000,\r\n          interval: 2000,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00BAFF'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            formatter: function(value) {\r\n              return value\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(0, 186, 255, 0.2)'\r\n            }\r\n          }\r\n        },\r\n        series: [{\r\n          name: '拟入库金额',\r\n          type: 'bar',\r\n          data: [\r\n            parseFloat(this.fundManagement.nextMonth) || 0,\r\n            parseFloat(this.fundManagement.twoMonthsLater) || 0,\r\n            parseFloat(this.fundManagement.threeMonthsLater) || 0\r\n          ],\r\n          barWidth: '50%',\r\n          itemStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0,\r\n              y: 0,\r\n              x2: 0,\r\n              y2: 1,\r\n              colorStops: [{\r\n                offset: 0, color: '#00BAFF' // 顶部颜色\r\n              }, {\r\n                offset: 1, color: '#0080CC' // 底部颜色\r\n              }],\r\n              global: false\r\n            },\r\n            borderRadius: [4, 4, 0, 0] // 顶部圆角\r\n          },\r\n          label: {\r\n            show: true,\r\n            position: 'top',\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            formatter: function(params) {\r\n              return params.value\r\n            }\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: '#33C7FF' // 高亮顶部颜色\r\n                }, {\r\n                  offset: 1, color: '#0099DD' // 高亮底部颜色\r\n                }],\r\n                global: false\r\n              }\r\n            }\r\n          }\r\n        }],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          formatter: function(params) {\r\n            const param = params[0]\r\n            return `${param.name}: ${param.value}`\r\n          }\r\n        }\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 供方管理饼图\r\n    initSupplierManagementChart() {\r\n      this.$nextTick(() => {\r\n        const chartDom = document.getElementById('supplierManagementChart')\r\n        if (!chartDom) {\r\n          console.error('找不到供方管理图表DOM元素')\r\n          return\r\n        }\r\n\r\n        const chart = echarts.init(chartDom)\r\n        this.charts.supplierManagement = chart\r\n\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          tooltip: {\r\n            trigger: 'item',\r\n            backgroundColor: 'rgba(0,0,0,0.8)',\r\n            borderColor: '#00BAFF',\r\n            textStyle: { color: '#fff' }\r\n          },\r\n          series: [{\r\n            type: 'pie',\r\n            radius: '60%',\r\n            center: ['50%', '60%'],\r\n            data: [\r\n              { value: 45, name: '工程' },\r\n              { value: 35, name: '货物' },\r\n              { value: 20, name: '服务' }\r\n            ],\r\n            itemStyle: {\r\n              color: function(params) {\r\n                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']\r\n                return colors[params.dataIndex % colors.length]\r\n              }\r\n            },\r\n            label: {\r\n              color: '#fff',\r\n              fontSize: 15\r\n            }\r\n          }]\r\n        }\r\n\r\n        chart.setOption(option)\r\n      })\r\n    },\r\n\r\n    // 更新资金管理图表数据\r\n    updateFundManagementChart() {\r\n      if (!this.charts.fundManagement) return\r\n\r\n      const newData = [\r\n        parseFloat(this.fundManagement.nextMonth) || 0,\r\n        parseFloat(this.fundManagement.twoMonthsLater) || 0,\r\n        parseFloat(this.fundManagement.threeMonthsLater) || 0\r\n      ]\r\n\r\n      this.charts.fundManagement.setOption({\r\n        series: [{\r\n          data: newData\r\n        }]\r\n      })\r\n    },\r\n\r\n    // 库存管理图表\r\n    initPersonalConsumptionChart() {\r\n      const chartDom = document.getElementById('personalConsumptionChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.personalConsumption = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'item',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        series: [{\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          center: ['50%', '50%'],\r\n          data: [\r\n            { value: 335, name: '矿料' },\r\n            { value: 310, name: '合金' },\r\n            { value: 234, name: '焦炭' },\r\n            { value: 135, name: '辅料/电极' }\r\n          ],\r\n          itemStyle: {\r\n            color: function(params) {\r\n              const colors = ['#00BAFF', '#3DE7C9', '#FFC107', '#FF6B6B']\r\n              return colors[params.dataIndex % colors.length]\r\n            }\r\n          },\r\n          label: {\r\n            color: '#fff',\r\n            fontSize: 12\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 供方管理图表\r\n    initPurchaseAnalysisChart() {\r\n      const chartDom = document.getElementById('purchaseAnalysisChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.purchaseAnalysis = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff' }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff' },\r\n          splitLine: { lineStyle: { color: 'rgba(0,186,255,0.2)' } }\r\n        },\r\n        series: [{\r\n          data: [120, 200, 150, 80, 70, 110],\r\n          type: 'line',\r\n          smooth: true,\r\n          lineStyle: { color: '#00BAFF', width: 2 },\r\n          itemStyle: { color: '#00BAFF' },\r\n          areaStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0, y: 0, x2: 0, y2: 1,\r\n              colorStops: [\r\n                { offset: 0, color: 'rgba(0,186,255,0.3)' },\r\n                { offset: 1, color: 'rgba(0,186,255,0.1)' }\r\n              ]\r\n            }\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 合同管理柱状图\r\n    initContractChart() {\r\n      const chartDom = document.getElementById('contractChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.contract = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' },\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const data = params[0]\r\n            let value = data.value\r\n            let formattedValue = ''\r\n            if (value >= 10000000) {\r\n              formattedValue = (value / 10000000).toFixed(1) + '千万'\r\n            } else if (value >= 10000) {\r\n              formattedValue = (value / 10000).toFixed(1) + '万'\r\n            } else {\r\n              formattedValue = value.toString()\r\n            }\r\n            return `${data.name}<br/>合同数量: ${formattedValue}`\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '20%',\r\n          top: '25%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: this.contractData.map(item => item.name),\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 0,\r\n            rotate: 30,\r\n            fontSize: 10\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '合同数量',\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            align: 'right'\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: function(value) {\r\n              if (value >= 10000000) {\r\n                return (value / 10000000).toFixed(1) + '千万'\r\n              } else if (value >= 10000) {\r\n                return (value / 10000).toFixed(1) + '万'\r\n              } else {\r\n                return value\r\n              }\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: [{\r\n          name: '合同数量',\r\n          type: 'bar',\r\n          data: this.contractData.map(item => item.count),\r\n          itemStyle: {\r\n            color: '#83bff6',\r\n            borderRadius: [4, 4, 0, 0]            \r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              color: '#83bff6',\r\n              borderRadius: [4, 4, 0, 0],\r\n              shadowBlur: 10,\r\n              shadowColor: 'rgba(255, 255, 255, 0.5)',\r\n              borderWidth: 2,\r\n              borderColor: '#fff'\r\n            }\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 趋势分析图表\r\n    initTrendChart() {\r\n      const chartDom = document.getElementById('trendChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.trend = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff', fontSize: 10 }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff', fontSize: 10 },\r\n          splitLine: { lineStyle: { color: 'rgba(0,186,255,0.2)' } }\r\n        },\r\n        series: [{\r\n          data: [820, 932, 901, 934, 1290, 1330, 1320],\r\n          type: 'line',\r\n          smooth: true,\r\n          lineStyle: { color: '#3DE7C9', width: 2 },\r\n          itemStyle: { color: '#3DE7C9' },\r\n          areaStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0, y: 0, x2: 0, y2: 1,\r\n              colorStops: [\r\n                { offset: 0, color: 'rgba(61,231,201,0.3)' },\r\n                { offset: 1, color: 'rgba(61,231,201,0.1)' }\r\n              ]\r\n            }\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 单一来源图表\r\n    initSupplierChart() {\r\n      this.$nextTick(() => {\r\n        const chartDom = document.getElementById('supplierChart')\r\n        console.log('单一来源图表DOM元素:', chartDom)\r\n\r\n        if (!chartDom) {\r\n          console.error('找不到单一来源图表DOM元素')\r\n          return\r\n        }\r\n\r\n        const chart = echarts.init(chartDom)\r\n        this.charts.supplier = chart\r\n\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          tooltip: {\r\n            trigger: 'item',\r\n            backgroundColor: 'rgba(0,0,0,0.8)',\r\n            borderColor: '#00BAFF',\r\n            textStyle: { color: '#fff' }\r\n          },\r\n          series: [{\r\n            type: 'pie',\r\n            radius: '50%',\r\n            center: ['50%', '30%'],\r\n            data: [\r\n              { value: 40, name: '工程' },\r\n              { value: 30, name: '货物' },\r\n              { value: 30, name: '服务' }\r\n            ],\r\n            itemStyle: {\r\n              color: function(params) {\r\n                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']\r\n                return colors[params.dataIndex % colors.length]\r\n              }\r\n            },\r\n            label: {\r\n              color: '#fff',\r\n              fontSize: 10\r\n            }\r\n          }]\r\n        }\r\n\r\n        chart.setOption(option)\r\n      })\r\n    },\r\n\r\n    // 获取高频物资数据\r\n    async fetchHighFrequencyData() {\r\n      try {\r\n        // 获取高频采购物料数据\r\n        await this.fetchHighFrequencyMaterialData()\r\n        // 获取价格趋势数据\r\n        await this.fetchPriceAndStoreData()\r\n        // 初始化词云和价格趋势图\r\n        this.$nextTick(() => {\r\n          this.initHighFrequencyMaterialCloud()\r\n          this.initHighFrequencyPriceTrendChart()\r\n        })\r\n      } catch (error) {\r\n        console.error('获取高频物资数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 获取高频采购物料数据\r\n    async fetchHighFrequencyMaterialData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          codeType: this.selectedCodeType,\r\n          itemType: this.selectedItemType\r\n        }\r\n\r\n        const response = await showHighFrequencyMaterialList(params)\r\n        if (response && response.data) {\r\n          // 取前10条数据\r\n          this.highFrequencyMaterialList = (response.data || []).slice(0, 10)\r\n        } else {\r\n          // 使用模拟数据\r\n          this.highFrequencyMaterialList = this.getMockHighFrequencyData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取高频物料数据失败:', error)\r\n        this.highFrequencyMaterialList = this.getMockHighFrequencyData()\r\n      }\r\n    },\r\n\r\n    // 初始化高频物料词云\r\n    initHighFrequencyMaterialCloud() {\r\n      const chartDom = document.getElementById('highFrequencyMaterialCloud')\r\n      if (!chartDom) {\r\n        console.error('找不到高频物料词云DOM元素')\r\n        return\r\n      }\r\n\r\n      chartDom.innerHTML = ''\r\n\r\n      const rawMaterialList = this.highFrequencyMaterialList\r\n      if (!rawMaterialList || rawMaterialList.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">无高频采购物料数据</div>'\r\n        return\r\n      }\r\n\r\n      // 按入库金额排序，取前15条\r\n      const highFrequencyMaterials = rawMaterialList\r\n        .sort((a, b) => (b.inAmt || 0) - (a.inAmt || 0))\r\n        .slice(0, 15)\r\n\r\n      const container = document.createElement('div')\r\n      container.style.width = '100%'\r\n      container.style.height = '100%'\r\n      container.style.position = 'relative'\r\n      container.style.overflow = 'hidden'\r\n\r\n      const colors = [\r\n        '#4fc3f7', '#a5d6a7', '#ffcc80', '#ef9a9a', '#ce93d8',\r\n        '#90caf9', '#80deea', '#c5e1a5', '#fff59d', '#ffab91'\r\n      ]\r\n\r\n      const maxFontSize = 24\r\n      const minFontSize = 12\r\n\r\n      highFrequencyMaterials.forEach((item, index) => {\r\n        let fontSize = maxFontSize - (index * 1.2)\r\n        if (fontSize < minFontSize) {\r\n          fontSize = minFontSize\r\n        }\r\n\r\n        const div = document.createElement('div')\r\n        div.textContent = item.itemName\r\n        div.style.position = 'absolute'\r\n        div.style.fontSize = `${fontSize}px`\r\n        div.style.fontWeight = 'bold'\r\n        div.style.color = colors[index % colors.length]\r\n        div.style.transform = `rotate(${Math.random() * 30 - 15}deg)`\r\n        div.style.left = `${10 + Math.random() * 60}%`\r\n        div.style.top = `${10 + Math.random() * 60}%`\r\n        div.style.whiteSpace = 'nowrap'\r\n        div.style.textShadow = '1px 1px 2px rgba(0,0,0,0.3)'\r\n        div.style.transition = 'all 0.3s ease'\r\n        div.style.cursor = 'pointer'\r\n        div.style.zIndex = highFrequencyMaterials.length - index\r\n\r\n        // 添加悬停效果\r\n        const self = this\r\n        div.addEventListener('mouseenter', function() {\r\n          this.style.transform = `rotate(${Math.random() * 30 - 15}deg) scale(1.1)`\r\n          this.style.zIndex = '999'\r\n          this.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)'\r\n\r\n          // 显示金额提示\r\n          const tooltip = document.createElement('div')\r\n          tooltip.className = 'material-tooltip'\r\n          tooltip.textContent = `金额: ${self.formatAmount(item.inAmt)}`\r\n          tooltip.style.position = 'absolute'\r\n          tooltip.style.bottom = '-25px'\r\n          tooltip.style.left = '50%'\r\n          tooltip.style.transform = 'translateX(-50%)'\r\n          tooltip.style.background = 'rgba(0,0,0,0.8)'\r\n          tooltip.style.color = '#fff'\r\n          tooltip.style.padding = '2px 6px'\r\n          tooltip.style.borderRadius = '3px'\r\n          tooltip.style.fontSize = '10px'\r\n          tooltip.style.whiteSpace = 'nowrap'\r\n          tooltip.style.zIndex = '1000'\r\n          this.appendChild(tooltip)\r\n        })\r\n\r\n        div.addEventListener('mouseleave', function() {\r\n          this.style.transform = `rotate(${Math.random() * 30 - 15}deg) scale(1)`\r\n          this.style.zIndex = (highFrequencyMaterials.length - index).toString()\r\n          this.style.textShadow = '1px 1px 2px rgba(0,0,0,0.3)'\r\n\r\n          const tooltip = this.querySelector('.material-tooltip')\r\n          if (tooltip) {\r\n            this.removeChild(tooltip)\r\n          }\r\n        })\r\n\r\n        container.appendChild(div)\r\n      })\r\n\r\n      chartDom.appendChild(container)\r\n    },\r\n\r\n    // 获取模拟高频物料数据\r\n    getMockHighFrequencyData() {\r\n      return [\r\n        { itemName: '粗粉', inAmt: 392467.2, inNum: 5421293 },\r\n        { itemName: '精粉', inAmt: 280350.5, inNum: 4250180 },\r\n        { itemName: '球团', inAmt: 195200.8, inNum: 3180970 },\r\n        { itemName: '烧结', inAmt: 150420.3, inNum: 2890540 },\r\n        { itemName: '焦炭', inAmt: 125680.7, inNum: 2350210 },\r\n        { itemName: '煤炭', inAmt: 98750.2, inNum: 1980760 },\r\n        { itemName: 'PB块', inAmt: 85420.1, inNum: 1650430 },\r\n        { itemName: '铁矿石', inAmt: 72350.8, inNum: 1420890 },\r\n        { itemName: '废钢', inAmt: 65280.4, inNum: 1280560 },\r\n        { itemName: '石灰石', inAmt: 58190.6, inNum: 1150320 },\r\n        { itemName: '合金', inAmt: 52180.3, inNum: 980450 },\r\n        { itemName: '电极', inAmt: 48750.9, inNum: 850320 },\r\n        { itemName: '耐火材料', inAmt: 42350.7, inNum: 720180 },\r\n        { itemName: '化工原料', inAmt: 38920.5, inNum: 650290 },\r\n        { itemName: '辅料', inAmt: 35680.2, inNum: 580160 }\r\n      ]\r\n    },\r\n\r\n    // 获取物料价格和采购量数据\r\n    async fetchPriceAndStoreData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemName: 'PB块'  // 固定获取PB块数据\r\n        }\r\n\r\n        const response = await getPurchasePriceAndStore(params)\r\n        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n          this.priceAndStoreData = response.data[0]\r\n          console.log('获取到PB块价格和采购量数据:', this.priceAndStoreData)\r\n        } else {\r\n          console.log('未获取到真实数据，使用模拟数据')\r\n          this.priceAndStoreData = this.getMockPriceAndStoreData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取价格和采购量数据失败:', error)\r\n        this.priceAndStoreData = this.getMockPriceAndStoreData()\r\n      }\r\n    },\r\n\r\n    // 获取模拟的价格和采购量数据（参考采购看板数据结构）\r\n    getMockPriceAndStoreData() {\r\n      return {\r\n        procurementPriceVoList: [\r\n          {\r\n            priceName: 'PB块价格',\r\n            priceList: [\r\n              { recordDate: '20240801', price: 850.0 },\r\n              { recordDate: '20240815', price: 870.5 },\r\n              { recordDate: '20240901', price: 890.2 },\r\n              { recordDate: '20240915', price: 875.8 },\r\n              { recordDate: '20241001', price: 920.3 },\r\n              { recordDate: '20241015', price: 905.7 },\r\n              { recordDate: '20241101', price: 880.4 },\r\n              { recordDate: '20241115', price: 895.6 },\r\n              { recordDate: '20241123', price: 910.2 },  // 2024年11月23日价格\r\n              { recordDate: '20241201', price: 925.8 },\r\n              { recordDate: '20241215', price: 940.1 },\r\n              { recordDate: '20250101', price: 930.5 },\r\n              { recordDate: '20250115', price: 915.3 },\r\n              { recordDate: '20250201', price: 900.7 },\r\n              { recordDate: '20250215', price: 885.9 },\r\n              { recordDate: '20250301', price: 870.2 },\r\n              { recordDate: '20250315', price: 855.8 },\r\n              { recordDate: '20250401', price: 840.6 },\r\n              { recordDate: '20250415', price: 825.4 },\r\n              { recordDate: '20250501', price: 810.9 },\r\n              { recordDate: '20250515', price: 795.7 },\r\n              { recordDate: '20250601', price: 780.3 }\r\n            ]\r\n          }\r\n        ],\r\n        procurementPurchaseAmountVoList: [\r\n          {\r\n            amountName: 'PB块采购量',\r\n            amountList: [\r\n              { recordDate: '20240801', amount: 125000 },   // 12.5万吨\r\n              { recordDate: '20240815', amount: 118000 },   // 11.8万吨\r\n              { recordDate: '20240901', amount: 132000 },   // 13.2万吨\r\n              { recordDate: '20240915', amount: 145000 },   // 14.5万吨\r\n              { recordDate: '20241001', amount: 138000 },   // 13.8万吨\r\n              { recordDate: '20241015', amount: 152000 },   // 15.2万吨\r\n              { recordDate: '20241101', amount: 168000 },   // 16.8万吨\r\n              { recordDate: '20241115', amount: 175000 },   // 17.5万吨\r\n              { recordDate: '20241123', amount: 100000 },   // 10万吨\r\n              { recordDate: '20241201', amount: 185000 },   // 18.5万吨\r\n              { recordDate: '20241215', amount: 192000 },   // 19.2万吨\r\n              { recordDate: '20250101', amount: 178000 },   // 17.8万吨\r\n              { recordDate: '20250115', amount: 165000 },   // 16.5万吨\r\n              { recordDate: '20250201', amount: 158000 },   // 15.8万吨\r\n              { recordDate: '20250215', amount: 142000 },   // 14.2万吨\r\n              { recordDate: '20250301', amount: 135000 },   // 13.5万吨\r\n              { recordDate: '20250315', amount: 128000 },   // 12.8万吨\r\n              { recordDate: '20250401', amount: 121000 },   // 12.1万吨\r\n              { recordDate: '20250415', amount: 115000 },   // 11.5万吨\r\n              { recordDate: '20250501', amount: 108000 },   // 10.8万吨\r\n              { recordDate: '20250515', amount: 102000 },   // 10.2万吨\r\n              { recordDate: '20250601', amount: 95000 }     // 9.5万吨\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n    },\r\n\r\n    // 初始化高频物资价格趋势图（完全按照图片效果重写）\r\n    initHighFrequencyPriceTrendChart() {\r\n      this.$nextTick(() => {\r\n        const chartDom = document.getElementById('highFrequencyPriceTrendChart')\r\n        if (!chartDom) {\r\n          console.error('找不到高频物资价格趋势图DOM元素')\r\n          return\r\n        }\r\n\r\n        // 清理现有实例\r\n        if (this.charts.highFrequencyPriceTrend) {\r\n          this.charts.highFrequencyPriceTrend.dispose()\r\n        }\r\n\r\n        const chart = echarts.init(chartDom)\r\n        this.charts.highFrequencyPriceTrend = chart\r\n\r\n        // 使用真实数据结构\r\n        const priceAndStoreData = this.priceAndStoreData\r\n        if (!priceAndStoreData) {\r\n          chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无价格趋势数据</div>'\r\n          return\r\n        }\r\n\r\n        // 收集所有日期\r\n        let allDates = new Set()\r\n\r\n        // 从价格数据中收集日期\r\n        if (priceAndStoreData.procurementPriceVoList) {\r\n          priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {\r\n            if (priceGroup.priceList) {\r\n              priceGroup.priceList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n\r\n        // 从采购量数据中收集日期\r\n        if (priceAndStoreData.procurementPurchaseAmountVoList) {\r\n          priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {\r\n            if (amountGroup.amountList) {\r\n              amountGroup.amountList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n\r\n        // 转换为排序的数组\r\n        allDates = Array.from(allDates).sort()\r\n\r\n        if (allDates.length === 0) {\r\n          chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无价格趋势数据</div>'\r\n          return\r\n        }\r\n\r\n        // 构建系列数据\r\n        const series = []\r\n        const legendData = []\r\n\r\n        // 构建价格系列（蓝色线）\r\n        if (priceAndStoreData.procurementPriceVoList) {\r\n          priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {\r\n            const priceData = allDates.map(date => {\r\n              const found = priceGroup.priceList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.price) : null\r\n            })\r\n\r\n            series.push({\r\n              name: priceGroup.priceName,\r\n              type: 'line',\r\n              yAxisIndex: 0,\r\n              data: priceData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 2,\r\n                color: '#00d4ff'  // 蓝色\r\n              },\r\n              itemStyle: {\r\n                color: '#00d4ff'\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 4,\r\n              connectNulls: true\r\n            })\r\n\r\n            legendData.push(priceGroup.priceName)\r\n          })\r\n        }\r\n\r\n        // 构建采购量系列（橙色线）\r\n        if (priceAndStoreData.procurementPurchaseAmountVoList) {\r\n          priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {\r\n            const amountData = allDates.map(date => {\r\n              const found = amountGroup.amountList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.amount) : null\r\n            })\r\n\r\n            series.push({\r\n              name: amountGroup.amountName,\r\n              type: 'line',\r\n              yAxisIndex: 1,\r\n              data: amountData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 2,\r\n                color: '#ff9f7f'  // 橙色\r\n              },\r\n              itemStyle: {\r\n                color: '#ff9f7f'\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 4,\r\n              connectNulls: true\r\n            })\r\n\r\n            legendData.push(amountGroup.amountName)\r\n          })\r\n        }\r\n\r\n        // 计算Y轴范围\r\n        let priceMin, priceMax\r\n\r\n        // 计算价格轴范围（左轴）\r\n        const priceValues = series.filter(s => s.yAxisIndex === 0)\r\n          .flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n        if (priceValues.length > 0) {\r\n          priceMin = Math.min(...priceValues)\r\n          priceMax = Math.max(...priceValues)\r\n        }\r\n\r\n        // 采购量轴范围固定为5万-20万\r\n\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: {\r\n              type: 'cross',\r\n              crossStyle: {\r\n                color: '#999'\r\n              }\r\n            },\r\n            backgroundColor: 'rgba(0,0,0,0.8)',\r\n            borderColor: '#00d4ff',\r\n            borderWidth: 1,\r\n            textStyle: {\r\n              color: '#fff'\r\n            },\r\n            formatter: function(params) {\r\n              let str = params[0].axisValueLabel + '<br/>'\r\n              params.forEach(item => {\r\n                if (item.value !== null && item.value !== undefined) {\r\n                  if (item.seriesName.includes('价格') || item.seriesName.includes('价')) {\r\n                    str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`\r\n                  } else {\r\n                    // 采购量显示为万吨\r\n                    const valueInWan = (parseFloat(item.value) / 10000).toFixed(1)\r\n                    str += `${item.marker}${item.seriesName}: ${valueInWan} 万吨<br/>`\r\n                  }\r\n                } else {\r\n                  str += `${item.marker}${item.seriesName}: -<br/>`\r\n                }\r\n              })\r\n              return str\r\n            }\r\n          },\r\n          legend: {\r\n            data: legendData,\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 12\r\n            },\r\n            top: '0%',\r\n            right: '10%',\r\n            itemGap: 5,\r\n            orient: 'horizontal'\r\n          },\r\n          grid: {\r\n            left: '12%',\r\n            right: '12%',\r\n            bottom: '8%',\r\n            top: '32%',\r\n            containLabel: false\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: allDates.map(date => {\r\n              const year = date.substring(0, 4)\r\n              const month = parseInt(date.substring(4, 6))\r\n              return `${year}.${month}`\r\n            }),\r\n            axisLabel: {\r\n              color: '#fff',\r\n              fontSize: 11,\r\n              interval: 'auto'\r\n            },\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#666'\r\n              }\r\n            },\r\n            axisTick: {\r\n              show: false\r\n            }\r\n          },\r\n          yAxis: [\r\n            {\r\n              type: 'value',\r\n              name: '价格(元/吨)',\r\n              nameLocation: 'middle',\r\n              nameGap: 35,\r\n              nameRotate: 90,\r\n              nameTextStyle: {\r\n                color: '#fff',\r\n                fontSize: 11,\r\n                fontWeight: 'normal'\r\n              },\r\n              position: 'left',\r\n              min: priceMin,\r\n              max: priceMax,\r\n              axisLine: {\r\n                show: true,\r\n                lineStyle: {\r\n                  color: '#666'\r\n                }\r\n              },\r\n              axisLabel: {\r\n                color: '#fff',\r\n                fontSize: 10\r\n              },\r\n              splitLine: {\r\n                show: true,\r\n                lineStyle: {\r\n                  color: 'rgba(255,255,255,0.1)',\r\n                  type: 'dashed'\r\n                }\r\n              },\r\n              axisTick: {\r\n                show: false\r\n              }\r\n            },\r\n            {\r\n              type: 'value',\r\n              name: '采购量(万吨)',\r\n              nameLocation: 'middle',\r\n              nameGap: 35,\r\n              nameRotate: -90,\r\n              nameTextStyle: {\r\n                color: '#fff',\r\n                fontSize: 11,\r\n                fontWeight: 'normal'\r\n              },\r\n              position: 'right',\r\n              min: 50000,  // 5万\r\n              max: 200000, // 20万\r\n              axisLine: {\r\n                show: true,\r\n                lineStyle: {\r\n                  color: '#666'\r\n                }\r\n              },\r\n              axisLabel: {\r\n                color: '#fff',\r\n                fontSize: 10,\r\n                formatter: function(value) {\r\n                  return (value / 10000).toFixed(0)\r\n                }\r\n              },\r\n              splitLine: {\r\n                show: false\r\n              },\r\n              axisTick: {\r\n                show: false\r\n              }\r\n            }\r\n          ],\r\n          series: series\r\n        }\r\n\r\n        chart.setOption(option, true)\r\n      })\r\n    },\r\n\r\n    // 格式化金额显示\r\n    formatAmount(amount) {\r\n      if (amount >= 10000) {\r\n        return (amount / 10000).toFixed(1) + '万'\r\n      }\r\n      return amount.toFixed(1)\r\n    },\r\n\r\n    // 处理窗口大小变化\r\n    handleResize() {\r\n      // 延迟执行，避免频繁触发\r\n      clearTimeout(this.resizeTimer)\r\n      this.resizeTimer = setTimeout(() => {\r\n        Object.values(this.charts).forEach(chart => {\r\n          if (chart && chart.resize) {\r\n            chart.resize()\r\n          }\r\n        })\r\n      }, 300)\r\n    },\r\n\r\n    // 初始化全屏监听器\r\n    initFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.on('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 移除全屏监听器\r\n    removeFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.off('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 处理全屏状态变化\r\n    handleFullscreenChange() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        const isFullscreen = screenfull.isFullscreen\r\n        this.$store.dispatch('app/setFullscreenMode', isFullscreen)\r\n\r\n        console.log('全屏状态变化:', isFullscreen) // 调试信息\r\n        console.log('Store状态:', this.$store.state.app.isFullscreenMode) // 调试Store状态\r\n\r\n        // 全屏状态变化后，重新调整图表大小\r\n        this.$nextTick(() => {\r\n          setTimeout(() => {\r\n            this.handleResize()\r\n          }, 300) // 给布局变化一些时间\r\n        })\r\n      }\r\n    },\r\n\r\n    // 切换全屏\r\n    toggleFullscreen() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.toggle()\r\n      } else {\r\n        this.$message({\r\n          message: '您的浏览器不支持全屏功能',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 时间过滤器变化处理\r\n    handleTimeFilterChange(filterId, dimensionType) {\r\n      this.activeFilter = filterId\r\n      this.currentDimensionType = dimensionType\r\n      console.log('选择的时间范围:', filterId, '维度:', dimensionType)\r\n\r\n      // 根据时间范围重新加载数据\r\n      this.fetchWarningData()\r\n      this.fetchFundManagementData()\r\n    },\r\n\r\n    // 跳转到供应商处罚页面\r\n    goToSupplierPenalty() {\r\n      // 在新窗口中打开供应商处罚页面\r\n      const routeUrl = this.$router.resolve('/purchase/suppPunishment')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 跳转到采购库存看板\r\n    goToStockDashboard() {\r\n      console.log('跳转到采购库存看板')\r\n      // 在新窗口中打开采购库存看板页面\r\n      const routeUrl = this.$router.resolve('/purchaseDashboardStock')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 跳转到高频物料看板\r\n    goToHighFrequencyDashboard() {\r\n      console.log('跳转到高频物料看板')\r\n      // 在新窗口中打开高频物料看板页面\r\n      const routeUrl = this.$router.resolve('/purchaseDashboardPrice')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 跳转到采购计划看板\r\n    goToPlanDashboard() {\r\n      console.log('跳转到采购计划看板')\r\n      // 在新窗口中打开采购计划看板页面\r\n      const routeUrl = this.$router.resolve('/purchaseDashboardPlan')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 获取矿焦煤库存数据\r\n    async fetchCokingCoalInventoryData() {\r\n      try {\r\n        const response = await showCokingCoalAmount()\r\n        console.log('fetchCokingCoalInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.cokingCoalInventoryData = response.data || []\r\n          console.log('fetchCokingCoalInventoryData - 设置的数据:', this.cokingCoalInventoryData)\r\n        } else {\r\n          console.error('获取矿焦煤库存数据失败', response)\r\n          this.cokingCoalInventoryData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取矿焦煤库存数据失败:', error)\r\n        this.cokingCoalInventoryData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalLineChart()\r\n      })\r\n    },\r\n\r\n    // 计算矿焦煤库存总量\r\n    calculateCokingCoalTotal() {\r\n      let total = 0\r\n      if (this.cokingCoalInventoryData && this.cokingCoalInventoryData.length > 0) {\r\n        // 找到最新日期\r\n        let latestDate = ''\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            item.purchaseCokingDailyDetailList.forEach(detail => {\r\n              if (detail.instockDate > latestDate) {\r\n                latestDate = detail.instockDate\r\n              }\r\n            })\r\n          }\r\n        })\r\n\r\n        // 计算最新日期各个物料的库存量合计\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)\r\n            if (latestDetail) {\r\n              total += parseFloat(latestDetail.invQty) || 0\r\n            }\r\n          }\r\n        })\r\n      }\r\n      return (total / 10000).toFixed(2) // 转换为万吨\r\n    },\r\n\r\n    // 处理矿焦煤类型下拉框变化\r\n    async handleCokingCoalTypeChange() {\r\n      console.log('矿焦煤类型变化:', this.selectedCokingCoalType)\r\n      // 重新初始化图表以应用过滤\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalLineChart()\r\n      })\r\n    },\r\n\r\n    // 获取矿焦煤物料类型的颜色映射\r\n    getCokingCoalMaterialColorMap() {\r\n      // 使用与库存看板一致的颜色方案\r\n      const baseColors = ['#0066ff', '#00ff00', '#ff0000', '#8b00ff', '#ffff00', '#ffffff']\r\n\r\n      // 基于所有原始数据为每个物料类型分配固定颜色，确保过滤时颜色保持一致\r\n      const allMaterialTypes = []\r\n      const inventoryData = this.cokingCoalInventoryData || []\r\n\r\n      // 收集所有物料类型\r\n      inventoryData.forEach(item => {\r\n        const materialName = item.class2Name || '未知物料'\r\n        if (!allMaterialTypes.includes(materialName)) {\r\n          allMaterialTypes.push(materialName)\r\n        }\r\n      })\r\n\r\n      // 按字母顺序排序，确保颜色分配的一致性\r\n      allMaterialTypes.sort()\r\n\r\n      // 为每个物料类型分配固定颜色\r\n      const colorMap = {}\r\n      allMaterialTypes.forEach((materialName, index) => {\r\n        colorMap[materialName] = baseColors[index % baseColors.length]\r\n      })\r\n\r\n      return colorMap\r\n    },\r\n\r\n    // 初始化矿焦煤库存折线图\r\n    initCokingCoalLineChart() {\r\n      const chartDom = document.getElementById('cokingCoalLineChart')\r\n      if (!chartDom) {\r\n        console.error('找不到矿焦煤折线图DOM: cokingCoalLineChart')\r\n        return\r\n      }\r\n\r\n      // 清理现有实例\r\n      if (this.charts.cokingCoalLineChart) {\r\n        this.charts.cokingCoalLineChart.dispose()\r\n      }\r\n\r\n      const myChart = echarts.init(chartDom)\r\n      this.charts.cokingCoalLineChart = myChart\r\n\r\n      const inventoryData = this.cokingCoalInventoryData || []\r\n\r\n      if (!inventoryData || inventoryData.length === 0) {\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          title: {\r\n            text: '暂无数据',\r\n            left: 'center',\r\n            top: 'middle',\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 16\r\n            }\r\n          }\r\n        }\r\n        myChart.setOption(option)\r\n        return\r\n      }\r\n\r\n      // 根据选中的类型过滤数据（使用与库存看板一致的过滤逻辑）\r\n      let filteredData = inventoryData\r\n      if (this.selectedCokingCoalType && this.selectedCokingCoalType !== '') {\r\n        filteredData = inventoryData.filter(item => {\r\n          return item.class2Name === this.selectedCokingCoalType ||\r\n                 item.class2Name.includes(this.selectedCokingCoalType) ||\r\n                 this.selectedCokingCoalType.includes(item.class2Name)\r\n        })\r\n      }\r\n\r\n      // 提取所有日期并排序\r\n      const allDates = new Set()\r\n      filteredData.forEach(item => {\r\n        if (item.purchaseCokingDailyDetailList) {\r\n          item.purchaseCokingDailyDetailList.forEach(detail => {\r\n            allDates.add(detail.instockDate)\r\n          })\r\n        }\r\n      })\r\n\r\n      const sortedDates = Array.from(allDates).sort()\r\n\r\n      // 格式化日期显示（从yyyyMMdd转换为MM-dd）\r\n      const formattedDates = sortedDates.map(dateStr => {\r\n        if (dateStr && dateStr.length === 8) {\r\n          const month = dateStr.substring(4, 6)\r\n          const day = dateStr.substring(6, 8)\r\n          return `${month}-${day}`\r\n        }\r\n        return dateStr\r\n      })\r\n\r\n      // 构建每个类型的曲线数据\r\n      const seriesData = []\r\n      const legendData = []\r\n\r\n      // 获取统一的颜色映射\r\n      const colorMap = this.getCokingCoalMaterialColorMap()\r\n\r\n      filteredData.forEach((item, index) => {\r\n        const typeName = item.class2Name || '未知物料'\r\n\r\n        // 为每个日期构建数据点\r\n        const lineData = sortedDates.map(date => {\r\n          const detail = item.purchaseCokingDailyDetailList?.find(d => d.instockDate === date)\r\n          return detail ? parseFloat(detail.invQty) || 0 : null\r\n        })\r\n\r\n        // 使用统一的颜色映射\r\n        const materialColor = colorMap[typeName] || '#83bff6'\r\n\r\n        const seriesItem = {\r\n          name: typeName,\r\n          type: 'line',\r\n          data: lineData,\r\n          smooth: true,\r\n          symbol: 'circle',\r\n          symbolSize: 6,\r\n          lineStyle: {\r\n            width: 3,\r\n            color: materialColor\r\n          },\r\n          itemStyle: {\r\n            color: materialColor\r\n          },\r\n          connectNulls: false\r\n        }\r\n\r\n        seriesData.push(seriesItem)\r\n        legendData.push(typeName)\r\n      })\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          },\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00d4ff',\r\n          borderWidth: 1,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              if (param.value !== null && param.value !== undefined) {\r\n                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 吨<br/>`\r\n              }\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          bottom: '5%',\r\n          left: 'center'\r\n        },\r\n        grid: {\r\n          left: '8%',\r\n          right: '5%',\r\n          bottom: '25%',\r\n          top: '15%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: formattedDates,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00d4ff'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff'\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '库存量(吨)',\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            align:'right',\r\n            fontSize: 9,\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00d4ff'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff'\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(0, 212, 255, 0.2)'\r\n            }\r\n          }\r\n        },\r\n        series: seriesData\r\n      }\r\n\r\n      myChart.setOption(option)\r\n    },\r\n\r\n    // 计算预警信息的百分比\r\n    getWarningPercentage(value) {\r\n      const numValue = parseInt(value) || 0\r\n\r\n      // 如果值为0，返回0%\r\n      if (numValue === 0) {\r\n        return 0\r\n      }\r\n\r\n      // 获取两个预警值中的最大值作为基准\r\n      const certificateValue = parseInt(this.warningInfo.certificateExpiry) || 0\r\n      const contractValue = parseInt(this.warningInfo.contractExpiry) || 0\r\n      const maxValue = Math.max(certificateValue, contractValue, 1) // 至少为1，避免除0\r\n\r\n      // 计算相对百分比，确保最大值显示为100%\r\n      const percentage = (numValue / maxValue) * 100\r\n      return Math.min(100, Math.max(0, percentage))\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.purchase-dashboard-main {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n\r\n  .dashboard-container {\r\n    width: 100%;\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n    color: #fff;\r\n    overflow-x: hidden;\r\n    padding: 10px;\r\n  }\r\n\r\n  .dashboard-header {\r\n    text-align: center;\r\n    margin-bottom: 10px;\r\n    position: relative;\r\n    padding: 5px 0;\r\n\r\n    h1 {\r\n      font-size: 24px;\r\n      position: relative;\r\n      display: inline-block;\r\n      padding: 5px 40px;\r\n      margin: 0;\r\n      color: #fff;\r\n    }\r\n\r\n    &::before,\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 50%;\r\n      width: 30%;\r\n      height: 2px;\r\n      background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n    }\r\n\r\n    &::before {\r\n      left: 0;\r\n    }\r\n\r\n    &::after {\r\n      right: 0;\r\n    }\r\n  }\r\n\r\n  .header-controls {\r\n    position: absolute;\r\n    right: 20px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 15px;\r\n    z-index: 1000;\r\n  }\r\n\r\n  .fullscreen-btn {\r\n    padding: 8px 12px;\r\n    border: none;\r\n    background-color: rgba(33, 10, 56, 0.7);\r\n    color: #eee;\r\n    border-radius: 20px;\r\n    font-size: 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    border: 1px solid rgba(0, 212, 255, 0.2);\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 40px;\r\n    height: 32px;\r\n\r\n    &:hover {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n      background-color: rgba(0, 212, 255, 0.2);\r\n      border-color: rgba(0, 212, 255, 0.7);\r\n      color: #00ffff;\r\n    }\r\n  }\r\n\r\n  .time-filter {\r\n    display: flex;\r\n    gap: 10px;\r\n  }\r\n\r\n  .time-filter-btn {\r\n    padding: 6px 12px;\r\n    border: none;\r\n    background-color: rgba(33, 10, 56, 0.7);\r\n    color: #eee;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    border: 1px solid rgba(0, 212, 255, 0.2);\r\n    position: relative;\r\n\r\n    &:hover {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n    }\r\n\r\n    &.active {\r\n      background-color: rgba(0, 212, 255, 0.2);\r\n      border-color: rgba(0, 212, 255, 0.7);\r\n      color: #00ffff;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  .dashboard-content {\r\n    display: flex;\r\n    height: calc(100vh - 80px);\r\n    gap: 10px;\r\n  }\r\n\r\n  .left-panel,\r\n  .right-panel {\r\n    flex: 0 0 320px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .center-panel {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 5px; /* 缩短行间距从10px到5px */\r\n  }\r\n\r\n  .center-row {\r\n    flex: 1;\r\n    display: flex;\r\n    gap: 5px; /* 缩短卡片间距从10px到5px */\r\n  }\r\n\r\n  .center-row .card {\r\n    flex: 1;\r\n  }\r\n\r\n  /* 第一行特定样式 - 缩短高度 */\r\n  .center-row-first {\r\n    flex: 1; /* 减小第一行的高度比例 */\r\n    max-height: 250px; /* 进一步限制第一行的最大高度 */\r\n  }\r\n\r\n  /* 第二行特定样式 - 缩短高度 */\r\n  .center-row-second {\r\n    flex: 0.7; /* 进一步减小第二行的高度比例 */\r\n    max-height: 330px; /* 进一步限制第二行的最大高度 */\r\n  }\r\n\r\n  /* 全屏模式下的样式调整 - 使用更高优先级的选择器 */\r\n  .purchase-dashboard-main.fullscreen-mode {\r\n    /* 调试样式 - 全屏时改变背景色 */\r\n    background: linear-gradient(135deg, #2a2a90, #5B1082, #900090) !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-first {\r\n    max-height: none !important; /* 全屏时移除第一行高度限制 */\r\n    flex: 1 !important; /* 确保flex比例正确 */\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-second {\r\n    max-height: none !important; /* 全屏时移除第二行高度限制 */\r\n    flex: 1 !important; /* 确保flex比例正确 */\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-full .card {\r\n    max-height: none !important; /* 全屏时移除全宽行高度限制 */\r\n    min-height: 60px !important; /* 保持最小高度 */\r\n  }\r\n\r\n  /* 全屏模式下调整整体容器高度和布局 */\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-content {\r\n    height: calc(100vh - 60px) !important; /* 全屏时减去标题高度 */\r\n    min-height: calc(100vh - 60px) !important;\r\n    display: flex !important;\r\n    gap: 10px !important;\r\n    width: 100% !important;\r\n    justify-content: center !important; /* 居中对齐 */\r\n    align-items: stretch !important;\r\n    overflow-x: auto !important; /* 允许水平滚动以防内容过宽 */\r\n    padding: 0 10px !important; /* 添加一些内边距 */\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n  /* 新增：全宽行样式 */\r\n  .center-row-full {\r\n    width: 100%;\r\n    margin: 2px 0; /* 缩短上下边距从5px到2px */\r\n    flex-shrink: 0; /* 防止被压缩 */\r\n  }\r\n\r\n  .center-row-full .card {\r\n    width: 100%;\r\n    min-height: 50px; /* 设置最小高度 */\r\n    max-height: 80px; /* 设置最大高度，确保不占用太多空间 */\r\n  }\r\n\r\n  .left-panel .card,\r\n  .right-panel .card {\r\n    flex: 1;\r\n  }\r\n\r\n  /* 全屏模式下右侧面板的特殊样式 - 解决高度被过度拉伸的问题 */\r\n  .purchase-dashboard-main.fullscreen-mode .right-panel {\r\n    /* 改变右侧面板的布局方式，平均分配空间而不是拉伸 */\r\n    justify-content: space-between !important;\r\n    align-items: stretch !important;\r\n    width: 320px !important; /* 固定右侧面板宽度 */\r\n    min-width: 320px !important;\r\n    max-width: 320px !important;\r\n    flex: none !important;\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .right-panel .card {\r\n    flex: 0 0 calc(33.33% - 8px) !important; /* 三等分，减去间距 */\r\n    height: auto !important; /* 让内容决定高度 */\r\n    min-height: 120px !important; /* 设置更小的最小高度 */\r\n    max-height: 200px !important; /* 限制最大高度，更加紧凑 */\r\n    overflow-y: auto !important; /* 内容过多时滚动 */\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .left-panel {\r\n    justify-content: space-between !important;\r\n    width: 320px !important; /* 固定左侧面板宽度 */\r\n    min-width: 320px !important;\r\n    max-width: 320px !important;\r\n    flex: none !important;\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .center-panel {\r\n    flex: 1 !important; /* 中间面板占用剩余空间 */\r\n    min-width: 400px !important; /* 最小宽度保证内容显示 */\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .left-panel .card {\r\n    flex: 0 0 calc(50% - 6px) !important; /* 二等分，减去间距 */\r\n    height: auto !important;\r\n    min-height: 140px !important;\r\n    max-height: 260px !important;\r\n    overflow-y: auto !important;\r\n  }\r\n\r\n  /* 全屏模式下优化具体内容的显示 - 缩小内容 */\r\n  .purchase-dashboard-main.fullscreen-mode .card-title {\r\n    font-size: 14px !important;\r\n    margin-bottom: 8px !important;\r\n    padding: 8px 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-analysis {\r\n    padding: 6px 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-item {\r\n    margin-bottom: 6px !important;\r\n    padding: 4px 0 !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-name {\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-value {\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .simple-display {\r\n    padding: 10px 12px !important;\r\n    text-align: center !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .display-number {\r\n    font-size: 24px !important;\r\n    margin-bottom: 4px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .display-label {\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-data {\r\n    padding: 6px 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-item {\r\n    margin-bottom: 4px !important;\r\n    padding: 3px 0 !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-label {\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-value {\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  /* 全屏模式下缩小警告条的高度 */\r\n  .purchase-dashboard-main.fullscreen-mode .warning-bar {\r\n    height: 16px !important;\r\n    margin-left: 8px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .bar-bg {\r\n    height: 16px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .bar-fill {\r\n    height: 16px !important;\r\n  }\r\n\r\n  /* 全屏模式下调整卡片内边距 */\r\n  .purchase-dashboard-main.fullscreen-mode .right-panel .card {\r\n    padding: 8px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .left-panel .card {\r\n    padding: 8px !important;\r\n  }\r\n\r\n  .card {\r\n    background-color: rgba(33, 10, 56, 0.7);\r\n    border-radius: 5px;\r\n    padding: 10px;\r\n    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);\r\n    position: relative;\r\n\r\n    // 计划管理简化样式\r\n    &.plan-management-card {\r\n      .plan-grid {\r\n        display: grid;\r\n        grid-template-columns: 1fr 1fr;\r\n        gap: 4px; /* 缩短网格间距从8px到4px */\r\n        padding: 5px 0; /* 缩短上下内边距从10px到5px */\r\n\r\n        .plan-item {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 6px 35px; /* 缩短内边距从8px到4px 6px */\r\n          border-radius: 6px;\r\n          transition: background 0.2s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 186, 255, 0.1);\r\n          }\r\n\r\n          .plan-icon {\r\n            font-size: 16px;\r\n            color: #00BAFF;\r\n            margin-right: 6px; /* 缩短右边距从8px到6px */\r\n            width: 18px; /* 缩短宽度从20px到18px */\r\n            text-align: center;\r\n          }\r\n\r\n          .plan-text {\r\n            flex: 1;\r\n            min-width: 0;\r\n\r\n            .plan-value {\r\n              color: #fff;\r\n              font-size: 14px;\r\n              font-weight: bold;\r\n              line-height: 1.1; /* 缩短行高从1.2到1.1 */\r\n              margin-bottom: 1px; /* 缩短下边距从2px到1px */\r\n            }\r\n\r\n            .plan-label {\r\n              color: rgba(255, 255, 255, 0.8);\r\n              font-size: 11px;\r\n              font-weight: bold;\r\n              line-height: 1.1; /* 缩短行高从1.2到1.1 */\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 计划执行状态样式\r\n    &.plan-execution-card {\r\n      .plan-execution-grid {\r\n        display: grid;\r\n        grid-template-columns: repeat(4, 1fr);\r\n        gap: 12px;\r\n        padding: 2px 0;\r\n\r\n        .execution-item {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 10px;\r\n          border-radius: 8px;\r\n          background: rgba(0, 186, 255, 0.1);\r\n          border: 1px solid rgba(0, 186, 255, 0.3);\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 186, 255, 0.2);\r\n            border-color: rgba(0, 186, 255, 0.5);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 186, 255, 0.3);\r\n          }\r\n\r\n          .execution-icon {\r\n            font-size: 18px;\r\n            color: #00BAFF;\r\n            margin-right: 10px;\r\n            width: 22px;\r\n            text-align: center;\r\n            flex-shrink: 0;\r\n          }\r\n\r\n          .execution-text {\r\n            flex: 1;\r\n            min-width: 0;\r\n\r\n            .execution-value {\r\n              color: #fff;\r\n              font-size: 16px;\r\n              font-weight: bold;\r\n              line-height: 1.2;\r\n              margin-bottom: 3px;\r\n            }\r\n\r\n            .execution-label {\r\n              color: rgba(255, 255, 255, 0.8);\r\n              font-size: 12px;\r\n              line-height: 1.2;\r\n              font-weight: bold;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    overflow: hidden; // 恢复hidden，防止重叠\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 2px;\r\n      background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n    }\r\n  }\r\n\r\n  .clickable-card {\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 5px 20px rgba(0, 212, 255, 0.3);\r\n      background-color: rgba(33, 10, 56, 0.9);\r\n    }\r\n\r\n    &:active {\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 14px;\r\n    margin-bottom: 5px;\r\n    font-weight: bold;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    color: #fff;\r\n  }\r\n\r\n  .inventory-total {\r\n    font-size: 12px;\r\n    color: #00d4ff;\r\n    font-weight: normal;\r\n    background: rgba(0, 212, 255, 0.1);\r\n    padding: 2px 8px;\r\n    border-radius: 4px;\r\n    border: 1px solid rgba(0, 212, 255, 0.3);\r\n  }\r\n\r\n  .chart-filter-dropdown-container {\r\n    z-index: 10;\r\n  }\r\n\r\n  .chart-filter-dropdown-container select {\r\n    padding: 4px 8px;\r\n    border-radius: 4px;\r\n    background-color: rgba(138, 43, 226, 0.7);\r\n    color: #fff;\r\n    border: 1px solid rgba(0, 212, 255, 0.3);\r\n    font-size: 12px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .chart-filter-dropdown-container select:hover {\r\n    background-color: rgba(138, 43, 226, 0.9);\r\n    border-color: rgba(0, 212, 255, 0.6);\r\n  }\r\n\r\n  .chart {\r\n    flex: 1;\r\n    width: 100%;\r\n    min-height: 150px;\r\n  }\r\n\r\n  .big-number-container {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .big-number {\r\n      color: #00BAFF;\r\n      font-size: 36px;\r\n      font-weight: bold;\r\n      text-align: center;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .unit-text {\r\n      color: #fff;\r\n      font-size: 14px;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .progress-container {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  // 漏斗数据样式\r\n  .funnel-data {\r\n    flex: 1;\r\n    padding: 10px 0;\r\n\r\n    .funnel-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 8px 0;\r\n      border-bottom: 1px solid rgba(0, 186, 255, 0.2);\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .funnel-label {\r\n        color: #fff;\r\n        font-size: 14px;\r\n      }\r\n\r\n      .funnel-value {\r\n        color: #00BAFF;\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 预警信息样式（完全照抄计划管理样式，只改颜色为红色）\r\n  .warning-analysis {\r\n    flex: 1;\r\n    padding: 10px 0;\r\n\r\n    .warning-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 15px;\r\n\r\n      .warning-name {\r\n        color: #fff;\r\n        font-size: 12px;\r\n        width: 80px;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .warning-bar {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-left: 10px;\r\n\r\n        .bar-bg {\r\n          flex: 1;\r\n          height: 8px;\r\n          background: rgba(255, 87, 87, 0.2);\r\n          border-radius: 4px;\r\n          overflow: hidden;\r\n          margin-right: 10px;\r\n\r\n          .bar-fill {\r\n            height: 100%;\r\n            background: linear-gradient(90deg, hsl(0, 85%, 69%), #f31804);\r\n            border-radius: 4px;\r\n            transition: width 0.3s ease;\r\n          }\r\n        }\r\n\r\n        .warning-value {\r\n          color: #FF5757;\r\n          font-size: 12px;\r\n          font-weight: bold;\r\n          width: 60px;\r\n          text-align: right;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 趋势统计样式\r\n  .trend-stats {\r\n    margin-bottom: 5px;\r\n    flex-shrink: 0;\r\n\r\n    .trend-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 3px 0;\r\n\r\n      .trend-label {\r\n        color: #fff;\r\n        font-size: 11px;\r\n      }\r\n\r\n      .trend-value {\r\n        color: #3DE7C9;\r\n        font-size: 12px;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 计划管理样式\r\n  .product-analysis {\r\n    flex: 1;\r\n    padding: 10px 0;\r\n\r\n    .product-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 15px;\r\n\r\n      .product-name {\r\n        color: #fff;\r\n        font-size: 12px;\r\n        width: 80px;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .product-bar {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-left: 10px;\r\n\r\n        .bar-bg {\r\n          flex: 1;\r\n          height: 8px;\r\n          background: rgba(0, 186, 255, 0.2);\r\n          border-radius: 4px;\r\n          overflow: hidden;\r\n          margin-right: 10px;\r\n\r\n          .bar-fill {\r\n            height: 100%;\r\n            background: linear-gradient(90deg, #00BAFF, #3DE7C9);\r\n            border-radius: 4px;\r\n            transition: width 0.3s ease;\r\n          }\r\n        }\r\n\r\n        .product-value {\r\n          color: #00BAFF;\r\n          font-size: 12px;\r\n          font-weight: bold;\r\n          width: 60px;\r\n          text-align: right;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 供应商圆形显示样式\r\n  .supplier-circles {\r\n    position: relative;\r\n    height: 100%;\r\n    padding: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .circle-item {\r\n      position: absolute;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n\r\n      .circle {\r\n        border-radius: 50%;\r\n        border: 2px solid;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-bottom: 8px;\r\n        transition: all 0.3s ease;\r\n\r\n        &:hover {\r\n          transform: scale(1.05);\r\n          filter: brightness(1.2);\r\n        }\r\n\r\n        &.clickable {\r\n          cursor: pointer;\r\n\r\n          &:hover {\r\n            transform: scale(1.1);\r\n            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);\r\n          }\r\n        }\r\n\r\n        .circle-number {\r\n          color: #fff;\r\n          font-weight: bold;\r\n          text-align: center;\r\n          line-height: 1.2;\r\n        }\r\n      }\r\n\r\n      .circle-label {\r\n        color: #fff;\r\n        text-align: center;\r\n        line-height: 1.2;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      // 普通圆形样式（随机位置）\r\n      &.random-position {\r\n        .circle {\r\n          width: 60px;\r\n          height: 60px;\r\n\r\n          .circle-number {\r\n            font-size: 12px;\r\n          }\r\n        }\r\n\r\n        .circle-label {\r\n          font-size: 10px;\r\n          max-width: 60px;\r\n        }\r\n      }\r\n\r\n      // 中心圆形样式（考核情况）\r\n      &.center-circle {\r\n        .circle {\r\n          width: 120px;\r\n          height: 120px;\r\n\r\n          .circle-number {\r\n            font-size: 14px;\r\n          }\r\n        }\r\n\r\n        .circle-label {\r\n          font-size: 12px;\r\n          max-width: 120px;\r\n          margin-top: 5px;\r\n        }\r\n      }\r\n\r\n      // 中心位置\r\n      &.center {\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n      }\r\n    }\r\n  }\r\n\r\n  // 简单显示样式\r\n  .simple-display {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    padding: 20px 0;\r\n\r\n    .display-number {\r\n      color: #FF8C00;\r\n      font-size: 36px;\r\n      font-weight: bold;\r\n      line-height: 1;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .display-label {\r\n      color: #fff;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  // 供方管理统计样式\r\n  .supplier-stats {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    align-items: center;\r\n    padding: 3px 5px;\r\n    gap: 5px;\r\n\r\n    .supplier-stat-item {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 2px;\r\n\r\n      .stat-number {\r\n        color: #FF8C00;\r\n        font-size: 28px;\r\n        font-weight: bold;\r\n        line-height: 1;\r\n        margin-bottom: 3px;\r\n      }\r\n\r\n      .stat-label {\r\n        color: #fff;\r\n        font-size: 15px;\r\n        font-weight: 500;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 供方管理卡片样式\r\n  .supplier-management-card {\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 100%;\r\n\r\n    .card-title {\r\n      flex-shrink: 0;\r\n      margin-bottom: -25px; /* 进一步减小下边距 */\r\n      font-size: 14px;\r\n    }\r\n\r\n    .chart {\r\n      flex-shrink: 0;\r\n      margin-bottom: 0px;\r\n    }\r\n\r\n    .supplier-stats {\r\n      flex: 1;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n  }\r\n\r\n  // 高频物资模块样式\r\n  .high-frequency-content {\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .high-frequency-materials {\r\n    flex: 2 !important;\r\n    margin-bottom: 0px;\r\n  }\r\n\r\n  .price-trend-section {\r\n    flex: 8 !important;\r\n    min-height: 120px; /* 减小最小高度 */\r\n    margin-bottom: 0px;\r\n    margin-top: 5px; /* 减小上边距 */\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 12px;\r\n    color: #00BAFF;\r\n    margin: 0 0 4px 0; /* 减小下边距从8px到4px */\r\n    font-weight: 500;\r\n  }\r\n\r\n  .material-cloud {\r\n    height: 140px;\r\n    width: 100%;\r\n    position: relative;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .chart-placeholder {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    color: #666;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .material-tooltip {\r\n    position: absolute;\r\n    background: rgba(0,0,0,0.8);\r\n    color: #fff;\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n    font-size: 10px;\r\n    white-space: nowrap;\r\n    z-index: 1000;\r\n    pointer-events: none;\r\n  }\r\n\r\n  .mini-chart {\r\n    height: 100px;\r\n    width: 100%;\r\n  }\r\n\r\n  // 滚动条样式\r\n  .material-list::-webkit-scrollbar {\r\n    width: 4px;\r\n  }\r\n\r\n  .material-list::-webkit-scrollbar-track {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .material-list::-webkit-scrollbar-thumb {\r\n    background: rgba(0, 186, 255, 0.5);\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .material-list::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(0, 186, 255, 0.8);\r\n  }\r\n\r\n  /* 响应式样式 - 计划执行状态模块 */\r\n  @media (max-width: 1400px) {\r\n    .plan-execution-card .plan-execution-grid {\r\n      grid-template-columns: repeat(3, 1fr);\r\n    }\r\n  }\r\n\r\n  @media (max-width: 1000px) {\r\n    .plan-execution-card .plan-execution-grid {\r\n      grid-template-columns: repeat(2, 1fr);\r\n    }\r\n  }\r\n\r\n  @media (max-width: 600px) {\r\n    .plan-execution-card .plan-execution-grid {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqPA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,sBAAA,GAAAH,OAAA;AAYA,IAAAI,kBAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAOA;EACAK,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,WAAA,GACA;QAAAC,EAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAF,EAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAF,EAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,YAAA;MACAC,oBAAA;MAEA;MACAC,MAAA;MACA;MACAC,WAAA;MAEA;MACAC,yBAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC,gBAAA;MAEA;MACAC,uBAAA;MACAC,sBAAA;MAAA;;MAEA;MACAC,aAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACA;MACAC,mBAAA,GACA;QAAApB,IAAA;QAAAK,KAAA;QAAAgB,UAAA;MAAA,GACA;QAAArB,IAAA;QAAAK,KAAA;QAAAgB,UAAA;MAAA,GACA;QAAArB,IAAA;QAAAK,KAAA;QAAAgB,UAAA;MAAA,GACA;QAAArB,IAAA;QAAAK,KAAA;QAAAgB,UAAA;MAAA,GACA;QAAArB,IAAA;QAAAK,KAAA;QAAAgB,UAAA;MAAA,GACA;QAAArB,IAAA;QAAAK,KAAA;QAAAgB,UAAA;MAAA,GACA;QAAArB,IAAA;QAAAK,KAAA;QAAAgB,UAAA;MAAA,GACA;QAAArB,IAAA;QAAAK,KAAA;QAAAgB,UAAA;MAAA,EAEA;MACA;MACAC,iBAAA,GACA;QAAAtB,IAAA;QAAAK,KAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,KAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,KAAA;MAAA,GACA;QAAAL,IAAA;QAAAK,KAAA;MAAA;MACA;MACA;MACA;MACA;MAAA,CACA;MACA;MACAkB,WAAA,GACA;QAAAnB,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACA;MACAmB,gBAAA;QACAvB,IAAA;QACAwB,KAAA;QACAC,OAAA;QACAC,UAAA;QACAC,WAAA;QACAC,MAAA;MACA;MACA;MACAC,YAAA,GACA;QAAA3B,EAAA;QAAAE,KAAA;QAAAD,KAAA;QAAA2B,KAAA;QAAAC,QAAA;UAAAC,GAAA;UAAAC,IAAA;QAAA;MAAA,GACA;QAAA/B,EAAA;QAAAE,KAAA;QAAAD,KAAA;QAAA2B,KAAA;QAAAC,QAAA;UAAAC,GAAA;UAAAE,KAAA;QAAA;MAAA,GACA;QAAAhC,EAAA;QAAAE,KAAA;QAAAD,KAAA;QAAA2B,KAAA;QAAAC,QAAA;UAAAI,MAAA;UAAAF,IAAA;QAAA;MAAA,GACA;QAAA/B,EAAA;QAAAE,KAAA;QAAAD,KAAA;QAAA2B,KAAA;QAAAC,QAAA;UAAAI,MAAA;UAAAD,KAAA;QAAA;MAAA,EACA;MACA;MACAE,UAAA;QACAjC,KAAA;QACAkC,MAAA;QACAC,KAAA;QACAR,KAAA;MACA;MACA;MACAS,iBAAA;MACA;MACAC,aAAA;QACAC,SAAA;QAAA;QACAC,WAAA;QAAA;QACAC,UAAA;MACA;MACA;MACAC,WAAA;QACAC,iBAAA;QAAA;QACAC,cAAA;MACA;MACA;MACAC,aAAA;QACAC,UAAA;MACA;MACA;MACAC,cAAA;QACAC,SAAA;QAAA;QACAC,cAAA;QAAA;QACAC,gBAAA;MACA;MACA;MACAC,YAAA,GACA;QAAAtD,IAAA;QAAAuC,KAAA;MAAA,GACA;QAAAvC,IAAA;QAAAuC,KAAA;MAAA,GACA;QAAAvC,IAAA;QAAAuC,KAAA;MAAA,GACA;QAAAvC,IAAA;QAAAuC,KAAA;MAAA,GACA;QAAAvC,IAAA;QAAAuC,KAAA;MAAA,GACA;QAAAvC,IAAA;QAAAuC,KAAA;MAAA;IAEA;EACA;EAEAgB,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAC,gBAAA;IACA;EACA;EAEAC,OAAA,WAAAA,QAAA;IACA,KAAAC,sBAAA;IACA,KAAAC,aAAA;IACA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,YAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,YAAA;IACA;IACAG,MAAA,CAAAC,MAAA,MAAA9D,MAAA,EAAA+D,OAAA,WAAAC,KAAA;MACA,IAAAA,KAAA,IAAAA,KAAA,CAAAC,OAAA;QACAD,KAAA,CAAAC,OAAA;MACA;IACA;IACA;IACA,KAAAC,wBAAA;IACA;IACA,KAAAjB,MAAA,CAAAkB,QAAA;EACA;EACAC,OAAA;IACAb,aAAA,WAAAA,cAAA;MAAA,IAAAc,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,EAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAEAT,KAAA,CAAAW,QAAA;YAAA;cACAX,KAAA,CAAAY,SAAA;gBACAZ,KAAA,CAAAa,UAAA;cACA;cAAAL,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAM,CAAA;cAEAC,OAAA,CAAAC,KAAA,cAAAV,EAAA;YAAA;cAAA,OAAAE,QAAA,CAAAS,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IAEAM,QAAA,WAAAA,SAAA;MAAA,IAAAO,MAAA;MAAA,WAAAjB,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAe,SAAA;QAAA,IAAAC,GAAA;QAAA,WAAAjB,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAc,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,CAAA;YAAA;cAAAY,SAAA,CAAAX,CAAA;cAAAW,SAAA,CAAAZ,CAAA;cAAA,OAGAa,OAAA,CAAAC,GAAA,EACAL,MAAA,CAAAM,gBAAA,IACAN,MAAA,CAAAO,uBAAA,GACA;YAAA;cAEA;cACA;cACA;cACA;cACA;cACA;;cAEAV,OAAA,CAAAW,GAAA;cAAAL,SAAA,CAAAZ,CAAA;cAAA;YAAA;cAAAY,SAAA,CAAAX,CAAA;cAAAU,GAAA,GAAAC,SAAA,CAAAP,CAAA;cAEAC,OAAA,CAAAC,KAAA,YAAAI,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAJ,CAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IAEA;IAEA;IACAM,uBAAA,WAAAA,wBAAA;MAAA,IAAAE,MAAA;MAAA,WAAA1B,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAwB,SAAA;QAAA,IAAAC,QAAA,EAAAzG,IAAA,EAAA0G,aAAA,EAAAC,aAAA,EAAAC,eAAA,EAAAC,GAAA;QAAA,WAAA9B,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAA2B,SAAA;UAAA,kBAAAA,SAAA,CAAAzB,CAAA;YAAA;cAAAyB,SAAA,CAAAxB,CAAA;cAAAwB,SAAA,CAAAzB,CAAA;cAAA,OAEA,IAAA0B,mCAAA;YAAA;cAAAN,QAAA,GAAAK,SAAA,CAAApB,CAAA;cAEA,IAAAe,QAAA,IAAAA,QAAA,CAAAzG,IAAA,IAAAgH,KAAA,CAAAC,OAAA,CAAAR,QAAA,CAAAzG,IAAA;gBACAA,IAAA,GAAAyG,QAAA,CAAAzG,IAAA,EAEA;gBACA0G,aAAA,GAAA1G,IAAA,CAAAkH,IAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,QAAA;gBAAA;gBACAT,aAAA,GAAA3G,IAAA,CAAAkH,IAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,QAAA;gBAAA;gBACAR,eAAA,GAAA5G,IAAA,CAAAkH,IAAA,WAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,QAAA;gBAAA,IAEA;gBACAb,MAAA,CAAAtD,cAAA,CAAAC,SAAA,GAAAwD,aAAA,GAAAA,aAAA,CAAAW,QAAA;gBACAd,MAAA,CAAAtD,cAAA,CAAAE,cAAA,GAAAwD,aAAA,GAAAA,aAAA,CAAAU,QAAA;gBACAd,MAAA,CAAAtD,cAAA,CAAAG,gBAAA,GAAAwD,eAAA,GAAAA,eAAA,CAAAS,QAAA;;gBAEA;gBACAd,MAAA,CAAAe,yBAAA;gBAEA3B,OAAA,CAAAW,GAAA,gBAAAC,MAAA,CAAAtD,cAAA;cACA;gBACA0C,OAAA,CAAAC,KAAA,iBAAAa,QAAA;gBACAF,MAAA,CAAAgB,uBAAA;cACA;cAAAT,SAAA,CAAAzB,CAAA;cAAA;YAAA;cAAAyB,SAAA,CAAAxB,CAAA;cAAAuB,GAAA,GAAAC,SAAA,CAAApB,CAAA;cAEAC,OAAA,CAAAC,KAAA,gBAAAiB,GAAA;cACAN,MAAA,CAAAgB,uBAAA;YAAA;cAAA,OAAAT,SAAA,CAAAjB,CAAA;UAAA;QAAA,GAAAW,QAAA;MAAA;IAEA;IAEA;IACAe,uBAAA,WAAAA,wBAAA;MACA,KAAAtE,cAAA,CAAAC,SAAA;MACA,KAAAD,cAAA,CAAAE,cAAA;MACA,KAAAF,cAAA,CAAAG,gBAAA;MACA;MACA,KAAAkE,yBAAA;IACA;IAEA;IACAlB,gBAAA,WAAAA,iBAAA;MAAA,IAAAoB,MAAA;MAAA,WAAA3C,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAyC,SAAA;QAAA,IAAAhB,QAAA,EAAAzG,IAAA,EAAA0H,GAAA;QAAA,WAAA3C,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAwC,SAAA;UAAA,kBAAAA,SAAA,CAAAtC,CAAA;YAAA;cAAAsC,SAAA,CAAArC,CAAA;cAAAqC,SAAA,CAAAtC,CAAA;cAAA,OAGA,IAAAuC,oCAAA;gBAAAC,aAAA,EAAAL,MAAA,CAAAlH;cAAA;YAAA;cAAAmG,QAAA,GAAAkB,SAAA,CAAAjC,CAAA;cAEA,IAAAe,QAAA,IAAAA,QAAA,CAAAzG,IAAA;gBACAA,IAAA,GAAAyG,QAAA,CAAAzG,IAAA,EAEA;gBACA;gBACAwH,MAAA,CAAA5E,WAAA,CAAAC,iBAAA,GAAA7C,IAAA,CAAA8H,WAAA;gBACA;gBACAN,MAAA,CAAA5E,WAAA,CAAAE,cAAA,GAAA9C,IAAA,CAAA+H,YAAA;;gBAEA;gBACAP,MAAA,CAAAQ,wBAAA,CAAAhI,IAAA;;gBAEA;gBACAwH,MAAA,CAAAS,uBAAA,CAAAjI,IAAA;;gBAEA;gBACAwH,MAAA,CAAAzE,aAAA,CAAAC,UAAA,GAAAhD,IAAA,CAAAgD,UAAA;gBAEA2C,OAAA,CAAAW,GAAA;kBACA1D,WAAA,EAAA4E,MAAA,CAAA5E,WAAA;kBACAsF,QAAA;oBACAC,YAAA,EAAAnI,IAAA,CAAAmI,YAAA;oBACAC,UAAA,EAAApI,IAAA,CAAAoI,UAAA;oBACAC,UAAA,EAAArI,IAAA,CAAAqI;kBACA;kBACAtF,aAAA,EAAAyE,MAAA,CAAAzE;gBACA;cACA;gBACA4C,OAAA,CAAAC,KAAA,YAAAa,QAAA;cACA;cAAAkB,SAAA,CAAAtC,CAAA;cAAA;YAAA;cAAAsC,SAAA,CAAArC,CAAA;cAAAoC,GAAA,GAAAC,SAAA,CAAAjC,CAAA;cAEAC,OAAA,CAAAC,KAAA,YAAA8B,GAAA;cACA;cACAF,MAAA,CAAA5E,WAAA,CAAAC,iBAAA;cACA2E,MAAA,CAAA5E,WAAA,CAAAE,cAAA;cACA0E,MAAA,CAAAzE,aAAA,CAAAC,UAAA;cACAwE,MAAA,CAAAc,uBAAA;YAAA;cAAA,OAAAX,SAAA,CAAA9B,CAAA;UAAA;QAAA,GAAA4B,QAAA;MAAA;IAEA;IAEA;IACAO,wBAAA,WAAAA,yBAAAhI,IAAA;MACA;MACA,IAAAA,IAAA,CAAAmI,YAAA,KAAAI,SAAA;QACA,KAAApH,mBAAA,IAAAf,KAAA,GAAAJ,IAAA,CAAAmI,YAAA,CAAAK,QAAA;QACA,KAAArH,mBAAA,IAAAC,UAAA,GAAAqH,IAAA,CAAAC,GAAA,MAAAD,IAAA,CAAAE,GAAA,IAAA3I,IAAA,CAAAmI,YAAA;MACA;;MAEA;MACA,IAAAnI,IAAA,CAAAoI,UAAA,KAAAG,SAAA;QACA,KAAApH,mBAAA,IAAAf,KAAA,GAAAJ,IAAA,CAAAoI,UAAA;QACA,KAAAjH,mBAAA,IAAAC,UAAA,GAAAqH,IAAA,CAAAC,GAAA,MAAAD,IAAA,CAAAE,GAAA,IAAA3I,IAAA,CAAAoI,UAAA;MACA;;MAEA;MACA,IAAApI,IAAA,CAAAqI,UAAA,KAAAE,SAAA;QACA,KAAApH,mBAAA,IAAAf,KAAA,GAAAJ,IAAA,CAAAqI,UAAA;QACA,KAAAlH,mBAAA,IAAAC,UAAA,GAAAqH,IAAA,CAAAC,GAAA,MAAAD,IAAA,CAAAE,GAAA,IAAA3I,IAAA,CAAAqI,UAAA;MACA;;MAEA;MACA,IAAArI,IAAA,CAAAqH,QAAA,KAAAkB,SAAA;QACA,KAAApH,mBAAA,IAAAf,KAAA,GAAAJ,IAAA,CAAAqH,QAAA,CAAAmB,QAAA;QACA,KAAArH,mBAAA,IAAAC,UAAA,GAAAqH,IAAA,CAAAC,GAAA,MAAAD,IAAA,CAAAE,GAAA,IAAA3I,IAAA,CAAAqH,QAAA;MACA;IACA;IAEA;IACAiB,uBAAA,WAAAA,wBAAA;MACA,KAAAnH,mBAAA,IAAAf,KAAA;MACA,KAAAe,mBAAA,IAAAC,UAAA;MACA,KAAAD,mBAAA,IAAAf,KAAA;MACA,KAAAe,mBAAA,IAAAC,UAAA;MACA,KAAAD,mBAAA,IAAAf,KAAA;MACA,KAAAe,mBAAA,IAAAC,UAAA;MACA,KAAAD,mBAAA,IAAAf,KAAA;MACA,KAAAe,mBAAA,IAAAC,UAAA;IACA;IAEA;IACA6G,uBAAA,WAAAA,wBAAAjI,IAAA;MACA,IAAAA,IAAA;QACA;QACA,IAAAA,IAAA,CAAA4I,kBAAA,KAAAL,SAAA;UACA,KAAAlH,iBAAA,IAAAjB,KAAA,GAAAJ,IAAA,CAAA4I,kBAAA;QACA;QACA;QACA,IAAA5I,IAAA,CAAA6I,iBAAA,KAAAN,SAAA;UACA,KAAAlH,iBAAA,IAAAjB,KAAA,QAAA0I,YAAA,CAAA9I,IAAA,CAAA6I,iBAAA;QACA;QACA;QACA,IAAA7I,IAAA,CAAA+I,eAAA,KAAAR,SAAA;UACA,KAAAlH,iBAAA,IAAAjB,KAAA,GAAAJ,IAAA,CAAA+I,eAAA,CAAAP,QAAA;QACA;QACA;QACA,IAAAxI,IAAA,CAAAgJ,iBAAA,KAAAT,SAAA;UACA,KAAAlH,iBAAA,IAAAjB,KAAA,GAAAJ,IAAA,CAAAgJ,iBAAA,CAAAR,QAAA;QACA;QACA;QACA,IAAAxI,IAAA,CAAAiJ,oBAAA,KAAAV,SAAA;UACA,KAAAlH,iBAAA,IAAAjB,KAAA,GAAAJ,IAAA,CAAAiJ,oBAAA;QACA;QACA;QACA,IAAAjJ,IAAA,CAAAkJ,qBAAA,KAAAX,SAAA;UACA,KAAAlH,iBAAA,IAAAjB,KAAA,GAAAJ,IAAA,CAAAkJ,qBAAA,CAAAV,QAAA;QACA;QACA;QACA,IAAAxI,IAAA,CAAAmJ,cAAA,KAAAZ,SAAA;UACA,KAAAlH,iBAAA,IAAAjB,KAAA,GAAAJ,IAAA,CAAAmJ,cAAA;QACA;QACA;QACA,IAAAnJ,IAAA,CAAAoJ,eAAA,KAAAb,SAAA;UACA,KAAAlH,iBAAA,IAAAjB,KAAA,GAAAJ,IAAA,CAAAoJ,eAAA;QACA;QACAzD,OAAA,CAAAW,GAAA,uBAAAjF,iBAAA;MACA;IACA;IAEA;IACAyH,YAAA,WAAAA,aAAAO,GAAA;MACA,IAAAA,GAAA,KAAAd,SAAA,IAAAc,GAAA;MACA,OAAAA,GAAA,CAAAb,QAAA,GAAAc,OAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAAC,KAAA;MACA,IAAAC,KAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;MACA,OAAAA,KAAA,CAAAD,KAAA;IACA;IAEA;IACAE,gBAAA,WAAAA,iBAAAF,KAAA;MACA,IAAAC,KAAA,IACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA;MACA;MAAA,CACA;MACA,OAAAA,KAAA,CAAAD,KAAA;IACA;IAEA/D,UAAA,WAAAA,WAAA;MACA,KAAAkE,uBAAA;MACA,KAAAC,2BAAA;MACA,KAAAC,4BAAA;MACA,KAAAC,yBAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,cAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,sBAAA;MACA,KAAAC,4BAAA;IACA;IAEA;IACAR,uBAAA,WAAAA,wBAAA;MACA,IAAAS,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,QAAA;MAEA,IAAA7F,KAAA,GAAA/E,OAAA,CAAA+K,IAAA,CAAAH,QAAA;MACA,KAAA7J,MAAA,CAAA0C,cAAA,GAAAsB,KAAA;MAEA,IAAAiG,MAAA;QACAC,eAAA;QACAC,IAAA;UACAzI,IAAA;UACAC,KAAA;UACAF,GAAA;UACAG,MAAA;UACAwI,YAAA;QACA;QACAC,KAAA;UACAC,IAAA;UACA7K,IAAA;UACA8K,QAAA;YACAC,SAAA;cACAjJ,KAAA;YACA;UACA;UACAkJ,SAAA;YACAlJ,KAAA;YACAmJ,QAAA;UACA;QACA;QACAC,KAAA;UACAL,IAAA;UACA9K,IAAA;UACAoL,aAAA;YACArJ,KAAA;YACAmJ,QAAA;YACAG,KAAA;UACA;UACA1C,GAAA;UACAC,GAAA;UACA0C,QAAA;UACAP,QAAA;YACAC,SAAA;cACAjJ,KAAA;YACA;UACA;UACAkJ,SAAA;YACAlJ,KAAA;YACAmJ,QAAA;YACAK,SAAA,WAAAA,UAAAlL,KAAA;cACA,OAAAA,KAAA;YACA;UACA;UACAmL,SAAA;YACAR,SAAA;cACAjJ,KAAA;YACA;UACA;QACA;QACA0J,MAAA;UACAzL,IAAA;UACA8K,IAAA;UACA7K,IAAA,GACAyL,UAAA,MAAAxI,cAAA,CAAAC,SAAA,QACAuI,UAAA,MAAAxI,cAAA,CAAAE,cAAA,QACAsI,UAAA,MAAAxI,cAAA,CAAAG,gBAAA,OACA;UACAsI,QAAA;UACAC,SAAA;YACA7J,KAAA;cACA+I,IAAA;cACAe,CAAA;cACAC,CAAA;cACAC,EAAA;cACAC,EAAA;cACAC,UAAA;gBACAC,MAAA;gBAAAnK,KAAA;cACA;gBACAmK,MAAA;gBAAAnK,KAAA;cACA;cACAoK,MAAA;YACA;YACAC,YAAA;UACA;UACAhM,KAAA;YACAiM,IAAA;YACArK,QAAA;YACAD,KAAA;YACAmJ,QAAA;YACAK,SAAA,WAAAA,UAAAe,MAAA;cACA,OAAAA,MAAA,CAAAjM,KAAA;YACA;UACA;UACAkM,QAAA;YACAX,SAAA;cACA7J,KAAA;gBACA+I,IAAA;gBACAe,CAAA;gBACAC,CAAA;gBACAC,EAAA;gBACAC,EAAA;gBACAC,UAAA;kBACAC,MAAA;kBAAAnK,KAAA;gBACA;kBACAmK,MAAA;kBAAAnK,KAAA;gBACA;gBACAoK,MAAA;cACA;YACA;UACA;QACA;QACAK,OAAA;UACAC,OAAA;UACA/B,eAAA;UACAgC,WAAA;UACAC,SAAA;YACA5K,KAAA;UACA;UACAwJ,SAAA,WAAAA,UAAAe,MAAA;YACA,IAAAM,KAAA,GAAAN,MAAA;YACA,UAAAO,MAAA,CAAAD,KAAA,CAAA5M,IAAA,QAAA6M,MAAA,CAAAD,KAAA,CAAAvM,KAAA;UACA;QACA;MACA;MAEAmE,KAAA,CAAAsI,SAAA,CAAArC,MAAA;IACA;IAEA;IACAZ,2BAAA,WAAAA,4BAAA;MAAA,IAAAkD,MAAA;MACA,KAAAtH,SAAA;QACA,IAAA4E,QAAA,GAAAC,QAAA,CAAAC,cAAA;QACA,KAAAF,QAAA;UACAzE,OAAA,CAAAC,KAAA;UACA;QACA;QAEA,IAAArB,KAAA,GAAA/E,OAAA,CAAA+K,IAAA,CAAAH,QAAA;QACA0C,MAAA,CAAAvM,MAAA,CAAAwM,kBAAA,GAAAxI,KAAA;QAEA,IAAAiG,MAAA;UACAC,eAAA;UACA8B,OAAA;YACAC,OAAA;YACA/B,eAAA;YACAgC,WAAA;YACAC,SAAA;cAAA5K,KAAA;YAAA;UACA;UACA0J,MAAA;YACAX,IAAA;YACAmC,MAAA;YACAC,MAAA;YACAjN,IAAA,GACA;cAAAI,KAAA;cAAAL,IAAA;YAAA,GACA;cAAAK,KAAA;cAAAL,IAAA;YAAA,GACA;cAAAK,KAAA;cAAAL,IAAA;YAAA,EACA;YACA4L,SAAA;cACA7J,KAAA,WAAAA,MAAAuK,MAAA;gBACA,IAAAzK,MAAA;gBACA,OAAAA,MAAA,CAAAyK,MAAA,CAAAa,SAAA,GAAAtL,MAAA,CAAAuL,MAAA;cACA;YACA;YACAhN,KAAA;cACA2B,KAAA;cACAmJ,QAAA;YACA;UACA;QACA;QAEA1G,KAAA,CAAAsI,SAAA,CAAArC,MAAA;MACA;IACA;IAEA;IACAlD,yBAAA,WAAAA,0BAAA;MACA,UAAA/G,MAAA,CAAA0C,cAAA;MAEA,IAAAmK,OAAA,IACA3B,UAAA,MAAAxI,cAAA,CAAAC,SAAA,QACAuI,UAAA,MAAAxI,cAAA,CAAAE,cAAA,QACAsI,UAAA,MAAAxI,cAAA,CAAAG,gBAAA,OACA;MAEA,KAAA7C,MAAA,CAAA0C,cAAA,CAAA4J,SAAA;QACArB,MAAA;UACAxL,IAAA,EAAAoN;QACA;MACA;IACA;IAEA;IACAvD,4BAAA,WAAAA,6BAAA;MACA,IAAAO,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,QAAA;MAEA,IAAA7F,KAAA,GAAA/E,OAAA,CAAA+K,IAAA,CAAAH,QAAA;MACA,KAAA7J,MAAA,CAAA8M,mBAAA,GAAA9I,KAAA;MAEA,IAAAiG,MAAA;QACAC,eAAA;QACA8B,OAAA;UACAC,OAAA;UACA/B,eAAA;UACAgC,WAAA;UACAC,SAAA;YAAA5K,KAAA;UAAA;QACA;QACA0J,MAAA;UACAX,IAAA;UACAmC,MAAA;UACAC,MAAA;UACAjN,IAAA,GACA;YAAAI,KAAA;YAAAL,IAAA;UAAA,GACA;YAAAK,KAAA;YAAAL,IAAA;UAAA,GACA;YAAAK,KAAA;YAAAL,IAAA;UAAA,GACA;YAAAK,KAAA;YAAAL,IAAA;UAAA,EACA;UACA4L,SAAA;YACA7J,KAAA,WAAAA,MAAAuK,MAAA;cACA,IAAAzK,MAAA;cACA,OAAAA,MAAA,CAAAyK,MAAA,CAAAa,SAAA,GAAAtL,MAAA,CAAAuL,MAAA;YACA;UACA;UACAhN,KAAA;YACA2B,KAAA;YACAmJ,QAAA;UACA;QACA;MACA;MAEA1G,KAAA,CAAAsI,SAAA,CAAArC,MAAA;IACA;IAEA;IACAV,yBAAA,WAAAA,0BAAA;MACA,IAAAM,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,QAAA;MAEA,IAAA7F,KAAA,GAAA/E,OAAA,CAAA+K,IAAA,CAAAH,QAAA;MACA,KAAA7J,MAAA,CAAA+M,gBAAA,GAAA/I,KAAA;MAEA,IAAAiG,MAAA;QACAC,eAAA;QACA8B,OAAA;UACAC,OAAA;UACA/B,eAAA;UACAgC,WAAA;UACAC,SAAA;YAAA5K,KAAA;UAAA;QACA;QACA8I,KAAA;UACAC,IAAA;UACA7K,IAAA;UACA8K,QAAA;YAAAC,SAAA;cAAAjJ,KAAA;YAAA;UAAA;UACAkJ,SAAA;YAAAlJ,KAAA;UAAA;QACA;QACAoJ,KAAA;UACAL,IAAA;UACAC,QAAA;YAAAC,SAAA;cAAAjJ,KAAA;YAAA;UAAA;UACAkJ,SAAA;YAAAlJ,KAAA;UAAA;UACAyJ,SAAA;YAAAR,SAAA;cAAAjJ,KAAA;YAAA;UAAA;QACA;QACA0J,MAAA;UACAxL,IAAA;UACA6K,IAAA;UACA0C,MAAA;UACAxC,SAAA;YAAAjJ,KAAA;YAAA0L,KAAA;UAAA;UACA7B,SAAA;YAAA7J,KAAA;UAAA;UACA2L,SAAA;YACA3L,KAAA;cACA+I,IAAA;cACAe,CAAA;cAAAC,CAAA;cAAAC,EAAA;cAAAC,EAAA;cACAC,UAAA,GACA;gBAAAC,MAAA;gBAAAnK,KAAA;cAAA,GACA;gBAAAmK,MAAA;gBAAAnK,KAAA;cAAA;YAEA;UACA;QACA;MACA;MAEAyC,KAAA,CAAAsI,SAAA,CAAArC,MAAA;IACA;IAEA;IACAT,iBAAA,WAAAA,kBAAA;MACA,IAAAK,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,QAAA;MAEA,IAAA7F,KAAA,GAAA/E,OAAA,CAAA+K,IAAA,CAAAH,QAAA;MACA,KAAA7J,MAAA,CAAAmN,QAAA,GAAAnJ,KAAA;MAEA,IAAAiG,MAAA;QACAC,eAAA;QACA8B,OAAA;UACAC,OAAA;UACA/B,eAAA;UACAgC,WAAA;UACAC,SAAA;YAAA5K,KAAA;UAAA;UACA6L,WAAA;YACA9C,IAAA;UACA;UACAS,SAAA,WAAAA,UAAAe,MAAA;YACA,IAAArM,IAAA,GAAAqM,MAAA;YACA,IAAAjM,KAAA,GAAAJ,IAAA,CAAAI,KAAA;YACA,IAAAwN,cAAA;YACA,IAAAxN,KAAA;cACAwN,cAAA,IAAAxN,KAAA,aAAAyN,OAAA;YACA,WAAAzN,KAAA;cACAwN,cAAA,IAAAxN,KAAA,UAAAyN,OAAA;YACA;cACAD,cAAA,GAAAxN,KAAA,CAAAoI,QAAA;YACA;YACA,UAAAoE,MAAA,CAAA5M,IAAA,CAAAD,IAAA,qCAAA6M,MAAA,CAAAgB,cAAA;UACA;QACA;QACAlD,IAAA;UACAzI,IAAA;UACAC,KAAA;UACAC,MAAA;UACAH,GAAA;UACA2I,YAAA;QACA;QACAC,KAAA;UACAC,IAAA;UACA7K,IAAA,OAAAqD,YAAA,CAAAyK,GAAA,WAAA3G,IAAA;YAAA,OAAAA,IAAA,CAAApH,IAAA;UAAA;UACA+K,QAAA;YACAC,SAAA;cACAjJ,KAAA;YACA;UACA;UACAkJ,SAAA;YACAlJ,KAAA;YACAuJ,QAAA;YACA0C,MAAA;YACA9C,QAAA;UACA;QACA;QACAC,KAAA;UACAL,IAAA;UACA9K,IAAA;UACAoL,aAAA;YACArJ,KAAA;YACAsJ,KAAA;UACA;UACAJ,SAAA;YACAlJ,KAAA;YACAwJ,SAAA,WAAAA,UAAAlL,KAAA;cACA,IAAAA,KAAA;gBACA,QAAAA,KAAA,aAAAyN,OAAA;cACA,WAAAzN,KAAA;gBACA,QAAAA,KAAA,UAAAyN,OAAA;cACA;gBACA,OAAAzN,KAAA;cACA;YACA;UACA;UACA0K,QAAA;YACAC,SAAA;cACAjJ,KAAA;YACA;UACA;UACAyJ,SAAA;YACAR,SAAA;cACAjJ,KAAA;YACA;UACA;QACA;QACA0J,MAAA;UACAzL,IAAA;UACA8K,IAAA;UACA7K,IAAA,OAAAqD,YAAA,CAAAyK,GAAA,WAAA3G,IAAA;YAAA,OAAAA,IAAA,CAAA7E,KAAA;UAAA;UACAqJ,SAAA;YACA7J,KAAA;YACAqK,YAAA;UACA;UACAG,QAAA;YACAX,SAAA;cACA7J,KAAA;cACAqK,YAAA;cACA6B,UAAA;cACAC,WAAA;cACAC,WAAA;cACAzB,WAAA;YACA;UACA;QACA;MACA;MAEAlI,KAAA,CAAAsI,SAAA,CAAArC,MAAA;IACA;IAEA;IACAR,cAAA,WAAAA,eAAA;MACA,IAAAI,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,QAAA;MAEA,IAAA7F,KAAA,GAAA/E,OAAA,CAAA+K,IAAA,CAAAH,QAAA;MACA,KAAA7J,MAAA,CAAA4N,KAAA,GAAA5J,KAAA;MAEA,IAAAiG,MAAA;QACAC,eAAA;QACA8B,OAAA;UACAC,OAAA;UACA/B,eAAA;UACAgC,WAAA;UACAC,SAAA;YAAA5K,KAAA;UAAA;QACA;QACA8I,KAAA;UACAC,IAAA;UACA7K,IAAA;UACA8K,QAAA;YAAAC,SAAA;cAAAjJ,KAAA;YAAA;UAAA;UACAkJ,SAAA;YAAAlJ,KAAA;YAAAmJ,QAAA;UAAA;QACA;QACAC,KAAA;UACAL,IAAA;UACAC,QAAA;YAAAC,SAAA;cAAAjJ,KAAA;YAAA;UAAA;UACAkJ,SAAA;YAAAlJ,KAAA;YAAAmJ,QAAA;UAAA;UACAM,SAAA;YAAAR,SAAA;cAAAjJ,KAAA;YAAA;UAAA;QACA;QACA0J,MAAA;UACAxL,IAAA;UACA6K,IAAA;UACA0C,MAAA;UACAxC,SAAA;YAAAjJ,KAAA;YAAA0L,KAAA;UAAA;UACA7B,SAAA;YAAA7J,KAAA;UAAA;UACA2L,SAAA;YACA3L,KAAA;cACA+I,IAAA;cACAe,CAAA;cAAAC,CAAA;cAAAC,EAAA;cAAAC,EAAA;cACAC,UAAA,GACA;gBAAAC,MAAA;gBAAAnK,KAAA;cAAA,GACA;gBAAAmK,MAAA;gBAAAnK,KAAA;cAAA;YAEA;UACA;QACA;MACA;MAEAyC,KAAA,CAAAsI,SAAA,CAAArC,MAAA;IACA;IAEA;IACAP,iBAAA,WAAAA,kBAAA;MAAA,IAAAmE,MAAA;MACA,KAAA5I,SAAA;QACA,IAAA4E,QAAA,GAAAC,QAAA,CAAAC,cAAA;QACA3E,OAAA,CAAAW,GAAA,iBAAA8D,QAAA;QAEA,KAAAA,QAAA;UACAzE,OAAA,CAAAC,KAAA;UACA;QACA;QAEA,IAAArB,KAAA,GAAA/E,OAAA,CAAA+K,IAAA,CAAAH,QAAA;QACAgE,MAAA,CAAA7N,MAAA,CAAA8N,QAAA,GAAA9J,KAAA;QAEA,IAAAiG,MAAA;UACAC,eAAA;UACA8B,OAAA;YACAC,OAAA;YACA/B,eAAA;YACAgC,WAAA;YACAC,SAAA;cAAA5K,KAAA;YAAA;UACA;UACA0J,MAAA;YACAX,IAAA;YACAmC,MAAA;YACAC,MAAA;YACAjN,IAAA,GACA;cAAAI,KAAA;cAAAL,IAAA;YAAA,GACA;cAAAK,KAAA;cAAAL,IAAA;YAAA,GACA;cAAAK,KAAA;cAAAL,IAAA;YAAA,EACA;YACA4L,SAAA;cACA7J,KAAA,WAAAA,MAAAuK,MAAA;gBACA,IAAAzK,MAAA;gBACA,OAAAA,MAAA,CAAAyK,MAAA,CAAAa,SAAA,GAAAtL,MAAA,CAAAuL,MAAA;cACA;YACA;YACAhN,KAAA;cACA2B,KAAA;cACAmJ,QAAA;YACA;UACA;QACA;QAEA1G,KAAA,CAAAsI,SAAA,CAAArC,MAAA;MACA;IACA;IAEA;IACAN,sBAAA,WAAAA,uBAAA;MAAA,IAAAoE,MAAA;MAAA,WAAAzJ,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAuJ,SAAA;QAAA,IAAAC,GAAA;QAAA,WAAAzJ,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAsJ,SAAA;UAAA,kBAAAA,SAAA,CAAApJ,CAAA;YAAA;cAAAoJ,SAAA,CAAAnJ,CAAA;cAAAmJ,SAAA,CAAApJ,CAAA;cAAA,OAGAiJ,MAAA,CAAAI,8BAAA;YAAA;cAAAD,SAAA,CAAApJ,CAAA;cAAA,OAEAiJ,MAAA,CAAAK,sBAAA;YAAA;cACA;cACAL,MAAA,CAAA9I,SAAA;gBACA8I,MAAA,CAAAM,8BAAA;gBACAN,MAAA,CAAAO,gCAAA;cACA;cAAAJ,SAAA,CAAApJ,CAAA;cAAA;YAAA;cAAAoJ,SAAA,CAAAnJ,CAAA;cAAAkJ,GAAA,GAAAC,SAAA,CAAA/I,CAAA;cAEAC,OAAA,CAAAC,KAAA,gBAAA4I,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA5I,CAAA;UAAA;QAAA,GAAA0I,QAAA;MAAA;IAEA;IAEA;IACAG,8BAAA,WAAAA,+BAAA;MAAA,IAAAI,MAAA;MAAA,WAAAjK,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA+J,SAAA;QAAA,IAAA1C,MAAA,EAAA5F,QAAA,EAAAuI,GAAA;QAAA,WAAAjK,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAA8J,SAAA;UAAA,kBAAAA,SAAA,CAAA5J,CAAA;YAAA;cAAA4J,SAAA,CAAA3J,CAAA;cAEA+G,MAAA;gBACAxE,aAAA,EAAAiH,MAAA,CAAAxO,oBAAA;gBACA4O,QAAA,EAAAJ,MAAA,CAAAnO,gBAAA;gBACAwO,QAAA,EAAAL,MAAA,CAAAlO;cACA;cAAAqO,SAAA,CAAA5J,CAAA;cAAA,OAEA,IAAA+J,gDAAA,EAAA/C,MAAA;YAAA;cAAA5F,QAAA,GAAAwI,SAAA,CAAAvJ,CAAA;cACA,IAAAe,QAAA,IAAAA,QAAA,CAAAzG,IAAA;gBACA;gBACA8O,MAAA,CAAArO,yBAAA,IAAAgG,QAAA,CAAAzG,IAAA,QAAAqP,KAAA;cACA;gBACA;gBACAP,MAAA,CAAArO,yBAAA,GAAAqO,MAAA,CAAAQ,wBAAA;cACA;cAAAL,SAAA,CAAA5J,CAAA;cAAA;YAAA;cAAA4J,SAAA,CAAA3J,CAAA;cAAA0J,GAAA,GAAAC,SAAA,CAAAvJ,CAAA;cAEAC,OAAA,CAAAC,KAAA,gBAAAoJ,GAAA;cACAF,MAAA,CAAArO,yBAAA,GAAAqO,MAAA,CAAAQ,wBAAA;YAAA;cAAA,OAAAL,SAAA,CAAApJ,CAAA;UAAA;QAAA,GAAAkJ,QAAA;MAAA;IAEA;IAEA;IACAH,8BAAA,WAAAA,+BAAA;MAAA,IAAAW,MAAA;MACA,IAAAnF,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,QAAA;QACAzE,OAAA,CAAAC,KAAA;QACA;MACA;MAEAwE,QAAA,CAAAoF,SAAA;MAEA,IAAAC,eAAA,QAAAhP,yBAAA;MACA,KAAAgP,eAAA,IAAAA,eAAA,CAAAtC,MAAA;QACA/C,QAAA,CAAAoF,SAAA;QACA;MACA;;MAEA;MACA,IAAAE,sBAAA,GAAAD,eAAA,CACAE,IAAA,WAAA9J,CAAA,EAAA+J,CAAA;QAAA,QAAAA,CAAA,CAAAC,KAAA,UAAAhK,CAAA,CAAAgK,KAAA;MAAA,GACAR,KAAA;MAEA,IAAAS,SAAA,GAAAzF,QAAA,CAAA0F,aAAA;MACAD,SAAA,CAAAE,KAAA,CAAAxC,KAAA;MACAsC,SAAA,CAAAE,KAAA,CAAAC,MAAA;MACAH,SAAA,CAAAE,KAAA,CAAAjO,QAAA;MACA+N,SAAA,CAAAE,KAAA,CAAAE,QAAA;MAEA,IAAAtO,MAAA,IACA,uDACA,sDACA;MAEA,IAAAuO,WAAA;MACA,IAAAC,WAAA;MAEAV,sBAAA,CAAApL,OAAA,WAAA6C,IAAA,EAAAqC,KAAA;QACA,IAAAyB,QAAA,GAAAkF,WAAA,GAAA3G,KAAA;QACA,IAAAyB,QAAA,GAAAmF,WAAA;UACAnF,QAAA,GAAAmF,WAAA;QACA;QAEA,IAAAC,GAAA,GAAAhG,QAAA,CAAA0F,aAAA;QACAM,GAAA,CAAAC,WAAA,GAAAnJ,IAAA,CAAAoJ,QAAA;QACAF,GAAA,CAAAL,KAAA,CAAAjO,QAAA;QACAsO,GAAA,CAAAL,KAAA,CAAA/E,QAAA,MAAA2B,MAAA,CAAA3B,QAAA;QACAoF,GAAA,CAAAL,KAAA,CAAAQ,UAAA;QACAH,GAAA,CAAAL,KAAA,CAAAlO,KAAA,GAAAF,MAAA,CAAA4H,KAAA,GAAA5H,MAAA,CAAAuL,MAAA;QACAkD,GAAA,CAAAL,KAAA,CAAAS,SAAA,aAAA7D,MAAA,CAAAnE,IAAA,CAAAiI,MAAA;QACAL,GAAA,CAAAL,KAAA,CAAA/N,IAAA,MAAA2K,MAAA,MAAAnE,IAAA,CAAAiI,MAAA;QACAL,GAAA,CAAAL,KAAA,CAAAhO,GAAA,MAAA4K,MAAA,MAAAnE,IAAA,CAAAiI,MAAA;QACAL,GAAA,CAAAL,KAAA,CAAAW,UAAA;QACAN,GAAA,CAAAL,KAAA,CAAAY,UAAA;QACAP,GAAA,CAAAL,KAAA,CAAAa,UAAA;QACAR,GAAA,CAAAL,KAAA,CAAAc,MAAA;QACAT,GAAA,CAAAL,KAAA,CAAAe,MAAA,GAAArB,sBAAA,CAAAvC,MAAA,GAAA3D,KAAA;;QAEA;QACA,IAAAwH,IAAA,GAAAzB,MAAA;QACAc,GAAA,CAAArM,gBAAA;UACA,KAAAgM,KAAA,CAAAS,SAAA,aAAA7D,MAAA,CAAAnE,IAAA,CAAAiI,MAAA;UACA,KAAAV,KAAA,CAAAe,MAAA;UACA,KAAAf,KAAA,CAAAY,UAAA;;UAEA;UACA,IAAArE,OAAA,GAAAlC,QAAA,CAAA0F,aAAA;UACAxD,OAAA,CAAA0E,SAAA;UACA1E,OAAA,CAAA+D,WAAA,oBAAA1D,MAAA,CAAAoE,IAAA,CAAAE,YAAA,CAAA/J,IAAA,CAAA0I,KAAA;UACAtD,OAAA,CAAAyD,KAAA,CAAAjO,QAAA;UACAwK,OAAA,CAAAyD,KAAA,CAAA7N,MAAA;UACAoK,OAAA,CAAAyD,KAAA,CAAA/N,IAAA;UACAsK,OAAA,CAAAyD,KAAA,CAAAS,SAAA;UACAlE,OAAA,CAAAyD,KAAA,CAAAmB,UAAA;UACA5E,OAAA,CAAAyD,KAAA,CAAAlO,KAAA;UACAyK,OAAA,CAAAyD,KAAA,CAAAoB,OAAA;UACA7E,OAAA,CAAAyD,KAAA,CAAA7D,YAAA;UACAI,OAAA,CAAAyD,KAAA,CAAA/E,QAAA;UACAsB,OAAA,CAAAyD,KAAA,CAAAW,UAAA;UACApE,OAAA,CAAAyD,KAAA,CAAAe,MAAA;UACA,KAAAM,WAAA,CAAA9E,OAAA;QACA;QAEA8D,GAAA,CAAArM,gBAAA;UACA,KAAAgM,KAAA,CAAAS,SAAA,aAAA7D,MAAA,CAAAnE,IAAA,CAAAiI,MAAA;UACA,KAAAV,KAAA,CAAAe,MAAA,IAAArB,sBAAA,CAAAvC,MAAA,GAAA3D,KAAA,EAAAhB,QAAA;UACA,KAAAwH,KAAA,CAAAY,UAAA;UAEA,IAAArE,OAAA,QAAA+E,aAAA;UACA,IAAA/E,OAAA;YACA,KAAAgF,WAAA,CAAAhF,OAAA;UACA;QACA;QAEAuD,SAAA,CAAAuB,WAAA,CAAAhB,GAAA;MACA;MAEAjG,QAAA,CAAAiH,WAAA,CAAAvB,SAAA;IACA;IAEA;IACAR,wBAAA,WAAAA,yBAAA;MACA,QACA;QAAAiB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,GACA;QAAAjB,QAAA;QAAAV,KAAA;QAAA2B,KAAA;MAAA,EACA;IACA;IAEA;IACA7C,sBAAA,WAAAA,uBAAA;MAAA,IAAA8C,MAAA;MAAA,WAAA5M,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA0M,SAAA;QAAA,IAAArF,MAAA,EAAA5F,QAAA,EAAAkL,GAAA;QAAA,WAAA5M,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAyM,SAAA;UAAA,kBAAAA,SAAA,CAAAvM,CAAA;YAAA;cAAAuM,SAAA,CAAAtM,CAAA;cAEA+G,MAAA;gBACAxE,aAAA,EAAA4J,MAAA,CAAAnR,oBAAA;gBACAiQ,QAAA;cACA;cAAAqB,SAAA,CAAAvM,CAAA;cAAA,OAEA,IAAAwM,2CAAA,EAAAxF,MAAA;YAAA;cAAA5F,QAAA,GAAAmL,SAAA,CAAAlM,CAAA;cACA,IAAAe,QAAA,IAAAA,QAAA,CAAAzG,IAAA,IAAAgH,KAAA,CAAAC,OAAA,CAAAR,QAAA,CAAAzG,IAAA,KAAAyG,QAAA,CAAAzG,IAAA,CAAAmN,MAAA;gBACAsE,MAAA,CAAA/Q,iBAAA,GAAA+F,QAAA,CAAAzG,IAAA;gBACA2F,OAAA,CAAAW,GAAA,oBAAAmL,MAAA,CAAA/Q,iBAAA;cACA;gBACAiF,OAAA,CAAAW,GAAA;gBACAmL,MAAA,CAAA/Q,iBAAA,GAAA+Q,MAAA,CAAAK,wBAAA;cACA;cAAAF,SAAA,CAAAvM,CAAA;cAAA;YAAA;cAAAuM,SAAA,CAAAtM,CAAA;cAAAqM,GAAA,GAAAC,SAAA,CAAAlM,CAAA;cAEAC,OAAA,CAAAC,KAAA,kBAAA+L,GAAA;cACAF,MAAA,CAAA/Q,iBAAA,GAAA+Q,MAAA,CAAAK,wBAAA;YAAA;cAAA,OAAAF,SAAA,CAAA/L,CAAA;UAAA;QAAA,GAAA6L,QAAA;MAAA;IAEA;IAEA;IACAI,wBAAA,WAAAA,yBAAA;MACA;QACAC,sBAAA,GACA;UACAC,SAAA;UACAC,SAAA,GACA;YAAAC,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA;UAAA;UACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,UAAA;YAAAC,KAAA;UAAA;QAEA,EACA;QACAC,+BAAA,GACA;UACAC,UAAA;UACAC,UAAA,GACA;YAAAJ,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;UACA;YAAA6P,UAAA;YAAA7P,MAAA;UAAA;UAAA;QAEA;MAEA;IACA;IAEA;IACAwM,gCAAA,WAAAA,iCAAA;MAAA,IAAA0D,MAAA;MACA,KAAA/M,SAAA;QACA,IAAA4E,QAAA,GAAAC,QAAA,CAAAC,cAAA;QACA,KAAAF,QAAA;UACAzE,OAAA,CAAAC,KAAA;UACA;QACA;;QAEA;QACA,IAAA2M,MAAA,CAAAhS,MAAA,CAAAiS,uBAAA;UACAD,MAAA,CAAAhS,MAAA,CAAAiS,uBAAA,CAAAhO,OAAA;QACA;QAEA,IAAAD,KAAA,GAAA/E,OAAA,CAAA+K,IAAA,CAAAH,QAAA;QACAmI,MAAA,CAAAhS,MAAA,CAAAiS,uBAAA,GAAAjO,KAAA;;QAEA;QACA,IAAA7D,iBAAA,GAAA6R,MAAA,CAAA7R,iBAAA;QACA,KAAAA,iBAAA;UACA0J,QAAA,CAAAoF,SAAA;UACA;QACA;;QAEA;QACA,IAAAiD,QAAA,OAAAC,GAAA;;QAEA;QACA,IAAAhS,iBAAA,CAAAqR,sBAAA;UACArR,iBAAA,CAAAqR,sBAAA,CAAAzN,OAAA,WAAAqO,UAAA;YACA,IAAAA,UAAA,CAAAV,SAAA;cACAU,UAAA,CAAAV,SAAA,CAAA3N,OAAA,WAAA6C,IAAA;gBACAsL,QAAA,CAAAG,GAAA,CAAAzL,IAAA,CAAA+K,UAAA;cACA;YACA;UACA;QACA;;QAEA;QACA,IAAAxR,iBAAA,CAAA0R,+BAAA;UACA1R,iBAAA,CAAA0R,+BAAA,CAAA9N,OAAA,WAAAuO,WAAA;YACA,IAAAA,WAAA,CAAAP,UAAA;cACAO,WAAA,CAAAP,UAAA,CAAAhO,OAAA,WAAA6C,IAAA;gBACAsL,QAAA,CAAAG,GAAA,CAAAzL,IAAA,CAAA+K,UAAA;cACA;YACA;UACA;QACA;;QAEA;QACAO,QAAA,GAAAzL,KAAA,CAAA8L,IAAA,CAAAL,QAAA,EAAA9C,IAAA;QAEA,IAAA8C,QAAA,CAAAtF,MAAA;UACA/C,QAAA,CAAAoF,SAAA;UACA;QACA;;QAEA;QACA,IAAAhE,MAAA;QACA,IAAAuH,UAAA;;QAEA;QACA,IAAArS,iBAAA,CAAAqR,sBAAA;UACArR,iBAAA,CAAAqR,sBAAA,CAAAzN,OAAA,WAAAqO,UAAA;YACA,IAAAK,SAAA,GAAAP,QAAA,CAAA3E,GAAA,WAAAmF,IAAA;cACA,IAAAC,KAAA,GAAAP,UAAA,CAAAV,SAAA,CAAA/K,IAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAA+K,UAAA,KAAAe,IAAA;cAAA;cACA,OAAAC,KAAA,GAAAzH,UAAA,CAAAyH,KAAA,CAAAf,KAAA;YACA;YAEA3G,MAAA,CAAA2H,IAAA;cACApT,IAAA,EAAA4S,UAAA,CAAAX,SAAA;cACAnH,IAAA;cACAuI,UAAA;cACApT,IAAA,EAAAgT,SAAA;cACAzF,MAAA;cACAxC,SAAA;gBACAyC,KAAA;gBACA1L,KAAA;cACA;cACA6J,SAAA;gBACA7J,KAAA;cACA;cACAuR,MAAA;cACAC,UAAA;cACAC,YAAA;YACA;YAEAR,UAAA,CAAAI,IAAA,CAAAR,UAAA,CAAAX,SAAA;UACA;QACA;;QAEA;QACA,IAAAtR,iBAAA,CAAA0R,+BAAA;UACA1R,iBAAA,CAAA0R,+BAAA,CAAA9N,OAAA,WAAAuO,WAAA;YACA,IAAAW,UAAA,GAAAf,QAAA,CAAA3E,GAAA,WAAAmF,IAAA;cACA,IAAAC,KAAA,GAAAL,WAAA,CAAAP,UAAA,CAAApL,IAAA,WAAAC,IAAA;gBAAA,OAAAA,IAAA,CAAA+K,UAAA,KAAAe,IAAA;cAAA;cACA,OAAAC,KAAA,GAAAzH,UAAA,CAAAyH,KAAA,CAAA7Q,MAAA;YACA;YAEAmJ,MAAA,CAAA2H,IAAA;cACApT,IAAA,EAAA8S,WAAA,CAAAR,UAAA;cACAxH,IAAA;cACAuI,UAAA;cACApT,IAAA,EAAAwT,UAAA;cACAjG,MAAA;cACAxC,SAAA;gBACAyC,KAAA;gBACA1L,KAAA;cACA;cACA6J,SAAA;gBACA7J,KAAA;cACA;cACAuR,MAAA;cACAC,UAAA;cACAC,YAAA;YACA;YAEAR,UAAA,CAAAI,IAAA,CAAAN,WAAA,CAAAR,UAAA;UACA;QACA;;QAEA;QACA,IAAAoB,QAAA,EAAAC,QAAA;;QAEA;QACA,IAAAC,WAAA,GAAAnI,MAAA,CAAAoI,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAT,UAAA;QAAA,GACAU,OAAA,WAAAD,CAAA;UAAA,OAAAA,CAAA,CAAA7T,IAAA,CAAA4T,MAAA,WAAAlO,CAAA;YAAA,OAAAA,CAAA,aAAAA,CAAA,KAAA6C,SAAA;UAAA;QAAA;QACA,IAAAoL,WAAA,CAAAxG,MAAA;UACAsG,QAAA,GAAAhL,IAAA,CAAAC,GAAA,CAAAqL,KAAA,CAAAtL,IAAA,MAAAuL,mBAAA,CAAAlP,OAAA,EAAA6O,WAAA;UACAD,QAAA,GAAAjL,IAAA,CAAAE,GAAA,CAAAoL,KAAA,CAAAtL,IAAA,MAAAuL,mBAAA,CAAAlP,OAAA,EAAA6O,WAAA;QACA;;QAEA;;QAEA,IAAAnJ,MAAA;UACAC,eAAA;UACA8B,OAAA;YACAC,OAAA;YACAmB,WAAA;cACA9C,IAAA;cACAoJ,UAAA;gBACAnS,KAAA;cACA;YACA;YACA2I,eAAA;YACAgC,WAAA;YACAyB,WAAA;YACAxB,SAAA;cACA5K,KAAA;YACA;YACAwJ,SAAA,WAAAA,UAAAe,MAAA;cACA,IAAA6H,GAAA,GAAA7H,MAAA,IAAA8H,cAAA;cACA9H,MAAA,CAAA/H,OAAA,WAAA6C,IAAA;gBACA,IAAAA,IAAA,CAAA/G,KAAA,aAAA+G,IAAA,CAAA/G,KAAA,KAAAmI,SAAA;kBACA,IAAApB,IAAA,CAAAiN,UAAA,CAAAC,QAAA,UAAAlN,IAAA,CAAAiN,UAAA,CAAAC,QAAA;oBACAH,GAAA,OAAAtH,MAAA,CAAAzF,IAAA,CAAAmN,MAAA,EAAA1H,MAAA,CAAAzF,IAAA,CAAAiN,UAAA,QAAAxH,MAAA,CAAAzF,IAAA,CAAA/G,KAAA;kBACA;oBACA;oBACA,IAAAmU,UAAA,IAAA9I,UAAA,CAAAtE,IAAA,CAAA/G,KAAA,WAAAyN,OAAA;oBACAqG,GAAA,OAAAtH,MAAA,CAAAzF,IAAA,CAAAmN,MAAA,EAAA1H,MAAA,CAAAzF,IAAA,CAAAiN,UAAA,QAAAxH,MAAA,CAAA2H,UAAA;kBACA;gBACA;kBACAL,GAAA,OAAAtH,MAAA,CAAAzF,IAAA,CAAAmN,MAAA,EAAA1H,MAAA,CAAAzF,IAAA,CAAAiN,UAAA;gBACA;cACA;cACA,OAAAF,GAAA;YACA;UACA;UACAM,MAAA;YACAxU,IAAA,EAAA+S,UAAA;YACArG,SAAA;cACA5K,KAAA;cACAmJ,QAAA;YACA;YACAjJ,GAAA;YACAE,KAAA;YACAuS,OAAA;YACAC,MAAA;UACA;UACAhK,IAAA;YACAzI,IAAA;YACAC,KAAA;YACAC,MAAA;YACAH,GAAA;YACA2I,YAAA;UACA;UACAC,KAAA;YACAC,IAAA;YACA7K,IAAA,EAAAyS,QAAA,CAAA3E,GAAA,WAAAmF,IAAA;cACA,IAAA0B,IAAA,GAAA1B,IAAA,CAAA2B,SAAA;cACA,IAAAC,KAAA,GAAAC,QAAA,CAAA7B,IAAA,CAAA2B,SAAA;cACA,UAAAhI,MAAA,CAAA+H,IAAA,OAAA/H,MAAA,CAAAiI,KAAA;YACA;YACA7J,SAAA;cACAlJ,KAAA;cACAmJ,QAAA;cACAI,QAAA;YACA;YACAP,QAAA;cACAC,SAAA;gBACAjJ,KAAA;cACA;YACA;YACAiT,QAAA;cACA3I,IAAA;YACA;UACA;UACAlB,KAAA,GACA;YACAL,IAAA;YACA9K,IAAA;YACAiV,YAAA;YACAC,OAAA;YACAC,UAAA;YACA/J,aAAA;cACArJ,KAAA;cACAmJ,QAAA;cACAuF,UAAA;YACA;YACAzO,QAAA;YACA2G,GAAA,EAAA+K,QAAA;YACA9K,GAAA,EAAA+K,QAAA;YACA5I,QAAA;cACAsB,IAAA;cACArB,SAAA;gBACAjJ,KAAA;cACA;YACA;YACAkJ,SAAA;cACAlJ,KAAA;cACAmJ,QAAA;YACA;YACAM,SAAA;cACAa,IAAA;cACArB,SAAA;gBACAjJ,KAAA;gBACA+I,IAAA;cACA;YACA;YACAkK,QAAA;cACA3I,IAAA;YACA;UACA,GACA;YACAvB,IAAA;YACA9K,IAAA;YACAiV,YAAA;YACAC,OAAA;YACAC,UAAA;YACA/J,aAAA;cACArJ,KAAA;cACAmJ,QAAA;cACAuF,UAAA;YACA;YACAzO,QAAA;YACA2G,GAAA;YAAA;YACAC,GAAA;YAAA;YACAmC,QAAA;cACAsB,IAAA;cACArB,SAAA;gBACAjJ,KAAA;cACA;YACA;YACAkJ,SAAA;cACAlJ,KAAA;cACAmJ,QAAA;cACAK,SAAA,WAAAA,UAAAlL,KAAA;gBACA,QAAAA,KAAA,UAAAyN,OAAA;cACA;YACA;YACAtC,SAAA;cACAa,IAAA;YACA;YACA2I,QAAA;cACA3I,IAAA;YACA;UACA,EACA;UACAZ,MAAA,EAAAA;QACA;QAEAjH,KAAA,CAAAsI,SAAA,CAAArC,MAAA;MACA;IACA;IAEA;IACA0G,YAAA,WAAAA,aAAA7O,MAAA;MACA,IAAAA,MAAA;QACA,QAAAA,MAAA,UAAAwL,OAAA;MACA;MACA,OAAAxL,MAAA,CAAAwL,OAAA;IACA;IAEA;IACA5J,YAAA,WAAAA,aAAA;MAAA,IAAAkR,OAAA;MACA;MACAC,YAAA,MAAA5U,WAAA;MACA,KAAAA,WAAA,GAAA6U,UAAA;QACAjR,MAAA,CAAAC,MAAA,CAAA8Q,OAAA,CAAA5U,MAAA,EAAA+D,OAAA,WAAAC,KAAA;UACA,IAAAA,KAAA,IAAAA,KAAA,CAAA+Q,MAAA;YACA/Q,KAAA,CAAA+Q,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAzR,sBAAA,WAAAA,uBAAA;MACA,IAAA0R,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAE,EAAA,gBAAAC,sBAAA;MACA;IACA;IAEA;IACAjR,wBAAA,WAAAA,yBAAA;MACA,IAAA8Q,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAI,GAAA,gBAAAD,sBAAA;MACA;IACA;IAEA;IACAA,sBAAA,WAAAA,uBAAA;MAAA,IAAAE,OAAA;MACA,IAAAL,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACA,IAAAjS,YAAA,GAAAgS,mBAAA,CAAAhS,YAAA;QACA,KAAAC,MAAA,CAAAkB,QAAA,0BAAAnB,YAAA;QAEAoC,OAAA,CAAAW,GAAA,YAAA/C,YAAA;QACAoC,OAAA,CAAAW,GAAA,kBAAA9C,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAC,gBAAA;;QAEA;QACA,KAAA6B,SAAA;UACA6P,UAAA;YACAO,OAAA,CAAA3R,YAAA;UACA;QACA;MACA;IACA;IAEA;IACA4R,gBAAA,WAAAA,iBAAA;MACA,IAAAN,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAO,MAAA;MACA;QACA,KAAAC,QAAA;UACAC,OAAA;UACAnL,IAAA;QACA;MACA;IACA;IAEA;IACAoL,sBAAA,WAAAA,uBAAAC,QAAA,EAAArO,aAAA;MACA,KAAAxH,YAAA,GAAA6V,QAAA;MACA,KAAA5V,oBAAA,GAAAuH,aAAA;MACAlC,OAAA,CAAAW,GAAA,aAAA4P,QAAA,SAAArO,aAAA;;MAEA;MACA,KAAAzB,gBAAA;MACA,KAAAC,uBAAA;IACA;IAEA;IACA8P,mBAAA,WAAAA,oBAAA;MACA;MACA,IAAAC,QAAA,QAAAC,OAAA,CAAAC,OAAA;MACAvS,MAAA,CAAAwS,IAAA,CAAAH,QAAA,CAAAI,IAAA;IACA;IAEA;IACAC,kBAAA,WAAAA,mBAAA;MACA9Q,OAAA,CAAAW,GAAA;MACA;MACA,IAAA8P,QAAA,QAAAC,OAAA,CAAAC,OAAA;MACAvS,MAAA,CAAAwS,IAAA,CAAAH,QAAA,CAAAI,IAAA;IACA;IAEA;IACAE,0BAAA,WAAAA,2BAAA;MACA/Q,OAAA,CAAAW,GAAA;MACA;MACA,IAAA8P,QAAA,QAAAC,OAAA,CAAAC,OAAA;MACAvS,MAAA,CAAAwS,IAAA,CAAAH,QAAA,CAAAI,IAAA;IACA;IAEA;IACAG,iBAAA,WAAAA,kBAAA;MACAhR,OAAA,CAAAW,GAAA;MACA;MACA,IAAA8P,QAAA,QAAAC,OAAA,CAAAC,OAAA;MACAvS,MAAA,CAAAwS,IAAA,CAAAH,QAAA,CAAAI,IAAA;IACA;IAEA;IACArM,4BAAA,WAAAA,6BAAA;MAAA,IAAAyM,OAAA;MAAA,WAAA/R,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA6R,SAAA;QAAA,IAAApQ,QAAA,EAAAqQ,GAAA;QAAA,WAAA/R,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAA4R,SAAA;UAAA,kBAAAA,SAAA,CAAA1R,CAAA;YAAA;cAAA0R,SAAA,CAAAzR,CAAA;cAAAyR,SAAA,CAAA1R,CAAA;cAAA,OAEA,IAAA2R,uCAAA;YAAA;cAAAvQ,QAAA,GAAAsQ,SAAA,CAAArR,CAAA;cACAC,OAAA,CAAAW,GAAA,yCAAAG,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAzG,IAAA;gBACA4W,OAAA,CAAA9V,uBAAA,GAAA2F,QAAA,CAAAzG,IAAA;gBACA2F,OAAA,CAAAW,GAAA,0CAAAsQ,OAAA,CAAA9V,uBAAA;cACA;gBACA6E,OAAA,CAAAC,KAAA,gBAAAa,QAAA;gBACAmQ,OAAA,CAAA9V,uBAAA;cACA;cAAAiW,SAAA,CAAA1R,CAAA;cAAA;YAAA;cAAA0R,SAAA,CAAAzR,CAAA;cAAAwR,GAAA,GAAAC,SAAA,CAAArR,CAAA;cAEAC,OAAA,CAAAC,KAAA,iBAAAkR,GAAA;cACAF,OAAA,CAAA9V,uBAAA;YAAA;cAGA;cACA8V,OAAA,CAAApR,SAAA;gBACAoR,OAAA,CAAAK,uBAAA;cACA;YAAA;cAAA,OAAAF,SAAA,CAAAlR,CAAA;UAAA;QAAA,GAAAgR,QAAA;MAAA;IACA;IAEA;IACAK,wBAAA,WAAAA,yBAAA;MACA,IAAAC,KAAA;MACA,SAAArW,uBAAA,SAAAA,uBAAA,CAAAqM,MAAA;QACA;QACA,IAAAiK,UAAA;QACA,KAAAtW,uBAAA,CAAAwD,OAAA,WAAA6C,IAAA;UACA,IAAAA,IAAA,CAAAkQ,6BAAA,IAAAlQ,IAAA,CAAAkQ,6BAAA,CAAAlK,MAAA;YACAhG,IAAA,CAAAkQ,6BAAA,CAAA/S,OAAA,WAAAgT,MAAA;cACA,IAAAA,MAAA,CAAAC,WAAA,GAAAH,UAAA;gBACAA,UAAA,GAAAE,MAAA,CAAAC,WAAA;cACA;YACA;UACA;QACA;;QAEA;QACA,KAAAzW,uBAAA,CAAAwD,OAAA,WAAA6C,IAAA;UACA,IAAAA,IAAA,CAAAkQ,6BAAA,IAAAlQ,IAAA,CAAAkQ,6BAAA,CAAAlK,MAAA;YACA,IAAAqK,YAAA,GAAArQ,IAAA,CAAAkQ,6BAAA,CAAAnQ,IAAA,WAAAoQ,MAAA;cAAA,OAAAA,MAAA,CAAAC,WAAA,KAAAH,UAAA;YAAA;YACA,IAAAI,YAAA;cACAL,KAAA,IAAA1L,UAAA,CAAA+L,YAAA,CAAAC,MAAA;YACA;UACA;QACA;MACA;MACA,QAAAN,KAAA,UAAAtJ,OAAA;IACA;IAEA;IACA6J,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9S,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAA4S,SAAA;QAAA,WAAA7S,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAA0S,SAAA;UAAA,kBAAAA,SAAA,CAAAxS,CAAA;YAAA;cACAM,OAAA,CAAAW,GAAA,aAAAqR,OAAA,CAAA5W,sBAAA;cACA;cACA4W,OAAA,CAAAnS,SAAA;gBACAmS,OAAA,CAAAV,uBAAA;cACA;YAAA;cAAA,OAAAY,SAAA,CAAAhS,CAAA;UAAA;QAAA,GAAA+R,QAAA;MAAA;IACA;IAEA;IACAE,6BAAA,WAAAA,8BAAA;MACA;MACA,IAAAC,UAAA;;MAEA;MACA,IAAAC,gBAAA;MACA,IAAAC,aAAA,QAAAnX,uBAAA;;MAEA;MACAmX,aAAA,CAAA3T,OAAA,WAAA6C,IAAA;QACA,IAAA+Q,YAAA,GAAA/Q,IAAA,CAAAgR,UAAA;QACA,KAAAH,gBAAA,CAAA3D,QAAA,CAAA6D,YAAA;UACAF,gBAAA,CAAA7E,IAAA,CAAA+E,YAAA;QACA;MACA;;MAEA;MACAF,gBAAA,CAAArI,IAAA;;MAEA;MACA,IAAAyI,QAAA;MACAJ,gBAAA,CAAA1T,OAAA,WAAA4T,YAAA,EAAA1O,KAAA;QACA4O,QAAA,CAAAF,YAAA,IAAAH,UAAA,CAAAvO,KAAA,GAAAuO,UAAA,CAAA5K,MAAA;MACA;MAEA,OAAAiL,QAAA;IACA;IAEA;IACAnB,uBAAA,WAAAA,wBAAA;MAAA,IAAAoB,OAAA;MACA,IAAAjO,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAAF,QAAA;QACAzE,OAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,SAAArF,MAAA,CAAA+X,mBAAA;QACA,KAAA/X,MAAA,CAAA+X,mBAAA,CAAA9T,OAAA;MACA;MAEA,IAAA+T,OAAA,GAAA/Y,OAAA,CAAA+K,IAAA,CAAAH,QAAA;MACA,KAAA7J,MAAA,CAAA+X,mBAAA,GAAAC,OAAA;MAEA,IAAAN,aAAA,QAAAnX,uBAAA;MAEA,KAAAmX,aAAA,IAAAA,aAAA,CAAA9K,MAAA;QACA,IAAA3C,OAAA;UACAC,eAAA;UACA+N,KAAA;YACAC,IAAA;YACAxW,IAAA;YACAD,GAAA;YACA0K,SAAA;cACA5K,KAAA;cACAmJ,QAAA;YACA;UACA;QACA;QACAsN,OAAA,CAAA1L,SAAA,CAAArC,OAAA;QACA;MACA;;MAEA;MACA,IAAAkO,YAAA,GAAAT,aAAA;MACA,SAAAlX,sBAAA,SAAAA,sBAAA;QACA2X,YAAA,GAAAT,aAAA,CAAArE,MAAA,WAAAzM,IAAA;UACA,OAAAA,IAAA,CAAAgR,UAAA,KAAAE,OAAA,CAAAtX,sBAAA,IACAoG,IAAA,CAAAgR,UAAA,CAAA9D,QAAA,CAAAgE,OAAA,CAAAtX,sBAAA,KACAsX,OAAA,CAAAtX,sBAAA,CAAAsT,QAAA,CAAAlN,IAAA,CAAAgR,UAAA;QACA;MACA;;MAEA;MACA,IAAA1F,QAAA,OAAAC,GAAA;MACAgG,YAAA,CAAApU,OAAA,WAAA6C,IAAA;QACA,IAAAA,IAAA,CAAAkQ,6BAAA;UACAlQ,IAAA,CAAAkQ,6BAAA,CAAA/S,OAAA,WAAAgT,MAAA;YACA7E,QAAA,CAAAG,GAAA,CAAA0E,MAAA,CAAAC,WAAA;UACA;QACA;MACA;MAEA,IAAAoB,WAAA,GAAA3R,KAAA,CAAA8L,IAAA,CAAAL,QAAA,EAAA9C,IAAA;;MAEA;MACA,IAAAiJ,cAAA,GAAAD,WAAA,CAAA7K,GAAA,WAAA+K,OAAA;QACA,IAAAA,OAAA,IAAAA,OAAA,CAAA1L,MAAA;UACA,IAAA0H,KAAA,GAAAgE,OAAA,CAAAjE,SAAA;UACA,IAAAkE,GAAA,GAAAD,OAAA,CAAAjE,SAAA;UACA,UAAAhI,MAAA,CAAAiI,KAAA,OAAAjI,MAAA,CAAAkM,GAAA;QACA;QACA,OAAAD,OAAA;MACA;;MAEA;MACA,IAAAE,UAAA;MACA,IAAAhG,UAAA;;MAEA;MACA,IAAAqF,QAAA,QAAAN,6BAAA;MAEAY,YAAA,CAAApU,OAAA,WAAA6C,IAAA,EAAAqC,KAAA;QACA,IAAAwP,QAAA,GAAA7R,IAAA,CAAAgR,UAAA;;QAEA;QACA,IAAAc,QAAA,GAAAN,WAAA,CAAA7K,GAAA,WAAAmF,IAAA;UAAA,IAAAiG,qBAAA;UACA,IAAA5B,MAAA,IAAA4B,qBAAA,GAAA/R,IAAA,CAAAkQ,6BAAA,cAAA6B,qBAAA,uBAAAA,qBAAA,CAAAhS,IAAA,WAAAiS,CAAA;YAAA,OAAAA,CAAA,CAAA5B,WAAA,KAAAtE,IAAA;UAAA;UACA,OAAAqE,MAAA,GAAA7L,UAAA,CAAA6L,MAAA,CAAAG,MAAA;QACA;;QAEA;QACA,IAAA2B,aAAA,GAAAhB,QAAA,CAAAY,QAAA;QAEA,IAAAK,UAAA;UACAtZ,IAAA,EAAAiZ,QAAA;UACAnO,IAAA;UACA7K,IAAA,EAAAiZ,QAAA;UACA1L,MAAA;UACA8F,MAAA;UACAC,UAAA;UACAvI,SAAA;YACAyC,KAAA;YACA1L,KAAA,EAAAsX;UACA;UACAzN,SAAA;YACA7J,KAAA,EAAAsX;UACA;UACA7F,YAAA;QACA;QAEAwF,UAAA,CAAA5F,IAAA,CAAAkG,UAAA;QACAtG,UAAA,CAAAI,IAAA,CAAA6F,QAAA;MACA;MAEA,IAAAxO,MAAA;QACAC,eAAA;QACA8B,OAAA;UACAC,OAAA;UACAmB,WAAA;YACA9C,IAAA;YACAoJ,UAAA;cACAnS,KAAA;YACA;UACA;UACA2I,eAAA;UACAgC,WAAA;UACAyB,WAAA;UACAxB,SAAA;YACA5K,KAAA;UACA;UACAwJ,SAAA,WAAAA,UAAAe,MAAA;YACA,IAAAiN,WAAA,GAAAjN,MAAA,IAAAtM,IAAA;YACAsM,MAAA,CAAA/H,OAAA,WAAAqI,KAAA;cACA,IAAAA,KAAA,CAAAvM,KAAA,aAAAuM,KAAA,CAAAvM,KAAA,KAAAmI,SAAA;gBACA+Q,WAAA,OAAA1M,MAAA,CAAAD,KAAA,CAAA2H,MAAA,EAAA1H,MAAA,CAAAD,KAAA,CAAAyH,UAAA,QAAAxH,MAAA,CAAAD,KAAA,CAAAvM,KAAA,CAAAyN,OAAA;cACA;YACA;YACA,OAAAyL,WAAA;UACA;QACA;QACA9E,MAAA;UACAxU,IAAA,EAAA+S,UAAA;UACArG,SAAA;YACA5K,KAAA;UACA;UACAK,MAAA;UACAF,IAAA;QACA;QACAyI,IAAA;UACAzI,IAAA;UACAC,KAAA;UACAC,MAAA;UACAH,GAAA;UACA2I,YAAA;QACA;QACAC,KAAA;UACAC,IAAA;UACA7K,IAAA,EAAA4Y,cAAA;UACA9N,QAAA;YACAC,SAAA;cACAjJ,KAAA;YACA;UACA;UACAkJ,SAAA;YACAlJ,KAAA;UACA;QACA;QACAoJ,KAAA;UACAL,IAAA;UACA9K,IAAA;UACAoL,aAAA;YACArJ,KAAA;YACAsJ,KAAA;YACAH,QAAA;UACA;UACAH,QAAA;YACAC,SAAA;cACAjJ,KAAA;YACA;UACA;UACAkJ,SAAA;YACAlJ,KAAA;UACA;UACAyJ,SAAA;YACAR,SAAA;cACAjJ,KAAA;YACA;UACA;QACA;QACA0J,MAAA,EAAAuN;MACA;MAEAR,OAAA,CAAA1L,SAAA,CAAArC,MAAA;IACA;IAEA;IACA+O,oBAAA,WAAAA,qBAAAnZ,KAAA;MACA,IAAAoZ,QAAA,GAAA1E,QAAA,CAAA1U,KAAA;;MAEA;MACA,IAAAoZ,QAAA;QACA;MACA;;MAEA;MACA,IAAAC,gBAAA,GAAA3E,QAAA,MAAAlS,WAAA,CAAAC,iBAAA;MACA,IAAA6W,aAAA,GAAA5E,QAAA,MAAAlS,WAAA,CAAAE,cAAA;MACA,IAAA6W,QAAA,GAAAlR,IAAA,CAAAE,GAAA,CAAA8Q,gBAAA,EAAAC,aAAA;;MAEA;MACA,IAAAtY,UAAA,GAAAoY,QAAA,GAAAG,QAAA;MACA,OAAAlR,IAAA,CAAAC,GAAA,MAAAD,IAAA,CAAAE,GAAA,IAAAvH,UAAA;IACA;EACA;AACA", "ignoreList": []}]}