{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\collect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\collect\\index.vue", "mtime": 1756456282473}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_info", "require", "_auth", "_zipdownload", "_dept", "_vueTreeselect", "_interopRequireDefault", "name", "components", "Treeselect", "data", "loading", "showSearch", "total", "listInfo", "title", "open", "queryParams", "pageNum", "pageSize", "workNo", "deptId", "assessDate", "status", "form", "id", "deptScore", "businessScore", "leaderScore", "rules", "deptOptions", "openCheck", "checkInfo", "deptName", "list", "spanList", "upload", "isUploading", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "created", "getDefaultAssessDate", "getTreeselect", "getList", "methods", "now", "Date", "currentDay", "getDate", "targetDate", "getFullYear", "getMonth", "year", "month", "concat", "_this", "listDept", "then", "response", "handleTree", "normalizer", "node", "children", "length", "label", "_this2", "rows", "cancel", "reset", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleDetail", "row", "_this3", "getInfo", "res", "console", "log", "code", "JSON", "parse", "content", "handleSpanList", "flag", "i", "push", "rowspan", "colspan", "item", "objectSpanMethod", "_ref", "column", "rowIndex", "columnIndex", "category", "handleExport", "_this4", "exportInfo", "_objectSpread2", "default", "download", "msg", "handleExportDetail", "_this5", "exportDetail", "handleBatchExport", "downLoadZip", "handleExportTechnicalSummary", "_this6", "$modal", "msgError", "exportTechnicalPerformanceSummary", "catch", "error", "handleExportAdministrativeSummary", "_this7", "exportAdministrativePerformanceSummary", "handleFileUploadProgress", "handleFileSuccess"], "sources": ["src/views/assess/self/collect/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.assessDate\"\r\n              type=\"month\"\r\n              value-format=\"yyyy-M\"\r\n              format=\"yyyy 年 M 月\"\r\n              placeholder=\"选择考核年月\"\r\n              :clearable=\"false\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <treeselect style=\"width: 200px;\" v-model=\"queryParams.deptId\" :multiple=\"false\" :options=\"deptOptions\" :normalizer=\"normalizer\" :disable-branch-nodes=\"true\" placeholder=\"请选择部门\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"工号\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.workNo\"\r\n              placeholder=\"请输入工号\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入姓名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable style=\"width: 200px;\">\r\n              <el-option label=\"未提交/退回\" value=\"0\"></el-option>\r\n              <el-option label=\"部门领导评分\" value=\"1\"></el-option>\r\n              <el-option label=\"事业部评分\" value=\"2\"></el-option>\r\n              <el-option label=\"运改部/组织部审核\" value=\"3\"></el-option>\r\n              <el-option label=\"总经理部评分\" value=\"4\"></el-option>\r\n              <el-option label=\"已完成\" value=\"5\"></el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            type=\"primary\"\r\n            plain\r\n            icon=\"el-icon-download\"\r\n            size=\"small\"\r\n            @click=\"handleExport\"\r\n          >导出列表</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            type=\"warning\"\r\n            plain\r\n            icon=\"el-icon-download\"\r\n            size=\"small\"\r\n            @click=\"handleBatchExport\"\r\n          >批量导出(zip)</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-download\"\r\n            size=\"small\"\r\n            @click=\"handleExportTechnicalSummary\"\r\n          >导出技术序列业绩汇总表</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            type=\"success\"\r\n            plain\r\n            icon=\"el-icon-download\"\r\n            size=\"small\"\r\n            @click=\"handleExportAdministrativeSummary\"\r\n          >导出行政序列业绩汇总表</el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-upload\r\n          accept=\".xlsx, .xls\"\r\n          :headers=\"upload.headers\"\r\n          :disabled=\"upload.isUploading\"\r\n          :action=\"upload.url\"\r\n          :show-file-list=\"false\"\r\n          :multiple=\"false\"\r\n          :on-progress=\"handleFileUploadProgress\"\r\n          :on-success=\"handleFileSuccess\">\r\n              <el-button size=\"small\" type=\"success\" plain icon=\"el-icon-download\">导入最终分数</el-button>\r\n          </el-upload>\r\n          <!-- <el-button\r\n            type=\"success\"\r\n            plain\r\n            icon=\"el-icon-upload2\"\r\n            size=\"small\"\r\n            @click=\"handleImportFinalScore\"\r\n          >导入最终分数</el-button> -->\r\n        </el-col>\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n\r\n      <el-table v-loading=\"loading\" :data=\"listInfo\">\r\n        <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n        <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n        <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" ></el-table-column>\r\n        <el-table-column label=\"自评分\" align=\"center\" prop=\"selfScore\" ></el-table-column>\r\n        <el-table-column label=\"部门领导评分\" align=\"center\" prop=\"selfScore\" >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.deptScore ? scope.row.deptScore : \"\"}}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"事业部评分\" align=\"center\" prop=\"selfScore\" >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.businessScore ? scope.row.businessScore : \"\"}}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"运改组织部审核\" align=\"center\" prop=\"selfScore\" >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.organizationScore ? scope.row.organizationScore : \"\"}}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"总经理部评分\" align=\"center\" prop=\"selfScore\" >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.leaderScore ? scope.row.leaderScore : \"\"}}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"最终分数\" align=\"center\" prop=\"finalScore\" >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.finalScore ? scope.row.finalScore : \"\"}}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\" >\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.status == '0' && scope.row.rejectReason\" type=\"danger\">退 回</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '0' && !scope.row.rejectReason\" type=\"info\">未提交</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '1'\" type=\"warning\">部门领导评分</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '2'\" type=\"warning\">事业部评分</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '3'\" type=\"warning\">运改部/组织部审核</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '4'\" type=\"warning\">总经理部评分</el-tag>\r\n            <el-tag v-if=\"scope.row.status == '5'\" type=\"success\">已完成</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleDetail(scope.row)\"\r\n            >详情</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n\r\n      <el-dialog\r\n        :visible.sync=\"open\"\r\n        fullscreen>\r\n        <h3 style=\"text-align: center;\">月度业绩考核表</h3>\r\n          <el-descriptions class=\"margin-top\" :column=\"3\">\r\n            <el-descriptions-item>\r\n              <template slot=\"label\">\r\n                姓名\r\n              </template>\r\n              {{ checkInfo.name }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item>\r\n              <template slot=\"label\">\r\n                部门\r\n              </template>\r\n              {{ checkInfo.deptName }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item>\r\n              <template slot=\"label\">\r\n                考核年月\r\n              </template>\r\n              {{ checkInfo.assessDate }}\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n\r\n          <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                type=\"primary\"\r\n                plain\r\n                icon=\"el-icon-plus\"\r\n                size=\"small\"\r\n                @click=\"handleExportDetail\"\r\n              >导出</el-button>\r\n            </el-col>\r\n          </el-row>\r\n          <el-table v-loading=\"loading\" :data=\"checkInfo.list\"\r\n            :span-method=\"objectSpanMethod\" border>\r\n            <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n            <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"150\"/>\r\n            <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"180\" />\r\n            <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n            <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" />\r\n            <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"150\" />\r\n            <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\" width=\"180\" />\r\n          </el-table>\r\n          <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" style=\"margin-top: 10px;\">\r\n            <el-form-item label=\"自评分数 / 签名：\">\r\n              <span >{{ checkInfo.selfScore + \" 分 / \" + checkInfo.name }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.deptScore && checkInfo.deptUserName\" label=\"部门领导评分 / 签名：\">\r\n              <span >{{ checkInfo.deptScore + \" 分 / \" + checkInfo.deptUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.businessUserName && checkInfo.businessScore\" label=\"事业部领导评分 / 签名：\">\r\n              <span>{{ checkInfo.businessScore + \" 分 / \" + checkInfo.businessUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.organizationScore && checkInfo.organizationUserName\" label=\"运改组织部审核：\">\r\n              <span >{{ checkInfo.organizationScore + \" 分 / \" + checkInfo.organizationUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.leaderScore && checkInfo.leaderName\" label=\"总经理部领导评分 / 签名：\">\r\n              <span >{{ checkInfo.leaderScore + \" 分 / \" + checkInfo.leaderName }}</span>\r\n            </el-form-item>\r\n          </el-form>\r\n          <div style=\"text-align: center;\">\r\n            <el-button plain type=\"info\" @click=\"cancel\">返 回</el-button>\r\n          </div>\r\n      </el-dialog>\r\n\r\n    </div>\r\n  </template>\r\n\r\n<script>\r\nimport { listInfo, getInfo, exportInfo, exportDetail, exportTechnicalPerformanceSummary, exportAdministrativePerformanceSummary } from \"@/api/assess/self/info\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { downLoadZip } from \"@/utils/zipdownload\";\r\nimport { listDept } from \"@/api/assess/lateral/dept\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n    name: \"SelfAssessCollect\",\r\n    components: {\r\n        Treeselect\r\n    },\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 绩效考核-干部自评人员配置表格数据\r\n        listInfo: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          workNo: null,\r\n          name:null,\r\n          deptId:null,\r\n          assessDate:null,\r\n          status:null\r\n        },\r\n        // 表单参数\r\n        form: {\r\n          id:null,\r\n          // 部门领导评分\r\n          deptScore:null,\r\n          // 事业部评分\r\n          businessScore:null,\r\n          // 条线领导评分\r\n          leaderScore:null,\r\n        },\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        deptOptions:[],\r\n        openCheck:false,\r\n        checkInfo:{\r\n          name:null,\r\n          assessDate:null,\r\n          deptName:null,\r\n          list:[]\r\n        },\r\n        // 合并单元格\r\n        spanList:[],\r\n        // 导入参数\r\n        upload: {\r\n          // 是否禁用上传\r\n          isUploading: false,\r\n          // 设置上传的请求头部\r\n          headers: { Authorization: 'Bearer ' + getToken() },\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/web/selfAssess/info/importFinalScore\",\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n        this.queryParams.assessDate = this.getDefaultAssessDate()\r\n        // this.getSelfAssessUser();\r\n        // this.getCheckDeptList();\r\n        this.getTreeselect();\r\n        this.getList();\r\n    },\r\n    methods: {\r\n\r\n        // 获取默认考核日期\r\n        getDefaultAssessDate() {\r\n            const now = new Date();\r\n            const currentDay = now.getDate();\r\n\r\n            let targetDate;\r\n            if (currentDay < 10) {\r\n                // 当前日期小于10日，默认为上个月\r\n                targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n            } else {\r\n                // 当前日期大于等于10日，默认为当月\r\n                targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n            }\r\n\r\n            // 格式化为 YYYY-M 格式\r\n            const year = targetDate.getFullYear();\r\n            const month = targetDate.getMonth() + 1;\r\n            return `${year}-${month}`;\r\n        },\r\n\r\n        // 获取部门信息\r\n        /** 查询横向评价部门下拉树结构 */\r\n        getTreeselect() {\r\n            listDept().then(response => {\r\n                this.deptOptions = this.handleTree(response.data, \"deptId\", \"parentId\");\r\n            });\r\n        },\r\n        /** 转换横向评价部门数据结构 */\r\n        normalizer(node) {\r\n            if (node.children && !node.children.length) {\r\n                delete node.children;\r\n            }\r\n            return {\r\n                id: node.deptId,\r\n                label: node.deptName,\r\n                children: node.children\r\n            };\r\n        },\r\n        /** 查询绩效考核-干部自评待审核列表 */\r\n        getList() {\r\n            this.loading = true;\r\n            listInfo(this.queryParams).then(response => {\r\n                this.listInfo = response.rows;\r\n                this.total = response.total;\r\n                this.loading = false;\r\n            });\r\n        },\r\n        // 取消按钮\r\n        cancel() {\r\n            this.open = false;\r\n            this.reset();\r\n        },\r\n        // 表单重置\r\n        reset() {\r\n            this.form = {\r\n                id: null,\r\n                deptScore: null,\r\n                businessScore: null,\r\n                leaderScore: null,\r\n            };\r\n            // this.resetForm(\"form\");\r\n        },\r\n        /** 搜索按钮操作 */\r\n        handleQuery() {\r\n            this.queryParams.pageNum = 1;\r\n            this.getList();\r\n        },\r\n        /** 重置按钮操作 */\r\n        resetQuery() {\r\n            this.resetForm(\"queryForm\");\r\n            this.handleQuery();\r\n        },\r\n        // 审批详情\r\n        handleDetail(row){\r\n            getInfo({id:row.id}).then(res => {\r\n                console.log(res);\r\n                if(res.code == 200){\r\n                    this.checkInfo = res.data;\r\n                    let list = JSON.parse(res.data.content);\r\n                    this.handleSpanList(list);\r\n                    this.checkInfo.list = list;\r\n                }\r\n                this.open = true\r\n            })\r\n        },\r\n\r\n        // 处理列表\r\n        handleSpanList(data){\r\n            let spanList = [];\r\n            let flag = 0;\r\n            for(let i = 0; i < data.length; i++){\r\n            // 相同考核项合并\r\n            if(i == 0){\r\n                spanList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n                })\r\n            }else{\r\n                if(data[i - 1].item == data[i].item){\r\n                spanList.push({\r\n                    rowspan: 0,\r\n                    colspan: 0\r\n                })\r\n                spanList[flag].rowspan += 1;\r\n                }else{\r\n                spanList.push({\r\n                    rowspan: 1,\r\n                    colspan: 1\r\n                })\r\n                flag = i;\r\n                }\r\n            }\r\n            }\r\n            this.spanList = spanList;\r\n        },\r\n\r\n        // 合并单元格方法\r\n        objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n            // 第一列相同项合并\r\n            if (columnIndex === 0) {\r\n            return this.spanList[rowIndex];\r\n            }\r\n            // 类别无内容 合并\r\n            if(columnIndex === 1){\r\n            if(!row.category){\r\n                return {\r\n                rowspan: 0,\r\n                colspan: 0\r\n                }\r\n            }\r\n            }\r\n            if(columnIndex === 2){\r\n            if(!row.category){\r\n                return {\r\n                rowspan: 1,\r\n                colspan: 2\r\n                }\r\n            }\r\n            }\r\n        },\r\n\r\n        // 导出按钮点击事件\r\n        handleExport(){\r\n          exportInfo({...this.queryParams}).then(res => {\r\n            console.log(res)\r\n            this.download(res.msg,\"月度绩效考核汇总表.xlsx\")\r\n          })\r\n        },\r\n\r\n        // 详细导出\r\n        handleExportDetail(){\r\n          exportDetail({id:this.checkInfo.id}).then(res => {\r\n            console.log(res)\r\n            this.download(res.msg,\"月度绩效考核表.xlsx\")\r\n          })\r\n        },\r\n\r\n        handleBatchExport(){\r\n          downLoadZip(\"/web/selfAssess/info/batchExportDetail?assessDate=\" + this.queryParams.assessDate, \"ruoyi\");\r\n        },\r\n\r\n        // 导出技术序列业绩汇总表\r\n        handleExportTechnicalSummary(){\r\n          if (!this.queryParams.assessDate) {\r\n            this.$modal.msgError(\"请选择考核年月\");\r\n            return;\r\n          }\r\n          exportTechnicalPerformanceSummary({assessDate: this.queryParams.assessDate}).then(res => {\r\n            console.log(res)\r\n            this.download(res.msg,\"技术序列业绩汇总表.xlsx\")\r\n          }).catch(error => {\r\n            console.error(\"导出失败：\", error);\r\n            this.$modal.msgError(\"导出失败，请稍后重试\");\r\n          })\r\n        },\r\n\r\n        // 导出行政序列业绩汇总表\r\n        handleExportAdministrativeSummary(){\r\n          if (!this.queryParams.assessDate) {\r\n            this.$modal.msgError(\"请选择考核年月\");\r\n            return;\r\n          }\r\n          exportAdministrativePerformanceSummary({assessDate: this.queryParams.assessDate}).then(res => {\r\n            console.log(res)\r\n            this.download(res.msg,\"行政序列业绩汇总表.xlsx\")\r\n          }).catch(error => {\r\n            console.error(\"导出失败：\", error);\r\n            this.$modal.msgError(\"导出失败，请稍后重试\");\r\n          })\r\n        },\r\n\r\n        // 导入相关\r\n        handleFileUploadProgress(){\r\n        this.upload.isUploading = true;\r\n        },\r\n        handleFileSuccess(response){\r\n            this.upload.isUploading = false;\r\n            console.log(response)\r\n            // this.handleQuery();\r\n            // this.importRes = response.data;\r\n            // this.openImportRes = true;\r\n        },\r\n    }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;AAmPA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAC,sBAAA,CAAAL,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAb,IAAA;QACAc,MAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;QACAC,EAAA;QACA;QACAC,SAAA;QACA;QACAC,aAAA;QACA;QACAC,WAAA;MACA;MACA;MACAC,KAAA,GACA;MACAC,WAAA;MACAC,SAAA;MACAC,SAAA;QACAzB,IAAA;QACAe,UAAA;QACAW,QAAA;QACAC,IAAA;MACA;MACA;MACAC,QAAA;MACA;MACAC,MAAA;QACA;QACAC,WAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAA5B,WAAA,CAAAK,UAAA,QAAAwB,oBAAA;IACA;IACA;IACA,KAAAC,aAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IAEA;IACAH,oBAAA,WAAAA,qBAAA;MACA,IAAAI,GAAA,OAAAC,IAAA;MACA,IAAAC,UAAA,GAAAF,GAAA,CAAAG,OAAA;MAEA,IAAAC,UAAA;MACA,IAAAF,UAAA;QACA;QACAE,UAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAM,QAAA;MACA;QACA;QACAF,UAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAM,QAAA;MACA;;MAEA;MACA,IAAAC,IAAA,GAAAH,UAAA,CAAAC,WAAA;MACA,IAAAG,KAAA,GAAAJ,UAAA,CAAAE,QAAA;MACA,UAAAG,MAAA,CAAAF,IAAA,OAAAE,MAAA,CAAAD,KAAA;IACA;IAEA;IACA;IACAX,aAAA,WAAAA,cAAA;MAAA,IAAAa,KAAA;MACA,IAAAC,cAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA9B,WAAA,GAAA8B,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAArD,IAAA;MACA;IACA;IACA,mBACAuD,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACA1C,EAAA,EAAAyC,IAAA,CAAA7C,MAAA;QACAgD,KAAA,EAAAH,IAAA,CAAAjC,QAAA;QACAkC,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA,uBACAnB,OAAA,WAAAA,QAAA;MAAA,IAAAsB,MAAA;MACA,KAAA3D,OAAA;MACA,IAAAG,cAAA,OAAAG,WAAA,EAAA6C,IAAA,WAAAC,QAAA;QACAO,MAAA,CAAAxD,QAAA,GAAAiD,QAAA,CAAAQ,IAAA;QACAD,MAAA,CAAAzD,KAAA,GAAAkD,QAAA,CAAAlD,KAAA;QACAyD,MAAA,CAAA3D,OAAA;MACA;IACA;IACA;IACA6D,MAAA,WAAAA,OAAA;MACA,KAAAxD,IAAA;MACA,KAAAyD,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAjD,IAAA;QACAC,EAAA;QACAC,SAAA;QACAC,aAAA;QACAC,WAAA;MACA;MACA;IACA;IACA,aACA8C,WAAA,WAAAA,YAAA;MACA,KAAAzD,WAAA,CAAAC,OAAA;MACA,KAAA8B,OAAA;IACA;IACA,aACA2B,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IACA;IACAG,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,aAAA;QAAAvD,EAAA,EAAAqD,GAAA,CAAArD;MAAA,GAAAqC,IAAA,WAAAmB,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAL,MAAA,CAAA/C,SAAA,GAAAiD,GAAA,CAAAvE,IAAA;UACA,IAAAwB,IAAA,GAAAmD,IAAA,CAAAC,KAAA,CAAAL,GAAA,CAAAvE,IAAA,CAAA6E,OAAA;UACAR,MAAA,CAAAS,cAAA,CAAAtD,IAAA;UACA6C,MAAA,CAAA/C,SAAA,CAAAE,IAAA,GAAAA,IAAA;QACA;QACA6C,MAAA,CAAA/D,IAAA;MACA;IACA;IAEA;IACAwE,cAAA,WAAAA,eAAA9E,IAAA;MACA,IAAAyB,QAAA;MACA,IAAAsD,IAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAhF,IAAA,CAAA0D,MAAA,EAAAsB,CAAA;QACA;QACA,IAAAA,CAAA;UACAvD,QAAA,CAAAwD,IAAA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;UACA,IAAAnF,IAAA,CAAAgF,CAAA,MAAAI,IAAA,IAAApF,IAAA,CAAAgF,CAAA,EAAAI,IAAA;YACA3D,QAAA,CAAAwD,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACA1D,QAAA,CAAAsD,IAAA,EAAAG,OAAA;UACA;YACAzD,QAAA,CAAAwD,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAJ,IAAA,GAAAC,CAAA;UACA;QACA;MACA;MACA,KAAAvD,QAAA,GAAAA,QAAA;IACA;IAEA;IACA4D,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAAlB,GAAA,GAAAkB,IAAA,CAAAlB,GAAA;QAAAmB,MAAA,GAAAD,IAAA,CAAAC,MAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;QAAAC,WAAA,GAAAH,IAAA,CAAAG,WAAA;MACA;MACA,IAAAA,WAAA;QACA,YAAAhE,QAAA,CAAA+D,QAAA;MACA;MACA;MACA,IAAAC,WAAA;QACA,KAAArB,GAAA,CAAAsB,QAAA;UACA;YACAR,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA,IAAAM,WAAA;QACA,KAAArB,GAAA,CAAAsB,QAAA;UACA;YACAR,OAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,gBAAA,MAAAC,cAAA,CAAAC,OAAA,WAAAxF,WAAA,GAAA6C,IAAA,WAAAmB,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACAqB,MAAA,CAAAI,QAAA,CAAAzB,GAAA,CAAA0B,GAAA;MACA;IACA;IAEA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,kBAAA;QAAArF,EAAA,OAAAO,SAAA,CAAAP;MAAA,GAAAqC,IAAA,WAAAmB,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA4B,MAAA,CAAAH,QAAA,CAAAzB,GAAA,CAAA0B,GAAA;MACA;IACA;IAEAI,iBAAA,WAAAA,kBAAA;MACA,IAAAC,wBAAA,8DAAA/F,WAAA,CAAAK,UAAA;IACA;IAEA;IACA2F,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,MAAA;MACA,UAAAjG,WAAA,CAAAK,UAAA;QACA,KAAA6F,MAAA,CAAAC,QAAA;QACA;MACA;MACA,IAAAC,uCAAA;QAAA/F,UAAA,OAAAL,WAAA,CAAAK;MAAA,GAAAwC,IAAA,WAAAmB,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACAiC,MAAA,CAAAR,QAAA,CAAAzB,GAAA,CAAA0B,GAAA;MACA,GAAAW,KAAA,WAAAC,KAAA;QACArC,OAAA,CAAAqC,KAAA,UAAAA,KAAA;QACAL,MAAA,CAAAC,MAAA,CAAAC,QAAA;MACA;IACA;IAEA;IACAI,iCAAA,WAAAA,kCAAA;MAAA,IAAAC,MAAA;MACA,UAAAxG,WAAA,CAAAK,UAAA;QACA,KAAA6F,MAAA,CAAAC,QAAA;QACA;MACA;MACA,IAAAM,4CAAA;QAAApG,UAAA,OAAAL,WAAA,CAAAK;MAAA,GAAAwC,IAAA,WAAAmB,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACAwC,MAAA,CAAAf,QAAA,CAAAzB,GAAA,CAAA0B,GAAA;MACA,GAAAW,KAAA,WAAAC,KAAA;QACArC,OAAA,CAAAqC,KAAA,UAAAA,KAAA;QACAE,MAAA,CAAAN,MAAA,CAAAC,QAAA;MACA;IACA;IAEA;IACAO,wBAAA,WAAAA,yBAAA;MACA,KAAAvF,MAAA,CAAAC,WAAA;IACA;IACAuF,iBAAA,WAAAA,kBAAA7D,QAAA;MACA,KAAA3B,MAAA,CAAAC,WAAA;MACA6C,OAAA,CAAAC,GAAA,CAAApB,QAAA;MACA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}