import * as echarts from 'echarts'

export default {
  methods: {
    // 获取矿焦煤物料类型的颜色映射
    getCokingCoalMaterialColorMap() {
      // 使用月度库存金额图表的颜色方案
      const baseColors = ['#0066ff', '#00ff00', '#ff0000', '#8b00ff', '#ffff00', '#ffffff']

      // 基于所有原始数据为每个物料类型分配固定颜色，确保过滤时颜色保持一致
      const allMaterialTypes = []
      const inventoryData = this.cokingCoalInventoryData || []

      // 收集所有物料类型
      inventoryData.forEach(item => {
        const materialName = item.class2Name || '未知物料'
        if (!allMaterialTypes.includes(materialName)) {
          allMaterialTypes.push(materialName)
        }
      })

      // 按字母顺序排序，确保颜色分配的一致性
      allMaterialTypes.sort()

      // 为每个物料类型分配固定颜色
      const colorMap = {}
      allMaterialTypes.forEach((materialName, index) => {
        colorMap[materialName] = baseColors[index % baseColors.length]
      })

      return colorMap
    },

    // 清理并重新初始化图表
    reinitChart(chartId) {
      const chartDom = document.getElementById(chartId)
      if (!chartDom) {
        console.error(`找不到图表DOM: ${chartId}`)
        return null
      }

      if (this.chartInstances[chartId] && this.chartInstances[chartId].intervalId) {
        clearInterval(this.chartInstances[chartId].intervalId)
        this.chartInstances[chartId].intervalId = null
      }

      const existingInstance = echarts.getInstanceByDom(chartDom)

      if (existingInstance) {
        try {
          echarts.dispose(existingInstance)
          console.log(`ECharts instance successfully disposed for: ${chartId}`)
        } catch (err) {
          console.error(`Error disposing ECharts instance for ${chartId}:`, err)
          chartDom.innerHTML = ''
        }
      }

      chartDom.innerHTML = '<div class="chart-placeholder">数据加载中...</div>'
      this.chartInstances[chartId] = null

      try {
        const newChart = echarts.init(chartDom)
        this.chartInstances[chartId] = newChart
        console.log(`创建新图表: ${chartId}`)
        return newChart
      } catch (err) {
        console.error(`创建图表失败: ${chartId}`, err)
        chartDom.innerHTML = '<div class="chart-placeholder">图表加载失败</div>'
        return null
      }
    },

    // 挂单至入库天数曲线图
    initMonthlyInventoryChart() {
      const timestamp = new Date().toLocaleTimeString()
      console.log(`initMonthlyInventoryChart - 挂单至入库天数图表初始化 [${timestamp}]`)
      const myChart = this.reinitChart('monthlyInventoryChart')
      if (!myChart) {
        console.error('initMonthlyInventoryChart - 图表初始化失败')
        return
      }
      console.log('initMonthlyInventoryChart - 图表实例创建成功')

      // 只使用真实数据，不使用模拟数据
      const orderData = this.orderToReceiptData || []
      console.log('initMonthlyInventoryChart - 使用的数据:', orderData)
      console.log('initMonthlyInventoryChart - 数据长度:', orderData.length)

      // 如果没有真实数据，直接返回，不渲染图表
      if (orderData.length === 0) {
        console.warn('initMonthlyInventoryChart - 没有真实数据，跳过图表渲染')
        return
      }
      // 使用真实数据
      console.log('initMonthlyInventoryChart - 使用真实数据')
      console.log('initMonthlyInventoryChart - 第一条真实数据:', orderData[0])

      const xAxisData = orderData.map(item => item.period)
      const yAxisData = orderData.map(item => item.avgDays)
      const midDaysData = orderData.map(item => item.midDays || 0)

      console.log('initMonthlyInventoryChart - 提取的X轴数据:', xAxisData)
      console.log('initMonthlyInventoryChart - 提取的Y轴数据:', yAxisData)
      console.log('initMonthlyInventoryChart - 提取的中位数数据:', midDaysData)

      // 构建单条曲线数据
      const seriesData = [{
        name: '挂单至入库天数',
        type: 'line',
        data: yAxisData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: '#00BAFF'
        },
        itemStyle: {
          color: '#00BAFF',
          borderColor: '#fff',
          borderWidth: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(0, 186, 255, 0.3)'
            }, {
              offset: 1, color: 'rgba(0, 186, 255, 0.1)'
            }]
          }
        },
        connectNulls: false
      }]



      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function(params) {
            let tooltipText = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.value !== null && param.value !== undefined) {
                tooltipText += `${param.marker}平均数: ${param.value}天<br/>`
                // 显示中位数（从真实数据中获取）
                const midValue = midDaysData[param.dataIndex]
                if (midValue !== undefined && midValue !== null) {
                  tooltipText += `${param.marker}中位数: ${midValue}天<br/>`
                }
              }
            })
            return tooltipText
          }
        },
        legend: {
          show: false
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '10%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee',
            interval: 0, // 强制显示所有标签
            rotate: 45 // 旋转45度避免重叠
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '平均天数',
          axisLabel: {
            color: '#eee',
            formatter: '{value}天'
          },
          nameTextStyle: {
            color: '#fff',
            fontSize: 12,
            align: 'right',
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: seriesData
      }

      myChart.setOption(option, true)
      console.log('initMonthlyInventoryChart - 图表配置设置完成')
    },

    // 第二个挂单至入库天数曲线图（使用factoryStockChart容器）
    initFactoryStockChart() {
      console.log('initFactoryStockChart - 挂单至入库天数图表初始化')
      const myChart = this.reinitChart('factoryStockChart')
      if (!myChart) return

      // 第二个图表暂时使用模拟数据
      console.log('initFactoryStockChart - 使用模拟数据（暂无API）')
      const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      const mockOrderDaysData = [15, 18, 12, 20, 16, 14, 22, 19, 17, 13, 21, 16]
      const mockMidDaysData = [13, 16, 11, 18, 14, 12, 20, 17, 15, 12, 19, 14]

      // 构建单条曲线数据
      const seriesData = [{
        name: '挂单至入库天数',
        type: 'line',
        data: mockOrderDaysData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: '#00BAFF'
        },
        itemStyle: {
          color: '#00BAFF',
          borderColor: '#fff',
          borderWidth: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(0, 186, 255, 0.3)'
            }, {
              offset: 1, color: 'rgba(0, 186, 255, 0.1)'
            }]
          }
        },
        connectNulls: false
      }]



      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function(params) {
            let tooltipText = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.value !== null && param.value !== undefined) {
                tooltipText += `${param.marker}${param.seriesName}: ${param.value}天<br/>`
                // 显示中位数
                const midValue = mockMidDaysData[param.dataIndex]
                if (midValue !== undefined && midValue !== null) {
                  tooltipText += `📊 中位数: ${midValue}天<br/>`
                }
              }
            })
            return tooltipText
          }
        },
        legend: {
          show: false
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '10%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: monthNames,
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee',
            interval: 0, // 强制显示所有标签
            rotate: 45 // 旋转45度避免重叠
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '平均天数',
          axisLabel: {
            color: '#eee',
            formatter: '{value}天'
          },
          nameTextStyle: {
            color: '#fff',
            fontSize: 12,
            align: 'right',
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: seriesData
      }

      myChart.setOption(option, true)
    },

    // 第三个挂单至入库天数曲线图（使用cokingCoalLineChart容器）
    initCokingCoalLineChart() {
      console.log('initCokingCoalLineChart - 第三个挂单至入库天数图表初始化')
      const myChart = this.reinitChart('cokingCoalLineChart')
      if (!myChart) return

      // 第三个图表暂时使用模拟数据
      console.log('initCokingCoalLineChart - 使用模拟数据（暂无API）')
      const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      const mockOrderDaysData = [15, 18, 12, 20, 16, 14, 22, 19, 17, 13, 21, 16]
      const mockMidDaysData = [13, 16, 11, 18, 14, 12, 20, 17, 15, 12, 19, 14]

      // 构建单条曲线数据
      const seriesData = [{
        name: '挂单至入库天数',
        type: 'line',
        data: mockOrderDaysData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: '#00BAFF'
        },
        itemStyle: {
          color: '#00BAFF',
          borderColor: '#fff',
          borderWidth: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(0, 186, 255, 0.3)'
            }, {
              offset: 1, color: 'rgba(0, 186, 255, 0.1)'
            }]
          }
        },
        connectNulls: false
      }]



      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function(params) {
            let tooltipText = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.value !== null && param.value !== undefined) {
                tooltipText += `${param.marker}${param.seriesName}: ${param.value}天<br/>`
                // 显示中位数
                const midValue = mockMidDaysData[param.dataIndex]
                if (midValue !== undefined && midValue !== null) {
                  tooltipText += `📊 中位数: ${midValue}天<br/>`
                }
              }
            })
            return tooltipText
          }
        },
        legend: {
          show: false
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '10%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: monthNames,
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee',
            interval: 0, // 强制显示所有标签
            rotate: 45 // 旋转45度避免重叠
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: '平均天数',
          axisLabel: {
            color: '#eee',
            formatter: '{value}天'
          },
          nameTextStyle: {
            color: '#fff',
            fontSize: 12,
            align: 'right',
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: seriesData
      }

      myChart.setOption(option, true)
    },

    // 物料入库统计图表（现在也是挂单至入库天数）
    initMaterialStatisticsChart() {
      console.log('initMaterialStatisticsChart - 第四个挂单至入库天数图表初始化')
      const myChart = this.reinitChart('materialStatisticsChart')
      if (!myChart) return

      const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']

      // 第四个图表暂时使用模拟数据
      console.log('initMaterialStatisticsChart - 使用模拟数据（暂无API）')
      const mockOrderDaysData = [15, 18, 12, 20, 16, 14, 22, 19, 17, 13, 21, 16]
      const mockMidDaysData = [13, 16, 11, 18, 14, 12, 20, 17, 15, 12, 19, 14]

      // 构建单条曲线数据
      const seriesData = [{
        name: '挂单至入库天数',
        type: 'line',
        data: mockOrderDaysData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: '#00BAFF'
        },
        itemStyle: {
          color: '#00BAFF',
          borderColor: '#fff',
          borderWidth: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(0, 186, 255, 0.3)'
            }, {
              offset: 1, color: 'rgba(0, 186, 255, 0.1)'
            }]
          }
        },
        connectNulls: false
      }]

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          formatter: function(params) {
            let tooltipText = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.value !== null && param.value !== undefined) {
                tooltipText += `${param.marker}${param.seriesName}: ${param.value}天<br/>`
                // 显示中位数
                const midValue = mockMidDaysData[param.dataIndex]
                if (midValue !== undefined && midValue !== null) {
                  tooltipText += `📊 中位数: ${midValue}天<br/>`
                }
              }
            })
            return tooltipText
          }
        },
        legend: {
          show: false
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '10%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: monthNames,
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee',
            interval: 0, // 强制显示所有标签
            rotate: 45 // 旋转45度避免重叠
          }
        },
        yAxis: {
          type: 'value',
          name: '平均天数',
          axisLabel: {
            color: '#eee',
            formatter: '{value}天'
          },
          nameTextStyle: {
            color: '#fff',
            fontSize: 12,
            align: 'right',
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: seriesData
      }

      myChart.setOption(option, true)
    },

    // 实时超期数柱状图
    initOverdueChart() {
      console.log('initOverdueChart - 实时超期数图表初始化')
      const myChart = this.reinitChart('overdueChart')
      if (!myChart) return

      const overdueData = this.overdueData || []

      // 横坐标：物料类型
      const materialTypes = overdueData.map(item => item.materialType)

      // 两个系列的数据
      const overdueNotReceivedData = overdueData.map(item => item.overdueNotReceived)
      const overdueNotUsedData = overdueData.map(item => item.overdueNotUsed)

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let tooltipText = params[0].axisValueLabel + '<br/>'
            params.forEach(param => {
              tooltipText += `${param.marker}${param.seriesName}: ${param.value}件<br/>`
            })
            return tooltipText
          }
        },
        legend: {
          data: ['超期未入库数', '超期未领用数'],
          textStyle: {
            color: '#fff'
          },
          top: '5%'
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '10%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: materialTypes,
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee',
            interval: 0,
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          name: '数量 (件)',
          axisLabel: {
            color: '#eee',
            formatter: '{value}件'
          },
          nameTextStyle: {
            color: '#fff',
            fontSize: 12,
            align: 'right',
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            name: '超期未入库数',
            type: 'bar',
            data: overdueNotReceivedData,
            itemStyle: {
              color: '#ff6b6b',
              borderRadius: [4, 4, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: '#ff5252',
                borderRadius: [4, 4, 0, 0],
                shadowBlur: 10,
                shadowColor: 'rgba(255, 255, 255, 0.5)',
                borderWidth: 2,
                borderColor: '#fff'
              }
            }
          },
          {
            name: '超期未领用数',
            type: 'bar',
            data: overdueNotUsedData,
            itemStyle: {
              color: '#ffa726',
              borderRadius: [4, 4, 0, 0]
            },
            emphasis: {
              itemStyle: {
                color: '#ff9800',
                borderRadius: [4, 4, 0, 0],
                shadowBlur: 10,
                shadowColor: 'rgba(255, 255, 255, 0.5)',
                borderWidth: 2,
                borderColor: '#fff'
              }
            }
          }
        ]
      }

      myChart.setOption(option, true)
    },


  }
}
