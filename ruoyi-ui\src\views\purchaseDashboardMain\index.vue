<template>
  <div class="purchase-dashboard-main" :class="{ 'fullscreen-mode': isFullscreen }">
    <div class="dashboard-container">
      <!-- 头部标题 -->
      <div class="dashboard-header">
        <h1>采购管理全景视图</h1>
        <div class="header-controls">
          <div class="fullscreen-btn" @click="toggleFullscreen" :title="isFullscreen ? '退出全屏' : '进入全屏'">
            <i :class="isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'"></i>
          </div>
          <div class="time-filter">
            <button
              v-for="filter in timeFilters"
              :key="filter.id"
              :class="['time-filter-btn', { active: filter.id === activeFilter }]"
              @click="handleTimeFilterChange(filter.id, filter.value)"
            >
              {{ filter.label }}
            </button>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="dashboard-content">
        <!-- 左侧面板 -->
        <div class="left-panel" :style="isFullscreen ? { width: '320px', minWidth: '320px', maxWidth: '320px', gap: '12px' } : {}">
          <!-- 左侧第一个：资金管理 -->
          <div class="card">
            <h2 class="card-title">资金管理</h2>
            <div class="chart" id="fundManagementChart"></div>
          </div>

          <!-- 左侧第二个：供应商信息全景 -->
          <div class="card">
            <h2 class="card-title">供应商信息全景</h2>
            <div class="supplier-circles">
              <!-- 中心圆形：考核情况 -->
              <div class="circle-item center-circle center" @click="goToSupplierPenalty">
                <div class="circle clickable" :style="{ borderColor: centerData.color, backgroundColor: centerData.color }">
                  <div class="circle-number">
                    <div>金额: {{ centerData.amount }}</div>
                    <div>次数: {{ centerData.count }}</div>
                  </div>
                </div>
                <div class="circle-label">{{ centerData.label }}</div>
              </div>

              <!-- 周围四个圆形 - 随机位置分布 -->
              <div
                class="circle-item random-position"
                v-for="item in supplierData"
                :key="item.id"
                :style="item.position"
              >
                <div class="circle" :style="{ borderColor: item.color, backgroundColor: item.color }">
                  <span class="circle-number">{{ item.value }}</span>
                </div>
                <div class="circle-label">{{ item.label }}</div>
              </div>
            </div>
          </div>

          <!-- 左侧第三个：合同管理（暂时隐藏） -->
          <!-- <div class="card">
            <h2 class="card-title">合同管理</h2>
            <div id="contractChart" class="chart"></div>
          </div> -->

          <!-- 左侧第三个：单一来源 -->
          <div class="card">
            <h2 class="card-title">单一来源</h2>
            <div id="supplierChart" class="chart"></div>
          </div>
        </div>

        <!-- 中间面板 -->
        <div class="center-panel" :style="isFullscreen ? { gap: '10px', flex: '1', minWidth: '400px' } : {}">
          <!-- 中间第一行 -->
          <div class="center-row center-row-first" :style="isFullscreen ? { maxHeight: 'none', flex: '1' } : {}">
            <!-- 中间第一个：计划管理 -->
            <div class="card clickable-card plan-management-card" @click="goToPlanDashboard">
              <h2 class="card-title">计划管理</h2>
              <div class="plan-grid" :style="isFullscreen ? { gap: '8px', padding: '10px 0' } : {}">
                <div class="plan-item" v-for="(item, index) in productAnalysisData" :key="item.name" :style="isFullscreen ? { padding: '10px 40px' } : {}">
                  <i :class="getPlanIcon(index)" class="plan-icon" :style="isFullscreen ? { fontSize: '18px', marginRight: '8px', width: '20px' } : {}"></i>
                  <div class="plan-text">
                    <div class="plan-value" :style="isFullscreen ? { marginBottom: '4px', fontSize: '15px' } : {}">{{ item.value }}</div>
                    <div class="plan-label" :style="isFullscreen ? { fontSize: '12px', lineHeight: '1.3' } : {}">{{ item.name }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 中间第二个：库存管理 -->
            <div class="card clickable-card" @click="goToStockDashboard">
              <h2 class="card-title">
                <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
                  <div style="display: flex; align-items: center; gap: 15px;">
                    <span>库存管理</span>
                    <span class="inventory-total">
                      合计: {{ calculateCokingCoalTotal() }}万吨
                    </span>
                  </div>
                  <div class="chart-filter-dropdown-container">
                    <select
                      v-model="selectedCokingCoalType"
                      @change="handleCokingCoalTypeChange"
                      @click.stop
                    >
                      <option value="">全部</option>
                      <option value="矿料类">矿料类</option>
                      <option value="焦炭">焦炭</option>
                      <option value="煤焦类">煤焦类</option>
                      <option value="合金类">合金类</option>
                      <option value="辅助类/电极">辅助类/电极</option>
                    </select>
                  </div>
                </div>
              </h2>
              <div id="cokingCoalLineChart" class="chart"></div>
            </div>
          </div>

          <!-- 新增：中间插入行 - 计划执行状态 -->
          <div class="center-row-full">
            <div class="card plan-execution-card" :style="isFullscreen ? { maxHeight: 'none', minHeight: '80px' } : {}">
              <!-- <h2 class="card-title">计划执行状态</h2> -->
              <div class="plan-execution-grid">
                <div class="execution-item" v-for="(item, index) in planExecutionData" :key="item.name">
                  <i :class="getExecutionIcon(index)" class="execution-icon"></i>
                  <div class="execution-text">
                    <div class="execution-value">{{ item.value }}</div>
                    <div class="execution-label">{{ item.name }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 中间第二行 -->
          <div class="center-row center-row-second" :style="isFullscreen ? { maxHeight: 'none', flex: '1' } : {}">
            <!-- 中间第三个：供方管理 -->
            <div class="card supplier-management-card">
              <h2 class="card-title">供方管理</h2>
              <!-- 上半部分：饼图 -->
              <div id="supplierManagementChart" class="chart" style="height: 40px;"></div>
              <!-- 下半部分：三个数字显示区域 -->
              <div class="supplier-stats">
                <div class="supplier-stat-item">
                  <div class="stat-number">{{ supplierStats.admission }}</div>
                  <div class="stat-label">准入</div>
                </div>
                <div class="supplier-stat-item">
                  <div class="stat-number">{{ supplierStats.elimination }}</div>
                  <div class="stat-label">淘汰</div>
                </div>
                <div class="supplier-stat-item">
                  <div class="stat-number">{{ supplierStats.suspension }}</div>
                  <div class="stat-label">暂缓</div>
                </div>
              </div>
            </div>
            <!-- 中间第四个：高频物资 -->
            <div class="card clickable-card" @click="goToHighFrequencyDashboard">
              <h2 class="card-title">高频物资</h2>
              <div class="high-frequency-content">
                <!-- 上半部分：高频采购物料词云 -->
                <div class="high-frequency-materials">
                  <h3 class="section-title">高频采购物料 TOP10</h3>
                  <div id="highFrequencyMaterialCloud" class="material-cloud"></div>
                </div>

                <!-- 下半部分：物料采购价格和采购量趋势图 -->
                <div class="price-trend-section">
                  <h3 class="section-title">PB块价格及采购量趋势</h3>
                  <div id="highFrequencyPriceTrendChart" class="mini-chart"></div>
                </div>
              </div>
            </div>
           
          </div>
        </div>

        <!-- 右侧面板 -->
        <div class="right-panel" :style="isFullscreen ? { width: '320px', minWidth: '320px', maxWidth: '320px', gap: '12px' } : {}">
          <!-- 右侧第一个：预警信息 -->
          <div class="card">
            <h2 class="card-title">预警信息</h2>
            <div class="warning-analysis">
              <div class="warning-item">
                <span class="warning-name">证书过期</span>
                <div class="warning-bar">
                  <div class="bar-bg">
                    <div class="bar-fill" :style="{ width: getWarningPercentage(warningInfo.certificateExpiry) + '%' }"></div>
                  </div>
                  <span class="warning-value">{{ warningInfo.certificateExpiry }}</span>
                </div>
              </div>
              <div class="warning-item">
                <span class="warning-name">合同过期</span>
                <div class="warning-bar">
                  <div class="bar-bg">
                    <div class="bar-fill" :style="{ width: getWarningPercentage(warningInfo.contractExpiry) + '%' }"></div>
                  </div>
                  <span class="warning-value">{{ warningInfo.contractExpiry }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧第二个：质量异议管理 -->
          <div class="card">
            <h2 class="card-title">质量异议管理</h2>
            <div class="simple-display">
              <div class="display-number">{{ qualityIssueCount }}</div>
              <div class="display-label">质量异议总数</div>
            </div>
          </div>

           <!-- 右侧第三个：异常管理 -->
            <div class="card">
              <h2 class="card-title">异常管理</h2>
              <div class="funnel-data">                
              <div class="funnel-item">
                <span class="funnel-label">到货完成度</span>
                <span class="funnel-value">{{ (purchaseStats.arriveRate || 0) + '%' }}</span>
              </div>
              <div class="funnel-item">
                <span class="funnel-label">采购职责不符合数量</span>
                <span class="funnel-value">9000</span>
              </div>
              <div class="funnel-item">
                <span class="funnel-label">计划被驳回数量</span>
                <span class="funnel-value">9000</span>
              </div>
            </div>
            </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import screenfull from 'screenfull'
import {
  getDashboardData,
  getPersonalConsumption,
  getPurchaseAnalysis,
  getProductAnalysis,
  getMapData,
  getRealTimeStats,
  getSalesFunnel,
  getPurchaseTrend,
  getSupplierAnalysis,
  getAmtManage
} from '@/api/purchaseDashboardMain'
import {
  showKeyIndicators,
  showHighFrequencyMaterialList,
  getPurchasePriceAndStore,
  showCokingCoalAmount
} from '@/api/purchaseDashboard/purchaseDashboard'

export default {
  name: 'PurchaseDashboardMain',
  data() {
    return {
      // 时间过滤器选项
      timeFilters: [
        { id: 'filter-3m', label: '近三个月', value: 1 },
        { id: 'filter-6m', label: '近六个月', value: 2 },
        { id: 'filter-1y', label: '近一年', value: 3 }
      ],
      activeFilter: 'filter-1y',
      currentDimensionType: 3,

      // 图表实例
      charts: {},
      // 窗口大小变化定时器
      resizeTimer: null,

      // 高频物资相关数据
      highFrequencyMaterialList: [],
      priceAndStoreData: null,
      selectedCodeType: 'ALL',
      selectedItemType: 'CLASS3',
      selectedMaterial: 'PB块',

      // 矿焦煤库存相关数据
      cokingCoalInventoryData: [],
      selectedCokingCoalType: '', // 选中的矿焦煤类型，默认为空（全部）

      // 实时数据
      realTimeStats: {
        sales: '343567',
        orders: '734245'
      },
      // 计划管理数据
      productAnalysisData: [
        { name: '计划总条数', value: '0', percentage: 0 },
        { name: '审核驳回', value: '0%', percentage: 0 },
        { name: '商务部驳回', value: '0%', percentage: 0 },
        { name: '订单至入库平均天数', value: '30000', percentage: 30 },
        { name: '入库至领用平均天数', value: '30000', percentage: 30 },
        { name: '接收至挂单平均天数', value: '40000', percentage: 40 },
        { name: '超期未入库数', value: '40000', percentage: 40 },
        { name: '超期未领用数', value: '40000', percentage: 40 }

      ],
      // 计划执行状态数据
      planExecutionData: [
        { name: '计划完成率', value: '85%' },
        { name: '在途订单数', value: '1,234' },
        { name: '待审核计划', value: '56' },
        { name: '紧急采购', value: '12' },
        // { name: '供应商响应率', value: '92%' },
        // { name: '库存周转率', value: '3.2' },
        // { name: '采购成本节约', value: '8.5%' },
        // { name: '质量合格率', value: '98.7%' }
      ],
      // 底部统计数据
      bottomStats: [
        { label: '供应商信息全景', value: '34554' },
        { label: '订单量', value: '34554' },
        { label: '客户数', value: '34554' }
      ],
      // 水位图配置
      waterLevelConfig: {
        data: [50],
        shape: 'circle',
        waveNum: 3,
        waveHeight: 40,
        waveOpacity: 0.4,
        colors: ['#00BAFF', '#3DE7C9']
      },
      // 供应商数据（周围四个圆）
      supplierData: [
        { id: 1, value: '8,092', label: '参标次数', color: '#FF6B6B', position: { top: '10%', left: '10%' } },
        { id: 2, value: '1,245', label: '中标次数', color: '#4ECDC4', position: { top: '10%', right: '10%' } },
        { id: 3, value: '89', label: '质量异议次数', color: '#45B7D1', position: { bottom: '10%', left: '10%' } },
        { id: 4, value: '156', label: '合作年限', color: '#96CEB4', position: { bottom: '10%', right: '10%' } }
      ],
      // 中心圆数据（考核情况）
      centerData: {
        label: '考核情况',
        amount: '567万',
        count: '23次',
        color: '#00BAFF'
      },
      // 质量异议总数
      qualityIssueCount: '156',
      // 供方管理统计数据
      supplierStats: {
        admission: '1,245',    // 准入
        elimination: '89',     // 淘汰
        suspension: '156'      // 暂缓
      },
      // 预警信息数据
      warningInfo: {
        certificateExpiry: 0, // 证书过期数量（供应商风险提醒）
        contractExpiry: 0     // 合同过期数量（合同到期提醒）
      },
      // 采购关键指标数据
      purchaseStats: {
        arriveRate: 0  // 到货完成度
      },
      // 资金管理数据
      fundManagement: {
        nextMonth: '0',    // 下月拟入库总金额
        twoMonthsLater: '0', // 2月后拟入库总金额
        threeMonthsLater: '0' // 3月后拟入库总金额
      },
      // 合同管理数据
      contractData: [
        { name: '原材料', count: 12095282 },
        { name: '辅耐材', count: 8340154 },
        { name: '材料类', count: 33344517 },
        { name: '通用备件', count: 76374451 },
        { name: '专用备件', count: 4353921 },
        { name: '办公', count: 23515 }
      ]
    }
  },

  computed: {
    isFullscreen() {
      return this.$store.state.app.isFullscreenMode
    }
  },

  mounted() {
    this.initFullscreenListener()
    this.initDashboard()
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize)
    // 销毁所有图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart && chart.dispose) {
        chart.dispose()
      }
    })
    // 移除全屏监听器
    this.removeFullscreenListener()
    // 确保退出全屏模式
    this.$store.dispatch('app/setFullscreenMode', false)
  },
  methods: {
    async initDashboard() {
      try {
        await this.loadData()
        this.$nextTick(() => {
          this.initCharts()
        })
      } catch (error) {
        console.error('初始化驾驶舱失败:', error)
      }
    },
    
    async loadData() {
      try {
        // 并行获取预警信息数据和资金管理数据
        await Promise.all([
          this.fetchWarningData(),
          this.fetchFundManagementData()
        ])

        // 这里可以并行加载其他数据
        // const [dashboardData, personalData, purchaseData] = await Promise.all([
        //   getDashboardData(),
        //   getPersonalConsumption(),
        //   getPurchaseAnalysis()
        // ])

        console.log('数据加载完成')
      } catch (error) {
        console.error('加载数据失败:', error)
      }
    },

    // 获取资金管理数据
    async fetchFundManagementData() {
      try {
        const response = await getAmtManage()

        if (response && response.data && Array.isArray(response.data)) {
          const data = response.data

          // 根据reserve1字段找到对应的数据
          const nextMonthData = data.find(item => item.reserve1 === '01')
          const twoMonthsData = data.find(item => item.reserve1 === '02')
          const threeMonthsData = data.find(item => item.reserve1 === '03')

          // 更新资金管理数据
          this.fundManagement.nextMonth = nextMonthData ? (nextMonthData.reserve4 || '0') : '0'
          this.fundManagement.twoMonthsLater = twoMonthsData ? (twoMonthsData.reserve4 || '0') : '0'
          this.fundManagement.threeMonthsLater = threeMonthsData ? (threeMonthsData.reserve4 || '0') : '0'

          // 更新图表
          this.updateFundManagementChart()

          console.log('资金管理数据获取成功:', this.fundManagement)
        } else {
          console.error('资金管理数据格式不正确:', response)
          this.resetFundManagementData()
        }
      } catch (error) {
        console.error('获取资金管理数据失败:', error)
        this.resetFundManagementData()
      }
    },

    // 重置资金管理数据为默认值
    resetFundManagementData() {
      this.fundManagement.nextMonth = '0'
      this.fundManagement.twoMonthsLater = '0'
      this.fundManagement.threeMonthsLater = '0'
      // 更新图表
      this.updateFundManagementChart()
    },

    // 获取预警信息和计划管理数据
    async fetchWarningData() {
      try {
        // 调用采购全景看板的关键指标接口
        const response = await showKeyIndicators({ dimensionType: this.currentDimensionType })

        if (response && response.data) {
          const data = response.data

          // 更新预警信息数据
          // 证书过期数据来自供应商风险提醒
          this.warningInfo.certificateExpiry = data.suppRiskNum || 0
          // 合同过期数据来自合同到期提醒
          this.warningInfo.contractExpiry = data.bpoExpireNum || 0

          // 更新计划管理数据
          this.updatePlanManagementData(data)

          // 更新计划执行状态数据
          this.updatePlanExecutionData(data)

          // 更新采购关键指标数据
          this.purchaseStats.arriveRate = data.arriveRate || 0

          console.log('预警信息和计划管理数据获取成功:', {
            warningInfo: this.warningInfo,
            planData: {
              planTotalNum: data.planTotalNum,
              rejectNum1: data.rejectNum1,
              rejectNum2: data.rejectNum2
            },
            purchaseStats: this.purchaseStats
          })
        } else {
          console.error('数据获取失败:', response)
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        // 使用默认值
        this.warningInfo.certificateExpiry = 0
        this.warningInfo.contractExpiry = 0
        this.purchaseStats.arriveRate = 0
        this.resetPlanManagementData()
      }
    },

    // 更新计划管理数据
    updatePlanManagementData(data) {
      // 计划总条数
      if (data.planTotalNum !== undefined) {
        this.productAnalysisData[0].value = data.planTotalNum.toString()
        this.productAnalysisData[0].percentage = Math.min(100, Math.max(0, (data.planTotalNum / 100000) * 100))
      }

      // 审核驳回（百分比）
      if (data.rejectNum1 !== undefined) {
        this.productAnalysisData[1].value = data.rejectNum1 + '%'
        this.productAnalysisData[1].percentage = Math.min(100, Math.max(0, data.rejectNum1))
      }

      // 商务部驳回（百分比）
      if (data.rejectNum2 !== undefined) {
        this.productAnalysisData[2].value = data.rejectNum2 + '%'
        this.productAnalysisData[2].percentage = Math.min(100, Math.max(0, data.rejectNum2))
      }

      // 订单至入库平均天数（绑定到reserve4字段）
      if (data.reserve4 !== undefined) {
        this.productAnalysisData[3].value = data.reserve4.toString() 
        this.productAnalysisData[3].percentage = Math.min(100, Math.max(0, (data.reserve4 / 30) * 100)) // 假设30天为100%
      }
    },

    // 重置计划管理数据为默认值
    resetPlanManagementData() {
      this.productAnalysisData[0].value = '0'
      this.productAnalysisData[0].percentage = 0
      this.productAnalysisData[1].value = '0%'
      this.productAnalysisData[1].percentage = 0
      this.productAnalysisData[2].value = '0%'
      this.productAnalysisData[2].percentage = 0
      this.productAnalysisData[3].value = '0天'
      this.productAnalysisData[3].percentage = 0
    },

    // 更新计划执行状态数据
    updatePlanExecutionData(data) {
      if (data) {
        // 计划完成率
        if (data.planCompletionRate !== undefined) {
          this.planExecutionData[0].value = data.planCompletionRate + '%'
        }
        // 在途订单数
        if (data.inTransitOrderNum !== undefined) {
          this.planExecutionData[1].value = this.formatNumber(data.inTransitOrderNum)
        }
        // 待审核计划
        if (data.pendingAuditNum !== undefined) {
          this.planExecutionData[2].value = data.pendingAuditNum.toString()
        }
        // 紧急采购
        if (data.urgentPurchaseNum !== undefined) {
          this.planExecutionData[3].value = data.urgentPurchaseNum.toString()
        }
        // 供应商响应率
        if (data.supplierResponseRate !== undefined) {
          this.planExecutionData[4].value = data.supplierResponseRate + '%'
        }
        // 库存周转率
        if (data.inventoryTurnoverRate !== undefined) {
          this.planExecutionData[5].value = data.inventoryTurnoverRate.toString()
        }
        // 采购成本节约
        if (data.costSavingRate !== undefined) {
          this.planExecutionData[6].value = data.costSavingRate + '%'
        }
        // 质量合格率
        if (data.qualityPassRate !== undefined) {
          this.planExecutionData[7].value = data.qualityPassRate + '%'
        }
        console.log('计划执行状态数据更新完成:', this.planExecutionData)
      }
    },

    // 格式化数字显示（添加千分位分隔符）
    formatNumber(num) {
      if (num === undefined || num === null) return '0'
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },

    // 获取计划管理图标
    getPlanIcon(index) {
      const icons = [
        'el-icon-document', // 计划总条数
        'el-icon-close', // 审核驳回
        'el-icon-error', // 商务部驳回
        'el-icon-time', // 订单至入库平均天数
        'el-icon-truck', // 入库至领用平均天数
        'el-icon-message', // 接收至挂单平均天数
        'el-icon-warning-outline', // 超期未入库数
        'el-icon-warning-outline' // 超期未领用数
      ]
      return icons[index] || 'el-icon-info'
    },

    // 获取计划执行状态图标
    getExecutionIcon(index) {
      const icons = [
        'el-icon-success', // 计划完成率
        'el-icon-goods', // 在途订单数
        'el-icon-edit-outline', // 待审核计划
        'el-icon-warning', // 紧急采购
        'el-icon-phone', // 供应商响应率
        'el-icon-refresh', // 库存周转率
        'el-icon-coin', // 采购成本节约
        'el-icon-circle-check' // 质量合格率
      ]
      return icons[index] || 'el-icon-info'
    },

    initCharts() {
      this.initFundManagementChart()
      this.initSupplierManagementChart()
      this.initPersonalConsumptionChart()
      this.initPurchaseAnalysisChart()
      this.initContractChart()
      this.initTrendChart()
      this.initSupplierChart()
      this.fetchHighFrequencyData()
      this.fetchCokingCoalInventoryData()
    },

    // 资金管理柱状图
    initFundManagementChart() {
      const chartDom = document.getElementById('fundManagementChart')
      if (!chartDom) return

      const chart = echarts.init(chartDom)
      this.charts.fundManagement = chart

      const option = {
        backgroundColor: 'transparent',
        grid: {
          left: '15%',
          right: '10%',
          top: '25%',
          bottom: '25%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['下月', '2月后', '3月后'],
          axisLine: {
            lineStyle: {
              color: '#00BAFF'
            }
          },
          axisLabel: {
            color: '#fff',
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          name: '拟入库金额',
          nameTextStyle: {
            color: '#fff',
            fontSize: 12,
            align: 'right',
          },
          min: 0,
          max: 8000,
          interval: 2000,
          axisLine: {
            lineStyle: {
              color: '#00BAFF'
            }
          },
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            formatter: function(value) {
              return value
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(0, 186, 255, 0.2)'
            }
          }
        },
        series: [{
          name: '拟入库金额',
          type: 'bar',
          data: [
            parseFloat(this.fundManagement.nextMonth) || 0,
            parseFloat(this.fundManagement.twoMonthsLater) || 0,
            parseFloat(this.fundManagement.threeMonthsLater) || 0
          ],
          barWidth: '50%',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: '#00BAFF' // 顶部颜色
              }, {
                offset: 1, color: '#0080CC' // 底部颜色
              }],
              global: false
            },
            borderRadius: [4, 4, 0, 0] // 顶部圆角
          },
          label: {
            show: true,
            position: 'top',
            color: '#fff',
            fontSize: 12,
            formatter: function(params) {
              return params.value
            }
          },
          emphasis: {
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: '#33C7FF' // 高亮顶部颜色
                }, {
                  offset: 1, color: '#0099DD' // 高亮底部颜色
                }],
                global: false
              }
            }
          }
        }],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00BAFF',
          textStyle: {
            color: '#fff'
          },
          formatter: function(params) {
            const param = params[0]
            return `${param.name}: ${param.value}`
          }
        }
      }

      chart.setOption(option)
    },

    // 供方管理饼图
    initSupplierManagementChart() {
      this.$nextTick(() => {
        const chartDom = document.getElementById('supplierManagementChart')
        if (!chartDom) {
          console.error('找不到供方管理图表DOM元素')
          return
        }

        const chart = echarts.init(chartDom)
        this.charts.supplierManagement = chart

        const option = {
          backgroundColor: 'transparent',
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(0,0,0,0.8)',
            borderColor: '#00BAFF',
            textStyle: { color: '#fff' }
          },
          series: [{
            type: 'pie',
            radius: '60%',
            center: ['50%', '60%'],
            data: [
              { value: 45, name: '工程' },
              { value: 35, name: '货物' },
              { value: 20, name: '服务' }
            ],
            itemStyle: {
              color: function(params) {
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
                return colors[params.dataIndex % colors.length]
              }
            },
            label: {
              color: '#fff',
              fontSize: 15
            }
          }]
        }

        chart.setOption(option)
      })
    },

    // 更新资金管理图表数据
    updateFundManagementChart() {
      if (!this.charts.fundManagement) return

      const newData = [
        parseFloat(this.fundManagement.nextMonth) || 0,
        parseFloat(this.fundManagement.twoMonthsLater) || 0,
        parseFloat(this.fundManagement.threeMonthsLater) || 0
      ]

      this.charts.fundManagement.setOption({
        series: [{
          data: newData
        }]
      })
    },

    // 库存管理图表
    initPersonalConsumptionChart() {
      const chartDom = document.getElementById('personalConsumptionChart')
      if (!chartDom) return

      const chart = echarts.init(chartDom)
      this.charts.personalConsumption = chart

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0,0,0,0.8)',
          borderColor: '#00BAFF',
          textStyle: { color: '#fff' }
        },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          data: [
            { value: 335, name: '矿料' },
            { value: 310, name: '合金' },
            { value: 234, name: '焦炭' },
            { value: 135, name: '辅料/电极' }
          ],
          itemStyle: {
            color: function(params) {
              const colors = ['#00BAFF', '#3DE7C9', '#FFC107', '#FF6B6B']
              return colors[params.dataIndex % colors.length]
            }
          },
          label: {
            color: '#fff',
            fontSize: 12
          }
        }]
      }

      chart.setOption(option)
    },

    // 供方管理图表
    initPurchaseAnalysisChart() {
      const chartDom = document.getElementById('purchaseAnalysisChart')
      if (!chartDom) return

      const chart = echarts.init(chartDom)
      this.charts.purchaseAnalysis = chart

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.8)',
          borderColor: '#00BAFF',
          textStyle: { color: '#fff' }
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
          axisLine: { lineStyle: { color: '#00BAFF' } },
          axisLabel: { color: '#fff' }
        },
        yAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#00BAFF' } },
          axisLabel: { color: '#fff' },
          splitLine: { lineStyle: { color: 'rgba(0,186,255,0.2)' } }
        },
        series: [{
          data: [120, 200, 150, 80, 70, 110],
          type: 'line',
          smooth: true,
          lineStyle: { color: '#00BAFF', width: 2 },
          itemStyle: { color: '#00BAFF' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(0,186,255,0.3)' },
                { offset: 1, color: 'rgba(0,186,255,0.1)' }
              ]
            }
          }
        }]
      }

      chart.setOption(option)
    },

    // 合同管理柱状图
    initContractChart() {
      const chartDom = document.getElementById('contractChart')
      if (!chartDom) return

      const chart = echarts.init(chartDom)
      this.charts.contract = chart

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.8)',
          borderColor: '#00BAFF',
          textStyle: { color: '#fff' },
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0]
            let value = data.value
            let formattedValue = ''
            if (value >= 10000000) {
              formattedValue = (value / 10000000).toFixed(1) + '千万'
            } else if (value >= 10000) {
              formattedValue = (value / 10000).toFixed(1) + '万'
            } else {
              formattedValue = value.toString()
            }
            return `${data.name}<br/>合同数量: ${formattedValue}`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '20%',
          top: '25%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.contractData.map(item => item.name),
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee',
            interval: 0,
            rotate: 30,
            fontSize: 10
          }
        },
        yAxis: {
          type: 'value',
          name: '合同数量',
          nameTextStyle: {
            color: '#fff',
            align: 'right'
          },
          axisLabel: {
            color: '#eee',
            formatter: function(value) {
              if (value >= 10000000) {
                return (value / 10000000).toFixed(1) + '千万'
              } else if (value >= 10000) {
                return (value / 10000).toFixed(1) + '万'
              } else {
                return value
              }
            }
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [{
          name: '合同数量',
          type: 'bar',
          data: this.contractData.map(item => item.count),
          itemStyle: {
            color: '#83bff6',
            borderRadius: [4, 4, 0, 0]            
          },
          emphasis: {
            itemStyle: {
              color: '#83bff6',
              borderRadius: [4, 4, 0, 0],
              shadowBlur: 10,
              shadowColor: 'rgba(255, 255, 255, 0.5)',
              borderWidth: 2,
              borderColor: '#fff'
            }
          }
        }]
      }

      chart.setOption(option)
    },

    // 趋势分析图表
    initTrendChart() {
      const chartDom = document.getElementById('trendChart')
      if (!chartDom) return

      const chart = echarts.init(chartDom)
      this.charts.trend = chart

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.8)',
          borderColor: '#00BAFF',
          textStyle: { color: '#fff' }
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          axisLine: { lineStyle: { color: '#00BAFF' } },
          axisLabel: { color: '#fff', fontSize: 10 }
        },
        yAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#00BAFF' } },
          axisLabel: { color: '#fff', fontSize: 10 },
          splitLine: { lineStyle: { color: 'rgba(0,186,255,0.2)' } }
        },
        series: [{
          data: [820, 932, 901, 934, 1290, 1330, 1320],
          type: 'line',
          smooth: true,
          lineStyle: { color: '#3DE7C9', width: 2 },
          itemStyle: { color: '#3DE7C9' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(61,231,201,0.3)' },
                { offset: 1, color: 'rgba(61,231,201,0.1)' }
              ]
            }
          }
        }]
      }

      chart.setOption(option)
    },

    // 单一来源图表
    initSupplierChart() {
      this.$nextTick(() => {
        const chartDom = document.getElementById('supplierChart')
        console.log('单一来源图表DOM元素:', chartDom)

        if (!chartDom) {
          console.error('找不到单一来源图表DOM元素')
          return
        }

        const chart = echarts.init(chartDom)
        this.charts.supplier = chart

        const option = {
          backgroundColor: 'transparent',
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(0,0,0,0.8)',
            borderColor: '#00BAFF',
            textStyle: { color: '#fff' }
          },
          series: [{
            type: 'pie',
            radius: '50%',
            center: ['50%', '30%'],
            data: [
              { value: 40, name: '工程' },
              { value: 30, name: '货物' },
              { value: 30, name: '服务' }
            ],
            itemStyle: {
              color: function(params) {
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
                return colors[params.dataIndex % colors.length]
              }
            },
            label: {
              color: '#fff',
              fontSize: 10
            }
          }]
        }

        chart.setOption(option)
      })
    },

    // 获取高频物资数据
    async fetchHighFrequencyData() {
      try {
        // 获取高频采购物料数据
        await this.fetchHighFrequencyMaterialData()
        // 获取价格趋势数据
        await this.fetchPriceAndStoreData()
        // 初始化词云和价格趋势图
        this.$nextTick(() => {
          this.initHighFrequencyMaterialCloud()
          this.initHighFrequencyPriceTrendChart()
        })
      } catch (error) {
        console.error('获取高频物资数据失败:', error)
      }
    },

    // 获取高频采购物料数据
    async fetchHighFrequencyMaterialData() {
      try {
        const params = {
          dimensionType: this.currentDimensionType,
          codeType: this.selectedCodeType,
          itemType: this.selectedItemType
        }

        const response = await showHighFrequencyMaterialList(params)
        if (response && response.data) {
          // 取前10条数据
          this.highFrequencyMaterialList = (response.data || []).slice(0, 10)
        } else {
          // 使用模拟数据
          this.highFrequencyMaterialList = this.getMockHighFrequencyData()
        }
      } catch (error) {
        console.error('获取高频物料数据失败:', error)
        this.highFrequencyMaterialList = this.getMockHighFrequencyData()
      }
    },

    // 初始化高频物料词云
    initHighFrequencyMaterialCloud() {
      const chartDom = document.getElementById('highFrequencyMaterialCloud')
      if (!chartDom) {
        console.error('找不到高频物料词云DOM元素')
        return
      }

      chartDom.innerHTML = ''

      const rawMaterialList = this.highFrequencyMaterialList
      if (!rawMaterialList || rawMaterialList.length === 0) {
        chartDom.innerHTML = '<div class="chart-placeholder">无高频采购物料数据</div>'
        return
      }

      // 按入库金额排序，取前15条
      const highFrequencyMaterials = rawMaterialList
        .sort((a, b) => (b.inAmt || 0) - (a.inAmt || 0))
        .slice(0, 15)

      const container = document.createElement('div')
      container.style.width = '100%'
      container.style.height = '100%'
      container.style.position = 'relative'
      container.style.overflow = 'hidden'

      const colors = [
        '#4fc3f7', '#a5d6a7', '#ffcc80', '#ef9a9a', '#ce93d8',
        '#90caf9', '#80deea', '#c5e1a5', '#fff59d', '#ffab91'
      ]

      const maxFontSize = 24
      const minFontSize = 12

      highFrequencyMaterials.forEach((item, index) => {
        let fontSize = maxFontSize - (index * 1.2)
        if (fontSize < minFontSize) {
          fontSize = minFontSize
        }

        const div = document.createElement('div')
        div.textContent = item.itemName
        div.style.position = 'absolute'
        div.style.fontSize = `${fontSize}px`
        div.style.fontWeight = 'bold'
        div.style.color = colors[index % colors.length]
        div.style.transform = `rotate(${Math.random() * 30 - 15}deg)`
        div.style.left = `${10 + Math.random() * 60}%`
        div.style.top = `${10 + Math.random() * 60}%`
        div.style.whiteSpace = 'nowrap'
        div.style.textShadow = '1px 1px 2px rgba(0,0,0,0.3)'
        div.style.transition = 'all 0.3s ease'
        div.style.cursor = 'pointer'
        div.style.zIndex = highFrequencyMaterials.length - index

        // 添加悬停效果
        const self = this
        div.addEventListener('mouseenter', function() {
          this.style.transform = `rotate(${Math.random() * 30 - 15}deg) scale(1.1)`
          this.style.zIndex = '999'
          this.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)'

          // 显示金额提示
          const tooltip = document.createElement('div')
          tooltip.className = 'material-tooltip'
          tooltip.textContent = `金额: ${self.formatAmount(item.inAmt)}`
          tooltip.style.position = 'absolute'
          tooltip.style.bottom = '-25px'
          tooltip.style.left = '50%'
          tooltip.style.transform = 'translateX(-50%)'
          tooltip.style.background = 'rgba(0,0,0,0.8)'
          tooltip.style.color = '#fff'
          tooltip.style.padding = '2px 6px'
          tooltip.style.borderRadius = '3px'
          tooltip.style.fontSize = '10px'
          tooltip.style.whiteSpace = 'nowrap'
          tooltip.style.zIndex = '1000'
          this.appendChild(tooltip)
        })

        div.addEventListener('mouseleave', function() {
          this.style.transform = `rotate(${Math.random() * 30 - 15}deg) scale(1)`
          this.style.zIndex = (highFrequencyMaterials.length - index).toString()
          this.style.textShadow = '1px 1px 2px rgba(0,0,0,0.3)'

          const tooltip = this.querySelector('.material-tooltip')
          if (tooltip) {
            this.removeChild(tooltip)
          }
        })

        container.appendChild(div)
      })

      chartDom.appendChild(container)
    },

    // 获取模拟高频物料数据
    getMockHighFrequencyData() {
      return [
        { itemName: '粗粉', inAmt: 392467.2, inNum: 5421293 },
        { itemName: '精粉', inAmt: 280350.5, inNum: 4250180 },
        { itemName: '球团', inAmt: 195200.8, inNum: 3180970 },
        { itemName: '烧结', inAmt: 150420.3, inNum: 2890540 },
        { itemName: '焦炭', inAmt: 125680.7, inNum: 2350210 },
        { itemName: '煤炭', inAmt: 98750.2, inNum: 1980760 },
        { itemName: 'PB块', inAmt: 85420.1, inNum: 1650430 },
        { itemName: '铁矿石', inAmt: 72350.8, inNum: 1420890 },
        { itemName: '废钢', inAmt: 65280.4, inNum: 1280560 },
        { itemName: '石灰石', inAmt: 58190.6, inNum: 1150320 },
        { itemName: '合金', inAmt: 52180.3, inNum: 980450 },
        { itemName: '电极', inAmt: 48750.9, inNum: 850320 },
        { itemName: '耐火材料', inAmt: 42350.7, inNum: 720180 },
        { itemName: '化工原料', inAmt: 38920.5, inNum: 650290 },
        { itemName: '辅料', inAmt: 35680.2, inNum: 580160 }
      ]
    },

    // 获取物料价格和采购量数据
    async fetchPriceAndStoreData() {
      try {
        const params = {
          dimensionType: this.currentDimensionType,
          itemName: 'PB块'  // 固定获取PB块数据
        }

        const response = await getPurchasePriceAndStore(params)
        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
          this.priceAndStoreData = response.data[0]
          console.log('获取到PB块价格和采购量数据:', this.priceAndStoreData)
        } else {
          console.log('未获取到真实数据，使用模拟数据')
          this.priceAndStoreData = this.getMockPriceAndStoreData()
        }
      } catch (error) {
        console.error('获取价格和采购量数据失败:', error)
        this.priceAndStoreData = this.getMockPriceAndStoreData()
      }
    },

    // 获取模拟的价格和采购量数据（参考采购看板数据结构）
    getMockPriceAndStoreData() {
      return {
        procurementPriceVoList: [
          {
            priceName: 'PB块价格',
            priceList: [
              { recordDate: '20240801', price: 850.0 },
              { recordDate: '20240815', price: 870.5 },
              { recordDate: '20240901', price: 890.2 },
              { recordDate: '20240915', price: 875.8 },
              { recordDate: '20241001', price: 920.3 },
              { recordDate: '20241015', price: 905.7 },
              { recordDate: '20241101', price: 880.4 },
              { recordDate: '20241115', price: 895.6 },
              { recordDate: '20241123', price: 910.2 },  // 2024年11月23日价格
              { recordDate: '20241201', price: 925.8 },
              { recordDate: '20241215', price: 940.1 },
              { recordDate: '20250101', price: 930.5 },
              { recordDate: '20250115', price: 915.3 },
              { recordDate: '20250201', price: 900.7 },
              { recordDate: '20250215', price: 885.9 },
              { recordDate: '20250301', price: 870.2 },
              { recordDate: '20250315', price: 855.8 },
              { recordDate: '20250401', price: 840.6 },
              { recordDate: '20250415', price: 825.4 },
              { recordDate: '20250501', price: 810.9 },
              { recordDate: '20250515', price: 795.7 },
              { recordDate: '20250601', price: 780.3 }
            ]
          }
        ],
        procurementPurchaseAmountVoList: [
          {
            amountName: 'PB块采购量',
            amountList: [
              { recordDate: '20240801', amount: 125000 },   // 12.5万吨
              { recordDate: '20240815', amount: 118000 },   // 11.8万吨
              { recordDate: '20240901', amount: 132000 },   // 13.2万吨
              { recordDate: '20240915', amount: 145000 },   // 14.5万吨
              { recordDate: '20241001', amount: 138000 },   // 13.8万吨
              { recordDate: '20241015', amount: 152000 },   // 15.2万吨
              { recordDate: '20241101', amount: 168000 },   // 16.8万吨
              { recordDate: '20241115', amount: 175000 },   // 17.5万吨
              { recordDate: '20241123', amount: 100000 },   // 10万吨
              { recordDate: '20241201', amount: 185000 },   // 18.5万吨
              { recordDate: '20241215', amount: 192000 },   // 19.2万吨
              { recordDate: '20250101', amount: 178000 },   // 17.8万吨
              { recordDate: '20250115', amount: 165000 },   // 16.5万吨
              { recordDate: '20250201', amount: 158000 },   // 15.8万吨
              { recordDate: '20250215', amount: 142000 },   // 14.2万吨
              { recordDate: '20250301', amount: 135000 },   // 13.5万吨
              { recordDate: '20250315', amount: 128000 },   // 12.8万吨
              { recordDate: '20250401', amount: 121000 },   // 12.1万吨
              { recordDate: '20250415', amount: 115000 },   // 11.5万吨
              { recordDate: '20250501', amount: 108000 },   // 10.8万吨
              { recordDate: '20250515', amount: 102000 },   // 10.2万吨
              { recordDate: '20250601', amount: 95000 }     // 9.5万吨
            ]
          }
        ]
      }
    },

    // 初始化高频物资价格趋势图（完全按照图片效果重写）
    initHighFrequencyPriceTrendChart() {
      this.$nextTick(() => {
        const chartDom = document.getElementById('highFrequencyPriceTrendChart')
        if (!chartDom) {
          console.error('找不到高频物资价格趋势图DOM元素')
          return
        }

        // 清理现有实例
        if (this.charts.highFrequencyPriceTrend) {
          this.charts.highFrequencyPriceTrend.dispose()
        }

        const chart = echarts.init(chartDom)
        this.charts.highFrequencyPriceTrend = chart

        // 使用真实数据结构
        const priceAndStoreData = this.priceAndStoreData
        if (!priceAndStoreData) {
          chartDom.innerHTML = '<div class="chart-placeholder">暂无价格趋势数据</div>'
          return
        }

        // 收集所有日期
        let allDates = new Set()

        // 从价格数据中收集日期
        if (priceAndStoreData.procurementPriceVoList) {
          priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {
            if (priceGroup.priceList) {
              priceGroup.priceList.forEach(item => {
                allDates.add(item.recordDate)
              })
            }
          })
        }

        // 从采购量数据中收集日期
        if (priceAndStoreData.procurementPurchaseAmountVoList) {
          priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {
            if (amountGroup.amountList) {
              amountGroup.amountList.forEach(item => {
                allDates.add(item.recordDate)
              })
            }
          })
        }

        // 转换为排序的数组
        allDates = Array.from(allDates).sort()

        if (allDates.length === 0) {
          chartDom.innerHTML = '<div class="chart-placeholder">暂无价格趋势数据</div>'
          return
        }

        // 构建系列数据
        const series = []
        const legendData = []

        // 构建价格系列（蓝色线）
        if (priceAndStoreData.procurementPriceVoList) {
          priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {
            const priceData = allDates.map(date => {
              const found = priceGroup.priceList.find(item => item.recordDate === date)
              return found ? parseFloat(found.price) : null
            })

            series.push({
              name: priceGroup.priceName,
              type: 'line',
              yAxisIndex: 0,
              data: priceData,
              smooth: true,
              lineStyle: {
                width: 2,
                color: '#00d4ff'  // 蓝色
              },
              itemStyle: {
                color: '#00d4ff'
              },
              symbol: 'circle',
              symbolSize: 4,
              connectNulls: true
            })

            legendData.push(priceGroup.priceName)
          })
        }

        // 构建采购量系列（橙色线）
        if (priceAndStoreData.procurementPurchaseAmountVoList) {
          priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {
            const amountData = allDates.map(date => {
              const found = amountGroup.amountList.find(item => item.recordDate === date)
              return found ? parseFloat(found.amount) : null
            })

            series.push({
              name: amountGroup.amountName,
              type: 'line',
              yAxisIndex: 1,
              data: amountData,
              smooth: true,
              lineStyle: {
                width: 2,
                color: '#ff9f7f'  // 橙色
              },
              itemStyle: {
                color: '#ff9f7f'
              },
              symbol: 'circle',
              symbolSize: 4,
              connectNulls: true
            })

            legendData.push(amountGroup.amountName)
          })
        }

        // 计算Y轴范围
        let priceMin, priceMax

        // 计算价格轴范围（左轴）
        const priceValues = series.filter(s => s.yAxisIndex === 0)
          .flatMap(s => s.data.filter(v => v !== null && v !== undefined))
        if (priceValues.length > 0) {
          priceMin = Math.min(...priceValues)
          priceMax = Math.max(...priceValues)
        }

        // 采购量轴范围固定为5万-20万

        const option = {
          backgroundColor: 'transparent',
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            },
            backgroundColor: 'rgba(0,0,0,0.8)',
            borderColor: '#00d4ff',
            borderWidth: 1,
            textStyle: {
              color: '#fff'
            },
            formatter: function(params) {
              let str = params[0].axisValueLabel + '<br/>'
              params.forEach(item => {
                if (item.value !== null && item.value !== undefined) {
                  if (item.seriesName.includes('价格') || item.seriesName.includes('价')) {
                    str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`
                  } else {
                    // 采购量显示为万吨
                    const valueInWan = (parseFloat(item.value) / 10000).toFixed(1)
                    str += `${item.marker}${item.seriesName}: ${valueInWan} 万吨<br/>`
                  }
                } else {
                  str += `${item.marker}${item.seriesName}: -<br/>`
                }
              })
              return str
            }
          },
          legend: {
            data: legendData,
            textStyle: {
              color: '#fff',
              fontSize: 12
            },
            top: '0%',
            right: '10%',
            itemGap: 5,
            orient: 'horizontal'
          },
          grid: {
            left: '12%',
            right: '12%',
            bottom: '8%',
            top: '32%',
            containLabel: false
          },
          xAxis: {
            type: 'category',
            data: allDates.map(date => {
              const year = date.substring(0, 4)
              const month = parseInt(date.substring(4, 6))
              return `${year}.${month}`
            }),
            axisLabel: {
              color: '#fff',
              fontSize: 11,
              interval: 'auto'
            },
            axisLine: {
              lineStyle: {
                color: '#666'
              }
            },
            axisTick: {
              show: false
            }
          },
          yAxis: [
            {
              type: 'value',
              name: '价格(元/吨)',
              nameLocation: 'middle',
              nameGap: 35,
              nameRotate: 90,
              nameTextStyle: {
                color: '#fff',
                fontSize: 11,
                fontWeight: 'normal'
              },
              position: 'left',
              min: priceMin,
              max: priceMax,
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#666'
                }
              },
              axisLabel: {
                color: '#fff',
                fontSize: 10
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgba(255,255,255,0.1)',
                  type: 'dashed'
                }
              },
              axisTick: {
                show: false
              }
            },
            {
              type: 'value',
              name: '采购量(万吨)',
              nameLocation: 'middle',
              nameGap: 35,
              nameRotate: -90,
              nameTextStyle: {
                color: '#fff',
                fontSize: 11,
                fontWeight: 'normal'
              },
              position: 'right',
              min: 50000,  // 5万
              max: 200000, // 20万
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#666'
                }
              },
              axisLabel: {
                color: '#fff',
                fontSize: 10,
                formatter: function(value) {
                  return (value / 10000).toFixed(0)
                }
              },
              splitLine: {
                show: false
              },
              axisTick: {
                show: false
              }
            }
          ],
          series: series
        }

        chart.setOption(option, true)
      })
    },

    // 格式化金额显示
    formatAmount(amount) {
      if (amount >= 10000) {
        return (amount / 10000).toFixed(1) + '万'
      }
      return amount.toFixed(1)
    },

    // 处理窗口大小变化
    handleResize() {
      // 延迟执行，避免频繁触发
      clearTimeout(this.resizeTimer)
      this.resizeTimer = setTimeout(() => {
        Object.values(this.charts).forEach(chart => {
          if (chart && chart.resize) {
            chart.resize()
          }
        })
      }, 300)
    },

    // 初始化全屏监听器
    initFullscreenListener() {
      if (screenfull && screenfull.isEnabled) {
        screenfull.on('change', this.handleFullscreenChange)
      }
    },

    // 移除全屏监听器
    removeFullscreenListener() {
      if (screenfull && screenfull.isEnabled) {
        screenfull.off('change', this.handleFullscreenChange)
      }
    },

    // 处理全屏状态变化
    handleFullscreenChange() {
      if (screenfull && screenfull.isEnabled) {
        const isFullscreen = screenfull.isFullscreen
        this.$store.dispatch('app/setFullscreenMode', isFullscreen)

        console.log('全屏状态变化:', isFullscreen) // 调试信息
        console.log('Store状态:', this.$store.state.app.isFullscreenMode) // 调试Store状态

        // 全屏状态变化后，重新调整图表大小
        this.$nextTick(() => {
          setTimeout(() => {
            this.handleResize()
          }, 300) // 给布局变化一些时间
        })
      }
    },

    // 切换全屏
    toggleFullscreen() {
      if (screenfull && screenfull.isEnabled) {
        screenfull.toggle()
      } else {
        this.$message({
          message: '您的浏览器不支持全屏功能',
          type: 'warning'
        })
      }
    },

    // 时间过滤器变化处理
    handleTimeFilterChange(filterId, dimensionType) {
      this.activeFilter = filterId
      this.currentDimensionType = dimensionType
      console.log('选择的时间范围:', filterId, '维度:', dimensionType)

      // 根据时间范围重新加载数据
      this.fetchWarningData()
      this.fetchFundManagementData()
    },

    // 跳转到供应商处罚页面
    goToSupplierPenalty() {
      // 在新窗口中打开供应商处罚页面
      const routeUrl = this.$router.resolve('/purchase/suppPunishment')
      window.open(routeUrl.href, '_blank')
    },

    // 跳转到采购库存看板
    goToStockDashboard() {
      console.log('跳转到采购库存看板')
      // 在新窗口中打开采购库存看板页面
      const routeUrl = this.$router.resolve('/purchaseDashboardStock')
      window.open(routeUrl.href, '_blank')
    },

    // 跳转到高频物料看板
    goToHighFrequencyDashboard() {
      console.log('跳转到高频物料看板')
      // 在新窗口中打开高频物料看板页面
      const routeUrl = this.$router.resolve('/purchaseDashboardPrice')
      window.open(routeUrl.href, '_blank')
    },

    // 跳转到采购计划看板
    goToPlanDashboard() {
      console.log('跳转到采购计划看板')
      // 在新窗口中打开采购计划看板页面
      const routeUrl = this.$router.resolve('/purchaseDashboardPlan')
      window.open(routeUrl.href, '_blank')
    },

    // 获取矿焦煤库存数据
    async fetchCokingCoalInventoryData() {
      try {
        const response = await showCokingCoalAmount()
        console.log('fetchCokingCoalInventoryData - 完整响应:', response)

        if (response && response.data) {
          this.cokingCoalInventoryData = response.data || []
          console.log('fetchCokingCoalInventoryData - 设置的数据:', this.cokingCoalInventoryData)
        } else {
          console.error('获取矿焦煤库存数据失败', response)
          this.cokingCoalInventoryData = []
        }
      } catch (error) {
        console.error('获取矿焦煤库存数据失败:', error)
        this.cokingCoalInventoryData = []
      }

      // 数据获取完成后重新初始化图表
      this.$nextTick(() => {
        this.initCokingCoalLineChart()
      })
    },

    // 计算矿焦煤库存总量
    calculateCokingCoalTotal() {
      let total = 0
      if (this.cokingCoalInventoryData && this.cokingCoalInventoryData.length > 0) {
        // 找到最新日期
        let latestDate = ''
        this.cokingCoalInventoryData.forEach(item => {
          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {
            item.purchaseCokingDailyDetailList.forEach(detail => {
              if (detail.instockDate > latestDate) {
                latestDate = detail.instockDate
              }
            })
          }
        })

        // 计算最新日期各个物料的库存量合计
        this.cokingCoalInventoryData.forEach(item => {
          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {
            const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)
            if (latestDetail) {
              total += parseFloat(latestDetail.invQty) || 0
            }
          }
        })
      }
      return (total / 10000).toFixed(2) // 转换为万吨
    },

    // 处理矿焦煤类型下拉框变化
    async handleCokingCoalTypeChange() {
      console.log('矿焦煤类型变化:', this.selectedCokingCoalType)
      // 重新初始化图表以应用过滤
      this.$nextTick(() => {
        this.initCokingCoalLineChart()
      })
    },

    // 获取矿焦煤物料类型的颜色映射
    getCokingCoalMaterialColorMap() {
      // 使用与库存看板一致的颜色方案
      const baseColors = ['#0066ff', '#00ff00', '#ff0000', '#8b00ff', '#ffff00', '#ffffff']

      // 基于所有原始数据为每个物料类型分配固定颜色，确保过滤时颜色保持一致
      const allMaterialTypes = []
      const inventoryData = this.cokingCoalInventoryData || []

      // 收集所有物料类型
      inventoryData.forEach(item => {
        const materialName = item.class2Name || '未知物料'
        if (!allMaterialTypes.includes(materialName)) {
          allMaterialTypes.push(materialName)
        }
      })

      // 按字母顺序排序，确保颜色分配的一致性
      allMaterialTypes.sort()

      // 为每个物料类型分配固定颜色
      const colorMap = {}
      allMaterialTypes.forEach((materialName, index) => {
        colorMap[materialName] = baseColors[index % baseColors.length]
      })

      return colorMap
    },

    // 初始化矿焦煤库存折线图
    initCokingCoalLineChart() {
      const chartDom = document.getElementById('cokingCoalLineChart')
      if (!chartDom) {
        console.error('找不到矿焦煤折线图DOM: cokingCoalLineChart')
        return
      }

      // 清理现有实例
      if (this.charts.cokingCoalLineChart) {
        this.charts.cokingCoalLineChart.dispose()
      }

      const myChart = echarts.init(chartDom)
      this.charts.cokingCoalLineChart = myChart

      const inventoryData = this.cokingCoalInventoryData || []

      if (!inventoryData || inventoryData.length === 0) {
        const option = {
          backgroundColor: 'transparent',
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'middle',
            textStyle: {
              color: '#fff',
              fontSize: 16
            }
          }
        }
        myChart.setOption(option)
        return
      }

      // 根据选中的类型过滤数据（使用与库存看板一致的过滤逻辑）
      let filteredData = inventoryData
      if (this.selectedCokingCoalType && this.selectedCokingCoalType !== '') {
        filteredData = inventoryData.filter(item => {
          return item.class2Name === this.selectedCokingCoalType ||
                 item.class2Name.includes(this.selectedCokingCoalType) ||
                 this.selectedCokingCoalType.includes(item.class2Name)
        })
      }

      // 提取所有日期并排序
      const allDates = new Set()
      filteredData.forEach(item => {
        if (item.purchaseCokingDailyDetailList) {
          item.purchaseCokingDailyDetailList.forEach(detail => {
            allDates.add(detail.instockDate)
          })
        }
      })

      const sortedDates = Array.from(allDates).sort()

      // 格式化日期显示（从yyyyMMdd转换为MM-dd）
      const formattedDates = sortedDates.map(dateStr => {
        if (dateStr && dateStr.length === 8) {
          const month = dateStr.substring(4, 6)
          const day = dateStr.substring(6, 8)
          return `${month}-${day}`
        }
        return dateStr
      })

      // 构建每个类型的曲线数据
      const seriesData = []
      const legendData = []

      // 获取统一的颜色映射
      const colorMap = this.getCokingCoalMaterialColorMap()

      filteredData.forEach((item, index) => {
        const typeName = item.class2Name || '未知物料'

        // 为每个日期构建数据点
        const lineData = sortedDates.map(date => {
          const detail = item.purchaseCokingDailyDetailList?.find(d => d.instockDate === date)
          return detail ? parseFloat(detail.invQty) || 0 : null
        })

        // 使用统一的颜色映射
        const materialColor = colorMap[typeName] || '#83bff6'

        const seriesItem = {
          name: typeName,
          type: 'line',
          data: lineData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: materialColor
          },
          itemStyle: {
            color: materialColor
          },
          connectNulls: false
        }

        seriesData.push(seriesItem)
        legendData.push(typeName)
      })

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          },
          backgroundColor: 'rgba(0,0,0,0.8)',
          borderColor: '#00d4ff',
          borderWidth: 1,
          textStyle: {
            color: '#fff'
          },
          formatter: function(params) {
            let tooltipText = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.value !== null && param.value !== undefined) {
                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 吨<br/>`
              }
            })
            return tooltipText
          }
        },
        legend: {
          data: legendData,
          textStyle: {
            color: '#fff'
          },
          bottom: '5%',
          left: 'center'
        },
        grid: {
          left: '8%',
          right: '5%',
          bottom: '25%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: formattedDates,
          axisLine: {
            lineStyle: {
              color: '#00d4ff'
            }
          },
          axisLabel: {
            color: '#fff'
          }
        },
        yAxis: {
          type: 'value',
          name: '库存量(吨)',
          nameTextStyle: {
            color: '#fff',
            align:'right',
            fontSize: 9,
          },
          axisLine: {
            lineStyle: {
              color: '#00d4ff'
            }
          },
          axisLabel: {
            color: '#fff'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(0, 212, 255, 0.2)'
            }
          }
        },
        series: seriesData
      }

      myChart.setOption(option)
    },

    // 计算预警信息的百分比
    getWarningPercentage(value) {
      const numValue = parseInt(value) || 0

      // 如果值为0，返回0%
      if (numValue === 0) {
        return 0
      }

      // 获取两个预警值中的最大值作为基准
      const certificateValue = parseInt(this.warningInfo.certificateExpiry) || 0
      const contractValue = parseInt(this.warningInfo.contractExpiry) || 0
      const maxValue = Math.max(certificateValue, contractValue, 1) // 至少为1，避免除0

      // 计算相对百分比，确保最大值显示为100%
      const percentage = (numValue / maxValue) * 100
      return Math.min(100, Math.max(0, percentage))
    }
  }
}
</script>

<style lang="scss" scoped>
.purchase-dashboard-main {
  width: 100%;
  min-height: 100vh;

  .dashboard-container {
    width: 100%;
    min-height: 100vh;
    background: linear-gradient(135deg, #191970, #4B0082, #800080);
    color: #fff;
    overflow-x: hidden;
    padding: 10px;
  }

  .dashboard-header {
    text-align: center;
    margin-bottom: 10px;
    position: relative;
    padding: 5px 0;

    h1 {
      font-size: 24px;
      position: relative;
      display: inline-block;
      padding: 5px 40px;
      margin: 0;
      color: #fff;
    }

    &::before,
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      width: 30%;
      height: 2px;
      background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);
    }

    &::before {
      left: 0;
    }

    &::after {
      right: 0;
    }
  }

  .header-controls {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 1000;
  }

  .fullscreen-btn {
    padding: 8px 12px;
    border: none;
    background-color: rgba(33, 10, 56, 0.7);
    color: #eee;
    border-radius: 20px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 212, 255, 0.2);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 32px;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
      background-color: rgba(0, 212, 255, 0.2);
      border-color: rgba(0, 212, 255, 0.7);
      color: #00ffff;
    }
  }

  .time-filter {
    display: flex;
    gap: 10px;
  }

  .time-filter-btn {
    padding: 6px 12px;
    border: none;
    background-color: rgba(33, 10, 56, 0.7);
    color: #eee;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 212, 255, 0.2);
    position: relative;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
    }

    &.active {
      background-color: rgba(0, 212, 255, 0.2);
      border-color: rgba(0, 212, 255, 0.7);
      color: #00ffff;
      font-weight: 500;
    }
  }

  .dashboard-content {
    display: flex;
    height: calc(100vh - 80px);
    gap: 10px;
  }

  .left-panel,
  .right-panel {
    flex: 0 0 320px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .center-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px; /* 缩短行间距从10px到5px */
  }

  .center-row {
    flex: 1;
    display: flex;
    gap: 5px; /* 缩短卡片间距从10px到5px */
  }

  .center-row .card {
    flex: 1;
  }

  /* 第一行特定样式 - 缩短高度 */
  .center-row-first {
    flex: 1; /* 减小第一行的高度比例 */
    max-height: 250px; /* 进一步限制第一行的最大高度 */
  }

  /* 第二行特定样式 - 缩短高度 */
  .center-row-second {
    flex: 0.7; /* 进一步减小第二行的高度比例 */
    max-height: 330px; /* 进一步限制第二行的最大高度 */
  }

  /* 全屏模式下的样式调整 - 使用更高优先级的选择器 */
  .purchase-dashboard-main.fullscreen-mode {
    /* 调试样式 - 全屏时改变背景色 */
    background: linear-gradient(135deg, #2a2a90, #5B1082, #900090) !important;
  }

  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-first {
    max-height: none !important; /* 全屏时移除第一行高度限制 */
    flex: 1 !important; /* 确保flex比例正确 */
  }

  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-second {
    max-height: none !important; /* 全屏时移除第二行高度限制 */
    flex: 1 !important; /* 确保flex比例正确 */
  }

  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-full .card {
    max-height: none !important; /* 全屏时移除全宽行高度限制 */
    min-height: 60px !important; /* 保持最小高度 */
  }

  /* 全屏模式下调整整体容器高度和布局 */
  .purchase-dashboard-main.fullscreen-mode .dashboard-content {
    height: calc(100vh - 60px) !important; /* 全屏时减去标题高度 */
    min-height: calc(100vh - 60px) !important;
    display: flex !important;
    gap: 10px !important;
    width: 100% !important;
    justify-content: center !important; /* 居中对齐 */
    align-items: stretch !important;
    overflow-x: auto !important; /* 允许水平滚动以防内容过宽 */
    padding: 0 10px !important; /* 添加一些内边距 */
    box-sizing: border-box !important;
  }

  /* 新增：全宽行样式 */
  .center-row-full {
    width: 100%;
    margin: 2px 0; /* 缩短上下边距从5px到2px */
    flex-shrink: 0; /* 防止被压缩 */
  }

  .center-row-full .card {
    width: 100%;
    min-height: 50px; /* 设置最小高度 */
    max-height: 80px; /* 设置最大高度，确保不占用太多空间 */
  }

  .left-panel .card,
  .right-panel .card {
    flex: 1;
  }

  /* 全屏模式下右侧面板的特殊样式 - 解决高度被过度拉伸的问题 */
  .purchase-dashboard-main.fullscreen-mode .right-panel {
    /* 改变右侧面板的布局方式，平均分配空间而不是拉伸 */
    justify-content: space-between !important;
    align-items: stretch !important;
    width: 320px !important; /* 固定右侧面板宽度 */
    min-width: 320px !important;
    max-width: 320px !important;
    flex: none !important;
    box-sizing: border-box !important;
  }

  .purchase-dashboard-main.fullscreen-mode .right-panel .card {
    flex: 0 0 calc(33.33% - 8px) !important; /* 三等分，减去间距 */
    height: auto !important; /* 让内容决定高度 */
    min-height: 120px !important; /* 设置更小的最小高度 */
    max-height: 200px !important; /* 限制最大高度，更加紧凑 */
    overflow-y: auto !important; /* 内容过多时滚动 */
  }

  .purchase-dashboard-main.fullscreen-mode .left-panel {
    justify-content: space-between !important;
    width: 320px !important; /* 固定左侧面板宽度 */
    min-width: 320px !important;
    max-width: 320px !important;
    flex: none !important;
    box-sizing: border-box !important;
  }

  .purchase-dashboard-main.fullscreen-mode .center-panel {
    flex: 1 !important; /* 中间面板占用剩余空间 */
    min-width: 400px !important; /* 最小宽度保证内容显示 */
    box-sizing: border-box !important;
  }



  .purchase-dashboard-main.fullscreen-mode .left-panel .card {
    flex: 0 0 calc(50% - 6px) !important; /* 二等分，减去间距 */
    height: auto !important;
    min-height: 140px !important;
    max-height: 260px !important;
    overflow-y: auto !important;
  }

  /* 全屏模式下优化具体内容的显示 - 缩小内容 */
  .purchase-dashboard-main.fullscreen-mode .card-title {
    font-size: 14px !important;
    margin-bottom: 8px !important;
    padding: 8px 12px !important;
  }

  .purchase-dashboard-main.fullscreen-mode .warning-analysis {
    padding: 6px 12px !important;
  }

  .purchase-dashboard-main.fullscreen-mode .warning-item {
    margin-bottom: 6px !important;
    padding: 4px 0 !important;
  }

  .purchase-dashboard-main.fullscreen-mode .warning-name {
    font-size: 12px !important;
  }

  .purchase-dashboard-main.fullscreen-mode .warning-value {
    font-size: 11px !important;
  }

  .purchase-dashboard-main.fullscreen-mode .simple-display {
    padding: 10px 12px !important;
    text-align: center !important;
  }

  .purchase-dashboard-main.fullscreen-mode .display-number {
    font-size: 24px !important;
    margin-bottom: 4px !important;
  }

  .purchase-dashboard-main.fullscreen-mode .display-label {
    font-size: 11px !important;
  }

  .purchase-dashboard-main.fullscreen-mode .funnel-data {
    padding: 6px 12px !important;
  }

  .purchase-dashboard-main.fullscreen-mode .funnel-item {
    margin-bottom: 4px !important;
    padding: 3px 0 !important;
  }

  .purchase-dashboard-main.fullscreen-mode .funnel-label {
    font-size: 11px !important;
  }

  .purchase-dashboard-main.fullscreen-mode .funnel-value {
    font-size: 12px !important;
  }

  /* 全屏模式下缩小警告条的高度 */
  .purchase-dashboard-main.fullscreen-mode .warning-bar {
    height: 16px !important;
    margin-left: 8px !important;
  }

  .purchase-dashboard-main.fullscreen-mode .bar-bg {
    height: 16px !important;
  }

  .purchase-dashboard-main.fullscreen-mode .bar-fill {
    height: 16px !important;
  }

  /* 全屏模式下调整卡片内边距 */
  .purchase-dashboard-main.fullscreen-mode .right-panel .card {
    padding: 8px !important;
  }

  .purchase-dashboard-main.fullscreen-mode .left-panel .card {
    padding: 8px !important;
  }

  .card {
    background-color: rgba(33, 10, 56, 0.7);
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    position: relative;

    // 计划管理简化样式
    &.plan-management-card {
      .plan-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4px; /* 缩短网格间距从8px到4px */
        padding: 5px 0; /* 缩短上下内边距从10px到5px */

        .plan-item {
          display: flex;
          align-items: center;
          padding: 6px 35px; /* 缩短内边距从8px到4px 6px */
          border-radius: 6px;
          transition: background 0.2s ease;

          &:hover {
            background: rgba(0, 186, 255, 0.1);
          }

          .plan-icon {
            font-size: 16px;
            color: #00BAFF;
            margin-right: 6px; /* 缩短右边距从8px到6px */
            width: 18px; /* 缩短宽度从20px到18px */
            text-align: center;
          }

          .plan-text {
            flex: 1;
            min-width: 0;

            .plan-value {
              color: #fff;
              font-size: 14px;
              font-weight: bold;
              line-height: 1.1; /* 缩短行高从1.2到1.1 */
              margin-bottom: 1px; /* 缩短下边距从2px到1px */
            }

            .plan-label {
              color: rgba(255, 255, 255, 0.8);
              font-size: 11px;
              font-weight: bold;
              line-height: 1.1; /* 缩短行高从1.2到1.1 */
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }

    // 计划执行状态样式
    &.plan-execution-card {
      .plan-execution-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
        padding: 2px 0;

        .execution-item {
          display: flex;
          align-items: center;
          padding: 10px;
          border-radius: 8px;
          background: rgba(0, 186, 255, 0.1);
          border: 1px solid rgba(0, 186, 255, 0.3);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(0, 186, 255, 0.2);
            border-color: rgba(0, 186, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 186, 255, 0.3);
          }

          .execution-icon {
            font-size: 18px;
            color: #00BAFF;
            margin-right: 10px;
            width: 22px;
            text-align: center;
            flex-shrink: 0;
          }

          .execution-text {
            flex: 1;
            min-width: 0;

            .execution-value {
              color: #fff;
              font-size: 16px;
              font-weight: bold;
              line-height: 1.2;
              margin-bottom: 3px;
            }

            .execution-label {
              color: rgba(255, 255, 255, 0.8);
              font-size: 12px;
              line-height: 1.2;
              font-weight: bold;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }

    overflow: hidden; // 恢复hidden，防止重叠
    display: flex;
    flex-direction: column;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);
    }
  }

  .clickable-card {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 20px rgba(0, 212, 255, 0.3);
      background-color: rgba(33, 10, 56, 0.9);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .card-title {
    font-size: 14px;
    margin-bottom: 5px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;
  }

  .inventory-total {
    font-size: 12px;
    color: #00d4ff;
    font-weight: normal;
    background: rgba(0, 212, 255, 0.1);
    padding: 2px 8px;
    border-radius: 4px;
    border: 1px solid rgba(0, 212, 255, 0.3);
  }

  .chart-filter-dropdown-container {
    z-index: 10;
  }

  .chart-filter-dropdown-container select {
    padding: 4px 8px;
    border-radius: 4px;
    background-color: rgba(138, 43, 226, 0.7);
    color: #fff;
    border: 1px solid rgba(0, 212, 255, 0.3);
    font-size: 12px;
    cursor: pointer;
  }

  .chart-filter-dropdown-container select:hover {
    background-color: rgba(138, 43, 226, 0.9);
    border-color: rgba(0, 212, 255, 0.6);
  }

  .chart {
    flex: 1;
    width: 100%;
    min-height: 150px;
  }

  .big-number-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .big-number {
      color: #00BAFF;
      font-size: 36px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 10px;
    }

    .unit-text {
      color: #fff;
      font-size: 14px;
      text-align: center;
    }
  }

  .progress-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  // 漏斗数据样式
  .funnel-data {
    flex: 1;
    padding: 10px 0;

    .funnel-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid rgba(0, 186, 255, 0.2);

      &:last-child {
        border-bottom: none;
      }

      .funnel-label {
        color: #fff;
        font-size: 14px;
      }

      .funnel-value {
        color: #00BAFF;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }

  // 预警信息样式（完全照抄计划管理样式，只改颜色为红色）
  .warning-analysis {
    flex: 1;
    padding: 10px 0;

    .warning-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .warning-name {
        color: #fff;
        font-size: 12px;
        width: 80px;
        flex-shrink: 0;
      }

      .warning-bar {
        flex: 1;
        display: flex;
        align-items: center;
        margin-left: 10px;

        .bar-bg {
          flex: 1;
          height: 8px;
          background: rgba(255, 87, 87, 0.2);
          border-radius: 4px;
          overflow: hidden;
          margin-right: 10px;

          .bar-fill {
            height: 100%;
            background: linear-gradient(90deg, hsl(0, 85%, 69%), #f31804);
            border-radius: 4px;
            transition: width 0.3s ease;
          }
        }

        .warning-value {
          color: #FF5757;
          font-size: 12px;
          font-weight: bold;
          width: 60px;
          text-align: right;
        }
      }
    }
  }

  // 趋势统计样式
  .trend-stats {
    margin-bottom: 5px;
    flex-shrink: 0;

    .trend-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 3px 0;

      .trend-label {
        color: #fff;
        font-size: 11px;
      }

      .trend-value {
        color: #3DE7C9;
        font-size: 12px;
        font-weight: bold;
      }
    }
  }

  // 计划管理样式
  .product-analysis {
    flex: 1;
    padding: 10px 0;

    .product-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .product-name {
        color: #fff;
        font-size: 12px;
        width: 80px;
        flex-shrink: 0;
      }

      .product-bar {
        flex: 1;
        display: flex;
        align-items: center;
        margin-left: 10px;

        .bar-bg {
          flex: 1;
          height: 8px;
          background: rgba(0, 186, 255, 0.2);
          border-radius: 4px;
          overflow: hidden;
          margin-right: 10px;

          .bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #00BAFF, #3DE7C9);
            border-radius: 4px;
            transition: width 0.3s ease;
          }
        }

        .product-value {
          color: #00BAFF;
          font-size: 12px;
          font-weight: bold;
          width: 60px;
          text-align: right;
        }
      }
    }
  }

  // 供应商圆形显示样式
  .supplier-circles {
    position: relative;
    height: 100%;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    .circle-item {
      position: absolute;
      display: flex;
      flex-direction: column;
      align-items: center;

      .circle {
        border-radius: 50%;
        border: 2px solid;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
          filter: brightness(1.2);
        }

        &.clickable {
          cursor: pointer;

          &:hover {
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
          }
        }

        .circle-number {
          color: #fff;
          font-weight: bold;
          text-align: center;
          line-height: 1.2;
        }
      }

      .circle-label {
        color: #fff;
        text-align: center;
        line-height: 1.2;
        word-wrap: break-word;
      }

      // 普通圆形样式（随机位置）
      &.random-position {
        .circle {
          width: 60px;
          height: 60px;

          .circle-number {
            font-size: 12px;
          }
        }

        .circle-label {
          font-size: 10px;
          max-width: 60px;
        }
      }

      // 中心圆形样式（考核情况）
      &.center-circle {
        .circle {
          width: 120px;
          height: 120px;

          .circle-number {
            font-size: 14px;
          }
        }

        .circle-label {
          font-size: 12px;
          max-width: 120px;
          margin-top: 5px;
        }
      }

      // 中心位置
      &.center {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }

  // 简单显示样式
  .simple-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20px 0;

    .display-number {
      color: #FF8C00;
      font-size: 36px;
      font-weight: bold;
      line-height: 1;
      margin-bottom: 10px;
    }

    .display-label {
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      text-align: center;
    }
  }

  // 供方管理统计样式
  .supplier-stats {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 3px 5px;
    gap: 5px;

    .supplier-stat-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2px;

      .stat-number {
        color: #FF8C00;
        font-size: 28px;
        font-weight: bold;
        line-height: 1;
        margin-bottom: 3px;
      }

      .stat-label {
        color: #fff;
        font-size: 15px;
        font-weight: 500;
        text-align: center;
      }
    }
  }

  // 供方管理卡片样式
  .supplier-management-card {
    display: flex;
    flex-direction: column;
    height: 100%;

    .card-title {
      flex-shrink: 0;
      margin-bottom: -25px; /* 进一步减小下边距 */
      font-size: 14px;
    }

    .chart {
      flex-shrink: 0;
      margin-bottom: 0px;
    }

    .supplier-stats {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 高频物资模块样式
  .high-frequency-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .high-frequency-materials {
    flex: 2 !important;
    margin-bottom: 0px;
  }

  .price-trend-section {
    flex: 8 !important;
    min-height: 120px; /* 减小最小高度 */
    margin-bottom: 0px;
    margin-top: 5px; /* 减小上边距 */
  }

  .section-title {
    font-size: 12px;
    color: #00BAFF;
    margin: 0 0 4px 0; /* 减小下边距从8px到4px */
    font-weight: 500;
  }

  .material-cloud {
    height: 140px;
    width: 100%;
    position: relative;
    overflow: hidden;
  }

  .chart-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
    font-size: 12px;
  }

  .material-tooltip {
    position: absolute;
    background: rgba(0,0,0,0.8);
    color: #fff;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
  }

  .mini-chart {
    height: 100px;
    width: 100%;
  }

  // 滚动条样式
  .material-list::-webkit-scrollbar {
    width: 4px;
  }

  .material-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  .material-list::-webkit-scrollbar-thumb {
    background: rgba(0, 186, 255, 0.5);
    border-radius: 2px;
  }

  .material-list::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 186, 255, 0.8);
  }

  /* 响应式样式 - 计划执行状态模块 */
  @media (max-width: 1400px) {
    .plan-execution-card .plan-execution-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (max-width: 1000px) {
    .plan-execution-card .plan-execution-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (max-width: 600px) {
    .plan-execution-card .plan-execution-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>


