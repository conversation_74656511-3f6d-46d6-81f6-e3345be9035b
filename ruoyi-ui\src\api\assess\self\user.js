import request from '@/utils/request'

// 查询绩效考核-干部自评人员配置列表
export function listSelfAssessUser(query) {
  return request({
    url: '/web/selfAssessUser/list',
    method: 'get',
    params: query
  })
}

// 查询绩效考核-干部自评人员配置列表
export function listSelfAssessUserAll(query) {
  return request({
    url: '/web/selfAssessUser/listAll',
    method: 'get',
    params: query
  })
}

// 查询绩效考核-干部自评人员配置详细
export function getSelfAssessUser(query) {
  return request({
    url: '/web/selfAssessUser/getInfo' ,
    method: 'get',
    params: query
  })
}

// 新增绩效考核-干部自评人员配置
export function addSelfAssessUser(data) {
  return request({
    url: '/web/selfAssessUser/insert',
    method: 'post',
    data: data
  })
}

// 修改绩效考核-干部自评人员配置
export function updateSelfAssessUser(data) {
  return request({
    url: '/web/selfAssessUser/update',
    method: 'put',
    data: data
  })
}

// 删除绩效考核-干部自评人员配置
export function delSelfAssessUser(data) {
  return request({
    url: '/web/selfAssessUser/delete',
    method: 'put',
    data: data
  })
}

// 根据工号查询用户信息(弃用)
export function getSelfAssessUserByWorkNo(query) {
  return request({
    url: '/web/selfAssessUser/getInfoByWorkNo' ,
    method: 'get',
    params: query
  })
}


// 根据查询当前用户信息
export function getSelfAssessUserInfo() {
  return request({
    url: '/web/selfAssessUser/getSelfAssessUserInfo' ,
    method: 'get',
  })
}

// 查询绩效考核-干部自评人员配置列表(根据权限查询可配置人员)
export function listAvailable(query) {
  return request({
    url: '/web/selfAssessUser/listAvailable',
    method: 'get',
    params: query
  })
}

// 批量获取用户填写状态
export function listAvailableWithStatus(query) {
  return request({
    url: '/web/selfAssessUser/listAvailableWithStatus',
    method: 'get',
    params: query
  })
}

// 获取用户部门（干部、一把手）
export function getReportDeptList() {
  return request({
    url: '/web/selfAssessUser/getReportDeptList' ,
    method: 'get',
  })
}

// 获取用户部门（一把手、条线领导）
export function getCheckDeptList() {
  return request({
    url: '/web/selfAssessUser/getCheckDeptList' ,
    method: 'get',
  })
}

export function getByWorkNoDeptId(query) {
  return request({
    url: '/web/selfAssessUser/getByWorkNoDeptId' ,
    method: 'get',
    params: query
  })
}

// 权限刷新
export function permissionRefresh() {
  return request({
    url: '/web/selfAssessUser/permissionRefresh',
    method: 'post',
  })
}
