{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardMain\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardMain\\index.vue", "mtime": 1756456493834}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQppbXBvcnQgc2NyZWVuZnVsbCBmcm9tICdzY3JlZW5mdWxsJw0KaW1wb3J0IHsNCiAgZ2V0RGFzaGJvYXJkRGF0YSwNCiAgZ2V0UGVyc29uYWxDb25zdW1wdGlvbiwNCiAgZ2V0UHVyY2hhc2VBbmFseXNpcywNCiAgZ2V0UHJvZHVjdEFuYWx5c2lzLA0KICBnZXRNYXBEYXRhLA0KICBnZXRSZWFsVGltZVN0YXRzLA0KICBnZXRTYWxlc0Z1bm5lbCwNCiAgZ2V0UHVyY2hhc2VUcmVuZCwNCiAgZ2V0U3VwcGxpZXJBbmFseXNpcywNCiAgZ2V0QW10TWFuYWdlDQp9IGZyb20gJ0AvYXBpL3B1cmNoYXNlRGFzaGJvYXJkTWFpbicNCmltcG9ydCB7DQogIHNob3dLZXlJbmRpY2F0b3JzLA0KICBzaG93SGlnaEZyZXF1ZW5jeU1hdGVyaWFsTGlzdCwNCiAgZ2V0UHVyY2hhc2VQcmljZUFuZFN0b3JlLA0KICBzaG93Q29raW5nQ29hbEFtb3VudA0KfSBmcm9tICdAL2FwaS9wdXJjaGFzZURhc2hib2FyZC9wdXJjaGFzZURhc2hib2FyZCcNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnUHVyY2hhc2VEYXNoYm9hcmRNYWluJywNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g5pe26Ze06L+H5ruk5Zmo6YCJ6aG5DQogICAgICB0aW1lRmlsdGVyczogWw0KICAgICAgICB7IGlkOiAnZmlsdGVyLTNtJywgbGFiZWw6ICfov5HkuInkuKrmnIgnLCB2YWx1ZTogMSB9LA0KICAgICAgICB7IGlkOiAnZmlsdGVyLTZtJywgbGFiZWw6ICfov5Hlha3kuKrmnIgnLCB2YWx1ZTogMiB9LA0KICAgICAgICB7IGlkOiAnZmlsdGVyLTF5JywgbGFiZWw6ICfov5HkuIDlubQnLCB2YWx1ZTogMyB9DQogICAgICBdLA0KICAgICAgYWN0aXZlRmlsdGVyOiAnZmlsdGVyLTF5JywNCiAgICAgIGN1cnJlbnREaW1lbnNpb25UeXBlOiAzLA0KDQogICAgICAvLyDlm77ooajlrp7kvosNCiAgICAgIGNoYXJ0czoge30sDQogICAgICAvLyDnqpflj6PlpKflsI/lj5jljJblrprml7blmagNCiAgICAgIHJlc2l6ZVRpbWVyOiBudWxsLA0KDQogICAgICAvLyDpq5jpopHnianotYTnm7jlhbPmlbDmja4NCiAgICAgIGhpZ2hGcmVxdWVuY3lNYXRlcmlhbExpc3Q6IFtdLA0KICAgICAgcHJpY2VBbmRTdG9yZURhdGE6IG51bGwsDQogICAgICBzZWxlY3RlZENvZGVUeXBlOiAnQUxMJywNCiAgICAgIHNlbGVjdGVkSXRlbVR5cGU6ICdDTEFTUzMnLA0KICAgICAgc2VsZWN0ZWRNYXRlcmlhbDogJ1BC5Z2XJywNCg0KICAgICAgLy8g55+/54Sm54Wk5bqT5a2Y55u45YWz5pWw5o2uDQogICAgICBjb2tpbmdDb2FsSW52ZW50b3J5RGF0YTogW10sDQogICAgICBzZWxlY3RlZENva2luZ0NvYWxUeXBlOiAnJywgLy8g6YCJ5Lit55qE55+/54Sm54Wk57G75Z6L77yM6buY6K6k5Li656m677yI5YWo6YOo77yJDQoNCiAgICAgIC8vIOWunuaXtuaVsOaNrg0KICAgICAgcmVhbFRpbWVTdGF0czogew0KICAgICAgICBzYWxlczogJzM0MzU2NycsDQogICAgICAgIG9yZGVyczogJzczNDI0NScNCiAgICAgIH0sDQogICAgICAvLyDorqHliJLnrqHnkIbmlbDmja4NCiAgICAgIHByb2R1Y3RBbmFseXNpc0RhdGE6IFsNCiAgICAgICAgeyBuYW1lOiAn6K6h5YiS5oC75p2h5pWwJywgdmFsdWU6ICcwJywgcGVyY2VudGFnZTogMCB9LA0KICAgICAgICB7IG5hbWU6ICflrqHmoLjpqbPlm54nLCB2YWx1ZTogJzAlJywgcGVyY2VudGFnZTogMCB9LA0KICAgICAgICB7IG5hbWU6ICfllYbliqHpg6jpqbPlm54nLCB2YWx1ZTogJzAlJywgcGVyY2VudGFnZTogMCB9LA0KICAgICAgICB7IG5hbWU6ICforqLljZXoh7PlhaXlupPlubPlnYflpKnmlbAnLCB2YWx1ZTogJzMwMDAwJywgcGVyY2VudGFnZTogMzAgfSwNCiAgICAgICAgeyBuYW1lOiAn5YWl5bqT6Iez6aKG55So5bmz5Z2H5aSp5pWwJywgdmFsdWU6ICczMDAwMCcsIHBlcmNlbnRhZ2U6IDMwIH0sDQogICAgICAgIHsgbmFtZTogJ+aOpeaUtuiHs+aMguWNleW5s+Wdh+WkqeaVsCcsIHZhbHVlOiAnNDAwMDAnLCBwZXJjZW50YWdlOiA0MCB9LA0KICAgICAgICB7IG5hbWU6ICfotoXmnJ/mnKrlhaXlupPmlbAnLCB2YWx1ZTogJzQwMDAwJywgcGVyY2VudGFnZTogNDAgfSwNCiAgICAgICAgeyBuYW1lOiAn6LaF5pyf5pyq6aKG55So5pWwJywgdmFsdWU6ICc0MDAwMCcsIHBlcmNlbnRhZ2U6IDQwIH0NCg0KICAgICAgXSwNCiAgICAgIC8vIOiuoeWIkuaJp+ihjOeKtuaAgeaVsOaNrg0KICAgICAgcGxhbkV4ZWN1dGlvbkRhdGE6IFsNCiAgICAgICAgeyBuYW1lOiAn6K6h5YiS5a6M5oiQ546HJywgdmFsdWU6ICc4NSUnIH0sDQogICAgICAgIHsgbmFtZTogJ+WcqOmAlOiuouWNleaVsCcsIHZhbHVlOiAnMSwyMzQnIH0sDQogICAgICAgIHsgbmFtZTogJ+W+heWuoeaguOiuoeWIkicsIHZhbHVlOiAnNTYnIH0sDQogICAgICAgIHsgbmFtZTogJ+e0p+aApemHh+i0rScsIHZhbHVlOiAnMTInIH0sDQogICAgICAgIC8vIHsgbmFtZTogJ+S+m+W6lOWVhuWTjeW6lOeOhycsIHZhbHVlOiAnOTIlJyB9LA0KICAgICAgICAvLyB7IG5hbWU6ICflupPlrZjlkajovaznjocnLCB2YWx1ZTogJzMuMicgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn6YeH6LSt5oiQ5pys6IqC57qmJywgdmFsdWU6ICc4LjUlJyB9LA0KICAgICAgICAvLyB7IG5hbWU6ICfotKjph4/lkIjmoLznjocnLCB2YWx1ZTogJzk4LjclJyB9DQogICAgICBdLA0KICAgICAgLy8g5bqV6YOo57uf6K6h5pWw5o2uDQogICAgICBib3R0b21TdGF0czogWw0KICAgICAgICB7IGxhYmVsOiAn5L6b5bqU5ZWG5L+h5oGv5YWo5pmvJywgdmFsdWU6ICczNDU1NCcgfSwNCiAgICAgICAgeyBsYWJlbDogJ+iuouWNlemHjycsIHZhbHVlOiAnMzQ1NTQnIH0sDQogICAgICAgIHsgbGFiZWw6ICflrqLmiLfmlbAnLCB2YWx1ZTogJzM0NTU0JyB9DQogICAgICBdLA0KICAgICAgLy8g5rC05L2N5Zu+6YWN572uDQogICAgICB3YXRlckxldmVsQ29uZmlnOiB7DQogICAgICAgIGRhdGE6IFs1MF0sDQogICAgICAgIHNoYXBlOiAnY2lyY2xlJywNCiAgICAgICAgd2F2ZU51bTogMywNCiAgICAgICAgd2F2ZUhlaWdodDogNDAsDQogICAgICAgIHdhdmVPcGFjaXR5OiAwLjQsDQogICAgICAgIGNvbG9yczogWycjMDBCQUZGJywgJyMzREU3QzknXQ0KICAgICAgfSwNCiAgICAgIC8vIOS+m+W6lOWVhuaVsOaNru+8iOWRqOWbtOWbm+S4quWchu+8iQ0KICAgICAgc3VwcGxpZXJEYXRhOiBbDQogICAgICAgIHsgaWQ6IDEsIHZhbHVlOiAnOCwwOTInLCBsYWJlbDogJ+WPguagh+asoeaVsCcsIGNvbG9yOiAnI0ZGNkI2QicsIHBvc2l0aW9uOiB7IHRvcDogJzEwJScsIGxlZnQ6ICcxMCUnIH0gfSwNCiAgICAgICAgeyBpZDogMiwgdmFsdWU6ICcxLDI0NScsIGxhYmVsOiAn5Lit5qCH5qyh5pWwJywgY29sb3I6ICcjNEVDREM0JywgcG9zaXRpb246IHsgdG9wOiAnMTAlJywgcmlnaHQ6ICcxMCUnIH0gfSwNCiAgICAgICAgeyBpZDogMywgdmFsdWU6ICc4OScsIGxhYmVsOiAn6LSo6YeP5byC6K6u5qyh5pWwJywgY29sb3I6ICcjNDVCN0QxJywgcG9zaXRpb246IHsgYm90dG9tOiAnMTAlJywgbGVmdDogJzEwJScgfSB9LA0KICAgICAgICB7IGlkOiA0LCB2YWx1ZTogJzE1NicsIGxhYmVsOiAn5ZCI5L2c5bm06ZmQJywgY29sb3I6ICcjOTZDRUI0JywgcG9zaXRpb246IHsgYm90dG9tOiAnMTAlJywgcmlnaHQ6ICcxMCUnIH0gfQ0KICAgICAgXSwNCiAgICAgIC8vIOS4reW/g+WchuaVsOaNru+8iOiAg+aguOaDheWGte+8iQ0KICAgICAgY2VudGVyRGF0YTogew0KICAgICAgICBsYWJlbDogJ+iAg+aguOaDheWGtScsDQogICAgICAgIGFtb3VudDogJzU2N+S4hycsDQogICAgICAgIGNvdW50OiAnMjPmrKEnLA0KICAgICAgICBjb2xvcjogJyMwMEJBRkYnDQogICAgICB9LA0KICAgICAgLy8g6LSo6YeP5byC6K6u5oC75pWwDQogICAgICBxdWFsaXR5SXNzdWVDb3VudDogJzE1NicsDQogICAgICAvLyDkvpvmlrnnrqHnkIbnu5/orqHmlbDmja4NCiAgICAgIHN1cHBsaWVyU3RhdHM6IHsNCiAgICAgICAgYWRtaXNzaW9uOiAnMSwyNDUnLCAgICAvLyDlh4blhaUNCiAgICAgICAgZWxpbWluYXRpb246ICc4OScsICAgICAvLyDmt5jmsbANCiAgICAgICAgc3VzcGVuc2lvbjogJzE1NicgICAgICAvLyDmmoLnvJMNCiAgICAgIH0sDQogICAgICAvLyDpooTorabkv6Hmga/mlbDmja4NCiAgICAgIHdhcm5pbmdJbmZvOiB7DQogICAgICAgIGNlcnRpZmljYXRlRXhwaXJ5OiAwLCAvLyDor4Hkuabov4fmnJ/mlbDph4/vvIjkvpvlupTllYbpo47pmanmj5DphpLvvIkNCiAgICAgICAgY29udHJhY3RFeHBpcnk6IDAgICAgIC8vIOWQiOWQjOi/h+acn+aVsOmHj++8iOWQiOWQjOWIsOacn+aPkOmGku+8iQ0KICAgICAgfSwNCiAgICAgIC8vIOmHh+i0reWFs+mUruaMh+agh+aVsOaNrg0KICAgICAgcHVyY2hhc2VTdGF0czogew0KICAgICAgICBhcnJpdmVSYXRlOiAwICAvLyDliLDotKflrozmiJDluqYNCiAgICAgIH0sDQogICAgICAvLyDotYTph5HnrqHnkIbmlbDmja4NCiAgICAgIGZ1bmRNYW5hZ2VtZW50OiB7DQogICAgICAgIG5leHRNb250aDogJzAnLCAgICAvLyDkuIvmnIjmi5/lhaXlupPmgLvph5Hpop0NCiAgICAgICAgdHdvTW9udGhzTGF0ZXI6ICcwJywgLy8gMuaciOWQjuaLn+WFpeW6k+aAu+mHkeminQ0KICAgICAgICB0aHJlZU1vbnRoc0xhdGVyOiAnMCcgLy8gM+aciOWQjuaLn+WFpeW6k+aAu+mHkeminQ0KICAgICAgfSwNCiAgICAgIC8vIOWQiOWQjOeuoeeQhuaVsOaNrg0KICAgICAgY29udHJhY3REYXRhOiBbDQogICAgICAgIHsgbmFtZTogJ+WOn+adkOaWmScsIGNvdW50OiAxMjA5NTI4MiB9LA0KICAgICAgICB7IG5hbWU6ICfovoXogJDmnZAnLCBjb3VudDogODM0MDE1NCB9LA0KICAgICAgICB7IG5hbWU6ICfmnZDmlpnnsbsnLCBjb3VudDogMzMzNDQ1MTcgfSwNCiAgICAgICAgeyBuYW1lOiAn6YCa55So5aSH5Lu2JywgY291bnQ6IDc2Mzc0NDUxIH0sDQogICAgICAgIHsgbmFtZTogJ+S4k+eUqOWkh+S7ticsIGNvdW50OiA0MzUzOTIxIH0sDQogICAgICAgIHsgbmFtZTogJ+WKnuWFrCcsIGNvdW50OiAyMzUxNSB9DQogICAgICBdDQogICAgfQ0KICB9LA0KDQogIGNvbXB1dGVkOiB7DQogICAgaXNGdWxsc2NyZWVuKCkgew0KICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLmFwcC5pc0Z1bGxzY3JlZW5Nb2RlDQogICAgfQ0KICB9LA0KDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pbml0RnVsbHNjcmVlbkxpc3RlbmVyKCkNCiAgICB0aGlzLmluaXREYXNoYm9hcmQoKQ0KICAgIC8vIOebkeWQrOeql+WPo+Wkp+Wwj+WPmOWMlg0KICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCB0aGlzLmhhbmRsZVJlc2l6ZSkNCiAgfSwNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDnp7vpmaTkuovku7bnm5HlkKwNCiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5oYW5kbGVSZXNpemUpDQogICAgLy8g6ZSA5q+B5omA5pyJ5Zu+6KGo5a6e5L6LDQogICAgT2JqZWN0LnZhbHVlcyh0aGlzLmNoYXJ0cykuZm9yRWFjaChjaGFydCA9PiB7DQogICAgICBpZiAoY2hhcnQgJiYgY2hhcnQuZGlzcG9zZSkgew0KICAgICAgICBjaGFydC5kaXNwb3NlKCkNCiAgICAgIH0NCiAgICB9KQ0KICAgIC8vIOenu+mZpOWFqOWxj+ebkeWQrOWZqA0KICAgIHRoaXMucmVtb3ZlRnVsbHNjcmVlbkxpc3RlbmVyKCkNCiAgICAvLyDnoa7kv53pgIDlh7rlhajlsY/mqKHlvI8NCiAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXBwL3NldEZ1bGxzY3JlZW5Nb2RlJywgZmFsc2UpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBhc3luYyBpbml0RGFzaGJvYXJkKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgdGhpcy5sb2FkRGF0YSgpDQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmluaXRDaGFydHMoKQ0KICAgICAgICB9KQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yid5aeL5YyW6am+6am26Iix5aSx6LSlOicsIGVycm9yKQ0KICAgICAgfQ0KICAgIH0sDQogICAgDQogICAgYXN5bmMgbG9hZERhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDlubbooYzojrflj5bpooTorabkv6Hmga/mlbDmja7lkozotYTph5HnrqHnkIbmlbDmja4NCiAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoWw0KICAgICAgICAgIHRoaXMuZmV0Y2hXYXJuaW5nRGF0YSgpLA0KICAgICAgICAgIHRoaXMuZmV0Y2hGdW5kTWFuYWdlbWVudERhdGEoKQ0KICAgICAgICBdKQ0KDQogICAgICAgIC8vIOi/memHjOWPr+S7peW5tuihjOWKoOi9veWFtuS7luaVsOaNrg0KICAgICAgICAvLyBjb25zdCBbZGFzaGJvYXJkRGF0YSwgcGVyc29uYWxEYXRhLCBwdXJjaGFzZURhdGFdID0gYXdhaXQgUHJvbWlzZS5hbGwoWw0KICAgICAgICAvLyAgIGdldERhc2hib2FyZERhdGEoKSwNCiAgICAgICAgLy8gICBnZXRQZXJzb25hbENvbnN1bXB0aW9uKCksDQogICAgICAgIC8vICAgZ2V0UHVyY2hhc2VBbmFseXNpcygpDQogICAgICAgIC8vIF0pDQoNCiAgICAgICAgY29uc29sZS5sb2coJ+aVsOaNruWKoOi9veWujOaIkCcpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3mlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOiOt+WPlui1hOmHkeeuoeeQhuaVsOaNrg0KICAgIGFzeW5jIGZldGNoRnVuZE1hbmFnZW1lbnREYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRBbXRNYW5hZ2UoKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YQ0KDQogICAgICAgICAgLy8g5qC55o2ucmVzZXJ2ZTHlrZfmrrXmib7liLDlr7nlupTnmoTmlbDmja4NCiAgICAgICAgICBjb25zdCBuZXh0TW9udGhEYXRhID0gZGF0YS5maW5kKGl0ZW0gPT4gaXRlbS5yZXNlcnZlMSA9PT0gJzAxJykNCiAgICAgICAgICBjb25zdCB0d29Nb250aHNEYXRhID0gZGF0YS5maW5kKGl0ZW0gPT4gaXRlbS5yZXNlcnZlMSA9PT0gJzAyJykNCiAgICAgICAgICBjb25zdCB0aHJlZU1vbnRoc0RhdGEgPSBkYXRhLmZpbmQoaXRlbSA9PiBpdGVtLnJlc2VydmUxID09PSAnMDMnKQ0KDQogICAgICAgICAgLy8g5pu05paw6LWE6YeR566h55CG5pWw5o2uDQogICAgICAgICAgdGhpcy5mdW5kTWFuYWdlbWVudC5uZXh0TW9udGggPSBuZXh0TW9udGhEYXRhID8gKG5leHRNb250aERhdGEucmVzZXJ2ZTQgfHwgJzAnKSA6ICcwJw0KICAgICAgICAgIHRoaXMuZnVuZE1hbmFnZW1lbnQudHdvTW9udGhzTGF0ZXIgPSB0d29Nb250aHNEYXRhID8gKHR3b01vbnRoc0RhdGEucmVzZXJ2ZTQgfHwgJzAnKSA6ICcwJw0KICAgICAgICAgIHRoaXMuZnVuZE1hbmFnZW1lbnQudGhyZWVNb250aHNMYXRlciA9IHRocmVlTW9udGhzRGF0YSA/ICh0aHJlZU1vbnRoc0RhdGEucmVzZXJ2ZTQgfHwgJzAnKSA6ICcwJw0KDQogICAgICAgICAgLy8g5pu05paw5Zu+6KGoDQogICAgICAgICAgdGhpcy51cGRhdGVGdW5kTWFuYWdlbWVudENoYXJ0KCkNCg0KICAgICAgICAgIGNvbnNvbGUubG9nKCfotYTph5HnrqHnkIbmlbDmja7ojrflj5bmiJDlip86JywgdGhpcy5mdW5kTWFuYWdlbWVudCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfotYTph5HnrqHnkIbmlbDmja7moLzlvI/kuI3mraPnoa46JywgcmVzcG9uc2UpDQogICAgICAgICAgdGhpcy5yZXNldEZ1bmRNYW5hZ2VtZW50RGF0YSgpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlui1hOmHkeeuoeeQhuaVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5yZXNldEZ1bmRNYW5hZ2VtZW50RGF0YSgpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOmHjee9rui1hOmHkeeuoeeQhuaVsOaNruS4uum7mOiupOWAvA0KICAgIHJlc2V0RnVuZE1hbmFnZW1lbnREYXRhKCkgew0KICAgICAgdGhpcy5mdW5kTWFuYWdlbWVudC5uZXh0TW9udGggPSAnMCcNCiAgICAgIHRoaXMuZnVuZE1hbmFnZW1lbnQudHdvTW9udGhzTGF0ZXIgPSAnMCcNCiAgICAgIHRoaXMuZnVuZE1hbmFnZW1lbnQudGhyZWVNb250aHNMYXRlciA9ICcwJw0KICAgICAgLy8g5pu05paw5Zu+6KGoDQogICAgICB0aGlzLnVwZGF0ZUZ1bmRNYW5hZ2VtZW50Q2hhcnQoKQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5bpooTorabkv6Hmga/lkozorqHliJLnrqHnkIbmlbDmja4NCiAgICBhc3luYyBmZXRjaFdhcm5pbmdEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6LCD55So6YeH6LSt5YWo5pmv55yL5p2/55qE5YWz6ZSu5oyH5qCH5o6l5Y+jDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgc2hvd0tleUluZGljYXRvcnMoeyBkaW1lbnNpb25UeXBlOiB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlIH0pDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YQ0KDQogICAgICAgICAgLy8g5pu05paw6aKE6K2m5L+h5oGv5pWw5o2uDQogICAgICAgICAgLy8g6K+B5Lmm6L+H5pyf5pWw5o2u5p2l6Ieq5L6b5bqU5ZWG6aOO6Zmp5o+Q6YaSDQogICAgICAgICAgdGhpcy53YXJuaW5nSW5mby5jZXJ0aWZpY2F0ZUV4cGlyeSA9IGRhdGEuc3VwcFJpc2tOdW0gfHwgMA0KICAgICAgICAgIC8vIOWQiOWQjOi/h+acn+aVsOaNruadpeiHquWQiOWQjOWIsOacn+aPkOmGkg0KICAgICAgICAgIHRoaXMud2FybmluZ0luZm8uY29udHJhY3RFeHBpcnkgPSBkYXRhLmJwb0V4cGlyZU51bSB8fCAwDQoNCiAgICAgICAgICAvLyDmm7TmlrDorqHliJLnrqHnkIbmlbDmja4NCiAgICAgICAgICB0aGlzLnVwZGF0ZVBsYW5NYW5hZ2VtZW50RGF0YShkYXRhKQ0KDQogICAgICAgICAgLy8g5pu05paw6K6h5YiS5omn6KGM54q25oCB5pWw5o2uDQogICAgICAgICAgdGhpcy51cGRhdGVQbGFuRXhlY3V0aW9uRGF0YShkYXRhKQ0KDQogICAgICAgICAgLy8g5pu05paw6YeH6LSt5YWz6ZSu5oyH5qCH5pWw5o2uDQogICAgICAgICAgdGhpcy5wdXJjaGFzZVN0YXRzLmFycml2ZVJhdGUgPSBkYXRhLmFycml2ZVJhdGUgfHwgMA0KDQogICAgICAgICAgY29uc29sZS5sb2coJ+mihOitpuS/oeaBr+WSjOiuoeWIkueuoeeQhuaVsOaNruiOt+WPluaIkOWKnzonLCB7DQogICAgICAgICAgICB3YXJuaW5nSW5mbzogdGhpcy53YXJuaW5nSW5mbywNCiAgICAgICAgICAgIHBsYW5EYXRhOiB7DQogICAgICAgICAgICAgIHBsYW5Ub3RhbE51bTogZGF0YS5wbGFuVG90YWxOdW0sDQogICAgICAgICAgICAgIHJlamVjdE51bTE6IGRhdGEucmVqZWN0TnVtMSwNCiAgICAgICAgICAgICAgcmVqZWN0TnVtMjogZGF0YS5yZWplY3ROdW0yDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgcHVyY2hhc2VTdGF0czogdGhpcy5wdXJjaGFzZVN0YXRzDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfmlbDmja7ojrflj5blpLHotKU6JywgcmVzcG9uc2UpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluaVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgICAgLy8g5L2/55So6buY6K6k5YC8DQogICAgICAgIHRoaXMud2FybmluZ0luZm8uY2VydGlmaWNhdGVFeHBpcnkgPSAwDQogICAgICAgIHRoaXMud2FybmluZ0luZm8uY29udHJhY3RFeHBpcnkgPSAwDQogICAgICAgIHRoaXMucHVyY2hhc2VTdGF0cy5hcnJpdmVSYXRlID0gMA0KICAgICAgICB0aGlzLnJlc2V0UGxhbk1hbmFnZW1lbnREYXRhKCkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5pu05paw6K6h5YiS566h55CG5pWw5o2uDQogICAgdXBkYXRlUGxhbk1hbmFnZW1lbnREYXRhKGRhdGEpIHsNCiAgICAgIC8vIOiuoeWIkuaAu+adoeaVsA0KICAgICAgaWYgKGRhdGEucGxhblRvdGFsTnVtICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgdGhpcy5wcm9kdWN0QW5hbHlzaXNEYXRhWzBdLnZhbHVlID0gZGF0YS5wbGFuVG90YWxOdW0udG9TdHJpbmcoKQ0KICAgICAgICB0aGlzLnByb2R1Y3RBbmFseXNpc0RhdGFbMF0ucGVyY2VudGFnZSA9IE1hdGgubWluKDEwMCwgTWF0aC5tYXgoMCwgKGRhdGEucGxhblRvdGFsTnVtIC8gMTAwMDAwKSAqIDEwMCkpDQogICAgICB9DQoNCiAgICAgIC8vIOWuoeaguOmps+Wbnu+8iOeZvuWIhuavlO+8iQ0KICAgICAgaWYgKGRhdGEucmVqZWN0TnVtMSAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgIHRoaXMucHJvZHVjdEFuYWx5c2lzRGF0YVsxXS52YWx1ZSA9IGRhdGEucmVqZWN0TnVtMSArICclJw0KICAgICAgICB0aGlzLnByb2R1Y3RBbmFseXNpc0RhdGFbMV0ucGVyY2VudGFnZSA9IE1hdGgubWluKDEwMCwgTWF0aC5tYXgoMCwgZGF0YS5yZWplY3ROdW0xKSkNCiAgICAgIH0NCg0KICAgICAgLy8g5ZWG5Yqh6YOo6amz5Zue77yI55m+5YiG5q+U77yJDQogICAgICBpZiAoZGF0YS5yZWplY3ROdW0yICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgdGhpcy5wcm9kdWN0QW5hbHlzaXNEYXRhWzJdLnZhbHVlID0gZGF0YS5yZWplY3ROdW0yICsgJyUnDQogICAgICAgIHRoaXMucHJvZHVjdEFuYWx5c2lzRGF0YVsyXS5wZXJjZW50YWdlID0gTWF0aC5taW4oMTAwLCBNYXRoLm1heCgwLCBkYXRhLnJlamVjdE51bTIpKQ0KICAgICAgfQ0KDQogICAgICAvLyDorqLljZXoh7PlhaXlupPlubPlnYflpKnmlbDvvIjnu5HlrprliLByZXNlcnZlNOWtl+aute+8iQ0KICAgICAgaWYgKGRhdGEucmVzZXJ2ZTQgIT09IHVuZGVmaW5lZCkgew0KICAgICAgICB0aGlzLnByb2R1Y3RBbmFseXNpc0RhdGFbM10udmFsdWUgPSBkYXRhLnJlc2VydmU0LnRvU3RyaW5nKCkgDQogICAgICAgIHRoaXMucHJvZHVjdEFuYWx5c2lzRGF0YVszXS5wZXJjZW50YWdlID0gTWF0aC5taW4oMTAwLCBNYXRoLm1heCgwLCAoZGF0YS5yZXNlcnZlNCAvIDMwKSAqIDEwMCkpIC8vIOWBh+iuvjMw5aSp5Li6MTAwJQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDph43nva7orqHliJLnrqHnkIbmlbDmja7kuLrpu5jorqTlgLwNCiAgICByZXNldFBsYW5NYW5hZ2VtZW50RGF0YSgpIHsNCiAgICAgIHRoaXMucHJvZHVjdEFuYWx5c2lzRGF0YVswXS52YWx1ZSA9ICcwJw0KICAgICAgdGhpcy5wcm9kdWN0QW5hbHlzaXNEYXRhWzBdLnBlcmNlbnRhZ2UgPSAwDQogICAgICB0aGlzLnByb2R1Y3RBbmFseXNpc0RhdGFbMV0udmFsdWUgPSAnMCUnDQogICAgICB0aGlzLnByb2R1Y3RBbmFseXNpc0RhdGFbMV0ucGVyY2VudGFnZSA9IDANCiAgICAgIHRoaXMucHJvZHVjdEFuYWx5c2lzRGF0YVsyXS52YWx1ZSA9ICcwJScNCiAgICAgIHRoaXMucHJvZHVjdEFuYWx5c2lzRGF0YVsyXS5wZXJjZW50YWdlID0gMA0KICAgICAgdGhpcy5wcm9kdWN0QW5hbHlzaXNEYXRhWzNdLnZhbHVlID0gJzDlpKknDQogICAgICB0aGlzLnByb2R1Y3RBbmFseXNpc0RhdGFbM10ucGVyY2VudGFnZSA9IDANCiAgICB9LA0KDQogICAgLy8g5pu05paw6K6h5YiS5omn6KGM54q25oCB5pWw5o2uDQogICAgdXBkYXRlUGxhbkV4ZWN1dGlvbkRhdGEoZGF0YSkgew0KICAgICAgaWYgKGRhdGEpIHsNCiAgICAgICAgLy8g6K6h5YiS5a6M5oiQ546HDQogICAgICAgIGlmIChkYXRhLnBsYW5Db21wbGV0aW9uUmF0ZSAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgdGhpcy5wbGFuRXhlY3V0aW9uRGF0YVswXS52YWx1ZSA9IGRhdGEucGxhbkNvbXBsZXRpb25SYXRlICsgJyUnDQogICAgICAgIH0NCiAgICAgICAgLy8g5Zyo6YCU6K6i5Y2V5pWwDQogICAgICAgIGlmIChkYXRhLmluVHJhbnNpdE9yZGVyTnVtICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICB0aGlzLnBsYW5FeGVjdXRpb25EYXRhWzFdLnZhbHVlID0gdGhpcy5mb3JtYXROdW1iZXIoZGF0YS5pblRyYW5zaXRPcmRlck51bSkNCiAgICAgICAgfQ0KICAgICAgICAvLyDlvoXlrqHmoLjorqHliJINCiAgICAgICAgaWYgKGRhdGEucGVuZGluZ0F1ZGl0TnVtICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICB0aGlzLnBsYW5FeGVjdXRpb25EYXRhWzJdLnZhbHVlID0gZGF0YS5wZW5kaW5nQXVkaXROdW0udG9TdHJpbmcoKQ0KICAgICAgICB9DQogICAgICAgIC8vIOe0p+aApemHh+i0rQ0KICAgICAgICBpZiAoZGF0YS51cmdlbnRQdXJjaGFzZU51bSAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgdGhpcy5wbGFuRXhlY3V0aW9uRGF0YVszXS52YWx1ZSA9IGRhdGEudXJnZW50UHVyY2hhc2VOdW0udG9TdHJpbmcoKQ0KICAgICAgICB9DQogICAgICAgIC8vIOS+m+W6lOWVhuWTjeW6lOeOhw0KICAgICAgICBpZiAoZGF0YS5zdXBwbGllclJlc3BvbnNlUmF0ZSAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgdGhpcy5wbGFuRXhlY3V0aW9uRGF0YVs0XS52YWx1ZSA9IGRhdGEuc3VwcGxpZXJSZXNwb25zZVJhdGUgKyAnJScNCiAgICAgICAgfQ0KICAgICAgICAvLyDlupPlrZjlkajovaznjocNCiAgICAgICAgaWYgKGRhdGEuaW52ZW50b3J5VHVybm92ZXJSYXRlICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICB0aGlzLnBsYW5FeGVjdXRpb25EYXRhWzVdLnZhbHVlID0gZGF0YS5pbnZlbnRvcnlUdXJub3ZlclJhdGUudG9TdHJpbmcoKQ0KICAgICAgICB9DQogICAgICAgIC8vIOmHh+i0reaIkOacrOiKgue6pg0KICAgICAgICBpZiAoZGF0YS5jb3N0U2F2aW5nUmF0ZSAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgdGhpcy5wbGFuRXhlY3V0aW9uRGF0YVs2XS52YWx1ZSA9IGRhdGEuY29zdFNhdmluZ1JhdGUgKyAnJScNCiAgICAgICAgfQ0KICAgICAgICAvLyDotKjph4/lkIjmoLznjocNCiAgICAgICAgaWYgKGRhdGEucXVhbGl0eVBhc3NSYXRlICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICB0aGlzLnBsYW5FeGVjdXRpb25EYXRhWzddLnZhbHVlID0gZGF0YS5xdWFsaXR5UGFzc1JhdGUgKyAnJScNCiAgICAgICAgfQ0KICAgICAgICBjb25zb2xlLmxvZygn6K6h5YiS5omn6KGM54q25oCB5pWw5o2u5pu05paw5a6M5oiQOicsIHRoaXMucGxhbkV4ZWN1dGlvbkRhdGEpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOagvOW8j+WMluaVsOWtl+aYvuekuu+8iOa3u+WKoOWNg+WIhuS9jeWIhumalOespu+8iQ0KICAgIGZvcm1hdE51bWJlcihudW0pIHsNCiAgICAgIGlmIChudW0gPT09IHVuZGVmaW5lZCB8fCBudW0gPT09IG51bGwpIHJldHVybiAnMCcNCiAgICAgIHJldHVybiBudW0udG9TdHJpbmcoKS5yZXBsYWNlKC9cQig/PShcZHszfSkrKD8hXGQpKS9nLCAnLCcpDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluiuoeWIkueuoeeQhuWbvuaghw0KICAgIGdldFBsYW5JY29uKGluZGV4KSB7DQogICAgICBjb25zdCBpY29ucyA9IFsNCiAgICAgICAgJ2VsLWljb24tZG9jdW1lbnQnLCAvLyDorqHliJLmgLvmnaHmlbANCiAgICAgICAgJ2VsLWljb24tY2xvc2UnLCAvLyDlrqHmoLjpqbPlm54NCiAgICAgICAgJ2VsLWljb24tZXJyb3InLCAvLyDllYbliqHpg6jpqbPlm54NCiAgICAgICAgJ2VsLWljb24tdGltZScsIC8vIOiuouWNleiHs+WFpeW6k+W5s+Wdh+WkqeaVsA0KICAgICAgICAnZWwtaWNvbi10cnVjaycsIC8vIOWFpeW6k+iHs+mihueUqOW5s+Wdh+WkqeaVsA0KICAgICAgICAnZWwtaWNvbi1tZXNzYWdlJywgLy8g5o6l5pS26Iez5oyC5Y2V5bmz5Z2H5aSp5pWwDQogICAgICAgICdlbC1pY29uLXdhcm5pbmctb3V0bGluZScsIC8vIOi2heacn+acquWFpeW6k+aVsA0KICAgICAgICAnZWwtaWNvbi13YXJuaW5nLW91dGxpbmUnIC8vIOi2heacn+acqumihueUqOaVsA0KICAgICAgXQ0KICAgICAgcmV0dXJuIGljb25zW2luZGV4XSB8fCAnZWwtaWNvbi1pbmZvJw0KICAgIH0sDQoNCiAgICAvLyDojrflj5borqHliJLmiafooYznirbmgIHlm77moIcNCiAgICBnZXRFeGVjdXRpb25JY29uKGluZGV4KSB7DQogICAgICBjb25zdCBpY29ucyA9IFsNCiAgICAgICAgJ2VsLWljb24tc3VjY2VzcycsIC8vIOiuoeWIkuWujOaIkOeOhw0KICAgICAgICAnZWwtaWNvbi1nb29kcycsIC8vIOWcqOmAlOiuouWNleaVsA0KICAgICAgICAnZWwtaWNvbi1lZGl0LW91dGxpbmUnLCAvLyDlvoXlrqHmoLjorqHliJINCiAgICAgICAgJ2VsLWljb24td2FybmluZycsIC8vIOe0p+aApemHh+i0rQ0KICAgICAgICAnZWwtaWNvbi1waG9uZScsIC8vIOS+m+W6lOWVhuWTjeW6lOeOhw0KICAgICAgICAnZWwtaWNvbi1yZWZyZXNoJywgLy8g5bqT5a2Y5ZGo6L2s546HDQogICAgICAgICdlbC1pY29uLWNvaW4nLCAvLyDph4fotK3miJDmnKzoioLnuqYNCiAgICAgICAgJ2VsLWljb24tY2lyY2xlLWNoZWNrJyAvLyDotKjph4/lkIjmoLznjocNCiAgICAgIF0NCiAgICAgIHJldHVybiBpY29uc1tpbmRleF0gfHwgJ2VsLWljb24taW5mbycNCiAgICB9LA0KDQogICAgaW5pdENoYXJ0cygpIHsNCiAgICAgIHRoaXMuaW5pdEZ1bmRNYW5hZ2VtZW50Q2hhcnQoKQ0KICAgICAgdGhpcy5pbml0U3VwcGxpZXJNYW5hZ2VtZW50Q2hhcnQoKQ0KICAgICAgdGhpcy5pbml0UGVyc29uYWxDb25zdW1wdGlvbkNoYXJ0KCkNCiAgICAgIHRoaXMuaW5pdFB1cmNoYXNlQW5hbHlzaXNDaGFydCgpDQogICAgICB0aGlzLmluaXRDb250cmFjdENoYXJ0KCkNCiAgICAgIHRoaXMuaW5pdFRyZW5kQ2hhcnQoKQ0KICAgICAgdGhpcy5pbml0U3VwcGxpZXJDaGFydCgpDQogICAgICB0aGlzLmZldGNoSGlnaEZyZXF1ZW5jeURhdGEoKQ0KICAgICAgdGhpcy5mZXRjaENva2luZ0NvYWxJbnZlbnRvcnlEYXRhKCkNCiAgICB9LA0KDQogICAgLy8g6LWE6YeR566h55CG5p+x54q25Zu+DQogICAgaW5pdEZ1bmRNYW5hZ2VtZW50Q2hhcnQoKSB7DQogICAgICBjb25zdCBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdmdW5kTWFuYWdlbWVudENoYXJ0JykNCiAgICAgIGlmICghY2hhcnREb20pIHJldHVybg0KDQogICAgICBjb25zdCBjaGFydCA9IGVjaGFydHMuaW5pdChjaGFydERvbSkNCiAgICAgIHRoaXMuY2hhcnRzLmZ1bmRNYW5hZ2VtZW50ID0gY2hhcnQNCg0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsDQogICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICBsZWZ0OiAnMTUlJywNCiAgICAgICAgICByaWdodDogJzEwJScsDQogICAgICAgICAgdG9wOiAnMjUlJywNCiAgICAgICAgICBib3R0b206ICcyNSUnLA0KICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogWyfkuIvmnIgnLCAnMuaciOWQjicsICcz5pyI5ZCOJ10sDQogICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyMwMEJBRkYnDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsDQogICAgICAgICAgICBmb250U2l6ZTogMTINCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBuYW1lOiAn5ouf5YWl5bqT6YeR6aKdJywNCiAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7DQogICAgICAgICAgICBjb2xvcjogJyNmZmYnLA0KICAgICAgICAgICAgZm9udFNpemU6IDEyLA0KICAgICAgICAgICAgYWxpZ246ICdyaWdodCcsDQogICAgICAgICAgfSwNCiAgICAgICAgICBtaW46IDAsDQogICAgICAgICAgbWF4OiA4MDAwLA0KICAgICAgICAgIGludGVydmFsOiAyMDAwLA0KICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjMDBCQUZGJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBjb2xvcjogJyNmZmYnLA0KICAgICAgICAgICAgZm9udFNpemU6IDEyLA0KICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbih2YWx1ZSkgew0KICAgICAgICAgICAgICByZXR1cm4gdmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHNwbGl0TGluZTogew0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgwLCAxODYsIDI1NSwgMC4yKScNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICBuYW1lOiAn5ouf5YWl5bqT6YeR6aKdJywNCiAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICBkYXRhOiBbDQogICAgICAgICAgICBwYXJzZUZsb2F0KHRoaXMuZnVuZE1hbmFnZW1lbnQubmV4dE1vbnRoKSB8fCAwLA0KICAgICAgICAgICAgcGFyc2VGbG9hdCh0aGlzLmZ1bmRNYW5hZ2VtZW50LnR3b01vbnRoc0xhdGVyKSB8fCAwLA0KICAgICAgICAgICAgcGFyc2VGbG9hdCh0aGlzLmZ1bmRNYW5hZ2VtZW50LnRocmVlTW9udGhzTGF0ZXIpIHx8IDANCiAgICAgICAgICBdLA0KICAgICAgICAgIGJhcldpZHRoOiAnNTAlJywNCiAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgIGNvbG9yOiB7DQogICAgICAgICAgICAgIHR5cGU6ICdsaW5lYXInLA0KICAgICAgICAgICAgICB4OiAwLA0KICAgICAgICAgICAgICB5OiAwLA0KICAgICAgICAgICAgICB4MjogMCwNCiAgICAgICAgICAgICAgeTI6IDEsDQogICAgICAgICAgICAgIGNvbG9yU3RvcHM6IFt7DQogICAgICAgICAgICAgICAgb2Zmc2V0OiAwLCBjb2xvcjogJyMwMEJBRkYnIC8vIOmhtumDqOminOiJsg0KICAgICAgICAgICAgICB9LCB7DQogICAgICAgICAgICAgICAgb2Zmc2V0OiAxLCBjb2xvcjogJyMwMDgwQ0MnIC8vIOW6lemDqOminOiJsg0KICAgICAgICAgICAgICB9XSwNCiAgICAgICAgICAgICAgZ2xvYmFsOiBmYWxzZQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGJvcmRlclJhZGl1czogWzQsIDQsIDAsIDBdIC8vIOmhtumDqOWchuinkg0KICAgICAgICAgIH0sDQogICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgIHNob3c6IHRydWUsDQogICAgICAgICAgICBwb3NpdGlvbjogJ3RvcCcsDQogICAgICAgICAgICBjb2xvcjogJyNmZmYnLA0KICAgICAgICAgICAgZm9udFNpemU6IDEyLA0KICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgICAgcmV0dXJuIHBhcmFtcy52YWx1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgZW1waGFzaXM6IHsNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogew0KICAgICAgICAgICAgICAgIHR5cGU6ICdsaW5lYXInLA0KICAgICAgICAgICAgICAgIHg6IDAsDQogICAgICAgICAgICAgICAgeTogMCwNCiAgICAgICAgICAgICAgICB4MjogMCwNCiAgICAgICAgICAgICAgICB5MjogMSwNCiAgICAgICAgICAgICAgICBjb2xvclN0b3BzOiBbew0KICAgICAgICAgICAgICAgICAgb2Zmc2V0OiAwLCBjb2xvcjogJyMzM0M3RkYnIC8vIOmrmOS6rumhtumDqOminOiJsg0KICAgICAgICAgICAgICAgIH0sIHsNCiAgICAgICAgICAgICAgICAgIG9mZnNldDogMSwgY29sb3I6ICcjMDA5OUREJyAvLyDpq5jkuq7lupXpg6jpopzoibINCiAgICAgICAgICAgICAgICB9XSwNCiAgICAgICAgICAgICAgICBnbG9iYWw6IGZhbHNlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH1dLA0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMCwgMCwgMCwgMC44KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjMDBCQUZGJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicNCiAgICAgICAgICB9LA0KICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7DQogICAgICAgICAgICBjb25zdCBwYXJhbSA9IHBhcmFtc1swXQ0KICAgICAgICAgICAgcmV0dXJuIGAke3BhcmFtLm5hbWV9OiAke3BhcmFtLnZhbHVlfWANCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY2hhcnQuc2V0T3B0aW9uKG9wdGlvbikNCiAgICB9LA0KDQogICAgLy8g5L6b5pa5566h55CG6aW85Zu+DQogICAgaW5pdFN1cHBsaWVyTWFuYWdlbWVudENoYXJ0KCkgew0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBjb25zdCBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdzdXBwbGllck1hbmFnZW1lbnRDaGFydCcpDQogICAgICAgIGlmICghY2hhcnREb20pIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfmib7kuI3liLDkvpvmlrnnrqHnkIblm77ooahET03lhYPntKAnKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgY2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnREb20pDQogICAgICAgIHRoaXMuY2hhcnRzLnN1cHBsaWVyTWFuYWdlbWVudCA9IGNoYXJ0DQoNCiAgICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JywNCiAgICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgICB0cmlnZ2VyOiAnaXRlbScsDQogICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDAsMCwwLDAuOCknLA0KICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjMDBCQUZGJywNCiAgICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICAgIHR5cGU6ICdwaWUnLA0KICAgICAgICAgICAgcmFkaXVzOiAnNjAlJywNCiAgICAgICAgICAgIGNlbnRlcjogWyc1MCUnLCAnNjAlJ10sDQogICAgICAgICAgICBkYXRhOiBbDQogICAgICAgICAgICAgIHsgdmFsdWU6IDQ1LCBuYW1lOiAn5bel56iLJyB9LA0KICAgICAgICAgICAgICB7IHZhbHVlOiAzNSwgbmFtZTogJ+i0p+eJqScgfSwNCiAgICAgICAgICAgICAgeyB2YWx1ZTogMjAsIG5hbWU6ICfmnI3liqEnIH0NCiAgICAgICAgICAgIF0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6IGZ1bmN0aW9uKHBhcmFtcykgew0KICAgICAgICAgICAgICAgIGNvbnN0IGNvbG9ycyA9IFsnI0ZGNkI2QicsICcjNEVDREM0JywgJyM0NUI3RDEnXQ0KICAgICAgICAgICAgICAgIHJldHVybiBjb2xvcnNbcGFyYW1zLmRhdGFJbmRleCAlIGNvbG9ycy5sZW5ndGhdDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBsYWJlbDogew0KICAgICAgICAgICAgICBjb2xvcjogJyNmZmYnLA0KICAgICAgICAgICAgICBmb250U2l6ZTogMTUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9XQ0KICAgICAgICB9DQoNCiAgICAgICAgY2hhcnQuc2V0T3B0aW9uKG9wdGlvbikNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOabtOaWsOi1hOmHkeeuoeeQhuWbvuihqOaVsOaNrg0KICAgIHVwZGF0ZUZ1bmRNYW5hZ2VtZW50Q2hhcnQoKSB7DQogICAgICBpZiAoIXRoaXMuY2hhcnRzLmZ1bmRNYW5hZ2VtZW50KSByZXR1cm4NCg0KICAgICAgY29uc3QgbmV3RGF0YSA9IFsNCiAgICAgICAgcGFyc2VGbG9hdCh0aGlzLmZ1bmRNYW5hZ2VtZW50Lm5leHRNb250aCkgfHwgMCwNCiAgICAgICAgcGFyc2VGbG9hdCh0aGlzLmZ1bmRNYW5hZ2VtZW50LnR3b01vbnRoc0xhdGVyKSB8fCAwLA0KICAgICAgICBwYXJzZUZsb2F0KHRoaXMuZnVuZE1hbmFnZW1lbnQudGhyZWVNb250aHNMYXRlcikgfHwgMA0KICAgICAgXQ0KDQogICAgICB0aGlzLmNoYXJ0cy5mdW5kTWFuYWdlbWVudC5zZXRPcHRpb24oew0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgZGF0YTogbmV3RGF0YQ0KICAgICAgICB9XQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5bqT5a2Y566h55CG5Zu+6KGoDQogICAgaW5pdFBlcnNvbmFsQ29uc3VtcHRpb25DaGFydCgpIHsNCiAgICAgIGNvbnN0IGNoYXJ0RG9tID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3BlcnNvbmFsQ29uc3VtcHRpb25DaGFydCcpDQogICAgICBpZiAoIWNoYXJ0RG9tKSByZXR1cm4NCg0KICAgICAgY29uc3QgY2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnREb20pDQogICAgICB0aGlzLmNoYXJ0cy5wZXJzb25hbENvbnN1bXB0aW9uID0gY2hhcnQNCg0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsDQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnaXRlbScsDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgwLDAsMCwwLjgpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyMwMEJBRkYnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0NCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbew0KICAgICAgICAgIHR5cGU6ICdwaWUnLA0KICAgICAgICAgIHJhZGl1czogWyc0MCUnLCAnNzAlJ10sDQogICAgICAgICAgY2VudGVyOiBbJzUwJScsICc1MCUnXSwNCiAgICAgICAgICBkYXRhOiBbDQogICAgICAgICAgICB7IHZhbHVlOiAzMzUsIG5hbWU6ICfnn7/mlpknIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAzMTAsIG5hbWU6ICflkIjph5EnIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAyMzQsIG5hbWU6ICfnhKbngq0nIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAxMzUsIG5hbWU6ICfovoXmlpkv55S15p6BJyB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgIGNvbG9yOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgICAgY29uc3QgY29sb3JzID0gWycjMDBCQUZGJywgJyMzREU3QzknLCAnI0ZGQzEwNycsICcjRkY2QjZCJ10NCiAgICAgICAgICAgICAgcmV0dXJuIGNvbG9yc1twYXJhbXMuZGF0YUluZGV4ICUgY29sb3JzLmxlbmd0aF0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgICBjb2xvcjogJyNmZmYnLA0KICAgICAgICAgICAgZm9udFNpemU6IDEyDQogICAgICAgICAgfQ0KICAgICAgICB9XQ0KICAgICAgfQ0KDQogICAgICBjaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgIH0sDQoNCiAgICAvLyDkvpvmlrnnrqHnkIblm77ooagNCiAgICBpbml0UHVyY2hhc2VBbmFseXNpc0NoYXJ0KCkgew0KICAgICAgY29uc3QgY2hhcnREb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgncHVyY2hhc2VBbmFseXNpc0NoYXJ0JykNCiAgICAgIGlmICghY2hhcnREb20pIHJldHVybg0KDQogICAgICBjb25zdCBjaGFydCA9IGVjaGFydHMuaW5pdChjaGFydERvbSkNCiAgICAgIHRoaXMuY2hhcnRzLnB1cmNoYXNlQW5hbHlzaXMgPSBjaGFydA0KDQogICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JywNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDAsMCwwLDAuOCknLA0KICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzAwQkFGRicsDQogICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI2ZmZicgfQ0KICAgICAgICB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogWycx5pyIJywgJzLmnIgnLCAnM+aciCcsICc05pyIJywgJzXmnIgnLCAnNuaciCddLA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMwMEJBRkYnIH0gfSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjZmZmJyB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjMDBCQUZGJyB9IH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnI2ZmZicgfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAncmdiYSgwLDE4NiwyNTUsMC4yKScgfSB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICBkYXRhOiBbMTIwLCAyMDAsIDE1MCwgODAsIDcwLCAxMTBdLA0KICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICBzbW9vdGg6IHRydWUsDQogICAgICAgICAgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzAwQkFGRicsIHdpZHRoOiAyIH0sDQogICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzAwQkFGRicgfSwNCiAgICAgICAgICBhcmVhU3R5bGU6IHsNCiAgICAgICAgICAgIGNvbG9yOiB7DQogICAgICAgICAgICAgIHR5cGU6ICdsaW5lYXInLA0KICAgICAgICAgICAgICB4OiAwLCB5OiAwLCB4MjogMCwgeTI6IDEsDQogICAgICAgICAgICAgIGNvbG9yU3RvcHM6IFsNCiAgICAgICAgICAgICAgICB7IG9mZnNldDogMCwgY29sb3I6ICdyZ2JhKDAsMTg2LDI1NSwwLjMpJyB9LA0KICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAxLCBjb2xvcjogJ3JnYmEoMCwxODYsMjU1LDAuMSknIH0NCiAgICAgICAgICAgICAgXQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfV0NCiAgICAgIH0NCg0KICAgICAgY2hhcnQuc2V0T3B0aW9uKG9wdGlvbikNCiAgICB9LA0KDQogICAgLy8g5ZCI5ZCM566h55CG5p+x54q25Zu+DQogICAgaW5pdENvbnRyYWN0Q2hhcnQoKSB7DQogICAgICBjb25zdCBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdjb250cmFjdENoYXJ0JykNCiAgICAgIGlmICghY2hhcnREb20pIHJldHVybg0KDQogICAgICBjb25zdCBjaGFydCA9IGVjaGFydHMuaW5pdChjaGFydERvbSkNCiAgICAgIHRoaXMuY2hhcnRzLmNvbnRyYWN0ID0gY2hhcnQNCg0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsDQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgwLDAsMCwwLjgpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyMwMEJBRkYnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0sDQogICAgICAgICAgYXhpc1BvaW50ZXI6IHsNCiAgICAgICAgICAgIHR5cGU6ICdzaGFkb3cnDQogICAgICAgICAgfSwNCiAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uKHBhcmFtcykgew0KICAgICAgICAgICAgY29uc3QgZGF0YSA9IHBhcmFtc1swXQ0KICAgICAgICAgICAgbGV0IHZhbHVlID0gZGF0YS52YWx1ZQ0KICAgICAgICAgICAgbGV0IGZvcm1hdHRlZFZhbHVlID0gJycNCiAgICAgICAgICAgIGlmICh2YWx1ZSA+PSAxMDAwMDAwMCkgew0KICAgICAgICAgICAgICBmb3JtYXR0ZWRWYWx1ZSA9ICh2YWx1ZSAvIDEwMDAwMDAwKS50b0ZpeGVkKDEpICsgJ+WNg+S4hycNCiAgICAgICAgICAgIH0gZWxzZSBpZiAodmFsdWUgPj0gMTAwMDApIHsNCiAgICAgICAgICAgICAgZm9ybWF0dGVkVmFsdWUgPSAodmFsdWUgLyAxMDAwMCkudG9GaXhlZCgxKSArICfkuIcnDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBmb3JtYXR0ZWRWYWx1ZSA9IHZhbHVlLnRvU3RyaW5nKCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHJldHVybiBgJHtkYXRhLm5hbWV9PGJyLz7lkIjlkIzmlbDph486ICR7Zm9ybWF0dGVkVmFsdWV9YA0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogew0KICAgICAgICAgIGxlZnQ6ICczJScsDQogICAgICAgICAgcmlnaHQ6ICc0JScsDQogICAgICAgICAgYm90dG9tOiAnMjAlJywNCiAgICAgICAgICB0b3A6ICcyNSUnLA0KICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogdGhpcy5jb250cmFjdERhdGEubWFwKGl0ZW0gPT4gaXRlbS5uYW1lKSwNCiAgICAgICAgICBheGlzTGluZTogew0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnI2VlZScNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgY29sb3I6ICcjZWVlJywNCiAgICAgICAgICAgIGludGVydmFsOiAwLA0KICAgICAgICAgICAgcm90YXRlOiAzMCwNCiAgICAgICAgICAgIGZvbnRTaXplOiAxMA0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgIG5hbWU6ICflkIjlkIzmlbDph48nLA0KICAgICAgICAgIG5hbWVUZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsDQogICAgICAgICAgICBhbGlnbjogJ3JpZ2h0Jw0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBjb2xvcjogJyNlZWUnLA0KICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbih2YWx1ZSkgew0KICAgICAgICAgICAgICBpZiAodmFsdWUgPj0gMTAwMDAwMDApIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gKHZhbHVlIC8gMTAwMDAwMDApLnRvRml4ZWQoMSkgKyAn5Y2D5LiHJw0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKHZhbHVlID49IDEwMDAwKSB7DQogICAgICAgICAgICAgICAgcmV0dXJuICh2YWx1ZSAvIDEwMDAwKS50b0ZpeGVkKDEpICsgJ+S4hycNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyNlZWUnDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC4xKScNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICBuYW1lOiAn5ZCI5ZCM5pWw6YePJywNCiAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICBkYXRhOiB0aGlzLmNvbnRyYWN0RGF0YS5tYXAoaXRlbSA9PiBpdGVtLmNvdW50KSwNCiAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnIzgzYmZmNicsDQogICAgICAgICAgICBib3JkZXJSYWRpdXM6IFs0LCA0LCAwLCAwXSAgICAgICAgICAgIA0KICAgICAgICAgIH0sDQogICAgICAgICAgZW1waGFzaXM6IHsNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyM4M2JmZjYnLA0KICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IFs0LCA0LCAwLCAwXSwNCiAgICAgICAgICAgICAgc2hhZG93Qmx1cjogMTAsDQogICAgICAgICAgICAgIHNoYWRvd0NvbG9yOiAncmdiYSgyNTUsIDI1NSwgMjU1LCAwLjUpJywNCiAgICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDIsDQogICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnI2ZmZicNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH1dDQogICAgICB9DQoNCiAgICAgIGNoYXJ0LnNldE9wdGlvbihvcHRpb24pDQogICAgfSwNCg0KICAgIC8vIOi2i+WKv+WIhuaekOWbvuihqA0KICAgIGluaXRUcmVuZENoYXJ0KCkgew0KICAgICAgY29uc3QgY2hhcnREb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgndHJlbmRDaGFydCcpDQogICAgICBpZiAoIWNoYXJ0RG9tKSByZXR1cm4NCg0KICAgICAgY29uc3QgY2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnREb20pDQogICAgICB0aGlzLmNoYXJ0cy50cmVuZCA9IGNoYXJ0DQoNCiAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgYmFja2dyb3VuZENvbG9yOiAndHJhbnNwYXJlbnQnLA0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMCwwLDAsMC44KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjMDBCQUZGJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9DQogICAgICAgIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBbJ+WRqOS4gCcsICflkajkuownLCAn5ZGo5LiJJywgJ+WRqOWbmycsICflkajkupQnLCAn5ZGo5YWtJywgJ+WRqOaXpSddLA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMwMEJBRkYnIH0gfSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjZmZmJywgZm9udFNpemU6IDEwIH0NCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMwMEJBRkYnIH0gfSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjZmZmJywgZm9udFNpemU6IDEwIH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJ3JnYmEoMCwxODYsMjU1LDAuMiknIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgZGF0YTogWzgyMCwgOTMyLCA5MDEsIDkzNCwgMTI5MCwgMTMzMCwgMTMyMF0sDQogICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgIHNtb290aDogdHJ1ZSwNCiAgICAgICAgICBsaW5lU3R5bGU6IHsgY29sb3I6ICcjM0RFN0M5Jywgd2lkdGg6IDIgfSwNCiAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjM0RFN0M5JyB9LA0KICAgICAgICAgIGFyZWFTdHlsZTogew0KICAgICAgICAgICAgY29sb3I6IHsNCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmVhcicsDQogICAgICAgICAgICAgIHg6IDAsIHk6IDAsIHgyOiAwLCB5MjogMSwNCiAgICAgICAgICAgICAgY29sb3JTdG9wczogWw0KICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAwLCBjb2xvcjogJ3JnYmEoNjEsMjMxLDIwMSwwLjMpJyB9LA0KICAgICAgICAgICAgICAgIHsgb2Zmc2V0OiAxLCBjb2xvcjogJ3JnYmEoNjEsMjMxLDIwMSwwLjEpJyB9DQogICAgICAgICAgICAgIF0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH1dDQogICAgICB9DQoNCiAgICAgIGNoYXJ0LnNldE9wdGlvbihvcHRpb24pDQogICAgfSwNCg0KICAgIC8vIOWNleS4gOadpea6kOWbvuihqA0KICAgIGluaXRTdXBwbGllckNoYXJ0KCkgew0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICBjb25zdCBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdzdXBwbGllckNoYXJ0JykNCiAgICAgICAgY29uc29sZS5sb2coJ+WNleS4gOadpea6kOWbvuihqERPTeWFg+e0oDonLCBjaGFydERvbSkNCg0KICAgICAgICBpZiAoIWNoYXJ0RG9tKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5om+5LiN5Yiw5Y2V5LiA5p2l5rqQ5Zu+6KGoRE9N5YWD57SgJykNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IGNoYXJ0ID0gZWNoYXJ0cy5pbml0KGNoYXJ0RG9tKQ0KICAgICAgICB0aGlzLmNoYXJ0cy5zdXBwbGllciA9IGNoYXJ0DQoNCiAgICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JywNCiAgICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgICB0cmlnZ2VyOiAnaXRlbScsDQogICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDAsMCwwLDAuOCknLA0KICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjMDBCQUZGJywNCiAgICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICAgIHR5cGU6ICdwaWUnLA0KICAgICAgICAgICAgcmFkaXVzOiAnNTAlJywNCiAgICAgICAgICAgIGNlbnRlcjogWyc1MCUnLCAnMzAlJ10sDQogICAgICAgICAgICBkYXRhOiBbDQogICAgICAgICAgICAgIHsgdmFsdWU6IDQwLCBuYW1lOiAn5bel56iLJyB9LA0KICAgICAgICAgICAgICB7IHZhbHVlOiAzMCwgbmFtZTogJ+i0p+eJqScgfSwNCiAgICAgICAgICAgICAgeyB2YWx1ZTogMzAsIG5hbWU6ICfmnI3liqEnIH0NCiAgICAgICAgICAgIF0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6IGZ1bmN0aW9uKHBhcmFtcykgew0KICAgICAgICAgICAgICAgIGNvbnN0IGNvbG9ycyA9IFsnI0ZGNkI2QicsICcjNEVDREM0JywgJyM0NUI3RDEnLCAnIzk2Q0VCNCddDQogICAgICAgICAgICAgICAgcmV0dXJuIGNvbG9yc1twYXJhbXMuZGF0YUluZGV4ICUgY29sb3JzLmxlbmd0aF0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGxhYmVsOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsDQogICAgICAgICAgICAgIGZvbnRTaXplOiAxMA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH1dDQogICAgICAgIH0NCg0KICAgICAgICBjaGFydC5zZXRPcHRpb24ob3B0aW9uKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g6I635Y+W6auY6aKR54mp6LWE5pWw5o2uDQogICAgYXN5bmMgZmV0Y2hIaWdoRnJlcXVlbmN5RGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOiOt+WPlumrmOmikemHh+i0reeJqeaWmeaVsOaNrg0KICAgICAgICBhd2FpdCB0aGlzLmZldGNoSGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YSgpDQogICAgICAgIC8vIOiOt+WPluS7t+agvOi2i+WKv+aVsOaNrg0KICAgICAgICBhd2FpdCB0aGlzLmZldGNoUHJpY2VBbmRTdG9yZURhdGEoKQ0KICAgICAgICAvLyDliJ3lp4vljJbor43kupHlkozku7fmoLzotovlir/lm74NCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHRoaXMuaW5pdEhpZ2hGcmVxdWVuY3lNYXRlcmlhbENsb3VkKCkNCiAgICAgICAgICB0aGlzLmluaXRIaWdoRnJlcXVlbmN5UHJpY2VUcmVuZENoYXJ0KCkNCiAgICAgICAgfSkNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumrmOmikeeJqei1hOaVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6I635Y+W6auY6aKR6YeH6LSt54mp5paZ5pWw5o2uDQogICAgYXN5bmMgZmV0Y2hIaWdoRnJlcXVlbmN5TWF0ZXJpYWxEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIGRpbWVuc2lvblR5cGU6IHRoaXMuY3VycmVudERpbWVuc2lvblR5cGUsDQogICAgICAgICAgY29kZVR5cGU6IHRoaXMuc2VsZWN0ZWRDb2RlVHlwZSwNCiAgICAgICAgICBpdGVtVHlwZTogdGhpcy5zZWxlY3RlZEl0ZW1UeXBlDQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHNob3dIaWdoRnJlcXVlbmN5TWF0ZXJpYWxMaXN0KHBhcmFtcykNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICAvLyDlj5bliY0xMOadoeaVsOaNrg0KICAgICAgICAgIHRoaXMuaGlnaEZyZXF1ZW5jeU1hdGVyaWFsTGlzdCA9IChyZXNwb25zZS5kYXRhIHx8IFtdKS5zbGljZSgwLCAxMCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDkvb/nlKjmqKHmi5/mlbDmja4NCiAgICAgICAgICB0aGlzLmhpZ2hGcmVxdWVuY3lNYXRlcmlhbExpc3QgPSB0aGlzLmdldE1vY2tIaWdoRnJlcXVlbmN5RGF0YSgpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumrmOmikeeJqeaWmeaVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5oaWdoRnJlcXVlbmN5TWF0ZXJpYWxMaXN0ID0gdGhpcy5nZXRNb2NrSGlnaEZyZXF1ZW5jeURhdGEoKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliJ3lp4vljJbpq5jpopHnianmlpnor43kupENCiAgICBpbml0SGlnaEZyZXF1ZW5jeU1hdGVyaWFsQ2xvdWQoKSB7DQogICAgICBjb25zdCBjaGFydERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdoaWdoRnJlcXVlbmN5TWF0ZXJpYWxDbG91ZCcpDQogICAgICBpZiAoIWNoYXJ0RG9tKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aJvuS4jeWIsOmrmOmikeeJqeaWmeivjeS6kURPTeWFg+e0oCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBjaGFydERvbS5pbm5lckhUTUwgPSAnJw0KDQogICAgICBjb25zdCByYXdNYXRlcmlhbExpc3QgPSB0aGlzLmhpZ2hGcmVxdWVuY3lNYXRlcmlhbExpc3QNCiAgICAgIGlmICghcmF3TWF0ZXJpYWxMaXN0IHx8IHJhd01hdGVyaWFsTGlzdC5sZW5ndGggPT09IDApIHsNCiAgICAgICAgY2hhcnREb20uaW5uZXJIVE1MID0gJzxkaXYgY2xhc3M9ImNoYXJ0LXBsYWNlaG9sZGVyIj7ml6Dpq5jpopHph4fotK3nianmlpnmlbDmja48L2Rpdj4nDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDmjInlhaXlupPph5Hpop3mjpLluo/vvIzlj5bliY0xNeadoQ0KICAgICAgY29uc3QgaGlnaEZyZXF1ZW5jeU1hdGVyaWFscyA9IHJhd01hdGVyaWFsTGlzdA0KICAgICAgICAuc29ydCgoYSwgYikgPT4gKGIuaW5BbXQgfHwgMCkgLSAoYS5pbkFtdCB8fCAwKSkNCiAgICAgICAgLnNsaWNlKDAsIDE1KQ0KDQogICAgICBjb25zdCBjb250YWluZXIgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdkaXYnKQ0KICAgICAgY29udGFpbmVyLnN0eWxlLndpZHRoID0gJzEwMCUnDQogICAgICBjb250YWluZXIuc3R5bGUuaGVpZ2h0ID0gJzEwMCUnDQogICAgICBjb250YWluZXIuc3R5bGUucG9zaXRpb24gPSAncmVsYXRpdmUnDQogICAgICBjb250YWluZXIuc3R5bGUub3ZlcmZsb3cgPSAnaGlkZGVuJw0KDQogICAgICBjb25zdCBjb2xvcnMgPSBbDQogICAgICAgICcjNGZjM2Y3JywgJyNhNWQ2YTcnLCAnI2ZmY2M4MCcsICcjZWY5YTlhJywgJyNjZTkzZDgnLA0KICAgICAgICAnIzkwY2FmOScsICcjODBkZWVhJywgJyNjNWUxYTUnLCAnI2ZmZjU5ZCcsICcjZmZhYjkxJw0KICAgICAgXQ0KDQogICAgICBjb25zdCBtYXhGb250U2l6ZSA9IDI0DQogICAgICBjb25zdCBtaW5Gb250U2l6ZSA9IDEyDQoNCiAgICAgIGhpZ2hGcmVxdWVuY3lNYXRlcmlhbHMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgbGV0IGZvbnRTaXplID0gbWF4Rm9udFNpemUgLSAoaW5kZXggKiAxLjIpDQogICAgICAgIGlmIChmb250U2l6ZSA8IG1pbkZvbnRTaXplKSB7DQogICAgICAgICAgZm9udFNpemUgPSBtaW5Gb250U2l6ZQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgZGl2ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2JykNCiAgICAgICAgZGl2LnRleHRDb250ZW50ID0gaXRlbS5pdGVtTmFtZQ0KICAgICAgICBkaXYuc3R5bGUucG9zaXRpb24gPSAnYWJzb2x1dGUnDQogICAgICAgIGRpdi5zdHlsZS5mb250U2l6ZSA9IGAke2ZvbnRTaXplfXB4YA0KICAgICAgICBkaXYuc3R5bGUuZm9udFdlaWdodCA9ICdib2xkJw0KICAgICAgICBkaXYuc3R5bGUuY29sb3IgPSBjb2xvcnNbaW5kZXggJSBjb2xvcnMubGVuZ3RoXQ0KICAgICAgICBkaXYuc3R5bGUudHJhbnNmb3JtID0gYHJvdGF0ZSgke01hdGgucmFuZG9tKCkgKiAzMCAtIDE1fWRlZylgDQogICAgICAgIGRpdi5zdHlsZS5sZWZ0ID0gYCR7MTAgKyBNYXRoLnJhbmRvbSgpICogNjB9JWANCiAgICAgICAgZGl2LnN0eWxlLnRvcCA9IGAkezEwICsgTWF0aC5yYW5kb20oKSAqIDYwfSVgDQogICAgICAgIGRpdi5zdHlsZS53aGl0ZVNwYWNlID0gJ25vd3JhcCcNCiAgICAgICAgZGl2LnN0eWxlLnRleHRTaGFkb3cgPSAnMXB4IDFweCAycHggcmdiYSgwLDAsMCwwLjMpJw0KICAgICAgICBkaXYuc3R5bGUudHJhbnNpdGlvbiA9ICdhbGwgMC4zcyBlYXNlJw0KICAgICAgICBkaXYuc3R5bGUuY3Vyc29yID0gJ3BvaW50ZXInDQogICAgICAgIGRpdi5zdHlsZS56SW5kZXggPSBoaWdoRnJlcXVlbmN5TWF0ZXJpYWxzLmxlbmd0aCAtIGluZGV4DQoNCiAgICAgICAgLy8g5re75Yqg5oKs5YGc5pWI5p6cDQogICAgICAgIGNvbnN0IHNlbGYgPSB0aGlzDQogICAgICAgIGRpdi5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWVudGVyJywgZnVuY3Rpb24oKSB7DQogICAgICAgICAgdGhpcy5zdHlsZS50cmFuc2Zvcm0gPSBgcm90YXRlKCR7TWF0aC5yYW5kb20oKSAqIDMwIC0gMTV9ZGVnKSBzY2FsZSgxLjEpYA0KICAgICAgICAgIHRoaXMuc3R5bGUuekluZGV4ID0gJzk5OScNCiAgICAgICAgICB0aGlzLnN0eWxlLnRleHRTaGFkb3cgPSAnMnB4IDJweCA0cHggcmdiYSgwLDAsMCwwLjUpJw0KDQogICAgICAgICAgLy8g5pi+56S66YeR6aKd5o+Q56S6DQogICAgICAgICAgY29uc3QgdG9vbHRpcCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2RpdicpDQogICAgICAgICAgdG9vbHRpcC5jbGFzc05hbWUgPSAnbWF0ZXJpYWwtdG9vbHRpcCcNCiAgICAgICAgICB0b29sdGlwLnRleHRDb250ZW50ID0gYOmHkeminTogJHtzZWxmLmZvcm1hdEFtb3VudChpdGVtLmluQW10KX1gDQogICAgICAgICAgdG9vbHRpcC5zdHlsZS5wb3NpdGlvbiA9ICdhYnNvbHV0ZScNCiAgICAgICAgICB0b29sdGlwLnN0eWxlLmJvdHRvbSA9ICctMjVweCcNCiAgICAgICAgICB0b29sdGlwLnN0eWxlLmxlZnQgPSAnNTAlJw0KICAgICAgICAgIHRvb2x0aXAuc3R5bGUudHJhbnNmb3JtID0gJ3RyYW5zbGF0ZVgoLTUwJSknDQogICAgICAgICAgdG9vbHRpcC5zdHlsZS5iYWNrZ3JvdW5kID0gJ3JnYmEoMCwwLDAsMC44KScNCiAgICAgICAgICB0b29sdGlwLnN0eWxlLmNvbG9yID0gJyNmZmYnDQogICAgICAgICAgdG9vbHRpcC5zdHlsZS5wYWRkaW5nID0gJzJweCA2cHgnDQogICAgICAgICAgdG9vbHRpcC5zdHlsZS5ib3JkZXJSYWRpdXMgPSAnM3B4Jw0KICAgICAgICAgIHRvb2x0aXAuc3R5bGUuZm9udFNpemUgPSAnMTBweCcNCiAgICAgICAgICB0b29sdGlwLnN0eWxlLndoaXRlU3BhY2UgPSAnbm93cmFwJw0KICAgICAgICAgIHRvb2x0aXAuc3R5bGUuekluZGV4ID0gJzEwMDAnDQogICAgICAgICAgdGhpcy5hcHBlbmRDaGlsZCh0b29sdGlwKQ0KICAgICAgICB9KQ0KDQogICAgICAgIGRpdi5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWxlYXZlJywgZnVuY3Rpb24oKSB7DQogICAgICAgICAgdGhpcy5zdHlsZS50cmFuc2Zvcm0gPSBgcm90YXRlKCR7TWF0aC5yYW5kb20oKSAqIDMwIC0gMTV9ZGVnKSBzY2FsZSgxKWANCiAgICAgICAgICB0aGlzLnN0eWxlLnpJbmRleCA9IChoaWdoRnJlcXVlbmN5TWF0ZXJpYWxzLmxlbmd0aCAtIGluZGV4KS50b1N0cmluZygpDQogICAgICAgICAgdGhpcy5zdHlsZS50ZXh0U2hhZG93ID0gJzFweCAxcHggMnB4IHJnYmEoMCwwLDAsMC4zKScNCg0KICAgICAgICAgIGNvbnN0IHRvb2x0aXAgPSB0aGlzLnF1ZXJ5U2VsZWN0b3IoJy5tYXRlcmlhbC10b29sdGlwJykNCiAgICAgICAgICBpZiAodG9vbHRpcCkgew0KICAgICAgICAgICAgdGhpcy5yZW1vdmVDaGlsZCh0b29sdGlwKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCg0KICAgICAgICBjb250YWluZXIuYXBwZW5kQ2hpbGQoZGl2KQ0KICAgICAgfSkNCg0KICAgICAgY2hhcnREb20uYXBwZW5kQ2hpbGQoY29udGFpbmVyKQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5bmqKHmi5/pq5jpopHnianmlpnmlbDmja4NCiAgICBnZXRNb2NrSGlnaEZyZXF1ZW5jeURhdGEoKSB7DQogICAgICByZXR1cm4gWw0KICAgICAgICB7IGl0ZW1OYW1lOiAn57KX57KJJywgaW5BbXQ6IDM5MjQ2Ny4yLCBpbk51bTogNTQyMTI5MyB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn57K+57KJJywgaW5BbXQ6IDI4MDM1MC41LCBpbk51bTogNDI1MDE4MCB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn55CD5ZuiJywgaW5BbXQ6IDE5NTIwMC44LCBpbk51bTogMzE4MDk3MCB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn54On57uTJywgaW5BbXQ6IDE1MDQyMC4zLCBpbk51bTogMjg5MDU0MCB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn54Sm54KtJywgaW5BbXQ6IDEyNTY4MC43LCBpbk51bTogMjM1MDIxMCB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn54Wk54KtJywgaW5BbXQ6IDk4NzUwLjIsIGluTnVtOiAxOTgwNzYwIH0sDQogICAgICAgIHsgaXRlbU5hbWU6ICdQQuWdlycsIGluQW10OiA4NTQyMC4xLCBpbk51bTogMTY1MDQzMCB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn6ZOB55+/55+zJywgaW5BbXQ6IDcyMzUwLjgsIGluTnVtOiAxNDIwODkwIH0sDQogICAgICAgIHsgaXRlbU5hbWU6ICflup/pkqInLCBpbkFtdDogNjUyODAuNCwgaW5OdW06IDEyODA1NjAgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+efs+eBsOefsycsIGluQW10OiA1ODE5MC42LCBpbk51bTogMTE1MDMyMCB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn5ZCI6YeRJywgaW5BbXQ6IDUyMTgwLjMsIGluTnVtOiA5ODA0NTAgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+eUteaegScsIGluQW10OiA0ODc1MC45LCBpbk51bTogODUwMzIwIH0sDQogICAgICAgIHsgaXRlbU5hbWU6ICfogJDngavmnZDmlpknLCBpbkFtdDogNDIzNTAuNywgaW5OdW06IDcyMDE4MCB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn5YyW5bel5Y6f5paZJywgaW5BbXQ6IDM4OTIwLjUsIGluTnVtOiA2NTAyOTAgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+i+heaWmScsIGluQW10OiAzNTY4MC4yLCBpbk51bTogNTgwMTYwIH0NCiAgICAgIF0NCiAgICB9LA0KDQogICAgLy8g6I635Y+W54mp5paZ5Lu35qC85ZKM6YeH6LSt6YeP5pWw5o2uDQogICAgYXN5bmMgZmV0Y2hQcmljZUFuZFN0b3JlRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICBkaW1lbnNpb25UeXBlOiB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlLA0KICAgICAgICAgIGl0ZW1OYW1lOiAnUELlnZcnICAvLyDlm7rlrprojrflj5ZQQuWdl+aVsOaNrg0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRQdXJjaGFzZVByaWNlQW5kU3RvcmUocGFyYW1zKQ0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpICYmIHJlc3BvbnNlLmRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMucHJpY2VBbmRTdG9yZURhdGEgPSByZXNwb25zZS5kYXRhWzBdDQogICAgICAgICAgY29uc29sZS5sb2coJ+iOt+WPluWIsFBC5Z2X5Lu35qC85ZKM6YeH6LSt6YeP5pWw5o2uOicsIHRoaXMucHJpY2VBbmRTdG9yZURhdGEpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5sb2coJ+acquiOt+WPluWIsOecn+WunuaVsOaNru+8jOS9v+eUqOaooeaLn+aVsOaNricpDQogICAgICAgICAgdGhpcy5wcmljZUFuZFN0b3JlRGF0YSA9IHRoaXMuZ2V0TW9ja1ByaWNlQW5kU3RvcmVEYXRhKCkNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5Lu35qC85ZKM6YeH6LSt6YeP5pWw5o2u5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLnByaWNlQW5kU3RvcmVEYXRhID0gdGhpcy5nZXRNb2NrUHJpY2VBbmRTdG9yZURhdGEoKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5bmqKHmi5/nmoTku7fmoLzlkozph4fotK3ph4/mlbDmja7vvIjlj4LogIPph4fotK3nnIvmnb/mlbDmja7nu5PmnoTvvIkNCiAgICBnZXRNb2NrUHJpY2VBbmRTdG9yZURhdGEoKSB7DQogICAgICByZXR1cm4gew0KICAgICAgICBwcm9jdXJlbWVudFByaWNlVm9MaXN0OiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcHJpY2VOYW1lOiAnUELlnZfku7fmoLwnLA0KICAgICAgICAgICAgcHJpY2VMaXN0OiBbDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjQwODAxJywgcHJpY2U6IDg1MC4wIH0sDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjQwODE1JywgcHJpY2U6IDg3MC41IH0sDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjQwOTAxJywgcHJpY2U6IDg5MC4yIH0sDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjQwOTE1JywgcHJpY2U6IDg3NS44IH0sDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjQxMDAxJywgcHJpY2U6IDkyMC4zIH0sDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjQxMDE1JywgcHJpY2U6IDkwNS43IH0sDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjQxMTAxJywgcHJpY2U6IDg4MC40IH0sDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjQxMTE1JywgcHJpY2U6IDg5NS42IH0sDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjQxMTIzJywgcHJpY2U6IDkxMC4yIH0sICAvLyAyMDI05bm0MTHmnIgyM+aXpeS7t+agvA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI0MTIwMScsIHByaWNlOiA5MjUuOCB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI0MTIxNScsIHByaWNlOiA5NDAuMSB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI1MDEwMScsIHByaWNlOiA5MzAuNSB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI1MDExNScsIHByaWNlOiA5MTUuMyB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI1MDIwMScsIHByaWNlOiA5MDAuNyB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI1MDIxNScsIHByaWNlOiA4ODUuOSB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI1MDMwMScsIHByaWNlOiA4NzAuMiB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI1MDMxNScsIHByaWNlOiA4NTUuOCB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI1MDQwMScsIHByaWNlOiA4NDAuNiB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI1MDQxNScsIHByaWNlOiA4MjUuNCB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI1MDUwMScsIHByaWNlOiA4MTAuOSB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI1MDUxNScsIHByaWNlOiA3OTUuNyB9LA0KICAgICAgICAgICAgICB7IHJlY29yZERhdGU6ICcyMDI1MDYwMScsIHByaWNlOiA3ODAuMyB9DQogICAgICAgICAgICBdDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBwcm9jdXJlbWVudFB1cmNoYXNlQW1vdW50Vm9MaXN0OiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgYW1vdW50TmFtZTogJ1BC5Z2X6YeH6LSt6YePJywNCiAgICAgICAgICAgIGFtb3VudExpc3Q6IFsNCiAgICAgICAgICAgICAgeyByZWNvcmREYXRlOiAnMjAyNDA4MDEnLCBhbW91bnQ6IDEyNTAwMCB9LCAgIC8vIDEyLjXkuIflkKgNCiAgICAgICAgICAgICAgeyByZWNvcmREYXRlOiAnMjAyNDA4MTUnLCBhbW91bnQ6IDExODAwMCB9LCAgIC8vIDExLjjkuIflkKgNCiAgICAgICAgICAgICAgeyByZWNvcmREYXRlOiAnMjAyNDA5MDEnLCBhbW91bnQ6IDEzMjAwMCB9LCAgIC8vIDEzLjLkuIflkKgNCiAgICAgICAgICAgICAgeyByZWNvcmREYXRlOiAnMjAyNDA5MTUnLCBhbW91bnQ6IDE0NTAwMCB9LCAgIC8vIDE0LjXkuIflkKgNCiAgICAgICAgICAgICAgeyByZWNvcmREYXRlOiAnMjAyNDEwMDEnLCBhbW91bnQ6IDEzODAwMCB9LCAgIC8vIDEzLjjkuIflkKgNCiAgICAgICAgICAgICAgeyByZWNvcmREYXRlOiAnMjAyNDEwMTUnLCBhbW91bnQ6IDE1MjAwMCB9LCAgIC8vIDE1LjLkuIflkKgNCiAgICAgICAgICAgICAgeyByZWNvcmREYXRlOiAnMjAyNDExMDEnLCBhbW91bnQ6IDE2ODAwMCB9LCAgIC8vIDE2LjjkuIflkKgNCiAgICAgICAgICAgICAgeyByZWNvcmREYXRlOiAnMjAyNDExMTUnLCBhbW91bnQ6IDE3NTAwMCB9LCAgIC8vIDE3LjXkuIflkKgNCiAgICAgICAgICAgICAgeyByZWNvcmREYXRlOiAnMjAyNDExMjMnLCBhbW91bnQ6IDEwMDAwMCB9LCAgIC8vIDEw5LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjQxMjAxJywgYW1vdW50OiAxODUwMDAgfSwgICAvLyAxOC415LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjQxMjE1JywgYW1vdW50OiAxOTIwMDAgfSwgICAvLyAxOS4y5LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjUwMTAxJywgYW1vdW50OiAxNzgwMDAgfSwgICAvLyAxNy445LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjUwMTE1JywgYW1vdW50OiAxNjUwMDAgfSwgICAvLyAxNi415LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjUwMjAxJywgYW1vdW50OiAxNTgwMDAgfSwgICAvLyAxNS445LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjUwMjE1JywgYW1vdW50OiAxNDIwMDAgfSwgICAvLyAxNC4y5LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjUwMzAxJywgYW1vdW50OiAxMzUwMDAgfSwgICAvLyAxMy415LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjUwMzE1JywgYW1vdW50OiAxMjgwMDAgfSwgICAvLyAxMi445LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjUwNDAxJywgYW1vdW50OiAxMjEwMDAgfSwgICAvLyAxMi4x5LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjUwNDE1JywgYW1vdW50OiAxMTUwMDAgfSwgICAvLyAxMS415LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjUwNTAxJywgYW1vdW50OiAxMDgwMDAgfSwgICAvLyAxMC445LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjUwNTE1JywgYW1vdW50OiAxMDIwMDAgfSwgICAvLyAxMC4y5LiH5ZCoDQogICAgICAgICAgICAgIHsgcmVjb3JkRGF0ZTogJzIwMjUwNjAxJywgYW1vdW50OiA5NTAwMCB9ICAgICAvLyA5LjXkuIflkKgNCiAgICAgICAgICAgIF0NCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yid5aeL5YyW6auY6aKR54mp6LWE5Lu35qC86LaL5Yq/5Zu+77yI5a6M5YWo5oyJ54Wn5Zu+54mH5pWI5p6c6YeN5YaZ77yJDQogICAgaW5pdEhpZ2hGcmVxdWVuY3lQcmljZVRyZW5kQ2hhcnQoKSB7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIGNvbnN0IGNoYXJ0RG9tID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2hpZ2hGcmVxdWVuY3lQcmljZVRyZW5kQ2hhcnQnKQ0KICAgICAgICBpZiAoIWNoYXJ0RG9tKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5om+5LiN5Yiw6auY6aKR54mp6LWE5Lu35qC86LaL5Yq/5Zu+RE9N5YWD57SgJykNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOa4heeQhueOsOacieWunuS+iw0KICAgICAgICBpZiAodGhpcy5jaGFydHMuaGlnaEZyZXF1ZW5jeVByaWNlVHJlbmQpIHsNCiAgICAgICAgICB0aGlzLmNoYXJ0cy5oaWdoRnJlcXVlbmN5UHJpY2VUcmVuZC5kaXNwb3NlKCkNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IGNoYXJ0ID0gZWNoYXJ0cy5pbml0KGNoYXJ0RG9tKQ0KICAgICAgICB0aGlzLmNoYXJ0cy5oaWdoRnJlcXVlbmN5UHJpY2VUcmVuZCA9IGNoYXJ0DQoNCiAgICAgICAgLy8g5L2/55So55yf5a6e5pWw5o2u57uT5p6EDQogICAgICAgIGNvbnN0IHByaWNlQW5kU3RvcmVEYXRhID0gdGhpcy5wcmljZUFuZFN0b3JlRGF0YQ0KICAgICAgICBpZiAoIXByaWNlQW5kU3RvcmVEYXRhKSB7DQogICAgICAgICAgY2hhcnREb20uaW5uZXJIVE1MID0gJzxkaXYgY2xhc3M9ImNoYXJ0LXBsYWNlaG9sZGVyIj7mmoLml6Dku7fmoLzotovlir/mlbDmja48L2Rpdj4nDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDmlLbpm4bmiYDmnInml6XmnJ8NCiAgICAgICAgbGV0IGFsbERhdGVzID0gbmV3IFNldCgpDQoNCiAgICAgICAgLy8g5LuO5Lu35qC85pWw5o2u5Lit5pS26ZuG5pel5pyfDQogICAgICAgIGlmIChwcmljZUFuZFN0b3JlRGF0YS5wcm9jdXJlbWVudFByaWNlVm9MaXN0KSB7DQogICAgICAgICAgcHJpY2VBbmRTdG9yZURhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdC5mb3JFYWNoKHByaWNlR3JvdXAgPT4gew0KICAgICAgICAgICAgaWYgKHByaWNlR3JvdXAucHJpY2VMaXN0KSB7DQogICAgICAgICAgICAgIHByaWNlR3JvdXAucHJpY2VMaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgICAgYWxsRGF0ZXMuYWRkKGl0ZW0ucmVjb3JkRGF0ZSkNCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5LuO6YeH6LSt6YeP5pWw5o2u5Lit5pS26ZuG5pel5pyfDQogICAgICAgIGlmIChwcmljZUFuZFN0b3JlRGF0YS5wcm9jdXJlbWVudFB1cmNoYXNlQW1vdW50Vm9MaXN0KSB7DQogICAgICAgICAgcHJpY2VBbmRTdG9yZURhdGEucHJvY3VyZW1lbnRQdXJjaGFzZUFtb3VudFZvTGlzdC5mb3JFYWNoKGFtb3VudEdyb3VwID0+IHsNCiAgICAgICAgICAgIGlmIChhbW91bnRHcm91cC5hbW91bnRMaXN0KSB7DQogICAgICAgICAgICAgIGFtb3VudEdyb3VwLmFtb3VudExpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgICBhbGxEYXRlcy5hZGQoaXRlbS5yZWNvcmREYXRlKQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCg0KICAgICAgICAvLyDovazmjaLkuLrmjpLluo/nmoTmlbDnu4QNCiAgICAgICAgYWxsRGF0ZXMgPSBBcnJheS5mcm9tKGFsbERhdGVzKS5zb3J0KCkNCg0KICAgICAgICBpZiAoYWxsRGF0ZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgY2hhcnREb20uaW5uZXJIVE1MID0gJzxkaXYgY2xhc3M9ImNoYXJ0LXBsYWNlaG9sZGVyIj7mmoLml6Dku7fmoLzotovlir/mlbDmja48L2Rpdj4nDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDmnoTlu7rns7vliJfmlbDmja4NCiAgICAgICAgY29uc3Qgc2VyaWVzID0gW10NCiAgICAgICAgY29uc3QgbGVnZW5kRGF0YSA9IFtdDQoNCiAgICAgICAgLy8g5p6E5bu65Lu35qC857O75YiX77yI6JOd6Imy57q/77yJDQogICAgICAgIGlmIChwcmljZUFuZFN0b3JlRGF0YS5wcm9jdXJlbWVudFByaWNlVm9MaXN0KSB7DQogICAgICAgICAgcHJpY2VBbmRTdG9yZURhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdC5mb3JFYWNoKHByaWNlR3JvdXAgPT4gew0KICAgICAgICAgICAgY29uc3QgcHJpY2VEYXRhID0gYWxsRGF0ZXMubWFwKGRhdGUgPT4gew0KICAgICAgICAgICAgICBjb25zdCBmb3VuZCA9IHByaWNlR3JvdXAucHJpY2VMaXN0LmZpbmQoaXRlbSA9PiBpdGVtLnJlY29yZERhdGUgPT09IGRhdGUpDQogICAgICAgICAgICAgIHJldHVybiBmb3VuZCA/IHBhcnNlRmxvYXQoZm91bmQucHJpY2UpIDogbnVsbA0KICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgc2VyaWVzLnB1c2goew0KICAgICAgICAgICAgICBuYW1lOiBwcmljZUdyb3VwLnByaWNlTmFtZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgICB5QXhpc0luZGV4OiAwLA0KICAgICAgICAgICAgICBkYXRhOiBwcmljZURhdGEsDQogICAgICAgICAgICAgIHNtb290aDogdHJ1ZSwNCiAgICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgICAgd2lkdGg6IDIsDQogICAgICAgICAgICAgICAgY29sb3I6ICcjMDBkNGZmJyAgLy8g6JOd6ImyDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICAgIGNvbG9yOiAnIzAwZDRmZicNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgc3ltYm9sOiAnY2lyY2xlJywNCiAgICAgICAgICAgICAgc3ltYm9sU2l6ZTogNCwNCiAgICAgICAgICAgICAgY29ubmVjdE51bGxzOiB0cnVlDQogICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICBsZWdlbmREYXRhLnB1c2gocHJpY2VHcm91cC5wcmljZU5hbWUpDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOaehOW7uumHh+i0remHj+ezu+WIl++8iOapmeiJsue6v++8iQ0KICAgICAgICBpZiAocHJpY2VBbmRTdG9yZURhdGEucHJvY3VyZW1lbnRQdXJjaGFzZUFtb3VudFZvTGlzdCkgew0KICAgICAgICAgIHByaWNlQW5kU3RvcmVEYXRhLnByb2N1cmVtZW50UHVyY2hhc2VBbW91bnRWb0xpc3QuZm9yRWFjaChhbW91bnRHcm91cCA9PiB7DQogICAgICAgICAgICBjb25zdCBhbW91bnREYXRhID0gYWxsRGF0ZXMubWFwKGRhdGUgPT4gew0KICAgICAgICAgICAgICBjb25zdCBmb3VuZCA9IGFtb3VudEdyb3VwLmFtb3VudExpc3QuZmluZChpdGVtID0+IGl0ZW0ucmVjb3JkRGF0ZSA9PT0gZGF0ZSkNCiAgICAgICAgICAgICAgcmV0dXJuIGZvdW5kID8gcGFyc2VGbG9hdChmb3VuZC5hbW91bnQpIDogbnVsbA0KICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgc2VyaWVzLnB1c2goew0KICAgICAgICAgICAgICBuYW1lOiBhbW91bnRHcm91cC5hbW91bnROYW1lLA0KICAgICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICAgIHlBeGlzSW5kZXg6IDEsDQogICAgICAgICAgICAgIGRhdGE6IGFtb3VudERhdGEsDQogICAgICAgICAgICAgIHNtb290aDogdHJ1ZSwNCiAgICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgICAgd2lkdGg6IDIsDQogICAgICAgICAgICAgICAgY29sb3I6ICcjZmY5ZjdmJyAgLy8g5qmZ6ImyDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgICAgIGNvbG9yOiAnI2ZmOWY3ZicNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgc3ltYm9sOiAnY2lyY2xlJywNCiAgICAgICAgICAgICAgc3ltYm9sU2l6ZTogNCwNCiAgICAgICAgICAgICAgY29ubmVjdE51bGxzOiB0cnVlDQogICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICBsZWdlbmREYXRhLnB1c2goYW1vdW50R3JvdXAuYW1vdW50TmFtZSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6K6h566XWei9tOiMg+WbtA0KICAgICAgICBsZXQgcHJpY2VNaW4sIHByaWNlTWF4DQoNCiAgICAgICAgLy8g6K6h566X5Lu35qC86L206IyD5Zu077yI5bem6L2077yJDQogICAgICAgIGNvbnN0IHByaWNlVmFsdWVzID0gc2VyaWVzLmZpbHRlcihzID0+IHMueUF4aXNJbmRleCA9PT0gMCkNCiAgICAgICAgICAuZmxhdE1hcChzID0+IHMuZGF0YS5maWx0ZXIodiA9PiB2ICE9PSBudWxsICYmIHYgIT09IHVuZGVmaW5lZCkpDQogICAgICAgIGlmIChwcmljZVZhbHVlcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgcHJpY2VNaW4gPSBNYXRoLm1pbiguLi5wcmljZVZhbHVlcykNCiAgICAgICAgICBwcmljZU1heCA9IE1hdGgubWF4KC4uLnByaWNlVmFsdWVzKQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6YeH6LSt6YeP6L206IyD5Zu05Zu65a6a5Li6NeS4hy0yMOS4hw0KDQogICAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsDQogICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgICAgYXhpc1BvaW50ZXI6IHsNCiAgICAgICAgICAgICAgdHlwZTogJ2Nyb3NzJywNCiAgICAgICAgICAgICAgY3Jvc3NTdHlsZTogew0KICAgICAgICAgICAgICAgIGNvbG9yOiAnIzk5OScNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMCwwLDAsMC44KScsDQogICAgICAgICAgICBib3JkZXJDb2xvcjogJyMwMGQ0ZmYnLA0KICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDEsDQogICAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjZmZmJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7DQogICAgICAgICAgICAgIGxldCBzdHIgPSBwYXJhbXNbMF0uYXhpc1ZhbHVlTGFiZWwgKyAnPGJyLz4nDQogICAgICAgICAgICAgIHBhcmFtcy5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAgIGlmIChpdGVtLnZhbHVlICE9PSBudWxsICYmIGl0ZW0udmFsdWUgIT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICAgICAgaWYgKGl0ZW0uc2VyaWVzTmFtZS5pbmNsdWRlcygn5Lu35qC8JykgfHwgaXRlbS5zZXJpZXNOYW1lLmluY2x1ZGVzKCfku7cnKSkgew0KICAgICAgICAgICAgICAgICAgICBzdHIgKz0gYCR7aXRlbS5tYXJrZXJ9JHtpdGVtLnNlcmllc05hbWV9OiAke2l0ZW0udmFsdWV9IOWFgy/lkKg8YnIvPmANCiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgIC8vIOmHh+i0remHj+aYvuekuuS4uuS4h+WQqA0KICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUluV2FuID0gKHBhcnNlRmxvYXQoaXRlbS52YWx1ZSkgLyAxMDAwMCkudG9GaXhlZCgxKQ0KICAgICAgICAgICAgICAgICAgICBzdHIgKz0gYCR7aXRlbS5tYXJrZXJ9JHtpdGVtLnNlcmllc05hbWV9OiAke3ZhbHVlSW5XYW59IOS4h+WQqDxici8+YA0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICBzdHIgKz0gYCR7aXRlbS5tYXJrZXJ9JHtpdGVtLnNlcmllc05hbWV9OiAtPGJyLz5gDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICByZXR1cm4gc3RyDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICAgIGRhdGE6IGxlZ2VuZERhdGEsDQogICAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjZmZmJywNCiAgICAgICAgICAgICAgZm9udFNpemU6IDEyDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgdG9wOiAnMCUnLA0KICAgICAgICAgICAgcmlnaHQ6ICcxMCUnLA0KICAgICAgICAgICAgaXRlbUdhcDogNSwNCiAgICAgICAgICAgIG9yaWVudDogJ2hvcml6b250YWwnDQogICAgICAgICAgfSwNCiAgICAgICAgICBncmlkOiB7DQogICAgICAgICAgICBsZWZ0OiAnMTIlJywNCiAgICAgICAgICAgIHJpZ2h0OiAnMTIlJywNCiAgICAgICAgICAgIGJvdHRvbTogJzglJywNCiAgICAgICAgICAgIHRvcDogJzMyJScsDQogICAgICAgICAgICBjb250YWluTGFiZWw6IGZhbHNlDQogICAgICAgICAgfSwNCiAgICAgICAgICB4QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICAgIGRhdGE6IGFsbERhdGVzLm1hcChkYXRlID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgeWVhciA9IGRhdGUuc3Vic3RyaW5nKDAsIDQpDQogICAgICAgICAgICAgIGNvbnN0IG1vbnRoID0gcGFyc2VJbnQoZGF0ZS5zdWJzdHJpbmcoNCwgNikpDQogICAgICAgICAgICAgIHJldHVybiBgJHt5ZWFyfS4ke21vbnRofWANCiAgICAgICAgICAgIH0pLA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsDQogICAgICAgICAgICAgIGZvbnRTaXplOiAxMSwNCiAgICAgICAgICAgICAgaW50ZXJ2YWw6ICdhdXRvJw0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICAgIGNvbG9yOiAnIzY2NicNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGF4aXNUaWNrOiB7DQogICAgICAgICAgICAgIHNob3c6IGZhbHNlDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB5QXhpczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgICAgICBuYW1lOiAn5Lu35qC8KOWFgy/lkKgpJywNCiAgICAgICAgICAgICAgbmFtZUxvY2F0aW9uOiAnbWlkZGxlJywNCiAgICAgICAgICAgICAgbmFtZUdhcDogMzUsDQogICAgICAgICAgICAgIG5hbWVSb3RhdGU6IDkwLA0KICAgICAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7DQogICAgICAgICAgICAgICAgY29sb3I6ICcjZmZmJywNCiAgICAgICAgICAgICAgICBmb250U2l6ZTogMTEsDQogICAgICAgICAgICAgICAgZm9udFdlaWdodDogJ25vcm1hbCcNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgcG9zaXRpb246ICdsZWZ0JywNCiAgICAgICAgICAgICAgbWluOiBwcmljZU1pbiwNCiAgICAgICAgICAgICAgbWF4OiBwcmljZU1heCwNCiAgICAgICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgICAgICBzaG93OiB0cnVlLA0KICAgICAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICAgICAgY29sb3I6ICcjNjY2Jw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICAgICAgY29sb3I6ICcjZmZmJywNCiAgICAgICAgICAgICAgICBmb250U2l6ZTogMTANCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgc3BsaXRMaW5lOiB7DQogICAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjEpJywNCiAgICAgICAgICAgICAgICAgIHR5cGU6ICdkYXNoZWQnDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICBheGlzVGljazogew0KICAgICAgICAgICAgICAgIHNob3c6IGZhbHNlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgICAgIG5hbWU6ICfph4fotK3ph48o5LiH5ZCoKScsDQogICAgICAgICAgICAgIG5hbWVMb2NhdGlvbjogJ21pZGRsZScsDQogICAgICAgICAgICAgIG5hbWVHYXA6IDM1LA0KICAgICAgICAgICAgICBuYW1lUm90YXRlOiAtOTAsDQogICAgICAgICAgICAgIG5hbWVUZXh0U3R5bGU6IHsNCiAgICAgICAgICAgICAgICBjb2xvcjogJyNmZmYnLA0KICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxMSwNCiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnbm9ybWFsJw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICBwb3NpdGlvbjogJ3JpZ2h0JywNCiAgICAgICAgICAgICAgbWluOiA1MDAwMCwgIC8vIDXkuIcNCiAgICAgICAgICAgICAgbWF4OiAyMDAwMDAsIC8vIDIw5LiHDQogICAgICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwNCiAgICAgICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzY2NicNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicsDQogICAgICAgICAgICAgICAgZm9udFNpemU6IDEwLA0KICAgICAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24odmFsdWUpIHsNCiAgICAgICAgICAgICAgICAgIHJldHVybiAodmFsdWUgLyAxMDAwMCkudG9GaXhlZCgwKQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgc3BsaXRMaW5lOiB7DQogICAgICAgICAgICAgICAgc2hvdzogZmFsc2UNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgYXhpc1RpY2s6IHsNCiAgICAgICAgICAgICAgICBzaG93OiBmYWxzZQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBzZXJpZXM6IHNlcmllcw0KICAgICAgICB9DQoNCiAgICAgICAgY2hhcnQuc2V0T3B0aW9uKG9wdGlvbiwgdHJ1ZSkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOagvOW8j+WMlumHkemineaYvuekug0KICAgIGZvcm1hdEFtb3VudChhbW91bnQpIHsNCiAgICAgIGlmIChhbW91bnQgPj0gMTAwMDApIHsNCiAgICAgICAgcmV0dXJuIChhbW91bnQgLyAxMDAwMCkudG9GaXhlZCgxKSArICfkuIcnDQogICAgICB9DQogICAgICByZXR1cm4gYW1vdW50LnRvRml4ZWQoMSkNCiAgICB9LA0KDQogICAgLy8g5aSE55CG56qX5Y+j5aSn5bCP5Y+Y5YyWDQogICAgaGFuZGxlUmVzaXplKCkgew0KICAgICAgLy8g5bu26L+f5omn6KGM77yM6YG/5YWN6aKR57mB6Kem5Y+RDQogICAgICBjbGVhclRpbWVvdXQodGhpcy5yZXNpemVUaW1lcikNCiAgICAgIHRoaXMucmVzaXplVGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgT2JqZWN0LnZhbHVlcyh0aGlzLmNoYXJ0cykuZm9yRWFjaChjaGFydCA9PiB7DQogICAgICAgICAgaWYgKGNoYXJ0ICYmIGNoYXJ0LnJlc2l6ZSkgew0KICAgICAgICAgICAgY2hhcnQucmVzaXplKCkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9LCAzMDApDQogICAgfSwNCg0KICAgIC8vIOWIneWni+WMluWFqOWxj+ebkeWQrOWZqA0KICAgIGluaXRGdWxsc2NyZWVuTGlzdGVuZXIoKSB7DQogICAgICBpZiAoc2NyZWVuZnVsbCAmJiBzY3JlZW5mdWxsLmlzRW5hYmxlZCkgew0KICAgICAgICBzY3JlZW5mdWxsLm9uKCdjaGFuZ2UnLCB0aGlzLmhhbmRsZUZ1bGxzY3JlZW5DaGFuZ2UpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOenu+mZpOWFqOWxj+ebkeWQrOWZqA0KICAgIHJlbW92ZUZ1bGxzY3JlZW5MaXN0ZW5lcigpIHsNCiAgICAgIGlmIChzY3JlZW5mdWxsICYmIHNjcmVlbmZ1bGwuaXNFbmFibGVkKSB7DQogICAgICAgIHNjcmVlbmZ1bGwub2ZmKCdjaGFuZ2UnLCB0aGlzLmhhbmRsZUZ1bGxzY3JlZW5DaGFuZ2UpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuWFqOWxj+eKtuaAgeWPmOWMlg0KICAgIGhhbmRsZUZ1bGxzY3JlZW5DaGFuZ2UoKSB7DQogICAgICBpZiAoc2NyZWVuZnVsbCAmJiBzY3JlZW5mdWxsLmlzRW5hYmxlZCkgew0KICAgICAgICBjb25zdCBpc0Z1bGxzY3JlZW4gPSBzY3JlZW5mdWxsLmlzRnVsbHNjcmVlbg0KICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXBwL3NldEZ1bGxzY3JlZW5Nb2RlJywgaXNGdWxsc2NyZWVuKQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCflhajlsY/nirbmgIHlj5jljJY6JywgaXNGdWxsc2NyZWVuKSAvLyDosIPor5Xkv6Hmga8NCiAgICAgICAgY29uc29sZS5sb2coJ1N0b3Jl54q25oCBOicsIHRoaXMuJHN0b3JlLnN0YXRlLmFwcC5pc0Z1bGxzY3JlZW5Nb2RlKSAvLyDosIPor5VTdG9yZeeKtuaAgQ0KDQogICAgICAgIC8vIOWFqOWxj+eKtuaAgeWPmOWMluWQju+8jOmHjeaWsOiwg+aVtOWbvuihqOWkp+Wwjw0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmhhbmRsZVJlc2l6ZSgpDQogICAgICAgICAgfSwgMzAwKSAvLyDnu5nluIPlsYDlj5jljJbkuIDkupvml7bpl7QNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5YiH5o2i5YWo5bGPDQogICAgdG9nZ2xlRnVsbHNjcmVlbigpIHsNCiAgICAgIGlmIChzY3JlZW5mdWxsICYmIHNjcmVlbmZ1bGwuaXNFbmFibGVkKSB7DQogICAgICAgIHNjcmVlbmZ1bGwudG9nZ2xlKCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfmgqjnmoTmtY/op4jlmajkuI3mlK/mjIHlhajlsY/lip/og70nLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDml7bpl7Tov4fmu6Tlmajlj5jljJblpITnkIYNCiAgICBoYW5kbGVUaW1lRmlsdGVyQ2hhbmdlKGZpbHRlcklkLCBkaW1lbnNpb25UeXBlKSB7DQogICAgICB0aGlzLmFjdGl2ZUZpbHRlciA9IGZpbHRlcklkDQogICAgICB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlID0gZGltZW5zaW9uVHlwZQ0KICAgICAgY29uc29sZS5sb2coJ+mAieaLqeeahOaXtumXtOiMg+WbtDonLCBmaWx0ZXJJZCwgJ+e7tOW6pjonLCBkaW1lbnNpb25UeXBlKQ0KDQogICAgICAvLyDmoLnmja7ml7bpl7TojIPlm7Tph43mlrDliqDovb3mlbDmja4NCiAgICAgIHRoaXMuZmV0Y2hXYXJuaW5nRGF0YSgpDQogICAgICB0aGlzLmZldGNoRnVuZE1hbmFnZW1lbnREYXRhKCkNCiAgICB9LA0KDQogICAgLy8g6Lez6L2s5Yiw5L6b5bqU5ZWG5aSE572a6aG16Z2iDQogICAgZ29Ub1N1cHBsaWVyUGVuYWx0eSgpIHsNCiAgICAgIC8vIOWcqOaWsOeql+WPo+S4reaJk+W8gOS+m+W6lOWVhuWkhOe9mumhtemdog0KICAgICAgY29uc3Qgcm91dGVVcmwgPSB0aGlzLiRyb3V0ZXIucmVzb2x2ZSgnL3B1cmNoYXNlL3N1cHBQdW5pc2htZW50JykNCiAgICAgIHdpbmRvdy5vcGVuKHJvdXRlVXJsLmhyZWYsICdfYmxhbmsnKQ0KICAgIH0sDQoNCiAgICAvLyDot7PovazliLDph4fotK3lupPlrZjnnIvmnb8NCiAgICBnb1RvU3RvY2tEYXNoYm9hcmQoKSB7DQogICAgICBjb25zb2xlLmxvZygn6Lez6L2s5Yiw6YeH6LSt5bqT5a2Y55yL5p2/JykNCiAgICAgIC8vIOWcqOaWsOeql+WPo+S4reaJk+W8gOmHh+i0reW6k+WtmOeci+adv+mhtemdog0KICAgICAgY29uc3Qgcm91dGVVcmwgPSB0aGlzLiRyb3V0ZXIucmVzb2x2ZSgnL3B1cmNoYXNlRGFzaGJvYXJkU3RvY2snKQ0KICAgICAgd2luZG93Lm9wZW4ocm91dGVVcmwuaHJlZiwgJ19ibGFuaycpDQogICAgfSwNCg0KICAgIC8vIOi3s+i9rOWIsOmrmOmikeeJqeaWmeeci+advw0KICAgIGdvVG9IaWdoRnJlcXVlbmN5RGFzaGJvYXJkKCkgew0KICAgICAgY29uc29sZS5sb2coJ+i3s+i9rOWIsOmrmOmikeeJqeaWmeeci+advycpDQogICAgICAvLyDlnKjmlrDnqpflj6PkuK3miZPlvIDpq5jpopHnianmlpnnnIvmnb/pobXpnaINCiAgICAgIGNvbnN0IHJvdXRlVXJsID0gdGhpcy4kcm91dGVyLnJlc29sdmUoJy9wdXJjaGFzZURhc2hib2FyZFByaWNlJykNCiAgICAgIHdpbmRvdy5vcGVuKHJvdXRlVXJsLmhyZWYsICdfYmxhbmsnKQ0KICAgIH0sDQoNCiAgICAvLyDot7PovazliLDph4fotK3orqHliJLnnIvmnb8NCiAgICBnb1RvUGxhbkRhc2hib2FyZCgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfot7PovazliLDph4fotK3orqHliJLnnIvmnb8nKQ0KICAgICAgLy8g5Zyo5paw56qX5Y+j5Lit5omT5byA6YeH6LSt6K6h5YiS55yL5p2/6aG16Z2iDQogICAgICBjb25zdCByb3V0ZVVybCA9IHRoaXMuJHJvdXRlci5yZXNvbHZlKCcvcHVyY2hhc2VEYXNoYm9hcmRQbGFuJykNCiAgICAgIHdpbmRvdy5vcGVuKHJvdXRlVXJsLmhyZWYsICdfYmxhbmsnKQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5bnn7/nhKbnhaTlupPlrZjmlbDmja4NCiAgICBhc3luYyBmZXRjaENva2luZ0NvYWxJbnZlbnRvcnlEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzaG93Q29raW5nQ29hbEFtb3VudCgpDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaENva2luZ0NvYWxJbnZlbnRvcnlEYXRhIC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5jb2tpbmdDb2FsSW52ZW50b3J5RGF0YSA9IHJlc3BvbnNlLmRhdGEgfHwgW10NCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hDb2tpbmdDb2FsSW52ZW50b3J5RGF0YSAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLmNva2luZ0NvYWxJbnZlbnRvcnlEYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluefv+eEpueFpOW6k+WtmOaVsOaNruWksei0pScsIHJlc3BvbnNlKQ0KICAgICAgICAgIHRoaXMuY29raW5nQ29hbEludmVudG9yeURhdGEgPSBbXQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnn7/nhKbnhaTlupPlrZjmlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuY29raW5nQ29hbEludmVudG9yeURhdGEgPSBbXQ0KICAgICAgfQ0KDQogICAgICAvLyDmlbDmja7ojrflj5blrozmiJDlkI7ph43mlrDliJ3lp4vljJblm77ooagNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5pbml0Q29raW5nQ29hbExpbmVDaGFydCgpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDorqHnrpfnn7/nhKbnhaTlupPlrZjmgLvph48NCiAgICBjYWxjdWxhdGVDb2tpbmdDb2FsVG90YWwoKSB7DQogICAgICBsZXQgdG90YWwgPSAwDQogICAgICBpZiAodGhpcy5jb2tpbmdDb2FsSW52ZW50b3J5RGF0YSAmJiB0aGlzLmNva2luZ0NvYWxJbnZlbnRvcnlEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgLy8g5om+5Yiw5pyA5paw5pel5pyfDQogICAgICAgIGxldCBsYXRlc3REYXRlID0gJycNCiAgICAgICAgdGhpcy5jb2tpbmdDb2FsSW52ZW50b3J5RGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmIChpdGVtLnB1cmNoYXNlQ29raW5nRGFpbHlEZXRhaWxMaXN0ICYmIGl0ZW0ucHVyY2hhc2VDb2tpbmdEYWlseURldGFpbExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgaXRlbS5wdXJjaGFzZUNva2luZ0RhaWx5RGV0YWlsTGlzdC5mb3JFYWNoKGRldGFpbCA9PiB7DQogICAgICAgICAgICAgIGlmIChkZXRhaWwuaW5zdG9ja0RhdGUgPiBsYXRlc3REYXRlKSB7DQogICAgICAgICAgICAgICAgbGF0ZXN0RGF0ZSA9IGRldGFpbC5pbnN0b2NrRGF0ZQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCg0KICAgICAgICAvLyDorqHnrpfmnIDmlrDml6XmnJ/lkITkuKrnianmlpnnmoTlupPlrZjph4/lkIjorqENCiAgICAgICAgdGhpcy5jb2tpbmdDb2FsSW52ZW50b3J5RGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmIChpdGVtLnB1cmNoYXNlQ29raW5nRGFpbHlEZXRhaWxMaXN0ICYmIGl0ZW0ucHVyY2hhc2VDb2tpbmdEYWlseURldGFpbExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgY29uc3QgbGF0ZXN0RGV0YWlsID0gaXRlbS5wdXJjaGFzZUNva2luZ0RhaWx5RGV0YWlsTGlzdC5maW5kKGRldGFpbCA9PiBkZXRhaWwuaW5zdG9ja0RhdGUgPT09IGxhdGVzdERhdGUpDQogICAgICAgICAgICBpZiAobGF0ZXN0RGV0YWlsKSB7DQogICAgICAgICAgICAgIHRvdGFsICs9IHBhcnNlRmxvYXQobGF0ZXN0RGV0YWlsLmludlF0eSkgfHwgMA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIHJldHVybiAodG90YWwgLyAxMDAwMCkudG9GaXhlZCgyKSAvLyDovazmjaLkuLrkuIflkKgNCiAgICB9LA0KDQogICAgLy8g5aSE55CG55+/54Sm54Wk57G75Z6L5LiL5ouJ5qGG5Y+Y5YyWDQogICAgYXN5bmMgaGFuZGxlQ29raW5nQ29hbFR5cGVDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn55+/54Sm54Wk57G75Z6L5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRDb2tpbmdDb2FsVHlwZSkNCiAgICAgIC8vIOmHjeaWsOWIneWni+WMluWbvuihqOS7peW6lOeUqOi/h+a7pA0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLmluaXRDb2tpbmdDb2FsTGluZUNoYXJ0KCkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluefv+eEpueFpOeJqeaWmeexu+Wei+eahOminOiJsuaYoOWwhA0KICAgIGdldENva2luZ0NvYWxNYXRlcmlhbENvbG9yTWFwKCkgew0KICAgICAgLy8g5L2/55So5LiO5bqT5a2Y55yL5p2/5LiA6Ie055qE6aKc6Imy5pa55qGIDQogICAgICBjb25zdCBiYXNlQ29sb3JzID0gWycjMDA2NmZmJywgJyMwMGZmMDAnLCAnI2ZmMDAwMCcsICcjOGIwMGZmJywgJyNmZmZmMDAnLCAnI2ZmZmZmZiddDQoNCiAgICAgIC8vIOWfuuS6juaJgOacieWOn+Wni+aVsOaNruS4uuavj+S4queJqeaWmeexu+Wei+WIhumFjeWbuuWumuminOiJsu+8jOehruS/nei/h+a7pOaXtuminOiJsuS/neaMgeS4gOiHtA0KICAgICAgY29uc3QgYWxsTWF0ZXJpYWxUeXBlcyA9IFtdDQogICAgICBjb25zdCBpbnZlbnRvcnlEYXRhID0gdGhpcy5jb2tpbmdDb2FsSW52ZW50b3J5RGF0YSB8fCBbXQ0KDQogICAgICAvLyDmlLbpm4bmiYDmnInnianmlpnnsbvlnosNCiAgICAgIGludmVudG9yeURhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgY29uc3QgbWF0ZXJpYWxOYW1lID0gaXRlbS5jbGFzczJOYW1lIHx8ICfmnKrnn6XnianmlpknDQogICAgICAgIGlmICghYWxsTWF0ZXJpYWxUeXBlcy5pbmNsdWRlcyhtYXRlcmlhbE5hbWUpKSB7DQogICAgICAgICAgYWxsTWF0ZXJpYWxUeXBlcy5wdXNoKG1hdGVyaWFsTmFtZSkNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgLy8g5oyJ5a2X5q+N6aG65bqP5o6S5bqP77yM56Gu5L+d6aKc6Imy5YiG6YWN55qE5LiA6Ie05oCnDQogICAgICBhbGxNYXRlcmlhbFR5cGVzLnNvcnQoKQ0KDQogICAgICAvLyDkuLrmr4/kuKrnianmlpnnsbvlnovliIbphY3lm7rlrprpopzoibINCiAgICAgIGNvbnN0IGNvbG9yTWFwID0ge30NCiAgICAgIGFsbE1hdGVyaWFsVHlwZXMuZm9yRWFjaCgobWF0ZXJpYWxOYW1lLCBpbmRleCkgPT4gew0KICAgICAgICBjb2xvck1hcFttYXRlcmlhbE5hbWVdID0gYmFzZUNvbG9yc1tpbmRleCAlIGJhc2VDb2xvcnMubGVuZ3RoXQ0KICAgICAgfSkNCg0KICAgICAgcmV0dXJuIGNvbG9yTWFwDQogICAgfSwNCg0KICAgIC8vIOWIneWni+WMluefv+eEpueFpOW6k+WtmOaKmOe6v+Wbvg0KICAgIGluaXRDb2tpbmdDb2FsTGluZUNoYXJ0KCkgew0KICAgICAgY29uc3QgY2hhcnREb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnY29raW5nQ29hbExpbmVDaGFydCcpDQogICAgICBpZiAoIWNoYXJ0RG9tKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aJvuS4jeWIsOefv+eEpueFpOaKmOe6v+WbvkRPTTogY29raW5nQ29hbExpbmVDaGFydCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDmuIXnkIbnjrDmnInlrp7kvosNCiAgICAgIGlmICh0aGlzLmNoYXJ0cy5jb2tpbmdDb2FsTGluZUNoYXJ0KSB7DQogICAgICAgIHRoaXMuY2hhcnRzLmNva2luZ0NvYWxMaW5lQ2hhcnQuZGlzcG9zZSgpDQogICAgICB9DQoNCiAgICAgIGNvbnN0IG15Q2hhcnQgPSBlY2hhcnRzLmluaXQoY2hhcnREb20pDQogICAgICB0aGlzLmNoYXJ0cy5jb2tpbmdDb2FsTGluZUNoYXJ0ID0gbXlDaGFydA0KDQogICAgICBjb25zdCBpbnZlbnRvcnlEYXRhID0gdGhpcy5jb2tpbmdDb2FsSW52ZW50b3J5RGF0YSB8fCBbXQ0KDQogICAgICBpZiAoIWludmVudG9yeURhdGEgfHwgaW52ZW50b3J5RGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JywNCiAgICAgICAgICB0aXRsZTogew0KICAgICAgICAgICAgdGV4dDogJ+aaguaXoOaVsOaNricsDQogICAgICAgICAgICBsZWZ0OiAnY2VudGVyJywNCiAgICAgICAgICAgIHRvcDogJ21pZGRsZScsDQogICAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjZmZmJywNCiAgICAgICAgICAgICAgZm9udFNpemU6IDE2DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIG15Q2hhcnQuc2V0T3B0aW9uKG9wdGlvbikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOagueaNrumAieS4reeahOexu+Wei+i/h+a7pOaVsOaNru+8iOS9v+eUqOS4juW6k+WtmOeci+adv+S4gOiHtOeahOi/h+a7pOmAu+i+ke+8iQ0KICAgICAgbGV0IGZpbHRlcmVkRGF0YSA9IGludmVudG9yeURhdGENCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkQ29raW5nQ29hbFR5cGUgJiYgdGhpcy5zZWxlY3RlZENva2luZ0NvYWxUeXBlICE9PSAnJykgew0KICAgICAgICBmaWx0ZXJlZERhdGEgPSBpbnZlbnRvcnlEYXRhLmZpbHRlcihpdGVtID0+IHsNCiAgICAgICAgICByZXR1cm4gaXRlbS5jbGFzczJOYW1lID09PSB0aGlzLnNlbGVjdGVkQ29raW5nQ29hbFR5cGUgfHwNCiAgICAgICAgICAgICAgICAgaXRlbS5jbGFzczJOYW1lLmluY2x1ZGVzKHRoaXMuc2VsZWN0ZWRDb2tpbmdDb2FsVHlwZSkgfHwNCiAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZENva2luZ0NvYWxUeXBlLmluY2x1ZGVzKGl0ZW0uY2xhc3MyTmFtZSkNCiAgICAgICAgfSkNCiAgICAgIH0NCg0KICAgICAgLy8g5o+Q5Y+W5omA5pyJ5pel5pyf5bm25o6S5bqPDQogICAgICBjb25zdCBhbGxEYXRlcyA9IG5ldyBTZXQoKQ0KICAgICAgZmlsdGVyZWREYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIGlmIChpdGVtLnB1cmNoYXNlQ29raW5nRGFpbHlEZXRhaWxMaXN0KSB7DQogICAgICAgICAgaXRlbS5wdXJjaGFzZUNva2luZ0RhaWx5RGV0YWlsTGlzdC5mb3JFYWNoKGRldGFpbCA9PiB7DQogICAgICAgICAgICBhbGxEYXRlcy5hZGQoZGV0YWlsLmluc3RvY2tEYXRlKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIGNvbnN0IHNvcnRlZERhdGVzID0gQXJyYXkuZnJvbShhbGxEYXRlcykuc29ydCgpDQoNCiAgICAgIC8vIOagvOW8j+WMluaXpeacn+aYvuekuu+8iOS7jnl5eXlNTWRk6L2s5o2i5Li6TU0tZGTvvIkNCiAgICAgIGNvbnN0IGZvcm1hdHRlZERhdGVzID0gc29ydGVkRGF0ZXMubWFwKGRhdGVTdHIgPT4gew0KICAgICAgICBpZiAoZGF0ZVN0ciAmJiBkYXRlU3RyLmxlbmd0aCA9PT0gOCkgew0KICAgICAgICAgIGNvbnN0IG1vbnRoID0gZGF0ZVN0ci5zdWJzdHJpbmcoNCwgNikNCiAgICAgICAgICBjb25zdCBkYXkgPSBkYXRlU3RyLnN1YnN0cmluZyg2LCA4KQ0KICAgICAgICAgIHJldHVybiBgJHttb250aH0tJHtkYXl9YA0KICAgICAgICB9DQogICAgICAgIHJldHVybiBkYXRlU3RyDQogICAgICB9KQ0KDQogICAgICAvLyDmnoTlu7rmr4/kuKrnsbvlnovnmoTmm7Lnur/mlbDmja4NCiAgICAgIGNvbnN0IHNlcmllc0RhdGEgPSBbXQ0KICAgICAgY29uc3QgbGVnZW5kRGF0YSA9IFtdDQoNCiAgICAgIC8vIOiOt+WPlue7n+S4gOeahOminOiJsuaYoOWwhA0KICAgICAgY29uc3QgY29sb3JNYXAgPSB0aGlzLmdldENva2luZ0NvYWxNYXRlcmlhbENvbG9yTWFwKCkNCg0KICAgICAgZmlsdGVyZWREYXRhLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgIGNvbnN0IHR5cGVOYW1lID0gaXRlbS5jbGFzczJOYW1lIHx8ICfmnKrnn6XnianmlpknDQoNCiAgICAgICAgLy8g5Li65q+P5Liq5pel5pyf5p6E5bu65pWw5o2u54K5DQogICAgICAgIGNvbnN0IGxpbmVEYXRhID0gc29ydGVkRGF0ZXMubWFwKGRhdGUgPT4gew0KICAgICAgICAgIGNvbnN0IGRldGFpbCA9IGl0ZW0ucHVyY2hhc2VDb2tpbmdEYWlseURldGFpbExpc3Q/LmZpbmQoZCA9PiBkLmluc3RvY2tEYXRlID09PSBkYXRlKQ0KICAgICAgICAgIHJldHVybiBkZXRhaWwgPyBwYXJzZUZsb2F0KGRldGFpbC5pbnZRdHkpIHx8IDAgOiBudWxsDQogICAgICAgIH0pDQoNCiAgICAgICAgLy8g5L2/55So57uf5LiA55qE6aKc6Imy5pig5bCEDQogICAgICAgIGNvbnN0IG1hdGVyaWFsQ29sb3IgPSBjb2xvck1hcFt0eXBlTmFtZV0gfHwgJyM4M2JmZjYnDQoNCiAgICAgICAgY29uc3Qgc2VyaWVzSXRlbSA9IHsNCiAgICAgICAgICBuYW1lOiB0eXBlTmFtZSwNCiAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgZGF0YTogbGluZURhdGEsDQogICAgICAgICAgc21vb3RoOiB0cnVlLA0KICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgc3ltYm9sU2l6ZTogNiwNCiAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgIHdpZHRoOiAzLA0KICAgICAgICAgICAgY29sb3I6IG1hdGVyaWFsQ29sb3INCiAgICAgICAgICB9LA0KICAgICAgICAgIGl0ZW1TdHlsZTogew0KICAgICAgICAgICAgY29sb3I6IG1hdGVyaWFsQ29sb3INCiAgICAgICAgICB9LA0KICAgICAgICAgIGNvbm5lY3ROdWxsczogZmFsc2UNCiAgICAgICAgfQ0KDQogICAgICAgIHNlcmllc0RhdGEucHVzaChzZXJpZXNJdGVtKQ0KICAgICAgICBsZWdlbmREYXRhLnB1c2godHlwZU5hbWUpDQogICAgICB9KQ0KDQogICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JywNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogew0KICAgICAgICAgICAgdHlwZTogJ2Nyb3NzJywNCiAgICAgICAgICAgIGNyb3NzU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjOTk5Jw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgwLDAsMCwwLjgpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyMwMGQ0ZmYnLA0KICAgICAgICAgIGJvcmRlcldpZHRoOiAxLA0KICAgICAgICAgIHRleHRTdHlsZTogew0KICAgICAgICAgICAgY29sb3I6ICcjZmZmJw0KICAgICAgICAgIH0sDQogICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgIGxldCB0b29sdGlwVGV4dCA9IHBhcmFtc1swXS5uYW1lICsgJzxici8+Jw0KICAgICAgICAgICAgcGFyYW1zLmZvckVhY2gocGFyYW0gPT4gew0KICAgICAgICAgICAgICBpZiAocGFyYW0udmFsdWUgIT09IG51bGwgJiYgcGFyYW0udmFsdWUgIT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICAgIHRvb2x0aXBUZXh0ICs9IGAke3BhcmFtLm1hcmtlcn0ke3BhcmFtLnNlcmllc05hbWV9OiAke3BhcmFtLnZhbHVlLnRvRml4ZWQoMil9IOWQqDxici8+YA0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgcmV0dXJuIHRvb2x0aXBUZXh0DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICBkYXRhOiBsZWdlbmREYXRhLA0KICAgICAgICAgIHRleHRTdHlsZTogew0KICAgICAgICAgICAgY29sb3I6ICcjZmZmJw0KICAgICAgICAgIH0sDQogICAgICAgICAgYm90dG9tOiAnNSUnLA0KICAgICAgICAgIGxlZnQ6ICdjZW50ZXInDQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICBsZWZ0OiAnOCUnLA0KICAgICAgICAgIHJpZ2h0OiAnNSUnLA0KICAgICAgICAgIGJvdHRvbTogJzI1JScsDQogICAgICAgICAgdG9wOiAnMTUlJywNCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGRhdGE6IGZvcm1hdHRlZERhdGVzLA0KICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjMDBkNGZmJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBjb2xvcjogJyNmZmYnDQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+W6k+WtmOmHjyjlkKgpJywNCiAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7DQogICAgICAgICAgICBjb2xvcjogJyNmZmYnLA0KICAgICAgICAgICAgYWxpZ246J3JpZ2h0JywNCiAgICAgICAgICAgIGZvbnRTaXplOiA5LA0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyMwMGQ0ZmYnDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicNCiAgICAgICAgICB9LA0KICAgICAgICAgIHNwbGl0TGluZTogew0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgwLCAyMTIsIDI1NSwgMC4yKScNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogc2VyaWVzRGF0YQ0KICAgICAgfQ0KDQogICAgICBteUNoYXJ0LnNldE9wdGlvbihvcHRpb24pDQogICAgfSwNCg0KICAgIC8vIOiuoeeul+mihOitpuS/oeaBr+eahOeZvuWIhuavlA0KICAgIGdldFdhcm5pbmdQZXJjZW50YWdlKHZhbHVlKSB7DQogICAgICBjb25zdCBudW1WYWx1ZSA9IHBhcnNlSW50KHZhbHVlKSB8fCAwDQoNCiAgICAgIC8vIOWmguaenOWAvOS4ujDvvIzov5Tlm54wJQ0KICAgICAgaWYgKG51bVZhbHVlID09PSAwKSB7DQogICAgICAgIHJldHVybiAwDQogICAgICB9DQoNCiAgICAgIC8vIOiOt+WPluS4pOS4qumihOitpuWAvOS4reeahOacgOWkp+WAvOS9nOS4uuWfuuWHhg0KICAgICAgY29uc3QgY2VydGlmaWNhdGVWYWx1ZSA9IHBhcnNlSW50KHRoaXMud2FybmluZ0luZm8uY2VydGlmaWNhdGVFeHBpcnkpIHx8IDANCiAgICAgIGNvbnN0IGNvbnRyYWN0VmFsdWUgPSBwYXJzZUludCh0aGlzLndhcm5pbmdJbmZvLmNvbnRyYWN0RXhwaXJ5KSB8fCAwDQogICAgICBjb25zdCBtYXhWYWx1ZSA9IE1hdGgubWF4KGNlcnRpZmljYXRlVmFsdWUsIGNvbnRyYWN0VmFsdWUsIDEpIC8vIOiHs+WwkeS4ujHvvIzpgb/lhY3pmaQwDQoNCiAgICAgIC8vIOiuoeeul+ebuOWvueeZvuWIhuavlO+8jOehruS/neacgOWkp+WAvOaYvuekuuS4ujEwMCUNCiAgICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSAobnVtVmFsdWUgLyBtYXhWYWx1ZSkgKiAxMDANCiAgICAgIHJldHVybiBNYXRoLm1pbigxMDAsIE1hdGgubWF4KDAsIHBlcmNlbnRhZ2UpKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/purchaseDashboardMain", "sourcesContent": ["<template>\r\n  <div class=\"purchase-dashboard-main\" :class=\"{ 'fullscreen-mode': isFullscreen }\">\r\n    <div class=\"dashboard-container\">\r\n      <!-- 头部标题 -->\r\n      <div class=\"dashboard-header\">\r\n        <h1>采购管理全景视图</h1>\r\n        <div class=\"header-controls\">\r\n          <div class=\"fullscreen-btn\" @click=\"toggleFullscreen\" :title=\"isFullscreen ? '退出全屏' : '进入全屏'\">\r\n            <i :class=\"isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'\"></i>\r\n          </div>\r\n          <div class=\"time-filter\">\r\n            <button\r\n              v-for=\"filter in timeFilters\"\r\n              :key=\"filter.id\"\r\n              :class=\"['time-filter-btn', { active: filter.id === activeFilter }]\"\r\n              @click=\"handleTimeFilterChange(filter.id, filter.value)\"\r\n            >\r\n              {{ filter.label }}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"dashboard-content\">\r\n        <!-- 左侧面板 -->\r\n        <div class=\"left-panel\" :style=\"isFullscreen ? { width: '320px', minWidth: '320px', maxWidth: '320px', gap: '12px' } : {}\">\r\n          <!-- 左侧第一个：资金管理 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">资金管理</h2>\r\n            <div class=\"chart\" id=\"fundManagementChart\"></div>\r\n          </div>\r\n\r\n          <!-- 左侧第二个：供应商信息全景 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">供应商信息全景</h2>\r\n            <div class=\"supplier-circles\">\r\n              <!-- 中心圆形：考核情况 -->\r\n              <div class=\"circle-item center-circle center\" @click=\"goToSupplierPenalty\">\r\n                <div class=\"circle clickable\" :style=\"{ borderColor: centerData.color, backgroundColor: centerData.color }\">\r\n                  <div class=\"circle-number\">\r\n                    <div>金额: {{ centerData.amount }}</div>\r\n                    <div>次数: {{ centerData.count }}</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"circle-label\">{{ centerData.label }}</div>\r\n              </div>\r\n\r\n              <!-- 周围四个圆形 - 随机位置分布 -->\r\n              <div\r\n                class=\"circle-item random-position\"\r\n                v-for=\"item in supplierData\"\r\n                :key=\"item.id\"\r\n                :style=\"item.position\"\r\n              >\r\n                <div class=\"circle\" :style=\"{ borderColor: item.color, backgroundColor: item.color }\">\r\n                  <span class=\"circle-number\">{{ item.value }}</span>\r\n                </div>\r\n                <div class=\"circle-label\">{{ item.label }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 左侧第三个：合同管理（暂时隐藏） -->\r\n          <!-- <div class=\"card\">\r\n            <h2 class=\"card-title\">合同管理</h2>\r\n            <div id=\"contractChart\" class=\"chart\"></div>\r\n          </div> -->\r\n\r\n          <!-- 左侧第三个：单一来源 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">单一来源</h2>\r\n            <div id=\"supplierChart\" class=\"chart\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 中间面板 -->\r\n        <div class=\"center-panel\" :style=\"isFullscreen ? { gap: '10px', flex: '1', minWidth: '400px' } : {}\">\r\n          <!-- 中间第一行 -->\r\n          <div class=\"center-row center-row-first\" :style=\"isFullscreen ? { maxHeight: 'none', flex: '1' } : {}\">\r\n            <!-- 中间第一个：计划管理 -->\r\n            <div class=\"card clickable-card plan-management-card\" @click=\"goToPlanDashboard\">\r\n              <h2 class=\"card-title\">计划管理</h2>\r\n              <div class=\"plan-grid\" :style=\"isFullscreen ? { gap: '8px', padding: '10px 0' } : {}\">\r\n                <div class=\"plan-item\" v-for=\"(item, index) in productAnalysisData\" :key=\"item.name\" :style=\"isFullscreen ? { padding: '10px 40px' } : {}\">\r\n                  <i :class=\"getPlanIcon(index)\" class=\"plan-icon\" :style=\"isFullscreen ? { fontSize: '18px', marginRight: '8px', width: '20px' } : {}\"></i>\r\n                  <div class=\"plan-text\">\r\n                    <div class=\"plan-value\" :style=\"isFullscreen ? { marginBottom: '4px', fontSize: '15px' } : {}\">{{ item.value }}</div>\r\n                    <div class=\"plan-label\" :style=\"isFullscreen ? { fontSize: '12px', lineHeight: '1.3' } : {}\">{{ item.name }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 中间第二个：库存管理 -->\r\n            <div class=\"card clickable-card\" @click=\"goToStockDashboard\">\r\n              <h2 class=\"card-title\">\r\n                <div style=\"display: flex; align-items: center; justify-content: space-between; width: 100%;\">\r\n                  <div style=\"display: flex; align-items: center; gap: 15px;\">\r\n                    <span>库存管理</span>\r\n                    <span class=\"inventory-total\">\r\n                      合计: {{ calculateCokingCoalTotal() }}万吨\r\n                    </span>\r\n                  </div>\r\n                  <div class=\"chart-filter-dropdown-container\">\r\n                    <select\r\n                      v-model=\"selectedCokingCoalType\"\r\n                      @change=\"handleCokingCoalTypeChange\"\r\n                      @click.stop\r\n                    >\r\n                      <option value=\"\">全部</option>\r\n                      <option value=\"矿料类\">矿料类</option>\r\n                      <option value=\"焦炭\">焦炭</option>\r\n                      <option value=\"煤焦类\">煤焦类</option>\r\n                      <option value=\"合金类\">合金类</option>\r\n                      <option value=\"辅助类/电极\">辅助类/电极</option>\r\n                    </select>\r\n                  </div>\r\n                </div>\r\n              </h2>\r\n              <div id=\"cokingCoalLineChart\" class=\"chart\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 新增：中间插入行 - 计划执行状态 -->\r\n          <div class=\"center-row-full\">\r\n            <div class=\"card plan-execution-card\" :style=\"isFullscreen ? { maxHeight: 'none', minHeight: '80px' } : {}\">\r\n              <!-- <h2 class=\"card-title\">计划执行状态</h2> -->\r\n              <div class=\"plan-execution-grid\">\r\n                <div class=\"execution-item\" v-for=\"(item, index) in planExecutionData\" :key=\"item.name\">\r\n                  <i :class=\"getExecutionIcon(index)\" class=\"execution-icon\"></i>\r\n                  <div class=\"execution-text\">\r\n                    <div class=\"execution-value\">{{ item.value }}</div>\r\n                    <div class=\"execution-label\">{{ item.name }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 中间第二行 -->\r\n          <div class=\"center-row center-row-second\" :style=\"isFullscreen ? { maxHeight: 'none', flex: '1' } : {}\">\r\n            <!-- 中间第三个：供方管理 -->\r\n            <div class=\"card supplier-management-card\">\r\n              <h2 class=\"card-title\">供方管理</h2>\r\n              <!-- 上半部分：饼图 -->\r\n              <div id=\"supplierManagementChart\" class=\"chart\" style=\"height: 40px;\"></div>\r\n              <!-- 下半部分：三个数字显示区域 -->\r\n              <div class=\"supplier-stats\">\r\n                <div class=\"supplier-stat-item\">\r\n                  <div class=\"stat-number\">{{ supplierStats.admission }}</div>\r\n                  <div class=\"stat-label\">准入</div>\r\n                </div>\r\n                <div class=\"supplier-stat-item\">\r\n                  <div class=\"stat-number\">{{ supplierStats.elimination }}</div>\r\n                  <div class=\"stat-label\">淘汰</div>\r\n                </div>\r\n                <div class=\"supplier-stat-item\">\r\n                  <div class=\"stat-number\">{{ supplierStats.suspension }}</div>\r\n                  <div class=\"stat-label\">暂缓</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 中间第四个：高频物资 -->\r\n            <div class=\"card clickable-card\" @click=\"goToHighFrequencyDashboard\">\r\n              <h2 class=\"card-title\">高频物资</h2>\r\n              <div class=\"high-frequency-content\">\r\n                <!-- 上半部分：高频采购物料词云 -->\r\n                <div class=\"high-frequency-materials\">\r\n                  <h3 class=\"section-title\">高频采购物料 TOP10</h3>\r\n                  <div id=\"highFrequencyMaterialCloud\" class=\"material-cloud\"></div>\r\n                </div>\r\n\r\n                <!-- 下半部分：物料采购价格和采购量趋势图 -->\r\n                <div class=\"price-trend-section\">\r\n                  <h3 class=\"section-title\">PB块价格及采购量趋势</h3>\r\n                  <div id=\"highFrequencyPriceTrendChart\" class=\"mini-chart\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n           \r\n          </div>\r\n        </div>\r\n\r\n        <!-- 右侧面板 -->\r\n        <div class=\"right-panel\" :style=\"isFullscreen ? { width: '320px', minWidth: '320px', maxWidth: '320px', gap: '12px' } : {}\">\r\n          <!-- 右侧第一个：预警信息 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">预警信息</h2>\r\n            <div class=\"warning-analysis\">\r\n              <div class=\"warning-item\">\r\n                <span class=\"warning-name\">证书过期</span>\r\n                <div class=\"warning-bar\">\r\n                  <div class=\"bar-bg\">\r\n                    <div class=\"bar-fill\" :style=\"{ width: getWarningPercentage(warningInfo.certificateExpiry) + '%' }\"></div>\r\n                  </div>\r\n                  <span class=\"warning-value\">{{ warningInfo.certificateExpiry }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"warning-item\">\r\n                <span class=\"warning-name\">合同过期</span>\r\n                <div class=\"warning-bar\">\r\n                  <div class=\"bar-bg\">\r\n                    <div class=\"bar-fill\" :style=\"{ width: getWarningPercentage(warningInfo.contractExpiry) + '%' }\"></div>\r\n                  </div>\r\n                  <span class=\"warning-value\">{{ warningInfo.contractExpiry }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 右侧第二个：质量异议管理 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">质量异议管理</h2>\r\n            <div class=\"simple-display\">\r\n              <div class=\"display-number\">{{ qualityIssueCount }}</div>\r\n              <div class=\"display-label\">质量异议总数</div>\r\n            </div>\r\n          </div>\r\n\r\n           <!-- 右侧第三个：异常管理 -->\r\n            <div class=\"card\">\r\n              <h2 class=\"card-title\">异常管理</h2>\r\n              <div class=\"funnel-data\">                \r\n              <div class=\"funnel-item\">\r\n                <span class=\"funnel-label\">到货完成度</span>\r\n                <span class=\"funnel-value\">{{ (purchaseStats.arriveRate || 0) + '%' }}</span>\r\n              </div>\r\n              <div class=\"funnel-item\">\r\n                <span class=\"funnel-label\">采购职责不符合数量</span>\r\n                <span class=\"funnel-value\">9000</span>\r\n              </div>\r\n              <div class=\"funnel-item\">\r\n                <span class=\"funnel-label\">计划被驳回数量</span>\r\n                <span class=\"funnel-value\">9000</span>\r\n              </div>\r\n            </div>\r\n            </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport screenfull from 'screenfull'\r\nimport {\r\n  getDashboardData,\r\n  getPersonalConsumption,\r\n  getPurchaseAnalysis,\r\n  getProductAnalysis,\r\n  getMapData,\r\n  getRealTimeStats,\r\n  getSalesFunnel,\r\n  getPurchaseTrend,\r\n  getSupplierAnalysis,\r\n  getAmtManage\r\n} from '@/api/purchaseDashboardMain'\r\nimport {\r\n  showKeyIndicators,\r\n  showHighFrequencyMaterialList,\r\n  getPurchasePriceAndStore,\r\n  showCokingCoalAmount\r\n} from '@/api/purchaseDashboard/purchaseDashboard'\r\n\r\nexport default {\r\n  name: 'PurchaseDashboardMain',\r\n  data() {\r\n    return {\r\n      // 时间过滤器选项\r\n      timeFilters: [\r\n        { id: 'filter-3m', label: '近三个月', value: 1 },\r\n        { id: 'filter-6m', label: '近六个月', value: 2 },\r\n        { id: 'filter-1y', label: '近一年', value: 3 }\r\n      ],\r\n      activeFilter: 'filter-1y',\r\n      currentDimensionType: 3,\r\n\r\n      // 图表实例\r\n      charts: {},\r\n      // 窗口大小变化定时器\r\n      resizeTimer: null,\r\n\r\n      // 高频物资相关数据\r\n      highFrequencyMaterialList: [],\r\n      priceAndStoreData: null,\r\n      selectedCodeType: 'ALL',\r\n      selectedItemType: 'CLASS3',\r\n      selectedMaterial: 'PB块',\r\n\r\n      // 矿焦煤库存相关数据\r\n      cokingCoalInventoryData: [],\r\n      selectedCokingCoalType: '', // 选中的矿焦煤类型，默认为空（全部）\r\n\r\n      // 实时数据\r\n      realTimeStats: {\r\n        sales: '343567',\r\n        orders: '734245'\r\n      },\r\n      // 计划管理数据\r\n      productAnalysisData: [\r\n        { name: '计划总条数', value: '0', percentage: 0 },\r\n        { name: '审核驳回', value: '0%', percentage: 0 },\r\n        { name: '商务部驳回', value: '0%', percentage: 0 },\r\n        { name: '订单至入库平均天数', value: '30000', percentage: 30 },\r\n        { name: '入库至领用平均天数', value: '30000', percentage: 30 },\r\n        { name: '接收至挂单平均天数', value: '40000', percentage: 40 },\r\n        { name: '超期未入库数', value: '40000', percentage: 40 },\r\n        { name: '超期未领用数', value: '40000', percentage: 40 }\r\n\r\n      ],\r\n      // 计划执行状态数据\r\n      planExecutionData: [\r\n        { name: '计划完成率', value: '85%' },\r\n        { name: '在途订单数', value: '1,234' },\r\n        { name: '待审核计划', value: '56' },\r\n        { name: '紧急采购', value: '12' },\r\n        // { name: '供应商响应率', value: '92%' },\r\n        // { name: '库存周转率', value: '3.2' },\r\n        // { name: '采购成本节约', value: '8.5%' },\r\n        // { name: '质量合格率', value: '98.7%' }\r\n      ],\r\n      // 底部统计数据\r\n      bottomStats: [\r\n        { label: '供应商信息全景', value: '34554' },\r\n        { label: '订单量', value: '34554' },\r\n        { label: '客户数', value: '34554' }\r\n      ],\r\n      // 水位图配置\r\n      waterLevelConfig: {\r\n        data: [50],\r\n        shape: 'circle',\r\n        waveNum: 3,\r\n        waveHeight: 40,\r\n        waveOpacity: 0.4,\r\n        colors: ['#00BAFF', '#3DE7C9']\r\n      },\r\n      // 供应商数据（周围四个圆）\r\n      supplierData: [\r\n        { id: 1, value: '8,092', label: '参标次数', color: '#FF6B6B', position: { top: '10%', left: '10%' } },\r\n        { id: 2, value: '1,245', label: '中标次数', color: '#4ECDC4', position: { top: '10%', right: '10%' } },\r\n        { id: 3, value: '89', label: '质量异议次数', color: '#45B7D1', position: { bottom: '10%', left: '10%' } },\r\n        { id: 4, value: '156', label: '合作年限', color: '#96CEB4', position: { bottom: '10%', right: '10%' } }\r\n      ],\r\n      // 中心圆数据（考核情况）\r\n      centerData: {\r\n        label: '考核情况',\r\n        amount: '567万',\r\n        count: '23次',\r\n        color: '#00BAFF'\r\n      },\r\n      // 质量异议总数\r\n      qualityIssueCount: '156',\r\n      // 供方管理统计数据\r\n      supplierStats: {\r\n        admission: '1,245',    // 准入\r\n        elimination: '89',     // 淘汰\r\n        suspension: '156'      // 暂缓\r\n      },\r\n      // 预警信息数据\r\n      warningInfo: {\r\n        certificateExpiry: 0, // 证书过期数量（供应商风险提醒）\r\n        contractExpiry: 0     // 合同过期数量（合同到期提醒）\r\n      },\r\n      // 采购关键指标数据\r\n      purchaseStats: {\r\n        arriveRate: 0  // 到货完成度\r\n      },\r\n      // 资金管理数据\r\n      fundManagement: {\r\n        nextMonth: '0',    // 下月拟入库总金额\r\n        twoMonthsLater: '0', // 2月后拟入库总金额\r\n        threeMonthsLater: '0' // 3月后拟入库总金额\r\n      },\r\n      // 合同管理数据\r\n      contractData: [\r\n        { name: '原材料', count: 12095282 },\r\n        { name: '辅耐材', count: 8340154 },\r\n        { name: '材料类', count: 33344517 },\r\n        { name: '通用备件', count: 76374451 },\r\n        { name: '专用备件', count: 4353921 },\r\n        { name: '办公', count: 23515 }\r\n      ]\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isFullscreen() {\r\n      return this.$store.state.app.isFullscreenMode\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.initFullscreenListener()\r\n    this.initDashboard()\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize)\r\n  },\r\n  beforeDestroy() {\r\n    // 移除事件监听\r\n    window.removeEventListener('resize', this.handleResize)\r\n    // 销毁所有图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      if (chart && chart.dispose) {\r\n        chart.dispose()\r\n      }\r\n    })\r\n    // 移除全屏监听器\r\n    this.removeFullscreenListener()\r\n    // 确保退出全屏模式\r\n    this.$store.dispatch('app/setFullscreenMode', false)\r\n  },\r\n  methods: {\r\n    async initDashboard() {\r\n      try {\r\n        await this.loadData()\r\n        this.$nextTick(() => {\r\n          this.initCharts()\r\n        })\r\n      } catch (error) {\r\n        console.error('初始化驾驶舱失败:', error)\r\n      }\r\n    },\r\n    \r\n    async loadData() {\r\n      try {\r\n        // 并行获取预警信息数据和资金管理数据\r\n        await Promise.all([\r\n          this.fetchWarningData(),\r\n          this.fetchFundManagementData()\r\n        ])\r\n\r\n        // 这里可以并行加载其他数据\r\n        // const [dashboardData, personalData, purchaseData] = await Promise.all([\r\n        //   getDashboardData(),\r\n        //   getPersonalConsumption(),\r\n        //   getPurchaseAnalysis()\r\n        // ])\r\n\r\n        console.log('数据加载完成')\r\n      } catch (error) {\r\n        console.error('加载数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 获取资金管理数据\r\n    async fetchFundManagementData() {\r\n      try {\r\n        const response = await getAmtManage()\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          const data = response.data\r\n\r\n          // 根据reserve1字段找到对应的数据\r\n          const nextMonthData = data.find(item => item.reserve1 === '01')\r\n          const twoMonthsData = data.find(item => item.reserve1 === '02')\r\n          const threeMonthsData = data.find(item => item.reserve1 === '03')\r\n\r\n          // 更新资金管理数据\r\n          this.fundManagement.nextMonth = nextMonthData ? (nextMonthData.reserve4 || '0') : '0'\r\n          this.fundManagement.twoMonthsLater = twoMonthsData ? (twoMonthsData.reserve4 || '0') : '0'\r\n          this.fundManagement.threeMonthsLater = threeMonthsData ? (threeMonthsData.reserve4 || '0') : '0'\r\n\r\n          // 更新图表\r\n          this.updateFundManagementChart()\r\n\r\n          console.log('资金管理数据获取成功:', this.fundManagement)\r\n        } else {\r\n          console.error('资金管理数据格式不正确:', response)\r\n          this.resetFundManagementData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取资金管理数据失败:', error)\r\n        this.resetFundManagementData()\r\n      }\r\n    },\r\n\r\n    // 重置资金管理数据为默认值\r\n    resetFundManagementData() {\r\n      this.fundManagement.nextMonth = '0'\r\n      this.fundManagement.twoMonthsLater = '0'\r\n      this.fundManagement.threeMonthsLater = '0'\r\n      // 更新图表\r\n      this.updateFundManagementChart()\r\n    },\r\n\r\n    // 获取预警信息和计划管理数据\r\n    async fetchWarningData() {\r\n      try {\r\n        // 调用采购全景看板的关键指标接口\r\n        const response = await showKeyIndicators({ dimensionType: this.currentDimensionType })\r\n\r\n        if (response && response.data) {\r\n          const data = response.data\r\n\r\n          // 更新预警信息数据\r\n          // 证书过期数据来自供应商风险提醒\r\n          this.warningInfo.certificateExpiry = data.suppRiskNum || 0\r\n          // 合同过期数据来自合同到期提醒\r\n          this.warningInfo.contractExpiry = data.bpoExpireNum || 0\r\n\r\n          // 更新计划管理数据\r\n          this.updatePlanManagementData(data)\r\n\r\n          // 更新计划执行状态数据\r\n          this.updatePlanExecutionData(data)\r\n\r\n          // 更新采购关键指标数据\r\n          this.purchaseStats.arriveRate = data.arriveRate || 0\r\n\r\n          console.log('预警信息和计划管理数据获取成功:', {\r\n            warningInfo: this.warningInfo,\r\n            planData: {\r\n              planTotalNum: data.planTotalNum,\r\n              rejectNum1: data.rejectNum1,\r\n              rejectNum2: data.rejectNum2\r\n            },\r\n            purchaseStats: this.purchaseStats\r\n          })\r\n        } else {\r\n          console.error('数据获取失败:', response)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取数据失败:', error)\r\n        // 使用默认值\r\n        this.warningInfo.certificateExpiry = 0\r\n        this.warningInfo.contractExpiry = 0\r\n        this.purchaseStats.arriveRate = 0\r\n        this.resetPlanManagementData()\r\n      }\r\n    },\r\n\r\n    // 更新计划管理数据\r\n    updatePlanManagementData(data) {\r\n      // 计划总条数\r\n      if (data.planTotalNum !== undefined) {\r\n        this.productAnalysisData[0].value = data.planTotalNum.toString()\r\n        this.productAnalysisData[0].percentage = Math.min(100, Math.max(0, (data.planTotalNum / 100000) * 100))\r\n      }\r\n\r\n      // 审核驳回（百分比）\r\n      if (data.rejectNum1 !== undefined) {\r\n        this.productAnalysisData[1].value = data.rejectNum1 + '%'\r\n        this.productAnalysisData[1].percentage = Math.min(100, Math.max(0, data.rejectNum1))\r\n      }\r\n\r\n      // 商务部驳回（百分比）\r\n      if (data.rejectNum2 !== undefined) {\r\n        this.productAnalysisData[2].value = data.rejectNum2 + '%'\r\n        this.productAnalysisData[2].percentage = Math.min(100, Math.max(0, data.rejectNum2))\r\n      }\r\n\r\n      // 订单至入库平均天数（绑定到reserve4字段）\r\n      if (data.reserve4 !== undefined) {\r\n        this.productAnalysisData[3].value = data.reserve4.toString() \r\n        this.productAnalysisData[3].percentage = Math.min(100, Math.max(0, (data.reserve4 / 30) * 100)) // 假设30天为100%\r\n      }\r\n    },\r\n\r\n    // 重置计划管理数据为默认值\r\n    resetPlanManagementData() {\r\n      this.productAnalysisData[0].value = '0'\r\n      this.productAnalysisData[0].percentage = 0\r\n      this.productAnalysisData[1].value = '0%'\r\n      this.productAnalysisData[1].percentage = 0\r\n      this.productAnalysisData[2].value = '0%'\r\n      this.productAnalysisData[2].percentage = 0\r\n      this.productAnalysisData[3].value = '0天'\r\n      this.productAnalysisData[3].percentage = 0\r\n    },\r\n\r\n    // 更新计划执行状态数据\r\n    updatePlanExecutionData(data) {\r\n      if (data) {\r\n        // 计划完成率\r\n        if (data.planCompletionRate !== undefined) {\r\n          this.planExecutionData[0].value = data.planCompletionRate + '%'\r\n        }\r\n        // 在途订单数\r\n        if (data.inTransitOrderNum !== undefined) {\r\n          this.planExecutionData[1].value = this.formatNumber(data.inTransitOrderNum)\r\n        }\r\n        // 待审核计划\r\n        if (data.pendingAuditNum !== undefined) {\r\n          this.planExecutionData[2].value = data.pendingAuditNum.toString()\r\n        }\r\n        // 紧急采购\r\n        if (data.urgentPurchaseNum !== undefined) {\r\n          this.planExecutionData[3].value = data.urgentPurchaseNum.toString()\r\n        }\r\n        // 供应商响应率\r\n        if (data.supplierResponseRate !== undefined) {\r\n          this.planExecutionData[4].value = data.supplierResponseRate + '%'\r\n        }\r\n        // 库存周转率\r\n        if (data.inventoryTurnoverRate !== undefined) {\r\n          this.planExecutionData[5].value = data.inventoryTurnoverRate.toString()\r\n        }\r\n        // 采购成本节约\r\n        if (data.costSavingRate !== undefined) {\r\n          this.planExecutionData[6].value = data.costSavingRate + '%'\r\n        }\r\n        // 质量合格率\r\n        if (data.qualityPassRate !== undefined) {\r\n          this.planExecutionData[7].value = data.qualityPassRate + '%'\r\n        }\r\n        console.log('计划执行状态数据更新完成:', this.planExecutionData)\r\n      }\r\n    },\r\n\r\n    // 格式化数字显示（添加千分位分隔符）\r\n    formatNumber(num) {\r\n      if (num === undefined || num === null) return '0'\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\r\n    },\r\n\r\n    // 获取计划管理图标\r\n    getPlanIcon(index) {\r\n      const icons = [\r\n        'el-icon-document', // 计划总条数\r\n        'el-icon-close', // 审核驳回\r\n        'el-icon-error', // 商务部驳回\r\n        'el-icon-time', // 订单至入库平均天数\r\n        'el-icon-truck', // 入库至领用平均天数\r\n        'el-icon-message', // 接收至挂单平均天数\r\n        'el-icon-warning-outline', // 超期未入库数\r\n        'el-icon-warning-outline' // 超期未领用数\r\n      ]\r\n      return icons[index] || 'el-icon-info'\r\n    },\r\n\r\n    // 获取计划执行状态图标\r\n    getExecutionIcon(index) {\r\n      const icons = [\r\n        'el-icon-success', // 计划完成率\r\n        'el-icon-goods', // 在途订单数\r\n        'el-icon-edit-outline', // 待审核计划\r\n        'el-icon-warning', // 紧急采购\r\n        'el-icon-phone', // 供应商响应率\r\n        'el-icon-refresh', // 库存周转率\r\n        'el-icon-coin', // 采购成本节约\r\n        'el-icon-circle-check' // 质量合格率\r\n      ]\r\n      return icons[index] || 'el-icon-info'\r\n    },\r\n\r\n    initCharts() {\r\n      this.initFundManagementChart()\r\n      this.initSupplierManagementChart()\r\n      this.initPersonalConsumptionChart()\r\n      this.initPurchaseAnalysisChart()\r\n      this.initContractChart()\r\n      this.initTrendChart()\r\n      this.initSupplierChart()\r\n      this.fetchHighFrequencyData()\r\n      this.fetchCokingCoalInventoryData()\r\n    },\r\n\r\n    // 资金管理柱状图\r\n    initFundManagementChart() {\r\n      const chartDom = document.getElementById('fundManagementChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.fundManagement = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        grid: {\r\n          left: '15%',\r\n          right: '10%',\r\n          top: '25%',\r\n          bottom: '25%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['下月', '2月后', '3月后'],\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00BAFF'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff',\r\n            fontSize: 12\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '拟入库金额',\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            align: 'right',\r\n          },\r\n          min: 0,\r\n          max: 8000,\r\n          interval: 2000,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00BAFF'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            formatter: function(value) {\r\n              return value\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(0, 186, 255, 0.2)'\r\n            }\r\n          }\r\n        },\r\n        series: [{\r\n          name: '拟入库金额',\r\n          type: 'bar',\r\n          data: [\r\n            parseFloat(this.fundManagement.nextMonth) || 0,\r\n            parseFloat(this.fundManagement.twoMonthsLater) || 0,\r\n            parseFloat(this.fundManagement.threeMonthsLater) || 0\r\n          ],\r\n          barWidth: '50%',\r\n          itemStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0,\r\n              y: 0,\r\n              x2: 0,\r\n              y2: 1,\r\n              colorStops: [{\r\n                offset: 0, color: '#00BAFF' // 顶部颜色\r\n              }, {\r\n                offset: 1, color: '#0080CC' // 底部颜色\r\n              }],\r\n              global: false\r\n            },\r\n            borderRadius: [4, 4, 0, 0] // 顶部圆角\r\n          },\r\n          label: {\r\n            show: true,\r\n            position: 'top',\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            formatter: function(params) {\r\n              return params.value\r\n            }\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: '#33C7FF' // 高亮顶部颜色\r\n                }, {\r\n                  offset: 1, color: '#0099DD' // 高亮底部颜色\r\n                }],\r\n                global: false\r\n              }\r\n            }\r\n          }\r\n        }],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          formatter: function(params) {\r\n            const param = params[0]\r\n            return `${param.name}: ${param.value}`\r\n          }\r\n        }\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 供方管理饼图\r\n    initSupplierManagementChart() {\r\n      this.$nextTick(() => {\r\n        const chartDom = document.getElementById('supplierManagementChart')\r\n        if (!chartDom) {\r\n          console.error('找不到供方管理图表DOM元素')\r\n          return\r\n        }\r\n\r\n        const chart = echarts.init(chartDom)\r\n        this.charts.supplierManagement = chart\r\n\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          tooltip: {\r\n            trigger: 'item',\r\n            backgroundColor: 'rgba(0,0,0,0.8)',\r\n            borderColor: '#00BAFF',\r\n            textStyle: { color: '#fff' }\r\n          },\r\n          series: [{\r\n            type: 'pie',\r\n            radius: '60%',\r\n            center: ['50%', '60%'],\r\n            data: [\r\n              { value: 45, name: '工程' },\r\n              { value: 35, name: '货物' },\r\n              { value: 20, name: '服务' }\r\n            ],\r\n            itemStyle: {\r\n              color: function(params) {\r\n                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']\r\n                return colors[params.dataIndex % colors.length]\r\n              }\r\n            },\r\n            label: {\r\n              color: '#fff',\r\n              fontSize: 15\r\n            }\r\n          }]\r\n        }\r\n\r\n        chart.setOption(option)\r\n      })\r\n    },\r\n\r\n    // 更新资金管理图表数据\r\n    updateFundManagementChart() {\r\n      if (!this.charts.fundManagement) return\r\n\r\n      const newData = [\r\n        parseFloat(this.fundManagement.nextMonth) || 0,\r\n        parseFloat(this.fundManagement.twoMonthsLater) || 0,\r\n        parseFloat(this.fundManagement.threeMonthsLater) || 0\r\n      ]\r\n\r\n      this.charts.fundManagement.setOption({\r\n        series: [{\r\n          data: newData\r\n        }]\r\n      })\r\n    },\r\n\r\n    // 库存管理图表\r\n    initPersonalConsumptionChart() {\r\n      const chartDom = document.getElementById('personalConsumptionChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.personalConsumption = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'item',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        series: [{\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          center: ['50%', '50%'],\r\n          data: [\r\n            { value: 335, name: '矿料' },\r\n            { value: 310, name: '合金' },\r\n            { value: 234, name: '焦炭' },\r\n            { value: 135, name: '辅料/电极' }\r\n          ],\r\n          itemStyle: {\r\n            color: function(params) {\r\n              const colors = ['#00BAFF', '#3DE7C9', '#FFC107', '#FF6B6B']\r\n              return colors[params.dataIndex % colors.length]\r\n            }\r\n          },\r\n          label: {\r\n            color: '#fff',\r\n            fontSize: 12\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 供方管理图表\r\n    initPurchaseAnalysisChart() {\r\n      const chartDom = document.getElementById('purchaseAnalysisChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.purchaseAnalysis = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff' }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff' },\r\n          splitLine: { lineStyle: { color: 'rgba(0,186,255,0.2)' } }\r\n        },\r\n        series: [{\r\n          data: [120, 200, 150, 80, 70, 110],\r\n          type: 'line',\r\n          smooth: true,\r\n          lineStyle: { color: '#00BAFF', width: 2 },\r\n          itemStyle: { color: '#00BAFF' },\r\n          areaStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0, y: 0, x2: 0, y2: 1,\r\n              colorStops: [\r\n                { offset: 0, color: 'rgba(0,186,255,0.3)' },\r\n                { offset: 1, color: 'rgba(0,186,255,0.1)' }\r\n              ]\r\n            }\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 合同管理柱状图\r\n    initContractChart() {\r\n      const chartDom = document.getElementById('contractChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.contract = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' },\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const data = params[0]\r\n            let value = data.value\r\n            let formattedValue = ''\r\n            if (value >= 10000000) {\r\n              formattedValue = (value / 10000000).toFixed(1) + '千万'\r\n            } else if (value >= 10000) {\r\n              formattedValue = (value / 10000).toFixed(1) + '万'\r\n            } else {\r\n              formattedValue = value.toString()\r\n            }\r\n            return `${data.name}<br/>合同数量: ${formattedValue}`\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '20%',\r\n          top: '25%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: this.contractData.map(item => item.name),\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 0,\r\n            rotate: 30,\r\n            fontSize: 10\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '合同数量',\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            align: 'right'\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: function(value) {\r\n              if (value >= 10000000) {\r\n                return (value / 10000000).toFixed(1) + '千万'\r\n              } else if (value >= 10000) {\r\n                return (value / 10000).toFixed(1) + '万'\r\n              } else {\r\n                return value\r\n              }\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: [{\r\n          name: '合同数量',\r\n          type: 'bar',\r\n          data: this.contractData.map(item => item.count),\r\n          itemStyle: {\r\n            color: '#83bff6',\r\n            borderRadius: [4, 4, 0, 0]            \r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              color: '#83bff6',\r\n              borderRadius: [4, 4, 0, 0],\r\n              shadowBlur: 10,\r\n              shadowColor: 'rgba(255, 255, 255, 0.5)',\r\n              borderWidth: 2,\r\n              borderColor: '#fff'\r\n            }\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 趋势分析图表\r\n    initTrendChart() {\r\n      const chartDom = document.getElementById('trendChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.trend = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff', fontSize: 10 }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff', fontSize: 10 },\r\n          splitLine: { lineStyle: { color: 'rgba(0,186,255,0.2)' } }\r\n        },\r\n        series: [{\r\n          data: [820, 932, 901, 934, 1290, 1330, 1320],\r\n          type: 'line',\r\n          smooth: true,\r\n          lineStyle: { color: '#3DE7C9', width: 2 },\r\n          itemStyle: { color: '#3DE7C9' },\r\n          areaStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0, y: 0, x2: 0, y2: 1,\r\n              colorStops: [\r\n                { offset: 0, color: 'rgba(61,231,201,0.3)' },\r\n                { offset: 1, color: 'rgba(61,231,201,0.1)' }\r\n              ]\r\n            }\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 单一来源图表\r\n    initSupplierChart() {\r\n      this.$nextTick(() => {\r\n        const chartDom = document.getElementById('supplierChart')\r\n        console.log('单一来源图表DOM元素:', chartDom)\r\n\r\n        if (!chartDom) {\r\n          console.error('找不到单一来源图表DOM元素')\r\n          return\r\n        }\r\n\r\n        const chart = echarts.init(chartDom)\r\n        this.charts.supplier = chart\r\n\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          tooltip: {\r\n            trigger: 'item',\r\n            backgroundColor: 'rgba(0,0,0,0.8)',\r\n            borderColor: '#00BAFF',\r\n            textStyle: { color: '#fff' }\r\n          },\r\n          series: [{\r\n            type: 'pie',\r\n            radius: '50%',\r\n            center: ['50%', '30%'],\r\n            data: [\r\n              { value: 40, name: '工程' },\r\n              { value: 30, name: '货物' },\r\n              { value: 30, name: '服务' }\r\n            ],\r\n            itemStyle: {\r\n              color: function(params) {\r\n                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']\r\n                return colors[params.dataIndex % colors.length]\r\n              }\r\n            },\r\n            label: {\r\n              color: '#fff',\r\n              fontSize: 10\r\n            }\r\n          }]\r\n        }\r\n\r\n        chart.setOption(option)\r\n      })\r\n    },\r\n\r\n    // 获取高频物资数据\r\n    async fetchHighFrequencyData() {\r\n      try {\r\n        // 获取高频采购物料数据\r\n        await this.fetchHighFrequencyMaterialData()\r\n        // 获取价格趋势数据\r\n        await this.fetchPriceAndStoreData()\r\n        // 初始化词云和价格趋势图\r\n        this.$nextTick(() => {\r\n          this.initHighFrequencyMaterialCloud()\r\n          this.initHighFrequencyPriceTrendChart()\r\n        })\r\n      } catch (error) {\r\n        console.error('获取高频物资数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 获取高频采购物料数据\r\n    async fetchHighFrequencyMaterialData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          codeType: this.selectedCodeType,\r\n          itemType: this.selectedItemType\r\n        }\r\n\r\n        const response = await showHighFrequencyMaterialList(params)\r\n        if (response && response.data) {\r\n          // 取前10条数据\r\n          this.highFrequencyMaterialList = (response.data || []).slice(0, 10)\r\n        } else {\r\n          // 使用模拟数据\r\n          this.highFrequencyMaterialList = this.getMockHighFrequencyData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取高频物料数据失败:', error)\r\n        this.highFrequencyMaterialList = this.getMockHighFrequencyData()\r\n      }\r\n    },\r\n\r\n    // 初始化高频物料词云\r\n    initHighFrequencyMaterialCloud() {\r\n      const chartDom = document.getElementById('highFrequencyMaterialCloud')\r\n      if (!chartDom) {\r\n        console.error('找不到高频物料词云DOM元素')\r\n        return\r\n      }\r\n\r\n      chartDom.innerHTML = ''\r\n\r\n      const rawMaterialList = this.highFrequencyMaterialList\r\n      if (!rawMaterialList || rawMaterialList.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">无高频采购物料数据</div>'\r\n        return\r\n      }\r\n\r\n      // 按入库金额排序，取前15条\r\n      const highFrequencyMaterials = rawMaterialList\r\n        .sort((a, b) => (b.inAmt || 0) - (a.inAmt || 0))\r\n        .slice(0, 15)\r\n\r\n      const container = document.createElement('div')\r\n      container.style.width = '100%'\r\n      container.style.height = '100%'\r\n      container.style.position = 'relative'\r\n      container.style.overflow = 'hidden'\r\n\r\n      const colors = [\r\n        '#4fc3f7', '#a5d6a7', '#ffcc80', '#ef9a9a', '#ce93d8',\r\n        '#90caf9', '#80deea', '#c5e1a5', '#fff59d', '#ffab91'\r\n      ]\r\n\r\n      const maxFontSize = 24\r\n      const minFontSize = 12\r\n\r\n      highFrequencyMaterials.forEach((item, index) => {\r\n        let fontSize = maxFontSize - (index * 1.2)\r\n        if (fontSize < minFontSize) {\r\n          fontSize = minFontSize\r\n        }\r\n\r\n        const div = document.createElement('div')\r\n        div.textContent = item.itemName\r\n        div.style.position = 'absolute'\r\n        div.style.fontSize = `${fontSize}px`\r\n        div.style.fontWeight = 'bold'\r\n        div.style.color = colors[index % colors.length]\r\n        div.style.transform = `rotate(${Math.random() * 30 - 15}deg)`\r\n        div.style.left = `${10 + Math.random() * 60}%`\r\n        div.style.top = `${10 + Math.random() * 60}%`\r\n        div.style.whiteSpace = 'nowrap'\r\n        div.style.textShadow = '1px 1px 2px rgba(0,0,0,0.3)'\r\n        div.style.transition = 'all 0.3s ease'\r\n        div.style.cursor = 'pointer'\r\n        div.style.zIndex = highFrequencyMaterials.length - index\r\n\r\n        // 添加悬停效果\r\n        const self = this\r\n        div.addEventListener('mouseenter', function() {\r\n          this.style.transform = `rotate(${Math.random() * 30 - 15}deg) scale(1.1)`\r\n          this.style.zIndex = '999'\r\n          this.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)'\r\n\r\n          // 显示金额提示\r\n          const tooltip = document.createElement('div')\r\n          tooltip.className = 'material-tooltip'\r\n          tooltip.textContent = `金额: ${self.formatAmount(item.inAmt)}`\r\n          tooltip.style.position = 'absolute'\r\n          tooltip.style.bottom = '-25px'\r\n          tooltip.style.left = '50%'\r\n          tooltip.style.transform = 'translateX(-50%)'\r\n          tooltip.style.background = 'rgba(0,0,0,0.8)'\r\n          tooltip.style.color = '#fff'\r\n          tooltip.style.padding = '2px 6px'\r\n          tooltip.style.borderRadius = '3px'\r\n          tooltip.style.fontSize = '10px'\r\n          tooltip.style.whiteSpace = 'nowrap'\r\n          tooltip.style.zIndex = '1000'\r\n          this.appendChild(tooltip)\r\n        })\r\n\r\n        div.addEventListener('mouseleave', function() {\r\n          this.style.transform = `rotate(${Math.random() * 30 - 15}deg) scale(1)`\r\n          this.style.zIndex = (highFrequencyMaterials.length - index).toString()\r\n          this.style.textShadow = '1px 1px 2px rgba(0,0,0,0.3)'\r\n\r\n          const tooltip = this.querySelector('.material-tooltip')\r\n          if (tooltip) {\r\n            this.removeChild(tooltip)\r\n          }\r\n        })\r\n\r\n        container.appendChild(div)\r\n      })\r\n\r\n      chartDom.appendChild(container)\r\n    },\r\n\r\n    // 获取模拟高频物料数据\r\n    getMockHighFrequencyData() {\r\n      return [\r\n        { itemName: '粗粉', inAmt: 392467.2, inNum: 5421293 },\r\n        { itemName: '精粉', inAmt: 280350.5, inNum: 4250180 },\r\n        { itemName: '球团', inAmt: 195200.8, inNum: 3180970 },\r\n        { itemName: '烧结', inAmt: 150420.3, inNum: 2890540 },\r\n        { itemName: '焦炭', inAmt: 125680.7, inNum: 2350210 },\r\n        { itemName: '煤炭', inAmt: 98750.2, inNum: 1980760 },\r\n        { itemName: 'PB块', inAmt: 85420.1, inNum: 1650430 },\r\n        { itemName: '铁矿石', inAmt: 72350.8, inNum: 1420890 },\r\n        { itemName: '废钢', inAmt: 65280.4, inNum: 1280560 },\r\n        { itemName: '石灰石', inAmt: 58190.6, inNum: 1150320 },\r\n        { itemName: '合金', inAmt: 52180.3, inNum: 980450 },\r\n        { itemName: '电极', inAmt: 48750.9, inNum: 850320 },\r\n        { itemName: '耐火材料', inAmt: 42350.7, inNum: 720180 },\r\n        { itemName: '化工原料', inAmt: 38920.5, inNum: 650290 },\r\n        { itemName: '辅料', inAmt: 35680.2, inNum: 580160 }\r\n      ]\r\n    },\r\n\r\n    // 获取物料价格和采购量数据\r\n    async fetchPriceAndStoreData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemName: 'PB块'  // 固定获取PB块数据\r\n        }\r\n\r\n        const response = await getPurchasePriceAndStore(params)\r\n        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n          this.priceAndStoreData = response.data[0]\r\n          console.log('获取到PB块价格和采购量数据:', this.priceAndStoreData)\r\n        } else {\r\n          console.log('未获取到真实数据，使用模拟数据')\r\n          this.priceAndStoreData = this.getMockPriceAndStoreData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取价格和采购量数据失败:', error)\r\n        this.priceAndStoreData = this.getMockPriceAndStoreData()\r\n      }\r\n    },\r\n\r\n    // 获取模拟的价格和采购量数据（参考采购看板数据结构）\r\n    getMockPriceAndStoreData() {\r\n      return {\r\n        procurementPriceVoList: [\r\n          {\r\n            priceName: 'PB块价格',\r\n            priceList: [\r\n              { recordDate: '20240801', price: 850.0 },\r\n              { recordDate: '20240815', price: 870.5 },\r\n              { recordDate: '20240901', price: 890.2 },\r\n              { recordDate: '20240915', price: 875.8 },\r\n              { recordDate: '20241001', price: 920.3 },\r\n              { recordDate: '20241015', price: 905.7 },\r\n              { recordDate: '20241101', price: 880.4 },\r\n              { recordDate: '20241115', price: 895.6 },\r\n              { recordDate: '20241123', price: 910.2 },  // 2024年11月23日价格\r\n              { recordDate: '20241201', price: 925.8 },\r\n              { recordDate: '20241215', price: 940.1 },\r\n              { recordDate: '20250101', price: 930.5 },\r\n              { recordDate: '20250115', price: 915.3 },\r\n              { recordDate: '20250201', price: 900.7 },\r\n              { recordDate: '20250215', price: 885.9 },\r\n              { recordDate: '20250301', price: 870.2 },\r\n              { recordDate: '20250315', price: 855.8 },\r\n              { recordDate: '20250401', price: 840.6 },\r\n              { recordDate: '20250415', price: 825.4 },\r\n              { recordDate: '20250501', price: 810.9 },\r\n              { recordDate: '20250515', price: 795.7 },\r\n              { recordDate: '20250601', price: 780.3 }\r\n            ]\r\n          }\r\n        ],\r\n        procurementPurchaseAmountVoList: [\r\n          {\r\n            amountName: 'PB块采购量',\r\n            amountList: [\r\n              { recordDate: '20240801', amount: 125000 },   // 12.5万吨\r\n              { recordDate: '20240815', amount: 118000 },   // 11.8万吨\r\n              { recordDate: '20240901', amount: 132000 },   // 13.2万吨\r\n              { recordDate: '20240915', amount: 145000 },   // 14.5万吨\r\n              { recordDate: '20241001', amount: 138000 },   // 13.8万吨\r\n              { recordDate: '20241015', amount: 152000 },   // 15.2万吨\r\n              { recordDate: '20241101', amount: 168000 },   // 16.8万吨\r\n              { recordDate: '20241115', amount: 175000 },   // 17.5万吨\r\n              { recordDate: '20241123', amount: 100000 },   // 10万吨\r\n              { recordDate: '20241201', amount: 185000 },   // 18.5万吨\r\n              { recordDate: '20241215', amount: 192000 },   // 19.2万吨\r\n              { recordDate: '20250101', amount: 178000 },   // 17.8万吨\r\n              { recordDate: '20250115', amount: 165000 },   // 16.5万吨\r\n              { recordDate: '20250201', amount: 158000 },   // 15.8万吨\r\n              { recordDate: '20250215', amount: 142000 },   // 14.2万吨\r\n              { recordDate: '20250301', amount: 135000 },   // 13.5万吨\r\n              { recordDate: '20250315', amount: 128000 },   // 12.8万吨\r\n              { recordDate: '20250401', amount: 121000 },   // 12.1万吨\r\n              { recordDate: '20250415', amount: 115000 },   // 11.5万吨\r\n              { recordDate: '20250501', amount: 108000 },   // 10.8万吨\r\n              { recordDate: '20250515', amount: 102000 },   // 10.2万吨\r\n              { recordDate: '20250601', amount: 95000 }     // 9.5万吨\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n    },\r\n\r\n    // 初始化高频物资价格趋势图（完全按照图片效果重写）\r\n    initHighFrequencyPriceTrendChart() {\r\n      this.$nextTick(() => {\r\n        const chartDom = document.getElementById('highFrequencyPriceTrendChart')\r\n        if (!chartDom) {\r\n          console.error('找不到高频物资价格趋势图DOM元素')\r\n          return\r\n        }\r\n\r\n        // 清理现有实例\r\n        if (this.charts.highFrequencyPriceTrend) {\r\n          this.charts.highFrequencyPriceTrend.dispose()\r\n        }\r\n\r\n        const chart = echarts.init(chartDom)\r\n        this.charts.highFrequencyPriceTrend = chart\r\n\r\n        // 使用真实数据结构\r\n        const priceAndStoreData = this.priceAndStoreData\r\n        if (!priceAndStoreData) {\r\n          chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无价格趋势数据</div>'\r\n          return\r\n        }\r\n\r\n        // 收集所有日期\r\n        let allDates = new Set()\r\n\r\n        // 从价格数据中收集日期\r\n        if (priceAndStoreData.procurementPriceVoList) {\r\n          priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {\r\n            if (priceGroup.priceList) {\r\n              priceGroup.priceList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n\r\n        // 从采购量数据中收集日期\r\n        if (priceAndStoreData.procurementPurchaseAmountVoList) {\r\n          priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {\r\n            if (amountGroup.amountList) {\r\n              amountGroup.amountList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n\r\n        // 转换为排序的数组\r\n        allDates = Array.from(allDates).sort()\r\n\r\n        if (allDates.length === 0) {\r\n          chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无价格趋势数据</div>'\r\n          return\r\n        }\r\n\r\n        // 构建系列数据\r\n        const series = []\r\n        const legendData = []\r\n\r\n        // 构建价格系列（蓝色线）\r\n        if (priceAndStoreData.procurementPriceVoList) {\r\n          priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {\r\n            const priceData = allDates.map(date => {\r\n              const found = priceGroup.priceList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.price) : null\r\n            })\r\n\r\n            series.push({\r\n              name: priceGroup.priceName,\r\n              type: 'line',\r\n              yAxisIndex: 0,\r\n              data: priceData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 2,\r\n                color: '#00d4ff'  // 蓝色\r\n              },\r\n              itemStyle: {\r\n                color: '#00d4ff'\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 4,\r\n              connectNulls: true\r\n            })\r\n\r\n            legendData.push(priceGroup.priceName)\r\n          })\r\n        }\r\n\r\n        // 构建采购量系列（橙色线）\r\n        if (priceAndStoreData.procurementPurchaseAmountVoList) {\r\n          priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {\r\n            const amountData = allDates.map(date => {\r\n              const found = amountGroup.amountList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.amount) : null\r\n            })\r\n\r\n            series.push({\r\n              name: amountGroup.amountName,\r\n              type: 'line',\r\n              yAxisIndex: 1,\r\n              data: amountData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 2,\r\n                color: '#ff9f7f'  // 橙色\r\n              },\r\n              itemStyle: {\r\n                color: '#ff9f7f'\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 4,\r\n              connectNulls: true\r\n            })\r\n\r\n            legendData.push(amountGroup.amountName)\r\n          })\r\n        }\r\n\r\n        // 计算Y轴范围\r\n        let priceMin, priceMax\r\n\r\n        // 计算价格轴范围（左轴）\r\n        const priceValues = series.filter(s => s.yAxisIndex === 0)\r\n          .flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n        if (priceValues.length > 0) {\r\n          priceMin = Math.min(...priceValues)\r\n          priceMax = Math.max(...priceValues)\r\n        }\r\n\r\n        // 采购量轴范围固定为5万-20万\r\n\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: {\r\n              type: 'cross',\r\n              crossStyle: {\r\n                color: '#999'\r\n              }\r\n            },\r\n            backgroundColor: 'rgba(0,0,0,0.8)',\r\n            borderColor: '#00d4ff',\r\n            borderWidth: 1,\r\n            textStyle: {\r\n              color: '#fff'\r\n            },\r\n            formatter: function(params) {\r\n              let str = params[0].axisValueLabel + '<br/>'\r\n              params.forEach(item => {\r\n                if (item.value !== null && item.value !== undefined) {\r\n                  if (item.seriesName.includes('价格') || item.seriesName.includes('价')) {\r\n                    str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`\r\n                  } else {\r\n                    // 采购量显示为万吨\r\n                    const valueInWan = (parseFloat(item.value) / 10000).toFixed(1)\r\n                    str += `${item.marker}${item.seriesName}: ${valueInWan} 万吨<br/>`\r\n                  }\r\n                } else {\r\n                  str += `${item.marker}${item.seriesName}: -<br/>`\r\n                }\r\n              })\r\n              return str\r\n            }\r\n          },\r\n          legend: {\r\n            data: legendData,\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 12\r\n            },\r\n            top: '0%',\r\n            right: '10%',\r\n            itemGap: 5,\r\n            orient: 'horizontal'\r\n          },\r\n          grid: {\r\n            left: '12%',\r\n            right: '12%',\r\n            bottom: '8%',\r\n            top: '32%',\r\n            containLabel: false\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: allDates.map(date => {\r\n              const year = date.substring(0, 4)\r\n              const month = parseInt(date.substring(4, 6))\r\n              return `${year}.${month}`\r\n            }),\r\n            axisLabel: {\r\n              color: '#fff',\r\n              fontSize: 11,\r\n              interval: 'auto'\r\n            },\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#666'\r\n              }\r\n            },\r\n            axisTick: {\r\n              show: false\r\n            }\r\n          },\r\n          yAxis: [\r\n            {\r\n              type: 'value',\r\n              name: '价格(元/吨)',\r\n              nameLocation: 'middle',\r\n              nameGap: 35,\r\n              nameRotate: 90,\r\n              nameTextStyle: {\r\n                color: '#fff',\r\n                fontSize: 11,\r\n                fontWeight: 'normal'\r\n              },\r\n              position: 'left',\r\n              min: priceMin,\r\n              max: priceMax,\r\n              axisLine: {\r\n                show: true,\r\n                lineStyle: {\r\n                  color: '#666'\r\n                }\r\n              },\r\n              axisLabel: {\r\n                color: '#fff',\r\n                fontSize: 10\r\n              },\r\n              splitLine: {\r\n                show: true,\r\n                lineStyle: {\r\n                  color: 'rgba(255,255,255,0.1)',\r\n                  type: 'dashed'\r\n                }\r\n              },\r\n              axisTick: {\r\n                show: false\r\n              }\r\n            },\r\n            {\r\n              type: 'value',\r\n              name: '采购量(万吨)',\r\n              nameLocation: 'middle',\r\n              nameGap: 35,\r\n              nameRotate: -90,\r\n              nameTextStyle: {\r\n                color: '#fff',\r\n                fontSize: 11,\r\n                fontWeight: 'normal'\r\n              },\r\n              position: 'right',\r\n              min: 50000,  // 5万\r\n              max: 200000, // 20万\r\n              axisLine: {\r\n                show: true,\r\n                lineStyle: {\r\n                  color: '#666'\r\n                }\r\n              },\r\n              axisLabel: {\r\n                color: '#fff',\r\n                fontSize: 10,\r\n                formatter: function(value) {\r\n                  return (value / 10000).toFixed(0)\r\n                }\r\n              },\r\n              splitLine: {\r\n                show: false\r\n              },\r\n              axisTick: {\r\n                show: false\r\n              }\r\n            }\r\n          ],\r\n          series: series\r\n        }\r\n\r\n        chart.setOption(option, true)\r\n      })\r\n    },\r\n\r\n    // 格式化金额显示\r\n    formatAmount(amount) {\r\n      if (amount >= 10000) {\r\n        return (amount / 10000).toFixed(1) + '万'\r\n      }\r\n      return amount.toFixed(1)\r\n    },\r\n\r\n    // 处理窗口大小变化\r\n    handleResize() {\r\n      // 延迟执行，避免频繁触发\r\n      clearTimeout(this.resizeTimer)\r\n      this.resizeTimer = setTimeout(() => {\r\n        Object.values(this.charts).forEach(chart => {\r\n          if (chart && chart.resize) {\r\n            chart.resize()\r\n          }\r\n        })\r\n      }, 300)\r\n    },\r\n\r\n    // 初始化全屏监听器\r\n    initFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.on('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 移除全屏监听器\r\n    removeFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.off('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 处理全屏状态变化\r\n    handleFullscreenChange() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        const isFullscreen = screenfull.isFullscreen\r\n        this.$store.dispatch('app/setFullscreenMode', isFullscreen)\r\n\r\n        console.log('全屏状态变化:', isFullscreen) // 调试信息\r\n        console.log('Store状态:', this.$store.state.app.isFullscreenMode) // 调试Store状态\r\n\r\n        // 全屏状态变化后，重新调整图表大小\r\n        this.$nextTick(() => {\r\n          setTimeout(() => {\r\n            this.handleResize()\r\n          }, 300) // 给布局变化一些时间\r\n        })\r\n      }\r\n    },\r\n\r\n    // 切换全屏\r\n    toggleFullscreen() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.toggle()\r\n      } else {\r\n        this.$message({\r\n          message: '您的浏览器不支持全屏功能',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 时间过滤器变化处理\r\n    handleTimeFilterChange(filterId, dimensionType) {\r\n      this.activeFilter = filterId\r\n      this.currentDimensionType = dimensionType\r\n      console.log('选择的时间范围:', filterId, '维度:', dimensionType)\r\n\r\n      // 根据时间范围重新加载数据\r\n      this.fetchWarningData()\r\n      this.fetchFundManagementData()\r\n    },\r\n\r\n    // 跳转到供应商处罚页面\r\n    goToSupplierPenalty() {\r\n      // 在新窗口中打开供应商处罚页面\r\n      const routeUrl = this.$router.resolve('/purchase/suppPunishment')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 跳转到采购库存看板\r\n    goToStockDashboard() {\r\n      console.log('跳转到采购库存看板')\r\n      // 在新窗口中打开采购库存看板页面\r\n      const routeUrl = this.$router.resolve('/purchaseDashboardStock')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 跳转到高频物料看板\r\n    goToHighFrequencyDashboard() {\r\n      console.log('跳转到高频物料看板')\r\n      // 在新窗口中打开高频物料看板页面\r\n      const routeUrl = this.$router.resolve('/purchaseDashboardPrice')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 跳转到采购计划看板\r\n    goToPlanDashboard() {\r\n      console.log('跳转到采购计划看板')\r\n      // 在新窗口中打开采购计划看板页面\r\n      const routeUrl = this.$router.resolve('/purchaseDashboardPlan')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 获取矿焦煤库存数据\r\n    async fetchCokingCoalInventoryData() {\r\n      try {\r\n        const response = await showCokingCoalAmount()\r\n        console.log('fetchCokingCoalInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.cokingCoalInventoryData = response.data || []\r\n          console.log('fetchCokingCoalInventoryData - 设置的数据:', this.cokingCoalInventoryData)\r\n        } else {\r\n          console.error('获取矿焦煤库存数据失败', response)\r\n          this.cokingCoalInventoryData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取矿焦煤库存数据失败:', error)\r\n        this.cokingCoalInventoryData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalLineChart()\r\n      })\r\n    },\r\n\r\n    // 计算矿焦煤库存总量\r\n    calculateCokingCoalTotal() {\r\n      let total = 0\r\n      if (this.cokingCoalInventoryData && this.cokingCoalInventoryData.length > 0) {\r\n        // 找到最新日期\r\n        let latestDate = ''\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            item.purchaseCokingDailyDetailList.forEach(detail => {\r\n              if (detail.instockDate > latestDate) {\r\n                latestDate = detail.instockDate\r\n              }\r\n            })\r\n          }\r\n        })\r\n\r\n        // 计算最新日期各个物料的库存量合计\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)\r\n            if (latestDetail) {\r\n              total += parseFloat(latestDetail.invQty) || 0\r\n            }\r\n          }\r\n        })\r\n      }\r\n      return (total / 10000).toFixed(2) // 转换为万吨\r\n    },\r\n\r\n    // 处理矿焦煤类型下拉框变化\r\n    async handleCokingCoalTypeChange() {\r\n      console.log('矿焦煤类型变化:', this.selectedCokingCoalType)\r\n      // 重新初始化图表以应用过滤\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalLineChart()\r\n      })\r\n    },\r\n\r\n    // 获取矿焦煤物料类型的颜色映射\r\n    getCokingCoalMaterialColorMap() {\r\n      // 使用与库存看板一致的颜色方案\r\n      const baseColors = ['#0066ff', '#00ff00', '#ff0000', '#8b00ff', '#ffff00', '#ffffff']\r\n\r\n      // 基于所有原始数据为每个物料类型分配固定颜色，确保过滤时颜色保持一致\r\n      const allMaterialTypes = []\r\n      const inventoryData = this.cokingCoalInventoryData || []\r\n\r\n      // 收集所有物料类型\r\n      inventoryData.forEach(item => {\r\n        const materialName = item.class2Name || '未知物料'\r\n        if (!allMaterialTypes.includes(materialName)) {\r\n          allMaterialTypes.push(materialName)\r\n        }\r\n      })\r\n\r\n      // 按字母顺序排序，确保颜色分配的一致性\r\n      allMaterialTypes.sort()\r\n\r\n      // 为每个物料类型分配固定颜色\r\n      const colorMap = {}\r\n      allMaterialTypes.forEach((materialName, index) => {\r\n        colorMap[materialName] = baseColors[index % baseColors.length]\r\n      })\r\n\r\n      return colorMap\r\n    },\r\n\r\n    // 初始化矿焦煤库存折线图\r\n    initCokingCoalLineChart() {\r\n      const chartDom = document.getElementById('cokingCoalLineChart')\r\n      if (!chartDom) {\r\n        console.error('找不到矿焦煤折线图DOM: cokingCoalLineChart')\r\n        return\r\n      }\r\n\r\n      // 清理现有实例\r\n      if (this.charts.cokingCoalLineChart) {\r\n        this.charts.cokingCoalLineChart.dispose()\r\n      }\r\n\r\n      const myChart = echarts.init(chartDom)\r\n      this.charts.cokingCoalLineChart = myChart\r\n\r\n      const inventoryData = this.cokingCoalInventoryData || []\r\n\r\n      if (!inventoryData || inventoryData.length === 0) {\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          title: {\r\n            text: '暂无数据',\r\n            left: 'center',\r\n            top: 'middle',\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 16\r\n            }\r\n          }\r\n        }\r\n        myChart.setOption(option)\r\n        return\r\n      }\r\n\r\n      // 根据选中的类型过滤数据（使用与库存看板一致的过滤逻辑）\r\n      let filteredData = inventoryData\r\n      if (this.selectedCokingCoalType && this.selectedCokingCoalType !== '') {\r\n        filteredData = inventoryData.filter(item => {\r\n          return item.class2Name === this.selectedCokingCoalType ||\r\n                 item.class2Name.includes(this.selectedCokingCoalType) ||\r\n                 this.selectedCokingCoalType.includes(item.class2Name)\r\n        })\r\n      }\r\n\r\n      // 提取所有日期并排序\r\n      const allDates = new Set()\r\n      filteredData.forEach(item => {\r\n        if (item.purchaseCokingDailyDetailList) {\r\n          item.purchaseCokingDailyDetailList.forEach(detail => {\r\n            allDates.add(detail.instockDate)\r\n          })\r\n        }\r\n      })\r\n\r\n      const sortedDates = Array.from(allDates).sort()\r\n\r\n      // 格式化日期显示（从yyyyMMdd转换为MM-dd）\r\n      const formattedDates = sortedDates.map(dateStr => {\r\n        if (dateStr && dateStr.length === 8) {\r\n          const month = dateStr.substring(4, 6)\r\n          const day = dateStr.substring(6, 8)\r\n          return `${month}-${day}`\r\n        }\r\n        return dateStr\r\n      })\r\n\r\n      // 构建每个类型的曲线数据\r\n      const seriesData = []\r\n      const legendData = []\r\n\r\n      // 获取统一的颜色映射\r\n      const colorMap = this.getCokingCoalMaterialColorMap()\r\n\r\n      filteredData.forEach((item, index) => {\r\n        const typeName = item.class2Name || '未知物料'\r\n\r\n        // 为每个日期构建数据点\r\n        const lineData = sortedDates.map(date => {\r\n          const detail = item.purchaseCokingDailyDetailList?.find(d => d.instockDate === date)\r\n          return detail ? parseFloat(detail.invQty) || 0 : null\r\n        })\r\n\r\n        // 使用统一的颜色映射\r\n        const materialColor = colorMap[typeName] || '#83bff6'\r\n\r\n        const seriesItem = {\r\n          name: typeName,\r\n          type: 'line',\r\n          data: lineData,\r\n          smooth: true,\r\n          symbol: 'circle',\r\n          symbolSize: 6,\r\n          lineStyle: {\r\n            width: 3,\r\n            color: materialColor\r\n          },\r\n          itemStyle: {\r\n            color: materialColor\r\n          },\r\n          connectNulls: false\r\n        }\r\n\r\n        seriesData.push(seriesItem)\r\n        legendData.push(typeName)\r\n      })\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          },\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00d4ff',\r\n          borderWidth: 1,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              if (param.value !== null && param.value !== undefined) {\r\n                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 吨<br/>`\r\n              }\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          bottom: '5%',\r\n          left: 'center'\r\n        },\r\n        grid: {\r\n          left: '8%',\r\n          right: '5%',\r\n          bottom: '25%',\r\n          top: '15%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: formattedDates,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00d4ff'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff'\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '库存量(吨)',\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            align:'right',\r\n            fontSize: 9,\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00d4ff'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff'\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(0, 212, 255, 0.2)'\r\n            }\r\n          }\r\n        },\r\n        series: seriesData\r\n      }\r\n\r\n      myChart.setOption(option)\r\n    },\r\n\r\n    // 计算预警信息的百分比\r\n    getWarningPercentage(value) {\r\n      const numValue = parseInt(value) || 0\r\n\r\n      // 如果值为0，返回0%\r\n      if (numValue === 0) {\r\n        return 0\r\n      }\r\n\r\n      // 获取两个预警值中的最大值作为基准\r\n      const certificateValue = parseInt(this.warningInfo.certificateExpiry) || 0\r\n      const contractValue = parseInt(this.warningInfo.contractExpiry) || 0\r\n      const maxValue = Math.max(certificateValue, contractValue, 1) // 至少为1，避免除0\r\n\r\n      // 计算相对百分比，确保最大值显示为100%\r\n      const percentage = (numValue / maxValue) * 100\r\n      return Math.min(100, Math.max(0, percentage))\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.purchase-dashboard-main {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n\r\n  .dashboard-container {\r\n    width: 100%;\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n    color: #fff;\r\n    overflow-x: hidden;\r\n    padding: 10px;\r\n  }\r\n\r\n  .dashboard-header {\r\n    text-align: center;\r\n    margin-bottom: 10px;\r\n    position: relative;\r\n    padding: 5px 0;\r\n\r\n    h1 {\r\n      font-size: 24px;\r\n      position: relative;\r\n      display: inline-block;\r\n      padding: 5px 40px;\r\n      margin: 0;\r\n      color: #fff;\r\n    }\r\n\r\n    &::before,\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 50%;\r\n      width: 30%;\r\n      height: 2px;\r\n      background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n    }\r\n\r\n    &::before {\r\n      left: 0;\r\n    }\r\n\r\n    &::after {\r\n      right: 0;\r\n    }\r\n  }\r\n\r\n  .header-controls {\r\n    position: absolute;\r\n    right: 20px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 15px;\r\n    z-index: 1000;\r\n  }\r\n\r\n  .fullscreen-btn {\r\n    padding: 8px 12px;\r\n    border: none;\r\n    background-color: rgba(33, 10, 56, 0.7);\r\n    color: #eee;\r\n    border-radius: 20px;\r\n    font-size: 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    border: 1px solid rgba(0, 212, 255, 0.2);\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 40px;\r\n    height: 32px;\r\n\r\n    &:hover {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n      background-color: rgba(0, 212, 255, 0.2);\r\n      border-color: rgba(0, 212, 255, 0.7);\r\n      color: #00ffff;\r\n    }\r\n  }\r\n\r\n  .time-filter {\r\n    display: flex;\r\n    gap: 10px;\r\n  }\r\n\r\n  .time-filter-btn {\r\n    padding: 6px 12px;\r\n    border: none;\r\n    background-color: rgba(33, 10, 56, 0.7);\r\n    color: #eee;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    border: 1px solid rgba(0, 212, 255, 0.2);\r\n    position: relative;\r\n\r\n    &:hover {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n    }\r\n\r\n    &.active {\r\n      background-color: rgba(0, 212, 255, 0.2);\r\n      border-color: rgba(0, 212, 255, 0.7);\r\n      color: #00ffff;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  .dashboard-content {\r\n    display: flex;\r\n    height: calc(100vh - 80px);\r\n    gap: 10px;\r\n  }\r\n\r\n  .left-panel,\r\n  .right-panel {\r\n    flex: 0 0 320px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .center-panel {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 5px; /* 缩短行间距从10px到5px */\r\n  }\r\n\r\n  .center-row {\r\n    flex: 1;\r\n    display: flex;\r\n    gap: 5px; /* 缩短卡片间距从10px到5px */\r\n  }\r\n\r\n  .center-row .card {\r\n    flex: 1;\r\n  }\r\n\r\n  /* 第一行特定样式 - 缩短高度 */\r\n  .center-row-first {\r\n    flex: 1; /* 减小第一行的高度比例 */\r\n    max-height: 250px; /* 进一步限制第一行的最大高度 */\r\n  }\r\n\r\n  /* 第二行特定样式 - 缩短高度 */\r\n  .center-row-second {\r\n    flex: 0.7; /* 进一步减小第二行的高度比例 */\r\n    max-height: 330px; /* 进一步限制第二行的最大高度 */\r\n  }\r\n\r\n  /* 全屏模式下的样式调整 - 使用更高优先级的选择器 */\r\n  .purchase-dashboard-main.fullscreen-mode {\r\n    /* 调试样式 - 全屏时改变背景色 */\r\n    background: linear-gradient(135deg, #2a2a90, #5B1082, #900090) !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-first {\r\n    max-height: none !important; /* 全屏时移除第一行高度限制 */\r\n    flex: 1 !important; /* 确保flex比例正确 */\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-second {\r\n    max-height: none !important; /* 全屏时移除第二行高度限制 */\r\n    flex: 1 !important; /* 确保flex比例正确 */\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-full .card {\r\n    max-height: none !important; /* 全屏时移除全宽行高度限制 */\r\n    min-height: 60px !important; /* 保持最小高度 */\r\n  }\r\n\r\n  /* 全屏模式下调整整体容器高度和布局 */\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-content {\r\n    height: calc(100vh - 60px) !important; /* 全屏时减去标题高度 */\r\n    min-height: calc(100vh - 60px) !important;\r\n    display: flex !important;\r\n    gap: 10px !important;\r\n    width: 100% !important;\r\n    justify-content: center !important; /* 居中对齐 */\r\n    align-items: stretch !important;\r\n    overflow-x: auto !important; /* 允许水平滚动以防内容过宽 */\r\n    padding: 0 10px !important; /* 添加一些内边距 */\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n  /* 新增：全宽行样式 */\r\n  .center-row-full {\r\n    width: 100%;\r\n    margin: 2px 0; /* 缩短上下边距从5px到2px */\r\n    flex-shrink: 0; /* 防止被压缩 */\r\n  }\r\n\r\n  .center-row-full .card {\r\n    width: 100%;\r\n    min-height: 50px; /* 设置最小高度 */\r\n    max-height: 80px; /* 设置最大高度，确保不占用太多空间 */\r\n  }\r\n\r\n  .left-panel .card,\r\n  .right-panel .card {\r\n    flex: 1;\r\n  }\r\n\r\n  /* 全屏模式下右侧面板的特殊样式 - 解决高度被过度拉伸的问题 */\r\n  .purchase-dashboard-main.fullscreen-mode .right-panel {\r\n    /* 改变右侧面板的布局方式，平均分配空间而不是拉伸 */\r\n    justify-content: space-between !important;\r\n    align-items: stretch !important;\r\n    width: 320px !important; /* 固定右侧面板宽度 */\r\n    min-width: 320px !important;\r\n    max-width: 320px !important;\r\n    flex: none !important;\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .right-panel .card {\r\n    flex: 0 0 calc(33.33% - 8px) !important; /* 三等分，减去间距 */\r\n    height: auto !important; /* 让内容决定高度 */\r\n    min-height: 120px !important; /* 设置更小的最小高度 */\r\n    max-height: 200px !important; /* 限制最大高度，更加紧凑 */\r\n    overflow-y: auto !important; /* 内容过多时滚动 */\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .left-panel {\r\n    justify-content: space-between !important;\r\n    width: 320px !important; /* 固定左侧面板宽度 */\r\n    min-width: 320px !important;\r\n    max-width: 320px !important;\r\n    flex: none !important;\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .center-panel {\r\n    flex: 1 !important; /* 中间面板占用剩余空间 */\r\n    min-width: 400px !important; /* 最小宽度保证内容显示 */\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .left-panel .card {\r\n    flex: 0 0 calc(50% - 6px) !important; /* 二等分，减去间距 */\r\n    height: auto !important;\r\n    min-height: 140px !important;\r\n    max-height: 260px !important;\r\n    overflow-y: auto !important;\r\n  }\r\n\r\n  /* 全屏模式下优化具体内容的显示 - 缩小内容 */\r\n  .purchase-dashboard-main.fullscreen-mode .card-title {\r\n    font-size: 14px !important;\r\n    margin-bottom: 8px !important;\r\n    padding: 8px 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-analysis {\r\n    padding: 6px 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-item {\r\n    margin-bottom: 6px !important;\r\n    padding: 4px 0 !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-name {\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-value {\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .simple-display {\r\n    padding: 10px 12px !important;\r\n    text-align: center !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .display-number {\r\n    font-size: 24px !important;\r\n    margin-bottom: 4px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .display-label {\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-data {\r\n    padding: 6px 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-item {\r\n    margin-bottom: 4px !important;\r\n    padding: 3px 0 !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-label {\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-value {\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  /* 全屏模式下缩小警告条的高度 */\r\n  .purchase-dashboard-main.fullscreen-mode .warning-bar {\r\n    height: 16px !important;\r\n    margin-left: 8px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .bar-bg {\r\n    height: 16px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .bar-fill {\r\n    height: 16px !important;\r\n  }\r\n\r\n  /* 全屏模式下调整卡片内边距 */\r\n  .purchase-dashboard-main.fullscreen-mode .right-panel .card {\r\n    padding: 8px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .left-panel .card {\r\n    padding: 8px !important;\r\n  }\r\n\r\n  .card {\r\n    background-color: rgba(33, 10, 56, 0.7);\r\n    border-radius: 5px;\r\n    padding: 10px;\r\n    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);\r\n    position: relative;\r\n\r\n    // 计划管理简化样式\r\n    &.plan-management-card {\r\n      .plan-grid {\r\n        display: grid;\r\n        grid-template-columns: 1fr 1fr;\r\n        gap: 4px; /* 缩短网格间距从8px到4px */\r\n        padding: 5px 0; /* 缩短上下内边距从10px到5px */\r\n\r\n        .plan-item {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 6px 35px; /* 缩短内边距从8px到4px 6px */\r\n          border-radius: 6px;\r\n          transition: background 0.2s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 186, 255, 0.1);\r\n          }\r\n\r\n          .plan-icon {\r\n            font-size: 16px;\r\n            color: #00BAFF;\r\n            margin-right: 6px; /* 缩短右边距从8px到6px */\r\n            width: 18px; /* 缩短宽度从20px到18px */\r\n            text-align: center;\r\n          }\r\n\r\n          .plan-text {\r\n            flex: 1;\r\n            min-width: 0;\r\n\r\n            .plan-value {\r\n              color: #fff;\r\n              font-size: 14px;\r\n              font-weight: bold;\r\n              line-height: 1.1; /* 缩短行高从1.2到1.1 */\r\n              margin-bottom: 1px; /* 缩短下边距从2px到1px */\r\n            }\r\n\r\n            .plan-label {\r\n              color: rgba(255, 255, 255, 0.8);\r\n              font-size: 11px;\r\n              font-weight: bold;\r\n              line-height: 1.1; /* 缩短行高从1.2到1.1 */\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 计划执行状态样式\r\n    &.plan-execution-card {\r\n      .plan-execution-grid {\r\n        display: grid;\r\n        grid-template-columns: repeat(4, 1fr);\r\n        gap: 12px;\r\n        padding: 2px 0;\r\n\r\n        .execution-item {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 10px;\r\n          border-radius: 8px;\r\n          background: rgba(0, 186, 255, 0.1);\r\n          border: 1px solid rgba(0, 186, 255, 0.3);\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 186, 255, 0.2);\r\n            border-color: rgba(0, 186, 255, 0.5);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 186, 255, 0.3);\r\n          }\r\n\r\n          .execution-icon {\r\n            font-size: 18px;\r\n            color: #00BAFF;\r\n            margin-right: 10px;\r\n            width: 22px;\r\n            text-align: center;\r\n            flex-shrink: 0;\r\n          }\r\n\r\n          .execution-text {\r\n            flex: 1;\r\n            min-width: 0;\r\n\r\n            .execution-value {\r\n              color: #fff;\r\n              font-size: 16px;\r\n              font-weight: bold;\r\n              line-height: 1.2;\r\n              margin-bottom: 3px;\r\n            }\r\n\r\n            .execution-label {\r\n              color: rgba(255, 255, 255, 0.8);\r\n              font-size: 12px;\r\n              line-height: 1.2;\r\n              font-weight: bold;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    overflow: hidden; // 恢复hidden，防止重叠\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 2px;\r\n      background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n    }\r\n  }\r\n\r\n  .clickable-card {\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 5px 20px rgba(0, 212, 255, 0.3);\r\n      background-color: rgba(33, 10, 56, 0.9);\r\n    }\r\n\r\n    &:active {\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 14px;\r\n    margin-bottom: 5px;\r\n    font-weight: bold;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    color: #fff;\r\n  }\r\n\r\n  .inventory-total {\r\n    font-size: 12px;\r\n    color: #00d4ff;\r\n    font-weight: normal;\r\n    background: rgba(0, 212, 255, 0.1);\r\n    padding: 2px 8px;\r\n    border-radius: 4px;\r\n    border: 1px solid rgba(0, 212, 255, 0.3);\r\n  }\r\n\r\n  .chart-filter-dropdown-container {\r\n    z-index: 10;\r\n  }\r\n\r\n  .chart-filter-dropdown-container select {\r\n    padding: 4px 8px;\r\n    border-radius: 4px;\r\n    background-color: rgba(138, 43, 226, 0.7);\r\n    color: #fff;\r\n    border: 1px solid rgba(0, 212, 255, 0.3);\r\n    font-size: 12px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .chart-filter-dropdown-container select:hover {\r\n    background-color: rgba(138, 43, 226, 0.9);\r\n    border-color: rgba(0, 212, 255, 0.6);\r\n  }\r\n\r\n  .chart {\r\n    flex: 1;\r\n    width: 100%;\r\n    min-height: 150px;\r\n  }\r\n\r\n  .big-number-container {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .big-number {\r\n      color: #00BAFF;\r\n      font-size: 36px;\r\n      font-weight: bold;\r\n      text-align: center;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .unit-text {\r\n      color: #fff;\r\n      font-size: 14px;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .progress-container {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  // 漏斗数据样式\r\n  .funnel-data {\r\n    flex: 1;\r\n    padding: 10px 0;\r\n\r\n    .funnel-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 8px 0;\r\n      border-bottom: 1px solid rgba(0, 186, 255, 0.2);\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .funnel-label {\r\n        color: #fff;\r\n        font-size: 14px;\r\n      }\r\n\r\n      .funnel-value {\r\n        color: #00BAFF;\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 预警信息样式（完全照抄计划管理样式，只改颜色为红色）\r\n  .warning-analysis {\r\n    flex: 1;\r\n    padding: 10px 0;\r\n\r\n    .warning-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 15px;\r\n\r\n      .warning-name {\r\n        color: #fff;\r\n        font-size: 12px;\r\n        width: 80px;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .warning-bar {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-left: 10px;\r\n\r\n        .bar-bg {\r\n          flex: 1;\r\n          height: 8px;\r\n          background: rgba(255, 87, 87, 0.2);\r\n          border-radius: 4px;\r\n          overflow: hidden;\r\n          margin-right: 10px;\r\n\r\n          .bar-fill {\r\n            height: 100%;\r\n            background: linear-gradient(90deg, hsl(0, 85%, 69%), #f31804);\r\n            border-radius: 4px;\r\n            transition: width 0.3s ease;\r\n          }\r\n        }\r\n\r\n        .warning-value {\r\n          color: #FF5757;\r\n          font-size: 12px;\r\n          font-weight: bold;\r\n          width: 60px;\r\n          text-align: right;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 趋势统计样式\r\n  .trend-stats {\r\n    margin-bottom: 5px;\r\n    flex-shrink: 0;\r\n\r\n    .trend-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 3px 0;\r\n\r\n      .trend-label {\r\n        color: #fff;\r\n        font-size: 11px;\r\n      }\r\n\r\n      .trend-value {\r\n        color: #3DE7C9;\r\n        font-size: 12px;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 计划管理样式\r\n  .product-analysis {\r\n    flex: 1;\r\n    padding: 10px 0;\r\n\r\n    .product-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 15px;\r\n\r\n      .product-name {\r\n        color: #fff;\r\n        font-size: 12px;\r\n        width: 80px;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .product-bar {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-left: 10px;\r\n\r\n        .bar-bg {\r\n          flex: 1;\r\n          height: 8px;\r\n          background: rgba(0, 186, 255, 0.2);\r\n          border-radius: 4px;\r\n          overflow: hidden;\r\n          margin-right: 10px;\r\n\r\n          .bar-fill {\r\n            height: 100%;\r\n            background: linear-gradient(90deg, #00BAFF, #3DE7C9);\r\n            border-radius: 4px;\r\n            transition: width 0.3s ease;\r\n          }\r\n        }\r\n\r\n        .product-value {\r\n          color: #00BAFF;\r\n          font-size: 12px;\r\n          font-weight: bold;\r\n          width: 60px;\r\n          text-align: right;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 供应商圆形显示样式\r\n  .supplier-circles {\r\n    position: relative;\r\n    height: 100%;\r\n    padding: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .circle-item {\r\n      position: absolute;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n\r\n      .circle {\r\n        border-radius: 50%;\r\n        border: 2px solid;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-bottom: 8px;\r\n        transition: all 0.3s ease;\r\n\r\n        &:hover {\r\n          transform: scale(1.05);\r\n          filter: brightness(1.2);\r\n        }\r\n\r\n        &.clickable {\r\n          cursor: pointer;\r\n\r\n          &:hover {\r\n            transform: scale(1.1);\r\n            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);\r\n          }\r\n        }\r\n\r\n        .circle-number {\r\n          color: #fff;\r\n          font-weight: bold;\r\n          text-align: center;\r\n          line-height: 1.2;\r\n        }\r\n      }\r\n\r\n      .circle-label {\r\n        color: #fff;\r\n        text-align: center;\r\n        line-height: 1.2;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      // 普通圆形样式（随机位置）\r\n      &.random-position {\r\n        .circle {\r\n          width: 60px;\r\n          height: 60px;\r\n\r\n          .circle-number {\r\n            font-size: 12px;\r\n          }\r\n        }\r\n\r\n        .circle-label {\r\n          font-size: 10px;\r\n          max-width: 60px;\r\n        }\r\n      }\r\n\r\n      // 中心圆形样式（考核情况）\r\n      &.center-circle {\r\n        .circle {\r\n          width: 120px;\r\n          height: 120px;\r\n\r\n          .circle-number {\r\n            font-size: 14px;\r\n          }\r\n        }\r\n\r\n        .circle-label {\r\n          font-size: 12px;\r\n          max-width: 120px;\r\n          margin-top: 5px;\r\n        }\r\n      }\r\n\r\n      // 中心位置\r\n      &.center {\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n      }\r\n    }\r\n  }\r\n\r\n  // 简单显示样式\r\n  .simple-display {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    padding: 20px 0;\r\n\r\n    .display-number {\r\n      color: #FF8C00;\r\n      font-size: 36px;\r\n      font-weight: bold;\r\n      line-height: 1;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .display-label {\r\n      color: #fff;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  // 供方管理统计样式\r\n  .supplier-stats {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    align-items: center;\r\n    padding: 3px 5px;\r\n    gap: 5px;\r\n\r\n    .supplier-stat-item {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 2px;\r\n\r\n      .stat-number {\r\n        color: #FF8C00;\r\n        font-size: 28px;\r\n        font-weight: bold;\r\n        line-height: 1;\r\n        margin-bottom: 3px;\r\n      }\r\n\r\n      .stat-label {\r\n        color: #fff;\r\n        font-size: 15px;\r\n        font-weight: 500;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 供方管理卡片样式\r\n  .supplier-management-card {\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 100%;\r\n\r\n    .card-title {\r\n      flex-shrink: 0;\r\n      margin-bottom: -25px; /* 进一步减小下边距 */\r\n      font-size: 14px;\r\n    }\r\n\r\n    .chart {\r\n      flex-shrink: 0;\r\n      margin-bottom: 0px;\r\n    }\r\n\r\n    .supplier-stats {\r\n      flex: 1;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n  }\r\n\r\n  // 高频物资模块样式\r\n  .high-frequency-content {\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .high-frequency-materials {\r\n    flex: 2 !important;\r\n    margin-bottom: 0px;\r\n  }\r\n\r\n  .price-trend-section {\r\n    flex: 8 !important;\r\n    min-height: 120px; /* 减小最小高度 */\r\n    margin-bottom: 0px;\r\n    margin-top: 5px; /* 减小上边距 */\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 12px;\r\n    color: #00BAFF;\r\n    margin: 0 0 4px 0; /* 减小下边距从8px到4px */\r\n    font-weight: 500;\r\n  }\r\n\r\n  .material-cloud {\r\n    height: 140px;\r\n    width: 100%;\r\n    position: relative;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .chart-placeholder {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    color: #666;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .material-tooltip {\r\n    position: absolute;\r\n    background: rgba(0,0,0,0.8);\r\n    color: #fff;\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n    font-size: 10px;\r\n    white-space: nowrap;\r\n    z-index: 1000;\r\n    pointer-events: none;\r\n  }\r\n\r\n  .mini-chart {\r\n    height: 100px;\r\n    width: 100%;\r\n  }\r\n\r\n  // 滚动条样式\r\n  .material-list::-webkit-scrollbar {\r\n    width: 4px;\r\n  }\r\n\r\n  .material-list::-webkit-scrollbar-track {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .material-list::-webkit-scrollbar-thumb {\r\n    background: rgba(0, 186, 255, 0.5);\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .material-list::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(0, 186, 255, 0.8);\r\n  }\r\n\r\n  /* 响应式样式 - 计划执行状态模块 */\r\n  @media (max-width: 1400px) {\r\n    .plan-execution-card .plan-execution-grid {\r\n      grid-template-columns: repeat(3, 1fr);\r\n    }\r\n  }\r\n\r\n  @media (max-width: 1000px) {\r\n    .plan-execution-card .plan-execution-grid {\r\n      grid-template-columns: repeat(2, 1fr);\r\n    }\r\n  }\r\n\r\n  @media (max-width: 600px) {\r\n    .plan-execution-card .plan-execution-grid {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n\r\n"]}]}