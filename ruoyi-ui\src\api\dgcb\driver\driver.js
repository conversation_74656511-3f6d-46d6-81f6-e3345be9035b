import request from '@/utils/request'

// 查询吨钢承包司机信息列表
export function listDriver(query) {
  return request({
    url: '/web/driver/list',
    method: 'get',
    params: query
  })
}

// 查询吨钢承包司机所有信息列表
export function listAllDriver(query) {
  return request({
    url: '/web/driver/getAllList',
    method: 'get',
    params: query
  })
}

export function getXctgDriverUserList(query) {
  return request({
    url: '/web/driver/getXctgDriverUserList',
    method: 'get',
    params: query
  })
}

export function getXctgDriverCarList(query) {
  return request({
    url: '/web/driver/getXctgDriverCarList',
    method: 'get',
    params: query
  })
}

// 查询指定公司下所有的司主信息
export function getAllListByCompanyId(query) {
  return request({
    url: '/web/driver/getAllListByCompanyId',
    method: 'get',
    params: query
  })
}

// 查询吨钢承包司机信息详细
export function getDriver(id) {
  return request({
    url: '/web/driver/' + id,
    method: 'get'
  })
}

// 新增吨钢承包司机信息
export function addDriver(data) {
  return request({
    url: '/web/driver',
    method: 'post',
    data: data
  })
}

// 修改吨钢承包司机信息
export function updateDriver(data) {
  return request({
    url: '/web/driver',
    method: 'put',
    data: data
  })
}

// 删除吨钢承包司机信息
export function delDriver(id) {
  return request({
    url: '/web/driver/' + id,
    method: 'delete'
  })
}

// 导出吨钢承包司机信息
export function exportDriver(query) {
  return request({
    url: '/web/driver/export',
    method: 'get',
    params: query
  })
}
