package com.ruoyi.app.v1.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * TYjyPermission对象 t_yjy_permission
 * 
 * <AUTHOR>
 * @date 2024-10-21
 */
@Data
public class TYjyPermission extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long formId;

    /** 角色id */
    @Excel(name = "角色id")
    private Long roleId;

    /** 部门id */
    @Excel(name = "部门id")
    private Long deptId;


    private String deptCode;

    private String deptName;


    private String workNo;

    private String ruleType;

    private List<String> workNos=new ArrayList<>();

    private String hiddenFlag;

}
