package com.ruoyi.app.purchase.service.impl;

import com.ruoyi.app.purchase.domain.*;
import com.ruoyi.app.purchase.dto.*;
import com.ruoyi.app.purchase.vo.ProcurementYearlyDetailResultVo;
import com.ruoyi.app.purchase.vo.ProcurementMonthlyResultVo;
import com.ruoyi.app.purchase.mapper.*;
import com.ruoyi.app.purchase.service.IProcurementDashboardService;
import com.ruoyi.web.service.TongyiHttpService;
import com.ruoyi.app.purchase.vo.PurchaseDashboardVO;
import com.ruoyi.app.purchase.vo.PurchaseRealTimeInventoryResultVo;
import com.ruoyi.app.purchase.vo.PurchaseCokingCoalInventoryDetailVo;
import com.ruoyi.app.purchase.vo.PurchaseCokingDailyDetailVo;
import com.ruoyi.app.purchase.vo.PurchaseCokingVo;
import com.ruoyi.app.purchase.vo.ProcurementPriceAndPurchaseResultVo;
import com.ruoyi.app.purchase.vo.ProcurementPurchasePriceVo;
import com.ruoyi.app.purchase.vo.ProcurementPurchaseAmountVo;
import com.ruoyi.app.purchase.vo.PriceDetailVo;
import com.ruoyi.app.purchase.vo.AmountDetailVo;
import com.ruoyi.app.purchase.vo.MaterialNameVo;
import com.ruoyi.app.purchase.vo.MaterialFuturePriceResultVo;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 采购全景
 * @CreateTime: 2025-05-22 11:12
 * @Author: liekkas
 */
@Service
public class ProcurementDashboardServiceImpl implements IProcurementDashboardService {

    private static final Logger logger = LoggerFactory.getLogger(ProcurementDashboardServiceImpl.class);

    @Autowired
    private PurchaseInboundMapper purchaseInboundMapper;

    @Autowired
    private PurchaseDailyFormulaMapper purchaseDailyFormulaMapper;

    @Autowired
    private PurchaseStatisticMapper purchaseStatisticMapper;

    @Autowired
    private PurchaseProviderTotalAmountMapper purchaseProviderTotalAmountMapper;

    @Autowired
    private PurchaseHighFrequencyMaterialMapper purchaseHighFrequencyMaterialMapper;

    @Autowired
    private PurchaseTotalAmountMapper purchaseTotalAmountMapper;

    @Autowired
    private PurchaseMaterialMapper purchaseMaterialMapper;

    @Autowired
    private PurchaseSuppRiskMapper purchaseSuppRiskMapper;

    @Autowired
    private PurchaseInventoryMapper inventoryMapper;

    @Autowired
    private PurchasePlanMapper purchasePlanMapper;

    @Autowired
    private PurchaseScrapSteelMapper purchaseScrapSteelMapper;

    @Autowired
    private ProcurementMaterialPriceMapper procurementMaterialPriceMapper;

    @Autowired
    private ProcurementMaterialAmountMapper procurementMaterialAmountMapper;

    @Override
    public PurchaseDashboardVO showData(ProcurementDto query) {

        //返回值
        PurchaseDashboardVO result = new PurchaseDashboardVO();
        //时间纬度
        int dimensionType = Optional.ofNullable(query.getDimensionType()).orElse(3);

        //1.采购入库数据（PB块）
        PurchaseInbound purchaseInbound = new PurchaseInbound();
        purchaseInbound.setDimensionType(dimensionType);
        List<PurchaseInbound> inboundList = purchaseInboundMapper.selectPurchaseInboundList(purchaseInbound);
        result.setInboundList(inboundList);
        //2.采购现货价格（PB块）
        PurchaseDailyFormula formulaQuery = new PurchaseDailyFormula();
        formulaQuery.setDimensionType(dimensionType);
        List<PurchaseDailyFormula> spotGoodsList = purchaseDailyFormulaMapper.selectPurchaseDailyFormulaList(formulaQuery);
        result.setSpotGoodsList(spotGoodsList);

        //3.核心指标
        PurchaseStatistic purchaseStatistic = new PurchaseStatistic();
        if (dimensionType == 1) {
            purchaseStatistic.setTimeFlag("03");
        }
        if (dimensionType == 2) {
            purchaseStatistic.setTimeFlag("06");
        }
        if (dimensionType == 3) {
            purchaseStatistic.setTimeFlag("12");
        }
        List<PurchaseStatistic> statisticList = purchaseStatisticMapper.selectPurchaseStatisticList(purchaseStatistic);
        if (!statisticList.isEmpty()) {
            result.setPurchaseStatistic(statisticList.get(0));
        }
        //4.供应商全景跟踪金额
        PurchaseProviderTotalAmount purchaseProviderTotalAmount = new PurchaseProviderTotalAmount();
        purchaseProviderTotalAmount.setItemType("CLASS1");
        if (dimensionType == 1) {
            purchaseProviderTotalAmount.setTimeFlag("03");
        }
        if (dimensionType == 2) {
            purchaseProviderTotalAmount.setTimeFlag("06");
        }
        if (dimensionType == 3) {
            purchaseProviderTotalAmount.setTimeFlag("12");
        }
        result.setPurchaseProviderTotalAmountList(purchaseProviderTotalAmountMapper.selectPurchaseProviderTotalAmountList(purchaseProviderTotalAmount).stream().limit(10).collect(Collectors.toList()));
        //5.高频物料
        PurchaseHighFrequencyMaterial purchaseHighFrequencyMaterial = new PurchaseHighFrequencyMaterial();
        if (dimensionType == 1) {
            purchaseHighFrequencyMaterial.setTimeFlag("03");
        }
        if (dimensionType == 2) {
            purchaseHighFrequencyMaterial.setTimeFlag("06");
        }
        if (dimensionType == 3) {
            purchaseHighFrequencyMaterial.setTimeFlag("12");
        }
        result.setPurchaseHighFrequencyMaterialList(Lists.newArrayList());
        //6.采购物料总数(扇形图)
        PurchaseTotalAmount purchaseTotalAmount = new PurchaseTotalAmount();
        if (dimensionType == 1) {
            purchaseTotalAmount.setTimeFlag("03");
        }
        if (dimensionType == 2) {
            purchaseTotalAmount.setTimeFlag("06");
        }
        if (dimensionType == 3) {
            purchaseTotalAmount.setTimeFlag("12");
        }
        List<PurchaseTotalAmount> totalAmountList = purchaseTotalAmountMapper.selectPurchaseTotalAmountList(purchaseTotalAmount);
        if (!totalAmountList.isEmpty()) {
            result.setPurchaseTotalAmount(totalAmountList.get(0));
        }
        //7.采购物料明细
        PurchaseMaterial purchaseMaterial = new PurchaseMaterial();
        if (dimensionType == 1) {
            purchaseMaterial.setTimeFlag("03");
        }
        if (dimensionType == 2) {
            purchaseMaterial.setTimeFlag("06");
        }
        if (dimensionType == 3) {
            purchaseMaterial.setTimeFlag("12");
        }
        //7.1大类
        purchaseMaterial.setItemType("CLASS1");
        result.setPurchaseClassOneMaterialList(purchaseMaterialMapper.selectPurchaseMaterialList(purchaseMaterial).stream()
                .limit(6) // 关键方法
                .collect(Collectors.toList()));
        //7.2中类
        purchaseMaterial.setItemType("CLASS2");
        result.setPurchaseClassTwoMaterialList(purchaseMaterialMapper.selectPurchaseMaterialList(purchaseMaterial).stream()
                .limit(6) // 关键方法
                .collect(Collectors.toList()));
        //7.3细类
        purchaseMaterial.setItemType("CLASS3");
        result.setPurchaseClassThreeMaterialList(purchaseMaterialMapper.selectPurchaseMaterialList(purchaseMaterial).stream()
                .limit(6) // 关键方法
                .collect(Collectors.toList()));
        //7.4叶类
        purchaseMaterial.setItemType("LEAF");
        result.setPurchaseClassFourMaterialList(purchaseMaterialMapper.selectPurchaseMaterialList(purchaseMaterial).stream()
                .limit(6) // 关键方法
                .collect(Collectors.toList()));

        //8.供应商信息
        PurchaseSuppRisk purchaseSuppRisk = new PurchaseSuppRisk();
        if (dimensionType == 1) {
            purchaseSuppRisk.setTimeFlag("03");
        }
        if (dimensionType == 2) {
            purchaseSuppRisk.setTimeFlag("06");
        }
        if (dimensionType == 3) {
            purchaseSuppRisk.setTimeFlag("12");
        }
        result.setPurchaseSuppRiskList(purchaseSuppRiskMapper.selectPurchaseSuppRiskList(purchaseSuppRisk));

        return result;
    }

    @Override
    public List<PurchaseSuppRisk> showPurchaseSuppRisk(PurchaseSuppRisk purchaseSuppRisk) {
        return purchaseSuppRiskMapper.selectPurchaseSuppRiskList(purchaseSuppRisk);
    }


    @Override
    public List<PurchaseMaterial> showItemTypeList(ProcurementOptionDto query) {
        PurchaseMaterial purchaseMaterial = new PurchaseMaterial();
        purchaseMaterial.setItemName(query.getItemName());
        if (query.getItemType().equals("1")) {
            purchaseMaterial.setItemType("CLASS1");
        }
        if (query.getItemType().equals("2")) {
            purchaseMaterial.setItemType("CLASS2");
        }
        if (query.getItemType().equals("3")) {
            purchaseMaterial.setItemType("CLASS3");
        }
        return purchaseMaterialMapper.selectItemTypeList(purchaseMaterial);
    }

    @Override
    public List<PurchaseMaterial> showMaterialList(ProcurementDetailDto query) {
        PurchaseMaterial purchaseMaterial = new PurchaseMaterial();
        purchaseMaterial.setItemId(query.getItemId());
        //1.类别
        if (query.getItemType().equals("1")) {
            purchaseMaterial.setItemType("CLASS1");
        }
        if (query.getItemType().equals("2")) {
            purchaseMaterial.setItemType("CLASS2");
        }
        if (query.getItemType().equals("3")) {
            purchaseMaterial.setItemType("CLASS3");
        }
        if (query.getItemType().equals("4")) {
            purchaseMaterial.setItemType("LEAF");
        }
        //2.时间维度
        int dimensionType = Optional.ofNullable(query.getDimensionType()).orElse(3);
        if (dimensionType == 1) {
            purchaseMaterial.setTimeFlag("03");
        }
        if (dimensionType == 2) {
            purchaseMaterial.setTimeFlag("06");
        }
        if (dimensionType == 3) {
            purchaseMaterial.setTimeFlag("12");
        }
        List<PurchaseMaterial> list = purchaseMaterialMapper.selectPurchaseMaterialList(purchaseMaterial);
        return list.stream()
                .limit(10) // 关键方法
                .collect(Collectors.toList());
    }

    @Override
    public List<PurchaseHighFrequencyMaterial> showHighFrequencyMaterialList(ProcurementFrequencyDto query) {
        PurchaseHighFrequencyMaterial highFrequencyMaterial = new PurchaseHighFrequencyMaterial();
        //1.时间维度
        int dimensionType = Optional.ofNullable(query.getDimensionType()).orElse(3);
        if (dimensionType == 1) {
            highFrequencyMaterial.setTimeFlag("03");
        }
        if (dimensionType == 2) {
            highFrequencyMaterial.setTimeFlag("06");
        }
        if (dimensionType == 3) {
            highFrequencyMaterial.setTimeFlag("12");
        }
        highFrequencyMaterial.setCodeType(query.getCodeType());
        highFrequencyMaterial.setItemType(query.getItemType());
        return purchaseHighFrequencyMaterialMapper.selectPurchaseHighFrequencyListTopFifty(highFrequencyMaterial);
    }

    @Override
    public List<PurchaseProviderTotalAmount> showSuppList(ProcurementSuppDto procurementSuppDto) {
        PurchaseProviderTotalAmount purchaseProviderTotalAmount = new PurchaseProviderTotalAmount();
        purchaseProviderTotalAmount.setItemType("CLASS1");
        purchaseProviderTotalAmount.setItemId(procurementSuppDto.getItemId());
        purchaseProviderTotalAmount.setOrderType(procurementSuppDto.getOrderType());
        //2.时间维度
        int dimensionType = Optional.ofNullable(procurementSuppDto.getDimensionType()).orElse(3);
        if (dimensionType == 1) {
            purchaseProviderTotalAmount.setTimeFlag("03");
        }
        if (dimensionType == 2) {
            purchaseProviderTotalAmount.setTimeFlag("06");
        }
        if (dimensionType == 3) {
            purchaseProviderTotalAmount.setTimeFlag("12");
        }
        return purchaseProviderTotalAmountMapper.selectPurchaseProviderTotalAmountList(purchaseProviderTotalAmount).stream()
                .limit(10) // 关键方法
                .collect(Collectors.toList());
    }

    @Override
    public List<PurchasePlan> showPurchasePlanList() {
        try {
            return purchasePlanMapper.selectPurchasePlanList();
        }catch (Exception e){
        logger.error("查询采购计划出错", e);
        return new ArrayList<>();
    }
    }

    @Override
    public PurchaseStatistic showKeyIndicators(ProcurementDto query) {
        PurchaseStatistic result = new PurchaseStatistic();
        //时间维度
        int dimensionType = Optional.ofNullable(query.getDimensionType()).orElse(3);
        //核心指标
        PurchaseStatistic purchaseStatistic = new PurchaseStatistic();
        if (dimensionType == 1) {
            purchaseStatistic.setTimeFlag("03");
        }
        if (dimensionType == 2) {
            purchaseStatistic.setTimeFlag("06");
        }
        if (dimensionType == 3) {
            purchaseStatistic.setTimeFlag("12");
        }
        List<PurchaseStatistic> statisticList = purchaseStatisticMapper.selectPurchaseStatisticList(purchaseStatistic);
        if (!statisticList.isEmpty()) {
            result = statisticList.get(0);
        }
        return result;
    }

    @Override
    public List<ProcurementYearlyDetailResultVo> showYearlyAmount(ProcurementYearlyQueryDto query) {
        try {
            // 查询库存金额数据
            List<ProcurementMonthlyResultVo> monthlyData = inventoryMapper.selectYearlyInventoryAmount(query);

            // 获取年份列表
            Set<String> yearSet = new LinkedHashSet<>();
            if (query.getYearList() != null && !query.getYearList().isEmpty()) {
                // 如果传入了年份列表，使用传入的年份
                yearSet.addAll(query.getYearList());
            } else {
                // 如果没有传入年份列表，从查询结果中提取所有年份
                for (ProcurementMonthlyResultVo monthData : monthlyData) {
                    if (monthData.getYear() != null) {
                        yearSet.add(monthData.getYear());
                    }
                }
            }

            // 如果没有任何数据，返回空结果
            if (yearSet.isEmpty()) {
                return new ArrayList<>();
            }

            // 按年份分组数据，只包含有实际数据的月份
            Map<String, List<ProcurementMonthlyResultVo>> yearGroupData = new LinkedHashMap<>();

            // 初始化年份分组
            for (String year : yearSet) {
                yearGroupData.put(year, new ArrayList<>());
            }

            // 填充实际查询到的数据
            for (ProcurementMonthlyResultVo monthData : monthlyData) {
                String year = monthData.getYear();

                if (yearGroupData.containsKey(year)) {
                    // 创建新的月份数据对象，不包含year字段（用于返回）
                    ProcurementMonthlyResultVo resultVo = new ProcurementMonthlyResultVo();
                    resultVo.setMonthIndex(monthData.getMonthIndex());
                    resultVo.setAmount(monthData.getAmount());

                    yearGroupData.get(year).add(resultVo);
                }
            }

            // 构建返回结果，按年份排序
            List<String> sortedYears = new ArrayList<>(yearSet);
            Collections.sort(sortedYears);

            List<ProcurementYearlyDetailResultVo> resultList = new ArrayList<>();
            for (String year : sortedYears) {
                ProcurementYearlyDetailResultVo yearlyResult = new ProcurementYearlyDetailResultVo();
                yearlyResult.setYear(year);

                // 获取该年份的月份数据并按月份排序
                List<ProcurementMonthlyResultVo> monthlyList = yearGroupData.get(year);
                monthlyList.sort(Comparator.comparingInt(ProcurementMonthlyResultVo::getMonthIndex));

                yearlyResult.setMonthlyResultVoList(monthlyList);
                resultList.add(yearlyResult);
            }

            return resultList;
        } catch (Exception e) {
            logger.error("查询库存金额年度统计出错", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<PurchaseRealTimeInventoryResultVo> showRealTimeAmount() {
        try {
            // 查询中心仓库金额
            List<PurchaseInventoryQueryDto> centerInventoryList = inventoryMapper.selectCenterInventoryAmount();

            // 查询机旁库金额
            List<PurchaseInventoryQueryDto> machineSideInventoryList = inventoryMapper.selectMachineSideInventoryAmount();

            // 将机旁库数据转换为Map，以class1为key
            Map<String, BigDecimal> machineSideInventoryMap = machineSideInventoryList.stream()
                    .collect(Collectors.toMap(
                            PurchaseInventoryQueryDto::getClass1,
                            dto -> dto.getStockMoney() != null ? dto.getStockMoney() : BigDecimal.ZERO,
                            (existing, replacement) -> existing
                    ));

            // 合并数据并构建结果
            List<PurchaseRealTimeInventoryResultVo> resultList = new ArrayList<>();

            for (PurchaseInventoryQueryDto centerData : centerInventoryList) {
                PurchaseRealTimeInventoryResultVo resultVo = new PurchaseRealTimeInventoryResultVo();

                // 设置物料类型编码和名称
                resultVo.setMaterialType(centerData.getClass1());
                resultVo.setMaterialName(centerData.getClass1Name());

                // 设置中心仓库库存金额
                BigDecimal centerAmount = centerData.getStockMoney() != null ? centerData.getStockMoney() : BigDecimal.ZERO;
                resultVo.setCenterInventoryAmount(centerAmount);

                // 设置机旁库库存金额
                BigDecimal machineSideAmount = machineSideInventoryMap.getOrDefault(centerData.getClass1(), BigDecimal.ZERO);
                resultVo.setMachineSideInventoryAmount(machineSideAmount);

                // 计算总金额
                BigDecimal totalAmount = centerAmount.add(machineSideAmount);
                resultVo.setTotalInventoryAmount(totalAmount);

                resultList.add(resultVo);
            }

            // 处理只在机旁库中存在的类型
            for (PurchaseInventoryQueryDto machineSideData : machineSideInventoryList) {
                String class1 = machineSideData.getClass1();
                // 检查是否已经在结果列表中
                boolean existsInResult = resultList.stream()
                        .anyMatch(result -> class1.equals(result.getMaterialType()));

                if (!existsInResult) {
                    PurchaseRealTimeInventoryResultVo resultVo = new PurchaseRealTimeInventoryResultVo();
                    resultVo.setMaterialType(class1);
                    resultVo.setMaterialName(machineSideData.getClass1Name()); // 可能为空
                    resultVo.setCenterInventoryAmount(BigDecimal.ZERO);

                    BigDecimal machineSideAmount = machineSideData.getStockMoney() != null ? machineSideData.getStockMoney() : BigDecimal.ZERO;
                    resultVo.setMachineSideInventoryAmount(machineSideAmount);
                    resultVo.setTotalInventoryAmount(machineSideAmount);

                    resultList.add(resultVo);
                }
            }

            // 按总金额降序排序
            resultList.sort((a, b) -> {
                BigDecimal totalA = a.getTotalInventoryAmount() != null ? a.getTotalInventoryAmount() : BigDecimal.ZERO;
                BigDecimal totalB = b.getTotalInventoryAmount() != null ? b.getTotalInventoryAmount() : BigDecimal.ZERO;
                return totalB.compareTo(totalA);
            });

            return resultList;

        } catch (Exception e) {
            logger.error("查询实时库存金额出错", e);
            return new ArrayList<>();
        }
    }
    @Override
    public List<PurchaseTotalAmount> showAmtManage() {
        try{
            return inventoryMapper.selectPlanInventoryAmount();
        }catch (Exception e){
            logger.error("查询拟入库总金额出错", e);
            return new ArrayList<>();
        }
    }



    @Override
    public List<PurchaseCokingVo> showCokingCoalAmount() {
        try {
            //获取七天前的日期，比如20250719
            String sevenDaysAgoDate = LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 先尝试查询七天前的数据
            List<PurchaseCokingCoalInventoryDetailVo> sevenDaysAgoData = inventoryMapper.selectCokingCoalInventoryByDate(sevenDaysAgoDate);

            //将class2和class2Name作为key分组
            Map<String, List<PurchaseCokingCoalInventoryDetailVo>> groupByType = sevenDaysAgoData.stream()
                    .collect(Collectors.groupingBy(item -> item.getClass2() + "_" + item.getClass2Name()));
            
            //将groupByType转换为List<PurchaseCokingVo>
            List<PurchaseCokingVo> purchaseCokingVoList = new ArrayList<>();
            for (Map.Entry<String, List<PurchaseCokingCoalInventoryDetailVo>> entry : groupByType.entrySet()) {
                PurchaseCokingVo purchaseCokingVo = new PurchaseCokingVo();
                purchaseCokingVo.setClass2(entry.getKey().split("_")[0]);
                purchaseCokingVo.setClass2Name(entry.getKey().split("_")[1]);
                purchaseCokingVo.setPurchaseCokingDailyDetailList(entry.getValue().stream()
                        .map(item -> {
                            PurchaseCokingDailyDetailVo purchaseCokingDailyDetailVo = new PurchaseCokingDailyDetailVo();
                            purchaseCokingDailyDetailVo.setInstockDate(item.getInstockDate());
                            purchaseCokingDailyDetailVo.setInvQty(item.getInvQty());
                            return purchaseCokingDailyDetailVo;
                        }).collect(Collectors.toList()));
                purchaseCokingVoList.add(purchaseCokingVo);
            }

            return purchaseCokingVoList;

        } catch (Exception e) {
            logger.error("查询矿焦煤库存出错", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ProcurementPriceAndPurchaseResultVo> getPurchasePriceAndStore(ProcurementPriceAndStoreQueryDto query) {
        try {
            // 时间维度，默认为近一年
            int dimensionType = Optional.ofNullable(query.getDimensionType()).orElse(3);

            // 获取查询的物资列表
            List<PriceAndStoreDetailDto> itemList = query.getItemList();
            if (itemList == null || itemList.isEmpty()) {
                logger.warn("查询物资列表为空");
                return new ArrayList<>();
            }

            // 提取所有物资名称并分类
            Set<String> allItemNames = new HashSet<>();
            Set<String> priceNeededItems = new HashSet<>();
            Set<String> amountNeededItems = new HashSet<>();

            // 用于跟踪每个物资需要的曲线类型（支持同一物资多种曲线类型）
            Map<String, Set<Integer>> itemCurveTypesMap = new HashMap<>();

            for (PriceAndStoreDetailDto item : itemList) {
                String itemName = item.getItemName();
                Integer curveType = item.getCurveType();

                if (itemName == null || itemName.trim().isEmpty()) {
                    logger.warn("物资名称为空，跳过处理");
                    continue;
                }

                String trimmedName = itemName.trim();
                allItemNames.add(trimmedName);

                // 为每个物资维护一个曲线类型集合
                itemCurveTypesMap.computeIfAbsent(trimmedName, k -> new HashSet<>()).add(curveType);

                // 根据曲线类型决定需要查询哪些数据
                boolean needPriceData = (curveType == null || curveType == 1); // 未指定或价格曲线
                boolean needAmountData = (curveType == null || curveType == 2); // 未指定或采购量曲线

                if (needPriceData) {
                    priceNeededItems.add(trimmedName);
                }
                if (needAmountData) {
                    amountNeededItems.add(trimmedName);
                }
            }

            if (allItemNames.isEmpty()) {
                logger.warn("没有有效的查询物资");
                return new ArrayList<>();
            }

            logger.info("批量查询物资：{}, 需要价格数据的物资：{}, 需要采购量数据的物资：{}",
                       allItemNames.size(), priceNeededItems.size(), amountNeededItems.size());

            // 批量查询所有价格数据
            Map<String, Map<String, Map<String, BigDecimal>>> allPriceData = new HashMap<>();
            if (!priceNeededItems.isEmpty()) {
                queryAllPriceDataBatch(priceNeededItems, dimensionType, allPriceData);
            }

            // 批量查询所有采购量数据
            Map<String, Map<String, Map<String, BigDecimal>>> allAmountData = new HashMap<>();
            if (!amountNeededItems.isEmpty()) {
                queryAllAmountDataBatch(amountNeededItems, dimensionType, allAmountData);
            }

            // 组装结果数据
            List<ProcurementPriceAndPurchaseResultVo> resultList = new ArrayList<>();

            for (String itemName : allItemNames) {
                Set<Integer> curveTypes = itemCurveTypesMap.get(itemName);

                // 判断该物资需要哪些类型的数据
                boolean needPriceData = curveTypes.contains(null) || curveTypes.contains(1);
                boolean needAmountData = curveTypes.contains(null) || curveTypes.contains(2);

                ProcurementPriceAndPurchaseResultVo materialData = new ProcurementPriceAndPurchaseResultVo();
                materialData.setItemName(itemName);

                // 设置价格列表
                List<ProcurementPurchasePriceVo> priceVoList = new ArrayList<>();
                if (needPriceData && allPriceData.containsKey(itemName)) {
                    Map<String, Map<String, BigDecimal>> materialPriceTypeByDateMap = allPriceData.get(itemName);

                    for (Map.Entry<String, Map<String, BigDecimal>> priceTypeEntry : materialPriceTypeByDateMap.entrySet()) {
                        String priceTypeName = priceTypeEntry.getKey();
                        Map<String, BigDecimal> priceByDateMap = priceTypeEntry.getValue();

                        if (priceByDateMap != null && !priceByDateMap.isEmpty()) {
                            List<PriceDetailVo> priceDetailList = new ArrayList<>();
                            for (Map.Entry<String, BigDecimal> entry : priceByDateMap.entrySet()) {
                                PriceDetailVo priceDetail = new PriceDetailVo();
                                priceDetail.setPrice(entry.getValue());
                                priceDetail.setRecordDate(entry.getKey());
                                priceDetailList.add(priceDetail);
                            }

                            // 按日期排序
                            priceDetailList.sort((a, b) -> a.getRecordDate().compareTo(b.getRecordDate()));

                            ProcurementPurchasePriceVo priceVo = new ProcurementPurchasePriceVo();
                            priceVo.setPriceName(priceTypeName);
                            priceVo.setPriceList(priceDetailList);
                            priceVoList.add(priceVo);
                        }
                    }
                }
                materialData.setProcurementPriceVoList(priceVoList);

                // 设置采购量列表
                List<ProcurementPurchaseAmountVo> purchaseAmountList = new ArrayList<>();
                if (needAmountData && allAmountData.containsKey(itemName)) {
                    Map<String, Map<String, BigDecimal>> materialAmountTypeByDateMap = allAmountData.get(itemName);

                    for (Map.Entry<String, Map<String, BigDecimal>> amountTypeEntry : materialAmountTypeByDateMap.entrySet()) {
                        String amountTypeName = amountTypeEntry.getKey();
                        Map<String, BigDecimal> amountByDateMap = amountTypeEntry.getValue();

                        if (amountByDateMap != null && !amountByDateMap.isEmpty()) {
                            List<AmountDetailVo> amountDetailList = new ArrayList<>();
                            for (Map.Entry<String, BigDecimal> entry : amountByDateMap.entrySet()) {
                                AmountDetailVo amountDetail = new AmountDetailVo();
                                amountDetail.setAmount(entry.getValue());
                                amountDetail.setRecordDate(entry.getKey());
                                amountDetailList.add(amountDetail);
                            }

                            // 按日期排序
                            amountDetailList.sort((a, b) -> a.getRecordDate().compareTo(b.getRecordDate()));

                            ProcurementPurchaseAmountVo purchaseAmountVo = new ProcurementPurchaseAmountVo();
                            purchaseAmountVo.setAmountName(amountTypeName);
                            purchaseAmountVo.setAmountList(amountDetailList);
                            purchaseAmountList.add(purchaseAmountVo);
                        }
                    }
                }
                materialData.setProcurementPurchaseAmountVoList(purchaseAmountList);

                // 只有当有价格数据或采购量数据时才添加到结果中
                if (!priceVoList.isEmpty() || !purchaseAmountList.isEmpty()) {
                    resultList.add(materialData);
                }
            }

            return resultList;

        } catch (Exception e) {
            logger.error("查询物资价格和入库数量出错", e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量查询所有物资的价格数据（优化版本）
     */
    private void queryAllPriceDataBatch(Set<String> itemNames, int dimensionType,
                                      Map<String, Map<String, Map<String, BigDecimal>>> allPriceData) {
        try {
            List<String> itemNameList = new ArrayList<>(itemNames);

            // 1. 批量查询矿石、煤炭、合金价格数据
            List<PurchaseDailyFormula> formulaList = purchaseDailyFormulaMapper.selectPurchaseDailyFormulaListByItemNames(itemNameList, dimensionType);

            for (PurchaseDailyFormula formula : formulaList) {
                String spotName = formula.getSpotName();
                if (spotName == null || spotName.trim().isEmpty()) {
                    continue;
                }

                String itemName = spotName.trim();
                String recordDate = formula.getStatisticalDate().replace("-", ""); // 转换为yyyyMMdd格式
                BigDecimal price = null;
                String priceTypeName = "";

                if ("ore".equals(formula.getPurchaseType())) {
                    price = formula.getSpotPrice();
                    priceTypeName = "现货价";
                } else if ("coal".equals(formula.getPurchaseType()) || "alloy".equals(formula.getPurchaseType())) {
                    price = formula.getSpotFormulaResult();
                    priceTypeName = "市场采购到厂价";
                }

                if (price != null && !priceTypeName.isEmpty()) {
                    allPriceData.computeIfAbsent(itemName, k -> new HashMap<>())
                              .computeIfAbsent(priceTypeName, k -> new HashMap<>())
                              .put(recordDate, price);
                }
            }

            // 2. 批量查询废钢价格数据
            List<PurchaseScrapSteel> scrapSteelList = purchaseScrapSteelMapper.selectPurchaseScrapSteelListByItemNames(itemNameList);

            // 按物资名称和价格类型分组废钢价格数据
            Map<String, Map<String, Map<String, BigDecimal>>> scrapSteelPriceGroupMap = new HashMap<>();

            for (PurchaseScrapSteel scrapSteel : scrapSteelList) {
                String materialName = scrapSteel.getItemName();
                if (materialName == null || materialName.trim().isEmpty()) {
                    continue;
                }

                String itemName = materialName.trim();
                BigDecimal price = scrapSteel.getPrice();
                String recordDate = "";
                if (scrapSteel.getMaintenanceDate() != null) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    recordDate = sdf.format(scrapSteel.getMaintenanceDate());
                }

                String priceTypeName = "";
                String transportDesc = "";
                String companyDesc = "";

                // 构建价格类型名称
                if (scrapSteel.getTransportMode() != null && !scrapSteel.getTransportMode().trim().isEmpty()) {
                    String trimmedTransportMode = scrapSteel.getTransportMode().trim();
                    // 处理中文运输方式
                    if (trimmedTransportMode.contains("车") || "1".equals(trimmedTransportMode)) {
                        transportDesc = "车运";
                    } else if (trimmedTransportMode.contains("船") || "2".equals(trimmedTransportMode)) {
                        transportDesc = "船运";
                    } else {
                        transportDesc = trimmedTransportMode; // 保持原值
                    }
                    logger.debug("废钢运输方式原始值: '{}', 处理后: '{}'", scrapSteel.getTransportMode(), transportDesc);
                }

                if (scrapSteel.getCompany() != null && !scrapSteel.getCompany().trim().isEmpty()) {
                    companyDesc = scrapSteel.getCompany().trim();
                    logger.debug("废钢公司: '{}'", companyDesc);
                }

                if (!transportDesc.isEmpty() && !companyDesc.isEmpty()) {
                    priceTypeName = companyDesc + "废钢收购价" + "(" + transportDesc + ")";
                } else if (!companyDesc.isEmpty()) {
                    priceTypeName = companyDesc + "废钢收购价";
                } else if (!transportDesc.isEmpty()) {
                    priceTypeName = transportDesc + "废钢收购价";
                } else {
                    priceTypeName = "废钢收购价";
                }

                if (price != null && !recordDate.isEmpty() && !priceTypeName.isEmpty()) {
                    scrapSteelPriceGroupMap.computeIfAbsent(itemName, k -> new HashMap<>())
                            .computeIfAbsent(priceTypeName, k -> new HashMap<>())
                            .put(recordDate, price);
                }
            }

            // 将废钢价格数据直接添加到结果中（不进行数据补充处理）
            for (String itemName : itemNames) {
                if (scrapSteelPriceGroupMap.containsKey(itemName)) {
                    Map<String, Map<String, BigDecimal>> itemPriceMap = scrapSteelPriceGroupMap.get(itemName);
                    for (Map.Entry<String, Map<String, BigDecimal>> priceTypeEntry : itemPriceMap.entrySet()) {
                        String priceTypeName = priceTypeEntry.getKey();
                        Map<String, BigDecimal> priceByDateMap = priceTypeEntry.getValue();
                        allPriceData.computeIfAbsent(itemName, k -> new HashMap<>())
                                   .put(priceTypeName, priceByDateMap);
                    }
                }
            }

            // 批量处理废钢价格数据的补充 - 已注释
            /*
            for (String itemName : itemNames) {
                if (scrapSteelPriceGroupMap.containsKey(itemName)) {
                    this.fillScrapSteelPriceDataForItem(scrapSteelPriceGroupMap.get(itemName), dimensionType, itemName, allPriceData);
                }
            }
            */

        } catch (Exception e) {
            logger.error("批量查询物资价格数据出错", e);
        }
    }

    /**
     * 批量查询所有物资的采购量数据（优化版本）
     */
    private void queryAllAmountDataBatch(Set<String> itemNames, int dimensionType,
                                       Map<String, Map<String, Map<String, BigDecimal>>> allAmountData) {
        try {
            List<String> itemNameList = new ArrayList<>(itemNames);

            // 1. 批量查询六大类物资入库数量
            List<PurchaseInbound> inboundList = purchaseInboundMapper.selectPurchaseInboundListByItemNames(itemNameList, dimensionType);

            for (PurchaseInbound inbound : inboundList) {
                String materialName = inbound.getItemName();
                if (materialName == null || materialName.trim().isEmpty()) {
                    continue;
                }

                String itemName = materialName.trim();
                String recordDate = inbound.getStatisticalDate().replace("-", ""); // 转换为yyyyMMdd格式
                String amountTypeName = "采购量";

                if (inbound.getInNum() != null) {
                    allAmountData.computeIfAbsent(itemName, k -> new HashMap<>())
                                .computeIfAbsent(amountTypeName, k -> new HashMap<>())
                                .put(recordDate, inbound.getInNum());
                }
            }

            // 2. 批量查询废钢入库数量 - 车运
            List<PurchaseInbound> scrapSteelTruckList = purchaseInboundMapper.selectScrapSteelStorageByItemNames(itemNameList, dimensionType, 1);

            for (PurchaseInbound scrapSteelInbound : scrapSteelTruckList) {
                String materialName = scrapSteelInbound.getItemName();
                if (materialName == null || materialName.trim().isEmpty()) {
                    continue;
                }

                String itemName = materialName.trim();
                String recordDate = scrapSteelInbound.getStatisticalDate().replace("-", "");
                String amountTypeName = "车运采购量";

                if (scrapSteelInbound.getInNum() != null) {
                    allAmountData.computeIfAbsent(itemName, k -> new HashMap<>())
                                .computeIfAbsent(amountTypeName, k -> new HashMap<>())
                                .put(recordDate, scrapSteelInbound.getInNum());
                }
            }

            // 3. 批量查询废钢入库数量 - 船运
            List<PurchaseInbound> scrapSteelShipList = purchaseInboundMapper.selectScrapSteelStorageByItemNames(itemNameList, dimensionType, 2);

            for (PurchaseInbound scrapSteelInbound : scrapSteelShipList) {
                String materialName = scrapSteelInbound.getItemName();
                if (materialName == null || materialName.trim().isEmpty()) {
                    continue;
                }

                String itemName = materialName.trim();
                String recordDate = scrapSteelInbound.getStatisticalDate().replace("-", "");
                String amountTypeName = "船运采购量";

                if (scrapSteelInbound.getInNum() != null) {
                    allAmountData.computeIfAbsent(itemName, k -> new HashMap<>())
                                .computeIfAbsent(amountTypeName, k -> new HashMap<>())
                                .put(recordDate, scrapSteelInbound.getInNum());
                }
            }

        } catch (Exception e) {
            logger.error("批量查询物资采购量数据出错", e);
        }
    }

    /**
     * 为单个物资填充废钢价格数据（从批量优化后拆分的方法）
     */
    private void fillScrapSteelPriceDataForItem(Map<String, Map<String, BigDecimal>> itemScrapSteelPriceMap,
                                              int dimensionType, String itemName,
                                              Map<String, Map<String, Map<String, BigDecimal>>> allPriceData) {
        try {
            // 获取时间范围
            Calendar calendar = Calendar.getInstance();
            Date endDate = calendar.getTime();

            // 根据时间维度设置开始时间
            switch (dimensionType) {
                case 1: // 近3个月
                    calendar.add(Calendar.MONTH, -3);
                    break;
                case 2: // 近6个月
                    calendar.add(Calendar.MONTH, -6);
                    break;
                case 3: // 近1年
                default:
                    calendar.add(Calendar.YEAR, -1);
                    break;
            }
            Date startDate = calendar.getTime();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String startDateStr = sdf.format(startDate);
            String endDateStr = sdf.format(endDate);

            // 为每种价格类型补充数据
            for (Map.Entry<String, Map<String, BigDecimal>> priceTypeEntry : itemScrapSteelPriceMap.entrySet()) {
                String priceTypeName = priceTypeEntry.getKey();
                Map<String, BigDecimal> priceByDateMap = priceTypeEntry.getValue();

                if (priceByDateMap == null || priceByDateMap.isEmpty()) {
                    continue;
                }

                // 获取已有的日期范围
                String minDate = priceByDateMap.keySet().stream().min(String::compareTo).orElse("");
                String maxDate = priceByDateMap.keySet().stream().max(String::compareTo).orElse("");

                if (minDate.isEmpty() || maxDate.isEmpty()) {
                    continue;
                }

                // 确定实际的开始和结束日期
                String actualStartDate = startDateStr.compareTo(minDate) > 0 ? startDateStr : minDate;
                String actualEndDate = endDateStr.compareTo(maxDate) < 0 ? endDateStr : maxDate;

                // 补充缺失的日期数据
                Calendar fillCalendar = Calendar.getInstance();
                try {
                    fillCalendar.setTime(sdf.parse(actualStartDate));
                } catch (ParseException e) {
                    logger.warn("日期解析失败: " + actualStartDate, e);
                    continue;
                }

                Calendar endCalendar = Calendar.getInstance();
                try {
                    endCalendar.setTime(sdf.parse(actualEndDate));
                } catch (ParseException e) {
                    logger.warn("日期解析失败: " + actualEndDate, e);
                    continue;
                }

                BigDecimal lastPrice = null;
                while (!fillCalendar.after(endCalendar)) {
                    String currentDateStr = sdf.format(fillCalendar.getTime());

                    if (priceByDateMap.containsKey(currentDateStr)) {
                        lastPrice = priceByDateMap.get(currentDateStr);
                    } else if (lastPrice != null) {
                        // 使用前一个价格填充缺失的日期
                        priceByDateMap.put(currentDateStr, lastPrice);
                    }

                    fillCalendar.add(Calendar.DAY_OF_MONTH, 1);
                }

                // 将补充后的数据添加到结果中
                allPriceData.computeIfAbsent(itemName, k -> new HashMap<>())
                           .put(priceTypeName, priceByDateMap);
            }

        } catch (Exception e) {
            logger.error("补充废钢价格数据出错: " + itemName, e);
        }
    }

    @Autowired
    private TongyiHttpService tongyiHttpService;

    @Override
    public MaterialFuturePriceResultVo getMaterialFuturePrice(ProcurementFuturePriceDto query) {
        MaterialFuturePriceResultVo result = new MaterialFuturePriceResultVo();
        
        try {
            logger.info("开始调用大模型预测物料价格，物料名称: {}", query.getMaterialName());

            // 参数校验
            if (query == null || !StringUtils.hasText(query.getMaterialName())) {
                logger.warn("物料名称为空，无法进行价格预测");
                result.setMaterialName("");
                result.setQuestion("");
                result.setAnswer("请提供有效的物料名称");
                result.setSuccess(false);
                result.setErrorMessage("物料名称为空");
                return result;
            }

            String materialName = query.getMaterialName();
            String materialType = query.getMaterialType();
            
            // 构建提问语句
            String question = buildPredictionQuestion(materialName, materialType);
            result.setMaterialName(materialName);
            result.setQuestion(question);

            // 调用通义千问HTTP API进行价格预测
            String prediction;
            logger.info("使用网络搜索进行详细价格预测");
            prediction = tongyiHttpService.predictPriceWithSearch(materialName, materialType);

            logger.info("物料价格预测完成，物料: {}, 预测结果长度: {}",
                    materialName, prediction.length());

            result.setAnswer(prediction);
            result.setSuccess(true);
            return result;

        } catch (Exception e) {
            logger.error("预测物料价格时发生异常，物料名称: {}", query.getMaterialName(), e);
            result.setMaterialName(query.getMaterialName());
            result.setQuestion(buildPredictionQuestion(query.getMaterialName(), query.getMaterialType()));
            result.setAnswer("价格预测服务暂时不可用，请稍后重试。");
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            return result;
        }
    }

    /**
     * 检查是否提供了详细参数
     *
     * @param query 价格预测请求参数
     * @return 是否有详细参数
     */
    private boolean hasDetailedParams(ProcurementFuturePriceDto query) {
        return StringUtils.hasText(query.getItemId())
                || StringUtils.hasText(query.getItemName())
                || query.getCurrentPrice() != null
                || query.getPredictPeriod() != null
                || StringUtils.hasText(query.getPriceHistory())
                || query.getScenario() != null;
    }

    /**
     * 构建价格预测问题语句
     *
     * @param materialName 物料名称
     * @param materialType 物料类型
     * @return 问题语句
     */
    private String buildPredictionQuestion(String materialName, String materialType) {
        String baseQuestion = "作为一名专业的大宗商品价格分析师，请对以下物料的未来价格走势进行预测分析: %s。请搜索最新的市场数据和新闻，";
        String specificAnalysis = "";
        String conclusion = "给出专业的价格预测分析，包含具体的价格范围预测和时间节点。控制在200字以内。";
        
        // 根据materialType数字编码映射到具体类型
        String typeDesc = getMaterialTypeDescription(materialType);
        
        switch (typeDesc) {
            case "矿石":
                specificAnalysis = "重点分析铁矿石供需平衡、钢厂库存变化、海运费波动、主要矿山产量、" +
                    "中国钢铁行业景气度、房地产和基建投资对需求的影响、";
                break;
            case "煤炭":
                specificAnalysis = "重点分析煤炭供需关系、电厂库存水平、进口政策变化、安全检查影响、" +
                    "新能源替代趋势、冬季供暖需求、煤矿产能释放情况、";
                break;
            case "合金":
                specificAnalysis = "重点分析合金市场供需状况、上游原材料价格波动、下游钢厂需求变化、" +
                    "合金厂商库存情况、进出口贸易政策、环保限产影响、";
                break;
            case "废钢":
                specificAnalysis = "重点分析废钢回收量变化、钢厂废钢比例调整、新增产能对废钢需求、" +
                    "废钢质量标准变化、运输成本波动、替代品竞争情况、";
                break;
            default:
                specificAnalysis = "分析当前的供需情况、政策影响、宏观经济因素、产业链上下游变化、";
        }
        
        return String.format(baseQuestion + specificAnalysis + conclusion, materialName);
    }

    /**
     * 根据materialType编码获取类型描述
     *
     * @param materialType 物料类型编码
     * @return 类型描述
     */
    private String getMaterialTypeDescription(String materialType) {
        if (materialType == null || materialType.isEmpty()) {
            return "通用";
        }
        
        switch (materialType) {
            case "1":
                return "矿石";
            case "2":
                return "煤炭";
            case "3":
                return "合金";
            case "4":
                return "废钢";
            default:
                return "通用";
        }
    }

    @Override
    public List<ProcurementPriceAndPurchaseResultVo> getPurchasePriceAndStoreFromNewTables(ProcurementPriceAndStoreQueryDto query) {
        try {
            // 时间维度，默认为近一年
            int dimensionType = Optional.ofNullable(query.getDimensionType()).orElse(3);

            // 获取查询的物资列表
            List<PriceAndStoreDetailDto> itemList = query.getItemList();
            if (itemList == null || itemList.isEmpty()) {
                logger.warn("查询物资列表为空");
                return new ArrayList<>();
            }

            // 提取所有物资名称并分类
            Set<String> allItemNames = new HashSet<>();
            Set<String> priceNeededItems = new HashSet<>();
            Set<String> amountNeededItems = new HashSet<>();

            // 用于跟踪每个物资需要的曲线类型（支持同一物资多种曲线类型）
            Map<String, Set<Integer>> itemCurveTypesMap = new HashMap<>();

            for (PriceAndStoreDetailDto item : itemList) {
                String itemName = item.getItemName();
                Integer curveType = item.getCurveType();

                if (itemName == null || itemName.trim().isEmpty()) {
                    logger.warn("物资名称为空，跳过处理");
                    continue;
                }

                String trimmedName = itemName.trim();
                allItemNames.add(trimmedName);

                // 为每个物资维护一个曲线类型集合
                itemCurveTypesMap.computeIfAbsent(trimmedName, k -> new HashSet<>()).add(curveType);

                // 根据曲线类型决定需要查询哪些数据
                boolean needPriceData = (curveType == null || curveType == 1); // 未指定或价格曲线
                boolean needAmountData = (curveType == null || curveType == 2); // 未指定或采购量曲线

                if (needPriceData) {
                    priceNeededItems.add(trimmedName);
                }
                if (needAmountData) {
                    amountNeededItems.add(trimmedName);
                }
            }

            if (allItemNames.isEmpty()) {
                logger.warn("没有有效的查询物资");
                return new ArrayList<>();
            }

            logger.info("从新数据表批量查询物资：{}, 需要价格数据的物资：{}, 需要采购量数据的物资：{}, 时间维度：{}",
                       allItemNames.size(), priceNeededItems.size(), amountNeededItems.size(), dimensionType);

            // 批量查询所有价格数据（从新表）
            Map<String, Map<String, Map<String, BigDecimal>>> allPriceData = new HashMap<>();
            if (!priceNeededItems.isEmpty()) {
                queryPriceDataFromNewTable(priceNeededItems, dimensionType, allPriceData);
            }

            // 批量查询所有采购量数据（从新表）
            Map<String, Map<String, Map<String, BigDecimal>>> allAmountData = new HashMap<>();
            if (!amountNeededItems.isEmpty()) {
                queryAmountDataFromNewTable(amountNeededItems, dimensionType, allAmountData);
            }

            // 组装结果数据
            List<ProcurementPriceAndPurchaseResultVo> resultList = new ArrayList<>();

            for (String itemName : allItemNames) {
                Set<Integer> curveTypes = itemCurveTypesMap.get(itemName);

                // 判断该物资需要哪些类型的数据
                boolean needPriceData = curveTypes.contains(null) || curveTypes.contains(1);
                boolean needAmountData = curveTypes.contains(null) || curveTypes.contains(2);

                ProcurementPriceAndPurchaseResultVo materialData = new ProcurementPriceAndPurchaseResultVo();
                materialData.setItemName(itemName);

                // 设置价格列表
                List<ProcurementPurchasePriceVo> priceVoList = new ArrayList<>();
                if (needPriceData && allPriceData.containsKey(itemName)) {
                    Map<String, Map<String, BigDecimal>> materialPriceTypeByDateMap = allPriceData.get(itemName);

                    for (Map.Entry<String, Map<String, BigDecimal>> priceTypeEntry : materialPriceTypeByDateMap.entrySet()) {
                        String priceTypeName = priceTypeEntry.getKey();
                        Map<String, BigDecimal> priceByDateMap = priceTypeEntry.getValue();

                        if (priceByDateMap != null && !priceByDateMap.isEmpty()) {
                            List<PriceDetailVo> priceDetailList = new ArrayList<>();
                            for (Map.Entry<String, BigDecimal> entry : priceByDateMap.entrySet()) {
                                PriceDetailVo priceDetail = new PriceDetailVo();
                                priceDetail.setPrice(entry.getValue());
                                priceDetail.setRecordDate(entry.getKey());
                                priceDetailList.add(priceDetail);
                            }

                            // 按日期排序
                            priceDetailList.sort((a, b) -> a.getRecordDate().compareTo(b.getRecordDate()));

                            ProcurementPurchasePriceVo priceVo = new ProcurementPurchasePriceVo();
                            priceVo.setPriceName(priceTypeName);
                            priceVo.setPriceList(priceDetailList);
                            priceVoList.add(priceVo);
                        }
                    }
                }
                materialData.setProcurementPriceVoList(priceVoList);

                // 设置采购量列表
                List<ProcurementPurchaseAmountVo> purchaseAmountList = new ArrayList<>();
                if (needAmountData && allAmountData.containsKey(itemName)) {
                    Map<String, Map<String, BigDecimal>> materialAmountTypeByDateMap = allAmountData.get(itemName);

                    for (Map.Entry<String, Map<String, BigDecimal>> amountTypeEntry : materialAmountTypeByDateMap.entrySet()) {
                        String amountTypeName = amountTypeEntry.getKey();
                        Map<String, BigDecimal> amountByDateMap = amountTypeEntry.getValue();

                        if (amountByDateMap != null && !amountByDateMap.isEmpty()) {
                            List<AmountDetailVo> amountDetailList = new ArrayList<>();
                            for (Map.Entry<String, BigDecimal> entry : amountByDateMap.entrySet()) {
                                AmountDetailVo amountDetail = new AmountDetailVo();
                                amountDetail.setAmount(entry.getValue());
                                amountDetail.setRecordDate(entry.getKey());
                                amountDetailList.add(amountDetail);
                            }

                            // 按日期排序
                            amountDetailList.sort((a, b) -> a.getRecordDate().compareTo(b.getRecordDate()));

                            ProcurementPurchaseAmountVo purchaseAmountVo = new ProcurementPurchaseAmountVo();
                            purchaseAmountVo.setAmountName(amountTypeName);
                            purchaseAmountVo.setAmountList(amountDetailList);
                            purchaseAmountList.add(purchaseAmountVo);
                        }
                    }
                }
                materialData.setProcurementPurchaseAmountVoList(purchaseAmountList);

                // 只有当有价格数据或采购量数据时才添加到结果中
                if (!priceVoList.isEmpty() || !purchaseAmountList.isEmpty()) {
                    resultList.add(materialData);
                }
            }

            return resultList;

        } catch (Exception e) {
            logger.error("从新数据表查询物资价格和入库数量出错", e);
            return new ArrayList<>();
        }
    }

    /**
     * 从新数据表查询价格数据
     */
    private void queryPriceDataFromNewTable(Set<String> itemNames,
                                           int dimensionType,
                                           Map<String, Map<String, Map<String, BigDecimal>>> allPriceData) {
        try {
            if (itemNames.isEmpty()) {
                return;
            }

            logger.debug("从新价格表查询物资：{}, 时间维度：{}", itemNames, dimensionType);
            List<ProcurementMaterialPrice> priceDataList = procurementMaterialPriceMapper.selectPriceDataByItemNames(
                new ArrayList<>(itemNames), dimensionType);

            if (priceDataList != null && !priceDataList.isEmpty()) {
                logger.debug("从新价格表查询到 {} 条数据", priceDataList.size());

                for (ProcurementMaterialPrice priceData : priceDataList) {
                    String itemName = priceData.getItemName();
                    Integer priceType = priceData.getPriceType();
                    Date maintenanceDate = priceData.getMaintenanceDate();
                    BigDecimal price = priceData.getPrice();

                    if (itemName == null || priceType == null || maintenanceDate == null || price == null) {
                        continue;
                    }

                    String priceTypeName = getPriceTypeNameByCode(priceType);
                    String dateStr = new SimpleDateFormat("yyyyMMdd").format(maintenanceDate);

                    allPriceData.computeIfAbsent(itemName, k -> new HashMap<>())
                                .computeIfAbsent(priceTypeName, k -> new HashMap<>())
                                .put(dateStr, price);
                }
            } else {
                logger.debug("从新价格表未查询到数据");
            }

            // 2. 补充废钢价格数据（参考原方法逻辑）
            List<String> itemNameList = new ArrayList<>(itemNames);
            List<PurchaseScrapSteel> scrapSteelList = purchaseScrapSteelMapper.selectPurchaseScrapSteelListByItemNames(itemNameList);

            // 按物资名称和价格类型分组废钢价格数据
            Map<String, Map<String, Map<String, BigDecimal>>> scrapSteelPriceGroupMap = new HashMap<>();

            for (PurchaseScrapSteel scrapSteel : scrapSteelList) {
                String materialName = scrapSteel.getItemName();
                if (materialName == null || materialName.trim().isEmpty()) {
                    continue;
                }

                String itemName = materialName.trim();
                BigDecimal price = scrapSteel.getPrice();
                String recordDate = "";
                if (scrapSteel.getMaintenanceDate() != null) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    recordDate = sdf.format(scrapSteel.getMaintenanceDate());
                }

                String priceTypeName = "";
                String transportDesc = "";
                String companyDesc = "";

                // 构建价格类型名称
                if (scrapSteel.getTransportMode() != null && !scrapSteel.getTransportMode().trim().isEmpty()) {
                    String trimmedTransportMode = scrapSteel.getTransportMode().trim();
                    // 处理中文运输方式
                    if (trimmedTransportMode.contains("车") || "1".equals(trimmedTransportMode)) {
                        transportDesc = "车运";
                    } else if (trimmedTransportMode.contains("船") || "2".equals(trimmedTransportMode)) {
                        transportDesc = "船运";
                    } else {
                        transportDesc = trimmedTransportMode; // 保持原值
                    }
                    logger.debug("废钢运输方式原始值: '{}', 处理后: '{}'", scrapSteel.getTransportMode(), transportDesc);
                }

                if (scrapSteel.getCompany() != null && !scrapSteel.getCompany().trim().isEmpty()) {
                    companyDesc = scrapSteel.getCompany().trim();
                    logger.debug("废钢公司: '{}'", companyDesc);
                }

                if (!transportDesc.isEmpty() && !companyDesc.isEmpty()) {
                    priceTypeName = companyDesc + "废钢收购价" + "(" + transportDesc + ")";
                } else if (!companyDesc.isEmpty()) {
                    priceTypeName = companyDesc + "废钢收购价";
                } else if (!transportDesc.isEmpty()) {
                    priceTypeName = transportDesc + "废钢收购价";
                } else {
                    priceTypeName = "废钢收购价";
                }

                if (price != null && !recordDate.isEmpty() && !priceTypeName.isEmpty()) {
                    scrapSteelPriceGroupMap.computeIfAbsent(itemName, k -> new HashMap<>())
                            .computeIfAbsent(priceTypeName, k -> new HashMap<>())
                            .put(recordDate, price);
                }
            }

            // 将废钢价格数据直接添加到结果中（不进行数据补充处理）
            for (String itemName : itemNames) {
                if (scrapSteelPriceGroupMap.containsKey(itemName)) {
                    Map<String, Map<String, BigDecimal>> itemPriceMap = scrapSteelPriceGroupMap.get(itemName);
                    for (Map.Entry<String, Map<String, BigDecimal>> priceTypeEntry : itemPriceMap.entrySet()) {
                        String priceTypeName = priceTypeEntry.getKey();
                        Map<String, BigDecimal> priceByDateMap = priceTypeEntry.getValue();
                        allPriceData.computeIfAbsent(itemName, k -> new HashMap<>())
                                   .put(priceTypeName, priceByDateMap);
                    }
                }
            }

            // 批量处理废钢价格数据的补充 - 已注释
            /*
            for (String itemName : itemNames) {
                if (scrapSteelPriceGroupMap.containsKey(itemName)) {
                    this.fillScrapSteelPriceDataForItem(scrapSteelPriceGroupMap.get(itemName), dimensionType, itemName, allPriceData);
                }
            }
            */

        } catch (Exception e) {
            logger.error("从新价格表查询数据失败", e);
        }
    }

    /**
     * 从新数据表查询采购量数据
     */
    private void queryAmountDataFromNewTable(Set<String> itemNames,
                                           int dimensionType,
                                           Map<String, Map<String, Map<String, BigDecimal>>> allAmountData) {
        try {
            if (itemNames.isEmpty()) {
                return;
            }

            logger.debug("从新采购量表查询物资：{}, 时间维度：{}", itemNames, dimensionType);
            List<ProcurementMaterialAmount> amountDataList = procurementMaterialAmountMapper.selectAmountDataByItemNames(
                new ArrayList<>(itemNames), dimensionType);

            if (amountDataList != null && !amountDataList.isEmpty()) {
                logger.debug("从新采购量表查询到 {} 条数据", amountDataList.size());

                for (ProcurementMaterialAmount amountData : amountDataList) {
                    String itemName = amountData.getItemName();
                    Integer category = amountData.getCategory();
                    Integer transportMode = amountData.getTransportMode();
                    Date maintenanceDate = amountData.getMaintenanceDate();
                    BigDecimal amount = amountData.getAmount();

                    if (itemName == null || category == null || maintenanceDate == null || amount == null) {
                        continue;
                    }

                    String amountTypeName = getAmountTypeNameByCategory(category, transportMode);
                    String dateStr = new SimpleDateFormat("yyyyMMdd").format(maintenanceDate);

                    allAmountData.computeIfAbsent(itemName, k -> new HashMap<>())
                                .computeIfAbsent(amountTypeName, k -> new HashMap<>())
                                .put(dateStr, amount);
                }
            } else {
                logger.debug("从新采购量表未查询到数据");
            }
        } catch (Exception e) {
            logger.error("从新采购量表查询数据失败", e);
        }
    }

    /**
     * 根据价格类型编码获取价格类型名称
     */
    private String getPriceTypeNameByCode(Integer priceTypeCode) {
        if (priceTypeCode == null) {
            return "其他价格";
        }
        switch (priceTypeCode) {
            case 1:
                return "现货价";
            case 2:
                return "市场采购到厂价";
            case 3:
                return "兴澄废钢收购价(车运)";
            case 4:
                return "兴澄废钢收购价(船运)";
            case 5:
                return "沙钢废钢收购价(车运)";
            case 6:
                return "沙钢废钢收购价(船运)";
            case 7:
            default:
                return "其他价格";
        }
    }

    /**
     * 根据类别编码获取采购量类型名称
     */
    private String getCategoryNameByCode(Integer categoryCode) {
        if (categoryCode == null) {
            return "其他采购量";
        }
        switch (categoryCode) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
                return "采购量"; // 矿石、煤炭、合金、废钢统一显示为"采购量"
            default:
                return "采购量";
        }
    }

    /**
     * 根据类别和运输方式获取采购量类型名称
     */
    private String getAmountTypeNameByCategory(Integer category, Integer transportMode) {
        if (category == null) {
            return "采购量";
        }

        // 废钢需要区分运输方式
        if (category == 4) {
            if (transportMode != null) {
                switch (transportMode) {
                    case 1:
                        return "车运采购量";
                    case 2:
                        return "船运采购量";
                    case 3:
                    default:
                        return "采购量";
                }
            } else {
                return "采购量";
            }
        } else {
            // 矿石、煤炭、合金不区分运输方式
            return "采购量";
        }
    }

    /**
     * 将日期格式化为字符串（yyyyMMdd格式）
     */
    private String formatDateToString(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    /**
     * 获取物资名称列表（使用新数据表）
     */
    @Override
    public List<MaterialNameVo> getMaterialNameListFromNewTables(MaterialNameQueryDto query) {
        try {
            // 用于存储所有物资名称的Set，自动去重
            Set<String> materialNames = new HashSet<>();

            List<Integer> categories = query != null ? query.getCategories() : null;
            Integer curveType = query != null ? query.getCurveType() : null;
            Integer dimensionType = query != null ? query.getDimensionType() : null;

            // 如果没有指定物资类型，默认查询所有类型
            if (categories == null || categories.isEmpty()) {
                categories = Arrays.asList(1, 2, 3, 4);
            }

            // 如果没有指定时间维度，默认为近一年
            if (dimensionType == null) {
                dimensionType = 3;
            }

            // 存储有价格数据的物资名称
            Set<String> materialsWithPrice = new HashSet<>();
            // 存储有采购量数据的物资名称
            Set<String> materialsWithPurchaseAmount = new HashSet<>();

            // 1. 从新价格表获取有价格数据的物资名称
            if (curveType == null || curveType == 1) {
                logger.debug("从新价格表查询物资名称，物资类型：{}, 时间维度：{}", categories, dimensionType);
                
                // 查询指定类型的价格数据物资名称
                List<ProcurementMaterialPrice> priceDataList = procurementMaterialPriceMapper.selectDistinctItemNamesByCategories(categories, dimensionType);
                
                for (ProcurementMaterialPrice priceData : priceDataList) {
                    if (priceData.getItemName() != null && !priceData.getItemName().trim().isEmpty()) {
                        String materialName = priceData.getItemName().trim();
                        materialNames.add(materialName);
                        materialsWithPrice.add(materialName);
                    }
                }
            }

            // 2. 从新采购量表获取有采购量数据的物资名称
            if (curveType == null || curveType == 2) {
                logger.debug("从新采购量表查询物资名称，物资类型：{}, 时间维度：{}", categories, dimensionType);
                
                // 查询指定类型的采购量数据物资名称
                List<ProcurementMaterialAmount> amountDataList = procurementMaterialAmountMapper.selectDistinctItemNamesByCategories(categories, dimensionType);
                
                for (ProcurementMaterialAmount amountData : amountDataList) {
                    if (amountData.getItemName() != null && !amountData.getItemName().trim().isEmpty()) {
                        String materialName = amountData.getItemName().trim();
                        materialNames.add(materialName);
                        materialsWithPurchaseAmount.add(materialName);
                    }
                }
            }

            // 根据曲线类型筛选物资
            Set<String> filteredMaterialNames = new HashSet<>();
            if (curveType != null) {
                if (curveType == 1) {
                    // 价格曲线：只返回有价格数据的物资
                    filteredMaterialNames = materialsWithPrice;
                } else if (curveType == 2) {
                    // 采购量曲线：只返回有采购量数据的物资
                    filteredMaterialNames = materialsWithPurchaseAmount;
                } else {
                    // 其他曲线类型：返回所有物资
                    filteredMaterialNames = materialNames;
                }
            } else {
                // 未指定曲线类型：返回所有物资
                filteredMaterialNames = materialNames;
            }

            // 3. 转换为带序号的列表
            List<String> sortedMaterialNames = new ArrayList<>(filteredMaterialNames);
            // 按物资名称排序
            Collections.sort(sortedMaterialNames);

            List<MaterialNameVo> resultList = new ArrayList<>();
            for (int i = 0; i < sortedMaterialNames.size(); i++) {
                MaterialNameVo materialNameVo = new MaterialNameVo();
                materialNameVo.setSerialNumber(i + 1); // 序号从1开始
                materialNameVo.setItemName(sortedMaterialNames.get(i));
                resultList.add(materialNameVo);
            }

            logger.debug("从新数据表查询到 {} 个物资名称，物资类型：{}", resultList.size(), categories);
            return resultList;

        } catch (Exception e) {
            logger.error("从新数据表查询物资名称列表出错", e);
            return new ArrayList<>();
        }
    }



    @Override
    public List<MaterialNameVo> getMaterialNameList(MaterialNameQueryDto query) {
        try {
            // 用于存储所有物资名称的Set，自动去重
            Set<String> materialNames = new HashSet<>();

            List<Integer> categories = query != null ? query.getCategories() : null;
            Integer curveType = query != null ? query.getCurveType() : null;
            Integer dimensionType = query != null ? query.getDimensionType() : null;

            // 如果没有指定物资类型，默认查询所有类型
            if (categories == null || categories.isEmpty()) {
                categories = Arrays.asList(1, 2, 3, 4);
            }

            // 如果没有指定时间维度，默认为近一年
            if (dimensionType == null) {
                dimensionType = 3;
            }

            // 存储有价格数据的物资名称
            Set<String> materialsWithPrice = new HashSet<>();
            // 存储有采购量数据的物资名称
            Set<String> materialsWithPurchaseAmount = new HashSet<>();

            // 处理矿石、煤炭、合金类型（categories 1, 2, 3）
            if (categories.contains(1) || categories.contains(2) || categories.contains(3) || categories.contains(99)) {
                // 1. 获取矿石、煤炭和合金物资名称
                PurchaseDailyFormula formulaQuery = new PurchaseDailyFormula();
                formulaQuery.setDimensionType(dimensionType); // 设置时间维度
                List<PurchaseDailyFormula> formulaList = purchaseDailyFormulaMapper.selectPurchaseDailyFormulaList(formulaQuery);

                for (PurchaseDailyFormula formula : formulaList) {
                    if (formula.getSpotName() != null && !formula.getSpotName().trim().isEmpty()) {
                        String materialName = formula.getSpotName().trim();

                        // 根据categories筛选
                        boolean shouldInclude = false;
                        if (categories.contains(99)) {
                            shouldInclude = true;
                        } else {
                            String purchaseType = formula.getPurchaseType();
                            if (purchaseType != null) {
                                if (purchaseType.equals("ore") && categories.contains(1)) {
                                    shouldInclude = true;
                                } else if (purchaseType.equals("coal") && categories.contains(2)) {
                                    shouldInclude = true;
                                } else if (purchaseType.equals("alloy") && categories.contains(3)) {
                                    shouldInclude = true;
                                }
                            }
                        }

                        if (shouldInclude) {
                            materialNames.add(materialName);

                            // 检查是否有价格数据
                            if (formula.getSpotPrice() != null || formula.getSpotFormulaResult() != null) {
                                materialsWithPrice.add(materialName);
                            }
                        }
                    }
                }

                // 2. 获取六大类物资入库数量
                PurchaseInbound inboundQuery = new PurchaseInbound();
                inboundQuery.setDimensionType(dimensionType); // 设置时间维度
                List<PurchaseInbound> inboundList = purchaseInboundMapper.selectPurchaseInboundList(inboundQuery);

                for (PurchaseInbound inbound : inboundList) {
                    if (inbound.getItemName() != null && !inbound.getItemName().trim().isEmpty()) {
                        String materialName = inbound.getItemName().trim();

                        // 根据categories筛选
                        boolean shouldInclude = false;
                        if (categories.contains(99)) {
                            shouldInclude = true;
                        } else {
                            Integer inboundCategory = inbound.getCategory();
                            if (inboundCategory != null && categories.contains(inboundCategory)) {
                                shouldInclude = true;
                            }
                        }

                        if (shouldInclude) {
                            materialNames.add(materialName);

                            // 检查是否有采购量数据
                            if (inbound.getInNum() != null && inbound.getInNum().compareTo(BigDecimal.ZERO) > 0) {
                                materialsWithPurchaseAmount.add(materialName);
                            }
                        }
                    }
                }
            }

            // 处理废钢类型（category 4）
            if (categories.contains(4) || categories.contains(99)) {
                // 3. 获取废钢价格数据（废钢价格数据表中没有时间维度筛选，保持原有逻辑）
                PurchaseScrapSteel scrapSteelQuery = new PurchaseScrapSteel();
                List<PurchaseScrapSteel> scrapSteelList = purchaseScrapSteelMapper.selectPurchaseScrapSteelList(scrapSteelQuery);

                for (PurchaseScrapSteel scrapSteel : scrapSteelList) {
                    if (scrapSteel.getItemName() != null && !scrapSteel.getItemName().trim().isEmpty()) {
                        String materialName = scrapSteel.getItemName().trim();
                        materialNames.add(materialName);

                        // 检查是否有价格数据
                        if (scrapSteel.getPrice() != null) {
                            materialsWithPrice.add(materialName);
                        }
                    }
                }

                // 4. 获取废钢入库数量 - 车运
                PurchaseInbound scrapSteelInboundTruckQuery = new PurchaseInbound();
                scrapSteelInboundTruckQuery.setTransportMode(1); // 1-车运
                scrapSteelInboundTruckQuery.setDimensionType(dimensionType); // 设置时间维度
                List<PurchaseInbound> scrapSteelTruckList = purchaseInboundMapper.selectScrapSteelStorage(scrapSteelInboundTruckQuery);

                for (PurchaseInbound scrapSteelInbound : scrapSteelTruckList) {
                    if (scrapSteelInbound.getItemName() != null && !scrapSteelInbound.getItemName().trim().isEmpty()) {
                        String materialName = scrapSteelInbound.getItemName().trim();
                        materialNames.add(materialName);

                        // 检查是否有采购量数据
                        if (scrapSteelInbound.getInNum() != null && scrapSteelInbound.getInNum().compareTo(BigDecimal.ZERO) > 0) {
                            materialsWithPurchaseAmount.add(materialName);
                        }
                    }
                }

                // 5. 获取废钢入库数量 - 船运
                PurchaseInbound scrapSteelInboundShipQuery = new PurchaseInbound();
                scrapSteelInboundShipQuery.setTransportMode(2); // 2-船运
                scrapSteelInboundShipQuery.setDimensionType(dimensionType); // 设置时间维度
                List<PurchaseInbound> scrapSteelShipList = purchaseInboundMapper.selectScrapSteelStorage(scrapSteelInboundShipQuery);

                for (PurchaseInbound scrapSteelInbound : scrapSteelShipList) {
                    if (scrapSteelInbound.getItemName() != null && !scrapSteelInbound.getItemName().trim().isEmpty()) {
                        String materialName = scrapSteelInbound.getItemName().trim();
                        materialNames.add(materialName);

                        // 检查是否有采购量数据
                        if (scrapSteelInbound.getInNum() != null && scrapSteelInbound.getInNum().compareTo(BigDecimal.ZERO) > 0) {
                            materialsWithPurchaseAmount.add(materialName);
                        }
                    }
                }
            }

            // 根据曲线类型筛选物资
            Set<String> filteredMaterialNames = new HashSet<>();
            if (curveType != null) {
                if (curveType == 1) {
                    // 价格曲线：只返回有价格数据的物资
                    filteredMaterialNames = materialsWithPrice;
                } else if (curveType == 2) {
                    // 采购量曲线：只返回有采购量数据的物资
                    filteredMaterialNames = materialsWithPurchaseAmount;
                } else {
                    // 其他曲线类型：返回所有物资
                    filteredMaterialNames = materialNames;
                }
            } else {
                // 未指定曲线类型：返回所有物资
                filteredMaterialNames = materialNames;
            }

            // 6. 转换为带序号的列表
            List<String> sortedMaterialNames = new ArrayList<>(filteredMaterialNames);
            // 按物资名称排序
            Collections.sort(sortedMaterialNames);

            List<MaterialNameVo> resultList = new ArrayList<>();
            for (int i = 0; i < sortedMaterialNames.size(); i++) {
                MaterialNameVo materialNameVo = new MaterialNameVo();
                materialNameVo.setSerialNumber(i + 1); // 序号从1开始
                materialNameVo.setItemName(sortedMaterialNames.get(i));
                resultList.add(materialNameVo);
            }

            return resultList;

        } catch (Exception e) {
            logger.error("查询物资名称列表出错", e);
            return new ArrayList<>();
        }
    }

    /**
     * 自动补充废钢价格数据
     * 废钢料型价格只有个别日期有，如果不调价，则在新日期之前，保持前一日期价格
     */
    private void fillScrapSteelPriceData(Map<String, Map<String, Map<String, BigDecimal>>> scrapSteelPriceGroupMap,
                                        int dimensionType,
                                        Map<String, Map<String, Map<String, BigDecimal>>> materialPriceTypeByDateMap) {

        try {
            // 根据维度类型确定开始日期范围
            Calendar dimensionStartCalendar = Calendar.getInstance();
            switch (dimensionType) {
                case 1: // 近三个月
                    dimensionStartCalendar.add(Calendar.DAY_OF_YEAR, -90);
                    break;
                case 2: // 近半年
                    dimensionStartCalendar.add(Calendar.DAY_OF_YEAR, -180);
                    break;
                case 3: // 近一年
                default:
                    dimensionStartCalendar.add(Calendar.DAY_OF_YEAR, -365);
                    break;
            }
            String dimensionStartDate = new SimpleDateFormat("yyyyMMdd").format(dimensionStartCalendar.getTime());

            // 遍历每个物资
            for (Map.Entry<String, Map<String, Map<String, BigDecimal>>> materialEntry : scrapSteelPriceGroupMap.entrySet()) {
                String materialName = materialEntry.getKey();
                Map<String, Map<String, BigDecimal>> priceTypeMap = materialEntry.getValue();

                // 遍历每个价格类型
                for (Map.Entry<String, Map<String, BigDecimal>> priceTypeEntry : priceTypeMap.entrySet()) {
                    String priceTypeName = priceTypeEntry.getKey();
                    Map<String, BigDecimal> originalPriceByDateMap = priceTypeEntry.getValue();

                    if (originalPriceByDateMap.isEmpty()) {
                        continue;
                    }

                    // 创建有序的日期价格映射
                    TreeMap<String, BigDecimal> sortedPriceMap = new TreeMap<>(originalPriceByDateMap);

                    // 获取第一个和最后一个有数据的日期
                    String firstDataDate = sortedPriceMap.firstKey();
                    String lastDataDate = sortedPriceMap.lastKey();

                    // 使用第一个有数据的日期和维度范围的开始日期中较晚的那个作为实际开始日期
                    String actualStartDate = firstDataDate;
                    if (firstDataDate.compareTo(dimensionStartDate) < 0) {
                        actualStartDate = dimensionStartDate;
                    }

                    // 结束日期使用最后一个有数据的日期
                    String actualEndDate = lastDataDate;

                    // 补充价格数据
                    Map<String, BigDecimal> filledPriceMap = new HashMap<>();
                    BigDecimal lastPrice = null;

                    // 找到实际开始日期对应的价格
                    // 如果实际开始日期没有价格，找到该日期之前最近的价格
                    for (Map.Entry<String, BigDecimal> priceEntry : sortedPriceMap.entrySet()) {
                        if (priceEntry.getKey().compareTo(actualStartDate) <= 0) {
                            lastPrice = priceEntry.getValue();
                        } else {
                            break;
                        }
                    }

                    // 生成从实际开始日期到最后有数据日期的所有日期
                    Calendar fillCalendar = Calendar.getInstance();
                    fillCalendar.setTime(new SimpleDateFormat("yyyyMMdd").parse(actualStartDate));
                    Calendar endCalendar = Calendar.getInstance();
                    endCalendar.setTime(new SimpleDateFormat("yyyyMMdd").parse(actualEndDate));

                    while (!fillCalendar.after(endCalendar)) {
                        String currentDate = new SimpleDateFormat("yyyyMMdd").format(fillCalendar.getTime());

                        if (sortedPriceMap.containsKey(currentDate)) {
                            // 如果当前日期有价格数据，更新lastPrice
                            lastPrice = sortedPriceMap.get(currentDate);
                            filledPriceMap.put(currentDate, lastPrice);
                        } else if (lastPrice != null) {
                            // 如果当前日期没有价格数据，但有历史价格，则使用历史价格
                            filledPriceMap.put(currentDate, lastPrice);
                        }

                        fillCalendar.add(Calendar.DAY_OF_YEAR, 1);
                    }

                    // 将补充后的数据添加到最终结果中
                    if (!filledPriceMap.isEmpty()) {
                        materialPriceTypeByDateMap.computeIfAbsent(materialName, k -> new HashMap<>())
                                .put(priceTypeName, filledPriceMap);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("自动补充废钢价格数据出错", e);
            // 如果补充数据出错，则使用原始数据
            for (Map.Entry<String, Map<String, Map<String, BigDecimal>>> materialEntry : scrapSteelPriceGroupMap.entrySet()) {
                String materialName = materialEntry.getKey();
                Map<String, Map<String, BigDecimal>> priceTypeMap = materialEntry.getValue();

                for (Map.Entry<String, Map<String, BigDecimal>> priceTypeEntry : priceTypeMap.entrySet()) {
                    String priceTypeName = priceTypeEntry.getKey();
                    Map<String, BigDecimal> priceByDateMap = priceTypeEntry.getValue();

                    materialPriceTypeByDateMap.computeIfAbsent(materialName, k -> new HashMap<>())
                            .put(priceTypeName, priceByDateMap);
                }
            }
        }
    }
}
