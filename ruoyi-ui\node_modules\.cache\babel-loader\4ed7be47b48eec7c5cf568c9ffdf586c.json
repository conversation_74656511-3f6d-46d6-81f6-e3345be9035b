{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPlan\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPlan\\index.vue", "mtime": 1756456493847}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_chartMethods", "_interopRequireDefault", "_chartMethodsExtended", "_screenfull", "_purchaseDashboard", "_similar", "_purdchaseFactoryStock", "name", "mixins", "chartMethods", "extendedChartMethods", "data", "_ref", "dashboardData", "purchaseStats", "topSuppliersOptions", "selectedTop<PERSON>uppliers<PERSON><PERSON>er", "selectedOrderType", "chartInstances", "originalTopSuppliersData", "selectedOrderFactoryDep", "selectedOrderMaterialType", "orderToReceiptData", "selectedCokingCoalFactoryDep", "selectedCokingCoalMaterialType", "selectedMaterialFactoryDep", "selectedMaterialMaterialType", "realTimeInventoryData", "cokingCoalInventoryData", "selectedCokingCoalType", "selectedMaterialCategory", "selectedMaterialItem", "materialItemOptions", "materialStatisticsData", "selectedCodeType", "selectedItemType", "highFrequencyMaterialData", "supplierRiskData", "pricePredictions", "predictionLoading", "materialNameOptions", "selectedMaterial", "_defineProperty2", "default", "computed", "isFullscreen", "$store", "state", "app", "isFullscreenMode", "groupedSimilarMaterials", "_this", "grouped", "similarMaterialsData", "for<PERSON>ach", "item", "groupKey", "concat", "itemName", "category", "priceType", "displayKey", "getCategoryName", "getPriceTypeName", "items", "push", "Object", "keys", "key", "sort", "a", "b", "rank", "mounted", "_this2", "checkEchartsAvailability", "fetchDashboardData", "fetchRealTimeInventoryData", "fetchCokingCoalInventoryData", "updateMaterialItemOptions", "then", "fetchMaterialStatisticsData", "fetchHighFrequencyMaterialData", "fetchSupplierRiskData", "fetchPurchaseAmountMaterialList", "fetchMarketPriceMaterialList", "fetchFactoryDepOptions", "fetchOrderToReceiptData", "fetchOverdueData", "setupResizeObserver", "initFullscreenListener", "window", "addEventListener", "resize<PERSON>ll<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "clearAllIntervals", "removeFullscreenListener", "removeEventListener", "dispatch", "methods", "screenfull", "isEnabled", "on", "handleFullscreenChange", "off", "_this3", "$nextTick", "setTimeout", "getDashboardData", "dimensionType", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "w", "_context", "n", "showData", "v", "getItemTypeList", "itemType", "_callee2", "_context2", "showItemTypeList", "getMaterialList", "params", "_callee3", "_context3", "showMaterialList", "getSupplierList", "_callee4", "_context4", "showSuppList", "getYearlyAmount", "_callee5", "_context5", "showYearlyAmount", "getRealTimeAmount", "_callee6", "_context6", "showRealTimeAmount", "getCokingCoalAmount", "_callee7", "_context7", "showCokingCoalAmount", "getKeyIndicators", "_callee8", "_context8", "showKeyIndicators", "getHighFrequencyMaterialList", "_callee9", "_context9", "showHighFrequencyMaterialList", "getPurchaseSuppRisk", "_callee0", "_context0", "showPurchaseSuppRisk", "getTimeFlagByDimensionType", "console", "error", "document", "querySelectorAll", "el", "innerHTML", "_arguments", "arguments", "_this4", "_callee1", "dimensionTypeParam", "_yield$Promise$all", "_yield$Promise$all2", "dashboardResponse", "keyIndicatorsResponse", "_t", "_context1", "length", "undefined", "currentDimensionType", "p", "Promise", "all", "_slicedToArray2", "log", "showErrorMessage", "initAllCharts", "message", "chart", "handleTimeFilterChange", "filterId", "activeFilter", "values", "instance", "intervalId", "clearInterval", "resize", "err", "timestamp", "Date", "toLocaleTimeString", "initFactoryStockChart", "initCokingCoalLineChart", "initMaterialStatisticsChart", "initMaterialCloud", "initTopSuppliersChart", "populateItemDropdown", "selectElementId", "dataPropertyName", "_this5", "_callee10", "response", "_t2", "_context10", "Array", "isArray", "handleTopSuppliersFilterChange", "_this6", "_callee11", "_context11", "refreshTopSuppliersChart", "handleOrderTypeChange", "_this7", "_callee12", "_context12", "_this8", "_callee13", "myChart", "newSupplierData", "_response", "_newSupplierData", "_t3", "_t4", "_context13", "topSuppliersChart", "showLoading", "orderType", "renderAndPaginateTopSuppliers", "hideLoading", "f", "itemId", "getElementById", "resizeObserver", "ResizeObserver", "entries", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "done", "entry", "value", "charts", "target", "id", "getInstanceByDom", "e", "card", "observe", "toggleFullscreen", "toggle", "$message", "type", "handleOrderFactoryDepChange", "_this9", "_callee14", "_context14", "handleOrderMaterialTypeChange", "_this0", "_callee15", "_context15", "_this1", "_callee16", "hasCountType", "dataToProcess", "countTypes", "filteredData", "sortedData", "_t5", "_context16", "showPurchasePlanList", "slice", "some", "countType", "_toConsumableArray2", "Set", "map", "filter", "warn", "midDays", "periodA", "parseInt", "period", "periodB", "avgDays", "parseFloat", "avgDay", "averageDays", "days", "dayCount", "midDay", "medianDays", "median", "formatPeriod", "originalData", "initMonthlyInventoryChart", "periodStr", "toString", "year", "substring", "month", "handleCokingCoalFactoryDepChange", "_this10", "_callee17", "_context17", "handleCokingCoalMaterialTypeChange", "_this11", "_callee18", "_context18", "handleMaterialFactoryDepChange", "_this12", "_callee19", "_context19", "handleMaterialMaterialTypeChange", "_this13", "_callee20", "_context20", "handleOverdueFactoryDepChange", "_this14", "_callee21", "_context21", "selectedOverdueFactoryDep", "_this15", "_callee22", "_context22", "overdueData", "getMockOverdueData", "initOverdueChart", "materialType", "overdueNotReceived", "overdueNotUsed", "_this16", "_callee23", "_t6", "_context23", "getMockRealTimeData", "initRealTimeInventoryChart", "materialName", "centerInventoryAmount", "machineSideInventoryAmount", "totalInventoryAmount", "_this17", "_callee24", "_t7", "_context24", "initCokingCoalInventoryChart", "handleMaterialCategoryChange", "_this18", "_callee25", "_context25", "handleMaterialItemChange", "_this19", "_callee26", "_context26", "_this20", "_callee27", "_t8", "_context27", "_this21", "_callee28", "_t9", "_context28", "getMockMaterialStatisticsData", "inAmt", "arriveRate", "_this22", "_callee29", "_t0", "_context29", "codeType", "getMockHighFrequencyData", "inNum", "handleCodeTypeChange", "_this23", "_callee30", "_context30", "handleItemTypeChange", "_this24", "_callee31", "_context31", "_this25", "_callee32", "_t1", "_context32", "timeFlag", "initSupplierRiskChart", "fetchMultiplePricePredictions", "materialNames", "_this26", "_callee34", "predictionPromises", "results", "successCount", "totalCount", "_t11", "_context34", "_ref2", "_callee33", "_t10", "_context33", "getMaterialFuturePrice", "code", "question", "prediction", "answer", "msg", "success", "_x", "apply", "r", "fetchMaterialNameList", "_this27", "_callee35", "pbMaterial", "_t12", "_context35", "getMaterialNameList", "find", "fetchPriceAndStoreData", "_this28", "_callee36", "_t13", "_context36", "getPurchasePriceAndStore", "priceAndStoreData", "initPriceTrendChart", "handleMaterialCategoryTypeChange", "_this29", "_callee37", "_context37", "handleMaterialChange", "_this30", "_callee38", "_context38", "calculateRealTimeInventoryTotal", "total", "toFixed", "calculateCokingCoalTotal", "latestDate", "purchaseCokingDailyDetailList", "detail", "instockDate", "latestDetail", "invQty", "handleCokingCoalTypeChange", "_this31", "_callee39", "_context39", "_this32", "_callee40", "_t14", "_context40", "getDepNameList", "factoryDepOptions", "handleFactoryDepChange", "_this33", "_callee41", "_context41", "selectedFactoryDep", "handleFactoryMaterialTypeChange", "_this34", "_callee42", "_context42", "selectedFactoryMaterialType", "fetchFactoryStockData", "_this35", "_callee43", "depName", "_t15", "_context43", "getListMonthly", "factoryStockData", "handlePurchaseAmountCategoriesChange", "_this36", "_callee44", "_context44", "purchaseAmountCategories", "selectedPurchaseAmountMaterials", "handleMarketPriceCategoriesChange", "_this37", "_callee45", "_context45", "marketPriceCategories", "selectedMarketPriceMaterials", "_this38", "_callee46", "_t16", "_context46", "categories", "curveType", "getMaterialNameListFromNewTables", "purchaseAmountMaterialOptions", "hasInitializedPriceChart", "checkAndTriggerInitialDataFetch", "_this39", "_callee47", "_t17", "_context47", "marketPriceMaterialOptions", "fetchPriceAndStoreDataForNewChart", "_this40", "_callee48", "itemList", "allSelectedMaterials", "_t18", "_context48", "warning", "fetchingPriceData", "getPurchasePriceAndStoreFromNewTables", "newPriceAndStoreData", "initNewPriceTrendChart", "fetchSimilarMaterials", "itemNames", "_this41", "_callee49", "_t19", "_context49", "similarMaterialsLoading", "listSimilarByItemNames", "getRankClass", "categoryMap", "priceTypeMap", "openComparisonDialog", "_this42", "currentComparison", "_objectSpread2", "comparisonDialogVisible", "fetchComparisonData", "closeComparisonDialog", "comparisonPriceData", "comparisonChartInstance", "dispose", "_this43", "_callee50", "basePriceTypeName", "comparePriceTypeName", "_t20", "_context50", "comparisonChartLoading", "compareItemName", "comparePriceType", "baseItemName", "materialData", "filteredMaterialData", "procurementPriceVoList", "priceGroup", "isMatch", "priceName", "totalMaterials", "materials", "_m$procurementPriceVo", "_m$procurementPriceVo2", "priceGroupCount", "priceGroups", "renderComparisonChart", "chartDom", "init", "formatDate", "dateStr", "day", "allDates", "priceList", "add", "recordDate", "from", "xAxisData", "series", "legendData", "colors", "colorIndex", "materialCount", "baseMaterial", "compareMaterial", "index", "_priceGroup$priceList", "groupIndex", "priceData", "date", "found", "price", "validDataCount", "uniqueName", "lineColor", "dataStr", "JSON", "stringify", "existingSeries", "adjustedData", "smooth", "lineStyle", "width", "color", "itemStyle", "symbol", "symbolSize", "connectNulls", "z", "i", "validCount", "priceMin", "priceMax", "priceValues", "flatMap", "Math", "min", "max", "option", "backgroundColor", "tooltip", "trigger", "axisPointer", "formatter", "str", "axisValueLabel", "marker", "seriesName", "legend", "textStyle", "top", "grid", "left", "right", "bottom", "containLabel", "xAxis", "axisLabel", "interval", "uniqueMonths", "monthsCount", "size", "totalDataPoints", "idealInterval", "floor", "originalDateStr", "axisLine", "yAxis", "splitLine", "setOption", "_this44", "includes"], "sources": ["src/views/purchaseDashboardPlan/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <div class=\"dashboard-header\">\r\n      <h1>采购计划看板</h1>\r\n      <div class=\"header-controls\">\r\n        <div class=\"fullscreen-btn\" @click=\"toggleFullscreen\" :title=\"isFullscreen ? '退出全屏' : '进入全屏'\">\r\n          <i :class=\"isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'\"></i>\r\n        </div>\r\n        <div class=\"time-filter\">\r\n          <button\r\n            v-for=\"filter in timeFilters\"\r\n            :key=\"filter.id\"\r\n            :class=\"['time-filter-btn', { active: filter.id === activeFilter }]\"\r\n            @click=\"handleTimeFilterChange(filter.id, filter.value)\"\r\n          >\r\n            {{ filter.label }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"dashboard-grid\">\r\n      <!-- 第一行：订单至入库天数 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          订单至入库天数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedOrderFactoryDep\"\r\n              @change=\"handleOrderFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedOrderMaterialType\"\r\n              @change=\"handleOrderMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"monthlyInventoryChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第一行：商务部接收至挂单天数 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          商务部接收至挂单天数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedFactoryDep\"\r\n              @change=\"handleFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedFactoryMaterialType\"\r\n              @change=\"handleFactoryMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"factoryStockChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 入库至领用天数 -->\r\n      <div class=\"card material-chart-card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          入库至领用天数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedMaterialFactoryDep\"\r\n              @change=\"handleMaterialFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedMaterialMaterialType\"\r\n              @change=\"handleMaterialMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"materialStatisticsChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第二行：实时超期数 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          实时超期数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedOverdueFactoryDep\"\r\n              @change=\"handleOverdueFactoryDepChange\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"overdueChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      \r\n\r\n    </div>\r\n\r\n    \r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport chartMethods from './chartMethods'\r\nimport extendedChartMethods from './chartMethodsExtended'\r\nimport screenfull from 'screenfull'\r\nimport { showYearlyAmount, showRealTimeAmount, showCokingCoalAmount, showKeyIndicators, showItemTypeList, showMaterialList, showData, showSuppList, showPurchasePlanList, showHighFrequencyMaterialList, showPurchaseSuppRisk, getMaterialFuturePrice, getMaterialNameList, getPurchasePriceAndStore, getMaterialNameListFromNewTables, getPurchasePriceAndStoreFromNewTables } from '@/api/purchaseDashboard/purchaseDashboard'\r\nimport { listSimilarByItemNames } from '@/api/purchase/similar'\r\nimport { getDepNameList, getListMonthly } from '@/api/purchase/purdchaseFactoryStock'\r\n\r\nexport default {\r\n  name: 'PurchaseDashboard',\r\n  mixins: [chartMethods, extendedChartMethods],\r\n  data() {\r\n    return {\r\n     \r\n\r\n      // 数据\r\n      dashboardData: {},\r\n      purchaseStats: {},\r\n\r\n      // 下拉选项\r\n      topSuppliersOptions: [],\r\n\r\n      // 选中的过滤器值\r\n      selectedTopSuppliersFilter: '',\r\n      selectedOrderType: 'TOP', // 排序类型，默认为TOP\r\n\r\n      // 图表实例\r\n      chartInstances: {},\r\n\r\n      // 原始数据备份\r\n      originalTopSuppliersData: [],\r\n\r\n      // 订单至入库天数相关\r\n      selectedOrderFactoryDep: '', // 选中的分厂\r\n      selectedOrderMaterialType: '', // 选中的物料类型\r\n      orderToReceiptData: [], // 订单至入库天数数据\r\n\r\n      // 第二个订单至入库天数模块相关\r\n      selectedCokingCoalFactoryDep: '', // 选中的分厂\r\n      selectedCokingCoalMaterialType: '', // 选中的物料类型\r\n\r\n      // 第三个订单至入库天数模块相关\r\n      selectedMaterialFactoryDep: '', // 选中的分厂\r\n      selectedMaterialMaterialType: '', // 选中的物料类型\r\n      realTimeInventoryData: [],\r\n      cokingCoalInventoryData: [],\r\n\r\n      // 矿焦煤库存图表相关\r\n      selectedCokingCoalType: '', // 选中的矿焦煤类型，默认为空（全部）\r\n\r\n      // 物料入库统计相关\r\n      selectedMaterialCategory: '1',\r\n      selectedMaterialItem: '',\r\n      materialItemOptions: [],\r\n      materialStatisticsData: [],\r\n\r\n      // 高频采购物料相关\r\n      selectedCodeType: 'ALL',\r\n      selectedItemType: 'CLASS3',\r\n      highFrequencyMaterialData: [],\r\n\r\n      // 供应商风险数据\r\n      supplierRiskData: [],\r\n\r\n      // AI价格预测相关\r\n      pricePredictions: [], // 改为数组，支持多个物料的预测\r\n      predictionLoading: false,\r\n\r\n      // 物料价格趋势图相关\r\n      materialNameOptions: [],\r\n      selectedMaterial: 'PB块',\r\n      selectedMaterialCategory: '1', // 默认选择矿石\r\n      priceAndStoreData: null,\r\n\r\n      // 新的价格趋势图相关属性\r\n      // 采购量曲线\r\n      purchaseAmountCategories: [99], // 默认选择全部\r\n      selectedPurchaseAmountMaterials: [],\r\n      purchaseAmountMaterialOptions: [],\r\n\r\n      // 市场价曲线\r\n      marketPriceCategories: [99], // 默认选择全部\r\n      selectedMarketPriceMaterials: [],\r\n      marketPriceMaterialOptions: [],\r\n\r\n      // 获取数据状态\r\n      fetchingPriceData: false,\r\n      newPriceAndStoreData: null,\r\n\r\n      // 初始化标志\r\n      hasInitializedPriceChart: false,\r\n\r\n      // 相似物料数据\r\n      similarMaterialsData: [],\r\n      similarMaterialsLoading: false,\r\n\r\n      // 对比弹框相关\r\n      comparisonDialogVisible: false,\r\n      comparisonChartLoading: false,\r\n      currentComparison: {},\r\n      comparisonChartInstance: null,\r\n      comparisonPriceData: null,\r\n\r\n      // 机旁库当前库存相关\r\n      selectedFactoryDep: '', // 选中的分厂\r\n      selectedFactoryMaterialType: '', // 选中的物料类型\r\n      factoryDepOptions: [], // 分厂选项列表\r\n      factoryStockData: [], // 机旁库存数据\r\n\r\n      // 实时超期数相关\r\n      selectedOverdueFactoryDep: '', // 选中的分厂\r\n      overdueData: [] // 超期数据\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isFullscreen() {\r\n      return this.$store.state.app.isFullscreenMode\r\n    },\r\n\r\n    // 按itemName、category、priceType联合索引分组相似物料数据\r\n    groupedSimilarMaterials() {\r\n      const grouped = {}\r\n      this.similarMaterialsData.forEach(item => {\r\n        // 创建联合索引key\r\n        const groupKey = `${item.itemName}_${item.category}_${item.priceType}`\r\n        const displayKey = `${item.itemName} (${this.getCategoryName(item.category)} - ${this.getPriceTypeName(item.priceType)})`\r\n\r\n        if (!grouped[displayKey]) {\r\n          grouped[displayKey] = {\r\n            groupKey: groupKey,\r\n            items: []\r\n          }\r\n        }\r\n        grouped[displayKey].items.push(item)\r\n      })\r\n\r\n      // 对每个组内的数据按排名排序\r\n      Object.keys(grouped).forEach(key => {\r\n        grouped[key].items.sort((a, b) => a.rank - b.rank)\r\n      })\r\n\r\n      return grouped\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.checkEchartsAvailability()\r\n    this.fetchDashboardData(3)\r\n\r\n    this.fetchRealTimeInventoryData()\r\n    this.fetchCokingCoalInventoryData()\r\n    // 初始化物料入库统计的下拉框选项和数据\r\n    this.updateMaterialItemOptions().then(() => {\r\n      this.fetchMaterialStatisticsData()\r\n    })\r\n    // 初始化高频采购物料数据\r\n    this.fetchHighFrequencyMaterialData()\r\n    // 初始化供应商风险数据\r\n    this.fetchSupplierRiskData()\r\n\r\n    // 初始化新的物料名称列表（会自动触发默认选中PB块和数据获取）\r\n    this.fetchPurchaseAmountMaterialList()\r\n    this.fetchMarketPriceMaterialList()\r\n\r\n    // 初始化机旁库存数据\r\n    this.fetchFactoryDepOptions()\r\n\r\n    // 初始化订单至入库天数数据\r\n    this.fetchOrderToReceiptData()\r\n\r\n    // 初始化超期数据\r\n    this.fetchOverdueData()\r\n\r\n    this.setupResizeObserver()\r\n    this.initFullscreenListener()\r\n\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.resizeAllCharts)\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 清理定时器和事件监听器\r\n    this.clearAllIntervals()\r\n    this.removeFullscreenListener()\r\n    window.removeEventListener('resize', this.resizeAllCharts)\r\n\r\n    // 确保退出全屏模式\r\n    this.$store.dispatch('app/setFullscreenMode', false)\r\n  },\r\n\r\n  methods: {\r\n    // 初始化全屏监听器\r\n    initFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.on('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 移除全屏监听器\r\n    removeFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.off('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 处理全屏状态变化\r\n    handleFullscreenChange() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        const isFullscreen = screenfull.isFullscreen\r\n        this.$store.dispatch('app/setFullscreenMode', isFullscreen)\r\n\r\n        // 全屏状态变化后，重新调整图表大小\r\n        this.$nextTick(() => {\r\n          setTimeout(() => {\r\n            this.resizeAllCharts()\r\n          }, 300) // 给布局变化一些时间\r\n        })\r\n      }\r\n    },\r\n\r\n    // API调用方法\r\n    async getDashboardData(dimensionType) {\r\n      return await showData({ dimensionType: dimensionType })\r\n    },\r\n\r\n    async getItemTypeList(itemType) {\r\n      return await showItemTypeList({ itemType: itemType })\r\n    },\r\n\r\n    async getMaterialList(params) {\r\n      return await showMaterialList(params)\r\n    },\r\n\r\n    async getSupplierList(params) {\r\n      return await showSuppList(params)\r\n    },\r\n\r\n    async getYearlyAmount(params) {\r\n      return await showYearlyAmount(params)\r\n    },\r\n\r\n    async getRealTimeAmount() {\r\n      return await showRealTimeAmount()\r\n    },\r\n\r\n    async getCokingCoalAmount() {\r\n      return await showCokingCoalAmount()\r\n    },\r\n\r\n    async getKeyIndicators(params) {\r\n      return await showKeyIndicators(params)\r\n    },\r\n\r\n    async getHighFrequencyMaterialList(params) {\r\n      return await showHighFrequencyMaterialList(params)\r\n    },\r\n\r\n    async getPurchaseSuppRisk(params) {\r\n      return await showPurchaseSuppRisk(params)\r\n    },\r\n\r\n    // 根据dimensionType获取timeFlag\r\n    getTimeFlagByDimensionType(dimensionType) {\r\n      switch(dimensionType) {\r\n        case 1: return '03' // 近三个月\r\n        case 2: return '06' // 近六个月\r\n        case 3: return '12' // 近一年\r\n        default: return '03'\r\n      }\r\n    },\r\n\r\n    // 检查ECharts可用性\r\n    checkEchartsAvailability() {\r\n      if (!echarts) {\r\n        console.error('ECharts库未能加载，使用备用显示方式')\r\n        document.querySelectorAll('.chart').forEach(el => {\r\n          el.innerHTML = '<div class=\"chart-placeholder\">图表加载失败</div>'\r\n        })\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n\r\n    // 获取仪表板数据\r\n    async fetchDashboardData(dimensionTypeParam = 1) {\r\n      this.currentDimensionType = dimensionTypeParam\r\n\r\n      // 清除所有定时器\r\n      this.clearAllIntervals()\r\n\r\n      try {\r\n        // 并行获取仪表板数据和关键指标数据\r\n        const [dashboardResponse, keyIndicatorsResponse] = await Promise.all([\r\n          this.getDashboardData(dimensionTypeParam),\r\n          this.getKeyIndicators({ dimensionType: dimensionTypeParam })\r\n        ])\r\n\r\n        // 处理仪表板数据\r\n        if (dashboardResponse && dashboardResponse.data) {\r\n          this.dashboardData = dashboardResponse.data\r\n          console.log('获取仪表板数据成功:', this.dashboardData)\r\n        } else {\r\n          console.error('API数据格式不正确或缺少data字段', dashboardResponse)\r\n          this.showErrorMessage('API数据格式不正确或缺少data字段')\r\n        }\r\n\r\n        // 处理关键指标数据\r\n        if (keyIndicatorsResponse && keyIndicatorsResponse.data) {\r\n          this.purchaseStats = keyIndicatorsResponse.data || {}\r\n          console.log('获取关键指标数据成功:', this.purchaseStats)\r\n        } else {\r\n          console.error('获取关键指标数据失败', keyIndicatorsResponse)\r\n          this.purchaseStats = {}\r\n        }\r\n\r\n        this.initAllCharts()\r\n      } catch (error) {\r\n        console.error('API请求或数据处理失败', error)\r\n        this.showErrorMessage('数据加载失败: ' + error.message)\r\n      }\r\n    },\r\n\r\n    // 显示错误信息\r\n    showErrorMessage(message) {\r\n      document.querySelectorAll('.chart').forEach(chart => {\r\n        chart.innerHTML = `<div class=\"chart-placeholder\">${message}</div>`\r\n      })\r\n    },\r\n\r\n    // 时间过滤器变化处理\r\n    handleTimeFilterChange(filterId, dimensionType) {\r\n      this.activeFilter = filterId\r\n      this.currentDimensionType = dimensionType\r\n      console.log('选择的时间范围:', filterId, '维度:', dimensionType)\r\n\r\n      this.clearAllIntervals()\r\n      this.fetchDashboardData(dimensionType)\r\n      // 同时更新高频物料数据\r\n      this.fetchHighFrequencyMaterialData()\r\n      // 同时更新供应商风险数据\r\n      this.fetchSupplierRiskData()\r\n      // 同时更新物料入库统计数据\r\n      this.fetchMaterialStatisticsData()\r\n      // 注意：价格趋势数据只在用户主动点击按钮时获取，不在时间过滤器变化时自动获取\r\n\r\n      // 同时更新新的物料列表（用于下拉框选项），但不会自动触发数据获取\r\n      this.fetchPurchaseAmountMaterialList()\r\n      this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 清除所有定时器\r\n    clearAllIntervals() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance && instance.intervalId) {\r\n          clearInterval(instance.intervalId)\r\n          instance.intervalId = null\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重新调整所有图表大小\r\n    resizeAllCharts() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance) {\r\n          try {\r\n            instance.resize()\r\n          } catch(err) {\r\n            console.error('图表大小调整失败:', err)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 初始化所有图表\r\n    initAllCharts() {\r\n      const timestamp = new Date().toLocaleTimeString()\r\n      console.log(`initAllCharts started [${timestamp}]`)\r\n      try {\r\n        // 注意：实时库存图表和矿焦煤库存图表会在各自数据获取完成后单独初始化\r\n        // 注意：第一个订单至入库天数图表使用真实数据，会在fetchOrderToReceiptData完成后单独初始化\r\n        // 注意：物料入库统计图表会在fetchMaterialStatisticsData完成后单独初始化\r\n        // 注意：机旁库存图表会在fetchFactoryStockData完成后单独初始化\r\n\r\n        // 初始化其他订单至入库天数图表（第一个会在数据获取完成后单独初始化）\r\n        this.initFactoryStockChart()\r\n        this.initCokingCoalLineChart()\r\n        this.initMaterialStatisticsChart()\r\n\r\n        // 初始化物料词云图\r\n        this.initMaterialCloud()\r\n\r\n        // 初始化TOP供应商图\r\n        this.initTopSuppliersChart()\r\n        this.populateItemDropdown('topSuppliersFilter', 1, 'topSuppliersOptions')\r\n\r\n        // 注意：供应商风险图表会在fetchSupplierRiskData完成后单独初始化\r\n\r\n        // 注意：实时超期数图表会在fetchOverdueData完成后单独初始化\r\n\r\n        // 注意：采购价格趋势图会在fetchPriceAndStoreData完成后单独初始化\r\n\r\n        console.log('所有图表初始化完成')\r\n      } catch (err) {\r\n        console.error('图表初始化主流程失败:', err)\r\n        this.showErrorMessage('图表初始化失败: ' + err.message)\r\n      }\r\n    },\r\n\r\n    // 填充物料类型下拉框\r\n    async populateItemDropdown(selectElementId, itemType, dataPropertyName) {\r\n      try {\r\n        const response = await this.getItemTypeList(itemType)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this[dataPropertyName] = response.data\r\n        } else {\r\n          console.error(`Invalid data format from showItemTypeList for itemType ${itemType}:`, response)\r\n          this[dataPropertyName] = []\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error fetching item types for ${selectElementId}:`, error)\r\n        this[dataPropertyName] = []\r\n      }\r\n    },\r\n\r\n    // 下拉框变化处理方法\r\n    async handleTopSuppliersFilterChange() {\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async handleOrderTypeChange() {\r\n      console.log('排序类型变化:', this.selectedOrderType)\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async refreshTopSuppliersChart() {\r\n      console.log(`Top supplier filter selected item ID: ${this.selectedTopSuppliersFilter}, orderType: ${this.selectedOrderType}`)\r\n      const myChart = this.chartInstances.topSuppliersChart\r\n      if (!myChart) {\r\n        console.error(\"TOP10供应商图表实例未找到\")\r\n        return\r\n      }\r\n\r\n      if (myChart.intervalId) {\r\n        clearInterval(myChart.intervalId)\r\n        myChart.intervalId = null\r\n      }\r\n\r\n      if (!this.selectedTopSuppliersFilter || this.selectedTopSuppliersFilter === \"\") {\r\n        // 使用原始数据，但需要根据orderType重新获取\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n            newSupplierData = this.originalTopSuppliersData\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          this.renderAndPaginateTopSuppliers(myChart, this.originalTopSuppliersData)\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      } else {\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            itemId: this.selectedTopSuppliersFilter,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          document.getElementById('topSuppliersChart').innerHTML = '<div class=\"chart-placeholder\">供应商数据加载失败</div>'\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      }\r\n    },\r\n\r\n    // 设置大小调整观察器\r\n    setupResizeObserver() {\r\n      const resizeObserver = new ResizeObserver(entries => {\r\n        for (let entry of entries) {\r\n          const charts = entry.target.querySelectorAll('.chart')\r\n          charts.forEach(chart => {\r\n            if (chart.id) {\r\n              const instance = echarts.getInstanceByDom(document.getElementById(chart.id))\r\n              if (instance) {\r\n                instance.resize()\r\n              }\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      document.querySelectorAll('.card').forEach(card => {\r\n        resizeObserver.observe(card)\r\n      })\r\n    },\r\n\r\n    toggleFullscreen() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.toggle()\r\n      } else {\r\n        this.$message({\r\n          message: '您的浏览器不支持全屏功能',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n\r\n    async handleOrderFactoryDepChange() {\r\n      console.log('订单至入库天数 - 分厂变化:', this.selectedOrderFactoryDep)\r\n      // 获取新数据并更新图表\r\n      await this.fetchOrderToReceiptData()\r\n    },\r\n\r\n    async handleOrderMaterialTypeChange() {\r\n      console.log('订单至入库天数 - 物料类型变化:', this.selectedOrderMaterialType)\r\n      // 获取新数据并更新图表\r\n      await this.fetchOrderToReceiptData()\r\n    },\r\n\r\n    // 获取订单至入库天数数据\r\n    async fetchOrderToReceiptData() {\r\n      try {\r\n        console.log('fetchOrderToReceiptData - 开始获取数据')\r\n        console.log('fetchOrderToReceiptData - 调用API: showPurchasePlanList')\r\n\r\n        const response = await showPurchasePlanList({\r\n          dimensionType: 3 // 使用固定的维度类型\r\n        })\r\n        console.log('fetchOrderToReceiptData - API调用成功')\r\n        console.log('fetchOrderToReceiptData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          console.log('fetchOrderToReceiptData - 原始数据长度:', response.data.length)\r\n          console.log('fetchOrderToReceiptData - 原始数据前3条:', response.data.slice(0, 3))\r\n\r\n          // 检查数据结构\r\n          console.log('fetchOrderToReceiptData - 检查数据结构')\r\n\r\n          // 检查是否有countType字段\r\n          const hasCountType = response.data.some(item => item.countType !== undefined)\r\n          console.log('fetchOrderToReceiptData - 是否有countType字段:', hasCountType)\r\n\r\n          let dataToProcess = response.data\r\n\r\n          if (hasCountType) {\r\n            // 检查所有可能的countType值\r\n            const countTypes = [...new Set(response.data.map(item => item.countType))]\r\n            console.log('fetchOrderToReceiptData - 所有countType值:', countTypes)\r\n\r\n            // 筛选countType=\"B\"的数据\r\n            const filteredData = response.data.filter(item => item.countType === 'B')\r\n            console.log('fetchOrderToReceiptData - 筛选后的数据:', filteredData)\r\n            console.log('fetchOrderToReceiptData - 筛选后数据长度:', filteredData.length)\r\n\r\n            if (filteredData.length === 0) {\r\n              console.warn('fetchOrderToReceiptData - 没有找到countType=\"B\"的数据，使用所有数据')\r\n              dataToProcess = response.data\r\n            } else {\r\n              dataToProcess = filteredData\r\n            }\r\n          } else {\r\n            console.log('fetchOrderToReceiptData - 没有countType字段，使用所有数据')\r\n          }\r\n\r\n          if (dataToProcess.length === 0) {\r\n            console.warn('fetchOrderToReceiptData - 没有可用数据，不显示图表')\r\n            this.orderToReceiptData = []\r\n          } else {\r\n            // 检查第一条数据的所有字段\r\n            console.log('fetchOrderToReceiptData - 第一条数据的所有字段:', Object.keys(dataToProcess[0]))\r\n            console.log('fetchOrderToReceiptData - 第一条数据内容:', dataToProcess[0])\r\n            console.log('fetchOrderToReceiptData - 第一条数据的midDays字段:', dataToProcess[0].midDays)\r\n\r\n            // 按period排序（从小到大）\r\n            const sortedData = dataToProcess.sort((a, b) => {\r\n              const periodA = parseInt(a.period) || 0\r\n              const periodB = parseInt(b.period) || 0\r\n              return periodA - periodB\r\n            })\r\n\r\n            // 转换数据格式，尝试多个可能的字段名\r\n            this.orderToReceiptData = sortedData.map(item => {\r\n              // 尝试多个可能的平均天数字段名\r\n              let avgDays = 0\r\n              if (item.avgDays !== undefined) {\r\n                avgDays = parseFloat(item.avgDays) || 0\r\n              } else if (item.avgDay !== undefined) {\r\n                avgDays = parseFloat(item.avgDay) || 0\r\n              } else if (item.averageDays !== undefined) {\r\n                avgDays = parseFloat(item.averageDays) || 0\r\n              } else if (item.days !== undefined) {\r\n                avgDays = parseFloat(item.days) || 0\r\n              } else if (item.dayCount !== undefined) {\r\n                avgDays = parseFloat(item.dayCount) || 0\r\n              }\r\n\r\n              // 提取中位数字段\r\n              let midDays = 0\r\n              if (item.midDays !== undefined) {\r\n                midDays = parseFloat(item.midDays) || 0\r\n              } else if (item.midDay !== undefined) {\r\n                midDays = parseFloat(item.midDay) || 0\r\n              } else if (item.medianDays !== undefined) {\r\n                midDays = parseFloat(item.medianDays) || 0\r\n              } else if (item.median !== undefined) {\r\n                midDays = parseFloat(item.median) || 0\r\n              }\r\n\r\n              console.log(`处理数据项 ${item.period}: 平均天数 = ${avgDays}, 中位数 = ${midDays}`)\r\n\r\n              return {\r\n                period: this.formatPeriod(item.period),\r\n                avgDays: avgDays,\r\n                midDays: midDays,\r\n                // 添加原始数据用于调试\r\n                originalData: item\r\n              }\r\n            })\r\n\r\n            console.log('fetchOrderToReceiptData - 处理后的数据:', this.orderToReceiptData)\r\n          }\r\n        } else {\r\n          console.error('获取订单至入库天数数据失败，不显示图表', response)\r\n          this.orderToReceiptData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取订单至入库天数数据失败:', error)\r\n        console.error('错误详情:', error.message)\r\n        this.orderToReceiptData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMonthlyInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 格式化期间（202508 -> 2025.8）\r\n    formatPeriod(period) {\r\n      if (!period) return ''\r\n      const periodStr = period.toString()\r\n      if (periodStr.length === 6) {\r\n        const year = periodStr.substring(0, 4)\r\n        const month = parseInt(periodStr.substring(4, 6))\r\n        return `${year}.${month}`\r\n      }\r\n      return periodStr\r\n    },\r\n\r\n\r\n\r\n    // 第二个订单至入库天数模块的事件处理\r\n    async handleCokingCoalFactoryDepChange() {\r\n      console.log('第二个订单至入库天数 - 分厂变化:', this.selectedCokingCoalFactoryDep)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initCokingCoalLineChart()\r\n    },\r\n\r\n    async handleCokingCoalMaterialTypeChange() {\r\n      console.log('第二个订单至入库天数 - 物料类型变化:', this.selectedCokingCoalMaterialType)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initCokingCoalLineChart()\r\n    },\r\n\r\n    // 第三个订单至入库天数模块的事件处理\r\n    async handleMaterialFactoryDepChange() {\r\n      console.log('第三个订单至入库天数 - 分厂变化:', this.selectedMaterialFactoryDep)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initMaterialStatisticsChart()\r\n    },\r\n\r\n    async handleMaterialMaterialTypeChange() {\r\n      console.log('第三个订单至入库天数 - 物料类型变化:', this.selectedMaterialMaterialType)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initMaterialStatisticsChart()\r\n    },\r\n\r\n    // 实时超期数相关方法\r\n    async handleOverdueFactoryDepChange() {\r\n      console.log('实时超期数 - 分厂变化:', this.selectedOverdueFactoryDep)\r\n      await this.fetchOverdueData()\r\n    },\r\n\r\n    async fetchOverdueData() {\r\n      try {\r\n        // 这里可以根据selectedOverdueFactoryDep获取真实数据\r\n        // 暂时使用模拟数据\r\n        this.overdueData = this.getMockOverdueData()\r\n\r\n        // 数据获取完成后重新初始化图表\r\n        this.$nextTick(() => {\r\n          this.initOverdueChart()\r\n        })\r\n      } catch (error) {\r\n        console.error('获取超期数据失败:', error)\r\n        this.overdueData = this.getMockOverdueData()\r\n        this.$nextTick(() => {\r\n          this.initOverdueChart()\r\n        })\r\n      }\r\n    },\r\n\r\n    // 生成模拟超期数据\r\n    getMockOverdueData() {\r\n      return [\r\n        { materialType: '原材料', overdueNotReceived: 25, overdueNotUsed: 18 },\r\n        { materialType: '辅耐材', overdueNotReceived: 12, overdueNotUsed: 8 },\r\n        { materialType: '材料类', overdueNotReceived: 35, overdueNotUsed: 22 },\r\n        { materialType: '通用备件', overdueNotReceived: 18, overdueNotUsed: 15 },\r\n        { materialType: '专用备件', overdueNotReceived: 28, overdueNotUsed: 20 },\r\n        { materialType: '办公', overdueNotReceived: 5, overdueNotUsed: 3 }\r\n      ]\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    async fetchRealTimeInventoryData() {\r\n      try {\r\n        const response = await this.getRealTimeAmount()\r\n        console.log('fetchRealTimeInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.realTimeInventoryData = response.data || []\r\n          console.log('fetchRealTimeInventoryData - 设置的数据:', this.realTimeInventoryData)\r\n        } else {\r\n          console.error('获取实时库存数据失败，使用模拟数据', response)\r\n          // 使用模拟数据\r\n          this.realTimeInventoryData = this.getMockRealTimeData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取实时库存数据失败，使用模拟数据:', error)\r\n        // 使用模拟数据\r\n        this.realTimeInventoryData = this.getMockRealTimeData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initRealTimeInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟实时库存数据\r\n    getMockRealTimeData() {\r\n      return [\r\n        {\r\n          materialType: 'A',\r\n          materialName: '通用备件',\r\n          centerInventoryAmount: 1250.30,\r\n          machineSideInventoryAmount: 380.50,\r\n          totalInventoryAmount: 1630.80\r\n        },\r\n        {\r\n          materialType: 'B',\r\n          materialName: '专用备件',\r\n          centerInventoryAmount: 980.75,\r\n          machineSideInventoryAmount: 420.25,\r\n          totalInventoryAmount: 1401.00\r\n        },\r\n        {\r\n          materialType: 'C',\r\n          materialName: '材料类',\r\n          centerInventoryAmount: 2150.60,\r\n          machineSideInventoryAmount: 650.40,\r\n          totalInventoryAmount: 2801.00\r\n        },\r\n        {\r\n          materialType: 'D',\r\n          materialName: '原材料',\r\n          centerInventoryAmount: 3200.90,\r\n          machineSideInventoryAmount: 890.10,\r\n          totalInventoryAmount: 4091.00\r\n        },\r\n        {\r\n          materialType: 'E',\r\n          materialName: '辅耐材',\r\n          centerInventoryAmount: 1580.40,\r\n          machineSideInventoryAmount: 320.60,\r\n          totalInventoryAmount: 1901.00\r\n        },\r\n        {\r\n          materialType: 'G',\r\n          materialName: '办公',\r\n          centerInventoryAmount: 150.20,\r\n          machineSideInventoryAmount: 50.80,\r\n          totalInventoryAmount: 201.00\r\n        }\r\n      ]\r\n    },\r\n\r\n    async fetchCokingCoalInventoryData() {\r\n      try {\r\n        const response = await this.getCokingCoalAmount()\r\n        console.log('fetchCokingCoalInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.cokingCoalInventoryData = response.data || []\r\n          console.log('fetchCokingCoalInventoryData - 设置的数据:', this.cokingCoalInventoryData)\r\n        } else {\r\n          console.error('获取矿焦煤库存数据失败', response)\r\n          this.cokingCoalInventoryData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取矿焦煤库存数据失败:', error)\r\n        this.cokingCoalInventoryData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 物料入库统计相关方法\r\n    async handleMaterialCategoryChange() {\r\n      console.log('物料类别变化:', this.selectedMaterialCategory)\r\n      this.selectedMaterialItem = '' // 重置第二个下拉框\r\n      await this.updateMaterialItemOptions()\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async handleMaterialItemChange() {\r\n      console.log('物料项目变化:', this.selectedMaterialItem)\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async updateMaterialItemOptions() {\r\n      if (this.selectedMaterialCategory === '1') {\r\n        // 大类：只有全部选项\r\n        this.materialItemOptions = []\r\n      } else {\r\n        // 中类、细类、叶类：获取对应的选项\r\n        const itemType = parseInt(this.selectedMaterialCategory) - 1 // 1->0, 2->1, 3->2, 4->3\r\n        try {\r\n          const response = await this.getItemTypeList(itemType)\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            this.materialItemOptions = response.data\r\n          } else {\r\n            this.materialItemOptions = []\r\n          }\r\n        } catch (error) {\r\n          console.error('获取物料项目选项失败:', error)\r\n          this.materialItemOptions = []\r\n        }\r\n      }\r\n    },\r\n\r\n    async fetchMaterialStatisticsData() {\r\n      try {\r\n        const params = {\r\n          itemType: parseInt(this.selectedMaterialCategory),\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        // 如果选择了具体物料项目，添加itemId参数\r\n        if (this.selectedMaterialItem && this.selectedMaterialItem !== '') {\r\n          params.itemId = this.selectedMaterialItem\r\n        }\r\n\r\n        console.log('fetchMaterialStatisticsData - 请求参数:', params)\r\n        const response = await this.getMaterialList(params)\r\n        console.log('fetchMaterialStatisticsData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.materialStatisticsData = response.data || []\r\n          console.log('fetchMaterialStatisticsData - 设置的数据:', this.materialStatisticsData)\r\n        } else {\r\n          console.error('获取物料统计数据失败，使用模拟数据', response)\r\n          this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料统计数据失败，使用模拟数据:', error)\r\n        this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialStatisticsChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟物料统计数据\r\n    getMockMaterialStatisticsData() {\r\n      return [\r\n        { itemName: '通用备件', inAmt: 1250.30, arriveRate: 85.5 },\r\n        { itemName: '专用备件', inAmt: 980.75, arriveRate: 78.2 },\r\n        { itemName: '材料类', inAmt: 2150.60, arriveRate: 92.1 },\r\n        { itemName: '原材料', inAmt: 3200.90, arriveRate: 88.7 },\r\n        { itemName: '辅耐材', inAmt: 1580.40, arriveRate: 91.3 },\r\n        { itemName: '办公', inAmt: 150.20, arriveRate: 95.0 }\r\n      ]\r\n    },\r\n\r\n    async fetchHighFrequencyMaterialData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          codeType: this.selectedCodeType,\r\n          itemType: this.selectedItemType\r\n        }\r\n\r\n        console.log('fetchHighFrequencyMaterialData - 请求参数:', params)\r\n        const response = await this.getHighFrequencyMaterialList(params)\r\n        console.log('fetchHighFrequencyMaterialData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.highFrequencyMaterialData = response.data || []\r\n          console.log('fetchHighFrequencyMaterialData - 设置的数据:', this.highFrequencyMaterialData)\r\n        } else {\r\n          console.error('获取高频物料数据失败，使用模拟数据', response)\r\n          this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取高频物料数据失败，使用模拟数据:', error)\r\n        this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialCloud()\r\n      })\r\n    },\r\n\r\n    // 生成模拟高频物料数据\r\n    getMockHighFrequencyData() {\r\n      return [\r\n        { itemName: '粗粉', inAmt: 392467.2, inNum: 5421293 },\r\n        { itemName: '精粉', inAmt: 280350.5, inNum: 4250180 },\r\n        { itemName: '球团', inAmt: 195200.8, inNum: 3180970 },\r\n        { itemName: '烧结', inAmt: 150420.3, inNum: 2890540 },\r\n        { itemName: '焦炭', inAmt: 125680.7, inNum: 2350210 },\r\n        { itemName: '煤炭', inAmt: 98750.2, inNum: 1980760 }\r\n      ]\r\n    },\r\n\r\n    async handleCodeTypeChange() {\r\n      console.log('大类类型变化:', this.selectedCodeType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    async handleItemTypeChange() {\r\n      console.log('维度变化:', this.selectedItemType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    // 获取供应商风险数据\r\n    async fetchSupplierRiskData() {\r\n      try {\r\n        const params = {\r\n          timeFlag: this.getTimeFlagByDimensionType(this.currentDimensionType)\r\n        }\r\n\r\n        console.log('fetchSupplierRiskData - 请求参数:', params)\r\n        const response = await this.getPurchaseSuppRisk(params)\r\n        console.log('fetchSupplierRiskData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.supplierRiskData = response.data || []\r\n          console.log('fetchSupplierRiskData - 设置的数据:', this.supplierRiskData)\r\n        } else {\r\n          console.error('获取供应商风险数据失败', response)\r\n          this.supplierRiskData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取供应商风险数据失败:', error)\r\n        this.supplierRiskData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initSupplierRiskChart()\r\n      })\r\n    },\r\n\r\n    // 获取多个物料的AI价格预测\r\n    async fetchMultiplePricePredictions(materialNames) {\r\n      this.predictionLoading = true\r\n      this.pricePredictions = [] // 清空之前的预测结果\r\n\r\n      try {\r\n        // 并行调用所有物料的预测接口\r\n        const predictionPromises = materialNames.map(async (materialName) => {\r\n          try {\r\n            const params = {\r\n              materialName: materialName,\r\n              materialType: '1' // 默认使用矿石类型，可以根据需要调整\r\n            }\r\n\r\n            console.log(`fetchPricePrediction - ${materialName} 请求参数:`, params)\r\n            const response = await getMaterialFuturePrice(params)\r\n            console.log(`fetchPricePrediction - ${materialName} 完整响应:`, response)\r\n\r\n            if (response && response.code && response.code === 200 && response.data) {\r\n              return {\r\n                materialName: materialName,\r\n                question: response.data.question || `关于${materialName}的价格预测`,\r\n                prediction: response.data.answer || response.data.prediction || response.msg,\r\n                success: response.data.success !== false\r\n              }\r\n            } else {\r\n              console.error(`获取${materialName}价格预测数据失败`, response)\r\n              return {\r\n                materialName: materialName,\r\n                question: `关于${materialName}的价格预测`,\r\n                prediction: `获取${materialName}价格预测失败`,\r\n                success: false\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error(`获取${materialName}价格预测数据失败:`, error)\r\n            return {\r\n              materialName: materialName,\r\n              question: `关于${materialName}的价格预测`,\r\n              prediction: `获取${materialName}价格预测失败：${error.message}`,\r\n              success: false\r\n            }\r\n          }\r\n        })\r\n\r\n        // 等待所有预测结果\r\n        const results = await Promise.all(predictionPromises)\r\n        this.pricePredictions = results\r\n        console.log('fetchMultiplePricePredictions - 设置的预测数据:', this.pricePredictions)\r\n\r\n        const successCount = results.filter(r => r.success).length\r\n        const totalCount = results.length\r\n\r\n        if (successCount > 0) {\r\n          this.$message.success(`成功获取${successCount}/${totalCount}个物料的价格预测`)\r\n        } else {\r\n          this.$message.error('所有物料的价格预测获取失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('批量获取价格预测数据失败:', error)\r\n        this.$message.error('批量获取价格预测失败：' + error.message)\r\n      } finally {\r\n        this.predictionLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取物料名称列表\r\n    async fetchMaterialNameList() {\r\n      try {\r\n        const params = {\r\n          category: parseInt(this.selectedMaterialCategory)\r\n        }\r\n\r\n        const response = await getMaterialNameList(params)\r\n        console.log('fetchMaterialNameList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.materialNameOptions = response.data\r\n          console.log('fetchMaterialNameList - 设置的数据:', this.materialNameOptions)\r\n\r\n          // 设置默认选中PB块，如果存在的话\r\n          const pbMaterial = this.materialNameOptions.find(item => item.itemName === 'PB块')\r\n          if (pbMaterial) {\r\n            this.selectedMaterial = 'PB块'\r\n          } else if (this.materialNameOptions.length > 0) {\r\n            // 如果没有PB块，选择第一个\r\n            this.selectedMaterial = this.materialNameOptions[0].itemName\r\n          }\r\n\r\n          // 获取价格数据\r\n          this.fetchPriceAndStoreData()\r\n        } else {\r\n          console.error('获取物料名称列表失败', response)\r\n          this.materialNameOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料名称列表失败:', error)\r\n        this.materialNameOptions = []\r\n      }\r\n    },\r\n\r\n    // 获取物料价格和采购量数据\r\n    async fetchPriceAndStoreData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemName: this.selectedMaterial\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStore(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n          this.priceAndStoreData = response.data[0] // 取第一个元素\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.priceAndStoreData)\r\n        } else {\r\n          console.error('获取价格和采购量数据失败', response)\r\n          this.priceAndStoreData = null\r\n        }\r\n      } catch (error) {\r\n        console.error('获取价格和采购量数据失败:', error)\r\n        this.priceAndStoreData = null\r\n      }\r\n\r\n      // 数据获取完成后重新初始化价格趋势图\r\n      this.$nextTick(() => {\r\n        this.initPriceTrendChart()\r\n      })\r\n    },\r\n\r\n    // 处理物资类型切换\r\n    async handleMaterialCategoryTypeChange() {\r\n      console.log('物资类型变化:', this.selectedMaterialCategory)\r\n      // 重新获取物料名称列表\r\n      await this.fetchMaterialNameList()\r\n    },\r\n\r\n    // 处理物料选择变化\r\n    async handleMaterialChange() {\r\n      console.log('物料选择变化:', this.selectedMaterial)\r\n      await this.fetchPriceAndStoreData()\r\n      // 不再自动触发AI预测，等用户点击按钮后再触发\r\n    },\r\n\r\n    calculateRealTimeInventoryTotal() {\r\n      let total = 0\r\n      if (this.realTimeInventoryData && this.realTimeInventoryData.length > 0) {\r\n        this.realTimeInventoryData.forEach(item => {\r\n          total += parseFloat(item.totalInventoryAmount) || 0\r\n        })\r\n      }\r\n      return total.toFixed(2)\r\n    },\r\n\r\n    calculateCokingCoalTotal() {\r\n      let total = 0\r\n      if (this.cokingCoalInventoryData && this.cokingCoalInventoryData.length > 0) {\r\n        // 找到所有数据中的最新日期\r\n        let latestDate = ''\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            item.purchaseCokingDailyDetailList.forEach(detail => {\r\n              if (detail.instockDate > latestDate) {\r\n                latestDate = detail.instockDate\r\n              }\r\n            })\r\n          }\r\n        })\r\n\r\n        // 计算最新日期各个物料的库存量合计\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)\r\n            if (latestDetail) {\r\n              total += parseFloat(latestDetail.invQty) || 0\r\n            }\r\n          }\r\n        })\r\n      }\r\n      return (total / 10000).toFixed(2) // 转换为万吨\r\n    },\r\n\r\n    // 处理矿焦煤类型下拉框变化\r\n    async handleCokingCoalTypeChange() {\r\n      console.log('矿焦煤类型变化:', this.selectedCokingCoalType)\r\n      // 重新初始化图表以应用过滤\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 机旁库存相关方法\r\n    // 获取分厂选项列表\r\n    async fetchFactoryDepOptions() {\r\n      try {\r\n        const response = await getDepNameList()\r\n        console.log('fetchFactoryDepOptions - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryDepOptions = response.data\r\n          console.log('fetchFactoryDepOptions - 设置的数据:', this.factoryDepOptions)\r\n        } else {\r\n          console.error('获取分厂选项列表失败', response)\r\n          this.factoryDepOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取分厂选项列表失败:', error)\r\n        this.factoryDepOptions = []\r\n      }\r\n\r\n      // 分厂选项加载完成后，图表会在initAllCharts中初始化\r\n    },\r\n\r\n    // 处理分厂选择变化\r\n    async handleFactoryDepChange() {\r\n      console.log('订单至入库天数 - 分厂选择变化:', this.selectedFactoryDep)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initFactoryStockChart()\r\n    },\r\n\r\n    // 处理物料类型选择变化\r\n    async handleFactoryMaterialTypeChange() {\r\n      console.log('订单至入库天数 - 物料类型选择变化:', this.selectedFactoryMaterialType)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initFactoryStockChart()\r\n    },\r\n\r\n    // 获取机旁库存数据\r\n    async fetchFactoryStockData() {\r\n      try {\r\n        const depName = this.selectedFactoryDep || '' // 空字符串表示全部\r\n        console.log('fetchFactoryStockData - 请求参数:', depName)\r\n\r\n        const response = await getListMonthly(depName)\r\n        console.log('fetchFactoryStockData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryStockData = response.data\r\n          console.log('fetchFactoryStockData - 设置的数据:', this.factoryStockData)\r\n        } else {\r\n          console.error('获取机旁库存数据失败', response)\r\n          this.factoryStockData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取机旁库存数据失败:', error)\r\n        this.factoryStockData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initFactoryStockChart()\r\n      })\r\n    },\r\n\r\n    // 新增方法：处理采购量曲线物料类型变化\r\n    async handlePurchaseAmountCategoriesChange() {\r\n      console.log('采购量曲线物料类型变化:', this.purchaseAmountCategories)\r\n      this.selectedPurchaseAmountMaterials = [] // 重置选中的物料\r\n      await this.fetchPurchaseAmountMaterialList()\r\n    },\r\n\r\n    // 新增方法：处理市场价曲线物料类型变化\r\n    async handleMarketPriceCategoriesChange() {\r\n      console.log('市场价曲线物料类型变化:', this.marketPriceCategories)\r\n      this.selectedMarketPriceMaterials = [] // 重置选中的物料\r\n      await this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 新增方法：获取采购量曲线物料列表\r\n    async fetchPurchaseAmountMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.purchaseAmountCategories,\r\n          curveType: 2, // 采购量曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchPurchaseAmountMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchPurchaseAmountMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.purchaseAmountMaterialOptions = response.data\r\n          console.log('fetchPurchaseAmountMaterialList - 设置的数据:', this.purchaseAmountMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedPurchaseAmountMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.purchaseAmountMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedPurchaseAmountMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 采购量曲线')\r\n\r\n              // 检查市场价曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取采购量曲线物料列表失败', response)\r\n          this.purchaseAmountMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取采购量曲线物料列表失败:', error)\r\n        this.purchaseAmountMaterialOptions = []\r\n      }\r\n    },\r\n\r\n    // 新增方法：获取市场价曲线物料列表\r\n    async fetchMarketPriceMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.marketPriceCategories,\r\n          curveType: 1, // 价格曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchMarketPriceMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchMarketPriceMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.marketPriceMaterialOptions = response.data\r\n          console.log('fetchMarketPriceMaterialList - 设置的数据:', this.marketPriceMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedMarketPriceMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.marketPriceMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedMarketPriceMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 市场价曲线')\r\n\r\n              // 检查采购量曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取市场价曲线物料列表失败', response)\r\n          this.marketPriceMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取市场价曲线物料列表失败:', error)\r\n        this.marketPriceMaterialOptions = []\r\n      }\r\n    },\r\n\r\n\r\n\r\n    // 新增方法：获取物料采购价格数据（用于新的价格趋势图）\r\n    async fetchPriceAndStoreDataForNewChart() {\r\n      if (this.selectedPurchaseAmountMaterials.length === 0 && this.selectedMarketPriceMaterials.length === 0) {\r\n        this.$message.warning('请至少选择一个物料')\r\n        return\r\n      }\r\n\r\n      this.fetchingPriceData = true\r\n      try {\r\n        // 构建itemList\r\n        const itemList = []\r\n\r\n        // 添加采购量曲线的物料\r\n        this.selectedPurchaseAmountMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 2, // 采购量曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        // 添加市场价曲线的物料\r\n        this.selectedMarketPriceMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 1, // 价格曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.newPriceAndStoreData = response.data\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.newPriceAndStoreData)\r\n\r\n          // 重新渲染图表\r\n          this.$nextTick(() => {\r\n            this.initNewPriceTrendChart()\r\n          })\r\n\r\n          // 获取所有选中物料的去重列表\r\n          const allSelectedMaterials = [...new Set([\r\n            ...this.selectedPurchaseAmountMaterials,\r\n            ...this.selectedMarketPriceMaterials\r\n          ])]\r\n\r\n          // 为每个物料调用AI预测接口\r\n          if (allSelectedMaterials.length > 0) {\r\n            this.fetchMultiplePricePredictions(allSelectedMaterials)\r\n          }\r\n\r\n          // 如果市场价曲线有选中物料，获取相似物料信息\r\n          if (this.selectedMarketPriceMaterials.length > 0) {\r\n            this.fetchSimilarMaterials(this.selectedMarketPriceMaterials)\r\n          } else {\r\n            // 清空相似物料数据\r\n            this.similarMaterialsData = []\r\n          }\r\n\r\n          this.$message.success('数据获取成功')\r\n        } else {\r\n          console.error('获取物料采购价格数据失败', response)\r\n          this.$message.error('获取数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料采购价格数据失败:', error)\r\n        this.$message.error('获取数据失败：' + error.message)\r\n      } finally {\r\n        this.fetchingPriceData = false\r\n      }\r\n    },\r\n\r\n    // 获取相似物料信息\r\n    async fetchSimilarMaterials(itemNames) {\r\n      this.similarMaterialsLoading = true\r\n      try {\r\n        const params = {\r\n          itemNames: itemNames\r\n        }\r\n\r\n        console.log('fetchSimilarMaterials - 请求参数:', params)\r\n        const response = await listSimilarByItemNames(params)\r\n        console.log('fetchSimilarMaterials - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.similarMaterialsData = response.data\r\n          console.log('fetchSimilarMaterials - 设置的数据:', this.similarMaterialsData)\r\n        } else {\r\n          console.error('获取相似物料数据失败', response)\r\n          this.similarMaterialsData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取相似物料数据失败:', error)\r\n        this.similarMaterialsData = []\r\n      } finally {\r\n        this.similarMaterialsLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取排名样式类\r\n    getRankClass(rank) {\r\n      if (rank === 1) return 'rank-first'\r\n      if (rank === 2) return 'rank-second'\r\n      if (rank === 3) return 'rank-third'\r\n      return 'rank-default'\r\n    },\r\n\r\n    // 获取商品分类名称\r\n    getCategoryName(category) {\r\n      const categoryMap = {\r\n        1: '矿石',\r\n        2: '煤炭',\r\n        3: '合金',\r\n        4: '废钢'\r\n      }\r\n      return categoryMap[category] || '未知'\r\n    },\r\n\r\n    // 获取价格类型名称\r\n    getPriceTypeName(priceType) {\r\n      const priceTypeMap = {\r\n        1: '现货价',\r\n        2: '市场采购到厂价',\r\n        3: '兴澄废钢收购价(车运)',\r\n        4: '兴澄废钢收购价(船运)',\r\n        5: '沙钢废钢收购价(车运)',\r\n        6: '沙钢废钢收购价(船运)'\r\n      }\r\n      return priceTypeMap[priceType] || '未知'\r\n    },\r\n\r\n    // 打开对比弹框\r\n    openComparisonDialog(item) {\r\n      console.log('openComparisonDialog - 传入的item数据:', item)\r\n      this.currentComparison = { ...item }\r\n      console.log('openComparisonDialog - 设置的currentComparison:', this.currentComparison)\r\n      this.comparisonDialogVisible = true\r\n\r\n      // 弹框打开后获取对比数据\r\n      this.$nextTick(() => {\r\n        this.fetchComparisonData()\r\n      })\r\n    },\r\n\r\n    // 关闭对比弹框\r\n    closeComparisonDialog() {\r\n      this.comparisonDialogVisible = false\r\n      this.currentComparison = {}\r\n      this.comparisonPriceData = null\r\n\r\n      // 清理图表实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n          this.comparisonChartInstance = null\r\n        } catch (err) {\r\n          console.error('清理对比图表实例失败:', err)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 获取对比数据（独立实现，不耦合现有趋势图）\r\n    async fetchComparisonData() {\r\n      this.comparisonChartLoading = true\r\n      try {\r\n        // 构建两个物料的对比请求，只获取价格曲线\r\n        const itemList = [\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.itemName\r\n          },\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.compareItemName\r\n          }\r\n        ]\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchComparisonData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchComparisonData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          // 对返回的数据进行筛选，确保基准物料和相似物料的指定价格类型都能被提取\r\n          const filteredData = []\r\n\r\n          // 获取基准物料和相似物料的目标价格类型名称\r\n          const basePriceTypeName = this.getPriceTypeName(this.currentComparison.priceType)\r\n          const comparePriceTypeName = this.getPriceTypeName(this.currentComparison.comparePriceType)\r\n\r\n          console.log('筛选条件:', {\r\n            baseItemName: this.currentComparison.itemName,\r\n            basePriceTypeName: basePriceTypeName,\r\n            compareItemName: this.currentComparison.compareItemName,\r\n            comparePriceTypeName: comparePriceTypeName\r\n          })\r\n\r\n          response.data.forEach(materialData => {\r\n            const filteredMaterialData = { ...materialData }\r\n\r\n            if (filteredMaterialData.procurementPriceVoList) {\r\n              // 只保留匹配的价格类型\r\n              filteredMaterialData.procurementPriceVoList = filteredMaterialData.procurementPriceVoList.filter(priceGroup => {\r\n                let isMatch = false\r\n                // 基准物料：匹配物料名称和基准价格类型\r\n                if (materialData.itemName === this.currentComparison.itemName) {\r\n                  isMatch = priceGroup.priceName === basePriceTypeName\r\n                  console.log(`基准物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${basePriceTypeName}] 匹配:${isMatch}`)\r\n                }\r\n\r\n                if(isMatch){\r\n                  return isMatch\r\n                }else{\r\n                  if (materialData.itemName === this.currentComparison.compareItemName) {\r\n                    const isMatch = priceGroup.priceName === comparePriceTypeName\r\n                    console.log(`相似物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${comparePriceTypeName}] 匹配:${isMatch}`)\r\n                    return isMatch\r\n                  }\r\n                }\r\n\r\n\r\n                return false\r\n              })\r\n\r\n              console.log(111111111)\r\n              console.log(filteredMaterialData.procurementPriceVoList)\r\n\r\n              // 只有当该物料有匹配的价格类型时才加入结果\r\n              if (filteredMaterialData.procurementPriceVoList.length > 0) {\r\n                filteredData.push(filteredMaterialData)\r\n                console.log(`添加物料[${materialData.itemName}]，包含${filteredMaterialData.procurementPriceVoList.length}个价格组`)\r\n              }\r\n            }\r\n          })\r\n\r\n          this.comparisonPriceData = filteredData\r\n          console.log('fetchComparisonData - 筛选后的数据:', this.comparisonPriceData)\r\n          console.log('筛选结果统计:', {\r\n            totalMaterials: filteredData.length,\r\n            materials: filteredData.map(m => ({\r\n              name: m.itemName,\r\n              priceGroupCount: m.procurementPriceVoList?.length || 0,\r\n              priceGroups: m.procurementPriceVoList?.map(p => p.priceName) || []\r\n            }))\r\n          })\r\n\r\n          // 渲染对比图表\r\n          this.$nextTick(() => {\r\n            this.renderComparisonChart()\r\n          })\r\n        } else {\r\n          console.error('获取对比数据失败', response)\r\n          this.$message.error('获取对比数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取对比数据失败:', error)\r\n        this.$message.error('获取对比数据失败：' + error.message)\r\n      } finally {\r\n        this.comparisonChartLoading = false\r\n      }\r\n    },\r\n\r\n    // 渲染对比图表（独立实现，不耦合现有趋势图）\r\n    renderComparisonChart() {\r\n      const chartDom = document.getElementById('comparisonChart')\r\n      if (!chartDom) {\r\n        console.error('找不到对比图表DOM元素')\r\n        return\r\n      }\r\n\r\n      // 清理现有实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n        } catch (err) {\r\n          console.error('清理现有对比图表实例失败:', err)\r\n        }\r\n      }\r\n\r\n      // 创建新的图表实例\r\n      try {\r\n        this.comparisonChartInstance = echarts.init(chartDom)\r\n      } catch (err) {\r\n        console.error('创建对比图表实例失败:', err)\r\n        return\r\n      }\r\n\r\n      if (!this.comparisonPriceData || this.comparisonPriceData.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      const formatDate = (dateStr) => {\r\n        const year = dateStr.substring(0, 4)\r\n        const month = dateStr.substring(4, 6)\r\n        const day = dateStr.substring(6, 8)\r\n        return `${year}年${month}月${day}日`\r\n      }\r\n\r\n      // 收集所有日期\r\n      let allDates = new Set()\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        if (materialData.procurementPriceVoList) {\r\n          materialData.procurementPriceVoList.forEach(priceGroup => {\r\n            if (priceGroup.priceList) {\r\n              priceGroup.priceList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      allDates = Array.from(allDates).sort()\r\n      const xAxisData = allDates.map(formatDate)\r\n\r\n      if (allDates.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      // 构建系列数据\r\n      const series = []\r\n      const legendData = []\r\n      const colors = ['#8fe9ff', '#ff9f7f', '#5fd8b6', '#ffb980']\r\n      let colorIndex = 0\r\n\r\n      console.log('=== 开始处理对比数据 ===')\r\n      console.log('对比数据总览:', {\r\n        materialCount: this.comparisonPriceData.length,\r\n        baseMaterial: this.currentComparison.itemName,\r\n        compareMaterial: this.currentComparison.compareItemName\r\n      })\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        const materialName = materialData.itemName\r\n        console.log(`\\n处理物料: ${materialName}`)\r\n\r\n        if (materialData.procurementPriceVoList) {\r\n          console.log(`  该物料有 ${materialData.procurementPriceVoList.length} 个价格组`)\r\n          materialData.procurementPriceVoList.forEach((priceGroup, index) => {\r\n            console.log(`  价格组 ${index + 1}: ${priceGroup.priceName}，数据点数量: ${priceGroup.priceList?.length || 0}`)\r\n          })\r\n\r\n          // 数据已经在fetchComparisonData中预先筛选过，这里直接处理所有匹配的价格组\r\n          materialData.procurementPriceVoList.forEach((priceGroup, groupIndex) => {\r\n            const priceData = allDates.map(date => {\r\n              const found = priceGroup.priceList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.price) : null\r\n            })\r\n\r\n            // 统计有效数据点\r\n            const validDataCount = priceData.filter(v => v !== null && v !== undefined).length\r\n            console.log(`    处理价格组[${priceGroup.priceName}]，有效数据点: ${validDataCount}/${priceData.length}`)\r\n\r\n            // 确保每条曲线都有唯一的名称和颜色，即使数据相同\r\n            const uniqueName = `${materialName}-${priceGroup.priceName}`\r\n            const lineColor = colors[colorIndex % colors.length]\r\n\r\n            // 检查是否已经有相同的数据，如果有则添加轻微偏移\r\n            const dataStr = JSON.stringify(priceData)\r\n            const existingSeries = series.find(s => JSON.stringify(s.data) === dataStr)\r\n            let adjustedData = priceData\r\n\r\n            if (existingSeries && priceData.some(v => v !== null)) {\r\n              // 为重复数据添加极小的偏移量（0.01），确保两条线都能显示\r\n              adjustedData = priceData.map(value => value !== null ? value + 0.01 : null)\r\n              console.log(`    检测到重复数据，为 ${uniqueName} 添加偏移`)\r\n            }\r\n\r\n            series.push({\r\n              name: uniqueName,\r\n              type: 'line',\r\n              data: adjustedData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 3,\r\n                color: lineColor,\r\n                // 如果是偏移的数据，使用虚线样式区分\r\n                type: adjustedData !== priceData ? 'dashed' : 'solid'\r\n              },\r\n              itemStyle: {\r\n                color: lineColor\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 6,\r\n              connectNulls: true,\r\n              // 添加z-index确保两条线都能显示\r\n              z: colorIndex + 1\r\n            })\r\n\r\n            legendData.push(uniqueName)\r\n            colorIndex++\r\n            console.log(`    ✓ 添加曲线: ${uniqueName}，颜色: ${lineColor}，有效数据: ${validDataCount}`)\r\n          })\r\n        }\r\n      })\r\n\r\n      console.log(`\\n=== 图表数据处理完成 ===`)\r\n      console.log(`总计添加 ${series.length} 条曲线:`)\r\n      series.forEach((s, i) => {\r\n        const validCount = s.data.filter(v => v !== null && v !== undefined).length\r\n        console.log(`  ${i + 1}. ${s.name} (有效数据: ${validCount})`)\r\n      })\r\n\r\n      // 计算Y轴范围\r\n      let priceMin, priceMax\r\n      const priceValues = series.flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n      if (priceValues.length > 0) {\r\n        priceMin = Math.min(...priceValues)\r\n        priceMax = Math.max(...priceValues)\r\n      }\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          formatter: function(params) {\r\n            let str = params[0].axisValueLabel + '<br/>'\r\n            params.forEach(item => {\r\n              if (item.value !== null && item.value !== undefined) {\r\n                str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`\r\n              } else {\r\n                str += `${item.marker}${item.seriesName}: -<br/>`\r\n              }\r\n            })\r\n            return str\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '12%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: function(index, value) {\r\n              if (index >= allDates.length || !allDates.length) return false\r\n\r\n              const uniqueMonths = new Set()\r\n              allDates.forEach(dateStr => {\r\n                const year = dateStr.substring(0, 4)\r\n                const month = dateStr.substring(4, 6)\r\n                uniqueMonths.add(`${year}${month}`)\r\n              })\r\n\r\n              const monthsCount = uniqueMonths.size\r\n              if (monthsCount <= 1) return true\r\n\r\n              const totalDataPoints = allDates.length\r\n              const idealInterval = Math.floor(totalDataPoints / Math.min(monthsCount, 8))\r\n\r\n              return index % Math.max(idealInterval, 1) === 0\r\n            },\r\n            formatter: function(value, index) {\r\n              if (index >= allDates.length) return ''\r\n              const originalDateStr = allDates[index]\r\n              if (!originalDateStr) return ''\r\n\r\n              const year = originalDateStr.substring(0, 4)\r\n              const month = parseInt(originalDateStr.substring(4, 6))\r\n              return `${year}.${month}`\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '价格（元/吨）',\r\n          min: priceMin,\r\n          max: priceMax,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee'\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: series\r\n      }\r\n\r\n      this.comparisonChartInstance.setOption(option, true)\r\n    },\r\n\r\n    // 检查两个曲线是否都已设置默认值，如果是则触发初始数据获取\r\n    checkAndTriggerInitialDataFetch() {\r\n      // 检查两个曲线是否都已经设置了默认的PB块\r\n      if (this.selectedPurchaseAmountMaterials.includes('PB块') &&\r\n        this.selectedMarketPriceMaterials.includes('PB块') &&\r\n        !this.hasInitializedPriceChart) {\r\n\r\n        this.hasInitializedPriceChart = true // 标记已经初始化过\r\n        console.log('两个曲线都已设置默认值，自动触发数据获取')\r\n\r\n        // 自动触发数据获取\r\n        this.$nextTick(() => {\r\n          this.fetchPriceAndStoreDataForNewChart()\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n  font-family: \"Microsoft YaHei\", sans-serif;\r\n}\r\n\r\n.dashboard-container {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  color: #fff;\r\n  overflow-x: hidden;\r\n  padding: 10px;\r\n}\r\n\r\n.dashboard-header {\r\n  text-align: center;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n  padding: 5px 0;\r\n}\r\n\r\n.dashboard-header h1 {\r\n  font-size: 24px;\r\n  position: relative;\r\n  display: inline-block;\r\n  padding: 5px 40px;\r\n}\r\n\r\n.dashboard-header::before,\r\n.dashboard-header::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 50%;\r\n  width: 30%;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.dashboard-header::before {\r\n  left: 0;\r\n}\r\n\r\n.dashboard-header::after {\r\n  right: 0;\r\n}\r\n\r\n.dashboard-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(12, 1fr);\r\n  grid-template-rows: auto auto auto auto auto auto;\r\n  gap: 10px;\r\n  min-height: calc(100vh - 80px);\r\n}\r\n\r\n.card {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 5px;\r\n  padding: 10px;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.card-title {\r\n  font-size: 14px;\r\n  margin-bottom: 5px;\r\n  font-weight: normal;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.chart-filter-dropdown-container {\r\n  z-index: 10;\r\n}\r\n\r\n.chart-filter-dropdown-container select {\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n  color: #fff;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  font-size: 12px;\r\n}\r\n\r\n.chart {\r\n  width: 100%;\r\n  height: calc(100% - 20px);\r\n  min-height: 200px;\r\n  flex: 1;\r\n}\r\n\r\n.stat-cards {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  height: 100%;\r\n  align-items: center;\r\n}\r\n\r\n.stat-card {\r\n  text-align: center;\r\n  flex-grow: 1;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 34px;\r\n  font-weight: bold;\r\n  color: #00ffff;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 18px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.chart-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: rgba(255,255,255,0.5);\r\n  font-size: 14px;\r\n}\r\n\r\n.material-chart-card {\r\n  height: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.material-chart-card .chart {\r\n  flex-grow: 1;\r\n  height: 250px;\r\n  min-height: 250px;\r\n}\r\n\r\n.time-filter {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.time-filter-btn {\r\n  padding: 6px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n}\r\n\r\n.time-filter-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.time-filter-btn.active {\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n  font-weight: 500;\r\n}\r\n\r\n.header-controls {\r\n  position: absolute;\r\n  right: 20px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  z-index: 1000;\r\n}\r\n\r\n.fullscreen-btn {\r\n  padding: 8px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 40px;\r\n  height: 32px;\r\n}\r\n\r\n.fullscreen-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n}\r\n\r\n/* AI价格预测区域样式 */\r\n.price-prediction-section {\r\n  margin-top: 15px;\r\n  margin-bottom: 15px;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-size: 13px;\r\n}\r\n\r\n.prediction-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.model-info {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 12px;\r\n}\r\n\r\n.prediction-content {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  border-left: 3px solid #00ffff;\r\n  position: relative;\r\n}\r\n\r\n.prediction-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n\r\n\r\n/* 多个预测结果的样式 */\r\n.predictions-container {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding-right: 5px;\r\n}\r\n\r\n.prediction-item {\r\n  margin-bottom: 15px;\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n  overflow: hidden;\r\n}\r\n\r\n.prediction-item.prediction-error {\r\n  border-left-color: #ff6b6b;\r\n}\r\n\r\n.prediction-material-title {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  padding: 8px 12px;\r\n  font-size: 13px;\r\n  font-weight: 600;\r\n  color: #00ffff;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-item.prediction-error .prediction-material-title {\r\n  background-color: rgba(255, 107, 107, 0.1);\r\n  color: #ff6b6b;\r\n  border-bottom-color: rgba(255, 107, 107, 0.2);\r\n}\r\n\r\n.prediction-material-title i {\r\n  margin-right: 6px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n/* 预测容器滚动条样式 */\r\n.predictions-container::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 一问一答样式 */\r\n.qa-section {\r\n  padding: 0;\r\n}\r\n\r\n.question-section, .answer-section {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.answer-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.qa-label {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  font-weight: 600;\r\n  font-size: 13px;\r\n}\r\n\r\n.question-label {\r\n  color: #ffb980;\r\n}\r\n\r\n.answer-label {\r\n  color: #00ffff;\r\n}\r\n\r\n.qa-label i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n.question-text, .answer-text {\r\n  background-color: rgba(0, 0, 0, 0.3);\r\n  border-radius: 8px;\r\n  padding: 12px 15px;\r\n  line-height: 1.6;\r\n  font-size: 13px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  white-space: pre-wrap;\r\n  word-wrap: break-word;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.question-text {\r\n  border-left: 3px solid #ffb980;\r\n}\r\n\r\n.answer-text {\r\n  border-left: 3px solid #00ffff;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding-right: 18px;\r\n}\r\n\r\n/* 问题文本样式 */\r\n.question-text {\r\n  font-style: italic;\r\n  color: rgba(255, 200, 150, 0.9);\r\n}\r\n\r\n/* 答案文本滚动条样式 */\r\n.answer-text::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 价格趋势卡片特殊样式 */\r\n.price-trend-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: auto;\r\n  min-height: 400px;\r\n}\r\n\r\n.price-trend-card .chart {\r\n  flex-shrink: 0;\r\n  height: 300px !important;\r\n  min-height: 300px;\r\n}\r\n\r\n.price-trend-card .price-prediction-section {\r\n  flex-shrink: 0;\r\n  margin-top: 15px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.inventory-total {\r\n  font-size: 12px;\r\n  color: #00ffff;\r\n  font-weight: normal;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 新增：价格趋势图控件样式 */\r\n.price-trend-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: rgba(16, 7, 33, 0.4);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.left-controls, .right-controls {\r\n  flex: 1;\r\n  max-width: 45%;\r\n}\r\n\r\n.curve-label {\r\n  font-size: 13px;\r\n  color: #00ffff;\r\n  margin-bottom: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n.left-controls .curve-label {\r\n  text-align: left;\r\n}\r\n\r\n.right-controls .curve-label {\r\n  text-align: right;\r\n}\r\n\r\n.dropdown-row {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.right-controls .dropdown-row {\r\n  justify-content: flex-end;\r\n}\r\n\r\n.fetch-data-btn-container {\r\n  text-align: right;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.modern-fetch-btn {\r\n  background: rgba(138, 43, 226, 0.7);\r\n  border: none;\r\n  border-radius: 12px;\r\n  padding: 10px 20px;\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 3px 10px rgba(138, 43, 226, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-fetch-btn:hover:not(:disabled) {\r\n  background: rgba(138, 43, 226, 0.9);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.5);\r\n}\r\n\r\n.modern-fetch-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.modern-fetch-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.modern-fetch-btn.loading {\r\n  background: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.modern-fetch-btn i {\r\n  margin-right: 8px;\r\n  animation: rotate 1s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n/* Element UI 下拉框样式覆盖 */\r\n.price-trend-controls .el-select {\r\n  background-color: transparent !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner {\r\n  background-color: #4a1c5a !important;\r\n  border: 1px solid rgba(116, 75, 162, 0.5) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 8px !important;\r\n  font-size: 13px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:hover {\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  box-shadow: 0 0 8px rgba(116, 75, 162, 0.3) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:focus {\r\n  border-color: #764ba2 !important;\r\n  box-shadow: 0 0 12px rgba(116, 75, 162, 0.5) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner::placeholder {\r\n  color: rgba(255, 255, 255, 0.7) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix i {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag {\r\n  background-color: rgba(116, 75, 162, 0.6) !important;\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 6px !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close:hover {\r\n  background-color: rgba(255, 255, 255, 0.2) !important;\r\n}\r\n\r\n/* 相似物料区域样式 */\r\n.similar-materials-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.similar-materials-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  font-size: 14px;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.similar-materials-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.section-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n.similar-materials-container {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 10px;\r\n  overflow-x: auto;\r\n}\r\n\r\n.materials-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  font-size: 13px;\r\n}\r\n\r\n.materials-table th {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  color: #00ffff;\r\n  padding: 8px 12px;\r\n  text-align: left;\r\n  border-bottom: 2px solid rgba(0, 212, 255, 0.3);\r\n  font-weight: 600;\r\n}\r\n\r\n.materials-table td {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.material-row {\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.material-row:hover {\r\n  background-color: rgba(0, 212, 255, 0.05);\r\n}\r\n\r\n.rank-cell {\r\n  text-align: center;\r\n  width: 60px;\r\n}\r\n\r\n.rank-badge {\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 50%;\r\n  color: #fff;\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n  min-width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.rank-first {\r\n  background: linear-gradient(135deg, #ffd700, #ffb347);\r\n  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.rank-second {\r\n  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);\r\n  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);\r\n}\r\n\r\n.rank-third {\r\n  background: linear-gradient(135deg, #cd7f32, #b8860b);\r\n  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);\r\n}\r\n\r\n.rank-default {\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.material-name, .compare-material-name {\r\n  font-weight: 500;\r\n  color: #fff;\r\n}\r\n\r\n.compare-material-name {\r\n  color: #00ffff;\r\n}\r\n\r\n.score-cell {\r\n  text-align: center;\r\n  width: 120px;\r\n  min-width: 120px;\r\n}\r\n\r\n.score-value {\r\n  display: inline-block;\r\n  padding: 2px 6px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 4px;\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n}\r\n\r\n.score-desc {\r\n  color: #ffb980;\r\n  font-style: italic;\r\n}\r\n\r\n.category-cell {\r\n  color: #5fd8b6;\r\n  font-weight: 500;\r\n}\r\n\r\n.similar-materials-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n.similar-materials-group {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.similar-materials-group:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.group-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n}\r\n\r\n.price-type-cell {\r\n  color: #e879ed;\r\n  font-size: 11px;\r\n  max-width: 120px;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.algorithm-desc {\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 11px;\r\n  font-style: italic;\r\n  margin-left: 8px;\r\n}\r\n\r\n.action-cell {\r\n  text-align: center;\r\n  width: 100px;\r\n}\r\n\r\n.view-comparison-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  color: #fff;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  white-space: nowrap;\r\n  min-width: 70px;\r\n}\r\n\r\n.view-comparison-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\r\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\r\n}\r\n\r\n.view-comparison-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.view-comparison-btn i {\r\n  font-size: 13px;\r\n}\r\n\r\n/* 对比弹框样式 */\r\n.comparison-dialog .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__header {\r\n  background: linear-gradient(135deg, rgba(33, 10, 56, 0.9), rgba(0, 212, 255, 0.2));\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__title {\r\n  color: #00ffff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close {\r\n  color: #00ffff;\r\n  font-size: 20px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close:hover {\r\n  color: #fff;\r\n  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n.comparison-dialog .el-dialog__body {\r\n  padding: 0;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-content {\r\n  padding: 20px;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.comparison-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 15px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.base-material {\r\n  color: #00ffff;\r\n  padding: 8px 16px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.vs-text {\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.compare-material {\r\n  color: #ea7ccc;\r\n  padding: 8px 16px;\r\n  background-color: rgba(234, 124, 204, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(234, 124, 204, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.similarity-info {\r\n  color: #ffb980;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  padding: 4px 12px;\r\n  background-color: rgba(255, 185, 128, 0.2);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 185, 128, 0.4);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-chart-container {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  overflow: hidden;\r\n  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.comparison-chart {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkJA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,qBAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,kBAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AACA,IAAAO,sBAAA,GAAAP,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAQ,IAAA;EACAC,MAAA,GAAAC,qBAAA,EAAAC,6BAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,IAAA;IACA,OAAAA,IAAA;MAGA;MACAC,aAAA;MACAC,aAAA;MAEA;MACAC,mBAAA;MAEA;MACAC,0BAAA;MACAC,iBAAA;MAAA;;MAEA;MACAC,cAAA;MAEA;MACAC,wBAAA;MAEA;MACAC,uBAAA;MAAA;MACAC,yBAAA;MAAA;MACAC,kBAAA;MAAA;;MAEA;MACAC,4BAAA;MAAA;MACAC,8BAAA;MAAA;;MAEA;MACAC,0BAAA;MAAA;MACAC,4BAAA;MAAA;MACAC,qBAAA;MACAC,uBAAA;MAEA;MACAC,sBAAA;MAAA;;MAEA;MACAC,wBAAA;MACAC,oBAAA;MACAC,mBAAA;MACAC,sBAAA;MAEA;MACAC,gBAAA;MACAC,gBAAA;MACAC,yBAAA;MAEA;MACAC,gBAAA;MAEA;MACAC,gBAAA;MAAA;MACAC,iBAAA;MAEA;MACAC,mBAAA;MACAC,gBAAA;IAAA,OAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA/B,IAAA,8BACA,2BACA,mCAIA,0CACA,sCACA,8BAGA,uCACA,mCACA,0BAGA,gCACA,WAAA8B,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA/B,IAAA,8BAGA,gCAGA,gCACA,mCAGA,kCACA,6BACA,gCACA,8BACA,6BAGA,oCACA,SAAA8B,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAA/B,IAAA,uBACA,yBACA,kCAGA,oBACA;EAEA;EAEAgC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAC,gBAAA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,OAAA;MACA,KAAAC,oBAAA,CAAAC,OAAA,WAAAC,IAAA;QACA;QACA,IAAAC,QAAA,MAAAC,MAAA,CAAAF,IAAA,CAAAG,QAAA,OAAAD,MAAA,CAAAF,IAAA,CAAAI,QAAA,OAAAF,MAAA,CAAAF,IAAA,CAAAK,SAAA;QACA,IAAAC,UAAA,MAAAJ,MAAA,CAAAF,IAAA,CAAAG,QAAA,QAAAD,MAAA,CAAAN,KAAA,CAAAW,eAAA,CAAAP,IAAA,CAAAI,QAAA,UAAAF,MAAA,CAAAN,KAAA,CAAAY,gBAAA,CAAAR,IAAA,CAAAK,SAAA;QAEA,KAAAR,OAAA,CAAAS,UAAA;UACAT,OAAA,CAAAS,UAAA;YACAL,QAAA,EAAAA,QAAA;YACAQ,KAAA;UACA;QACA;QACAZ,OAAA,CAAAS,UAAA,EAAAG,KAAA,CAAAC,IAAA,CAAAV,IAAA;MACA;;MAEA;MACAW,MAAA,CAAAC,IAAA,CAAAf,OAAA,EAAAE,OAAA,WAAAc,GAAA;QACAhB,OAAA,CAAAgB,GAAA,EAAAJ,KAAA,CAAAK,IAAA,WAAAC,CAAA,EAAAC,CAAA;UAAA,OAAAD,CAAA,CAAAE,IAAA,GAAAD,CAAA,CAAAC,IAAA;QAAA;MACA;MAEA,OAAApB,OAAA;IACA;EACA;EAEAqB,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,KAAAC,wBAAA;IACA,KAAAC,kBAAA;IAEA,KAAAC,0BAAA;IACA,KAAAC,4BAAA;IACA;IACA,KAAAC,yBAAA,GAAAC,IAAA;MACAN,MAAA,CAAAO,2BAAA;IACA;IACA;IACA,KAAAC,8BAAA;IACA;IACA,KAAAC,qBAAA;;IAEA;IACA,KAAAC,+BAAA;IACA,KAAAC,4BAAA;;IAEA;IACA,KAAAC,sBAAA;;IAEA;IACA,KAAAC,uBAAA;;IAEA;IACA,KAAAC,gBAAA;IAEA,KAAAC,mBAAA;IACA,KAAAC,sBAAA;;IAEA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,eAAA;EACA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACA,KAAAC,iBAAA;IACA,KAAAC,wBAAA;IACAL,MAAA,CAAAM,mBAAA,gBAAAJ,eAAA;;IAEA;IACA,KAAA/C,MAAA,CAAAoD,QAAA;EACA;EAEAC,OAAA;IACA;IACAT,sBAAA,WAAAA,uBAAA;MACA,IAAAU,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAE,EAAA,gBAAAC,sBAAA;MACA;IACA;IAEA;IACAP,wBAAA,WAAAA,yBAAA;MACA,IAAAI,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAI,GAAA,gBAAAD,sBAAA;MACA;IACA;IAEA;IACAA,sBAAA,WAAAA,uBAAA;MAAA,IAAAE,MAAA;MACA,IAAAL,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACA,IAAAxD,YAAA,GAAAuD,mBAAA,CAAAvD,YAAA;QACA,KAAAC,MAAA,CAAAoD,QAAA,0BAAArD,YAAA;;QAEA;QACA,KAAA6D,SAAA;UACAC,UAAA;YACAF,MAAA,CAAAZ,eAAA;UACA;QACA;MACA;IACA;IAEA;IACAe,gBAAA,WAAAA,iBAAAC,aAAA;MAAA,WAAAC,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAC,QAAA;QAAA,WAAAF,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OACA,IAAAC,2BAAA;gBAAAR,aAAA,EAAAA;cAAA;YAAA;cAAA,OAAAM,QAAA,CAAA7C,CAAA,IAAA6C,QAAA,CAAAG,CAAA;UAAA;QAAA,GAAAL,OAAA;MAAA;IACA;IAEAM,eAAA,WAAAA,gBAAAC,QAAA;MAAA,WAAAV,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAS,SAAA;QAAA,WAAAV,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAQ,SAAA;UAAA,kBAAAA,SAAA,CAAAN,CAAA;YAAA;cAAAM,SAAA,CAAAN,CAAA;cAAA,OACA,IAAAO,mCAAA;gBAAAH,QAAA,EAAAA;cAAA;YAAA;cAAA,OAAAE,SAAA,CAAApD,CAAA,IAAAoD,SAAA,CAAAJ,CAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IAEAG,eAAA,WAAAA,gBAAAC,MAAA;MAAA,WAAAf,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAc,SAAA;QAAA,WAAAf,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAa,SAAA;UAAA,kBAAAA,SAAA,CAAAX,CAAA;YAAA;cAAAW,SAAA,CAAAX,CAAA;cAAA,OACA,IAAAY,mCAAA,EAAAH,MAAA;YAAA;cAAA,OAAAE,SAAA,CAAAzD,CAAA,IAAAyD,SAAA,CAAAT,CAAA;UAAA;QAAA,GAAAQ,QAAA;MAAA;IACA;IAEAG,eAAA,WAAAA,gBAAAJ,MAAA;MAAA,WAAAf,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAkB,SAAA;QAAA,WAAAnB,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAiB,SAAA;UAAA,kBAAAA,SAAA,CAAAf,CAAA;YAAA;cAAAe,SAAA,CAAAf,CAAA;cAAA,OACA,IAAAgB,+BAAA,EAAAP,MAAA;YAAA;cAAA,OAAAM,SAAA,CAAA7D,CAAA,IAAA6D,SAAA,CAAAb,CAAA;UAAA;QAAA,GAAAY,QAAA;MAAA;IACA;IAEAG,eAAA,WAAAA,gBAAAR,MAAA;MAAA,WAAAf,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAsB,SAAA;QAAA,WAAAvB,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAqB,SAAA;UAAA,kBAAAA,SAAA,CAAAnB,CAAA;YAAA;cAAAmB,SAAA,CAAAnB,CAAA;cAAA,OACA,IAAAoB,mCAAA,EAAAX,MAAA;YAAA;cAAA,OAAAU,SAAA,CAAAjE,CAAA,IAAAiE,SAAA,CAAAjB,CAAA;UAAA;QAAA,GAAAgB,QAAA;MAAA;IACA;IAEAG,iBAAA,WAAAA,kBAAA;MAAA,WAAA3B,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA0B,SAAA;QAAA,WAAA3B,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAyB,SAAA;UAAA,kBAAAA,SAAA,CAAAvB,CAAA;YAAA;cAAAuB,SAAA,CAAAvB,CAAA;cAAA,OACA,IAAAwB,qCAAA;YAAA;cAAA,OAAAD,SAAA,CAAArE,CAAA,IAAAqE,SAAA,CAAArB,CAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IACA;IAEAG,mBAAA,WAAAA,oBAAA;MAAA,WAAA/B,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA8B,SAAA;QAAA,WAAA/B,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA6B,SAAA;UAAA,kBAAAA,SAAA,CAAA3B,CAAA;YAAA;cAAA2B,SAAA,CAAA3B,CAAA;cAAA,OACA,IAAA4B,uCAAA;YAAA;cAAA,OAAAD,SAAA,CAAAzE,CAAA,IAAAyE,SAAA,CAAAzB,CAAA;UAAA;QAAA,GAAAwB,QAAA;MAAA;IACA;IAEAG,gBAAA,WAAAA,iBAAApB,MAAA;MAAA,WAAAf,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAkC,SAAA;QAAA,WAAAnC,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAiC,SAAA;UAAA,kBAAAA,SAAA,CAAA/B,CAAA;YAAA;cAAA+B,SAAA,CAAA/B,CAAA;cAAA,OACA,IAAAgC,oCAAA,EAAAvB,MAAA;YAAA;cAAA,OAAAsB,SAAA,CAAA7E,CAAA,IAAA6E,SAAA,CAAA7B,CAAA;UAAA;QAAA,GAAA4B,QAAA;MAAA;IACA;IAEAG,4BAAA,WAAAA,6BAAAxB,MAAA;MAAA,WAAAf,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAsC,SAAA;QAAA,WAAAvC,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAqC,SAAA;UAAA,kBAAAA,SAAA,CAAAnC,CAAA;YAAA;cAAAmC,SAAA,CAAAnC,CAAA;cAAA,OACA,IAAAoC,gDAAA,EAAA3B,MAAA;YAAA;cAAA,OAAA0B,SAAA,CAAAjF,CAAA,IAAAiF,SAAA,CAAAjC,CAAA;UAAA;QAAA,GAAAgC,QAAA;MAAA;IACA;IAEAG,mBAAA,WAAAA,oBAAA5B,MAAA;MAAA,WAAAf,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA0C,SAAA;QAAA,WAAA3C,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAyC,SAAA;UAAA,kBAAAA,SAAA,CAAAvC,CAAA;YAAA;cAAAuC,SAAA,CAAAvC,CAAA;cAAA,OACA,IAAAwC,uCAAA,EAAA/B,MAAA;YAAA;cAAA,OAAA8B,SAAA,CAAArF,CAAA,IAAAqF,SAAA,CAAArC,CAAA;UAAA;QAAA,GAAAoC,QAAA;MAAA;IACA;IAEA;IACAG,0BAAA,WAAAA,2BAAAhD,aAAA;MACA,QAAAA,aAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;MACA;IACA;IAEA;IACAlC,wBAAA,WAAAA,yBAAA;MACA,KAAA9E,OAAA;QACAiK,OAAA,CAAAC,KAAA;QACAC,QAAA,CAAAC,gBAAA,WAAA3G,OAAA,WAAA4G,EAAA;UACAA,EAAA,CAAAC,SAAA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAvF,kBAAA,WAAAA,mBAAA;MAAA,IAAAwF,UAAA,GAAAC,SAAA;QAAAC,MAAA;MAAA,WAAAxD,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAuD,SAAA;QAAA,IAAAC,kBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,EAAA;QAAA,WAAA9D,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA4D,SAAA;UAAA,kBAAAA,SAAA,CAAA1D,CAAA;YAAA;cAAAoD,kBAAA,GAAAJ,UAAA,CAAAW,MAAA,QAAAX,UAAA,QAAAY,SAAA,GAAAZ,UAAA;cACAE,MAAA,CAAAW,oBAAA,GAAAT,kBAAA;;cAEA;cACAF,MAAA,CAAAvE,iBAAA;cAAA+E,SAAA,CAAAI,CAAA;cAAAJ,SAAA,CAAA1D,CAAA;cAAA,OAIA+D,OAAA,CAAAC,GAAA,EACAd,MAAA,CAAA1D,gBAAA,CAAA4D,kBAAA,GACAF,MAAA,CAAArB,gBAAA;gBAAApC,aAAA,EAAA2D;cAAA,GACA;YAAA;cAAAC,kBAAA,GAAAK,SAAA,CAAAxD,CAAA;cAAAoD,mBAAA,OAAAW,eAAA,CAAA1I,OAAA,EAAA8H,kBAAA;cAHAE,iBAAA,GAAAD,mBAAA;cAAAE,qBAAA,GAAAF,mBAAA;cAKA;cACA,IAAAC,iBAAA,IAAAA,iBAAA,CAAAhK,IAAA;gBACA2J,MAAA,CAAAzJ,aAAA,GAAA8J,iBAAA,CAAAhK,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,eAAAhB,MAAA,CAAAzJ,aAAA;cACA;gBACAiJ,OAAA,CAAAC,KAAA,wBAAAY,iBAAA;gBACAL,MAAA,CAAAiB,gBAAA;cACA;;cAEA;cACA,IAAAX,qBAAA,IAAAA,qBAAA,CAAAjK,IAAA;gBACA2J,MAAA,CAAAxJ,aAAA,GAAA8J,qBAAA,CAAAjK,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,gBAAAhB,MAAA,CAAAxJ,aAAA;cACA;gBACAgJ,OAAA,CAAAC,KAAA,eAAAa,qBAAA;gBACAN,MAAA,CAAAxJ,aAAA;cACA;cAEAwJ,MAAA,CAAAkB,aAAA;cAAAV,SAAA,CAAA1D,CAAA;cAAA;YAAA;cAAA0D,SAAA,CAAAI,CAAA;cAAAL,EAAA,GAAAC,SAAA,CAAAxD,CAAA;cAEAwC,OAAA,CAAAC,KAAA,iBAAAc,EAAA;cACAP,MAAA,CAAAiB,gBAAA,cAAAV,EAAA,CAAAY,OAAA;YAAA;cAAA,OAAAX,SAAA,CAAAxG,CAAA;UAAA;QAAA,GAAAiG,QAAA;MAAA;IAEA;IAEA;IACAgB,gBAAA,WAAAA,iBAAAE,OAAA;MACAzB,QAAA,CAAAC,gBAAA,WAAA3G,OAAA,WAAAoI,KAAA;QACAA,KAAA,CAAAvB,SAAA,uCAAA1G,MAAA,CAAAgI,OAAA;MACA;IACA;IAEA;IACAE,sBAAA,WAAAA,uBAAAC,QAAA,EAAA/E,aAAA;MACA,KAAAgF,YAAA,GAAAD,QAAA;MACA,KAAAX,oBAAA,GAAApE,aAAA;MACAiD,OAAA,CAAAwB,GAAA,aAAAM,QAAA,SAAA/E,aAAA;MAEA,KAAAd,iBAAA;MACA,KAAAnB,kBAAA,CAAAiC,aAAA;MACA;MACA,KAAA3B,8BAAA;MACA;MACA,KAAAC,qBAAA;MACA;MACA,KAAAF,2BAAA;MACA;;MAEA;MACA,KAAAG,+BAAA;MACA,KAAAC,4BAAA;IACA;IAEA;IACAU,iBAAA,WAAAA,kBAAA;MACA7B,MAAA,CAAA4H,MAAA,MAAA5K,cAAA,EAAAoC,OAAA,WAAAyI,QAAA;QACA,IAAAA,QAAA,IAAAA,QAAA,CAAAC,UAAA;UACAC,aAAA,CAAAF,QAAA,CAAAC,UAAA;UACAD,QAAA,CAAAC,UAAA;QACA;MACA;IACA;IAEA;IACAnG,eAAA,WAAAA,gBAAA;MACA3B,MAAA,CAAA4H,MAAA,MAAA5K,cAAA,EAAAoC,OAAA,WAAAyI,QAAA;QACA,IAAAA,QAAA;UACA;YACAA,QAAA,CAAAG,MAAA;UACA,SAAAC,GAAA;YACArC,OAAA,CAAAC,KAAA,cAAAoC,GAAA;UACA;QACA;MACA;IACA;IAEA;IACAX,aAAA,WAAAA,cAAA;MACA,IAAAY,SAAA,OAAAC,IAAA,GAAAC,kBAAA;MACAxC,OAAA,CAAAwB,GAAA,2BAAA7H,MAAA,CAAA2I,SAAA;MACA;QACA;QACA;QACA;QACA;;QAEA;QACA,KAAAG,qBAAA;QACA,KAAAC,uBAAA;QACA,KAAAC,2BAAA;;QAEA;QACA,KAAAC,iBAAA;;QAEA;QACA,KAAAC,qBAAA;QACA,KAAAC,oBAAA;;QAEA;;QAEA;;QAEA;;QAEA9C,OAAA,CAAAwB,GAAA;MACA,SAAAa,GAAA;QACArC,OAAA,CAAAC,KAAA,gBAAAoC,GAAA;QACA,KAAAZ,gBAAA,eAAAY,GAAA,CAAAV,OAAA;MACA;IACA;IAEA;IACAmB,oBAAA,WAAAA,qBAAAC,eAAA,EAAArF,QAAA,EAAAsF,gBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjG,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAgG,UAAA;QAAA,IAAAC,QAAA,EAAAC,GAAA;QAAA,WAAAnG,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAiG,UAAA;UAAA,kBAAAA,UAAA,CAAA/F,CAAA;YAAA;cAAA+F,UAAA,CAAAjC,CAAA;cAAAiC,UAAA,CAAA/F,CAAA;cAAA,OAEA2F,MAAA,CAAAxF,eAAA,CAAAC,QAAA;YAAA;cAAAyF,QAAA,GAAAE,UAAA,CAAA7F,CAAA;cAEA,IAAA2F,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAoM,MAAA,CAAAD,gBAAA,IAAAG,QAAA,CAAAtM,IAAA;cACA;gBACAmJ,OAAA,CAAAC,KAAA,2DAAAtG,MAAA,CAAA+D,QAAA,QAAAyF,QAAA;gBACAF,MAAA,CAAAD,gBAAA;cACA;cAAAK,UAAA,CAAA/F,CAAA;cAAA;YAAA;cAAA+F,UAAA,CAAAjC,CAAA;cAAAgC,GAAA,GAAAC,UAAA,CAAA7F,CAAA;cAEAwC,OAAA,CAAAC,KAAA,kCAAAtG,MAAA,CAAAoJ,eAAA,QAAAK,GAAA;cACAH,MAAA,CAAAD,gBAAA;YAAA;cAAA,OAAAK,UAAA,CAAA7I,CAAA;UAAA;QAAA,GAAA0I,SAAA;MAAA;IAEA;IAEA;IACAM,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,MAAA;MAAA,WAAAzG,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAwG,UAAA;QAAA,WAAAzG,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAuG,UAAA;UAAA,kBAAAA,UAAA,CAAArG,CAAA;YAAA;cAAAqG,UAAA,CAAArG,CAAA;cAAA,OACAmG,MAAA,CAAAG,wBAAA;YAAA;cAAA,OAAAD,UAAA,CAAAnJ,CAAA;UAAA;QAAA,GAAAkJ,SAAA;MAAA;IACA;IAEAG,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MAAA,WAAA9G,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA6G,UAAA;QAAA,WAAA9G,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA4G,UAAA;UAAA,kBAAAA,UAAA,CAAA1G,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,YAAAsC,MAAA,CAAA3M,iBAAA;cAAA6M,UAAA,CAAA1G,CAAA;cAAA,OACAwG,MAAA,CAAAF,wBAAA;YAAA;cAAA,OAAAI,UAAA,CAAAxJ,CAAA;UAAA;QAAA,GAAAuJ,SAAA;MAAA;IACA;IAEAH,wBAAA,WAAAA,yBAAA;MAAA,IAAAK,MAAA;MAAA,WAAAjH,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAgH,UAAA;QAAA,IAAAC,OAAA,EAAAhB,QAAA,EAAAiB,eAAA,EAAAC,SAAA,EAAAC,gBAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,WAAAvH,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAqH,UAAA;UAAA,kBAAAA,UAAA,CAAAnH,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,0CAAA7H,MAAA,CAAAsK,MAAA,CAAA/M,0BAAA,mBAAAyC,MAAA,CAAAsK,MAAA,CAAA9M,iBAAA;cACAgN,OAAA,GAAAF,MAAA,CAAA7M,cAAA,CAAAsN,iBAAA;cAAA,IACAP,OAAA;gBAAAM,UAAA,CAAAnH,CAAA;gBAAA;cAAA;cACA0C,OAAA,CAAAC,KAAA;cAAA,OAAAwE,UAAA,CAAAjK,CAAA;YAAA;cAIA,IAAA2J,OAAA,CAAAjC,UAAA;gBACAC,aAAA,CAAAgC,OAAA,CAAAjC,UAAA;gBACAiC,OAAA,CAAAjC,UAAA;cACA;cAAA,MAEA,CAAA+B,MAAA,CAAA/M,0BAAA,IAAA+M,MAAA,CAAA/M,0BAAA;gBAAAuN,UAAA,CAAAnH,CAAA;gBAAA;cAAA;cACA;cACA6G,OAAA,CAAAQ,WAAA;cAAAF,UAAA,CAAArD,CAAA;cAAAqD,UAAA,CAAAnH,CAAA;cAAA,OAEA2G,MAAA,CAAA9F,eAAA;gBACApB,aAAA,EAAAkH,MAAA,CAAA9C,oBAAA;gBACAyD,SAAA,EAAAX,MAAA,CAAA9M;cACA;YAAA;cAHAgM,QAAA,GAAAsB,UAAA,CAAAjH,CAAA;cAKA4G,eAAA;cACA,IAAAjB,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAuN,eAAA,GAAAjB,QAAA,CAAAtM,IAAA;cACA;gBACAmJ,OAAA,CAAAC,KAAA,8BAAAkD,QAAA;gBACAiB,eAAA,GAAAH,MAAA,CAAA5M,wBAAA;cACA;cACA4M,MAAA,CAAAY,6BAAA,CAAAV,OAAA,EAAAC,eAAA;cAAAK,UAAA,CAAAnH,CAAA;cAAA;YAAA;cAAAmH,UAAA,CAAArD,CAAA;cAAAmD,GAAA,GAAAE,UAAA,CAAAjH,CAAA;cAEAwC,OAAA,CAAAC,KAAA,mFAAAsE,GAAA;cACAN,MAAA,CAAAY,6BAAA,CAAAV,OAAA,EAAAF,MAAA,CAAA5M,wBAAA;YAAA;cAAAoN,UAAA,CAAArD,CAAA;cAEA+C,OAAA,CAAAW,WAAA;cAAA,OAAAL,UAAA,CAAAM,CAAA;YAAA;cAAAN,UAAA,CAAAnH,CAAA;cAAA;YAAA;cAGA6G,OAAA,CAAAQ,WAAA;cAAAF,UAAA,CAAArD,CAAA;cAAAqD,UAAA,CAAAnH,CAAA;cAAA,OAEA2G,MAAA,CAAA9F,eAAA;gBACApB,aAAA,EAAAkH,MAAA,CAAA9C,oBAAA;gBACA6D,MAAA,EAAAf,MAAA,CAAA/M,0BAAA;gBACA0N,SAAA,EAAAX,MAAA,CAAA9M;cACA;YAAA;cAJAgM,SAAA,GAAAsB,UAAA,CAAAjH,CAAA;cAMA4G,gBAAA;cACA,IAAAjB,SAAA,IAAAA,SAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,SAAA,CAAAtM,IAAA;gBACAuN,gBAAA,GAAAjB,SAAA,CAAAtM,IAAA;cACA;gBACAmJ,OAAA,CAAAC,KAAA,8BAAAkD,SAAA;cACA;cACAc,MAAA,CAAAY,6BAAA,CAAAV,OAAA,EAAAC,gBAAA;cAAAK,UAAA,CAAAnH,CAAA;cAAA;YAAA;cAAAmH,UAAA,CAAArD,CAAA;cAAAoD,GAAA,GAAAC,UAAA,CAAAjH,CAAA;cAEAwC,OAAA,CAAAC,KAAA,mFAAAuE,GAAA;cACAtE,QAAA,CAAA+E,cAAA,sBAAA5E,SAAA;YAAA;cAAAoE,UAAA,CAAArD,CAAA;cAEA+C,OAAA,CAAAW,WAAA;cAAA,OAAAL,UAAA,CAAAM,CAAA;YAAA;cAAA,OAAAN,UAAA,CAAAjK,CAAA;UAAA;QAAA,GAAA0J,SAAA;MAAA;IAGA;IAEA;IACAvI,mBAAA,WAAAA,oBAAA;MACA,IAAAuJ,cAAA,OAAAC,cAAA,WAAAC,OAAA;QAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAzM,OAAA,EACAuM,OAAA;UAAAG,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAA/H,CAAA,IAAAmI,IAAA;YAAA,IAAAC,KAAA,GAAAH,KAAA,CAAAI,KAAA;YACA,IAAAC,MAAA,GAAAF,KAAA,CAAAG,MAAA,CAAA1F,gBAAA;YACAyF,MAAA,CAAApM,OAAA,WAAAoI,KAAA;cACA,IAAAA,KAAA,CAAAkE,EAAA;gBACA,IAAA7D,QAAA,GAAAlM,OAAA,CAAAgQ,gBAAA,CAAA7F,QAAA,CAAA+E,cAAA,CAAArD,KAAA,CAAAkE,EAAA;gBACA,IAAA7D,QAAA;kBACAA,QAAA,CAAAG,MAAA;gBACA;cACA;YACA;UACA;QAAA,SAAAC,GAAA;UAAAgD,SAAA,CAAAW,CAAA,CAAA3D,GAAA;QAAA;UAAAgD,SAAA,CAAAN,CAAA;QAAA;MACA;MAEA7E,QAAA,CAAAC,gBAAA,UAAA3G,OAAA,WAAAyM,IAAA;QACAf,cAAA,CAAAgB,OAAA,CAAAD,IAAA;MACA;IACA;IAEAE,gBAAA,WAAAA,iBAAA;MACA,IAAA7J,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAA8J,MAAA;MACA;QACA,KAAAC,QAAA;UACA1E,OAAA;UACA2E,IAAA;QACA;MACA;IACA;IAEAC,2BAAA,WAAAA,4BAAA;MAAA,IAAAC,MAAA;MAAA,WAAAxJ,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAuJ,UAAA;QAAA,WAAAxJ,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAsJ,UAAA;UAAA,kBAAAA,UAAA,CAAApJ,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,oBAAAgF,MAAA,CAAAlP,uBAAA;cACA;cAAAoP,UAAA,CAAApJ,CAAA;cAAA,OACAkJ,MAAA,CAAA/K,uBAAA;YAAA;cAAA,OAAAiL,UAAA,CAAAlM,CAAA;UAAA;QAAA,GAAAiM,SAAA;MAAA;IACA;IAEAE,6BAAA,WAAAA,8BAAA;MAAA,IAAAC,MAAA;MAAA,WAAA5J,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA2J,UAAA;QAAA,WAAA5J,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA0J,UAAA;UAAA,kBAAAA,UAAA,CAAAxJ,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,sBAAAoF,MAAA,CAAArP,yBAAA;cACA;cAAAuP,UAAA,CAAAxJ,CAAA;cAAA,OACAsJ,MAAA,CAAAnL,uBAAA;YAAA;cAAA,OAAAqL,UAAA,CAAAtM,CAAA;UAAA;QAAA,GAAAqM,SAAA;MAAA;IACA;IAEA;IACApL,uBAAA,WAAAA,wBAAA;MAAA,IAAAsL,MAAA;MAAA,WAAA/J,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA8J,UAAA;QAAA,IAAA7D,QAAA,EAAA8D,YAAA,EAAAC,aAAA,EAAAC,UAAA,EAAAC,YAAA,EAAAC,UAAA,EAAAC,GAAA;QAAA,WAAArK,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAmK,UAAA;UAAA,kBAAAA,UAAA,CAAAjK,CAAA;YAAA;cAAAiK,UAAA,CAAAnG,CAAA;cAEApB,OAAA,CAAAwB,GAAA;cACAxB,OAAA,CAAAwB,GAAA;cAAA+F,UAAA,CAAAjK,CAAA;cAAA,OAEA,IAAAkK,uCAAA;gBACAzK,aAAA;cACA;YAAA;cAFAoG,QAAA,GAAAoE,UAAA,CAAA/J,CAAA;cAGAwC,OAAA,CAAAwB,GAAA;cACAxB,OAAA,CAAAwB,GAAA,oCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,sCAAA2B,QAAA,CAAAtM,IAAA,CAAAoK,MAAA;gBACAjB,OAAA,CAAAwB,GAAA,uCAAA2B,QAAA,CAAAtM,IAAA,CAAA4Q,KAAA;;gBAEA;gBACAzH,OAAA,CAAAwB,GAAA;;gBAEA;gBACAyF,YAAA,GAAA9D,QAAA,CAAAtM,IAAA,CAAA6Q,IAAA,WAAAjO,IAAA;kBAAA,OAAAA,IAAA,CAAAkO,SAAA,KAAAzG,SAAA;gBAAA;gBACAlB,OAAA,CAAAwB,GAAA,8CAAAyF,YAAA;gBAEAC,aAAA,GAAA/D,QAAA,CAAAtM,IAAA;gBAEA,IAAAoQ,YAAA;kBACA;kBACAE,UAAA,OAAAS,mBAAA,CAAA/O,OAAA,MAAAgP,GAAA,CAAA1E,QAAA,CAAAtM,IAAA,CAAAiR,GAAA,WAAArO,IAAA;oBAAA,OAAAA,IAAA,CAAAkO,SAAA;kBAAA;kBACA3H,OAAA,CAAAwB,GAAA,4CAAA2F,UAAA;;kBAEA;kBACAC,YAAA,GAAAjE,QAAA,CAAAtM,IAAA,CAAAkR,MAAA,WAAAtO,IAAA;oBAAA,OAAAA,IAAA,CAAAkO,SAAA;kBAAA;kBACA3H,OAAA,CAAAwB,GAAA,sCAAA4F,YAAA;kBACApH,OAAA,CAAAwB,GAAA,uCAAA4F,YAAA,CAAAnG,MAAA;kBAEA,IAAAmG,YAAA,CAAAnG,MAAA;oBACAjB,OAAA,CAAAgI,IAAA;oBACAd,aAAA,GAAA/D,QAAA,CAAAtM,IAAA;kBACA;oBACAqQ,aAAA,GAAAE,YAAA;kBACA;gBACA;kBACApH,OAAA,CAAAwB,GAAA;gBACA;gBAEA,IAAA0F,aAAA,CAAAjG,MAAA;kBACAjB,OAAA,CAAAgI,IAAA;kBACAjB,MAAA,CAAAvP,kBAAA;gBACA;kBACA;kBACAwI,OAAA,CAAAwB,GAAA,0CAAApH,MAAA,CAAAC,IAAA,CAAA6M,aAAA;kBACAlH,OAAA,CAAAwB,GAAA,uCAAA0F,aAAA;kBACAlH,OAAA,CAAAwB,GAAA,+CAAA0F,aAAA,IAAAe,OAAA;;kBAEA;kBACAZ,UAAA,GAAAH,aAAA,CAAA3M,IAAA,WAAAC,CAAA,EAAAC,CAAA;oBACA,IAAAyN,OAAA,GAAAC,QAAA,CAAA3N,CAAA,CAAA4N,MAAA;oBACA,IAAAC,OAAA,GAAAF,QAAA,CAAA1N,CAAA,CAAA2N,MAAA;oBACA,OAAAF,OAAA,GAAAG,OAAA;kBACA,IAEA;kBACAtB,MAAA,CAAAvP,kBAAA,GAAA6P,UAAA,CAAAS,GAAA,WAAArO,IAAA;oBACA;oBACA,IAAA6O,OAAA;oBACA,IAAA7O,IAAA,CAAA6O,OAAA,KAAApH,SAAA;sBACAoH,OAAA,GAAAC,UAAA,CAAA9O,IAAA,CAAA6O,OAAA;oBACA,WAAA7O,IAAA,CAAA+O,MAAA,KAAAtH,SAAA;sBACAoH,OAAA,GAAAC,UAAA,CAAA9O,IAAA,CAAA+O,MAAA;oBACA,WAAA/O,IAAA,CAAAgP,WAAA,KAAAvH,SAAA;sBACAoH,OAAA,GAAAC,UAAA,CAAA9O,IAAA,CAAAgP,WAAA;oBACA,WAAAhP,IAAA,CAAAiP,IAAA,KAAAxH,SAAA;sBACAoH,OAAA,GAAAC,UAAA,CAAA9O,IAAA,CAAAiP,IAAA;oBACA,WAAAjP,IAAA,CAAAkP,QAAA,KAAAzH,SAAA;sBACAoH,OAAA,GAAAC,UAAA,CAAA9O,IAAA,CAAAkP,QAAA;oBACA;;oBAEA;oBACA,IAAAV,OAAA;oBACA,IAAAxO,IAAA,CAAAwO,OAAA,KAAA/G,SAAA;sBACA+G,OAAA,GAAAM,UAAA,CAAA9O,IAAA,CAAAwO,OAAA;oBACA,WAAAxO,IAAA,CAAAmP,MAAA,KAAA1H,SAAA;sBACA+G,OAAA,GAAAM,UAAA,CAAA9O,IAAA,CAAAmP,MAAA;oBACA,WAAAnP,IAAA,CAAAoP,UAAA,KAAA3H,SAAA;sBACA+G,OAAA,GAAAM,UAAA,CAAA9O,IAAA,CAAAoP,UAAA;oBACA,WAAApP,IAAA,CAAAqP,MAAA,KAAA5H,SAAA;sBACA+G,OAAA,GAAAM,UAAA,CAAA9O,IAAA,CAAAqP,MAAA;oBACA;oBAEA9I,OAAA,CAAAwB,GAAA,mCAAA7H,MAAA,CAAAF,IAAA,CAAA2O,MAAA,mCAAAzO,MAAA,CAAA2O,OAAA,6BAAA3O,MAAA,CAAAsO,OAAA;oBAEA;sBACAG,MAAA,EAAArB,MAAA,CAAAgC,YAAA,CAAAtP,IAAA,CAAA2O,MAAA;sBACAE,OAAA,EAAAA,OAAA;sBACAL,OAAA,EAAAA,OAAA;sBACA;sBACAe,YAAA,EAAAvP;oBACA;kBACA;kBAEAuG,OAAA,CAAAwB,GAAA,sCAAAuF,MAAA,CAAAvP,kBAAA;gBACA;cACA;gBACAwI,OAAA,CAAAC,KAAA,wBAAAkD,QAAA;gBACA4D,MAAA,CAAAvP,kBAAA;cACA;cAAA+P,UAAA,CAAAjK,CAAA;cAAA;YAAA;cAAAiK,UAAA,CAAAnG,CAAA;cAAAkG,GAAA,GAAAC,UAAA,CAAA/J,CAAA;cAEAwC,OAAA,CAAAC,KAAA,mBAAAqH,GAAA;cACAtH,OAAA,CAAAC,KAAA,UAAAqH,GAAA,CAAA3F,OAAA;cACAoF,MAAA,CAAAvP,kBAAA;YAAA;cAGA;cACAuP,MAAA,CAAAnK,SAAA;gBACAmK,MAAA,CAAAkC,yBAAA;cACA;YAAA;cAAA,OAAA1B,UAAA,CAAA/M,CAAA;UAAA;QAAA,GAAAwM,SAAA;MAAA;IACA;IAEA;IACA+B,YAAA,WAAAA,aAAAX,MAAA;MACA,KAAAA,MAAA;MACA,IAAAc,SAAA,GAAAd,MAAA,CAAAe,QAAA;MACA,IAAAD,SAAA,CAAAjI,MAAA;QACA,IAAAmI,IAAA,GAAAF,SAAA,CAAAG,SAAA;QACA,IAAAC,KAAA,GAAAnB,QAAA,CAAAe,SAAA,CAAAG,SAAA;QACA,UAAA1P,MAAA,CAAAyP,IAAA,OAAAzP,MAAA,CAAA2P,KAAA;MACA;MACA,OAAAJ,SAAA;IACA;IAIA;IACAK,gCAAA,WAAAA,iCAAA;MAAA,IAAAC,OAAA;MAAA,WAAAxM,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAuM,UAAA;QAAA,WAAAxM,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAsM,UAAA;UAAA,kBAAAA,UAAA,CAAApM,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,uBAAAgI,OAAA,CAAA/R,4BAAA;cACA;cACA+R,OAAA,CAAA9G,uBAAA;YAAA;cAAA,OAAAgH,UAAA,CAAAlP,CAAA;UAAA;QAAA,GAAAiP,SAAA;MAAA;IACA;IAEAE,kCAAA,WAAAA,mCAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5M,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA2M,UAAA;QAAA,WAAA5M,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA0M,UAAA;UAAA,kBAAAA,UAAA,CAAAxM,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,yBAAAoI,OAAA,CAAAlS,8BAAA;cACA;cACAkS,OAAA,CAAAlH,uBAAA;YAAA;cAAA,OAAAoH,UAAA,CAAAtP,CAAA;UAAA;QAAA,GAAAqP,SAAA;MAAA;IACA;IAEA;IACAE,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,OAAA;MAAA,WAAAhN,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA+M,UAAA;QAAA,WAAAhN,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA8M,UAAA;UAAA,kBAAAA,UAAA,CAAA5M,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,uBAAAwI,OAAA,CAAArS,0BAAA;cACA;cACAqS,OAAA,CAAArH,2BAAA;YAAA;cAAA,OAAAuH,UAAA,CAAA1P,CAAA;UAAA;QAAA,GAAAyP,SAAA;MAAA;IACA;IAEAE,gCAAA,WAAAA,iCAAA;MAAA,IAAAC,OAAA;MAAA,WAAApN,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAmN,UAAA;QAAA,WAAApN,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAkN,UAAA;UAAA,kBAAAA,UAAA,CAAAhN,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,yBAAA4I,OAAA,CAAAxS,4BAAA;cACA;cACAwS,OAAA,CAAAzH,2BAAA;YAAA;cAAA,OAAA2H,UAAA,CAAA9P,CAAA;UAAA;QAAA,GAAA6P,SAAA;MAAA;IACA;IAEA;IACAE,6BAAA,WAAAA,8BAAA;MAAA,IAAAC,OAAA;MAAA,WAAAxN,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAuN,UAAA;QAAA,WAAAxN,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAsN,UAAA;UAAA,kBAAAA,UAAA,CAAApN,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,kBAAAgJ,OAAA,CAAAG,yBAAA;cAAAD,UAAA,CAAApN,CAAA;cAAA,OACAkN,OAAA,CAAA9O,gBAAA;YAAA;cAAA,OAAAgP,UAAA,CAAAlQ,CAAA;UAAA;QAAA,GAAAiQ,SAAA;MAAA;IACA;IAEA/O,gBAAA,WAAAA,iBAAA;MAAA,IAAAkP,OAAA;MAAA,WAAA5N,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA2N,UAAA;QAAA,WAAA5N,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA0N,UAAA;UAAA,kBAAAA,UAAA,CAAAxN,CAAA;YAAA;cACA;gBACA;gBACA;gBACAsN,OAAA,CAAAG,WAAA,GAAAH,OAAA,CAAAI,kBAAA;;gBAEA;gBACAJ,OAAA,CAAAhO,SAAA;kBACAgO,OAAA,CAAAK,gBAAA;gBACA;cACA,SAAAhL,KAAA;gBACAD,OAAA,CAAAC,KAAA,cAAAA,KAAA;gBACA2K,OAAA,CAAAG,WAAA,GAAAH,OAAA,CAAAI,kBAAA;gBACAJ,OAAA,CAAAhO,SAAA;kBACAgO,OAAA,CAAAK,gBAAA;gBACA;cACA;YAAA;cAAA,OAAAH,UAAA,CAAAtQ,CAAA;UAAA;QAAA,GAAAqQ,SAAA;MAAA;IACA;IAEA;IACAG,kBAAA,WAAAA,mBAAA;MACA,QACA;QAAAE,YAAA;QAAAC,kBAAA;QAAAC,cAAA;MAAA,GACA;QAAAF,YAAA;QAAAC,kBAAA;QAAAC,cAAA;MAAA,GACA;QAAAF,YAAA;QAAAC,kBAAA;QAAAC,cAAA;MAAA,GACA;QAAAF,YAAA;QAAAC,kBAAA;QAAAC,cAAA;MAAA,GACA;QAAAF,YAAA;QAAAC,kBAAA;QAAAC,cAAA;MAAA,GACA;QAAAF,YAAA;QAAAC,kBAAA;QAAAC,cAAA;MAAA,EACA;IACA;IAMArQ,0BAAA,WAAAA,2BAAA;MAAA,IAAAsQ,OAAA;MAAA,WAAArO,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAoO,UAAA;QAAA,IAAAnI,QAAA,EAAAoI,GAAA;QAAA,WAAAtO,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAoO,UAAA;UAAA,kBAAAA,UAAA,CAAAlO,CAAA;YAAA;cAAAkO,UAAA,CAAApK,CAAA;cAAAoK,UAAA,CAAAlO,CAAA;cAAA,OAEA+N,OAAA,CAAA1M,iBAAA;YAAA;cAAAwE,QAAA,GAAAqI,UAAA,CAAAhO,CAAA;cACAwC,OAAA,CAAAwB,GAAA,uCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACAwU,OAAA,CAAAxT,qBAAA,GAAAsL,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,wCAAA6J,OAAA,CAAAxT,qBAAA;cACA;gBACAmI,OAAA,CAAAC,KAAA,sBAAAkD,QAAA;gBACA;gBACAkI,OAAA,CAAAxT,qBAAA,GAAAwT,OAAA,CAAAI,mBAAA;cACA;cAAAD,UAAA,CAAAlO,CAAA;cAAA;YAAA;cAAAkO,UAAA,CAAApK,CAAA;cAAAmK,GAAA,GAAAC,UAAA,CAAAhO,CAAA;cAEAwC,OAAA,CAAAC,KAAA,uBAAAsL,GAAA;cACA;cACAF,OAAA,CAAAxT,qBAAA,GAAAwT,OAAA,CAAAI,mBAAA;YAAA;cAGA;cACAJ,OAAA,CAAAzO,SAAA;gBACAyO,OAAA,CAAAK,0BAAA;cACA;YAAA;cAAA,OAAAF,UAAA,CAAAhR,CAAA;UAAA;QAAA,GAAA8Q,SAAA;MAAA;IACA;IAEA;IACAG,mBAAA,WAAAA,oBAAA;MACA,QACA;QACAP,YAAA;QACAS,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,GACA;QACAZ,YAAA;QACAS,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,GACA;QACAZ,YAAA;QACAS,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,GACA;QACAZ,YAAA;QACAS,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,GACA;QACAZ,YAAA;QACAS,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,GACA;QACAZ,YAAA;QACAS,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,EACA;IACA;IAEA9Q,4BAAA,WAAAA,6BAAA;MAAA,IAAA+Q,OAAA;MAAA,WAAA/O,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA8O,UAAA;QAAA,IAAA7I,QAAA,EAAA8I,GAAA;QAAA,WAAAhP,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA8O,UAAA;UAAA,kBAAAA,UAAA,CAAA5O,CAAA;YAAA;cAAA4O,UAAA,CAAA9K,CAAA;cAAA8K,UAAA,CAAA5O,CAAA;cAAA,OAEAyO,OAAA,CAAAhN,mBAAA;YAAA;cAAAoE,QAAA,GAAA+I,UAAA,CAAA1O,CAAA;cACAwC,OAAA,CAAAwB,GAAA,yCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACAkV,OAAA,CAAAjU,uBAAA,GAAAqL,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,0CAAAuK,OAAA,CAAAjU,uBAAA;cACA;gBACAkI,OAAA,CAAAC,KAAA,gBAAAkD,QAAA;gBACA4I,OAAA,CAAAjU,uBAAA;cACA;cAAAoU,UAAA,CAAA5O,CAAA;cAAA;YAAA;cAAA4O,UAAA,CAAA9K,CAAA;cAAA6K,GAAA,GAAAC,UAAA,CAAA1O,CAAA;cAEAwC,OAAA,CAAAC,KAAA,iBAAAgM,GAAA;cACAF,OAAA,CAAAjU,uBAAA;YAAA;cAGA;cACAiU,OAAA,CAAAnP,SAAA;gBACAmP,OAAA,CAAAI,4BAAA;cACA;YAAA;cAAA,OAAAD,UAAA,CAAA1R,CAAA;UAAA;QAAA,GAAAwR,SAAA;MAAA;IACA;IAIA;IACAI,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,OAAA;MAAA,WAAArP,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAoP,UAAA;QAAA,WAAArP,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAmP,UAAA;UAAA,kBAAAA,UAAA,CAAAjP,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,YAAA6K,OAAA,CAAArU,wBAAA;cACAqU,OAAA,CAAApU,oBAAA;cAAAsU,UAAA,CAAAjP,CAAA;cAAA,OACA+O,OAAA,CAAApR,yBAAA;YAAA;cAAAsR,UAAA,CAAAjP,CAAA;cAAA,OACA+O,OAAA,CAAAlR,2BAAA;YAAA;cAAA,OAAAoR,UAAA,CAAA/R,CAAA;UAAA;QAAA,GAAA8R,SAAA;MAAA;IACA;IAEAE,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzP,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAwP,UAAA;QAAA,WAAAzP,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAuP,UAAA;UAAA,kBAAAA,UAAA,CAAArP,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,YAAAiL,OAAA,CAAAxU,oBAAA;cAAA0U,UAAA,CAAArP,CAAA;cAAA,OACAmP,OAAA,CAAAtR,2BAAA;YAAA;cAAA,OAAAwR,UAAA,CAAAnS,CAAA;UAAA;QAAA,GAAAkS,SAAA;MAAA;IACA;IAEAzR,yBAAA,WAAAA,0BAAA;MAAA,IAAA2R,OAAA;MAAA,WAAA5P,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA2P,UAAA;QAAA,IAAAnP,QAAA,EAAAyF,QAAA,EAAA2J,GAAA;QAAA,WAAA7P,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA2P,UAAA;UAAA,kBAAAA,UAAA,CAAAzP,CAAA;YAAA;cAAA,MACAsP,OAAA,CAAA5U,wBAAA;gBAAA+U,UAAA,CAAAzP,CAAA;gBAAA;cAAA;cACA;cACAsP,OAAA,CAAA1U,mBAAA;cAAA6U,UAAA,CAAAzP,CAAA;cAAA;YAAA;cAEA;cACAI,QAAA,GAAAyK,QAAA,CAAAyE,OAAA,CAAA5U,wBAAA;cAAA+U,UAAA,CAAA3L,CAAA;cAAA2L,UAAA,CAAAzP,CAAA;cAAA,OAEAsP,OAAA,CAAAnP,eAAA,CAAAC,QAAA;YAAA;cAAAyF,QAAA,GAAA4J,UAAA,CAAAvP,CAAA;cACA,IAAA2F,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACA+V,OAAA,CAAA1U,mBAAA,GAAAiL,QAAA,CAAAtM,IAAA;cACA;gBACA+V,OAAA,CAAA1U,mBAAA;cACA;cAAA6U,UAAA,CAAAzP,CAAA;cAAA;YAAA;cAAAyP,UAAA,CAAA3L,CAAA;cAAA0L,GAAA,GAAAC,UAAA,CAAAvP,CAAA;cAEAwC,OAAA,CAAAC,KAAA,gBAAA6M,GAAA;cACAF,OAAA,CAAA1U,mBAAA;YAAA;cAAA,OAAA6U,UAAA,CAAAvS,CAAA;UAAA;QAAA,GAAAqS,SAAA;MAAA;IAGA;IAEA1R,2BAAA,WAAAA,4BAAA;MAAA,IAAA6R,OAAA;MAAA,WAAAhQ,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA+P,UAAA;QAAA,IAAAlP,MAAA,EAAAoF,QAAA,EAAA+J,GAAA;QAAA,WAAAjQ,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA+P,UAAA;UAAA,kBAAAA,UAAA,CAAA7P,CAAA;YAAA;cAAA6P,UAAA,CAAA/L,CAAA;cAEArD,MAAA;gBACAL,QAAA,EAAAyK,QAAA,CAAA6E,OAAA,CAAAhV,wBAAA;gBACA+E,aAAA,EAAAiQ,OAAA,CAAA7L;cACA,GAEA;cACA,IAAA6L,OAAA,CAAA/U,oBAAA,IAAA+U,OAAA,CAAA/U,oBAAA;gBACA8F,MAAA,CAAAiH,MAAA,GAAAgI,OAAA,CAAA/U,oBAAA;cACA;cAEA+H,OAAA,CAAAwB,GAAA,wCAAAzD,MAAA;cAAAoP,UAAA,CAAA7P,CAAA;cAAA,OACA0P,OAAA,CAAAlP,eAAA,CAAAC,MAAA;YAAA;cAAAoF,QAAA,GAAAgK,UAAA,CAAA3P,CAAA;cACAwC,OAAA,CAAAwB,GAAA,wCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACAmW,OAAA,CAAA7U,sBAAA,GAAAgL,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,yCAAAwL,OAAA,CAAA7U,sBAAA;cACA;gBACA6H,OAAA,CAAAC,KAAA,sBAAAkD,QAAA;gBACA6J,OAAA,CAAA7U,sBAAA,GAAA6U,OAAA,CAAAI,6BAAA;cACA;cAAAD,UAAA,CAAA7P,CAAA;cAAA;YAAA;cAAA6P,UAAA,CAAA/L,CAAA;cAAA8L,GAAA,GAAAC,UAAA,CAAA3P,CAAA;cAEAwC,OAAA,CAAAC,KAAA,uBAAAiN,GAAA;cACAF,OAAA,CAAA7U,sBAAA,GAAA6U,OAAA,CAAAI,6BAAA;YAAA;cAGA;cACAJ,OAAA,CAAApQ,SAAA;gBACAoQ,OAAA,CAAArK,2BAAA;cACA;YAAA;cAAA,OAAAwK,UAAA,CAAA3S,CAAA;UAAA;QAAA,GAAAyS,SAAA;MAAA;IACA;IAEA;IACAG,6BAAA,WAAAA,8BAAA;MACA,QACA;QAAAxT,QAAA;QAAAyT,KAAA;QAAAC,UAAA;MAAA,GACA;QAAA1T,QAAA;QAAAyT,KAAA;QAAAC,UAAA;MAAA,GACA;QAAA1T,QAAA;QAAAyT,KAAA;QAAAC,UAAA;MAAA,GACA;QAAA1T,QAAA;QAAAyT,KAAA;QAAAC,UAAA;MAAA,GACA;QAAA1T,QAAA;QAAAyT,KAAA;QAAAC,UAAA;MAAA,GACA;QAAA1T,QAAA;QAAAyT,KAAA;QAAAC,UAAA;MAAA,EACA;IACA;IAEAlS,8BAAA,WAAAA,+BAAA;MAAA,IAAAmS,OAAA;MAAA,WAAAvQ,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAsQ,UAAA;QAAA,IAAAzP,MAAA,EAAAoF,QAAA,EAAAsK,GAAA;QAAA,WAAAxQ,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAsQ,UAAA;UAAA,kBAAAA,UAAA,CAAApQ,CAAA;YAAA;cAAAoQ,UAAA,CAAAtM,CAAA;cAEArD,MAAA;gBACAhB,aAAA,EAAAwQ,OAAA,CAAApM,oBAAA;gBACAwM,QAAA,EAAAJ,OAAA,CAAAnV,gBAAA;gBACAsF,QAAA,EAAA6P,OAAA,CAAAlV;cACA;cAEA2H,OAAA,CAAAwB,GAAA,2CAAAzD,MAAA;cAAA2P,UAAA,CAAApQ,CAAA;cAAA,OACAiQ,OAAA,CAAAhO,4BAAA,CAAAxB,MAAA;YAAA;cAAAoF,QAAA,GAAAuK,UAAA,CAAAlQ,CAAA;cACAwC,OAAA,CAAAwB,GAAA,2CAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACA0W,OAAA,CAAAjV,yBAAA,GAAA6K,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,4CAAA+L,OAAA,CAAAjV,yBAAA;cACA;gBACA0H,OAAA,CAAAC,KAAA,sBAAAkD,QAAA;gBACAoK,OAAA,CAAAjV,yBAAA,GAAAiV,OAAA,CAAAK,wBAAA;cACA;cAAAF,UAAA,CAAApQ,CAAA;cAAA;YAAA;cAAAoQ,UAAA,CAAAtM,CAAA;cAAAqM,GAAA,GAAAC,UAAA,CAAAlQ,CAAA;cAEAwC,OAAA,CAAAC,KAAA,uBAAAwN,GAAA;cACAF,OAAA,CAAAjV,yBAAA,GAAAiV,OAAA,CAAAK,wBAAA;YAAA;cAGA;cACAL,OAAA,CAAA3Q,SAAA;gBACA2Q,OAAA,CAAA3K,iBAAA;cACA;YAAA;cAAA,OAAA8K,UAAA,CAAAlT,CAAA;UAAA;QAAA,GAAAgT,SAAA;MAAA;IACA;IAEA;IACAI,wBAAA,WAAAA,yBAAA;MACA,QACA;QAAAhU,QAAA;QAAAyT,KAAA;QAAAQ,KAAA;MAAA,GACA;QAAAjU,QAAA;QAAAyT,KAAA;QAAAQ,KAAA;MAAA,GACA;QAAAjU,QAAA;QAAAyT,KAAA;QAAAQ,KAAA;MAAA,GACA;QAAAjU,QAAA;QAAAyT,KAAA;QAAAQ,KAAA;MAAA,GACA;QAAAjU,QAAA;QAAAyT,KAAA;QAAAQ,KAAA;MAAA,GACA;QAAAjU,QAAA;QAAAyT,KAAA;QAAAQ,KAAA;MAAA,EACA;IACA;IAEAC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/Q,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA8Q,UAAA;QAAA,WAAA/Q,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA6Q,UAAA;UAAA,kBAAAA,UAAA,CAAA3Q,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,YAAAuM,OAAA,CAAA3V,gBAAA;cAAA6V,UAAA,CAAA3Q,CAAA;cAAA,OACAyQ,OAAA,CAAA3S,8BAAA;YAAA;cAAA,OAAA6S,UAAA,CAAAzT,CAAA;UAAA;QAAA,GAAAwT,SAAA;MAAA;IACA;IAEAE,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAnR,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAkR,UAAA;QAAA,WAAAnR,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAiR,UAAA;UAAA,kBAAAA,UAAA,CAAA/Q,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,UAAA2M,OAAA,CAAA9V,gBAAA;cAAAgW,UAAA,CAAA/Q,CAAA;cAAA,OACA6Q,OAAA,CAAA/S,8BAAA;YAAA;cAAA,OAAAiT,UAAA,CAAA7T,CAAA;UAAA;QAAA,GAAA4T,SAAA;MAAA;IACA;IAEA;IACA/S,qBAAA,WAAAA,sBAAA;MAAA,IAAAiT,OAAA;MAAA,WAAAtR,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAqR,UAAA;QAAA,IAAAxQ,MAAA,EAAAoF,QAAA,EAAAqL,GAAA;QAAA,WAAAvR,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAqR,UAAA;UAAA,kBAAAA,UAAA,CAAAnR,CAAA;YAAA;cAAAmR,UAAA,CAAArN,CAAA;cAEArD,MAAA;gBACA2Q,QAAA,EAAAJ,OAAA,CAAAvO,0BAAA,CAAAuO,OAAA,CAAAnN,oBAAA;cACA;cAEAnB,OAAA,CAAAwB,GAAA,kCAAAzD,MAAA;cAAA0Q,UAAA,CAAAnR,CAAA;cAAA,OACAgR,OAAA,CAAA3O,mBAAA,CAAA5B,MAAA;YAAA;cAAAoF,QAAA,GAAAsL,UAAA,CAAAjR,CAAA;cACAwC,OAAA,CAAAwB,GAAA,kCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACAyX,OAAA,CAAA/V,gBAAA,GAAA4K,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,mCAAA8M,OAAA,CAAA/V,gBAAA;cACA;gBACAyH,OAAA,CAAAC,KAAA,gBAAAkD,QAAA;gBACAmL,OAAA,CAAA/V,gBAAA;cACA;cAAAkW,UAAA,CAAAnR,CAAA;cAAA;YAAA;cAAAmR,UAAA,CAAArN,CAAA;cAAAoN,GAAA,GAAAC,UAAA,CAAAjR,CAAA;cAEAwC,OAAA,CAAAC,KAAA,iBAAAuO,GAAA;cACAF,OAAA,CAAA/V,gBAAA;YAAA;cAGA;cACA+V,OAAA,CAAA1R,SAAA;gBACA0R,OAAA,CAAAK,qBAAA;cACA;YAAA;cAAA,OAAAF,UAAA,CAAAjU,CAAA;UAAA;QAAA,GAAA+T,SAAA;MAAA;IACA;IAEA;IACAK,6BAAA,WAAAA,8BAAAC,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9R,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA6R,UAAA;QAAA,IAAAC,kBAAA,EAAAC,OAAA,EAAAC,YAAA,EAAAC,UAAA,EAAAC,IAAA;QAAA,WAAAnS,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAiS,UAAA;UAAA,kBAAAA,UAAA,CAAA/R,CAAA;YAAA;cACAwR,OAAA,CAAArW,iBAAA;cACAqW,OAAA,CAAAtW,gBAAA;cAAA6W,UAAA,CAAAjO,CAAA;cAGA;cACA4N,kBAAA,GAAAH,aAAA,CAAA/G,GAAA;gBAAA,IAAAwH,KAAA,OAAAtS,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAqS,UAAA5D,YAAA;kBAAA,IAAA5N,MAAA,EAAAoF,QAAA,EAAAqM,IAAA;kBAAA,WAAAvS,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAqS,UAAA;oBAAA,kBAAAA,UAAA,CAAAnS,CAAA;sBAAA;wBAAAmS,UAAA,CAAArO,CAAA;wBAEArD,MAAA;0BACA4N,YAAA,EAAAA,YAAA;0BACAT,YAAA;wBACA;wBAEAlL,OAAA,CAAAwB,GAAA,2BAAA7H,MAAA,CAAAgS,YAAA,iCAAA5N,MAAA;wBAAA0R,UAAA,CAAAnS,CAAA;wBAAA,OACA,IAAAoS,yCAAA,EAAA3R,MAAA;sBAAA;wBAAAoF,QAAA,GAAAsM,UAAA,CAAAjS,CAAA;wBACAwC,OAAA,CAAAwB,GAAA,2BAAA7H,MAAA,CAAAgS,YAAA,iCAAAxI,QAAA;wBAAA,MAEAA,QAAA,IAAAA,QAAA,CAAAwM,IAAA,IAAAxM,QAAA,CAAAwM,IAAA,YAAAxM,QAAA,CAAAtM,IAAA;0BAAA4Y,UAAA,CAAAnS,CAAA;0BAAA;wBAAA;wBAAA,OAAAmS,UAAA,CAAAjV,CAAA,IACA;0BACAmR,YAAA,EAAAA,YAAA;0BACAiE,QAAA,EAAAzM,QAAA,CAAAtM,IAAA,CAAA+Y,QAAA,mBAAAjW,MAAA,CAAAgS,YAAA;0BACAkE,UAAA,EAAA1M,QAAA,CAAAtM,IAAA,CAAAiZ,MAAA,IAAA3M,QAAA,CAAAtM,IAAA,CAAAgZ,UAAA,IAAA1M,QAAA,CAAA4M,GAAA;0BACAC,OAAA,EAAA7M,QAAA,CAAAtM,IAAA,CAAAmZ,OAAA;wBACA;sBAAA;wBAEAhQ,OAAA,CAAAC,KAAA,gBAAAtG,MAAA,CAAAgS,YAAA,uDAAAxI,QAAA;wBAAA,OAAAsM,UAAA,CAAAjV,CAAA,IACA;0BACAmR,YAAA,EAAAA,YAAA;0BACAiE,QAAA,iBAAAjW,MAAA,CAAAgS,YAAA;0BACAkE,UAAA,iBAAAlW,MAAA,CAAAgS,YAAA;0BACAqE,OAAA;wBACA;sBAAA;wBAAAP,UAAA,CAAAnS,CAAA;wBAAA;sBAAA;wBAAAmS,UAAA,CAAArO,CAAA;wBAAAoO,IAAA,GAAAC,UAAA,CAAAjS,CAAA;wBAGAwC,OAAA,CAAAC,KAAA,gBAAAtG,MAAA,CAAAgS,YAAA,wDAAA6D,IAAA;wBAAA,OAAAC,UAAA,CAAAjV,CAAA,IACA;0BACAmR,YAAA,EAAAA,YAAA;0BACAiE,QAAA,iBAAAjW,MAAA,CAAAgS,YAAA;0BACAkE,UAAA,iBAAAlW,MAAA,CAAAgS,YAAA,gDAAAhS,MAAA,CAAA6V,IAAA,CAAA7N,OAAA;0BACAqO,OAAA;wBACA;sBAAA;wBAAA,OAAAP,UAAA,CAAAjV,CAAA;oBAAA;kBAAA,GAAA+U,SAAA;gBAAA,CAEA;gBAAA,iBAAAU,EAAA;kBAAA,OAAAX,KAAA,CAAAY,KAAA,OAAA3P,SAAA;gBAAA;cAAA,MAEA;cAAA8O,UAAA,CAAA/R,CAAA;cAAA,OACA+D,OAAA,CAAAC,GAAA,CAAA0N,kBAAA;YAAA;cAAAC,OAAA,GAAAI,UAAA,CAAA7R,CAAA;cACAsR,OAAA,CAAAtW,gBAAA,GAAAyW,OAAA;cACAjP,OAAA,CAAAwB,GAAA,6CAAAsN,OAAA,CAAAtW,gBAAA;cAEA0W,YAAA,GAAAD,OAAA,CAAAlH,MAAA,WAAAoI,CAAA;gBAAA,OAAAA,CAAA,CAAAH,OAAA;cAAA,GAAA/O,MAAA;cACAkO,UAAA,GAAAF,OAAA,CAAAhO,MAAA;cAEA,IAAAiO,YAAA;gBACAJ,OAAA,CAAAzI,QAAA,CAAA2J,OAAA,4BAAArW,MAAA,CAAAuV,YAAA,OAAAvV,MAAA,CAAAwV,UAAA;cACA;gBACAL,OAAA,CAAAzI,QAAA,CAAApG,KAAA;cACA;cAAAoP,UAAA,CAAA/R,CAAA;cAAA;YAAA;cAAA+R,UAAA,CAAAjO,CAAA;cAAAgO,IAAA,GAAAC,UAAA,CAAA7R,CAAA;cAEAwC,OAAA,CAAAC,KAAA,kBAAAmP,IAAA;cACAN,OAAA,CAAAzI,QAAA,CAAApG,KAAA,iBAAAmP,IAAA,CAAAzN,OAAA;YAAA;cAAA0N,UAAA,CAAAjO,CAAA;cAEA0N,OAAA,CAAArW,iBAAA;cAAA,OAAA4W,UAAA,CAAAtK,CAAA;YAAA;cAAA,OAAAsK,UAAA,CAAA7U,CAAA;UAAA;QAAA,GAAAuU,SAAA;MAAA;IAEA;IAEA;IACAqB,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MAAA,WAAArT,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAoT,UAAA;QAAA,IAAAvS,MAAA,EAAAoF,QAAA,EAAAoN,UAAA,EAAAC,IAAA;QAAA,WAAAvT,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAqT,UAAA;UAAA,kBAAAA,UAAA,CAAAnT,CAAA;YAAA;cAAAmT,UAAA,CAAArP,CAAA;cAEArD,MAAA;gBACAlE,QAAA,EAAAsO,QAAA,CAAAkI,OAAA,CAAArY,wBAAA;cACA;cAAAyY,UAAA,CAAAnT,CAAA;cAAA,OAEA,IAAAoT,sCAAA,EAAA3S,MAAA;YAAA;cAAAoF,QAAA,GAAAsN,UAAA,CAAAjT,CAAA;cACAwC,OAAA,CAAAwB,GAAA,kCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAwZ,OAAA,CAAA3X,mBAAA,GAAAyK,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,mCAAA6O,OAAA,CAAA3X,mBAAA;;gBAEA;gBACA6X,UAAA,GAAAF,OAAA,CAAA3X,mBAAA,CAAAiY,IAAA,WAAAlX,IAAA;kBAAA,OAAAA,IAAA,CAAAG,QAAA;gBAAA;gBACA,IAAA2W,UAAA;kBACAF,OAAA,CAAA1X,gBAAA;gBACA,WAAA0X,OAAA,CAAA3X,mBAAA,CAAAuI,MAAA;kBACA;kBACAoP,OAAA,CAAA1X,gBAAA,GAAA0X,OAAA,CAAA3X,mBAAA,IAAAkB,QAAA;gBACA;;gBAEA;gBACAyW,OAAA,CAAAO,sBAAA;cACA;gBACA5Q,OAAA,CAAAC,KAAA,eAAAkD,QAAA;gBACAkN,OAAA,CAAA3X,mBAAA;cACA;cAAA+X,UAAA,CAAAnT,CAAA;cAAA;YAAA;cAAAmT,UAAA,CAAArP,CAAA;cAAAoP,IAAA,GAAAC,UAAA,CAAAjT,CAAA;cAEAwC,OAAA,CAAAC,KAAA,gBAAAuQ,IAAA;cACAH,OAAA,CAAA3X,mBAAA;YAAA;cAAA,OAAA+X,UAAA,CAAAjW,CAAA;UAAA;QAAA,GAAA8V,SAAA;MAAA;IAEA;IAEA;IACAM,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA7T,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA4T,UAAA;QAAA,IAAA/S,MAAA,EAAAoF,QAAA,EAAA4N,IAAA;QAAA,WAAA9T,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA4T,UAAA;UAAA,kBAAAA,UAAA,CAAA1T,CAAA;YAAA;cAAA0T,UAAA,CAAA5P,CAAA;cAEArD,MAAA;gBACAhB,aAAA,EAAA8T,OAAA,CAAA1P,oBAAA;gBACAvH,QAAA,EAAAiX,OAAA,CAAAlY;cACA;cAEAqH,OAAA,CAAAwB,GAAA,mCAAAzD,MAAA;cAAAiT,UAAA,CAAA1T,CAAA;cAAA,OACA,IAAA2T,2CAAA,EAAAlT,MAAA;YAAA;cAAAoF,QAAA,GAAA6N,UAAA,CAAAxT,CAAA;cACAwC,OAAA,CAAAwB,GAAA,mCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA,KAAAsM,QAAA,CAAAtM,IAAA,CAAAoK,MAAA;gBACA4P,OAAA,CAAAK,iBAAA,GAAA/N,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,oCAAAqP,OAAA,CAAAK,iBAAA;cACA;gBACAlR,OAAA,CAAAC,KAAA,iBAAAkD,QAAA;gBACA0N,OAAA,CAAAK,iBAAA;cACA;cAAAF,UAAA,CAAA1T,CAAA;cAAA;YAAA;cAAA0T,UAAA,CAAA5P,CAAA;cAAA2P,IAAA,GAAAC,UAAA,CAAAxT,CAAA;cAEAwC,OAAA,CAAAC,KAAA,kBAAA8Q,IAAA;cACAF,OAAA,CAAAK,iBAAA;YAAA;cAGA;cACAL,OAAA,CAAAjU,SAAA;gBACAiU,OAAA,CAAAM,mBAAA;cACA;YAAA;cAAA,OAAAH,UAAA,CAAAxW,CAAA;UAAA;QAAA,GAAAsW,SAAA;MAAA;IACA;IAEA;IACAM,gCAAA,WAAAA,iCAAA;MAAA,IAAAC,OAAA;MAAA,WAAArU,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAoU,UAAA;QAAA,WAAArU,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAmU,UAAA;UAAA,kBAAAA,UAAA,CAAAjU,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,YAAA6P,OAAA,CAAArZ,wBAAA;cACA;cAAAuZ,UAAA,CAAAjU,CAAA;cAAA,OACA+T,OAAA,CAAAjB,qBAAA;YAAA;cAAA,OAAAmB,UAAA,CAAA/W,CAAA;UAAA;QAAA,GAAA8W,SAAA;MAAA;IACA;IAEA;IACAE,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzU,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAwU,UAAA;QAAA,WAAAzU,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAuU,UAAA;UAAA,kBAAAA,UAAA,CAAArU,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,YAAAiQ,OAAA,CAAA9Y,gBAAA;cAAAgZ,UAAA,CAAArU,CAAA;cAAA,OACAmU,OAAA,CAAAb,sBAAA;YAAA;cAAA,OAAAe,UAAA,CAAAnX,CAAA;UAAA;QAAA,GAAAkX,SAAA;MAAA;IAEA;IAEAE,+BAAA,WAAAA,gCAAA;MACA,IAAAC,KAAA;MACA,SAAAha,qBAAA,SAAAA,qBAAA,CAAAoJ,MAAA;QACA,KAAApJ,qBAAA,CAAA2B,OAAA,WAAAC,IAAA;UACAoY,KAAA,IAAAtJ,UAAA,CAAA9O,IAAA,CAAAqS,oBAAA;QACA;MACA;MACA,OAAA+F,KAAA,CAAAC,OAAA;IACA;IAEAC,wBAAA,WAAAA,yBAAA;MACA,IAAAF,KAAA;MACA,SAAA/Z,uBAAA,SAAAA,uBAAA,CAAAmJ,MAAA;QACA;QACA,IAAA+Q,UAAA;QACA,KAAAla,uBAAA,CAAA0B,OAAA,WAAAC,IAAA;UACA,IAAAA,IAAA,CAAAwY,6BAAA,IAAAxY,IAAA,CAAAwY,6BAAA,CAAAhR,MAAA;YACAxH,IAAA,CAAAwY,6BAAA,CAAAzY,OAAA,WAAA0Y,MAAA;cACA,IAAAA,MAAA,CAAAC,WAAA,GAAAH,UAAA;gBACAA,UAAA,GAAAE,MAAA,CAAAC,WAAA;cACA;YACA;UACA;QACA;;QAEA;QACA,KAAAra,uBAAA,CAAA0B,OAAA,WAAAC,IAAA;UACA,IAAAA,IAAA,CAAAwY,6BAAA,IAAAxY,IAAA,CAAAwY,6BAAA,CAAAhR,MAAA;YACA,IAAAmR,YAAA,GAAA3Y,IAAA,CAAAwY,6BAAA,CAAAtB,IAAA,WAAAuB,MAAA;cAAA,OAAAA,MAAA,CAAAC,WAAA,KAAAH,UAAA;YAAA;YACA,IAAAI,YAAA;cACAP,KAAA,IAAAtJ,UAAA,CAAA6J,YAAA,CAAAC,MAAA;YACA;UACA;QACA;MACA;MACA,QAAAR,KAAA,UAAAC,OAAA;IACA;IAEA;IACAQ,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,OAAA;MAAA,WAAAvV,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAsV,UAAA;QAAA,WAAAvV,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAqV,UAAA;UAAA,kBAAAA,UAAA,CAAAnV,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,aAAA+Q,OAAA,CAAAxa,sBAAA;cACA;cACAwa,OAAA,CAAA3V,SAAA;gBACA2V,OAAA,CAAApG,4BAAA;cACA;YAAA;cAAA,OAAAsG,UAAA,CAAAjY,CAAA;UAAA;QAAA,GAAAgY,SAAA;MAAA;IACA;IAEA;IACA;IACAhX,sBAAA,WAAAA,uBAAA;MAAA,IAAAkX,OAAA;MAAA,WAAA1V,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAyV,UAAA;QAAA,IAAAxP,QAAA,EAAAyP,IAAA;QAAA,WAAA3V,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAyV,UAAA;UAAA,kBAAAA,UAAA,CAAAvV,CAAA;YAAA;cAAAuV,UAAA,CAAAzR,CAAA;cAAAyR,UAAA,CAAAvV,CAAA;cAAA,OAEA,IAAAwV,qCAAA;YAAA;cAAA3P,QAAA,GAAA0P,UAAA,CAAArV,CAAA;cACAwC,OAAA,CAAAwB,GAAA,mCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACA6b,OAAA,CAAAK,iBAAA,GAAA5P,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,oCAAAkR,OAAA,CAAAK,iBAAA;cACA;gBACA/S,OAAA,CAAAC,KAAA,eAAAkD,QAAA;gBACAuP,OAAA,CAAAK,iBAAA;cACA;cAAAF,UAAA,CAAAvV,CAAA;cAAA;YAAA;cAAAuV,UAAA,CAAAzR,CAAA;cAAAwR,IAAA,GAAAC,UAAA,CAAArV,CAAA;cAEAwC,OAAA,CAAAC,KAAA,gBAAA2S,IAAA;cACAF,OAAA,CAAAK,iBAAA;YAAA;cAAA,OAAAF,UAAA,CAAArY,CAAA;UAAA;QAAA,GAAAmY,SAAA;MAAA;IAIA;IAEA;IACAK,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAjW,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAgW,UAAA;QAAA,WAAAjW,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA+V,UAAA;UAAA,kBAAAA,UAAA,CAAA7V,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,sBAAAyR,OAAA,CAAAG,kBAAA;cACA;cACAH,OAAA,CAAAxQ,qBAAA;YAAA;cAAA,OAAA0Q,UAAA,CAAA3Y,CAAA;UAAA;QAAA,GAAA0Y,SAAA;MAAA;IACA;IAEA;IACAG,+BAAA,WAAAA,gCAAA;MAAA,IAAAC,OAAA;MAAA,WAAAtW,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAqW,UAAA;QAAA,WAAAtW,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAoW,UAAA;UAAA,kBAAAA,UAAA,CAAAlW,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,wBAAA8R,OAAA,CAAAG,2BAAA;cACA;cACAH,OAAA,CAAA7Q,qBAAA;YAAA;cAAA,OAAA+Q,UAAA,CAAAhZ,CAAA;UAAA;QAAA,GAAA+Y,SAAA;MAAA;IACA;IAEA;IACAG,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA3W,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA0W,UAAA;QAAA,IAAAC,OAAA,EAAA1Q,QAAA,EAAA2Q,IAAA;QAAA,WAAA7W,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA2W,UAAA;UAAA,kBAAAA,UAAA,CAAAzW,CAAA;YAAA;cAAAyW,UAAA,CAAA3S,CAAA;cAEAyS,OAAA,GAAAF,OAAA,CAAAP,kBAAA;cACApT,OAAA,CAAAwB,GAAA,kCAAAqS,OAAA;cAAAE,UAAA,CAAAzW,CAAA;cAAA,OAEA,IAAA0W,qCAAA,EAAAH,OAAA;YAAA;cAAA1Q,QAAA,GAAA4Q,UAAA,CAAAvW,CAAA;cACAwC,OAAA,CAAAwB,GAAA,kCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACA8c,OAAA,CAAAM,gBAAA,GAAA9Q,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,mCAAAmS,OAAA,CAAAM,gBAAA;cACA;gBACAjU,OAAA,CAAAC,KAAA,eAAAkD,QAAA;gBACAwQ,OAAA,CAAAM,gBAAA;cACA;cAAAF,UAAA,CAAAzW,CAAA;cAAA;YAAA;cAAAyW,UAAA,CAAA3S,CAAA;cAAA0S,IAAA,GAAAC,UAAA,CAAAvW,CAAA;cAEAwC,OAAA,CAAAC,KAAA,gBAAA6T,IAAA;cACAH,OAAA,CAAAM,gBAAA;YAAA;cAGA;cACAN,OAAA,CAAA/W,SAAA;gBACA+W,OAAA,CAAAlR,qBAAA;cACA;YAAA;cAAA,OAAAsR,UAAA,CAAAvZ,CAAA;UAAA;QAAA,GAAAoZ,SAAA;MAAA;IACA;IAEA;IACAM,oCAAA,WAAAA,qCAAA;MAAA,IAAAC,OAAA;MAAA,WAAAnX,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAkX,UAAA;QAAA,WAAAnX,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAiX,UAAA;UAAA,kBAAAA,UAAA,CAAA/W,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,iBAAA2S,OAAA,CAAAG,wBAAA;cACAH,OAAA,CAAAI,+BAAA;cAAAF,UAAA,CAAA/W,CAAA;cAAA,OACA6W,OAAA,CAAA7Y,+BAAA;YAAA;cAAA,OAAA+Y,UAAA,CAAA7Z,CAAA;UAAA;QAAA,GAAA4Z,SAAA;MAAA;IACA;IAEA;IACAI,iCAAA,WAAAA,kCAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzX,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAwX,UAAA;QAAA,WAAAzX,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAuX,UAAA;UAAA,kBAAAA,UAAA,CAAArX,CAAA;YAAA;cACA0C,OAAA,CAAAwB,GAAA,iBAAAiT,OAAA,CAAAG,qBAAA;cACAH,OAAA,CAAAI,4BAAA;cAAAF,UAAA,CAAArX,CAAA;cAAA,OACAmX,OAAA,CAAAlZ,4BAAA;YAAA;cAAA,OAAAoZ,UAAA,CAAAna,CAAA;UAAA;QAAA,GAAAka,SAAA;MAAA;IACA;IAEA;IACApZ,+BAAA,WAAAA,gCAAA;MAAA,IAAAwZ,OAAA;MAAA,WAAA9X,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA6X,UAAA;QAAA,IAAAhX,MAAA,EAAAoF,QAAA,EAAAoN,UAAA,EAAAyE,IAAA;QAAA,WAAA/X,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA6X,UAAA;UAAA,kBAAAA,UAAA,CAAA3X,CAAA;YAAA;cAAA2X,UAAA,CAAA7T,CAAA;cAEArD,MAAA;gBACAmX,UAAA,EAAAJ,OAAA,CAAAR,wBAAA;gBACAa,SAAA;gBAAA;gBACApY,aAAA,EAAA+X,OAAA,CAAA3T;cACA;cAEAnB,OAAA,CAAAwB,GAAA,4CAAAzD,MAAA;cAAAkX,UAAA,CAAA3X,CAAA;cAAA,OACA,IAAA8X,mDAAA,EAAArX,MAAA;YAAA;cAAAoF,QAAA,GAAA8R,UAAA,CAAAzX,CAAA;cACAwC,OAAA,CAAAwB,GAAA,4CAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAie,OAAA,CAAAO,6BAAA,GAAAlS,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,6CAAAsT,OAAA,CAAAO,6BAAA;;gBAEA;gBACA,IAAAP,OAAA,CAAAP,+BAAA,CAAAtT,MAAA,WAAA6T,OAAA,CAAAQ,wBAAA;kBACA/E,UAAA,GAAAuE,OAAA,CAAAO,6BAAA,CAAA1E,IAAA,WAAAlX,IAAA;oBAAA,OAAAA,IAAA,CAAAG,QAAA;kBAAA;kBACA,IAAA2W,UAAA;oBACAuE,OAAA,CAAAP,+BAAA;oBACAvU,OAAA,CAAAwB,GAAA;;oBAEA;oBACAsT,OAAA,CAAAS,+BAAA;kBACA;gBACA;cACA;gBACAvV,OAAA,CAAAC,KAAA,kBAAAkD,QAAA;gBACA2R,OAAA,CAAAO,6BAAA;cACA;cAAAJ,UAAA,CAAA3X,CAAA;cAAA;YAAA;cAAA2X,UAAA,CAAA7T,CAAA;cAAA4T,IAAA,GAAAC,UAAA,CAAAzX,CAAA;cAEAwC,OAAA,CAAAC,KAAA,mBAAA+U,IAAA;cACAF,OAAA,CAAAO,6BAAA;YAAA;cAAA,OAAAJ,UAAA,CAAAza,CAAA;UAAA;QAAA,GAAAua,SAAA;MAAA;IAEA;IAEA;IACAxZ,4BAAA,WAAAA,6BAAA;MAAA,IAAAia,OAAA;MAAA,WAAAxY,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAAuY,UAAA;QAAA,IAAA1X,MAAA,EAAAoF,QAAA,EAAAoN,UAAA,EAAAmF,IAAA;QAAA,WAAAzY,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAAuY,UAAA;UAAA,kBAAAA,UAAA,CAAArY,CAAA;YAAA;cAAAqY,UAAA,CAAAvU,CAAA;cAEArD,MAAA;gBACAmX,UAAA,EAAAM,OAAA,CAAAZ,qBAAA;gBACAO,SAAA;gBAAA;gBACApY,aAAA,EAAAyY,OAAA,CAAArU;cACA;cAEAnB,OAAA,CAAAwB,GAAA,yCAAAzD,MAAA;cAAA4X,UAAA,CAAArY,CAAA;cAAA,OACA,IAAA8X,mDAAA,EAAArX,MAAA;YAAA;cAAAoF,QAAA,GAAAwS,UAAA,CAAAnY,CAAA;cACAwC,OAAA,CAAAwB,GAAA,yCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACA2e,OAAA,CAAAI,0BAAA,GAAAzS,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,0CAAAgU,OAAA,CAAAI,0BAAA;;gBAEA;gBACA,IAAAJ,OAAA,CAAAX,4BAAA,CAAA5T,MAAA,WAAAuU,OAAA,CAAAF,wBAAA;kBACA/E,UAAA,GAAAiF,OAAA,CAAAI,0BAAA,CAAAjF,IAAA,WAAAlX,IAAA;oBAAA,OAAAA,IAAA,CAAAG,QAAA;kBAAA;kBACA,IAAA2W,UAAA;oBACAiF,OAAA,CAAAX,4BAAA;oBACA7U,OAAA,CAAAwB,GAAA;;oBAEA;oBACAgU,OAAA,CAAAD,+BAAA;kBACA;gBACA;cACA;gBACAvV,OAAA,CAAAC,KAAA,kBAAAkD,QAAA;gBACAqS,OAAA,CAAAI,0BAAA;cACA;cAAAD,UAAA,CAAArY,CAAA;cAAA;YAAA;cAAAqY,UAAA,CAAAvU,CAAA;cAAAsU,IAAA,GAAAC,UAAA,CAAAnY,CAAA;cAEAwC,OAAA,CAAAC,KAAA,mBAAAyV,IAAA;cACAF,OAAA,CAAAI,0BAAA;YAAA;cAAA,OAAAD,UAAA,CAAAnb,CAAA;UAAA;QAAA,GAAAib,SAAA;MAAA;IAEA;IAIA;IACAI,iCAAA,WAAAA,kCAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9Y,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA6Y,UAAA;QAAA,IAAAC,QAAA,EAAAjY,MAAA,EAAAoF,QAAA,EAAA8S,oBAAA,EAAAC,IAAA;QAAA,WAAAjZ,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA+Y,UAAA;UAAA,kBAAAA,UAAA,CAAA7Y,CAAA;YAAA;cAAA,MACAwY,OAAA,CAAAvB,+BAAA,CAAAtT,MAAA,UAAA6U,OAAA,CAAAjB,4BAAA,CAAA5T,MAAA;gBAAAkV,UAAA,CAAA7Y,CAAA;gBAAA;cAAA;cACAwY,OAAA,CAAAzP,QAAA,CAAA+P,OAAA;cAAA,OAAAD,UAAA,CAAA3b,CAAA;YAAA;cAIAsb,OAAA,CAAAO,iBAAA;cAAAF,UAAA,CAAA/U,CAAA;cAEA;cACA4U,QAAA,OAEA;cACAF,OAAA,CAAAvB,+BAAA,CAAA/a,OAAA,WAAAI,QAAA;gBACAoc,QAAA,CAAA7b,IAAA;kBACAgb,SAAA;kBAAA;kBACAvb,QAAA,EAAAA;gBACA;cACA;;cAEA;cACAkc,OAAA,CAAAjB,4BAAA,CAAArb,OAAA,WAAAI,QAAA;gBACAoc,QAAA,CAAA7b,IAAA;kBACAgb,SAAA;kBAAA;kBACAvb,QAAA,EAAAA;gBACA;cACA;cAEAmE,MAAA;gBACAhB,aAAA,EAAA+Y,OAAA,CAAA3U,oBAAA;gBACA6U,QAAA,EAAAA;cACA;cAEAhW,OAAA,CAAAwB,GAAA,mCAAAzD,MAAA;cAAAoY,UAAA,CAAA7Y,CAAA;cAAA,OACA,IAAAgZ,wDAAA,EAAAvY,MAAA;YAAA;cAAAoF,QAAA,GAAAgT,UAAA,CAAA3Y,CAAA;cACAwC,OAAA,CAAAwB,GAAA,mCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACAif,OAAA,CAAAS,oBAAA,GAAApT,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,oCAAAsU,OAAA,CAAAS,oBAAA;;gBAEA;gBACAT,OAAA,CAAAlZ,SAAA;kBACAkZ,OAAA,CAAAU,sBAAA;gBACA;;gBAEA;gBACAP,oBAAA,OAAArO,mBAAA,CAAA/O,OAAA,MAAAgP,GAAA,IAAAlO,MAAA,KAAAiO,mBAAA,CAAA/O,OAAA,EACAid,OAAA,CAAAvB,+BAAA,OAAA3M,mBAAA,CAAA/O,OAAA,EACAid,OAAA,CAAAjB,4BAAA,EACA,IAEA;gBACA,IAAAoB,oBAAA,CAAAhV,MAAA;kBACA6U,OAAA,CAAAlH,6BAAA,CAAAqH,oBAAA;gBACA;;gBAEA;gBACA,IAAAH,OAAA,CAAAjB,4BAAA,CAAA5T,MAAA;kBACA6U,OAAA,CAAAW,qBAAA,CAAAX,OAAA,CAAAjB,4BAAA;gBACA;kBACA;kBACAiB,OAAA,CAAAvc,oBAAA;gBACA;gBAEAuc,OAAA,CAAAzP,QAAA,CAAA2J,OAAA;cACA;gBACAhQ,OAAA,CAAAC,KAAA,iBAAAkD,QAAA;gBACA2S,OAAA,CAAAzP,QAAA,CAAApG,KAAA;cACA;cAAAkW,UAAA,CAAA7Y,CAAA;cAAA;YAAA;cAAA6Y,UAAA,CAAA/U,CAAA;cAAA8U,IAAA,GAAAC,UAAA,CAAA3Y,CAAA;cAEAwC,OAAA,CAAAC,KAAA,kBAAAiW,IAAA;cACAJ,OAAA,CAAAzP,QAAA,CAAApG,KAAA,aAAAiW,IAAA,CAAAvU,OAAA;YAAA;cAAAwU,UAAA,CAAA/U,CAAA;cAEA0U,OAAA,CAAAO,iBAAA;cAAA,OAAAF,UAAA,CAAApR,CAAA;YAAA;cAAA,OAAAoR,UAAA,CAAA3b,CAAA;UAAA;QAAA,GAAAub,SAAA;MAAA;IAEA;IAEA;IACAU,qBAAA,WAAAA,sBAAAC,SAAA;MAAA,IAAAC,OAAA;MAAA,WAAA3Z,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA0Z,UAAA;QAAA,IAAA7Y,MAAA,EAAAoF,QAAA,EAAA0T,IAAA;QAAA,WAAA5Z,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA0Z,UAAA;UAAA,kBAAAA,UAAA,CAAAxZ,CAAA;YAAA;cACAqZ,OAAA,CAAAI,uBAAA;cAAAD,UAAA,CAAA1V,CAAA;cAEArD,MAAA;gBACA2Y,SAAA,EAAAA;cACA;cAEA1W,OAAA,CAAAwB,GAAA,kCAAAzD,MAAA;cAAA+Y,UAAA,CAAAxZ,CAAA;cAAA,OACA,IAAA0Z,+BAAA,EAAAjZ,MAAA;YAAA;cAAAoF,QAAA,GAAA2T,UAAA,CAAAtZ,CAAA;cACAwC,OAAA,CAAAwB,GAAA,kCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACA8f,OAAA,CAAApd,oBAAA,GAAA4J,QAAA,CAAAtM,IAAA;gBACAmJ,OAAA,CAAAwB,GAAA,mCAAAmV,OAAA,CAAApd,oBAAA;cACA;gBACAyG,OAAA,CAAAC,KAAA,eAAAkD,QAAA;gBACAwT,OAAA,CAAApd,oBAAA;cACA;cAAAud,UAAA,CAAAxZ,CAAA;cAAA;YAAA;cAAAwZ,UAAA,CAAA1V,CAAA;cAAAyV,IAAA,GAAAC,UAAA,CAAAtZ,CAAA;cAEAwC,OAAA,CAAAC,KAAA,gBAAA4W,IAAA;cACAF,OAAA,CAAApd,oBAAA;YAAA;cAAAud,UAAA,CAAA1V,CAAA;cAEAuV,OAAA,CAAAI,uBAAA;cAAA,OAAAD,UAAA,CAAA/R,CAAA;YAAA;cAAA,OAAA+R,UAAA,CAAAtc,CAAA;UAAA;QAAA,GAAAoc,SAAA;MAAA;IAEA;IAEA;IACAK,YAAA,WAAAA,aAAAvc,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA;IACA;IAEA;IACAV,eAAA,WAAAA,gBAAAH,QAAA;MACA,IAAAqd,WAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAArd,QAAA;IACA;IAEA;IACAI,gBAAA,WAAAA,iBAAAH,SAAA;MACA,IAAAqd,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,YAAA,CAAArd,SAAA;IACA;IAEA;IACAsd,oBAAA,WAAAA,qBAAA3d,IAAA;MAAA,IAAA4d,OAAA;MACArX,OAAA,CAAAwB,GAAA,sCAAA/H,IAAA;MACA,KAAA6d,iBAAA,OAAAC,cAAA,CAAA1e,OAAA,MAAAY,IAAA;MACAuG,OAAA,CAAAwB,GAAA,sDAAA8V,iBAAA;MACA,KAAAE,uBAAA;;MAEA;MACA,KAAA5a,SAAA;QACAya,OAAA,CAAAI,mBAAA;MACA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAA;MACA,KAAAF,uBAAA;MACA,KAAAF,iBAAA;MACA,KAAAK,mBAAA;;MAEA;MACA,SAAAC,uBAAA;QACA;UACA,KAAAA,uBAAA,CAAAC,OAAA;UACA,KAAAD,uBAAA;QACA,SAAAvV,GAAA;UACArC,OAAA,CAAAC,KAAA,gBAAAoC,GAAA;QACA;MACA;IACA;IAEA;IACAoV,mBAAA,WAAAA,oBAAA;MAAA,IAAAK,OAAA;MAAA,WAAA9a,kBAAA,CAAAnE,OAAA,mBAAAoE,aAAA,CAAApE,OAAA,IAAAqE,CAAA,UAAA6a,UAAA;QAAA,IAAA/B,QAAA,EAAAjY,MAAA,EAAAoF,QAAA,EAAAiE,YAAA,EAAA4Q,iBAAA,EAAAC,oBAAA,EAAAC,IAAA;QAAA,WAAAjb,aAAA,CAAApE,OAAA,IAAAuE,CAAA,WAAA+a,UAAA;UAAA,kBAAAA,UAAA,CAAA7a,CAAA;YAAA;cACAwa,OAAA,CAAAM,sBAAA;cAAAD,UAAA,CAAA/W,CAAA;cAEA;cACA4U,QAAA,IACA;gBACAb,SAAA;gBAAA;gBACAvb,QAAA,EAAAke,OAAA,CAAAR,iBAAA,CAAA1d;cACA,GACA;gBACAub,SAAA;gBAAA;gBACAvb,QAAA,EAAAke,OAAA,CAAAR,iBAAA,CAAAe;cACA,EACA;cAEAta,MAAA;gBACAhB,aAAA,EAAA+a,OAAA,CAAA3W,oBAAA;gBACA6U,QAAA,EAAAA;cACA;cAEAhW,OAAA,CAAAwB,GAAA,gCAAAzD,MAAA;cAAAoa,UAAA,CAAA7a,CAAA;cAAA,OACA,IAAAgZ,wDAAA,EAAAvY,MAAA;YAAA;cAAAoF,QAAA,GAAAgV,UAAA,CAAA3a,CAAA;cACAwC,OAAA,CAAAwB,GAAA,gCAAA2B,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACA;gBACAuQ,YAAA,OAEA;gBACA4Q,iBAAA,GAAAF,OAAA,CAAA7d,gBAAA,CAAA6d,OAAA,CAAAR,iBAAA,CAAAxd,SAAA;gBACAme,oBAAA,GAAAH,OAAA,CAAA7d,gBAAA,CAAA6d,OAAA,CAAAR,iBAAA,CAAAgB,gBAAA;gBAEAtY,OAAA,CAAAwB,GAAA;kBACA+W,YAAA,EAAAT,OAAA,CAAAR,iBAAA,CAAA1d,QAAA;kBACAoe,iBAAA,EAAAA,iBAAA;kBACAK,eAAA,EAAAP,OAAA,CAAAR,iBAAA,CAAAe,eAAA;kBACAJ,oBAAA,EAAAA;gBACA;gBAEA9U,QAAA,CAAAtM,IAAA,CAAA2C,OAAA,WAAAgf,YAAA;kBACA,IAAAC,oBAAA,OAAAlB,cAAA,CAAA1e,OAAA,MAAA2f,YAAA;kBAEA,IAAAC,oBAAA,CAAAC,sBAAA;oBACA;oBACAD,oBAAA,CAAAC,sBAAA,GAAAD,oBAAA,CAAAC,sBAAA,CAAA3Q,MAAA,WAAA4Q,UAAA;sBACA,IAAAC,OAAA;sBACA;sBACA,IAAAJ,YAAA,CAAA5e,QAAA,KAAAke,OAAA,CAAAR,iBAAA,CAAA1d,QAAA;wBACAgf,OAAA,GAAAD,UAAA,CAAAE,SAAA,KAAAb,iBAAA;wBACAhY,OAAA,CAAAwB,GAAA,6BAAA7H,MAAA,CAAA6e,YAAA,CAAA5e,QAAA,iCAAAD,MAAA,CAAAgf,UAAA,CAAAE,SAAA,iCAAAlf,MAAA,CAAAqe,iBAAA,qBAAAre,MAAA,CAAAif,OAAA;sBACA;sBAEA,IAAAA,OAAA;wBACA,OAAAA,OAAA;sBACA;wBACA,IAAAJ,YAAA,CAAA5e,QAAA,KAAAke,OAAA,CAAAR,iBAAA,CAAAe,eAAA;0BACA,IAAAO,QAAA,GAAAD,UAAA,CAAAE,SAAA,KAAAZ,oBAAA;0BACAjY,OAAA,CAAAwB,GAAA,6BAAA7H,MAAA,CAAA6e,YAAA,CAAA5e,QAAA,iCAAAD,MAAA,CAAAgf,UAAA,CAAAE,SAAA,iCAAAlf,MAAA,CAAAse,oBAAA,qBAAAte,MAAA,CAAAif,QAAA;0BACA,OAAAA,QAAA;wBACA;sBACA;sBAGA;oBACA;oBAEA5Y,OAAA,CAAAwB,GAAA;oBACAxB,OAAA,CAAAwB,GAAA,CAAAiX,oBAAA,CAAAC,sBAAA;;oBAEA;oBACA,IAAAD,oBAAA,CAAAC,sBAAA,CAAAzX,MAAA;sBACAmG,YAAA,CAAAjN,IAAA,CAAAse,oBAAA;sBACAzY,OAAA,CAAAwB,GAAA,6BAAA7H,MAAA,CAAA6e,YAAA,CAAA5e,QAAA,yBAAAD,MAAA,CAAA8e,oBAAA,CAAAC,sBAAA,CAAAzX,MAAA;oBACA;kBACA;gBACA;gBAEA6W,OAAA,CAAAH,mBAAA,GAAAvQ,YAAA;gBACApH,OAAA,CAAAwB,GAAA,kCAAAsW,OAAA,CAAAH,mBAAA;gBACA3X,OAAA,CAAAwB,GAAA;kBACAsX,cAAA,EAAA1R,YAAA,CAAAnG,MAAA;kBACA8X,SAAA,EAAA3R,YAAA,CAAAU,GAAA,WAAA5K,CAAA;oBAAA,IAAA8b,qBAAA,EAAAC,sBAAA;oBAAA;sBACAxiB,IAAA,EAAAyG,CAAA,CAAAtD,QAAA;sBACAsf,eAAA,IAAAF,qBAAA,GAAA9b,CAAA,CAAAwb,sBAAA,cAAAM,qBAAA,uBAAAA,qBAAA,CAAA/X,MAAA;sBACAkY,WAAA,IAAAF,sBAAA,GAAA/b,CAAA,CAAAwb,sBAAA,cAAAO,sBAAA,uBAAAA,sBAAA,CAAAnR,GAAA,WAAA1G,CAAA;wBAAA,OAAAA,CAAA,CAAAyX,SAAA;sBAAA;oBACA;kBAAA;gBACA;;gBAEA;gBACAf,OAAA,CAAAlb,SAAA;kBACAkb,OAAA,CAAAsB,qBAAA;gBACA;cACA;gBACApZ,OAAA,CAAAC,KAAA,aAAAkD,QAAA;gBACA2U,OAAA,CAAAzR,QAAA,CAAApG,KAAA;cACA;cAAAkY,UAAA,CAAA7a,CAAA;cAAA;YAAA;cAAA6a,UAAA,CAAA/W,CAAA;cAAA8W,IAAA,GAAAC,UAAA,CAAA3a,CAAA;cAEAwC,OAAA,CAAAC,KAAA,cAAAiY,IAAA;cACAJ,OAAA,CAAAzR,QAAA,CAAApG,KAAA,eAAAiY,IAAA,CAAAvW,OAAA;YAAA;cAAAwW,UAAA,CAAA/W,CAAA;cAEA0W,OAAA,CAAAM,sBAAA;cAAA,OAAAD,UAAA,CAAApT,CAAA;YAAA;cAAA,OAAAoT,UAAA,CAAA3d,CAAA;UAAA;QAAA,GAAAud,SAAA;MAAA;IAEA;IAEA;IACAqB,qBAAA,WAAAA,sBAAA;MACA,IAAAC,QAAA,GAAAnZ,QAAA,CAAA+E,cAAA;MACA,KAAAoU,QAAA;QACArZ,OAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,SAAA2X,uBAAA;QACA;UACA,KAAAA,uBAAA,CAAAC,OAAA;QACA,SAAAxV,GAAA;UACArC,OAAA,CAAAC,KAAA,kBAAAoC,GAAA;QACA;MACA;;MAEA;MACA;QACA,KAAAuV,uBAAA,GAAA7hB,OAAA,CAAAujB,IAAA,CAAAD,QAAA;MACA,SAAAhX,GAAA;QACArC,OAAA,CAAAC,KAAA,gBAAAoC,GAAA;QACA;MACA;MAEA,UAAAsV,mBAAA,SAAAA,mBAAA,CAAA1W,MAAA;QACAoY,QAAA,CAAAhZ,SAAA;QACA;MACA;MAEA,IAAAkZ,UAAA,YAAAA,WAAAC,OAAA;QACA,IAAApQ,IAAA,GAAAoQ,OAAA,CAAAnQ,SAAA;QACA,IAAAC,KAAA,GAAAkQ,OAAA,CAAAnQ,SAAA;QACA,IAAAoQ,GAAA,GAAAD,OAAA,CAAAnQ,SAAA;QACA,UAAA1P,MAAA,CAAAyP,IAAA,YAAAzP,MAAA,CAAA2P,KAAA,YAAA3P,MAAA,CAAA8f,GAAA;MACA;;MAEA;MACA,IAAAC,QAAA,OAAA7R,GAAA;MAEA,KAAA8P,mBAAA,CAAAne,OAAA,WAAAgf,YAAA;QACA,IAAAA,YAAA,CAAAE,sBAAA;UACAF,YAAA,CAAAE,sBAAA,CAAAlf,OAAA,WAAAmf,UAAA;YACA,IAAAA,UAAA,CAAAgB,SAAA;cACAhB,UAAA,CAAAgB,SAAA,CAAAngB,OAAA,WAAAC,IAAA;gBACAigB,QAAA,CAAAE,GAAA,CAAAngB,IAAA,CAAAogB,UAAA;cACA;YACA;UACA;QACA;MACA;MAEAH,QAAA,GAAApW,KAAA,CAAAwW,IAAA,CAAAJ,QAAA,EAAAnf,IAAA;MACA,IAAAwf,SAAA,GAAAL,QAAA,CAAA5R,GAAA,CAAAyR,UAAA;MAEA,IAAAG,QAAA,CAAAzY,MAAA;QACAoY,QAAA,CAAAhZ,SAAA;QACA;MACA;;MAEA;MACA,IAAA2Z,MAAA;MACA,IAAAC,UAAA;MACA,IAAAC,MAAA;MACA,IAAAC,UAAA;MAEAna,OAAA,CAAAwB,GAAA;MACAxB,OAAA,CAAAwB,GAAA;QACA4Y,aAAA,OAAAzC,mBAAA,CAAA1W,MAAA;QACAoZ,YAAA,OAAA/C,iBAAA,CAAA1d,QAAA;QACA0gB,eAAA,OAAAhD,iBAAA,CAAAe;MACA;MAEA,KAAAV,mBAAA,CAAAne,OAAA,WAAAgf,YAAA;QACA,IAAA7M,YAAA,GAAA6M,YAAA,CAAA5e,QAAA;QACAoG,OAAA,CAAAwB,GAAA,gCAAA7H,MAAA,CAAAgS,YAAA;QAEA,IAAA6M,YAAA,CAAAE,sBAAA;UACA1Y,OAAA,CAAAwB,GAAA,+BAAA7H,MAAA,CAAA6e,YAAA,CAAAE,sBAAA,CAAAzX,MAAA;UACAuX,YAAA,CAAAE,sBAAA,CAAAlf,OAAA,WAAAmf,UAAA,EAAA4B,KAAA;YAAA,IAAAC,qBAAA;YACAxa,OAAA,CAAAwB,GAAA,yBAAA7H,MAAA,CAAA4gB,KAAA,YAAA5gB,MAAA,CAAAgf,UAAA,CAAAE,SAAA,4CAAAlf,MAAA,GAAA6gB,qBAAA,GAAA7B,UAAA,CAAAgB,SAAA,cAAAa,qBAAA,uBAAAA,qBAAA,CAAAvZ,MAAA;UACA;;UAEA;UACAuX,YAAA,CAAAE,sBAAA,CAAAlf,OAAA,WAAAmf,UAAA,EAAA8B,UAAA;YACA,IAAAC,SAAA,GAAAhB,QAAA,CAAA5R,GAAA,WAAA6S,IAAA;cACA,IAAAC,KAAA,GAAAjC,UAAA,CAAAgB,SAAA,CAAAhJ,IAAA,WAAAlX,IAAA;gBAAA,OAAAA,IAAA,CAAAogB,UAAA,KAAAc,IAAA;cAAA;cACA,OAAAC,KAAA,GAAArS,UAAA,CAAAqS,KAAA,CAAAC,KAAA;YACA;;YAEA;YACA,IAAAC,cAAA,GAAAJ,SAAA,CAAA3S,MAAA,WAAAvK,CAAA;cAAA,OAAAA,CAAA,aAAAA,CAAA,KAAA0D,SAAA;YAAA,GAAAD,MAAA;YACAjB,OAAA,CAAAwB,GAAA,uCAAA7H,MAAA,CAAAgf,UAAA,CAAAE,SAAA,6CAAAlf,MAAA,CAAAmhB,cAAA,OAAAnhB,MAAA,CAAA+gB,SAAA,CAAAzZ,MAAA;;YAEA;YACA,IAAA8Z,UAAA,MAAAphB,MAAA,CAAAgS,YAAA,OAAAhS,MAAA,CAAAgf,UAAA,CAAAE,SAAA;YACA,IAAAmC,SAAA,GAAAd,MAAA,CAAAC,UAAA,GAAAD,MAAA,CAAAjZ,MAAA;;YAEA;YACA,IAAAga,OAAA,GAAAC,IAAA,CAAAC,SAAA,CAAAT,SAAA;YACA,IAAAU,cAAA,GAAApB,MAAA,CAAArJ,IAAA,WAAAnL,CAAA;cAAA,OAAA0V,IAAA,CAAAC,SAAA,CAAA3V,CAAA,CAAA3O,IAAA,MAAAokB,OAAA;YAAA;YACA,IAAAI,YAAA,GAAAX,SAAA;YAEA,IAAAU,cAAA,IAAAV,SAAA,CAAAhT,IAAA,WAAAlK,CAAA;cAAA,OAAAA,CAAA;YAAA;cACA;cACA6d,YAAA,GAAAX,SAAA,CAAA5S,GAAA,WAAAnC,KAAA;gBAAA,OAAAA,KAAA,YAAAA,KAAA;cAAA;cACA3F,OAAA,CAAAwB,GAAA,+DAAA7H,MAAA,CAAAohB,UAAA;YACA;YAEAf,MAAA,CAAA7f,IAAA;cACA1D,IAAA,EAAAskB,UAAA;cACAzU,IAAA;cACAzP,IAAA,EAAAwkB,YAAA;cACAC,MAAA;cACAC,SAAA;gBACAC,KAAA;gBACAC,KAAA,EAAAT,SAAA;gBACA;gBACA1U,IAAA,EAAA+U,YAAA,KAAAX,SAAA;cACA;cACAgB,SAAA;gBACAD,KAAA,EAAAT;cACA;cACAW,MAAA;cACAC,UAAA;cACAC,YAAA;cACA;cACAC,CAAA,EAAA3B,UAAA;YACA;YAEAF,UAAA,CAAA9f,IAAA,CAAA4gB,UAAA;YACAZ,UAAA;YACAna,OAAA,CAAAwB,GAAA,yCAAA7H,MAAA,CAAAohB,UAAA,0BAAAphB,MAAA,CAAAqhB,SAAA,sCAAArhB,MAAA,CAAAmhB,cAAA;UACA;QACA;MACA;MAEA9a,OAAA,CAAAwB,GAAA;MACAxB,OAAA,CAAAwB,GAAA,6BAAA7H,MAAA,CAAAqgB,MAAA,CAAA/Y,MAAA;MACA+Y,MAAA,CAAAxgB,OAAA,WAAAgM,CAAA,EAAAuW,CAAA;QACA,IAAAC,UAAA,GAAAxW,CAAA,CAAA3O,IAAA,CAAAkR,MAAA,WAAAvK,CAAA;UAAA,OAAAA,CAAA,aAAAA,CAAA,KAAA0D,SAAA;QAAA,GAAAD,MAAA;QACAjB,OAAA,CAAAwB,GAAA,MAAA7H,MAAA,CAAAoiB,CAAA,YAAApiB,MAAA,CAAA6L,CAAA,CAAA/O,IAAA,kCAAAkD,MAAA,CAAAqiB,UAAA;MACA;;MAEA;MACA,IAAAC,QAAA,EAAAC,QAAA;MACA,IAAAC,WAAA,GAAAnC,MAAA,CAAAoC,OAAA,WAAA5W,CAAA;QAAA,OAAAA,CAAA,CAAA3O,IAAA,CAAAkR,MAAA,WAAAvK,CAAA;UAAA,OAAAA,CAAA,aAAAA,CAAA,KAAA0D,SAAA;QAAA;MAAA;MACA,IAAAib,WAAA,CAAAlb,MAAA;QACAgb,QAAA,GAAAI,IAAA,CAAAC,GAAA,CAAApM,KAAA,CAAAmM,IAAA,MAAAzU,mBAAA,CAAA/O,OAAA,EAAAsjB,WAAA;QACAD,QAAA,GAAAG,IAAA,CAAAE,GAAA,CAAArM,KAAA,CAAAmM,IAAA,MAAAzU,mBAAA,CAAA/O,OAAA,EAAAsjB,WAAA;MACA;MAEA,IAAAK,MAAA;QACAC,eAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACAtW,IAAA;UACA;UACAuW,SAAA,WAAAA,UAAA9e,MAAA;YACA,IAAA+e,GAAA,GAAA/e,MAAA,IAAAgf,cAAA;YACAhf,MAAA,CAAAvE,OAAA,WAAAC,IAAA;cACA,IAAAA,IAAA,CAAAkM,KAAA,aAAAlM,IAAA,CAAAkM,KAAA,KAAAzE,SAAA;gBACA4b,GAAA,OAAAnjB,MAAA,CAAAF,IAAA,CAAAujB,MAAA,EAAArjB,MAAA,CAAAF,IAAA,CAAAwjB,UAAA,QAAAtjB,MAAA,CAAAF,IAAA,CAAAkM,KAAA;cACA;gBACAmX,GAAA,OAAAnjB,MAAA,CAAAF,IAAA,CAAAujB,MAAA,EAAArjB,MAAA,CAAAF,IAAA,CAAAwjB,UAAA;cACA;YACA;YACA,OAAAH,GAAA;UACA;QACA;QACAI,MAAA;UACArmB,IAAA,EAAAojB,UAAA;UACAkD,SAAA;YACA1B,KAAA;UACA;UACA2B,GAAA;QACA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAJ,GAAA;UACAK,YAAA;QACA;QACAC,KAAA;UACApX,IAAA;UACAzP,IAAA,EAAAkjB,SAAA;UACA4D,SAAA;YACAlC,KAAA;YACAmC,QAAA,WAAAA,SAAArD,KAAA,EAAA5U,KAAA;cACA,IAAA4U,KAAA,IAAAb,QAAA,CAAAzY,MAAA,KAAAyY,QAAA,CAAAzY,MAAA;cAEA,IAAA4c,YAAA,OAAAhW,GAAA;cACA6R,QAAA,CAAAlgB,OAAA,WAAAggB,OAAA;gBACA,IAAApQ,IAAA,GAAAoQ,OAAA,CAAAnQ,SAAA;gBACA,IAAAC,KAAA,GAAAkQ,OAAA,CAAAnQ,SAAA;gBACAwU,YAAA,CAAAjE,GAAA,IAAAjgB,MAAA,CAAAyP,IAAA,EAAAzP,MAAA,CAAA2P,KAAA;cACA;cAEA,IAAAwU,WAAA,GAAAD,YAAA,CAAAE,IAAA;cACA,IAAAD,WAAA;cAEA,IAAAE,eAAA,GAAAtE,QAAA,CAAAzY,MAAA;cACA,IAAAgd,aAAA,GAAA5B,IAAA,CAAA6B,KAAA,CAAAF,eAAA,GAAA3B,IAAA,CAAAC,GAAA,CAAAwB,WAAA;cAEA,OAAAvD,KAAA,GAAA8B,IAAA,CAAAE,GAAA,CAAA0B,aAAA;YACA;YACApB,SAAA,WAAAA,UAAAlX,KAAA,EAAA4U,KAAA;cACA,IAAAA,KAAA,IAAAb,QAAA,CAAAzY,MAAA;cACA,IAAAkd,eAAA,GAAAzE,QAAA,CAAAa,KAAA;cACA,KAAA4D,eAAA;cAEA,IAAA/U,IAAA,GAAA+U,eAAA,CAAA9U,SAAA;cACA,IAAAC,KAAA,GAAAnB,QAAA,CAAAgW,eAAA,CAAA9U,SAAA;cACA,UAAA1P,MAAA,CAAAyP,IAAA,OAAAzP,MAAA,CAAA2P,KAAA;YACA;UACA;UACA8U,QAAA;YACA7C,SAAA;cACAE,KAAA;YACA;UACA;QACA;QACA4C,KAAA;UACA/X,IAAA;UACA7P,IAAA;UACA6lB,GAAA,EAAAL,QAAA;UACAM,GAAA,EAAAL,QAAA;UACAkC,QAAA;YACA7C,SAAA;cACAE,KAAA;YACA;UACA;UACAkC,SAAA;YACAlC,KAAA;UACA;UACA6C,SAAA;YACA/C,SAAA;cACAE,KAAA;YACA;UACA;QACA;QACAzB,MAAA,EAAAA;MACA;MAEA,KAAApC,uBAAA,CAAA2G,SAAA,CAAA/B,MAAA;IACA;IAEA;IACAjH,+BAAA,WAAAA,gCAAA;MAAA,IAAAiJ,OAAA;MACA;MACA,SAAAjK,+BAAA,CAAAkK,QAAA,WACA,KAAA5J,4BAAA,CAAA4J,QAAA,WACA,MAAAnJ,wBAAA;QAEA,KAAAA,wBAAA;QACAtV,OAAA,CAAAwB,GAAA;;QAEA;QACA,KAAA5E,SAAA;UACA4hB,OAAA,CAAA3I,iCAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}