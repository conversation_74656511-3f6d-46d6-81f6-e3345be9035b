<template>
  <div class="quality-cost-dashboard">
    <header class="header">
      <div class="header-wrapper">
        <h1>兴澄特钢质量成本看板</h1>
        <!-- 标题右下角筛选区域 -->
        <div class="header-filters">
          <div class="filter-item">
            <span class="label">成本中心：</span>
            <el-select v-model="costCenter" placeholder="请选择成本中心" style="width: 160px;" :loading="costCenterLoading"
              size="small">
              <el-option v-for="item in costCenterOptions" :key="item.key" :label="item.label" :value="item.key">
              </el-option>
            </el-select>
          </div>
          <div class="filter-item">
            <span class="label">会计期：</span>
            <el-date-picker v-model="accountingPeriod" type="month" placeholder="请选择年月" format="yyyy-MM"
              value-format="yyyy-MM" style="width: 130px;" size="small">
            </el-date-picker>
          </div>
          <div class="filter-item">
            <span class="label">质量成本类型：</span>
            <el-select v-model="containType" placeholder="请选择质量成本类型" style="width: 130px;" size="small">
              <el-option label="含不列入项" :value="2"></el-option>
              <el-option label="不含列入项" :value="1"></el-option>
            </el-select>
          </div>
        </div>
      </div>
      <!-- <p>数据更新时间: {{ updateTime }}</p> -->
    </header>

    <div class="dashboard-grid">
      <!-- 第四类：核心绩效指标（KPI）看板 -->
      <div class="kpi-grid">
        <div class="kpi-card">
          <div class="title">{{ costCenter === 'JYXCTZG' ? '销量' : '产量' }}</div>
          <div class="value">{{ formatTonnage(containType === 2 ? qualityCostData.allcTon : qualityCostData.costTon) }}</div>
          <div class="comparison">
            <svg :class="['arrow', getPercentageClass(qualityCostDetail.costTonUpPercent)]" fill="none"
              stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path v-if="isNegativePercentage(qualityCostDetail.costTonUpPercent)" stroke-linecap="round"
                stroke-linejoin="round" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
              <path v-else stroke-linecap="round" stroke-linejoin="round" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
            </svg>
            <span :class="getPercentageClass(qualityCostDetail.costTonUpPercent)">{{ qualityCostDetail.costTonUpPercent
            }}
              vs 上期</span>
          </div>
        </div>
        <div class="kpi-card">
          <div class="title">总金额</div>
          <div class="value">{{ formatAmount(containType === 2 ? qualityCostData.allcEx : qualityCostData.costEx) }}</div>
          <div class="comparison">
            <svg :class="['arrow', getPercentageClass(qualityCostDetail.costExPercent)]" fill="none"
              stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path v-if="isNegativePercentage(qualityCostDetail.costExPercent)" stroke-linecap="round"
                stroke-linejoin="round" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
              <path v-else stroke-linecap="round" stroke-linejoin="round" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
            </svg>
            <span :class="getPercentageClass(qualityCostDetail.costExPercent)">{{ qualityCostDetail.costExPercent }} vs
              上期</span>
          </div>
        </div>
        <div class="kpi-card">
          <div class="title">吨钢成本</div>
          <div class="value">{{ formatUnitCost(containType === 2 ? qualityCostData.allcPerEx : qualityCostData.costPerEx) }}</div>
          <div class="comparison">
            <svg :class="['arrow', getPercentageClass(qualityCostDetail.costPerExPercent)]" fill="none"
              stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path v-if="isNegativePercentage(qualityCostDetail.costPerExPercent)" stroke-linecap="round"
                stroke-linejoin="round" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
              <path v-else stroke-linecap="round" stroke-linejoin="round" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
            </svg>
            <span :class="getPercentageClass(qualityCostDetail.costPerExPercent)">{{ qualityCostDetail.costPerExPercent
            }}
              vs 上期</span>
          </div>
        </div>
      </div>

      <!-- 第2行：质量成本四大类别占比（占1/2宽度）+ 四大质量成本趋势（占1/2宽度） -->
      <div class="chart-container" style="grid-column: span 2;">
        <h3>1. 质量成本四大类别占比</h3>
        <div ref="pieChart" class="chart"></div>
      </div>
      <div class="chart-container" style="grid-column: span 2;">
        <h3>2. 四大质量成本趋势</h3>
        <div ref="multiLineChart" class="chart"></div>
      </div>

      <!-- 第3行：外部损失成本构成图表 + 内部损失成本构成图表 - 每个占据行宽的50% -->
      <div class="chart-container large-chart" style="grid-column: span 2;">
        <h3>3. 外部损失成本构成</h3>
        <div ref="externalCostDetailChart" class="chart"></div>
      </div>
      <div class="chart-container large-chart" style="grid-column: span 2;">
        <h3>4. 内部损失成本构成</h3>
        <div ref="internalCostDetailChart" class="chart"></div>
      </div>

      <!-- 第3行：产品挽救处理成本分析图表 - 占据整行宽度 -->
      <div class="chart-container" style="grid-column: 1 / -1;">
        <h3>5. 产品挽救处理成本分析</h3>
        <div ref="waterfallChart" class="chart"></div>
      </div>

      <!-- 第4行：产品报废损失明细图表 - 占据整行宽度 -->
      <div class="chart-container" style="grid-column: 1 / -1;">
        <h3>6. 产品报废损失明细</h3>
        <div ref="scrapLossChart" class="chart"></div>
      </div>

      <!-- 第5行：产品质量异议损失明细图表 - 占据整行宽度 -->
      <div class="chart-container" style="grid-column: 1 / -1;">
        <h3>7. 产品质量异议损失明细</h3>
        <div ref="qualityObjectionChart" class="chart"></div>
      </div>

      <!-- 第7行：控制成本 vs 失败成本对比（占满整行） -->
      <div class="chart-container" style="grid-column: 1 / -1;">
        <h3>8. "控制成本" vs "失败成本" 对比</h3>
        <div ref="comboChart" class="chart"></div>
      </div>

      <!-- 第8行：各分厂改判汇总图表 - 占据整行宽度 -->
      <div class="chart-container" style="grid-column: 1 / -1;">
        <h3>9. 各分厂改判汇总</h3>
        <div ref="factoryRejectionChart" class="chart"></div>
      </div>

      <!-- 第9行：各分厂报废汇总图表 - 占据整行宽度 -->
      <div class="chart-container" style="grid-column: 1 / -1;">
        <h3>10. 各分厂报废汇总</h3>
        <div ref="factoryScrapChart" class="chart"></div>
      </div>

      <!-- 第10行：各分厂脱合同汇总图表 - 占据整行宽度 -->
      <div class="chart-container" style="grid-column: 1 / -1;">
        <h3>11. 各分厂脱合同汇总</h3>
        <div ref="factoryContractChart" class="chart"></div>
      </div>

      <!-- 第11行：各分厂退货汇总图表 - 占据整行宽度 -->
      <div class="chart-container" style="grid-column: 1 / -1;">
        <h3>12. 各分厂退货汇总</h3>
        <div ref="factoryReturnChart" class="chart"></div>
      </div>

    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { costCenterlist } from "@/api/qualityCost/qualityCostDetail";
import { getPieChartData, getMultiLineChartData, getQualityCostDetail, getExternalCostDetail, getInternalCostDetail, getComboChartDetail,getWaterfallChartDetail,getScrapLossChartDetailsDetail,getQualityObjectionLossDetail,getFactoryRejectionChartDetail,getFactoryScrapChartDetail,getFactoryContractChartDetail,getFactoryReturnChartDetail } from "@/api/qualityCost/dashboard";

export default {
  name: 'QualityCostDashboard',
  data() {
    // 获取默认会计期（上个月）
    const getDefaultYearMonth = () => {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1; // 1-12
      const day = now.getDate();
      const hour = now.getHours();

      // 如果今天是本月25号8点前（含25号7:59），则用上个月
      if (day < 28 || (day === 28 && hour < 1)) {
        // 处理1月时的跨年
        const prevMonth = month === 1 ? 12 : month - 1;
        const prevYear = month === 1 ? year - 1 : year;
        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;
      } else {
        return `${year}-${String(month).padStart(2, '0')}`;
      }
    };

    return {
      updateTime: '2023-10-27 10:00',
      charts: {},
      // 成本中心和会计期
      costCenter: '',
      accountingPeriod: getDefaultYearMonth(),
      // 质量成本类型，默认值为1（不含列入项）
      containType: 1,
      // 成本中心选项
      costCenterOptions: [],
      costCenterLoading: false,
      qualityCostDetail: {},
      qualityCostData: {}
    }
  },
  watch: {
    // 监听成本中心变化
    costCenter: {
      handler() {
        console.log('成本中心变化:', this.costCenter);
        this.refreshChartData();
      }
    },
    // 监听会计期变化
    accountingPeriod: {
      handler() {
        console.log('会计期变化:', this.accountingPeriod);
        this.refreshChartData();
      }
    },
    // 监听质量成本类型变化
    containType: {
      handler() {
        console.log('质量成本类型变化:', this.containType);
        this.refreshChartData();
      }
    }
  },
  mounted() {
    this.getCostCenterList();
    //质量成本四大类别占比

    this.initCharts();
    this.resizeObserver = new ResizeObserver(() => {
      this.resizeCharts()
    })
    this.resizeObserver.observe(this.$el)
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    // 销毁所有图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
    window.removeEventListener('resize', this.resizeCharts)
  },
  methods: {
    // 判断百分比是否为负数
    isNegativePercentage(percentage) {
      if (!percentage) return false;
      return percentage.toString().startsWith('-');
    },

    // 根据百分比正负值返回对应的CSS类
    getPercentageClass(percentage) {
      if (!percentage) return 'neutral';
      return this.isNegativePercentage(percentage) ? 'negative' : 'positive';
    },

    // 格式化数字，最多保留两位小数
    formatNumber(num) {
      if (num === null || num === undefined || num === '') {
        return '0';
      }
      const number = Number(num);
      if (isNaN(number)) {
        return '0';
      }
      // 使用toFixed(2)保留两位小数，然后用parseFloat去掉末尾的0
      return parseFloat(number.toFixed(2)).toString();
    },

    // 添加千分位分隔符
    addThousandSeparator(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },

    // 格式化产量/销量为万吨单位
    formatTonnage(num) {
      if (num === null || num === undefined || num === '') {
        return '0万吨';
      }
      const number = Number(num);
      if (isNaN(number)) {
        return '0万吨';
      }
      // 转换为万吨并保留两位小数，添加千分位分隔符
      const result = (number / 10000).toFixed(2);
      return `${this.addThousandSeparator(result)}万吨`;
    },

    // 格式化总金额为万元单位
    formatAmount(num) {
      if (num === null || num === undefined || num === '') {
        return '0万元';
      }
      const number = Number(num);
      if (isNaN(number)) {
        return '0万元';
      }
      // 转换为万元并保留两位小数，添加千分位分隔符
      const result = (number / 10000).toFixed(2);
      return `${this.addThousandSeparator(result)}万元`;
    },

    // 格式化吨钢成本为元/吨单位
    formatUnitCost(num) {
      if (num === null || num === undefined || num === '') {
        return '0元/吨';
      }
      const number = Number(num);
      if (isNaN(number)) {
        return '0元/吨';
      }
      // 保留两位小数并添加单位，添加千分位分隔符
      const result = number.toFixed(2);
      return `${this.addThousandSeparator(result)}元/吨`;
    },

    getFactoryRejectionChartDetail() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getFactoryRejectionChartDetail(params).then(response => {
        console.log('getFactoryRejectionChartDetail:', response);
        if (response.data) {
          // 更新WaterfallChart柱状图
          this.updateFactoryRejectionChart(response.data);
        }
      }).catch(error => {
        console.error('获取WaterfallChart数据失败:', error);
        this.$message.error('获取WaterfallChart数据失败');
      });
    },

    getFactoryScrapChartDetail() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getFactoryScrapChartDetail(params).then(response => {
        console.log('getFactoryScrapChartDetail:', response);
        if (response.data) {
          // 更新各分厂报废汇总柱状图
          this.updateFactoryScrapChart(response.data);
        }
      }).catch(error => {
        console.error('获取各分厂报废汇总数据失败:', error);
        this.$message.error('获取各分厂报废汇总数据失败');
      });
    },

    getFactoryContractChartDetail() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getFactoryContractChartDetail(params).then(response => {
        console.log('getFactoryContractChartDetail:', response);
        if (response.data) {
          // 更新各分厂脱合同汇总柱状图
          this.updateFactoryContractChart(response.data);
        }
      }).catch(error => {
        console.error('获取各分厂脱合同汇总数据失败:', error);
        this.$message.error('获取各分厂脱合同汇总数据失败');
      });
    },

    getFactoryReturnChartDetail() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getFactoryReturnChartDetail(params).then(response => {
        console.log('getFactoryReturnChartDetail:', response);
        if (response.data) {
          // 更新各分厂退货汇总柱状图
          this.updateFactoryReturnChart(response.data);
        }
      }).catch(error => {
        console.error('获取各分厂退货汇总数据失败:', error);
        this.$message.error('获取各分厂退货汇总数据失败');
      });
    },

    updateFactoryReturnChart(data) {
      if (this.charts.factoryReturnChart && data) {
        console.log('接收到的FactoryReturnChart数据:', data);
        console.log('factoryReturnMap:', data.factoryReturnMap);

        // 处理各分厂退货数据，单位为元
        const xAxisData = [];      // x轴分厂名称数据
        const seriesData = [];     // 柱状图数据
        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];

        if (data.factoryReturnMap) {
          // 将factoryReturnMap对象转换为数组，单位为元，保留两位小数
          const dataItems = Object.entries(data.factoryReturnMap).map(([key, value]) => ({
            name: key,    // 分厂名称
            value: (Number(value) || 0).toFixed(2)  // 退货金额，单位为元，保留两位小数
          }));

          console.log('处理后的分厂退货数据:', dataItems);

          if (dataItems.length > 0) {
            // 按数值从高到低排序
            dataItems.sort((a, b) => b.value - a.value);

            // 分离排序后的数据，确保每个柱子颜色不同
            dataItems.forEach((item, index) => {
              xAxisData.push(item.name);
              seriesData.push({
                value: item.value,
                itemStyle: { color: colors[index % colors.length] }
              });
            });
          } else {
            console.warn('没有找到有效的分厂退货数据');
            // 添加默认数据以便测试
            xAxisData.push('无数据');
            seriesData.push({
              value: 0,
              itemStyle: { color: colors[0] }
            });
          }
        } else {
          console.warn('factoryReturnMap数据不存在');
          // 添加默认数据以便测试
          xAxisData.push('无数据');
          seriesData.push({
            value: 0,
            itemStyle: { color: colors[0] }
          });
        }

        console.log('x轴分厂数据:', xAxisData);
        console.log('柱状图数据:', seriesData);

        // 更新图表配置
        const option = {
          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            backgroundColor: 'rgba(30, 41, 59, 0.9)',
            borderColor: '#93C5FD',
            textStyle: { color: '#fff' },
            formatter: function (params) {
              let result = params[0].name + '<br/>';
              params.forEach(function (item) {
                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';
              });
              return result;
            }
          },
          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              color: '#9CA3AF',
              interval: 0, // 显示所有标签
              rotate: 0, // 水平显示标签
              align: 'center' // 居中对齐
            },
            axisLine: { lineStyle: { color: '#4B5563' } }
          },
          yAxis: {
            type: 'value',
            name: '金额 (元)',
            nameTextStyle: { color: '#9CA3AF' },
            axisLabel: {
              color: '#9CA3AF',
              formatter: function(value) {
                // 在Y轴标签上也显示千分位分隔符
                return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              }
            },
            axisLine: { lineStyle: { color: '#4B5563' } },
            splitLine: { lineStyle: { color: '#374151' } }
          },
          series: [{
            name: '退货金额',
            type: 'bar',
            data: seriesData
          }]
        };

        this.charts.factoryReturnChart.setOption(option, true); // 使用true强制刷新
        console.log('FactoryReturnChart柱状图数据已更新');
      } else {
        console.error('FactoryReturnChart实例不存在或数据为空');
      }
    },

    updateFactoryRejectionChart(data) {
      if (this.charts.factoryRejectionChart && data) {
        console.log('接收到的FactoryRejectionChart数据:', data);
        console.log('factoryRejectionMap:', data.factoryRejectionMap);

        // 处理各分厂改判数据，单位为元
        const xAxisData = [];      // x轴分厂名称数据
        const seriesData = [];     // 柱状图数据
        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];

        if (data.factoryRejectionMap) {
          // 将factoryRejectionMap对象转换为数组，单位为元，保留两位小数
          const dataItems = Object.entries(data.factoryRejectionMap).map(([key, value]) => ({
            name: key,    // 分厂名称
            value: (Number(value) || 0).toFixed(2)  // 改判金额，单位为元，保留两位小数
          }));

          console.log('处理后的分厂改判数据:', dataItems);

          if (dataItems.length > 0) {
            // 按数值从高到低排序
            dataItems.sort((a, b) => b.value - a.value);

            // 分离排序后的数据，确保每个柱子颜色不同
            dataItems.forEach((item, index) => {
              xAxisData.push(item.name);
              seriesData.push({
                value: item.value,
                itemStyle: { color: colors[index % colors.length] }
              });
            });
          } else {
            console.warn('没有找到有效的分厂改判数据');
            // 添加默认数据以便测试
            xAxisData.push('无数据');
            seriesData.push({
              value: 0,
              itemStyle: { color: colors[0] }
            });
          }
        } else {
          console.warn('factoryRejectionMap数据不存在');
          // 添加默认数据以便测试
          xAxisData.push('无数据');
          seriesData.push({
            value: 0,
            itemStyle: { color: colors[0] }
          });
        }

        console.log('x轴分厂数据:', xAxisData);
        console.log('柱状图数据:', seriesData);

        // 更新图表配置
        const option = {
          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            backgroundColor: 'rgba(30, 41, 59, 0.9)',
            borderColor: '#93C5FD',
            textStyle: { color: '#fff' },
            formatter: function (params) {
              let result = params[0].name + '<br/>';
              params.forEach(function (item) {
                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';
              });
              return result;
            }
          },
          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              color: '#9CA3AF',
              interval: 0, // 显示所有标签
              rotate: 0, // 水平显示标签
              align: 'center' // 居中对齐
            },
            axisLine: { lineStyle: { color: '#4B5563' } }
          },
          yAxis: {
            type: 'value',
            name: '金额 (元)',
            nameTextStyle: { color: '#9CA3AF' },
            axisLabel: {
              color: '#9CA3AF',
              formatter: function(value) {
                // 在Y轴标签上也显示千分位分隔符
                return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              }
            },
            axisLine: { lineStyle: { color: '#4B5563' } },
            splitLine: { lineStyle: { color: '#374151' } }
          },
          series: [{
            name: '改判金额',
            type: 'bar',
            data: seriesData
          }]
        };

        this.charts.factoryRejectionChart.setOption(option, true); // 使用true强制刷新
        console.log('FactoryRejectionChart柱状图数据已更新');
      } else {
        console.error('FactoryRejectionChart实例不存在或数据为空');
      }
    },

    updateFactoryScrapChart(data) {
      if (this.charts.factoryScrapChart && data) {
        console.log('接收到的FactoryScrapChart数据:', data);
        console.log('factoryScrapMap:', data.factoryScrapMap);

        // 处理各分厂报废数据，单位为元
        const xAxisData = [];      // x轴分厂名称数据
        const seriesData = [];     // 柱状图数据
        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];

        if (data.factoryScrapMap) {
          // 将factoryScrapMap对象转换为数组，单位为元，保留两位小数
          const dataItems = Object.entries(data.factoryScrapMap).map(([key, value]) => ({
            name: key,    // 分厂名称
            value: (Number(value) || 0).toFixed(2)  // 报废金额，单位为元，保留两位小数
          }));

          console.log('处理后的分厂报废数据:', dataItems);

          if (dataItems.length > 0) {
            // 按数值从高到低排序
            dataItems.sort((a, b) => b.value - a.value);

            // 分离排序后的数据，确保每个柱子颜色不同
            dataItems.forEach((item, index) => {
              xAxisData.push(item.name);
              seriesData.push({
                value: item.value,
                itemStyle: { color: colors[index % colors.length] }
              });
            });
          } else {
            console.warn('没有找到有效的分厂报废数据');
            // 添加默认数据以便测试
            xAxisData.push('无数据');
            seriesData.push({
              value: 0,
              itemStyle: { color: colors[0] }
            });
          }
        } else {
          console.warn('factoryScrapMap数据不存在');
          // 添加默认数据以便测试
          xAxisData.push('无数据');
          seriesData.push({
            value: 0,
            itemStyle: { color: colors[0] }
          });
        }

        console.log('x轴分厂数据:', xAxisData);
        console.log('柱状图数据:', seriesData);

        // 更新图表配置
        const option = {
          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            backgroundColor: 'rgba(30, 41, 59, 0.9)',
            borderColor: '#93C5FD',
            textStyle: { color: '#fff' },
            formatter: function (params) {
              let result = params[0].name + '<br/>';
              params.forEach(function (item) {
                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';
              });
              return result;
            }
          },
          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              color: '#9CA3AF',
              interval: 0, // 显示所有标签
              rotate: 0, // 水平显示标签
              align: 'center' // 居中对齐
            },
            axisLine: { lineStyle: { color: '#4B5563' } }
          },
          yAxis: {
            type: 'value',
            name: '金额 (元)',
            nameTextStyle: { color: '#9CA3AF' },
            axisLabel: {
              color: '#9CA3AF',
              formatter: function(value) {
                // 在Y轴标签上也显示千分位分隔符
                return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              }
            },
            axisLine: { lineStyle: { color: '#4B5563' } },
            splitLine: { lineStyle: { color: '#374151' } }
          },
          series: [{
            name: '报废金额',
            type: 'bar',
            data: seriesData
          }]
        };

        this.charts.factoryScrapChart.setOption(option, true); // 使用true强制刷新
        console.log('FactoryScrapChart柱状图数据已更新');
      } else {
        console.error('FactoryScrapChart实例不存在或数据为空');
      }
    },

    updateFactoryContractChart(data) {
      if (this.charts.factoryContractChart && data) {
        console.log('接收到的FactoryContractChart数据:', data);
        console.log('factoryContractMap:', data.factoryContractMap);

        // 处理各分厂脱合同数据，单位为元
        const xAxisData = [];      // x轴分厂名称数据
        const seriesData = [];     // 柱状图数据
        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];

        if (data.factoryContractMap) {
          // 将factoryContractMap对象转换为数组，单位为元，保留两位小数
          const dataItems = Object.entries(data.factoryContractMap).map(([key, value]) => ({
            name: key,    // 分厂名称
            value: (Number(value) || 0).toFixed(2)  // 脱合同金额，单位为元，保留两位小数
          }));

          console.log('处理后的分厂脱合同数据:', dataItems);

          if (dataItems.length > 0) {
            // 按数值从高到低排序
            dataItems.sort((a, b) => b.value - a.value);

            // 分离排序后的数据，确保每个柱子颜色不同
            dataItems.forEach((item, index) => {
              xAxisData.push(item.name);
              seriesData.push({
                value: item.value,
                itemStyle: { color: colors[index % colors.length] }
              });
            });
          } else {
            console.warn('没有找到有效的分厂脱合同数据');
            // 添加默认数据以便测试
            xAxisData.push('无数据');
            seriesData.push({
              value: 0,
              itemStyle: { color: colors[0] }
            });
          }
        } else {
          console.warn('factoryContractMap数据不存在');
          // 添加默认数据以便测试
          xAxisData.push('无数据');
          seriesData.push({
            value: 0,
            itemStyle: { color: colors[0] }
          });
        }

        console.log('x轴分厂数据:', xAxisData);
        console.log('柱状图数据:', seriesData);

        // 更新图表配置
        const option = {
          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            backgroundColor: 'rgba(30, 41, 59, 0.9)',
            borderColor: '#93C5FD',
            textStyle: { color: '#fff' },
            formatter: function (params) {
              let result = params[0].name + '<br/>';
              params.forEach(function (item) {
                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';
              });
              return result;
            }
          },
          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              color: '#9CA3AF',
              interval: 0, // 显示所有标签
              rotate: 0, // 水平显示标签
              align: 'center' // 居中对齐
            },
            axisLine: { lineStyle: { color: '#4B5563' } }
          },
          yAxis: {
            type: 'value',
            name: '金额 (元)',
            nameTextStyle: { color: '#9CA3AF' },
            axisLabel: {
              color: '#9CA3AF',
              formatter: function(value) {
                // 在Y轴标签上也显示千分位分隔符
                return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              }
            },
            axisLine: { lineStyle: { color: '#4B5563' } },
            splitLine: { lineStyle: { color: '#374151' } }
          },
          series: [{
            name: '脱合同金额',
            type: 'bar',
            data: seriesData
          }]
        };

        this.charts.factoryContractChart.setOption(option, true); // 使用true强制刷新
        console.log('FactoryContractChart柱状图数据已更新');
      } else {
        console.error('FactoryContractChart实例不存在或数据为空');
      }
    },


    getWaterfallChartDetail() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        costCenter: this.costCenter,
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getWaterfallChartDetail(params).then(response => {
        console.log('getWaterfallChartDetail:', response);
        if (response.data) {
          // 更新WaterfallChart柱状图
          this.updateWaterfallChart(response.data);
        }
      }).catch(error => {
        console.error('获取WaterfallChart数据失败:', error);
        this.$message.error('获取WaterfallChart数据失败');
      });
    },

    // 更新WaterfallChart柱状图
    updateWaterfallChart(data) {
      if (this.charts.waterfallChart && data) {
        console.log('接收到的WaterfallChart数据:', data);

        // 处理rescueProject数据
        const xAxisData = [];      // x轴维度数据
        const seriesData = [];     // 柱状图数据
        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];

        let dataItems = [];

        if (data.rescueProject) {
          // 将rescueProject对象转换为数组，转换为万元
          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({
            name: key,    // 第一项为维度名称
            value: ((Number(value) || 0) / 10000).toFixed(2)  // 第二项为对应维度的值，转换为万元
          }));
        }

        console.log('处理后的数据项:', dataItems);

        if (dataItems.length > 0) {
          // 按数值从高到低排序
          dataItems.sort((a, b) => b.value - a.value);

          // 只取前十个最大的数据
          const topTenItems = dataItems.slice(0, 10);
          console.log('取前十个最大数据:', topTenItems);

          // 分离排序后的数据
          topTenItems.forEach((item, index) => {
            xAxisData.push(item.name);
            seriesData.push({
              value: item.value,
              itemStyle: { color: colors[index % colors.length] }
            });
          });
        } else {
          console.warn('没有找到有效的数据项');
          // 添加默认数据以便测试
          xAxisData.push('无数据');
          seriesData.push({
            value: 0,
            itemStyle: { color: colors[0] }
          });
        }

        console.log('x轴维度数据:', xAxisData);
        console.log('柱状图数据:', seriesData);

        // 更新图表配置
        const option = {
          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            backgroundColor: 'rgba(30, 41, 59, 0.9)',
            borderColor: '#93C5FD',
            textStyle: { color: '#fff' },
            formatter: function (params) {
              let result = params[0].name + '<br/>';
              params.forEach(function (item) {
                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';
              });
              return result;
            }
          },
          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              color: '#9CA3AF',
              interval: 0, // 显示所有标签
              rotate: 0, // 水平显示标签
              align: 'center' // 居中对齐
            },
            axisLine: { lineStyle: { color: '#4B5563' } }
          },
          yAxis: {
            type: 'value',
            name: '金额 (万元)',
            nameTextStyle: { color: '#9CA3AF' },
            axisLabel: { color: '#9CA3AF' },
            axisLine: { lineStyle: { color: '#4B5563' } },
            splitLine: { lineStyle: { color: '#374151' } }
          },
          series: [{
            name: '挽救处理成本',
            type: 'bar',
            data: seriesData
          }]
        };

        this.charts.waterfallChart.setOption(option, true); // 使用true强制刷新
        console.log('WaterfallChart柱状图数据已更新');
      } else {
        console.error('WaterfallChart实例不存在或数据为空');
      }
    },

    // 更新ScrapLossChart柱状图
    updateScrapLossChart(data) {
      if (this.charts.scrapLossChart && data) {
        console.log('接收到的ScrapLossChart数据:', data);
        console.log('数据类型:', typeof data);
        console.log('数据键:', Object.keys(data));

        // 处理报废损失数据，尝试多种可能的数据结构
        const xAxisData = [];      // x轴维度数据
        const seriesData = [];     // 柱状图数据
        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];
        let dataItems = [];

        // 尝试不同的数据结构，转换为万元
        if (data.scrapLossMap) {
          // 情况1: 使用scrapLossMap数据（根据实际API返回的数据结构）
          console.log('使用scrapLossMap数据');
          dataItems = Object.entries(data.scrapLossMap).map(([key, value]) => ({
            name: key,
            value: ((Number(value) || 0) / 10000).toFixed(2)
          }));
        } else if (data.rescueProject) {
          // 情况2: 使用rescueProject数据（与WaterfallChart相同）
          console.log('使用rescueProject数据');
          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({
            name: key,
            value: ((Number(value) || 0) / 10000).toFixed(2)
          }));
        } else if (data.scrapLoss) {
          // 情况3: 使用scrapLoss数据
          console.log('使用scrapLoss数据');
          dataItems = Object.entries(data.scrapLoss).map(([key, value]) => ({
            name: key,
            value: ((Number(value) || 0) / 10000).toFixed(2)
          }));
        } else if (data.scrapLossProject) {
          // 情况4: 使用scrapLossProject数据
          console.log('使用scrapLossProject数据');
          dataItems = Object.entries(data.scrapLossProject).map(([key, value]) => ({
            name: key,
            value: ((Number(value) || 0) / 10000).toFixed(2)
          }));
        } else {
          // 情况5: 直接使用data作为对象
          console.log('直接使用data对象');
          dataItems = Object.entries(data).map(([key, value]) => ({
            name: key,
            value: ((Number(value) || 0) / 10000).toFixed(2)
          }));
        }

        console.log('处理后的数据项:', dataItems);

        if (dataItems.length > 0) {
          // 按数值从高到低排序
          dataItems.sort((a, b) => b.value - a.value);

          // 只取前十个最大的数据
          const topTenItems = dataItems.slice(0, 10);
          console.log('取前十个最大数据:', topTenItems);

          // 分离排序后的数据
          topTenItems.forEach((item, index) => {
            xAxisData.push(item.name);
            seriesData.push({
              value: item.value,
              itemStyle: { color: colors[index % colors.length] }
            });
          });
        } else {
          console.warn('没有找到有效的数据项');
          // 添加默认数据以便测试
          xAxisData.push('无数据');
          seriesData.push({
            value: 0,
            itemStyle: { color: colors[0] }
          });
        }

        console.log('x轴维度数据:', xAxisData);
        console.log('柱状图数据:', seriesData);

        // 更新图表配置
        const option = {
          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            backgroundColor: 'rgba(30, 41, 59, 0.9)',
            borderColor: '#93C5FD',
            textStyle: { color: '#fff' },
            formatter: function (params) {
              let result = params[0].name + '<br/>';
              params.forEach(function (item) {
                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';
              });
              return result;
            }
          },
          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              color: '#9CA3AF',
              interval: 0, // 显示所有标签
              rotate: 0, // 水平显示标签
              align: 'center' // 居中对齐
            },
            axisLine: { lineStyle: { color: '#4B5563' } }
          },
          yAxis: {
            type: 'value',
            name: '金额 (万元)',
            nameTextStyle: { color: '#9CA3AF' },
            axisLabel: { color: '#9CA3AF' },
            axisLine: { lineStyle: { color: '#4B5563' } },
            splitLine: { lineStyle: { color: '#374151' } }
          },
          series: [{
            name: '报废损失成本',
            type: 'bar',
            data: seriesData
          }]
        };

        this.charts.scrapLossChart.setOption(option, true); // 使用true强制刷新
        console.log('ScrapLossChart柱状图数据已更新');
      } else {
        console.error('ScrapLossChart实例不存在或数据为空');
      }
    },

    // 更新QualityObjectionChart柱状图
    updateQualityObjectionChart(data) {
      if (this.charts.qualityObjectionChart && data) {
        console.log('接收到的QualityObjectionChart数据:', data);
        console.log('数据类型:', typeof data);
        console.log('数据键:', Object.keys(data));

        // 处理质量异议损失数据，尝试多种可能的数据结构
        const xAxisData = [];      // x轴维度数据
        const seriesData = [];     // 柱状图数据
        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];
        let dataItems = [];

        // 尝试不同的数据结构，转换为万元
        if (data.qualityObjectionLossMap) {
          // 情况1: 使用qualityObjectionLossMap数据
          console.log('使用qualityObjectionLossMap数据');
          dataItems = Object.entries(data.qualityObjectionLossMap).map(([key, value]) => ({
            name: key,
            value: ((Number(value) || 0) / 10000).toFixed(2)
          }));
        } else if (data.rescueProject) {
          // 情况2: 使用rescueProject数据（与WaterfallChart相同）
          console.log('使用rescueProject数据');
          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({
            name: key,
            value: ((Number(value) || 0) / 10000).toFixed(2)
          }));
        } else {
          // 情况3: 直接使用data作为对象
          console.log('直接使用data对象');
          dataItems = Object.entries(data).map(([key, value]) => ({
            name: key,
            value: ((Number(value) || 0) / 10000).toFixed(2)
          }));
        }

        console.log('处理后的数据项:', dataItems);

        if (dataItems.length > 0) {
          // 按数值从高到低排序
          dataItems.sort((a, b) => b.value - a.value);

          // 只取前十个最大的数据
          const topTenItems = dataItems.slice(0, 10);
          console.log('取前十个最大数据:', topTenItems);

          // 分离排序后的数据
          topTenItems.forEach((item, index) => {
            xAxisData.push(item.name);
            seriesData.push({
              value: item.value,
              itemStyle: { color: colors[index % colors.length] }
            });
          });
        } else {
          console.warn('没有找到有效的数据项');
          // 添加默认数据以便测试
          xAxisData.push('无数据');
          seriesData.push({
            value: 0,
            itemStyle: { color: colors[0] }
          });
        }

        console.log('x轴维度数据:', xAxisData);
        console.log('柱状图数据:', seriesData);

        // 更新图表配置
        const option = {
          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            backgroundColor: 'rgba(30, 41, 59, 0.9)',
            borderColor: '#93C5FD',
            textStyle: { color: '#fff' },
            formatter: function (params) {
              let result = params[0].name + '<br/>';
              params.forEach(function (item) {
                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';
              });
              return result;
            }
          },
          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              color: '#9CA3AF',
              interval: 0, // 显示所有标签
              rotate: 0, // 水平显示标签
              align: 'center' // 居中对齐
            },
            axisLine: { lineStyle: { color: '#4B5563' } }
          },
          yAxis: {
            type: 'value',
            name: '金额 (万元)',
            nameTextStyle: { color: '#9CA3AF' },
            axisLabel: { color: '#9CA3AF' },
            axisLine: { lineStyle: { color: '#4B5563' } },
            splitLine: { lineStyle: { color: '#374151' } }
          },
          series: [{
            name: '质量异议损失成本',
            type: 'bar',
            data: seriesData
          }]
        };

        this.charts.qualityObjectionChart.setOption(option, true); // 使用true强制刷新
        console.log('QualityObjectionChart柱状图数据已更新');
      } else {
        console.error('QualityObjectionChart实例不存在或数据为空');
      }
    },

    getQualityObjectionLossDetail() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        costCenter: this.costCenter,
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getQualityObjectionLossDetail(params).then(response => {
        console.log('getQualityObjectionLossDetail:', response);
        if (response.data) {
          // 更新QualityObjectionChart柱状图
          this.updateQualityObjectionChart(response.data);
        }
      }).catch(error => {
        console.error('获取QualityObjectionChart数据失败:', error);
        this.$message.error('获取产品质量异议损失明细数据失败');
      });
    },

    getScrapLossChartDetailsDetail() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        costCenter: this.costCenter,
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getScrapLossChartDetailsDetail(params).then(response => {
        console.log('getScrapLossChartDetailsDetail:', response);
        if (response.data) {
          // 更新ScrapLossChart柱状图
          this.updateScrapLossChart(response.data);
        }
      }).catch(error => {
        console.error('获取ScrapLossChart数据失败:', error);
        this.$message.error('获取产品报废损失明细数据失败');
      });
    },

    getExternalCostDetail() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        costCenter: this.costCenter,
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getExternalCostDetail(params).then(response => {
        console.log('getExternalCostDetail:', response);
        if (response.data) {
          // 更新外部损失成本构成图表
          this.updateExternalCostDetailChart(response.data);
        }
      }).catch(error => {
        // console.error('获取外部损失成本数据失败:', error);
        this.$message.error('获取外部损失成本数据失败');
      });
    },

    getInternalCostDetail() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        costCenter: this.costCenter,
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getInternalCostDetail(params).then(response => {
        console.log('getInternalCostDetail:', response);
        if (response.data) {
          // 更新内部损失成本构成图表
          this.updateInternalCostDetailChart(response.data);
        }
      }).catch(error => {
        this.$message.error('获取内部损失成本数据失败');
      });
    },

    // 更新内部损失成本构成图表
    updateInternalCostDetailChart(data) {
      if (this.charts.internalCostDetailChart && data) {
        console.log('接收到的内部损失成本数据:', data);

        // 收集所有数据项
        const allDataItems = [];
        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD'];

        // 处理各个成本项，收集到统一数组中，转换为万元
        if (data.contractionLoss) {
          Object.entries(data.contractionLoss).forEach(([key, value]) => {
            // 确保数值转换，包括0值也要显示，转换为万元
            const numValue = ((Number(value) || 0) / 10000).toFixed(2);
            allDataItems.push({ name: key, value: numValue });
          });
        }

        if (data.rescueCost) {
          Object.entries(data.rescueCost).forEach(([key, value]) => {
            // 确保数值转换，包括0值也要显示，转换为万元
            const numValue = ((Number(value) || 0) / 10000).toFixed(2);
            allDataItems.push({ name: key, value: numValue });
          });
        }

        if (data.revisionLoss) {
          Object.entries(data.revisionLoss).forEach(([key, value]) => {
            // 确保数值转换，包括0值也要显示，转换为万元
            const numValue = ((Number(value) || 0) / 10000).toFixed(2);
            allDataItems.push({ name: key, value: numValue });
          });
        }

        if (data.scrapLoss) {
          Object.entries(data.scrapLoss).forEach(([key, value]) => {
            // 确保数值转换，包括0值也要显示，转换为万元
            const numValue = ((Number(value) || 0) / 10000).toFixed(2);
            allDataItems.push({ name: key, value: numValue });
          });
        }

        console.log('收集到的所有数据项（包含0值）:', allDataItems);

        // 按数值从高到低排序（0值会排在负值之前，正值之后）
        allDataItems.sort((a, b) => b.value - a.value);

        console.log('排序后的数据（包含0值）:', allDataItems);

        // 分离排序后的数据，反转顺序使金额大的显示在上面
        const yAxisData = [];
        const seriesData = [];

        // 反转数组，使金额大的显示在图表上方
        allDataItems.reverse().forEach((item, index) => {
          yAxisData.push(item.name);
          seriesData.push({
            value: item.value,
            itemStyle: { color: colors[index % colors.length] }
          });
        });

        console.log('y轴数据:', yAxisData);
        console.log('系列数据（包含0值）:', seriesData);

        // 更新图表配置
        const option = {
          yAxis: {
            type: 'category',
            data: yAxisData,
            axisLabel: { color: '#9CA3AF' },
            axisLine: { lineStyle: { color: '#4B5563' } }
          },
          series: [{
            name: '金额 (万元)',
            type: 'bar',
            data: seriesData
          }]
        };

        this.charts.internalCostDetailChart.setOption(option);
        console.log('内部损失成本构成图表数据已更新（包含0值，按数值从高到低排序）');
      }
    },

    // 更新外部损失成本构成图表
    updateExternalCostDetailChart(data) {
      if (this.charts.externalCostDetailChart && data) {
        // 收集所有数据项
        const allDataItems = [];
        const colors = ['#FCA5A5', '#FDE68A', '#86EFAC', '#93C5FD', '#C4B5FD'];

        // 处理各个成本项，收集到统一数组中，转换为万元
        if (data.customerClaimCost) {
          Object.entries(data.customerClaimCost).forEach(([key, value]) => {
            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });
          });
        }

        if (data.qualityObjectionFeeCost) {
          Object.entries(data.qualityObjectionFeeCost).forEach(([key, value]) => {
            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });
          });
        }

        if (data.qualityObjectionTravelCost) {
          Object.entries(data.qualityObjectionTravelCost).forEach(([key, value]) => {
            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });
          });
        }

        if (data.returnLoss) {
          Object.entries(data.returnLoss).forEach(([key, value]) => {
            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });
          });
        }

        // 按数值从高到低排序
        allDataItems.sort((a, b) => b.value - a.value);

        // 分离排序后的数据，反转顺序使金额大的显示在上面
        const yAxisData = [];
        const seriesData = [];

        // 反转数组，使金额大的显示在图表上方
        allDataItems.reverse().forEach((item, index) => {
          yAxisData.push(item.name);
          seriesData.push({
            value: item.value,
            itemStyle: { color: colors[index % colors.length] }
          });
        });

        // 更新图表配置
        const option = {
          yAxis: {
            type: 'category',
            data: yAxisData,
            axisLabel: { color: '#9CA3AF' },
            axisLine: { lineStyle: { color: '#4B5563' } }
          },
          series: [{
            name: '金额 (万元)',
            type: 'bar',
            data: seriesData
          }]
        };

        this.charts.externalCostDetailChart.setOption(option);
        console.log('外部损失成本构成图表数据已更新（已按数值从高到低排序）');
      }
    },

    getQualityCostDetail() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        costCenter: this.costCenter,
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getQualityCostDetail(params).then(response => {
        console.log('getQualityCostDetail:', response);
        if (response.data) {
          this.qualityCostData = response.data.qualityCostData;
          this.qualityCostDetail = response.data;
        }
      }).catch(error => {
        // console.error('获取饼图数据失败:', error);
        this.$message.error('获取质量成本数据失败');
      });
    },

    //质量成本四大类别占比
    getMultiLineChartData() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        costCenter: this.costCenter,
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getMultiLineChartData(params).then(response => {
        console.log('getMultiLineChartData:', response);
        if (response.data) {
          this.updateMultiLineChart(response.data);
        }
      }).catch(error => {
        // console.error('获取饼图数据失败:', error);
        this.$message.error('获取质量成本数据失败');
      });
    },

    getComboChartDetail() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        costCenter: this.costCenter,
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };

      getComboChartDetail(params).then(response => {
        console.log('getComboChartDetail:', response);
        if (response.data) {
          this.updateComboChart(response.data);
        }
      }).catch(error => {
        this.$message.error('获取ComboChart数据失败');
      });
    },

    // 更新ComboChart图表
    updateComboChart(data) {
      if (this.charts.comboChart && data) {
        console.log('接收到的ComboChart数据:', data);

        // 基于会计期生成近6个月的月份标签作为x轴数据
        const months = this.generateComboChartMonthsByAccountingPeriod();
        console.log('生成的月份标签:', months);

        // 生成对应的年月格式用于数据匹配
        const yearMonths = this.generateYearMonthsByAccountingPeriod();
        console.log('生成的年月格式:', yearMonths);

        const failureCostData = [];     // 失败成本数据
        const controllingCostData = []; // 控制成本数据

        // 为每个月份提取对应的数值，转换为万元
        yearMonths.forEach(yearMonth => {
          // 获取失败成本数据，转换为万元
          const failureValue = data.failureCostMap && data.failureCostMap[yearMonth]
            ? ((Number(data.failureCostMap[yearMonth]) || 0) / 10000).toFixed(2)
            : 0;
          failureCostData.push(failureValue);

          // 获取控制成本数据，转换为万元
          const controllingValue = data.controllingCostMap && data.controllingCostMap[yearMonth]
            ? ((Number(data.controllingCostMap[yearMonth]) || 0) / 10000).toFixed(2)
            : 0;
          controllingCostData.push(controllingValue);
        });

        console.log('x轴月份数据:', months.map(month => `${month}月`));
        console.log('失败成本数据:', failureCostData);
        console.log('控制成本数据:', controllingCostData);

        // 更新图表配置
        const option = {
          // 图例配置 - 标注颜色对应的维度
          legend: {
            data: ['失败成本', '控制成本'], // 失败成本(红色#FCA5A5)，控制成本(绿色#86EFAC)
            textStyle: { color: '#E5E7EB' }
          },
          grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: months.map(month => `${month}月`), // 近6个月的月份
            axisLabel: {
              color: '#9CA3AF',
              rotate: 0, // 水平显示标签
              align: 'center' // 居中对齐
            },
            axisLine: { lineStyle: { color: '#4B5563' } }
          },
          yAxis: {
            type: 'value',
            name: '成本 (万元)',
            nameTextStyle: { color: '#9CA3AF' },
            axisLabel: { color: '#9CA3AF' },
            axisLine: { lineStyle: { color: '#4B5563' } },
            splitLine: { lineStyle: { color: '#374151' } }
          },

          series: [
            {
              name: '失败成本', // 红色曲线 #FCA5A5
              type: 'line',
              data: failureCostData,
              smooth: true, // 启用平滑曲线
              lineStyle: { color: '#FCA5A5', width: 3 },
              itemStyle: { color: '#FCA5A5' },
              symbol: 'circle',
              symbolSize: 6
            },
            {
              name: '控制成本', // 绿色曲线 #86EFAC
              type: 'line',
              data: controllingCostData,
              smooth: true, // 启用平滑曲线
              lineStyle: { color: '#86EFAC', width: 3 },
              itemStyle: { color: '#86EFAC' },
              symbol: 'circle',
              symbolSize: 6
            }
          ]
        };

        this.charts.comboChart.setOption(option);
        console.log('ComboChart图表数据已更新');
      }
    },

    // 生成ComboChart的月份标签（当前月份和之前的5个月）
    generateComboChartMonths() {
      const months = [];
      const currentDate = new Date();

      for (let i = 5; i >= 0; i--) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
        const month = date.getMonth() + 1;
        months.push(month);
      }

      return months;
    },

    // 生成对应的年月格式（当前月份和之前的5个月，如202501, 202502等）
    generateYearMonths() {
      const yearMonths = [];
      const currentDate = new Date();

      for (let i = 5; i >= 0; i--) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const yearMonth = `${year}${String(month).padStart(2, '0')}`;
        yearMonths.push(yearMonth);
      }

      return yearMonths;
    },

    // 基于会计期生成ComboChart的月份标签（会计期当前月份和之前的5个月）
    generateComboChartMonthsByAccountingPeriod() {
      const months = [];

      if (!this.accountingPeriod) {
        console.warn('会计期为空，使用系统当前时间');
        return this.generateComboChartMonths();
      }

      // 解析会计期 (格式: 2025-06)
      const [year, month] = this.accountingPeriod.split('-').map(Number);
      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始

      for (let i = 5; i >= 0; i--) {
        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);
        const monthNum = date.getMonth() + 1;
        months.push(monthNum);
      }

      return months;
    },

    // 基于会计期生成对应的年月格式（会计期当前月份和之前的5个月）
    generateYearMonthsByAccountingPeriod() {
      const yearMonths = [];

      if (!this.accountingPeriod) {
        console.warn('会计期为空，使用系统当前时间');
        return this.generateYearMonths();
      }

      // 解析会计期 (格式: 2025-06)
      const [year, month] = this.accountingPeriod.split('-').map(Number);
      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始

      for (let i = 5; i >= 0; i--) {
        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);
        const yearNum = date.getFullYear();
        const monthNum = date.getMonth() + 1;
        const yearMonth = `${yearNum}${String(monthNum).padStart(2, '0')}`;
        yearMonths.push(yearMonth);
      }

      return yearMonths;
    },

    //质量成本四大类别占比
    getPieChartData() {
      // 只有当成本中心和会计期都有值时才请求
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');
        return;
      }

      const params = {
        costCenter: this.costCenter,
        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506
        containType: this.containType
      };


      getPieChartData(params).then(response => {
        console.log('getPieChartData:', response);
        if (response.data) {
          this.updatePieChart(response.data);
        }
      }).catch(error => {
        console.error('获取饼图数据失败:', error);
        this.$message.error('获取质量成本数据失败');
      });
    },
    // 获取成本中心列表
    getCostCenterList() {
      this.costCenterLoading = true;
      costCenterlist().then(response => {
        this.costCenterOptions = response.data || [];
        // 如果有数据，设置默认选中第一个
        if (this.costCenterOptions.length > 0) {
          console.log('获取成本中心列表:', this.costCenterOptions);
          this.costCenter = this.costCenterOptions[0].key;
          // 设置默认值后，主动触发一次数据刷新
          this.$nextTick(() => {
            this.refreshChartData();
          });
        }
      }).catch(error => {
        console.error('获取成本中心列表失败:', error);
        this.$message.error('获取成本中心列表失败');
      }).finally(() => {
        this.costCenterLoading = false;
      });
    },

    // 更新饼图数据
    updatePieChart(data) {
      if (this.charts.pieChart && data) {
        // 更新饼图的数据，转换为万元
        const option = this.charts.pieChart.getOption();
        if (option && option.series && option.series[0]) {
          option.series[0].data = [
            { value: (data.preventionCost / 10000).toFixed(2), name: '预防成本', itemStyle: { color: '#93C5FD' } },
            { value: (data.appraisalCost / 10000).toFixed(2), name: '鉴定成本', itemStyle: { color: '#86EFAC' } },
            { value: (data.internalCost / 10000).toFixed(2), name: '内部损失成本', itemStyle: { color: '#FDE68A' } },
            { value: (data.externalCost / 10000).toFixed(2), name: '外部损失成本', itemStyle: { color: '#FCA5A5' } },
          ],
            this.charts.pieChart.setOption(option);
          // console.log('饼图数据已更新');
        }
      }
    },

    // 更新多线图数据
    updateMultiLineChart(data) {
      if (this.charts.multiLineChart && data) {
        // 基于会计期生成月份标签和对应的年月数字
        const months = this.generateComboChartMonthsByAccountingPeriod();
        const yearMonths = this.generateYearMonthsByAccountingPeriod();

        // 处理各种成本数据，转换为万元
        const preventionData = this.processMapData(data.preventionCostMap, yearMonths, true);
        const appraisalData = this.processMapData(data.appraisalCostMap, yearMonths, true);
        const internalData = this.processMapData(data.internalCostMap, yearMonths, true);
        const externalData = this.processMapData(data.externalCostMap, yearMonths, true);

        // 更新多线图的配置
        const option = {
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: months.map(month => `${month}月`), // 格式化为"X月"
            axisLabel: {
              color: '#9CA3AF',
              rotate: 0, // 水平显示标签
              align: 'center' // 居中对齐
            },
            axisLine: { lineStyle: { color: '#4B5563' } }
          },
          series: [
            {
              name: '预防成本',
              type: 'line',
              data: preventionData,
              smooth: true, // 启用平滑曲线
              lineStyle: { color: '#93C5FD', width: 3 },
              itemStyle: { color: '#93C5FD' },
              symbol: 'circle',
              symbolSize: 6
            },
            {
              name: '鉴定成本',
              type: 'line',
              data: appraisalData,
              smooth: true, // 启用平滑曲线
              lineStyle: { color: '#86EFAC', width: 3 },
              itemStyle: { color: '#86EFAC' },
              symbol: 'circle',
              symbolSize: 6
            },
            {
              name: '内部损失成本',
              type: 'line',
              data: internalData,
              smooth: true, // 启用平滑曲线
              lineStyle: { color: '#FDE68A', width: 3 },
              itemStyle: { color: '#FDE68A' },
              symbol: 'circle',
              symbolSize: 6
            },
            {
              name: '外部损失成本',
              type: 'line',
              data: externalData,
              smooth: true, // 启用平滑曲线
              lineStyle: { color: '#FCA5A5', width: 3 },
              itemStyle: { color: '#FCA5A5' },
              symbol: 'circle',
              symbolSize: 6
            }
          ]
        };

        this.charts.multiLineChart.setOption(option);
        console.log('多线图数据已更新');
      }
    },

    // 处理Map数据，根据年月匹配对应的值
    processMapData(costMap, yearMonths, convertToWanYuan = false) {
      if (!costMap) return new Array(yearMonths.length).fill(0);

      return yearMonths.map(yearMonth => {
        const value = costMap[yearMonth] || 0;
        return convertToWanYuan ? (value / 10000).toFixed(2) : value;
      });
    },

    // 生成月份标签（当前月份及前五个月份）
    generateMonthLabels() {
      const months = [];
      const yearMonths = [];
      const currentDate = new Date();

      for (let i = 5; i >= 0; i--) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
        const year = date.getFullYear();
        const month = date.getMonth() + 1;

        months.push(`${month}月`);
        yearMonths.push(parseInt(`${year}${String(month).padStart(2, '0')}`));
      }

      return { months, yearMonths };
    },

    // 刷新图表数据
    refreshChartData() {
      // 只有当成本中心和会计期都有值时才刷新
      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {
        return;
      }

      this.getQualityCostDetail();
      this.getPieChartData();
      this.getMultiLineChartData();
      this.getExternalCostDetail();
      this.getInternalCostDetail();
      this.getComboChartDetail();
      this.getWaterfallChartDetail();
      this.getScrapLossChartDetailsDetail();
      this.getQualityObjectionLossDetail();
      this.getFactoryRejectionChartDetail();
      this.getFactoryScrapChartDetail();
      this.getFactoryContractChartDetail();
      this.getFactoryReturnChartDetail();

      // 这里可以添加其他图表的数据刷新
      // this.$message.success(`已切换到成本中心: ${this.costCenter}, 会计期: ${this.accountingPeriod}`);
    },

    /** 查询按钮操作 */
    handleQuery() {
      this.refreshChartData();
    },

    /** 重置按钮操作 */
    resetQuery() {
      // 重置为默认值
      if (this.costCenterOptions.length > 0) {
        this.costCenter = this.costCenterOptions[0].key;
      }

      // 获取默认会计期
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth();
      const prevMonth = month === 0 ? 12 : month;
      const prevYear = month === 0 ? year - 1 : year;
      this.accountingPeriod = `${prevYear}-${String(prevMonth).padStart(2, '0')}`;

      this.$message.success('查询条件已重置');
    },

    initCharts() {
      const THEME = 'dark'

      // 定义商务风淡色系色彩方案
      this.businessColors = {
        light: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#7DD3FC', '#F9A8D4', '#BEF264'],
        gradient: [
          { offset: 0, color: '#3B82F6' },
          { offset: 1, color: '#1E40AF' }
        ]
      }

      // 初始化所有图表
      this.charts.pieChart = echarts.init(this.$refs.pieChart, THEME)
      this.charts.multiLineChart = echarts.init(this.$refs.multiLineChart, THEME)
      this.charts.externalCostDetailChart = echarts.init(this.$refs.externalCostDetailChart, THEME)
      this.charts.internalCostDetailChart = echarts.init(this.$refs.internalCostDetailChart, THEME)
      this.charts.waterfallChart = echarts.init(this.$refs.waterfallChart, THEME)
      this.charts.comboChart = echarts.init(this.$refs.comboChart, THEME)
      this.charts.scrapLossChart = echarts.init(this.$refs.scrapLossChart, THEME)
      this.charts.qualityObjectionChart = echarts.init(this.$refs.qualityObjectionChart, THEME)
      this.charts.factoryRejectionChart = echarts.init(this.$refs.factoryRejectionChart, THEME)
      this.charts.factoryScrapChart = echarts.init(this.$refs.factoryScrapChart, THEME)
      this.charts.factoryContractChart = echarts.init(this.$refs.factoryContractChart, THEME)
      this.charts.factoryReturnChart = echarts.init(this.$refs.factoryReturnChart, THEME)

      // 配置所有图表
      this.setPieChartOption()
      this.setMultiLineChartOption()
      this.setExternalCostDetailChartOption()
      this.setInternalCostDetailChartOption()
      this.setWaterfallChartOption()
      this.setComboChartOption()
      this.setScrapLossChartOption()
      this.setQualityObjectionChartOption()
      this.setFactoryRejectionChartOption()
      this.setFactoryScrapChartOption()
      this.setFactoryContractChartOption()
      this.setFactoryReturnChartOption()
    },

    setPieChartOption() {
      this.charts.pieChart.setOption({
        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const value = parseFloat(params.value).toFixed(2);
            const formattedValue = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            return `${params.seriesName} <br/>${params.name}: ${formattedValue}万元 (${params.percent}%)`;
          },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' }
        },
        legend: {
          top: 'bottom',
          left: 'center',
          textStyle: { color: '#E5E7EB', fontSize: 12 }
        },
        series: [{
          name: '成本类别',
          type: 'pie',
          radius: '65%',
          data: [],
          emphasis: {
            itemStyle: {
              shadowBlur: 15,
              shadowOffsetX: 0,
              shadowColor: 'rgba(147, 197, 253, 0.6)'
            }
          },
          labelLine: { lineStyle: { color: '#9CA3AF' } },
          label: {
            color: '#E5E7EB',
            formatter: (params) => {
              const value = parseFloat(params.value).toFixed(2);
              const formattedValue = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              return `${params.name}(${formattedValue}万元, ${params.percent}%)`;
            }
          }
        }]
      })
    },



    setComboChartOption() {
      this.charts.comboChart.setOption({
        color: ['#FCA5A5', '#86EFAC'],
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' }
        },
        legend: {
          data: ['失败成本', '控制成本'],
          textStyle: { color: '#E5E7EB' }
        },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
          axisLabel: {
            color: '#9CA3AF',
            rotate: 0, // 水平显示标签
            align: 'center' // 居中对齐
          },
          axisLine: { lineStyle: { color: '#4B5563' } }
        },
        yAxis: {
          type: 'value',
          name: '成本 (万元)',
          nameTextStyle: { color: '#9CA3AF' },
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } },
          splitLine: { lineStyle: { color: '#374151' } }
        },
        series: [
          {
            name: '失败成本', // 红色曲线 #FCA5A5
            type: 'line',
            data: [280, 260, 240, 220, 200, 180],
            smooth: true, // 启用平滑曲线
            lineStyle: { color: '#FCA5A5', width: 3 },
            itemStyle: { color: '#FCA5A5' },
            symbol: 'circle',
            symbolSize: 6
          },
          {
            name: '控制成本', // 绿色曲线 #86EFAC
            type: 'line',
            data: [120, 125, 130, 135, 140, 145],
            smooth: true, // 启用平滑曲线
            lineStyle: { color: '#86EFAC', width: 3 },
            itemStyle: { color: '#86EFAC' },
            symbol: 'circle',
            symbolSize: 6
          }
        ]
      })
    },

    setMultiLineChartOption() {
      this.charts.multiLineChart.setOption({
        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' }
        },
        legend: {
          data: ['预防成本', '鉴定成本', '内部损失成本', '外部损失成本'],
          textStyle: { color: '#E5E7EB' }
        },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
          axisLabel: {
            color: '#9CA3AF',
            rotate: 0, // 水平显示标签
            align: 'center' // 居中对齐
          },
          axisLine: { lineStyle: { color: '#4B5563' } }
        },
        yAxis: {
          type: 'value',
          name: '成本 (万元)',
          nameTextStyle: { color: '#9CA3AF' },
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } },
          splitLine: { lineStyle: { color: '#374151' } }
        },
        series: [
          {
            name: '预防成本',
            type: 'line',
            data: [80, 82, 85, 88, 90, 95],
            smooth: true, // 启用平滑曲线
            lineStyle: { color: '#93C5FD', width: 3 },
            itemStyle: { color: '#93C5FD' },
            symbol: 'circle',
            symbolSize: 6
          },
          {
            name: '鉴定成本',
            type: 'line',
            data: [120, 122, 125, 128, 130, 135],
            smooth: true, // 启用平滑曲线
            lineStyle: { color: '#86EFAC', width: 3 },
            itemStyle: { color: '#86EFAC' },
            symbol: 'circle',
            symbolSize: 6
          },
          {
            name: '内部损失成本',
            type: 'line',
            data: [450, 430, 410, 380, 350, 320],
            smooth: true, // 启用平滑曲线
            lineStyle: { color: '#FDE68A', width: 3 },
            itemStyle: { color: '#FDE68A' },
            symbol: 'circle',
            symbolSize: 6
          },
          {
            name: '外部损失成本',
            type: 'line',
            data: [350, 340, 310, 290, 260, 230],
            smooth: true, // 启用平滑曲线
            lineStyle: { color: '#FCA5A5', width: 3 },
            itemStyle: { color: '#FCA5A5' },
            symbol: 'circle',
            symbolSize: 6
          }
        ]
      })
    },





    setParetoChartOption() {
      this.charts.paretoChart.setOption({
        color: ['#93C5FD', '#FDE68A'],
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' }
        },
        grid: { right: '20%' },
        xAxis: [{
          type: 'category',
          data: ['产品报废', '产品改判', '设备故障', '工艺废料', '其他'],
          axisLabel: {
            interval: 0,
            rotate: 0, // 水平显示标签
            align: 'center', // 居中对齐
            color: '#9CA3AF'
          },
          axisLine: { lineStyle: { color: '#4B5563' } }
        }],
        yAxis: [
          {
            type: 'value',
            name: '损失金额(元)',
            nameTextStyle: { color: '#9CA3AF' },
            axisLabel: { color: '#9CA3AF' },
            axisLine: { lineStyle: { color: '#4B5563' } },
            splitLine: { lineStyle: { color: '#374151' } }
          },
          {
            type: 'value',
            name: '累计占比',
            min: 0,
            max: 100,
            axisLabel: {
              formatter: '{value} %',
              color: '#9CA3AF'
            },
            nameTextStyle: { color: '#9CA3AF' },
            axisLine: { lineStyle: { color: '#4B5563' } }
          }
        ],
        series: [
          {
            name: '损失金额',
            type: 'bar',
            data: [280, 110, 35, 20, 5],
            itemStyle: { color: '#93C5FD' }
          },
          {
            name: '累计占比',
            type: 'line',
            yAxisIndex: 1,
            data: [62.2, 86.7, 94.4, 98.9, 100],
            lineStyle: { color: '#FDE68A', width: 3 },
            itemStyle: { color: '#FDE68A' },
            symbol: 'circle',
            symbolSize: 8
          }
        ]
      })
    },

    setExternalCostDetailChartOption() {
      this.charts.externalCostDetailChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' },
          formatter: function (params) {
            let result = params[0].name + '<br/>';
            params.forEach(function (item) {
              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';
            });
            return result;
          }
        },
        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },
        xAxis: {
          type: 'value',
          name: '金额（万元）',
          nameTextStyle: { color: '#9CA3AF' },
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } },
          splitLine: { lineStyle: { color: '#374151' } }
        },
        yAxis: {
          type: 'category',

          data: [],
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } }
        },
        series: [{
          name: '金额 (元)',
          type: 'bar',
          data: []
        }]
      })
    },

    setInternalCostDetailChartOption() {
      this.charts.internalCostDetailChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' },
          formatter: function (params) {
            let result = params[0].name + '<br/>';
            params.forEach(function (item) {
              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';
            });
            return result;
          }
        },
        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },
        xAxis: {
          type: 'value',
          name: '金额（万元）',
          nameTextStyle: { color: '#9CA3AF' },
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } },
          splitLine: { lineStyle: { color: '#374151' } }
        },
        yAxis: {
          type: 'category',
          data: [],
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } }
        },
        series: [{
          name: '金额 (元)',
          type: 'bar',
          data: []
        }]
      })
    },

    setWaterfallChartOption() {
      this.charts.waterfallChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' },
        },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: {
          type: 'category',
          data: ['初始成本', '修磨', '矫直', '探伤', '热处理', '总成本'],
          axisLabel: {
            interval: 0,
            rotate: 0, // 水平显示标签
            align: 'center', // 居中对齐
            color: '#9CA3AF'
          },
          axisLine: { lineStyle: { color: '#4B5563' } }
        },
        yAxis: {
          type: 'value',
          name: '金额 (元)',
          nameTextStyle: { color: '#9CA3AF' },
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } },
          splitLine: { lineStyle: { color: '#374151' } }
        },
        series: [
          {
            name: '辅助',
            type: 'bar',
            stack: '总量',
            itemStyle: {
              color: 'rgba(0,0,0,0)',
              borderColor: 'rgba(0,0,0,0)',
              borderWidth: 0
            },
            emphasis: {
              itemStyle: {
                color: 'rgba(0,0,0,0)'
              }
            },
            data: [0, 0, 50, 80, 105, 0]
          },
        ]
      })
    },

    setScrapLossChartOption() {
      this.charts.scrapLossChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' },
        },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            color: '#9CA3AF',
            interval: 0, // 显示所有标签
            rotate: 0, // 水平显示标签
            align: 'center' // 居中对齐
          },
          axisLine: { lineStyle: { color: '#4B5563' } }
        },
        yAxis: {
          type: 'value',
          name: '金额 (元)',
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } },
          splitLine: { lineStyle: { color: '#374151' } }
        },
        series: [{
          name: '报废损失成本',
          type: 'bar',
          data: []
        }]
      })
    },

    setQualityObjectionChartOption() {
      this.charts.qualityObjectionChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' },
        },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            color: '#9CA3AF',
            interval: 0, // 显示所有标签
            rotate: 0, // 水平显示标签
            align: 'center' // 居中对齐
          },
          axisLine: { lineStyle: { color: '#4B5563' } }
        },
        yAxis: {
          type: 'value',
          name: '金额 (元)',
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } },
          splitLine: { lineStyle: { color: '#374151' } }
        },
        series: [{
          name: '质量异议损失成本',
          type: 'bar',
          data: []
        }]
      })
    },

    setFactoryRejectionChartOption() {
      this.charts.factoryRejectionChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' },
        },
        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: {
          type: 'category',
          data: [], // 动态数据，由updateFactoryRejectionChart方法填充
          axisLabel: {
            color: '#9CA3AF',
            interval: 0, // 显示所有标签
            rotate: 0, // 水平显示标签
            align: 'center' // 居中对齐
          },
          axisLine: { lineStyle: { color: '#4B5563' } }
        },
        yAxis: {
          type: 'value',
          name: '金额 (万元)',
          nameTextStyle: { color: '#9CA3AF' },
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } },
          splitLine: { lineStyle: { color: '#374151' } }
        },
        series: [{
          name: '改判金额',
          type: 'bar',
          data: [] // 动态数据，由updateFactoryRejectionChart方法填充
        }]
      })
    },

    setFactoryScrapChartOption() {
      this.charts.factoryScrapChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' },
        },
        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: {
          type: 'category',
          data: [], // 动态数据，由updateFactoryScrapChart方法填充
          axisLabel: {
            color: '#9CA3AF',
            interval: 0, // 显示所有标签
            rotate: 0, // 水平显示标签
            align: 'center' // 居中对齐
          },
          axisLine: { lineStyle: { color: '#4B5563' } }
        },
        yAxis: {
          type: 'value',
          name: '金额 (元)',
          nameTextStyle: { color: '#9CA3AF' },
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } },
          splitLine: { lineStyle: { color: '#374151' } }
        },
        series: [{
          name: '报废金额',
          type: 'bar',
          data: [] // 动态数据，由updateFactoryScrapChart方法填充
        }]
      })
    },

    setFactoryContractChartOption() {
      this.charts.factoryContractChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' },
        },
        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: {
          type: 'category',
          data: [], // 动态数据，由updateFactoryContractChart方法填充
          axisLabel: {
            color: '#9CA3AF',
            interval: 0, // 显示所有标签
            rotate: 0, // 水平显示标签
            align: 'center' // 居中对齐
          },
          axisLine: { lineStyle: { color: '#4B5563' } }
        },
        yAxis: {
          type: 'value',
          name: '金额 (元)',
          nameTextStyle: { color: '#9CA3AF' },
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } },
          splitLine: { lineStyle: { color: '#374151' } }
        },
        series: [{
          name: '脱合同金额',
          type: 'bar',
          data: [] // 动态数据，由updateFactoryContractChart方法填充
        }]
      })
    },

    setFactoryReturnChartOption() {
      this.charts.factoryReturnChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' },
        },
        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: {
          type: 'category',
          data: [], // 动态数据，由updateFactoryReturnChart方法填充
          axisLabel: {
            color: '#9CA3AF',
            interval: 0, // 显示所有标签
            rotate: 0, // 水平显示标签
            align: 'center' // 居中对齐
          },
          axisLine: { lineStyle: { color: '#4B5563' } }
        },
        yAxis: {
          type: 'value',
          name: '金额 (元)',
          nameTextStyle: { color: '#9CA3AF' },
          axisLabel: { color: '#9CA3AF' },
          axisLine: { lineStyle: { color: '#4B5563' } },
          splitLine: { lineStyle: { color: '#374151' } }
        },
        series: [{
          name: '退货金额',
          type: 'bar',
          data: [] // 动态数据，由updateFactoryReturnChart方法填充
        }]
      })
    },

    setDualYChartOption() {
      this.charts.dualYChart.setOption({
        color: ['#93C5FD', '#FDE68A'],
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
          backgroundColor: 'rgba(30, 41, 59, 0.9)',
          borderColor: '#93C5FD',
          textStyle: { color: '#fff' }
        },
        legend: {
          data: ['产量(吨)', '吨钢成本(元)'],
          textStyle: { color: '#E5E7EB' }
        },
        xAxis: [{
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月'],
          axisLabel: {
            color: '#9CA3AF',
            rotate: 0, // 水平显示标签
            align: 'center' // 居中对齐
          },
          axisLine: { lineStyle: { color: '#4B5563' } }
        }],
        yAxis: [
          {
            type: 'value',
            name: '产量(吨)',
            nameTextStyle: { color: '#9CA3AF' },
            axisLabel: { color: '#9CA3AF' },
            axisLine: { lineStyle: { color: '#4B5563' } },
            splitLine: { lineStyle: { color: '#374151' } }
          },
          {
            type: 'value',
            name: '吨钢成本(元)',
            nameTextStyle: { color: '#9CA3AF' },
            axisLabel: { color: '#9CA3AF' },
            axisLine: { lineStyle: { color: '#4B5563' } }
          }
        ],
        series: [
          {
            name: '产量(吨)',
            type: 'bar',
            data: [80000, 82000, 85000, 83000, 88000, 90000],
            itemStyle: { color: '#93C5FD' }
          },
          {
            name: '吨钢成本(元)',
            type: 'line',
            yAxisIndex: 1,
            data: [13.1, 12.8, 12.5, 12.6, 12.2, 12.0],
            lineStyle: { color: '#FDE68A', width: 3 },
            itemStyle: { color: '#FDE68A' },
            symbol: 'circle',
            symbolSize: 8
          }
        ]
      })
    },

    resizeCharts() {
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.resize()
        }
      })
    }
  }
}
</script>

<style scoped>
.quality-cost-dashboard {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  background-color: #111827;
  /* 深色背景 */
  color: #d1d5db;
  /* 浅色文字 */
  margin: 0;
  padding: 24px;
  min-height: 100vh;
}

.header {
  margin-bottom: 24px;
  text-align: center;
  position: relative;
}

.header-wrapper {
  display: inline-block;
  position: relative;
}

.header h1 {
  font-size: 28px;
  color: #f9fafb;
  /* 白色标题 */
  font-weight: 600;
  margin: 0;
  margin-bottom: 8px;
}

.header p {
  font-size: 16px;
  color: #9ca3af;
  /* 中灰色文字 */
  margin: 8px 0 0 0;
}

.header-filters {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: nowrap;
  justify-content: flex-start;
  margin-top: 12px;
  margin-left: 950px;
  /* 向左对齐 */
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item .label {
  color: #d1d5db;
  /* 浅色标签文字 */
  font-size: 14px;
  white-space: nowrap;
}

/* 右上角筛选区域的样式 */
.header-filters .el-input__inner,
.header-filters .el-select .el-input__inner {
  background-color: #111827; /* 与页面背景一致的深色 */
  border-color: #374151;
  color: #ffffff; /* 白色字体 */
}

.header-filters .el-input__inner:focus,
.header-filters .el-select .el-input__inner:focus {
  border-color: #93c5fd;
  background-color: #111827; /* 聚焦时保持背景色 */
}

.header-filters .el-select-dropdown {
  background-color: #111827; /* 下拉菜单背景与页面一致 */
  border-color: #374151;
}

.header-filters .el-select-dropdown .el-select-dropdown__item {
  color: #ffffff; /* 下拉选项白色字体 */
  background-color: #111827;
}

.header-filters .el-select-dropdown .el-select-dropdown__item:hover {
  background-color: #1f2937; /* 悬浮时稍微亮一点 */
  color: #ffffff;
}

.header-filters .el-select-dropdown .el-select-dropdown__item.selected {
  background-color: #374151; /* 选中项背景 */
  color: #ffffff;
}

/* 下拉框箭头颜色 */
.header-filters .el-select .el-input__suffix {
  color: #ffffff;
}

.header-filters .el-select .el-select__caret {
  color: #ffffff;
}

/* 占位符文字颜色 */
.header-filters .el-input__inner::placeholder {
  color: #9ca3af;
}

/* 清除按钮颜色 */
.header-filters .el-select .el-select__clear {
  color: #9ca3af;
}

.header-filters .el-select .el-select__clear:hover {
  color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-filters {
    flex-wrap: wrap;
    gap: 8px;
    margin-left: 0;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .header {
    text-align: center;
  }

  .header-filters {
    flex-direction: column;
    gap: 12px;
    align-items: center;
    margin-left: 0;
    /* 在小屏幕上取消左边距 */
  }

  .filter-item {
    justify-content: center;
  }
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 改为四列布局，支持1/4和3/4分配 */
  gap: 24px;
}

/* 响应式设计：在小屏幕上改为单列 */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 1fr; /* 小屏幕时改为单列 */
  }

  /* 小屏幕时重置所有grid-column样式 */
  .dashboard-grid .chart-container {
    grid-column: 1 !important;
  }
}

.chart-container,
.kpi-card {
  background-color: #1f2937;
  /* 深色卡片背景 */
  border-radius: 8px;
  border: 1px solid #374151;
  /* 边框 */
  box-shadow: none;
  padding: 24px;
  display: flex;
  flex-direction: column;
}

.chart-container {
  height: 400px;
}

/* 大图表样式 - 用于两两排列的图表 */
.chart-container.large-chart {
  height: 500px; /* 增加高度 */
  min-height: 500px;
}

.chart-container h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
  color: #f9fafb;
  /* 白色卡片标题 */
}

.chart {
  width: 100%;
  flex-grow: 1;
}

.kpi-grid {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.kpi-card {
  justify-content: space-between;
}

.kpi-card .title {
  font-size: 14px;
  color: #9ca3af;
  margin-bottom: 8px;
}

.kpi-card .value {
  font-size: 32px;
  font-weight: 700;
  color: #f9fafb;
}

.kpi-card .comparison {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-top: 8px;
}

.kpi-card .comparison .arrow {
  width: 20px;
  height: 20px;
  margin-right: 4px;
  stroke-width: 2.5px;
}

.kpi-card .comparison .positive {
  color: #34d399;
  /* 亮绿色 */
}

.kpi-card .comparison .negative {
  color: #f87171;
  /* 亮红色 */
}
</style>
