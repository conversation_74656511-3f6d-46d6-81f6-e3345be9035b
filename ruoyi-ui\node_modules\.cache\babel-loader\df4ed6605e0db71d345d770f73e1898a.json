{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPlan\\chartMethods.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPlan\\chartMethods.js", "mtime": 1756456493837}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_default", "exports", "default", "methods", "getCokingCoalMaterialColorMap", "baseColors", "allMaterialTypes", "inventoryData", "cokingCoalInventoryData", "for<PERSON>ach", "item", "materialName", "class2Name", "includes", "push", "sort", "colorMap", "index", "length", "reinitChart", "chartId", "chartDom", "document", "getElementById", "console", "error", "concat", "chartInstances", "intervalId", "clearInterval", "existingInstance", "getInstanceByDom", "dispose", "log", "err", "innerHTML", "new<PERSON>hart", "init", "initMonthlyInventoryChart", "timestamp", "Date", "toLocaleTimeString", "myChart", "orderData", "orderToReceiptData", "warn", "xAxisData", "map", "period", "yAxisData", "avgDays", "midDaysData", "midDays", "seriesData", "name", "type", "data", "smooth", "symbol", "symbolSize", "lineStyle", "width", "color", "itemStyle", "borderColor", "borderWidth", "areaStyle", "x", "y", "x2", "y2", "colorStops", "offset", "connectNulls", "option", "backgroundColor", "tooltip", "trigger", "axisPointer", "crossStyle", "formatter", "params", "tooltipText", "param", "value", "undefined", "marker", "midValue", "dataIndex", "legend", "show", "grid", "left", "right", "bottom", "top", "containLabel", "xAxis", "axisLine", "axisLabel", "interval", "rotate", "splitLine", "yAxis", "nameTextStyle", "fontSize", "align", "series", "setOption", "initFactoryStockChart", "monthNames", "mockOrderDaysData", "mockMidDaysData", "seriesName", "initCokingCoalLineChart", "initMaterialStatisticsChart", "initOverdueChart", "overdueData", "materialTypes", "materialType", "overdueNotReceivedData", "overdueNotReceived", "overdueNotUsedData", "overdueNotUsed", "axisValueLabel", "textStyle", "borderRadius", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/views/purchaseDashboardPlan/chartMethods.js"], "sourcesContent": ["import * as echarts from 'echarts'\r\n\r\nexport default {\r\n  methods: {\r\n    // 获取矿焦煤物料类型的颜色映射\r\n    getCokingCoalMaterialColorMap() {\r\n      // 使用月度库存金额图表的颜色方案\r\n      const baseColors = ['#0066ff', '#00ff00', '#ff0000', '#8b00ff', '#ffff00', '#ffffff']\r\n\r\n      // 基于所有原始数据为每个物料类型分配固定颜色，确保过滤时颜色保持一致\r\n      const allMaterialTypes = []\r\n      const inventoryData = this.cokingCoalInventoryData || []\r\n\r\n      // 收集所有物料类型\r\n      inventoryData.forEach(item => {\r\n        const materialName = item.class2Name || '未知物料'\r\n        if (!allMaterialTypes.includes(materialName)) {\r\n          allMaterialTypes.push(materialName)\r\n        }\r\n      })\r\n\r\n      // 按字母顺序排序，确保颜色分配的一致性\r\n      allMaterialTypes.sort()\r\n\r\n      // 为每个物料类型分配固定颜色\r\n      const colorMap = {}\r\n      allMaterialTypes.forEach((materialName, index) => {\r\n        colorMap[materialName] = baseColors[index % baseColors.length]\r\n      })\r\n\r\n      return colorMap\r\n    },\r\n\r\n    // 清理并重新初始化图表\r\n    reinitChart(chartId) {\r\n      const chartDom = document.getElementById(chartId)\r\n      if (!chartDom) {\r\n        console.error(`找不到图表DOM: ${chartId}`)\r\n        return null\r\n      }\r\n\r\n      if (this.chartInstances[chartId] && this.chartInstances[chartId].intervalId) {\r\n        clearInterval(this.chartInstances[chartId].intervalId)\r\n        this.chartInstances[chartId].intervalId = null\r\n      }\r\n\r\n      const existingInstance = echarts.getInstanceByDom(chartDom)\r\n\r\n      if (existingInstance) {\r\n        try {\r\n          echarts.dispose(existingInstance)\r\n          console.log(`ECharts instance successfully disposed for: ${chartId}`)\r\n        } catch (err) {\r\n          console.error(`Error disposing ECharts instance for ${chartId}:`, err)\r\n          chartDom.innerHTML = ''\r\n        }\r\n      }\r\n\r\n      chartDom.innerHTML = '<div class=\"chart-placeholder\">数据加载中...</div>'\r\n      this.chartInstances[chartId] = null\r\n\r\n      try {\r\n        const newChart = echarts.init(chartDom)\r\n        this.chartInstances[chartId] = newChart\r\n        console.log(`创建新图表: ${chartId}`)\r\n        return newChart\r\n      } catch (err) {\r\n        console.error(`创建图表失败: ${chartId}`, err)\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">图表加载失败</div>'\r\n        return null\r\n      }\r\n    },\r\n\r\n    // 挂单至入库天数曲线图\r\n    initMonthlyInventoryChart() {\r\n      const timestamp = new Date().toLocaleTimeString()\r\n      console.log(`initMonthlyInventoryChart - 挂单至入库天数图表初始化 [${timestamp}]`)\r\n      const myChart = this.reinitChart('monthlyInventoryChart')\r\n      if (!myChart) {\r\n        console.error('initMonthlyInventoryChart - 图表初始化失败')\r\n        return\r\n      }\r\n      console.log('initMonthlyInventoryChart - 图表实例创建成功')\r\n\r\n      // 只使用真实数据，不使用模拟数据\r\n      const orderData = this.orderToReceiptData || []\r\n      console.log('initMonthlyInventoryChart - 使用的数据:', orderData)\r\n      console.log('initMonthlyInventoryChart - 数据长度:', orderData.length)\r\n\r\n      // 如果没有真实数据，直接返回，不渲染图表\r\n      if (orderData.length === 0) {\r\n        console.warn('initMonthlyInventoryChart - 没有真实数据，跳过图表渲染')\r\n        return\r\n      }\r\n      // 使用真实数据\r\n      console.log('initMonthlyInventoryChart - 使用真实数据')\r\n      console.log('initMonthlyInventoryChart - 第一条真实数据:', orderData[0])\r\n\r\n      const xAxisData = orderData.map(item => item.period)\r\n      const yAxisData = orderData.map(item => item.avgDays)\r\n      const midDaysData = orderData.map(item => item.midDays || 0)\r\n\r\n      console.log('initMonthlyInventoryChart - 提取的X轴数据:', xAxisData)\r\n      console.log('initMonthlyInventoryChart - 提取的Y轴数据:', yAxisData)\r\n      console.log('initMonthlyInventoryChart - 提取的中位数数据:', midDaysData)\r\n\r\n      // 构建单条曲线数据\r\n      const seriesData = [{\r\n        name: '挂单至入库天数',\r\n        type: 'line',\r\n        data: yAxisData,\r\n        smooth: true,\r\n        symbol: 'circle',\r\n        symbolSize: 8,\r\n        lineStyle: {\r\n          width: 3,\r\n          color: '#00BAFF'\r\n        },\r\n        itemStyle: {\r\n          color: '#00BAFF',\r\n          borderColor: '#fff',\r\n          borderWidth: 2\r\n        },\r\n        areaStyle: {\r\n          color: {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 0,\r\n            y2: 1,\r\n            colorStops: [{\r\n              offset: 0, color: 'rgba(0, 186, 255, 0.3)'\r\n            }, {\r\n              offset: 1, color: 'rgba(0, 186, 255, 0.1)'\r\n            }]\r\n          }\r\n        },\r\n        connectNulls: false\r\n      }]\r\n\r\n\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              if (param.value !== null && param.value !== undefined) {\r\n                tooltipText += `${param.marker}平均数: ${param.value}天<br/>`\r\n                // 显示中位数（从真实数据中获取）\r\n                const midValue = midDaysData[param.dataIndex]\r\n                if (midValue !== undefined && midValue !== null) {\r\n                  tooltipText += `${param.marker}中位数: ${midValue}天<br/>`\r\n                }\r\n              }\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          show: false\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '8%',\r\n          bottom: '10%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 0, // 强制显示所有标签\r\n            rotate: 45 // 旋转45度避免重叠\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '平均天数',\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: '{value}天'\r\n          },\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            align: 'right',\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: seriesData\r\n      }\r\n\r\n      myChart.setOption(option, true)\r\n      console.log('initMonthlyInventoryChart - 图表配置设置完成')\r\n    },\r\n\r\n    // 第二个挂单至入库天数曲线图（使用factoryStockChart容器）\r\n    initFactoryStockChart() {\r\n      console.log('initFactoryStockChart - 挂单至入库天数图表初始化')\r\n      const myChart = this.reinitChart('factoryStockChart')\r\n      if (!myChart) return\r\n\r\n      // 第二个图表暂时使用模拟数据\r\n      console.log('initFactoryStockChart - 使用模拟数据（暂无API）')\r\n      const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']\r\n      const mockOrderDaysData = [15, 18, 12, 20, 16, 14, 22, 19, 17, 13, 21, 16]\r\n      const mockMidDaysData = [13, 16, 11, 18, 14, 12, 20, 17, 15, 12, 19, 14]\r\n\r\n      // 构建单条曲线数据\r\n      const seriesData = [{\r\n        name: '挂单至入库天数',\r\n        type: 'line',\r\n        data: mockOrderDaysData,\r\n        smooth: true,\r\n        symbol: 'circle',\r\n        symbolSize: 8,\r\n        lineStyle: {\r\n          width: 3,\r\n          color: '#00BAFF'\r\n        },\r\n        itemStyle: {\r\n          color: '#00BAFF',\r\n          borderColor: '#fff',\r\n          borderWidth: 2\r\n        },\r\n        areaStyle: {\r\n          color: {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 0,\r\n            y2: 1,\r\n            colorStops: [{\r\n              offset: 0, color: 'rgba(0, 186, 255, 0.3)'\r\n            }, {\r\n              offset: 1, color: 'rgba(0, 186, 255, 0.1)'\r\n            }]\r\n          }\r\n        },\r\n        connectNulls: false\r\n      }]\r\n\r\n\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              if (param.value !== null && param.value !== undefined) {\r\n                tooltipText += `${param.marker}${param.seriesName}: ${param.value}天<br/>`\r\n                // 显示中位数\r\n                const midValue = mockMidDaysData[param.dataIndex]\r\n                if (midValue !== undefined && midValue !== null) {\r\n                  tooltipText += `📊 中位数: ${midValue}天<br/>`\r\n                }\r\n              }\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          show: false\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '8%',\r\n          bottom: '10%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: monthNames,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 0, // 强制显示所有标签\r\n            rotate: 45 // 旋转45度避免重叠\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '平均天数',\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: '{value}天'\r\n          },\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            align: 'right',\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: seriesData\r\n      }\r\n\r\n      myChart.setOption(option, true)\r\n    },\r\n\r\n    // 第三个挂单至入库天数曲线图（使用cokingCoalLineChart容器）\r\n    initCokingCoalLineChart() {\r\n      console.log('initCokingCoalLineChart - 第三个挂单至入库天数图表初始化')\r\n      const myChart = this.reinitChart('cokingCoalLineChart')\r\n      if (!myChart) return\r\n\r\n      // 第三个图表暂时使用模拟数据\r\n      console.log('initCokingCoalLineChart - 使用模拟数据（暂无API）')\r\n      const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']\r\n      const mockOrderDaysData = [15, 18, 12, 20, 16, 14, 22, 19, 17, 13, 21, 16]\r\n      const mockMidDaysData = [13, 16, 11, 18, 14, 12, 20, 17, 15, 12, 19, 14]\r\n\r\n      // 构建单条曲线数据\r\n      const seriesData = [{\r\n        name: '挂单至入库天数',\r\n        type: 'line',\r\n        data: mockOrderDaysData,\r\n        smooth: true,\r\n        symbol: 'circle',\r\n        symbolSize: 8,\r\n        lineStyle: {\r\n          width: 3,\r\n          color: '#00BAFF'\r\n        },\r\n        itemStyle: {\r\n          color: '#00BAFF',\r\n          borderColor: '#fff',\r\n          borderWidth: 2\r\n        },\r\n        areaStyle: {\r\n          color: {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 0,\r\n            y2: 1,\r\n            colorStops: [{\r\n              offset: 0, color: 'rgba(0, 186, 255, 0.3)'\r\n            }, {\r\n              offset: 1, color: 'rgba(0, 186, 255, 0.1)'\r\n            }]\r\n          }\r\n        },\r\n        connectNulls: false\r\n      }]\r\n\r\n\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              if (param.value !== null && param.value !== undefined) {\r\n                tooltipText += `${param.marker}${param.seriesName}: ${param.value}天<br/>`\r\n                // 显示中位数\r\n                const midValue = mockMidDaysData[param.dataIndex]\r\n                if (midValue !== undefined && midValue !== null) {\r\n                  tooltipText += `📊 中位数: ${midValue}天<br/>`\r\n                }\r\n              }\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          show: false\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '8%',\r\n          bottom: '10%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: monthNames,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 0, // 强制显示所有标签\r\n            rotate: 45 // 旋转45度避免重叠\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '平均天数',\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: '{value}天'\r\n          },\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            align: 'right',\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: seriesData\r\n      }\r\n\r\n      myChart.setOption(option, true)\r\n    },\r\n\r\n    // 物料入库统计图表（现在也是挂单至入库天数）\r\n    initMaterialStatisticsChart() {\r\n      console.log('initMaterialStatisticsChart - 第四个挂单至入库天数图表初始化')\r\n      const myChart = this.reinitChart('materialStatisticsChart')\r\n      if (!myChart) return\r\n\r\n      const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']\r\n\r\n      // 第四个图表暂时使用模拟数据\r\n      console.log('initMaterialStatisticsChart - 使用模拟数据（暂无API）')\r\n      const mockOrderDaysData = [15, 18, 12, 20, 16, 14, 22, 19, 17, 13, 21, 16]\r\n      const mockMidDaysData = [13, 16, 11, 18, 14, 12, 20, 17, 15, 12, 19, 14]\r\n\r\n      // 构建单条曲线数据\r\n      const seriesData = [{\r\n        name: '挂单至入库天数',\r\n        type: 'line',\r\n        data: mockOrderDaysData,\r\n        smooth: true,\r\n        symbol: 'circle',\r\n        symbolSize: 8,\r\n        lineStyle: {\r\n          width: 3,\r\n          color: '#00BAFF'\r\n        },\r\n        itemStyle: {\r\n          color: '#00BAFF',\r\n          borderColor: '#fff',\r\n          borderWidth: 2\r\n        },\r\n        areaStyle: {\r\n          color: {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 0,\r\n            y2: 1,\r\n            colorStops: [{\r\n              offset: 0, color: 'rgba(0, 186, 255, 0.3)'\r\n            }, {\r\n              offset: 1, color: 'rgba(0, 186, 255, 0.1)'\r\n            }]\r\n          }\r\n        },\r\n        connectNulls: false\r\n      }]\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              if (param.value !== null && param.value !== undefined) {\r\n                tooltipText += `${param.marker}${param.seriesName}: ${param.value}天<br/>`\r\n                // 显示中位数\r\n                const midValue = mockMidDaysData[param.dataIndex]\r\n                if (midValue !== undefined && midValue !== null) {\r\n                  tooltipText += `📊 中位数: ${midValue}天<br/>`\r\n                }\r\n              }\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          show: false\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '8%',\r\n          bottom: '10%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: monthNames,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 0, // 强制显示所有标签\r\n            rotate: 45 // 旋转45度避免重叠\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '平均天数',\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: '{value}天'\r\n          },\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            align: 'right',\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: seriesData\r\n      }\r\n\r\n      myChart.setOption(option, true)\r\n    },\r\n\r\n    // 实时超期数柱状图\r\n    initOverdueChart() {\r\n      console.log('initOverdueChart - 实时超期数图表初始化')\r\n      const myChart = this.reinitChart('overdueChart')\r\n      if (!myChart) return\r\n\r\n      const overdueData = this.overdueData || []\r\n\r\n      // 横坐标：物料类型\r\n      const materialTypes = overdueData.map(item => item.materialType)\r\n\r\n      // 两个系列的数据\r\n      const overdueNotReceivedData = overdueData.map(item => item.overdueNotReceived)\r\n      const overdueNotUsedData = overdueData.map(item => item.overdueNotUsed)\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].axisValueLabel + '<br/>'\r\n            params.forEach(param => {\r\n              tooltipText += `${param.marker}${param.seriesName}: ${param.value}件<br/>`\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['超期未入库数', '超期未领用数'],\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '8%',\r\n          bottom: '10%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: materialTypes,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 0,\r\n            rotate: 0\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '数量 (件)',\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: '{value}件'\r\n          },\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            align: 'right',\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '超期未入库数',\r\n            type: 'bar',\r\n            data: overdueNotReceivedData,\r\n            itemStyle: {\r\n              color: '#ff6b6b',\r\n              borderRadius: [4, 4, 0, 0]\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                color: '#ff5252',\r\n                borderRadius: [4, 4, 0, 0],\r\n                shadowBlur: 10,\r\n                shadowColor: 'rgba(255, 255, 255, 0.5)',\r\n                borderWidth: 2,\r\n                borderColor: '#fff'\r\n              }\r\n            }\r\n          },\r\n          {\r\n            name: '超期未领用数',\r\n            type: 'bar',\r\n            data: overdueNotUsedData,\r\n            itemStyle: {\r\n              color: '#ffa726',\r\n              borderRadius: [4, 4, 0, 0]\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                color: '#ff9800',\r\n                borderRadius: [4, 4, 0, 0],\r\n                shadowBlur: 10,\r\n                shadowColor: 'rgba(255, 255, 255, 0.5)',\r\n                borderWidth: 2,\r\n                borderColor: '#fff'\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      }\r\n\r\n      myChart.setOption(option, true)\r\n    },\r\n\r\n\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAkC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEnB;EACbC,OAAO,EAAE;IACP;IACAC,6BAA6B,WAA7BA,6BAA6BA,CAAA,EAAG;MAC9B;MACA,IAAMC,UAAU,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;;MAErF;MACA,IAAMC,gBAAgB,GAAG,EAAE;MAC3B,IAAMC,aAAa,GAAG,IAAI,CAACC,uBAAuB,IAAI,EAAE;;MAExD;MACAD,aAAa,CAACE,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC5B,IAAMC,YAAY,GAAGD,IAAI,CAACE,UAAU,IAAI,MAAM;QAC9C,IAAI,CAACN,gBAAgB,CAACO,QAAQ,CAACF,YAAY,CAAC,EAAE;UAC5CL,gBAAgB,CAACQ,IAAI,CAACH,YAAY,CAAC;QACrC;MACF,CAAC,CAAC;;MAEF;MACAL,gBAAgB,CAACS,IAAI,CAAC,CAAC;;MAEvB;MACA,IAAMC,QAAQ,GAAG,CAAC,CAAC;MACnBV,gBAAgB,CAACG,OAAO,CAAC,UAACE,YAAY,EAAEM,KAAK,EAAK;QAChDD,QAAQ,CAACL,YAAY,CAAC,GAAGN,UAAU,CAACY,KAAK,GAAGZ,UAAU,CAACa,MAAM,CAAC;MAChE,CAAC,CAAC;MAEF,OAAOF,QAAQ;IACjB,CAAC;IAED;IACAG,WAAW,WAAXA,WAAWA,CAACC,OAAO,EAAE;MACnB,IAAMC,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAACH,OAAO,CAAC;MACjD,IAAI,CAACC,QAAQ,EAAE;QACbG,OAAO,CAACC,KAAK,uCAAAC,MAAA,CAAcN,OAAO,CAAE,CAAC;QACrC,OAAO,IAAI;MACb;MAEA,IAAI,IAAI,CAACO,cAAc,CAACP,OAAO,CAAC,IAAI,IAAI,CAACO,cAAc,CAACP,OAAO,CAAC,CAACQ,UAAU,EAAE;QAC3EC,aAAa,CAAC,IAAI,CAACF,cAAc,CAACP,OAAO,CAAC,CAACQ,UAAU,CAAC;QACtD,IAAI,CAACD,cAAc,CAACP,OAAO,CAAC,CAACQ,UAAU,GAAG,IAAI;MAChD;MAEA,IAAME,gBAAgB,GAAGjC,OAAO,CAACkC,gBAAgB,CAACV,QAAQ,CAAC;MAE3D,IAAIS,gBAAgB,EAAE;QACpB,IAAI;UACFjC,OAAO,CAACmC,OAAO,CAACF,gBAAgB,CAAC;UACjCN,OAAO,CAACS,GAAG,gDAAAP,MAAA,CAAgDN,OAAO,CAAE,CAAC;QACvE,CAAC,CAAC,OAAOc,GAAG,EAAE;UACZV,OAAO,CAACC,KAAK,yCAAAC,MAAA,CAAyCN,OAAO,QAAKc,GAAG,CAAC;UACtEb,QAAQ,CAACc,SAAS,GAAG,EAAE;QACzB;MACF;MAEAd,QAAQ,CAACc,SAAS,GAAG,+CAA+C;MACpE,IAAI,CAACR,cAAc,CAACP,OAAO,CAAC,GAAG,IAAI;MAEnC,IAAI;QACF,IAAMgB,QAAQ,GAAGvC,OAAO,CAACwC,IAAI,CAAChB,QAAQ,CAAC;QACvC,IAAI,CAACM,cAAc,CAACP,OAAO,CAAC,GAAGgB,QAAQ;QACvCZ,OAAO,CAACS,GAAG,oCAAAP,MAAA,CAAWN,OAAO,CAAE,CAAC;QAChC,OAAOgB,QAAQ;MACjB,CAAC,CAAC,OAAOF,GAAG,EAAE;QACZV,OAAO,CAACC,KAAK,0CAAAC,MAAA,CAAYN,OAAO,GAAIc,GAAG,CAAC;QACxCb,QAAQ,CAACc,SAAS,GAAG,6CAA6C;QAClE,OAAO,IAAI;MACb;IACF,CAAC;IAED;IACAG,yBAAyB,WAAzBA,yBAAyBA,CAAA,EAAG;MAC1B,IAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;MACjDjB,OAAO,CAACS,GAAG,0GAAAP,MAAA,CAA8Ca,SAAS,MAAG,CAAC;MACtE,IAAMG,OAAO,GAAG,IAAI,CAACvB,WAAW,CAAC,uBAAuB,CAAC;MACzD,IAAI,CAACuB,OAAO,EAAE;QACZlB,OAAO,CAACC,KAAK,CAAC,qCAAqC,CAAC;QACpD;MACF;MACAD,OAAO,CAACS,GAAG,CAAC,sCAAsC,CAAC;;MAEnD;MACA,IAAMU,SAAS,GAAG,IAAI,CAACC,kBAAkB,IAAI,EAAE;MAC/CpB,OAAO,CAACS,GAAG,CAAC,oCAAoC,EAAEU,SAAS,CAAC;MAC5DnB,OAAO,CAACS,GAAG,CAAC,mCAAmC,EAAEU,SAAS,CAACzB,MAAM,CAAC;;MAElE;MACA,IAAIyB,SAAS,CAACzB,MAAM,KAAK,CAAC,EAAE;QAC1BM,OAAO,CAACqB,IAAI,CAAC,2CAA2C,CAAC;QACzD;MACF;MACA;MACArB,OAAO,CAACS,GAAG,CAAC,oCAAoC,CAAC;MACjDT,OAAO,CAACS,GAAG,CAAC,sCAAsC,EAAEU,SAAS,CAAC,CAAC,CAAC,CAAC;MAEjE,IAAMG,SAAS,GAAGH,SAAS,CAACI,GAAG,CAAC,UAAArC,IAAI;QAAA,OAAIA,IAAI,CAACsC,MAAM;MAAA,EAAC;MACpD,IAAMC,SAAS,GAAGN,SAAS,CAACI,GAAG,CAAC,UAAArC,IAAI;QAAA,OAAIA,IAAI,CAACwC,OAAO;MAAA,EAAC;MACrD,IAAMC,WAAW,GAAGR,SAAS,CAACI,GAAG,CAAC,UAAArC,IAAI;QAAA,OAAIA,IAAI,CAAC0C,OAAO,IAAI,CAAC;MAAA,EAAC;MAE5D5B,OAAO,CAACS,GAAG,CAAC,sCAAsC,EAAEa,SAAS,CAAC;MAC9DtB,OAAO,CAACS,GAAG,CAAC,sCAAsC,EAAEgB,SAAS,CAAC;MAC9DzB,OAAO,CAACS,GAAG,CAAC,uCAAuC,EAAEkB,WAAW,CAAC;;MAEjE;MACA,IAAME,UAAU,GAAG,CAAC;QAClBC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAEP,SAAS;QACfQ,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,QAAQ;QAChBC,UAAU,EAAE,CAAC;QACbC,SAAS,EAAE;UACTC,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE;QACT,CAAC;QACDC,SAAS,EAAE;UACTD,KAAK,EAAE,SAAS;UAChBE,WAAW,EAAE,MAAM;UACnBC,WAAW,EAAE;QACf,CAAC;QACDC,SAAS,EAAE;UACTJ,KAAK,EAAE;YACLP,IAAI,EAAE,QAAQ;YACdY,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJC,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,CAAC;YACLC,UAAU,EAAE,CAAC;cACXC,MAAM,EAAE,CAAC;cAAEV,KAAK,EAAE;YACpB,CAAC,EAAE;cACDU,MAAM,EAAE,CAAC;cAAEV,KAAK,EAAE;YACpB,CAAC;UACH;QACF,CAAC;QACDW,YAAY,EAAE;MAChB,CAAC,CAAC;MAIF,IAAMC,MAAM,GAAG;QACbC,eAAe,EAAE,aAAa;QAC9BC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXvB,IAAI,EAAE,OAAO;YACbwB,UAAU,EAAE;cACVjB,KAAK,EAAE;YACT;UACF,CAAC;UACDkB,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIC,WAAW,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC3B,IAAI,GAAG,OAAO;YAC1C2B,MAAM,CAACxE,OAAO,CAAC,UAAA0E,KAAK,EAAI;cACtB,IAAIA,KAAK,CAACC,KAAK,KAAK,IAAI,IAAID,KAAK,CAACC,KAAK,KAAKC,SAAS,EAAE;gBACrDH,WAAW,OAAAxD,MAAA,CAAOyD,KAAK,CAACG,MAAM,0BAAA5D,MAAA,CAAQyD,KAAK,CAACC,KAAK,gBAAQ;gBACzD;gBACA,IAAMG,QAAQ,GAAGpC,WAAW,CAACgC,KAAK,CAACK,SAAS,CAAC;gBAC7C,IAAID,QAAQ,KAAKF,SAAS,IAAIE,QAAQ,KAAK,IAAI,EAAE;kBAC/CL,WAAW,OAAAxD,MAAA,CAAOyD,KAAK,CAACG,MAAM,0BAAA5D,MAAA,CAAQ6D,QAAQ,gBAAQ;gBACxD;cACF;YACF,CAAC,CAAC;YACF,OAAOL,WAAW;UACpB;QACF,CAAC;QACDO,MAAM,EAAE;UACNC,IAAI,EAAE;QACR,CAAC;QACDC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbC,GAAG,EAAE,KAAK;UACVC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACL1C,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAEV,SAAS;UACfoD,QAAQ,EAAE;YACRtC,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF,CAAC;UACDqC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbsC,QAAQ,EAAE,CAAC;YAAE;YACbC,MAAM,EAAE,EAAE,CAAC;UACb,CAAC;UACDC,SAAS,EAAE;YACTZ,IAAI,EAAE;UACR;QACF,CAAC;QACDa,KAAK,EAAE;UACLhD,IAAI,EAAE,OAAO;UACbD,IAAI,EAAE,MAAM;UACZ6C,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbkB,SAAS,EAAE;UACb,CAAC;UACDwB,aAAa,EAAE;YACb1C,KAAK,EAAE,MAAM;YACb2C,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE;UACT,CAAC;UACDR,QAAQ,EAAE;YACRtC,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF,CAAC;UACDwC,SAAS,EAAE;YACT1C,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACD6C,MAAM,EAAEtD;MACV,CAAC;MAEDX,OAAO,CAACkE,SAAS,CAAClC,MAAM,EAAE,IAAI,CAAC;MAC/BlD,OAAO,CAACS,GAAG,CAAC,sCAAsC,CAAC;IACrD,CAAC;IAED;IACA4E,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;MACtBrF,OAAO,CAACS,GAAG,CAAC,sCAAsC,CAAC;MACnD,IAAMS,OAAO,GAAG,IAAI,CAACvB,WAAW,CAAC,mBAAmB,CAAC;MACrD,IAAI,CAACuB,OAAO,EAAE;;MAEd;MACAlB,OAAO,CAACS,GAAG,CAAC,uCAAuC,CAAC;MACpD,IAAM6E,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAC9F,IAAMC,iBAAiB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC1E,IAAMC,eAAe,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;MAExE;MACA,IAAM3D,UAAU,GAAG,CAAC;QAClBC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAEuD,iBAAiB;QACvBtD,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,QAAQ;QAChBC,UAAU,EAAE,CAAC;QACbC,SAAS,EAAE;UACTC,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE;QACT,CAAC;QACDC,SAAS,EAAE;UACTD,KAAK,EAAE,SAAS;UAChBE,WAAW,EAAE,MAAM;UACnBC,WAAW,EAAE;QACf,CAAC;QACDC,SAAS,EAAE;UACTJ,KAAK,EAAE;YACLP,IAAI,EAAE,QAAQ;YACdY,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJC,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,CAAC;YACLC,UAAU,EAAE,CAAC;cACXC,MAAM,EAAE,CAAC;cAAEV,KAAK,EAAE;YACpB,CAAC,EAAE;cACDU,MAAM,EAAE,CAAC;cAAEV,KAAK,EAAE;YACpB,CAAC;UACH;QACF,CAAC;QACDW,YAAY,EAAE;MAChB,CAAC,CAAC;MAIF,IAAMC,MAAM,GAAG;QACbC,eAAe,EAAE,aAAa;QAC9BC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXvB,IAAI,EAAE,OAAO;YACbwB,UAAU,EAAE;cACVjB,KAAK,EAAE;YACT;UACF,CAAC;UACDkB,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIC,WAAW,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC3B,IAAI,GAAG,OAAO;YAC1C2B,MAAM,CAACxE,OAAO,CAAC,UAAA0E,KAAK,EAAI;cACtB,IAAIA,KAAK,CAACC,KAAK,KAAK,IAAI,IAAID,KAAK,CAACC,KAAK,KAAKC,SAAS,EAAE;gBACrDH,WAAW,OAAAxD,MAAA,CAAOyD,KAAK,CAACG,MAAM,EAAA5D,MAAA,CAAGyD,KAAK,CAAC8B,UAAU,QAAAvF,MAAA,CAAKyD,KAAK,CAACC,KAAK,gBAAQ;gBACzE;gBACA,IAAMG,QAAQ,GAAGyB,eAAe,CAAC7B,KAAK,CAACK,SAAS,CAAC;gBACjD,IAAID,QAAQ,KAAKF,SAAS,IAAIE,QAAQ,KAAK,IAAI,EAAE;kBAC/CL,WAAW,wCAAAxD,MAAA,CAAe6D,QAAQ,gBAAQ;gBAC5C;cACF;YACF,CAAC,CAAC;YACF,OAAOL,WAAW;UACpB;QACF,CAAC;QACDO,MAAM,EAAE;UACNC,IAAI,EAAE;QACR,CAAC;QACDC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbC,GAAG,EAAE,KAAK;UACVC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACL1C,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAEsD,UAAU;UAChBZ,QAAQ,EAAE;YACRtC,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF,CAAC;UACDqC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbsC,QAAQ,EAAE,CAAC;YAAE;YACbC,MAAM,EAAE,EAAE,CAAC;UACb,CAAC;UACDC,SAAS,EAAE;YACTZ,IAAI,EAAE;UACR;QACF,CAAC;QACDa,KAAK,EAAE;UACLhD,IAAI,EAAE,OAAO;UACbD,IAAI,EAAE,MAAM;UACZ6C,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbkB,SAAS,EAAE;UACb,CAAC;UACDwB,aAAa,EAAE;YACb1C,KAAK,EAAE,MAAM;YACb2C,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE;UACT,CAAC;UACDR,QAAQ,EAAE;YACRtC,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF,CAAC;UACDwC,SAAS,EAAE;YACT1C,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACD6C,MAAM,EAAEtD;MACV,CAAC;MAEDX,OAAO,CAACkE,SAAS,CAAClC,MAAM,EAAE,IAAI,CAAC;IACjC,CAAC;IAED;IACAwC,uBAAuB,WAAvBA,uBAAuBA,CAAA,EAAG;MACxB1F,OAAO,CAACS,GAAG,CAAC,2CAA2C,CAAC;MACxD,IAAMS,OAAO,GAAG,IAAI,CAACvB,WAAW,CAAC,qBAAqB,CAAC;MACvD,IAAI,CAACuB,OAAO,EAAE;;MAEd;MACAlB,OAAO,CAACS,GAAG,CAAC,yCAAyC,CAAC;MACtD,IAAM6E,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAC9F,IAAMC,iBAAiB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC1E,IAAMC,eAAe,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;MAExE;MACA,IAAM3D,UAAU,GAAG,CAAC;QAClBC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAEuD,iBAAiB;QACvBtD,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,QAAQ;QAChBC,UAAU,EAAE,CAAC;QACbC,SAAS,EAAE;UACTC,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE;QACT,CAAC;QACDC,SAAS,EAAE;UACTD,KAAK,EAAE,SAAS;UAChBE,WAAW,EAAE,MAAM;UACnBC,WAAW,EAAE;QACf,CAAC;QACDC,SAAS,EAAE;UACTJ,KAAK,EAAE;YACLP,IAAI,EAAE,QAAQ;YACdY,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJC,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,CAAC;YACLC,UAAU,EAAE,CAAC;cACXC,MAAM,EAAE,CAAC;cAAEV,KAAK,EAAE;YACpB,CAAC,EAAE;cACDU,MAAM,EAAE,CAAC;cAAEV,KAAK,EAAE;YACpB,CAAC;UACH;QACF,CAAC;QACDW,YAAY,EAAE;MAChB,CAAC,CAAC;MAIF,IAAMC,MAAM,GAAG;QACbC,eAAe,EAAE,aAAa;QAC9BC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXvB,IAAI,EAAE,OAAO;YACbwB,UAAU,EAAE;cACVjB,KAAK,EAAE;YACT;UACF,CAAC;UACDkB,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIC,WAAW,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC3B,IAAI,GAAG,OAAO;YAC1C2B,MAAM,CAACxE,OAAO,CAAC,UAAA0E,KAAK,EAAI;cACtB,IAAIA,KAAK,CAACC,KAAK,KAAK,IAAI,IAAID,KAAK,CAACC,KAAK,KAAKC,SAAS,EAAE;gBACrDH,WAAW,OAAAxD,MAAA,CAAOyD,KAAK,CAACG,MAAM,EAAA5D,MAAA,CAAGyD,KAAK,CAAC8B,UAAU,QAAAvF,MAAA,CAAKyD,KAAK,CAACC,KAAK,gBAAQ;gBACzE;gBACA,IAAMG,QAAQ,GAAGyB,eAAe,CAAC7B,KAAK,CAACK,SAAS,CAAC;gBACjD,IAAID,QAAQ,KAAKF,SAAS,IAAIE,QAAQ,KAAK,IAAI,EAAE;kBAC/CL,WAAW,wCAAAxD,MAAA,CAAe6D,QAAQ,gBAAQ;gBAC5C;cACF;YACF,CAAC,CAAC;YACF,OAAOL,WAAW;UACpB;QACF,CAAC;QACDO,MAAM,EAAE;UACNC,IAAI,EAAE;QACR,CAAC;QACDC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbC,GAAG,EAAE,KAAK;UACVC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACL1C,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAEsD,UAAU;UAChBZ,QAAQ,EAAE;YACRtC,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF,CAAC;UACDqC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbsC,QAAQ,EAAE,CAAC;YAAE;YACbC,MAAM,EAAE,EAAE,CAAC;UACb,CAAC;UACDC,SAAS,EAAE;YACTZ,IAAI,EAAE;UACR;QACF,CAAC;QACDa,KAAK,EAAE;UACLhD,IAAI,EAAE,OAAO;UACbD,IAAI,EAAE,MAAM;UACZ6C,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbkB,SAAS,EAAE;UACb,CAAC;UACDwB,aAAa,EAAE;YACb1C,KAAK,EAAE,MAAM;YACb2C,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE;UACT,CAAC;UACDR,QAAQ,EAAE;YACRtC,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF,CAAC;UACDwC,SAAS,EAAE;YACT1C,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACD6C,MAAM,EAAEtD;MACV,CAAC;MAEDX,OAAO,CAACkE,SAAS,CAAClC,MAAM,EAAE,IAAI,CAAC;IACjC,CAAC;IAED;IACAyC,2BAA2B,WAA3BA,2BAA2BA,CAAA,EAAG;MAC5B3F,OAAO,CAACS,GAAG,CAAC,+CAA+C,CAAC;MAC5D,IAAMS,OAAO,GAAG,IAAI,CAACvB,WAAW,CAAC,yBAAyB,CAAC;MAC3D,IAAI,CAACuB,OAAO,EAAE;MAEd,IAAMoE,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;;MAE9F;MACAtF,OAAO,CAACS,GAAG,CAAC,6CAA6C,CAAC;MAC1D,IAAM8E,iBAAiB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC1E,IAAMC,eAAe,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;MAExE;MACA,IAAM3D,UAAU,GAAG,CAAC;QAClBC,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,MAAM;QACZC,IAAI,EAAEuD,iBAAiB;QACvBtD,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,QAAQ;QAChBC,UAAU,EAAE,CAAC;QACbC,SAAS,EAAE;UACTC,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE;QACT,CAAC;QACDC,SAAS,EAAE;UACTD,KAAK,EAAE,SAAS;UAChBE,WAAW,EAAE,MAAM;UACnBC,WAAW,EAAE;QACf,CAAC;QACDC,SAAS,EAAE;UACTJ,KAAK,EAAE;YACLP,IAAI,EAAE,QAAQ;YACdY,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJC,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,CAAC;YACLC,UAAU,EAAE,CAAC;cACXC,MAAM,EAAE,CAAC;cAAEV,KAAK,EAAE;YACpB,CAAC,EAAE;cACDU,MAAM,EAAE,CAAC;cAAEV,KAAK,EAAE;YACpB,CAAC;UACH;QACF,CAAC;QACDW,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,IAAMC,MAAM,GAAG;QACbC,eAAe,EAAE,aAAa;QAC9BC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXvB,IAAI,EAAE,OAAO;YACbwB,UAAU,EAAE;cACVjB,KAAK,EAAE;YACT;UACF,CAAC;UACDkB,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIC,WAAW,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC3B,IAAI,GAAG,OAAO;YAC1C2B,MAAM,CAACxE,OAAO,CAAC,UAAA0E,KAAK,EAAI;cACtB,IAAIA,KAAK,CAACC,KAAK,KAAK,IAAI,IAAID,KAAK,CAACC,KAAK,KAAKC,SAAS,EAAE;gBACrDH,WAAW,OAAAxD,MAAA,CAAOyD,KAAK,CAACG,MAAM,EAAA5D,MAAA,CAAGyD,KAAK,CAAC8B,UAAU,QAAAvF,MAAA,CAAKyD,KAAK,CAACC,KAAK,gBAAQ;gBACzE;gBACA,IAAMG,QAAQ,GAAGyB,eAAe,CAAC7B,KAAK,CAACK,SAAS,CAAC;gBACjD,IAAID,QAAQ,KAAKF,SAAS,IAAIE,QAAQ,KAAK,IAAI,EAAE;kBAC/CL,WAAW,wCAAAxD,MAAA,CAAe6D,QAAQ,gBAAQ;gBAC5C;cACF;YACF,CAAC,CAAC;YACF,OAAOL,WAAW;UACpB;QACF,CAAC;QACDO,MAAM,EAAE;UACNC,IAAI,EAAE;QACR,CAAC;QACDC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbC,GAAG,EAAE,KAAK;UACVC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACL1C,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAEsD,UAAU;UAChBZ,QAAQ,EAAE;YACRtC,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF,CAAC;UACDqC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbsC,QAAQ,EAAE,CAAC;YAAE;YACbC,MAAM,EAAE,EAAE,CAAC;UACb;QACF,CAAC;QACDE,KAAK,EAAE;UACLhD,IAAI,EAAE,OAAO;UACbD,IAAI,EAAE,MAAM;UACZ6C,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbkB,SAAS,EAAE;UACb,CAAC;UACDwB,aAAa,EAAE;YACb1C,KAAK,EAAE,MAAM;YACb2C,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE;UACT,CAAC;UACDR,QAAQ,EAAE;YACRtC,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF,CAAC;UACDwC,SAAS,EAAE;YACT1C,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACD6C,MAAM,EAAEtD;MACV,CAAC;MAEDX,OAAO,CAACkE,SAAS,CAAClC,MAAM,EAAE,IAAI,CAAC;IACjC,CAAC;IAED;IACA0C,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB5F,OAAO,CAACS,GAAG,CAAC,+BAA+B,CAAC;MAC5C,IAAMS,OAAO,GAAG,IAAI,CAACvB,WAAW,CAAC,cAAc,CAAC;MAChD,IAAI,CAACuB,OAAO,EAAE;MAEd,IAAM2E,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,EAAE;;MAE1C;MACA,IAAMC,aAAa,GAAGD,WAAW,CAACtE,GAAG,CAAC,UAAArC,IAAI;QAAA,OAAIA,IAAI,CAAC6G,YAAY;MAAA,EAAC;;MAEhE;MACA,IAAMC,sBAAsB,GAAGH,WAAW,CAACtE,GAAG,CAAC,UAAArC,IAAI;QAAA,OAAIA,IAAI,CAAC+G,kBAAkB;MAAA,EAAC;MAC/E,IAAMC,kBAAkB,GAAGL,WAAW,CAACtE,GAAG,CAAC,UAAArC,IAAI;QAAA,OAAIA,IAAI,CAACiH,cAAc;MAAA,EAAC;MAEvE,IAAMjD,MAAM,GAAG;QACbC,eAAe,EAAE,aAAa;QAC9BC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXvB,IAAI,EAAE;UACR,CAAC;UACDyB,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIC,WAAW,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC2C,cAAc,GAAG,OAAO;YACpD3C,MAAM,CAACxE,OAAO,CAAC,UAAA0E,KAAK,EAAI;cACtBD,WAAW,OAAAxD,MAAA,CAAOyD,KAAK,CAACG,MAAM,EAAA5D,MAAA,CAAGyD,KAAK,CAAC8B,UAAU,QAAAvF,MAAA,CAAKyD,KAAK,CAACC,KAAK,gBAAQ;YAC3E,CAAC,CAAC;YACF,OAAOF,WAAW;UACpB;QACF,CAAC;QACDO,MAAM,EAAE;UACNjC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;UAC1BqE,SAAS,EAAE;YACT/D,KAAK,EAAE;UACT,CAAC;UACDiC,GAAG,EAAE;QACP,CAAC;QACDJ,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbC,GAAG,EAAE,KAAK;UACVC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACL1C,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE8D,aAAa;UACnBpB,QAAQ,EAAE;YACRtC,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF,CAAC;UACDqC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbsC,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE;UACV;QACF,CAAC;QACDE,KAAK,EAAE;UACLhD,IAAI,EAAE,OAAO;UACbD,IAAI,EAAE,QAAQ;UACd6C,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbkB,SAAS,EAAE;UACb,CAAC;UACDwB,aAAa,EAAE;YACb1C,KAAK,EAAE,MAAM;YACb2C,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE;UACT,CAAC;UACDR,QAAQ,EAAE;YACRtC,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF,CAAC;UACDwC,SAAS,EAAE;YACT1C,SAAS,EAAE;cACTE,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACD6C,MAAM,EAAE,CACN;UACErD,IAAI,EAAE,QAAQ;UACdC,IAAI,EAAE,KAAK;UACXC,IAAI,EAAEgE,sBAAsB;UAC5BzD,SAAS,EAAE;YACTD,KAAK,EAAE,SAAS;YAChBgE,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UAC3B,CAAC;UACDC,QAAQ,EAAE;YACRhE,SAAS,EAAE;cACTD,KAAK,EAAE,SAAS;cAChBgE,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;cAC1BE,UAAU,EAAE,EAAE;cACdC,WAAW,EAAE,0BAA0B;cACvChE,WAAW,EAAE,CAAC;cACdD,WAAW,EAAE;YACf;UACF;QACF,CAAC,EACD;UACEV,IAAI,EAAE,QAAQ;UACdC,IAAI,EAAE,KAAK;UACXC,IAAI,EAAEkE,kBAAkB;UACxB3D,SAAS,EAAE;YACTD,KAAK,EAAE,SAAS;YAChBgE,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UAC3B,CAAC;UACDC,QAAQ,EAAE;YACRhE,SAAS,EAAE;cACTD,KAAK,EAAE,SAAS;cAChBgE,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;cAC1BE,UAAU,EAAE,EAAE;cACdC,WAAW,EAAE,0BAA0B;cACvChE,WAAW,EAAE,CAAC;cACdD,WAAW,EAAE;YACf;UACF;QACF,CAAC;MAEL,CAAC;MAEDtB,OAAO,CAACkE,SAAS,CAAClC,MAAM,EAAE,IAAI,CAAC;IACjC;EAGF;AACF,CAAC", "ignoreList": []}]}