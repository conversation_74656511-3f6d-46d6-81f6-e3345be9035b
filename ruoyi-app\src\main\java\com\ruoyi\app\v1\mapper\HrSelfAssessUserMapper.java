package com.ruoyi.app.v1.mapper;

import java.util.List;

import com.ruoyi.app.v1.domain.HrLateralAssessDept;
import com.ruoyi.app.v1.domain.HrSelfAssessUser;
import com.ruoyi.common.core.mapper.BaseSoMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 绩效考核-干部自评人员配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface HrSelfAssessUserMapper extends BaseSoMapper<HrSelfAssessUser>
{
    int insert(HrSelfAssessUser hrSelfAssessUser);

    int batchHrSelfAssessUserDept(List<HrSelfAssessUser> hrSelfAssessUsers);

    int batchHrSelfAssessUserLeader(List<HrSelfAssessUser> hrSelfAssessUsers);

    int deleteHrSelfAssessUserDept(String userId);

    int deleteHrSelfAssessUserLeader(String userId);

    Long[] selectHrSelfAssessUserDeptByUserId(String userId);

    String[] selectHrSelfAssessUserLeaderByUserId(String userId);
    // 查询待审批用户id列表
    String[] selectHrSelfAssessUsersByLeaderWorkNo(String workNo);

    List<HrLateralAssessDept> selectHrSelfAssessUserDeptListByUserId(String userId);

    // （弃用）
    HrSelfAssessUser getByWorkNo(String workNo);
    // 检验用户-部门唯一
    HrSelfAssessUser checkUserUnique(HrSelfAssessUser hrSelfAssessUser);

    // 根据用户信息查询（工号、部门id数组）
    HrSelfAssessUser getByUserInfo(HrSelfAssessUser hrSelfAssessUser);
    // 根据用户信息查询（工号、部门id）
    HrSelfAssessUser getByWorkNoDeptId(HrSelfAssessUser hrSelfAssessUser);

    HrSelfAssessUser getLeaderWorkNoByName(String name);

    List<HrSelfAssessUser> listAvailable(HrSelfAssessUser hrSelfAssessUser);

    // 获取自评填报用户部门
    List<HrLateralAssessDept> getDeptListByUser(HrSelfAssessUser user);

    // 查询用户审批部门角色是否匹配
    HrSelfAssessUser getAuth(HrSelfAssessUser hrSelfAssessUser);

    // 获取用户在指定考核年月的填写状态
    String getUserAssessStatus(@Param("workNo") String workNo, @Param("assessDate") String assessDate);
}
