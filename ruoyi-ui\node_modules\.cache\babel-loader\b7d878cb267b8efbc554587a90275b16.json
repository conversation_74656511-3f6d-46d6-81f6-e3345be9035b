{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\qualityCost\\scrapDetail.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\qualityCost\\scrapDetail.js", "mtime": 1756456493791}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listScrapDetail", "query", "request", "url", "method", "params", "listAllScrapDetail", "getScrapDetail", "recCreator", "addScrapDetail", "data", "updateScrapDetail", "delScrapDetail", "exportScrapDetail", "getScrapDetailByCenterAndPeriod", "getSum", "getAllSum"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/api/qualityCost/scrapDetail.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询内部损失成本-产品报废损失明细列表\r\nexport function listScrapDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/scrapDetail/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询内部损失成本-产品报废损失明细列表（全量）\r\nexport function listAllScrapDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/scrapDetail/listAll',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询内部损失成本-产品报废损失明细详细\r\nexport function getScrapDetail(recCreator) {\r\n  return request({\r\n    url: '/qualityCost/scrapDetail/' + recCreator,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增内部损失成本-产品报废损失明细\r\nexport function addScrapDetail(data) {\r\n  return request({\r\n    url: '/qualityCost/scrapDetail',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改内部损失成本-产品报废损失明细\r\nexport function updateScrapDetail(data) {\r\n  return request({\r\n    url: '/qualityCost/scrapDetail',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除内部损失成本-产品报废损失明细\r\nexport function delScrapDetail(recCreator) {\r\n  return request({\r\n    url: '/qualityCost/scrapDetail/' + recCreator,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 导出内部损失成本-产品报废损失明细\r\nexport function exportScrapDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/scrapDetail/export',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 按成本中心和会计期查询产品报废损失明细\r\nexport function getScrapDetailByCenterAndPeriod(query) {\r\n  return request({\r\n    url: '/qualityCost/scrapDetail/scrapDetailByCenterAndPeriod',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getSum(query) {\r\n  return request({\r\n    url: '/qualityCost/scrapDetail/getSum',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getAllSum(query) {\r\n  return request({\r\n    url: '/qualityCost/scrapDetail/getAllSum',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,kBAAkBA,CAACL,KAAK,EAAE;EACxC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,cAAcA,CAACC,UAAU,EAAE;EACzC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGK,UAAU;IAC7CJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iBAAiBA,CAACD,IAAI,EAAE;EACtC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,cAAcA,CAACJ,UAAU,EAAE;EACzC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGK,UAAU;IAC7CJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,iBAAiBA,CAACZ,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,+BAA+BA,CAACb,KAAK,EAAE;EACrD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uDAAuD;IAC5DC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASc,MAAMA,CAACd,KAAK,EAAE;EAC5B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASe,SAASA,CAACf,KAAK,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}