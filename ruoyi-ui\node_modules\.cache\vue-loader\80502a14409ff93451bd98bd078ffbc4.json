{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue", "mtime": 1756456493795}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEluZm9CeURhdGUsIHNhdmVJbmZvLCBzdWJtaXRJbmZvLCBkZWxJbmZvLCBsaXN0QmVBc3Nlc3NlZCB9IGZyb20gIkAvYXBpL2Fzc2Vzcy9zZWxmL2luZm8iOwovLyBpbXBvcnQgeyBiYXRjaFRhcmdldCwgbGlzdFNlbGZUYXJnZXRBbGwgfSBmcm9tICJAL2FwaS9hc3Nlc3Mvc2VsZi90YXJnZXQiOwppbXBvcnQgeyBnZXRSZXBvcnREZXB0TGlzdCwgZ2V0QnlXb3JrTm9EZXB0SWQgfSBmcm9tICJAL2FwaS9hc3Nlc3Mvc2VsZi91c2VyIjsKaW1wb3J0IHsgVnVlU2lnbmF0dXJlUGFkIH0gZnJvbSAndnVlLXNpZ25hdHVyZS1wYWQnOwoKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIFZ1ZVNpZ25hdHVyZVBhZAogIH0sCiAgbmFtZTogIlNlbGZBc3Nlc3NSZXBvcnQiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIG9wZW5TaWduOmZhbHNlLAogICAgICAvLyDnu6nmlYjogIPmoLgt6Ieq6K+E5oyH5qCH6YWN572u6KGo5qC85pWw5o2uCiAgICAgIGxpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHVzZXJJZDpudWxsLAogICAgICAgIHdvcmtObzogbnVsbCwKICAgICAgICBkZXB0SWQ6bnVsbCwKICAgICAgICBhc3Nlc3NEYXRlOiBudWxsLAogICAgICB9LAogICAgICAvLyDogIPmoLjlubTmnIjmlofmnKzmmL7npLoKICAgICAgYXNzZXNzRGF0ZVRleHQ6bnVsbCwKICAgICAgLy8g6YOo6Zeo5pi+56S6CiAgICAgIGRlcHROYW1lOm51bGwsCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgIH0sCiAgICAgIC8vIOeUqOaIt+S/oeaBrwogICAgICB1c2VySW5mbzp7fSwKICAgICAgLy8g5ZCI5bm25Y2V5YWD5qC85L+h5oGvCiAgICAgIHNwYW5MaXN0OnsKICAgICAgICBpdGVtTGlzdDpbXSwKICAgICAgICBzdGFuZGFyZExpc3Q6W10KICAgICAgfSwKICAgICAgLy8g5piv5ZCm5pi+56S66YeN572u5oyJ6ZKuCiAgICAgIHJlc2V0U2hvdzpmYWxzZSwKICAgICAgLy8g5piv5ZCm5Y+q6K+7CiAgICAgIHJlYWRPbmx5OmZhbHNlLAogICAgICAvLyDpg6jpl6jpgInpobkKICAgICAgZGVwdE9wdGlvbnM6W10sCiAgICAgIC8vIOiHquivhOS/oeaBr0lkCiAgICAgIGlkOm51bGwsCiAgICAgIC8vIOiHquivhOWIhuaVsAogICAgICBzZWxmU2NvcmU6MTAwLAogICAgICAvLyDnirbmgIEKICAgICAgc3RhdHVzOiIwIiwKICAgICAgLy8g6Ieq6K+E5L+h5oGvCiAgICAgIGluZm86e30sCiAgICAgIC8vIOaoquWQkeiiq+iAg+ivhOS/oeaBrwogICAgICBiZUFzc2Vzc2VkTGlzdDpbXSwKICAgICAgLy8g6YCA5Zue55CG55SxCiAgICAgIHJlamVjdFJlYXNvbjoiIiwKICAgICAgLy8g6Ieq6K+E562+5ZCNCiAgICAgIHNlbGZTaWduOiIiLAogICAgICAvLyDnrb7lkI3mnb/phY3nva4KICAgICAgc2lnbk9wdGlvbnM6IHsKICAgICAgICBvbkJlZ2luOiAoKSA9PiB0aGlzLiRyZWZzLnNpZ25hdHVyZVBhZC5yZXNpemVDYW52YXMoKSwKICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDEpJwogICAgICB9LAogICAgICBzaWduOiIiLAogICAgICBmaWxlOm51bGwsCiAgICAgIGZpbGVMaXN0OltdLAogICAgICB1cGxvYWQ6IHsKICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYAKICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2FwcC9jb21tb24vdXBsb2FkTWluaW8iLAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgfSwKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlID0gdGhpcy5nZXREZWZhdWx0QXNzZXNzRGF0ZSgpCiAgICB0aGlzLmFzc2Vzc0RhdGVUZXh0ID0gdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlLnJlcGxhY2UoIi0iLCIg5bm0ICIpICsgIiDmnIgiOwogICAgLy8gdGhpcy5nZXRTZWxmQXNzZXNzVXNlcigpOwogICAgdGhpcy5nZXRSZXBvcnREZXB0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewoKICAgIC8vIOiOt+WPlum7mOiupOiAg+aguOaXpeacnwogICAgZ2V0RGVmYXVsdEFzc2Vzc0RhdGUoKSB7CiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIGNvbnN0IGN1cnJlbnREYXkgPSBub3cuZ2V0RGF0ZSgpOwoKICAgICAgbGV0IHRhcmdldERhdGU7CiAgICAgIGlmIChjdXJyZW50RGF5IDwgMTApIHsKICAgICAgICAvLyDlvZPliY3ml6XmnJ/lsI/kuo4xMOaXpe+8jOm7mOiupOS4uuS4iuS4quaciAogICAgICAgIHRhcmdldERhdGUgPSBuZXcgRGF0ZShub3cuZ2V0RnVsbFllYXIoKSwgbm93LmdldE1vbnRoKCkgLSAxLCAxKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlvZPliY3ml6XmnJ/lpKfkuo7nrYnkuo4xMOaXpe+8jOm7mOiupOS4uuW9k+aciAogICAgICAgIHRhcmdldERhdGUgPSBuZXcgRGF0ZShub3cuZ2V0RnVsbFllYXIoKSwgbm93LmdldE1vbnRoKCksIDEpOwogICAgICB9CgogICAgICAvLyDmoLzlvI/ljJbkuLogWVlZWS1NIOagvOW8jwogICAgICBjb25zdCB5ZWFyID0gdGFyZ2V0RGF0ZS5nZXRGdWxsWWVhcigpOwogICAgICBjb25zdCBtb250aCA9IHRhcmdldERhdGUuZ2V0TW9udGgoKSArIDE7CiAgICAgIHJldHVybiBgJHt5ZWFyfS0ke21vbnRofWA7CiAgICB9LAoKICAgIC8vIOiOt+WPlumDqOmXqOS/oeaBrwogICAgZ2V0UmVwb3J0RGVwdExpc3QoKXsKICAgICAgZ2V0UmVwb3J0RGVwdExpc3QoKS50aGVuKHJlcyA9PiB7CiAgICAgICAgY29uc29sZS5sb2cocmVzKQogICAgICAgIGlmKHJlcy5jb2RlID09IDIwMCl7CiAgICAgICAgICB0aGlzLmhhbmRsZURlcHRMaXN0KHJlcy5kYXRhKTsKICAgICAgICAgIC8vIOagueaNrumDqOmXqOiOt+WPlueUqOaIt+S/oeaBrwogICAgICAgICAgdGhpcy5nZXRCeVdvcmtOb0RlcHRJZCgpOwogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvLyDojrflj5bnlKjmiLfkv6Hmga8KICAgIGdldEJ5V29ya05vRGVwdElkKCl7CiAgICAgIGdldEJ5V29ya05vRGVwdElkKHtkZXB0SWQ6dGhpcy5xdWVyeVBhcmFtcy5kZXB0SWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgY29uc29sZS5sb2cocmVzKQogICAgICAgIGlmKHJlcy5jb2RlID09IDIwMCl7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnVzZXJJZCA9IHJlcy5kYXRhLmlkOwogICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy53b3JrTm8gPSByZXMuZGF0YS53b3JrTm87CiAgICAgICAgICB0aGlzLnVzZXJJbmZvID0gcmVzLmRhdGE7CiAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgIC8vIOiOt+WPluiiq+iAg+aguOS/oeaBrwogICAgICAgICAgdGhpcy5nZXRCZUFzc2Vzc2VkTGlzdCgpOwogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAKICAgIC8vIOiOt+WPluiiq+iAg+aguOS/oeaBrwogICAgZ2V0QmVBc3Nlc3NlZExpc3QoKXsKICAgICAgbGlzdEJlQXNzZXNzZWQoe2RlcHRJZDp0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCxhc3Nlc3NEYXRlOnRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZX0pLnRoZW4ocmVzID0+ewogICAgICAgIGxldCBiZUFzc2Vzc2VkTGlzdCA9IFtdOwogICAgICAgIGlmKHJlcy5jb2RlID09IDIwMCl7CiAgICAgICAgICBpZihyZXMuZGF0YS5sZW5ndGggPiAwKXsKICAgICAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgICBiZUFzc2Vzc2VkTGlzdCA9IFsuLi5iZUFzc2Vzc2VkTGlzdCwuLi5pdGVtLmhyTGF0ZXJhbEFzc2Vzc0luZm9MaXN0XQogICAgICAgICAgICB9KQogICAgICAgICAgICB0aGlzLmJlQXNzZXNzZWRMaXN0ID0gYmVBc3Nlc3NlZExpc3Q7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIGNvbnNvbGUubG9nKGJlQXNzZXNzZWRMaXN0KQogICAgICB9KQogICAgfSwKICAgIC8qKiDmn6Xor6Lnu6nmlYjogIPmoLgt6Ieq6K+E5oyH5qCH6YWN572u5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBnZXRJbmZvQnlEYXRlKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGNvbnNvbGUubG9nKHJlc3BvbnNlLmRhdGEpOwogICAgICAgIC8vIGNvbnNvbGUubG9nKHR5cGVvZiByZXNwb25zZS5kYXRhKTsKICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgewogICAgICAgICAgLy8g5oyH5qCH6YWN572u5pWw5o2uCiAgICAgICAgICB0aGlzLmhhbmRsZVNwYW5MaXN0KHJlc3BvbnNlLmRhdGEpOwogICAgICAgICAgdGhpcy5saXN0ID0gcmVzcG9uc2UuZGF0YS5tYXAoaXRlbSA9PiB7CiAgICAgICAgICAgIGl0ZW0ucGVyZm9ybWFuY2UgPSAiIjsKICAgICAgICAgICAgLy8g5qC55o2u6aG555uu57G75Z6L6K6+572u6buY6K6k5YiG5pWwCiAgICAgICAgICAgIGxldCBub1NwYWNlU3RyID0gaXRlbS5pdGVtLnJlcGxhY2UoL1xzKy9nLCAnJyk7CiAgICAgICAgICAgIGlmKG5vU3BhY2VTdHIuaW5jbHVkZXMoIuaciOW6pumHjeeCueW3peS9nCIpKXsKICAgICAgICAgICAgICBpdGVtLmRlUG9pbnRzID0gNTsgLy8g5pyI5bqm6YeN54K55bel5L2c6buY6K6kNeWIhgogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIGl0ZW0uZGVQb2ludHMgPSAwOyAvLyDlhbbku5bpobnnm67pu5jorqQw5YiGCiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIGl0ZW07CiAgICAgICAgICB9KTsKICAgICAgICAgIHRoaXMuc3RhdHVzID0gIjAiOwogICAgICAgICAgdGhpcy5yZWFkT25seSA9IGZhbHNlOwogICAgICAgICAgdGhpcy5yZXNldFNob3cgPSBmYWxzZTsKICAgICAgICAgIHRoaXMucmVqZWN0UmVhc29uID0gbnVsbDsKICAgICAgICAgIHRoaXMuY2FsY3VsYXRlU2VsZlNjb3JlKCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIC8vIOiHquivhOS/oeaBrwogICAgICAgICAgbGV0IGluZm8gPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgbGV0IGxpc3QgPSBKU09OLnBhcnNlKGluZm8uY29udGVudCk7CiAgICAgICAgICB0aGlzLmhhbmRsZVNwYW5MaXN0KGxpc3QpOwogICAgICAgICAgdGhpcy5saXN0ID0gbGlzdDsKICAgICAgICAgIHRoaXMuaWQgPSBpbmZvLmlkOwogICAgICAgICAgdGhpcy5zZWxmU2NvcmUgPSBpbmZvLnNlbGZTY29yZTsKICAgICAgICAgIHRoaXMucmVqZWN0UmVhc29uID0gaW5mby5yZWplY3RSZWFzb247CiAgICAgICAgICB0aGlzLnN0YXR1cyA9IGluZm8uc3RhdHVzOwogICAgICAgICAgaWYoaW5mby5zaWduKXsKICAgICAgICAgICAgdGhpcy5zZWxmU2lnbiA9IEpTT04ucGFyc2UoaW5mby5zaWduKTsKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuaW5mbyA9IGluZm87CiAgICAgICAgICBpZihpbmZvLnN0YXR1cyA9PSAiMCIpewogICAgICAgICAgICB0aGlzLnJlYWRPbmx5ID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMucmVzZXRTaG93ID0gdHJ1ZTsKICAgICAgICAgIH1lbHNlewogICAgICAgICAgICB0aGlzLnJlYWRPbmx5ID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5yZXNldFNob3cgPSBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKCiAgICAvLyDlpITnkIbliJfooagKICAgIGhhbmRsZVNwYW5MaXN0KGRhdGEpewogICAgICBsZXQgaXRlbUxpc3QgPSBbXTsKICAgICAgbGV0IHN0YW5kYXJkTGlzdCA9IFtdOwogICAgICBsZXQgaXRlbUZsYWcgPSAwOwogICAgICBsZXQgc3RhbmRhcmRGbGFnID0gMDsKICAgICAgZm9yKGxldCBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpKyspewogICAgICAgIC8vIOebuOWQjOiAg+aguOmhueOAgeivhOWIhuagh+WHhuWQiOW5tgogICAgICAgIGlmKGkgPT0gMCl7CiAgICAgICAgICBpdGVtTGlzdC5wdXNoKHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgfSkKICAgICAgICAgIHN0YW5kYXJkTGlzdC5wdXNoKHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgfSkKICAgICAgICB9ZWxzZXsKICAgICAgICAgIC8vIOiAg+aguOmhuQogICAgICAgICAgaWYoZGF0YVtpIC0gMV0uaXRlbSA9PSBkYXRhW2ldLml0ZW0pewogICAgICAgICAgICBpdGVtTGlzdC5wdXNoKHsKICAgICAgICAgICAgICByb3dzcGFuOiAwLAogICAgICAgICAgICAgIGNvbHNwYW46IDAKICAgICAgICAgICAgfSkKICAgICAgICAgICAgaXRlbUxpc3RbaXRlbUZsYWddLnJvd3NwYW4gKz0gMTsKICAgICAgICAgIH1lbHNlewogICAgICAgICAgICBpdGVtTGlzdC5wdXNoKHsKICAgICAgICAgICAgICByb3dzcGFuOiAxLAogICAgICAgICAgICAgIGNvbHNwYW46IDEKICAgICAgICAgICAgfSkKICAgICAgICAgICAgaXRlbUZsYWcgPSBpOwogICAgICAgICAgfQogICAgICAgICAgLy8g6K+E5YiG5qCH5YeGCiAgICAgICAgICBpZihkYXRhW2kgLSAxXS5zdGFuZGFyZCA9PSBkYXRhW2ldLnN0YW5kYXJkKXsKICAgICAgICAgICAgc3RhbmRhcmRMaXN0LnB1c2goewogICAgICAgICAgICAgIHJvd3NwYW46IDAsCiAgICAgICAgICAgICAgY29sc3BhbjogMAogICAgICAgICAgICB9KQogICAgICAgICAgICBzdGFuZGFyZExpc3Rbc3RhbmRhcmRGbGFnXS5yb3dzcGFuICs9IDE7CiAgICAgICAgICB9ZWxzZXsKICAgICAgICAgICAgc3RhbmRhcmRMaXN0LnB1c2goewogICAgICAgICAgICAgIHJvd3NwYW46IDEsCiAgICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgICB9KQogICAgICAgICAgICBzdGFuZGFyZEZsYWcgPSBpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLnNwYW5MaXN0Lml0ZW1MaXN0ID0gaXRlbUxpc3Q7CiAgICAgIHRoaXMuc3Bhbkxpc3Quc3RhbmRhcmRMaXN0ID0gc3RhbmRhcmRMaXN0OwogICAgfSwKCgogICAgLy8g5aSE55CG6YOo6Zeo5LiL5ouJ6YCJ6aG5CiAgICBoYW5kbGVEZXB0TGlzdChkYXRhKXsKICAgICAgLy8gbGV0IHN5YiA9IFsi54K86ZOB5LqL5Lia6YOoIiwi54K86ZKi5LqL5Lia6YOoIiwi6L2n6ZKi5LqL5Lia6YOoIiwi54m55p2/5LqL5Lia6YOoIiwi5Yqo5Yqb5LqL5Lia6YOoIiwi54mp5rWB5LqL5Lia6YOoIiwi56CU56m26ZmiIl07CiAgICAgIGxldCBkZXB0TGlzdCA9IFtdOwogICAgICBkYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgLy9pZihzeWIuaW5kZXhPZihpdGVtLmRlcHROYW1lKSA9PSAtMSl7CiAgICAgICAgICBkZXB0TGlzdC5wdXNoKHsKICAgICAgICAgICAgZGVwdE5hbWU6aXRlbS5kZXB0TmFtZSwKICAgICAgICAgICAgZGVwdElkOml0ZW0uZGVwdElkCiAgICAgICAgICB9KQogICAgICAgIC8vfQogICAgICB9KQogICAgICB0aGlzLmRlcHRPcHRpb25zID0gZGVwdExpc3Q7CiAgICAgIGlmKGRlcHRMaXN0Lmxlbmd0aCA+IDApewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gZGVwdExpc3RbMF0uZGVwdElkOwogICAgICAgIHRoaXMuZGVwdE5hbWUgPSBkZXB0TGlzdFswXS5kZXB0TmFtZTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLmFzc2Vzc0RhdGVUZXh0ID0gdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlLnJlcGxhY2UoIi0iLCIg5bm0ICIpICsgIiDmnIgiOwogICAgICB0aGlzLmRlcHRPcHRpb25zLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaWYoaXRlbS5kZXB0SWQgPT0gdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQpewogICAgICAgICAgdGhpcy5kZXB0TmFtZSA9IGl0ZW0uZGVwdE5hbWU7CiAgICAgICAgfQogICAgICB9KQogICAgICB0aGlzLmlkID0gbnVsbDsKICAgICAgdGhpcy5pbmZvID0gbnVsbDsKICAgICAgdGhpcy5zZWxmU2NvcmUgPSAiIjsKICAgICAgdGhpcy5saXN0ID0gW107CiAgICAgIHRoaXMuYmVBc3Nlc3NlZExpc3QgPSBbXTsKICAgICAgdGhpcy5yZWplY3RSZWFzb24gPSAiIjsKICAgICAgdGhpcy5yZWFkT25seSA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0U2hvdyA9IGZhbHNlOwogICAgICB0aGlzLmdldEJ5V29ya05vRGVwdElkKCk7CiAgICB9LAoKICAgIC8vIOS/neWtmAogICAgc2F2ZSgpewogICAgICBpZih0aGlzLmxpc3QubGVuZ3RoID09IDApewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmnKrphY3nva7nm7jlhbPkv6Hmga/vvIzor7flhYjphY3nva7mjIfmoIcnCiAgICAgICAgICB9KTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBsZXQgZGF0YSA9IHRoaXMuaGFuZGxlRGF0YSh0aGlzLmxpc3QpOwogICAgICBsZXQgZm9ybSA9IHsKICAgICAgICBpZDp0aGlzLmlkLAogICAgICAgIHdvcmtObzp0aGlzLnVzZXJJbmZvLndvcmtObywKICAgICAgICBhc3Nlc3NEYXRlOnRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZSwKICAgICAgICBkZXB0SWQ6dGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQsCiAgICAgICAgY29udGVudDpKU09OLnN0cmluZ2lmeShkYXRhKSwKICAgICAgICBzdGF0dXM6IjAiLAogICAgICAgIHVzZXJJZDp0aGlzLnF1ZXJ5UGFyYW1zLnVzZXJJZCwKICAgICAgICBkZXB0TmFtZTp0aGlzLmRlcHROYW1lLAogICAgICAgIG5hbWU6dGhpcy51c2VySW5mby5uYW1lLAogICAgICAgIHNlbGZTY29yZTp0aGlzLnNlbGZTY29yZSwKICAgICAgICBqb2I6dGhpcy51c2VySW5mby5qb2IsCiAgICAgICAgcG9zdFR5cGU6dGhpcy51c2VySW5mby5wb3N0VHlwZQogICAgICB9CiAgICAgIHNhdmVJbmZvKGZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApewogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICBtZXNzYWdlOiAn5L+d5a2Y5oiQ5YqfIScKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSkKICAgIH0sCgogICAgLy8g56Gu6K6k5o+Q5Lqk54K55Ye75LqL5Lu2CiAgICBzdWJtaXQoKXsKICAgICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTlkI7lsIbmtYHovazoh7PkuIvkuIDoioLngrksIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuc3VibWl0RGF0YSgpOwogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKCiAgICAgICAgfSk7CiAgICB9LAoKICAgIG9uU3VibWl0KCl7CiAgICAgIGlmKHRoaXMudmVyaWZ5SW5zZXJ0KCkpewogICAgICAgIHRoaXMub3BlblNpZ24gPSB0cnVlOwogICAgICB9CiAgICB9LAoKICAgIGNsZWFyU2lnbigpewogICAgICB0aGlzLiRyZWZzLnNpZ25hdHVyZVBhZC5jbGVhclNpZ25hdHVyZSgpOwogICAgfSwKCiAgICAvLyDmj5DkuqTmlbDmja7pqozor4EKICAgIHZlcmlmeUluc2VydCgpewogICAgICBpZih0aGlzLmxpc3QubGVuZ3RoID09IDApewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmnKrphY3nva7nm7jlhbPkv6Hmga/vvIzor7flhYjphY3nva7mjIfmoIcnCiAgICAgICAgICB9KTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQoKICAgICAgLy8g5riF6Zmk5LmL5YmN55qE6ZSZ6K+v54q25oCBCiAgICAgIHRoaXMuY2xlYXJWYWxpZGF0aW9uRXJyb3JzKCk7CgogICAgICBmb3IobGV0IGkgPSAwOyBpIDwgdGhpcy5saXN0Lmxlbmd0aDsgaSsrKXsKICAgICAgICBjb25zdCBpdGVtID0gdGhpcy5saXN0W2ldOwoKICAgICAgICAvLyDpqozor4HlrozmiJDlrp7nu6kKICAgICAgICBpZighaXRlbS5wZXJmb3JtYW5jZSB8fCBpdGVtLnBlcmZvcm1hbmNlLnRyaW0oKSA9PT0gJycpewogICAgICAgICAgdGhpcy4kc2V0KGl0ZW0sICdwZXJmb3JtYW5jZUVycm9yJywgJ+ivt+Whq+WGmeWujOaIkOWunue7qScpOwogICAgICAgICAgdGhpcy5mb2N1c0FuZFNjcm9sbFRvRmllbGQoYHBlcmZvcm1hbmNlXyR7aX1gLCBpICsgMSwgJ+WujOaIkOWunue7qScpOwogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KCiAgICAgICAgLy8g6aqM6K+B5Yqg5YeP5YiGCiAgICAgICAgaWYoaXRlbS5kZVBvaW50cyA9PT0gbnVsbCB8fCBpdGVtLmRlUG9pbnRzID09PSB1bmRlZmluZWQgfHwgaXRlbS5kZVBvaW50cyA9PT0gJycpewogICAgICAgICAgdGhpcy4kc2V0KGl0ZW0sICdkZVBvaW50c0Vycm9yJywgJ+ivt+Whq+WGmeWKoOWHj+WIhicpOwogICAgICAgICAgdGhpcy5mb2N1c0FuZFNjcm9sbFRvRmllbGQoYGRlUG9pbnRzXyR7aX1gLCBpICsgMSwgJ+WKoOWHj+WIhicpOwogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KCiAgICAgICAgLy8g6aqM6K+B5Yqg5YeP5YiG5Y6f5ZugCiAgICAgICAgaWYoaXRlbS5kZVBvaW50cyAhPSAwICYmICghaXRlbS5wb2ludHNSZWFzb24gfHwgaXRlbS5wb2ludHNSZWFzb24udHJpbSgpID09PSAnJykpewogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5Li65pyI5bqm6YeN54K55bel5L2cCiAgICAgICAgICBsZXQgbm9TcGFjZVN0ciA9IGl0ZW0uaXRlbS5yZXBsYWNlKC9ccysvZywgJycpOwogICAgICAgICAgaWYoIW5vU3BhY2VTdHIuaW5jbHVkZXMoIuaciOW6pumHjeeCueW3peS9nCIpKXsKICAgICAgICAgICAgLy8g6Z2e5pyI5bqm6YeN54K55bel5L2c6ZyA6KaB5aGr5YaZ5Yqg5YeP5YiG5Y6f5ZugCiAgICAgICAgICAgIHRoaXMuJHNldChpdGVtLCAncG9pbnRzUmVhc29uRXJyb3InLCAn5pyJ5Yqg5YeP5YiG55qE6K+35aGr5YaZ5Y6f5ZugJyk7CiAgICAgICAgICAgIHRoaXMuZm9jdXNBbmRTY3JvbGxUb0ZpZWxkKGBwb2ludHNSZWFzb25fJHtpfWAsIGkgKyAxLCAn5Yqg5YeP5YiG5Y6f5ZugJyk7CiAgICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIGlmKCF0aGlzLnNlbGZTY29yZSl7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgICAgbWVzc2FnZTogJ+ivt+Whq+WGmeiHquivhOWIhuaVsCcKICAgICAgICAgIH0pOwogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIHJldHVybiB0cnVlOwogICAgfSwKCiAgICAvLyDmlrDlop7mlbDmja4KICAgIHN1Ym1pdERhdGEoKXsKICAgICAgbGV0IGRhdGEgPSB0aGlzLmhhbmRsZURhdGEodGhpcy5saXN0KTsKICAgICAgbGV0IGZvcm0gPSB7CiAgICAgICAgaWQ6dGhpcy5pZCwKICAgICAgICB3b3JrTm86dGhpcy51c2VySW5mby53b3JrTm8sCiAgICAgICAgYXNzZXNzRGF0ZTp0aGlzLnF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGUsCiAgICAgICAgZGVwdElkOnRoaXMucXVlcnlQYXJhbXMuZGVwdElkLAogICAgICAgIGNvbnRlbnQ6SlNPTi5zdHJpbmdpZnkoZGF0YSksCiAgICAgICAgc3RhdHVzOiIxIiwKICAgICAgICB1c2VySWQ6dGhpcy5xdWVyeVBhcmFtcy51c2VySWQsCiAgICAgICAgZGVwdE5hbWU6dGhpcy5kZXB0TmFtZSwKICAgICAgICBuYW1lOnRoaXMudXNlckluZm8ubmFtZSwKICAgICAgICBzZWxmU2NvcmU6dGhpcy5zZWxmU2NvcmUsCiAgICAgICAgam9iOnRoaXMudXNlckluZm8uam9iLAogICAgICAgIHBvc3RUeXBlOnRoaXMudXNlckluZm8ucG9zdFR5cGUsCiAgICAgICAgYXZlcmFnZUxpbmtGbGFnOnRoaXMudXNlckluZm8uYXZlcmFnZUxpbmtGbGFnLAogICAgICAgIGJlbmVmaXRMaW5rRmxhZzp0aGlzLnVzZXJJbmZvLmJlbmVmaXRMaW5rRmxhZywKICAgICAgICBzaWduOkpTT04uc3RyaW5naWZ5KHRoaXMuc2lnbikKICAgICAgfQogICAgICBzdWJtaXRJbmZvKGZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApewogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgICB0aGlzLnNpZ24gPSAiIjsKICAgICAgICAgIHRoaXMuJHJlZnMuc2lnbmF0dXJlUGFkLmNsZWFyU2lnbmF0dXJlKCk7CiAgICAgICAgICB0aGlzLm9wZW5TaWduID0gZmFsc2U7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICBtZXNzYWdlOiAn5o+Q5Lqk5oiQ5YqfIScKICAgICAgICAgIH0pOwogICAgICAgIH1lbHNlewoKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAoKICAgIC8vIOS/neWtmOmHjee9rgogICAgcmVzZXRJbmZvKCl7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+mHjee9ruWQjuWwhua4heepuuaJgOacieW3suWhq+WGmeeahOWGheWuue+8jOaYr+WQpuehruiupOmHjee9rj8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgLy8g5Yig6Zmk5L+d5a2Y5L+h5oGvCiAgICAgICAgZGVsSW5mbyh7aWQ6dGhpcy5pZH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIGlmKHJlcy5jb2RlID09IDIwMCl7CiAgICAgICAgICAgIHRoaXMuaWQgPSBudWxsOwogICAgICAgICAgICB0aGlzLnNlbGZTY29yZSA9IG51bGw7CiAgICAgICAgICAgIC8vIOiOt+WPlumFjee9ruS/oeaBrwogICAgICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfph43nva7miJDlip8hJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfph43nva7lpLHotKU6JywgZXJyb3IpOwogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfph43nva7lpLHotKXvvIzor7fph43or5UnCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIC8vIOeUqOaIt+WPlua2iOmHjee9rgogICAgICB9KTsKICAgIH0sCgogICAgLy8g5aSE55CG5o+Q5Lqk5pWw5o2uCiAgICBoYW5kbGVEYXRhKGRhdGEpewogICAgICBsZXQgcmVzdWx0ID0gW10KICAgICAgZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGxldCBmb3JtID0gewogICAgICAgICAgaXRlbTogaXRlbS5pdGVtLAogICAgICAgICAgY2F0ZWdvcnk6IGl0ZW0uY2F0ZWdvcnksCiAgICAgICAgICB0YXJnZXQ6IGl0ZW0udGFyZ2V0LAogICAgICAgICAgc3RhbmRhcmQ6IGl0ZW0uc3RhbmRhcmQsCiAgICAgICAgICBwZXJmb3JtYW5jZTogaXRlbS5wZXJmb3JtYW5jZSwKICAgICAgICAgIGRlUG9pbnRzOiBpdGVtLmRlUG9pbnRzLAogICAgICAgICAgcG9pbnRzUmVhc29uOml0ZW0ucG9pbnRzUmVhc29uCiAgICAgICAgfTsKICAgICAgICByZXN1bHQucHVzaChmb3JtKTsKICAgICAgfSkKICAgICAgcmV0dXJuIHJlc3VsdAogICAgfSwKCiAgICAvKiog5qCH5YeG6YWN572u6Lez6L2sICovCiAgICBoYW5kbGVDb25maWcoKXsKICAgICAgZ2V0QnlXb3JrTm9EZXB0SWQoe2RlcHRJZDp0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZH0pLnRoZW4ocmVzID0+IHsKICAgICAgICBjb25zb2xlLmxvZyhyZXMpCiAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsKICAgICAgICAgIGlmKHJlcy5kYXRhLmlkKXsKICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICAgICAgcGF0aDoiL2Fzc2Vzcy9zZWxmL3VzZXIvZGV0YWlsIiwKICAgICAgICAgICAgcXVlcnk6ewogICAgICAgICAgICAgIHVzZXJJZDpyZXMuZGF0YS5pZAogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pCiAgICAgIAogICAgfSwKCgogICAgLy8g5ZCI5bm25Y2V5YWD5qC85pa55rOVCiAgICBvYmplY3RTcGFuTWV0aG9kKHsgcm93LCByb3dJbmRleCwgY29sdW1uSW5kZXggfSkgewogICAgICAvLyDnrKzkuIDliJfnm7jlkIzpobnlkIjlubYKICAgICAgaWYgKGNvbHVtbkluZGV4ID09PSAwKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuc3Bhbkxpc3QuaXRlbUxpc3Rbcm93SW5kZXhdOwogICAgICB9CiAgICAgIC8vIOivhOWIhuagh+WHhuebuOWQjOWQiOW5tgogICAgICBpZihjb2x1bW5JbmRleCA9PT0gMyl7CiAgICAgICAgcmV0dXJuIHRoaXMuc3Bhbkxpc3Quc3RhbmRhcmRMaXN0W3Jvd0luZGV4XTsKICAgICAgfQogICAgICAvLyDnsbvliKvml6DlhoXlrrkg5ZCI5bm2CiAgICAgIGlmKGNvbHVtbkluZGV4ID09PSAxKXsKICAgICAgICBpZighcm93LmNhdGVnb3J5KXsKICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIHJvd3NwYW46IDAsCiAgICAgICAgICAgIGNvbHNwYW46IDAKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgICAgaWYoY29sdW1uSW5kZXggPT09IDIpewogICAgICAgIGlmKCFyb3cuY2F0ZWdvcnkpewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMgogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvLyDooqvogIPmoLjkuovpobnngrnlh7vkuovku7YKICAgIGhhbmRsZUJlQXNzZXNzZWRDbGljayhyb3csb3B0aW9uUm93LGluZGV4KXsKICAgICAgY29uc29sZS5sb2cocm93KQogICAgICAvLyDlsIbkuovpobnloavlhaXlrozmiJDlrp7nu6nliJfvvIjlvIPnlKjvvIkKICAgICAgLy8gaWYocm93LnBlcmZvcm1hbmNlKXsKICAgICAgLy8gICB0aGlzLiRzZXQocm93LCAncGVyZm9ybWFuY2UnLCByb3cucGVyZm9ybWFuY2UgKyAi77ybIiArIG9wdGlvblJvdy5hc3Nlc3NDb250ZW50KTsKICAgICAgLy8gfWVsc2V7CiAgICAgIC8vICAgdGhpcy4kc2V0KHJvdywgJ3BlcmZvcm1hbmNlJywgb3B0aW9uUm93LmFzc2Vzc0NvbnRlbnQpOwogICAgICAvLyB9CiAgICAgIAogICAgICAvLyDlsIbliIbmlbDloavlhaXliqDlh4/liIbliJcKICAgICAgaWYocm93LmRlUG9pbnRzKXsKICAgICAgICB0aGlzLiRzZXQocm93LCAnZGVQb2ludHMnLCBOdW1iZXIocm93LmRlUG9pbnRzKSArIE51bWJlcihvcHRpb25Sb3cuZGVkdWN0aW9uT2ZQb2ludCkpOwogICAgICB9ZWxzZXsKICAgICAgICB0aGlzLiRzZXQocm93LCAnZGVQb2ludHMnLCBOdW1iZXIob3B0aW9uUm93LmRlZHVjdGlvbk9mUG9pbnQpKTsKICAgICAgfQogICAgICAKICAgICAgLy8g5bCG5LqL6aG5K+WIhuaVsOWhq+WFpeWKoOWHj+WIhueQhueUseWIlwogICAgICBsZXQgcmVhc29uQ29udGVudCA9IG9wdGlvblJvdy5hc3Nlc3NDb250ZW50ICsgIigiICsgb3B0aW9uUm93LmRlZHVjdGlvbk9mUG9pbnQgKyAi5YiGKSI7CiAgICAgIGlmKHJvdy5wb2ludHNSZWFzb24pewogICAgICAgIHRoaXMuJHNldChyb3csICdwb2ludHNSZWFzb24nLCByb3cucG9pbnRzUmVhc29uICsgIu+8myIgKyByZWFzb25Db250ZW50KTsKICAgICAgfWVsc2V7CiAgICAgICAgdGhpcy4kc2V0KHJvdywgJ3BvaW50c1JlYXNvbicsIHJlYXNvbkNvbnRlbnQpOwogICAgICB9CiAgICAgIAogICAgICB0aGlzLiRyZWZzW2Bwb3BvdmVyJHtpbmRleH1gXS5zaG93UG9wcGVyID0gZmFsc2U7CiAgICAgIC8vIOmHjeaWsOiuoeeul+iHquivhOWIhuaVsAogICAgICB0aGlzLnNjb3JlSW5wdXQoKTsKICAgIH0sCgogICAgLy8g5Yqg5YeP5YiG6L6T5YWlCiAgICBzY29yZUlucHV0KHJvdyA9IG51bGwpewogICAgICAvLyDpqozor4HliqDlh4/liIbop4TliJnvvIjku4XlvZPkvKDlhaVyb3flj4LmlbDml7bov5vooYzpqozor4HvvIkKICAgICAgaWYgKHJvdyAmJiByb3cuaXRlbSkgewogICAgICAgIGxldCBub1NwYWNlU3RyID0gcm93Lml0ZW0ucmVwbGFjZSgvXHMrL2csICcnKTsKICAgICAgICBsZXQgdmFsdWUgPSByb3cuZGVQb2ludHM7CgogICAgICAgIGlmICh2YWx1ZSAhPT0gbnVsbCAmJiB2YWx1ZSAhPT0gdW5kZWZpbmVkICYmIHZhbHVlICE9PSAnJykgewogICAgICAgICAgbGV0IG51bVZhbHVlID0gTnVtYmVyKHZhbHVlKTsKCiAgICAgICAgICAvLyDmnIjluqbph43ngrnlt6XkvZzvvJrlj6rog73kuLox44CBM+aIljXliIYKICAgICAgICAgIGlmIChub1NwYWNlU3RyLmluY2x1ZGVzKCLmnIjluqbph43ngrnlt6XkvZwiKSkgewogICAgICAgICAgICBpZiAoIVsxLCAzLCA1XS5pbmNsdWRlcyhudW1WYWx1ZSkpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfmnIjluqbph43ngrnlt6XkvZzkuLrlvpfliIbliLbvvIzlj6rog73kuLox5YiG44CBM+WIhuaIljXliIYnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgJ2RlUG9pbnRzJywgbnVsbCk7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICAvLyDliqDliIbpobnvvJrpmaTmnIjluqbph43ngrnlt6XkvZzlpJbvvIzlj6rog73loaswCiAgICAgICAgICBlbHNlIGlmIChub1NwYWNlU3RyLmluY2x1ZGVzKCLliqDliIbpobkiKSkgewogICAgICAgICAgICBpZiAobnVtVmFsdWUgIT09IDApIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfliqDliIbpobnlj6rog73loasw5YiGJwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuJHNldChyb3csICdkZVBvaW50cycsIDApOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgICAgLy8g5YW25LuW57G75Z6L6aG577ya5Y+q6IO95aGrMOaIlui0n+aVsAogICAgICAgICAgZWxzZSB7CiAgICAgICAgICAgIGlmIChudW1WYWx1ZSA+IDApIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfor6Xpobnnm67lj6rog73loasw5YiG5oiW6LSf5pWwJwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuJHNldChyb3csICdkZVBvaW50cycsIDApOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g6YeN5paw6K6h566X6Ieq6K+E5YiG5pWwCiAgICAgIHRoaXMuY2FsY3VsYXRlU2VsZlNjb3JlKCk7CiAgICB9LAoKICAgIC8qKiDorqHnrpfoh6ror4TliIbmlbAgKi8KICAgIGNhbGN1bGF0ZVNlbGZTY29yZSgpIHsKICAgICAgbGV0IHBvaW50cyA9IDA7ICAgLy8g5pyI5bqm6YeN54K55bel5L2c5YiG5pWwCiAgICAgIHRoaXMubGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgcG9pbnRzICs9IE51bWJlcihpdGVtLmRlUG9pbnRzKTsKICAgICAgfSk7CiAgICAgIC8vIOiuoeeul+aAu+WIhu+8muWfuuehgOWIhjg1ICsg5Yqg5YeP5YiGCiAgICAgIHRoaXMuc2VsZlNjb3JlID0gODUgKyBwb2ludHM7CiAgICB9LAoKICAgIC8vIOetvuWQjeS4iuS8oOebuOWFswogICAgdXBsb2FkU2lnbmF0dXJlKCl7CiAgICAgIGNvbnN0IHsgaXNFbXB0eSwgZGF0YSB9ID0gdGhpcy4kcmVmcy5zaWduYXR1cmVQYWQuc2F2ZVNpZ25hdHVyZSgpOwogICAgICBjb25zb2xlLmxvZyhpc0VtcHR5LGRhdGEpCiAgICAgIGlmKGlzRW1wdHkpewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+etvuWQjSEnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9ZWxzZXsKICAgICAgICBjb25zdCBibG9iQmluID0gYXRvYihkYXRhLnNwbGl0KCcsJylbMV0pOwogICAgICAgIGxldCBhcnJheSA9IFtdOwogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYmxvYkJpbi5sZW5ndGg7IGkrKykgewogICAgICAgICAgYXJyYXkucHVzaChibG9iQmluLmNoYXJDb2RlQXQoaSkpOwogICAgICAgIH0KICAgICAgICBjb25zdCBmaWxlQmxvYiA9IG5ldyBCbG9iKFtuZXcgVWludDhBcnJheShhcnJheSldLCB7IHR5cGU6ICdpbWFnZS9wbmcnIH0pOwogICAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7CiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCdmaWxlJywgZmlsZUJsb2IsIGAke0RhdGUubm93KCl9LnBuZ2ApOwogICAgICAgIGZldGNoKHRoaXMudXBsb2FkLnVybCwgewogICAgICAgICAgbWV0aG9kOiAnUE9TVCcsCiAgICAgICAgICBib2R5OiBmb3JtRGF0YSwKICAgICAgICB9KQogICAgICAgIC50aGVuKHJlc3BvbnNlID0+IHJlc3BvbnNlLmpzb24oKSkKICAgICAgICAudGhlbihkYXRhID0+IHsKICAgICAgICAgIGNvbnNvbGUubG9nKCdTdWNjZXNzOicsIGRhdGEpOwogICAgICAgICAgaWYoZGF0YS5jb2RlID09IDIwMCl7CiAgICAgICAgICAgIHRoaXMuc2lnbiA9IHtmaWxlTmFtZTp0aGlzLnVzZXJJbmZvLm5hbWUgKyAiLnBuZyIsdXJsOmRhdGEudXJsfTsKICAgICAgICAgICAgdGhpcy5zdWJtaXQoKTsKICAgICAgICAgIH1lbHNlewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfnrb7lkI3kuIrkvKDlpLHotKUnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gewogICAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3I6JywgZXJyb3IpOwogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfnrb7lkI3kuIrkvKDlvILluLgnCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfQoKICAgIH0sCgogICAgLyoqIOmqjOivgeWNleS4quWtl+aute+8iOWkseeEpuaXtuiwg+eUqO+8iSAqLwogICAgdmFsaWRhdGVGaWVsZChyb3csIGZpZWxkVHlwZSkgewogICAgICAvLyDmuIXpmaTkuYvliY3nmoTplJnor6/kv6Hmga8KICAgICAgdGhpcy4kc2V0KHJvdywgYCR7ZmllbGRUeXBlfUVycm9yYCwgJycpOwoKICAgICAgaWYgKGZpZWxkVHlwZSA9PT0gJ3BlcmZvcm1hbmNlJykgewogICAgICAgIC8vIOmqjOivgeWujOaIkOWunue7qQogICAgICAgIGlmICghcm93LnBlcmZvcm1hbmNlIHx8IHJvdy5wZXJmb3JtYW5jZS50cmltKCkgPT09ICcnKSB7CiAgICAgICAgICB0aGlzLiRzZXQocm93LCAncGVyZm9ybWFuY2VFcnJvcicsICfor7floavlhpnlrozmiJDlrp7nu6knKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0gZWxzZSBpZiAoZmllbGRUeXBlID09PSAnZGVQb2ludHMnKSB7CiAgICAgICAgLy8g6aqM6K+B5Yqg5YeP5YiGCiAgICAgICAgaWYgKHJvdy5kZVBvaW50cyA9PT0gbnVsbCB8fCByb3cuZGVQb2ludHMgPT09IHVuZGVmaW5lZCB8fCByb3cuZGVQb2ludHMgPT09ICcnKSB7CiAgICAgICAgICB0aGlzLiRzZXQocm93LCAnZGVQb2ludHNFcnJvcicsICfor7floavlhpnliqDlh4/liIYnKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0gZWxzZSBpZiAoZmllbGRUeXBlID09PSAncG9pbnRzUmVhc29uJykgewogICAgICAgIC8vIOmqjOivgeWKoOWHj+WIhuWOn+WboAogICAgICAgIGlmIChyb3cuZGVQb2ludHMgIT0gMCAmJiAoIXJvdy5wb2ludHNSZWFzb24gfHwgcm93LnBvaW50c1JlYXNvbi50cmltKCkgPT09ICcnKSkgewogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5Li65pyI5bqm6YeN54K55bel5L2cCiAgICAgICAgICBsZXQgbm9TcGFjZVN0ciA9IHJvdy5pdGVtLnJlcGxhY2UoL1xzKy9nLCAnJyk7CiAgICAgICAgICBpZiAoIW5vU3BhY2VTdHIuaW5jbHVkZXMoIuaciOW6pumHjeeCueW3peS9nCIpKSB7CiAgICAgICAgICAgIC8vIOmdnuaciOW6pumHjeeCueW3peS9nOmcgOimgeWhq+WGmeWOn+WboAogICAgICAgICAgICB0aGlzLiRzZXQocm93LCAncG9pbnRzUmVhc29uRXJyb3InLCAn5pyJ5Yqg5YeP5YiG55qE6K+35aGr5YaZ5Y6f5ZugJyk7CiAgICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KCiAgICAgIHJldHVybiB0cnVlOwogICAgfSwKCiAgICAvKiog5riF6Zmk5omA5pyJ6aqM6K+B6ZSZ6K+vICovCiAgICBjbGVhclZhbGlkYXRpb25FcnJvcnMoKSB7CiAgICAgIHRoaXMubGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIHRoaXMuJHNldChpdGVtLCAncGVyZm9ybWFuY2VFcnJvcicsICcnKTsKICAgICAgICB0aGlzLiRzZXQoaXRlbSwgJ2RlUG9pbnRzRXJyb3InLCAnJyk7CiAgICAgICAgdGhpcy4kc2V0KGl0ZW0sICdwb2ludHNSZWFzb25FcnJvcicsICcnKTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDlrprkvY3liLDlrZfmrrXlubbmmL7npLror6bnu4bplJnor6/kv6Hmga8gKi8KICAgIGZvY3VzQW5kU2Nyb2xsVG9GaWVsZChyZWZOYW1lLCByb3dOdW1iZXIsIGZpZWxkTmFtZSkgewogICAgICBjb25zb2xlLmxvZyhg5byA5aeL5a6a5L2N5Yiw5a2X5q61OiAke3JlZk5hbWV9LCDooYzlj7c6ICR7cm93TnVtYmVyfSwg5a2X5q615ZCNOiAke2ZpZWxkTmFtZX1gKTsKCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAvLyDnrYnlvoVET03mm7TmlrDlkI7lho3miafooYwKICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gdGhpcy4kcmVmc1tyZWZOYW1lXTsKICAgICAgICAgICAgY29uc29sZS5sb2coJ+aJvuWIsOeahOWtl+auteWFg+e0oDonLCBmaWVsZCk7CgogICAgICAgICAgICBsZXQgdGFyZ2V0RWxlbWVudCA9IG51bGw7CiAgICAgICAgICAgIGxldCBpbnB1dEVsZW1lbnQgPSBudWxsOwoKICAgICAgICAgICAgaWYgKGZpZWxkICYmIGZpZWxkLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICAvLyDmlbDnu4TlvaLlvI/nmoRyZWbvvIh2LWZvcuS4reeahHJlZu+8iQogICAgICAgICAgICAgIGlucHV0RWxlbWVudCA9IGZpZWxkWzBdOwogICAgICAgICAgICAgIHRhcmdldEVsZW1lbnQgPSBpbnB1dEVsZW1lbnQuJGVsIHx8IGlucHV0RWxlbWVudDsKICAgICAgICAgICAgfSBlbHNlIGlmIChmaWVsZCkgewogICAgICAgICAgICAgIC8vIOWNleS4qnJlZgogICAgICAgICAgICAgIGlucHV0RWxlbWVudCA9IGZpZWxkOwogICAgICAgICAgICAgIHRhcmdldEVsZW1lbnQgPSBpbnB1dEVsZW1lbnQuJGVsIHx8IGlucHV0RWxlbWVudDsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgaWYgKHRhcmdldEVsZW1lbnQgJiYgaW5wdXRFbGVtZW50KSB7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+aJvuWIsOebruagh+WFg+e0oO+8jOW8gOWni+WumuS9jScpOwoKICAgICAgICAgICAgICAvLyAxLiDlhYjmu5rliqjliLDnm67moIfkvY3nva4KICAgICAgICAgICAgICB0YXJnZXRFbGVtZW50LnNjcm9sbEludG9WaWV3KHsKICAgICAgICAgICAgICAgIGJlaGF2aW9yOiAnc21vb3RoJywKICAgICAgICAgICAgICAgIGJsb2NrOiAnY2VudGVyJywKICAgICAgICAgICAgICAgIGlubGluZTogJ25lYXJlc3QnCiAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgIC8vIDIuIOetieW+hea7muWKqOWujOaIkOWQjuiBmueEpgogICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICAgICAgaW5wdXRFbGVtZW50LmZvY3VzKCk7CiAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflt7LogZrnhKbliLDovpPlhaXmoYYnKTsKICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGZvY3VzRXJyb3IpIHsKICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCfogZrnhKblpLHotKU6JywgZm9jdXNFcnJvcik7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSwgNTAwKTsKCiAgICAgICAgICAgICAgLy8gMy4g5pi+56S66ZSZ6K+v5L+h5oGvCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiBg56ysJHtyb3dOdW1iZXJ96KGMICR7ZmllbGROYW1lfSDmnKrloavlhpnlrozmlbTvvIzor7fmo4Dmn6XlubbloavlhplgLAogICAgICAgICAgICAgICAgZHVyYXRpb246IDUwMDAKICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgLy8g5aaC5p6c5om+5LiN5Yiw5YW35L2T5a2X5q6177yM5bCd6K+V5a6a5L2N5Yiw6KGo5qC86KGMCiAgICAgICAgICAgICAgY29uc29sZS53YXJuKGDmib7kuI3liLDlrZfmrrUgJHtyZWZOYW1lfe+8jOWwneivleWumuS9jeWIsOihqOagvOihjGApOwogICAgICAgICAgICAgIHRoaXMuc2Nyb2xsVG9UYWJsZVJvdyhyb3dOdW1iZXIgLSAxKTsKCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiBg56ysJHtyb3dOdW1iZXJ96KGMICR7ZmllbGROYW1lfSDmnKrloavlhpnlrozmlbTvvIzor7fmo4Dmn6XlubbloavlhplgLAogICAgICAgICAgICAgICAgZHVyYXRpb246IDUwMDAKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQoKICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WumuS9jeWtl+auteaXtuWPkeeUn+mUmeivrzonLCBlcnJvcik7CiAgICAgICAgICAgIC8vIOmZjee6p+WkhOeQhu+8muiHs+Wwkea7muWKqOWIsOihqOagvOWMuuWfnwogICAgICAgICAgICB0aGlzLnNjcm9sbFRvVGFibGUoKTsKCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgICAgICBtZXNzYWdlOiBg56ysJHtyb3dOdW1iZXJ96KGMICR7ZmllbGROYW1lfSDmnKrloavlhpnlrozmlbTvvIzor7fmo4Dmn6XlubbloavlhplgLAogICAgICAgICAgICAgIGR1cmF0aW9uOiA1MDAwCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0sIDEwMCk7IC8vIOe7meS4gOeCueaXtumXtOiuqemUmeivr+eKtuaAgeabtOaWsAogICAgICB9KTsKICAgIH0sCgogICAgLyoqIOa7muWKqOWIsOaMh+WumuihqOagvOihjCAqLwogICAgc2Nyb2xsVG9UYWJsZVJvdyhyb3dJbmRleCkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHRhYmxlUm93cyA9IHRoaXMuJGVsLnF1ZXJ5U2VsZWN0b3JBbGwoJy5lbC10YWJsZV9fYm9keSB0cicpOwogICAgICAgIGlmICh0YWJsZVJvd3MgJiYgdGFibGVSb3dzW3Jvd0luZGV4XSkgewogICAgICAgICAgdGFibGVSb3dzW3Jvd0luZGV4XS5zY3JvbGxJbnRvVmlldyh7CiAgICAgICAgICAgIGJlaGF2aW9yOiAnc21vb3RoJywKICAgICAgICAgICAgYmxvY2s6ICdjZW50ZXInCiAgICAgICAgICB9KTsKICAgICAgICAgIGNvbnNvbGUubG9nKGDlt7Lmu5rliqjliLDnrKwke3Jvd0luZGV4ICsgMX3ooYxgKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS53YXJuKGDmib7kuI3liLDnrKwke3Jvd0luZGV4ICsgMX3ooYzvvIzmu5rliqjliLDooajmoLxgKTsKICAgICAgICAgIHRoaXMuc2Nyb2xsVG9UYWJsZSgpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfmu5rliqjliLDooajmoLzooYzlpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuc2Nyb2xsVG9UYWJsZSgpOwogICAgICB9CiAgICB9LAoKICAgIC8qKiDmu5rliqjliLDooajmoLzljLrln58gKi8KICAgIHNjcm9sbFRvVGFibGUoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgdGFibGUgPSB0aGlzLiRlbC5xdWVyeVNlbGVjdG9yKCcuZWwtdGFibGUnKTsKICAgICAgICBpZiAodGFibGUpIHsKICAgICAgICAgIHRhYmxlLnNjcm9sbEludG9WaWV3KHsKICAgICAgICAgICAgYmVoYXZpb3I6ICdzbW9vdGgnLAogICAgICAgICAgICBibG9jazogJ3N0YXJ0JwogICAgICAgICAgfSk7CiAgICAgICAgICBjb25zb2xlLmxvZygn5bey5rua5Yqo5Yiw6KGo5qC85Yy65Z+fJyk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+a7muWKqOWIsOihqOagvOWksei0pTonLCBlcnJvcik7CiAgICAgIH0KICAgIH0KICB9LAp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAq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file": "index.vue", "sourceRoot": "src/views/assess/self", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.assessDate\"\r\n            type=\"month\"\r\n            value-format=\"yyyy-M\"\r\n            format=\"yyyy 年 M 月\"\r\n            placeholder=\"选择考核年月\"\r\n            @change=\"handleQuery\"\r\n            :clearable=\"false\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <el-select v-model=\"queryParams.deptId\" placeholder=\"请选择部门\" \r\n            @change=\"handleQuery\">\r\n            <el-option\r\n              v-for=\"item in deptOptions\"\r\n              :key=\"item.deptId\"\r\n              :label=\"item.deptName\"\r\n              :value=\"item.deptId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button type=\"warning\" icon=\"el-icon-s-tools\" size=\"mini\" @click=\"handleConfig\">指标配置</el-button>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row>\r\n        <el-form-item label=\"当前状态\">\r\n          <el-tag v-if=\"status == '0' && rejectReason\" type=\"danger\">退 回</el-tag>\r\n          <el-tag v-if=\"status == '0' && !rejectReason\" type=\"info\">未提交</el-tag>\r\n          <el-tag v-if=\"status == '1'\" type=\"warning\">部门领导评分</el-tag>\r\n          <el-tag v-if=\"status == '2'\" type=\"warning\">事业部评分</el-tag>\r\n          <el-tag v-if=\"status == '3'\" type=\"warning\">运改部/组织部审核</el-tag>\r\n          <el-tag v-if=\"status == '4'\" type=\"warning\">总经理部评分</el-tag>\r\n          <el-tag v-if=\"status == '5'\" type=\"success\">已完成</el-tag>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row v-if=\"rejectReason\">\r\n        <el-form-item label=\"退回原因\" prop=\"rejectReason\">\r\n            <span class=\"el-icon-warning\" style=\"color: #f56c6c;\"></span>\r\n            {{ rejectReason }}\r\n        </el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n      <h3 style=\"text-align: center;\">月度业绩考核表</h3>\r\n      <el-descriptions class=\"margin-top\" :column=\"3\">\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            姓名\r\n          </template>\r\n          {{ userInfo.name }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            部门\r\n          </template>\r\n          {{ deptName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            考核年月\r\n          </template>\r\n          {{ assessDateText }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-table v-loading=\"loading\" :data=\"list\"\r\n        :span-method=\"objectSpanMethod\" border>\r\n        <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n        <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"140\"/>\r\n        <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"150\" />\r\n        <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n        <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.performance }}</span>\r\n                <div v-else style=\"display: flex\">\r\n                  <!-- <el-button icon=\"el-icon-search\" size=\"small\"></el-button> -->\r\n                  <el-popover\r\n                    placement=\"left\"\r\n                    width=\"636\"\r\n                    trigger=\"click\"\r\n                    :ref=\"'popover' + scope.$index\">\r\n                    <el-table :data=\"beAssessedList\">\r\n                      <el-table-column width=\"150\" property=\"assessDeptName\" label=\"提出考核单位\"></el-table-column>\r\n                      <el-table-column width=\"300\" property=\"assessContent\" label=\"事项\"></el-table-column>\r\n                      <el-table-column width=\"80\" property=\"deductionOfPoint\" label=\"加减分\"></el-table-column>\r\n                      <el-table-column width=\"80\" label=\"操作\">\r\n                        <template slot-scope=\"beAssessed\">\r\n                          <el-button\r\n                            size=\"mini\"\r\n                            type=\"text\"\r\n                            icon=\"el-icon-edit\"\r\n                            @click=\"handleBeAssessedClick(scope.row,beAssessed.row,scope.$index)\"\r\n                          >填入</el-button>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                    <el-button slot=\"reference\" icon=\"el-icon-search\" size=\"small\"></el-button>\r\n                  </el-popover>\r\n                  <el-input\r\n                    :ref=\"`performance_${scope.$index}`\"\r\n                    class=\"table-input\"\r\n                    type=\"textarea\"\r\n                    autosize\r\n                    v-model=\"scope.row.performance\"\r\n                    placeholder=\"请输入完成实绩\"\r\n                    @blur=\"validateField(scope.row, 'performance')\"\r\n                    :class=\"{'is-error': scope.row.performanceError}\" />\r\n                  <div v-if=\"scope.row.performanceError\" class=\"el-form-item__error\">{{ scope.row.performanceError }}</div>\r\n                </div>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"108\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.dePoints }}</span>\r\n                <div v-else>\r\n                  <el-input\r\n                    :ref=\"`dePoints_${scope.$index}`\"\r\n                    class=\"table-input\"\r\n                    type=\"number\"\r\n                    autosize\r\n                    v-model=\"scope.row.dePoints\"\r\n                    placeholder=\"请输入加减分\"\r\n                    @input=\"scoreInput(scope.row)\"\r\n                    @blur=\"validateField(scope.row, 'dePoints')\"\r\n                    :class=\"{'is-error': scope.row.dePointsError}\" />\r\n                  <div v-if=\"scope.row.dePointsError\" class=\"el-form-item__error\">{{ scope.row.dePointsError }}</div>\r\n                </div>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\"  width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.pointsReason }}</span>\r\n                <div v-else>\r\n                  <el-input\r\n                    :ref=\"`pointsReason_${scope.$index}`\"\r\n                    class=\"table-input\"\r\n                    type=\"textarea\"\r\n                    autosize\r\n                    v-model=\"scope.row.pointsReason\"\r\n                    placeholder=\"请输入加减分原因\"\r\n                    @blur=\"validateField(scope.row, 'pointsReason')\"\r\n                    :class=\"{'is-error': scope.row.pointsReasonError}\" />\r\n                  <div v-if=\"scope.row.pointsReasonError\" class=\"el-form-item__error\">{{ scope.row.pointsReasonError }}</div>\r\n                </div>\r\n            </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" style=\"margin-top: 10px;\" label-position=\"left\">\r\n            <el-form-item v-if=\"readOnly\" label=\"自评分数 / 签名：\" prop=\"deptId\">\r\n              <div style=\"display: flex;\">\r\n                <span >{{ selfScore + \" 分 / \" }}</span>\r\n                <span v-if=\"!selfSign\">{{info.name}}</span>\r\n                <el-image v-else\r\n                  style=\"width: 100px; height: 46px\"\r\n                  :src=\"selfSign.url\"></el-image>\r\n              </div>\r\n              \r\n            </el-form-item>\r\n            <el-form-item v-else label=\"自评分数：\" prop=\"selfScore\">\r\n              <div style=\"display: flex;width: 180px;\">\r\n                <el-input type=\"number\" autosize v-model=\"selfScore\" placeholder=\"请输入分数\" :readonly=\"readOnly\" />\r\n                <span style=\"margin-left: 8px;\">分</span>\r\n              </div>\r\n            </el-form-item>\r\n            <!-- <el-form-item v-if=\"status > '1' && info.deptScore && info.deptUserName\" label=\"部门领导评分 / 签名：\">\r\n              <span >{{ info.deptScore + \" 分 / \" + info.deptUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"status > '2' && info.businessUserName && info.businessScore\" label=\"事业部领导评分 / 签名：\">\r\n              <span>{{ info.businessScore + \" 分 / \" + info.businessUserName }}</span>\r\n            </el-form-item> -->\r\n            \r\n            <!-- 部门领导评分 -->\r\n            <el-form-item v-if=\"info && info.deptScore && info.deptUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  部门领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ info.deptScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ info.deptUserName }}</span>\r\n                <div v-if=\"info.deptScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ info.deptScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 事业部领导评分 -->\r\n            <el-form-item v-if=\"info && info.businessUserName && info.businessScore\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  事业部领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ info.businessScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ info.businessUserName }}</span>\r\n                <div v-if=\"info.businessScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ info.businessScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 运改组织部评分 -->\r\n            <el-form-item v-if=\"info && info.organizationScore && info.organizationUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  运改组织部评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ info.organizationScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ info.organizationUserName }}</span>\r\n                <div v-if=\"info.organizationScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ info.organizationScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n\r\n\r\n            <!-- <el-form-item v-if=\"status > '4' && info.leaderScore && info.leaderName\" label=\"总经理部领导评分 / 签名：\" prop=\"deptId\">\r\n              <span >{{ info.leaderScore + \" 分 / \" + info.leaderName }}</span>\r\n            </el-form-item> -->\r\n          </el-form>\r\n      <div v-if=\"!readOnly\" style=\"text-align: center;\">\r\n        <el-button v-if=\"resetShow\" plain type=\"info\" @click=\"resetInfo\">重 置 </el-button>\r\n        <el-button type=\"success\" @click=\"save\">保 存</el-button>\r\n        <el-button type=\"primary\" @click=\"onSubmit\">提 交</el-button>\r\n      </div>\r\n\r\n      <!-- 签名板 -->\r\n      <el-dialog title=\"签字确认\" :visible.sync=\"openSign\" width=\"760px\" append-to-body>\r\n        <div style=\"border: 1px #ccc solid;width: 702px;\">\r\n            <vue-signature-pad\r\n            width=\"700px\"\r\n            height=\"300px\"\r\n            ref=\"signaturePad\"\r\n            :options=\"signOptions\"\r\n          />\r\n        </div>\r\n        <div style=\"text-align: center;padding: 10px 10px;\">\r\n          <el-button style=\"margin-right: 20px;\" type=\"success\" @click=\"clearSign\">清除</el-button>\r\n          <el-button type=\"primary\" @click=\"uploadSignature\">确认</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </template>\r\n\r\n  <script>\r\n  import { getInfoByDate, saveInfo, submitInfo, delInfo, listBeAssessed } from \"@/api/assess/self/info\";\r\n  // import { batchTarget, listSelfTargetAll } from \"@/api/assess/self/target\";\r\n  import { getReportDeptList, getByWorkNoDeptId } from \"@/api/assess/self/user\";\r\n  import { VueSignaturePad } from 'vue-signature-pad';\r\n\r\n  export default {\r\n    components: {\r\n      VueSignaturePad\r\n    },\r\n    name: \"SelfAssessReport\",\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        openSign:false,\r\n        // 绩效考核-自评指标配置表格数据\r\n        list: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          userId:null,\r\n          workNo: null,\r\n          deptId:null,\r\n          assessDate: null,\r\n        },\r\n        // 考核年月文本显示\r\n        assessDateText:null,\r\n        // 部门显示\r\n        deptName:null,\r\n        // 表单参数\r\n        form: {},\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        // 用户信息\r\n        userInfo:{},\r\n        // 合并单元格信息\r\n        spanList:{\r\n          itemList:[],\r\n          standardList:[]\r\n        },\r\n        // 是否显示重置按钮\r\n        resetShow:false,\r\n        // 是否只读\r\n        readOnly:false,\r\n        // 部门选项\r\n        deptOptions:[],\r\n        // 自评信息Id\r\n        id:null,\r\n        // 自评分数\r\n        selfScore:100,\r\n        // 状态\r\n        status:\"0\",\r\n        // 自评信息\r\n        info:{},\r\n        // 横向被考评信息\r\n        beAssessedList:[],\r\n        // 退回理由\r\n        rejectReason:\"\",\r\n        // 自评签名\r\n        selfSign:\"\",\r\n        // 签名板配置\r\n        signOptions: {\r\n          onBegin: () => this.$refs.signaturePad.resizeCanvas(),\r\n          backgroundColor: 'rgba(255, 255, 255, 1)'\r\n        },\r\n        sign:\"\",\r\n        file:null,\r\n        fileList:[],\r\n        upload: {\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/app/common/uploadMinio\",\r\n          isUploading: false,\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n      this.queryParams.assessDate = this.getDefaultAssessDate()\r\n      this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n      // this.getSelfAssessUser();\r\n      this.getReportDeptList();\r\n    },\r\n    methods: {\r\n\r\n      // 获取默认考核日期\r\n      getDefaultAssessDate() {\r\n        const now = new Date();\r\n        const currentDay = now.getDate();\r\n\r\n        let targetDate;\r\n        if (currentDay < 10) {\r\n          // 当前日期小于10日，默认为上个月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n        } else {\r\n          // 当前日期大于等于10日，默认为当月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n        }\r\n\r\n        // 格式化为 YYYY-M 格式\r\n        const year = targetDate.getFullYear();\r\n        const month = targetDate.getMonth() + 1;\r\n        return `${year}-${month}`;\r\n      },\r\n\r\n      // 获取部门信息\r\n      getReportDeptList(){\r\n        getReportDeptList().then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.handleDeptList(res.data);\r\n            // 根据部门获取用户信息\r\n            this.getByWorkNoDeptId();\r\n          }\r\n        })\r\n      },\r\n      // 获取用户信息\r\n      getByWorkNoDeptId(){\r\n        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.queryParams.userId = res.data.id;\r\n            this.queryParams.workNo = res.data.workNo;\r\n            this.userInfo = res.data;\r\n            this.getList();\r\n            // 获取被考核信息\r\n            this.getBeAssessedList();\r\n          }\r\n        })\r\n      },\r\n      \r\n      // 获取被考核信息\r\n      getBeAssessedList(){\r\n        listBeAssessed({deptId:this.queryParams.deptId,assessDate:this.queryParams.assessDate}).then(res =>{\r\n          let beAssessedList = [];\r\n          if(res.code == 200){\r\n            if(res.data.length > 0){\r\n              res.data.forEach(item => {\r\n                beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]\r\n              })\r\n              this.beAssessedList = beAssessedList;\r\n            }\r\n          }\r\n          console.log(beAssessedList)\r\n        })\r\n      },\r\n      /** 查询绩效考核-自评指标配置列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        getInfoByDate(this.queryParams).then(response => {\r\n          console.log(response.data);\r\n          // console.log(typeof response.data);\r\n          if (Array.isArray(response.data)) {\r\n            // 指标配置数据\r\n            this.handleSpanList(response.data);\r\n            this.list = response.data.map(item => {\r\n              item.performance = \"\";\r\n              // 根据项目类型设置默认分数\r\n              let noSpaceStr = item.item.replace(/\\s+/g, '');\r\n              if(noSpaceStr.includes(\"月度重点工作\")){\r\n                item.dePoints = 5; // 月度重点工作默认5分\r\n              } else {\r\n                item.dePoints = 0; // 其他项目默认0分\r\n              }\r\n              return item;\r\n            });\r\n            this.status = \"0\";\r\n            this.readOnly = false;\r\n            this.resetShow = false;\r\n            this.rejectReason = null;\r\n            this.calculateSelfScore();\r\n          } else {\r\n            // 自评信息\r\n            let info = response.data;\r\n            let list = JSON.parse(info.content);\r\n            this.handleSpanList(list);\r\n            this.list = list;\r\n            this.id = info.id;\r\n            this.selfScore = info.selfScore;\r\n            this.rejectReason = info.rejectReason;\r\n            this.status = info.status;\r\n            if(info.sign){\r\n              this.selfSign = JSON.parse(info.sign);\r\n            }\r\n            this.info = info;\r\n            if(info.status == \"0\"){\r\n              this.readOnly = false;\r\n              this.resetShow = true;\r\n            }else{\r\n              this.readOnly = true;\r\n              this.resetShow = false;\r\n            }\r\n          }\r\n          this.loading = false;\r\n        });\r\n      },\r\n\r\n      // 处理列表\r\n      handleSpanList(data){\r\n        let itemList = [];\r\n        let standardList = [];\r\n        let itemFlag = 0;\r\n        let standardFlag = 0;\r\n        for(let i = 0; i < data.length; i++){\r\n          // 相同考核项、评分标准合并\r\n          if(i == 0){\r\n            itemList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            standardList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n          }else{\r\n            // 考核项\r\n            if(data[i - 1].item == data[i].item){\r\n              itemList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              itemList[itemFlag].rowspan += 1;\r\n            }else{\r\n              itemList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              itemFlag = i;\r\n            }\r\n            // 评分标准\r\n            if(data[i - 1].standard == data[i].standard){\r\n              standardList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              standardList[standardFlag].rowspan += 1;\r\n            }else{\r\n              standardList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              standardFlag = i;\r\n            }\r\n          }\r\n        }\r\n        this.spanList.itemList = itemList;\r\n        this.spanList.standardList = standardList;\r\n      },\r\n\r\n\r\n      // 处理部门下拉选项\r\n      handleDeptList(data){\r\n        // let syb = [\"炼铁事业部\",\"炼钢事业部\",\"轧钢事业部\",\"特板事业部\",\"动力事业部\",\"物流事业部\",\"研究院\"];\r\n        let deptList = [];\r\n        data.forEach(item => {\r\n          //if(syb.indexOf(item.deptName) == -1){\r\n            deptList.push({\r\n              deptName:item.deptName,\r\n              deptId:item.deptId\r\n            })\r\n          //}\r\n        })\r\n        this.deptOptions = deptList;\r\n        if(deptList.length > 0){\r\n          this.queryParams.deptId = deptList[0].deptId;\r\n          this.deptName = deptList[0].deptName;\r\n        }\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n        this.deptOptions.forEach(item => {\r\n          if(item.deptId == this.queryParams.deptId){\r\n            this.deptName = item.deptName;\r\n          }\r\n        })\r\n        this.id = null;\r\n        this.info = null;\r\n        this.selfScore = \"\";\r\n        this.list = [];\r\n        this.beAssessedList = [];\r\n        this.rejectReason = \"\";\r\n        this.readOnly = false;\r\n        this.resetShow = false;\r\n        this.getByWorkNoDeptId();\r\n      },\r\n\r\n      // 保存\r\n      save(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"0\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType\r\n        }\r\n        saveInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.$message({\r\n              type: 'success',\r\n              message: '保存成功!'\r\n            });\r\n          }\r\n        })\r\n      },\r\n\r\n      // 确认提交点击事件\r\n      submit(){\r\n          this.$confirm('确认后将流转至下一节点, 是否继续?', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.submitData();\r\n          }).catch(() => {\r\n\r\n          });\r\n      },\r\n\r\n      onSubmit(){\r\n        if(this.verifyInsert()){\r\n          this.openSign = true;\r\n        }\r\n      },\r\n\r\n      clearSign(){\r\n        this.$refs.signaturePad.clearSignature();\r\n      },\r\n\r\n      // 提交数据验证\r\n      verifyInsert(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n\r\n        // 清除之前的错误状态\r\n        this.clearValidationErrors();\r\n\r\n        for(let i = 0; i < this.list.length; i++){\r\n          const item = this.list[i];\r\n\r\n          // 验证完成实绩\r\n          if(!item.performance || item.performance.trim() === ''){\r\n            this.$set(item, 'performanceError', '请填写完成实绩');\r\n            this.focusAndScrollToField(`performance_${i}`, i + 1, '完成实绩');\r\n            return false;\r\n          }\r\n\r\n          // 验证加减分\r\n          if(item.dePoints === null || item.dePoints === undefined || item.dePoints === ''){\r\n            this.$set(item, 'dePointsError', '请填写加减分');\r\n            this.focusAndScrollToField(`dePoints_${i}`, i + 1, '加减分');\r\n            return false;\r\n          }\r\n\r\n          // 验证加减分原因\r\n          if(item.dePoints != 0 && (!item.pointsReason || item.pointsReason.trim() === '')){\r\n            // 检查是否为月度重点工作\r\n            let noSpaceStr = item.item.replace(/\\s+/g, '');\r\n            if(!noSpaceStr.includes(\"月度重点工作\")){\r\n              // 非月度重点工作需要填写加减分原因\r\n              this.$set(item, 'pointsReasonError', '有加减分的请填写原因');\r\n              this.focusAndScrollToField(`pointsReason_${i}`, i + 1, '加减分原因');\r\n              return false;\r\n            }\r\n          }\r\n        }\r\n\r\n        if(!this.selfScore){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '请填写自评分数'\r\n            });\r\n            return false;\r\n        }\r\n        return true;\r\n      },\r\n\r\n      // 新增数据\r\n      submitData(){\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"1\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType,\r\n          averageLinkFlag:this.userInfo.averageLinkFlag,\r\n          benefitLinkFlag:this.userInfo.benefitLinkFlag,\r\n          sign:JSON.stringify(this.sign)\r\n        }\r\n        submitInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.sign = \"\";\r\n            this.$refs.signaturePad.clearSignature();\r\n            this.openSign = false;\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            });\r\n          }else{\r\n\r\n          }\r\n        })\r\n      },\r\n\r\n      // 保存重置\r\n      resetInfo(){\r\n        this.$confirm('重置后将清空所有已填写的内容，是否确认重置?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          // 删除保存信息\r\n          delInfo({id:this.id}).then(res => {\r\n            if(res.code == 200){\r\n              this.id = null;\r\n              this.selfScore = null;\r\n              // 获取配置信息\r\n              this.getList();\r\n              this.$message({\r\n                type: 'success',\r\n                message: '重置成功!'\r\n              });\r\n            }\r\n          }).catch(error => {\r\n            console.error('重置失败:', error);\r\n            this.$message({\r\n              type: 'error',\r\n              message: '重置失败，请重试'\r\n            });\r\n          });\r\n        }).catch(() => {\r\n          // 用户取消重置\r\n        });\r\n      },\r\n\r\n      // 处理提交数据\r\n      handleData(data){\r\n        let result = []\r\n        data.forEach(item => {\r\n          let form = {\r\n            item: item.item,\r\n            category: item.category,\r\n            target: item.target,\r\n            standard: item.standard,\r\n            performance: item.performance,\r\n            dePoints: item.dePoints,\r\n            pointsReason:item.pointsReason\r\n          };\r\n          result.push(form);\r\n        })\r\n        return result\r\n      },\r\n\r\n      /** 标准配置跳转 */\r\n      handleConfig(){\r\n        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            if(res.data.id){\r\n            this.$router.push({\r\n              path:\"/assess/self/user/detail\",\r\n              query:{\r\n                userId:res.data.id\r\n              }\r\n            })\r\n          }\r\n          }\r\n        })\r\n        \r\n      },\r\n\r\n\r\n      // 合并单元格方法\r\n      objectSpanMethod({ row, rowIndex, columnIndex }) {\r\n        // 第一列相同项合并\r\n        if (columnIndex === 0) {\r\n          return this.spanList.itemList[rowIndex];\r\n        }\r\n        // 评分标准相同合并\r\n        if(columnIndex === 3){\r\n          return this.spanList.standardList[rowIndex];\r\n        }\r\n        // 类别无内容 合并\r\n        if(columnIndex === 1){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 0,\r\n              colspan: 0\r\n            }\r\n          }\r\n        }\r\n        if(columnIndex === 2){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 1,\r\n              colspan: 2\r\n            }\r\n          }\r\n        }\r\n      },\r\n\r\n      // 被考核事项点击事件\r\n      handleBeAssessedClick(row,optionRow,index){\r\n        console.log(row)\r\n        // 将事项填入完成实绩列（弃用）\r\n        // if(row.performance){\r\n        //   this.$set(row, 'performance', row.performance + \"；\" + optionRow.assessContent);\r\n        // }else{\r\n        //   this.$set(row, 'performance', optionRow.assessContent);\r\n        // }\r\n        \r\n        // 将分数填入加减分列\r\n        if(row.dePoints){\r\n          this.$set(row, 'dePoints', Number(row.dePoints) + Number(optionRow.deductionOfPoint));\r\n        }else{\r\n          this.$set(row, 'dePoints', Number(optionRow.deductionOfPoint));\r\n        }\r\n        \r\n        // 将事项+分数填入加减分理由列\r\n        let reasonContent = optionRow.assessContent + \"(\" + optionRow.deductionOfPoint + \"分)\";\r\n        if(row.pointsReason){\r\n          this.$set(row, 'pointsReason', row.pointsReason + \"；\" + reasonContent);\r\n        }else{\r\n          this.$set(row, 'pointsReason', reasonContent);\r\n        }\r\n        \r\n        this.$refs[`popover${index}`].showPopper = false;\r\n        // 重新计算自评分数\r\n        this.scoreInput();\r\n      },\r\n\r\n      // 加减分输入\r\n      scoreInput(row = null){\r\n        // 验证加减分规则（仅当传入row参数时进行验证）\r\n        if (row && row.item) {\r\n          let noSpaceStr = row.item.replace(/\\s+/g, '');\r\n          let value = row.dePoints;\r\n\r\n          if (value !== null && value !== undefined && value !== '') {\r\n            let numValue = Number(value);\r\n\r\n            // 月度重点工作：只能为1、3或5分\r\n            if (noSpaceStr.includes(\"月度重点工作\")) {\r\n              if (![1, 3, 5].includes(numValue)) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '月度重点工作为得分制，只能为1分、3分或5分'\r\n                });\r\n                this.$set(row, 'dePoints', null);\r\n                return;\r\n              }\r\n            }\r\n            // 加分项：除月度重点工作外，只能填0\r\n            else if (noSpaceStr.includes(\"加分项\")) {\r\n              if (numValue !== 0) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '加分项只能填0分'\r\n                });\r\n                this.$set(row, 'dePoints', 0);\r\n                return;\r\n              }\r\n            }\r\n            // 其他类型项：只能填0或负数\r\n            else {\r\n              if (numValue > 0) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '该项目只能填0分或负数'\r\n                });\r\n                this.$set(row, 'dePoints', 0);\r\n                return;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 重新计算自评分数\r\n        this.calculateSelfScore();\r\n      },\r\n\r\n      /** 计算自评分数 */\r\n      calculateSelfScore() {\r\n        let points = 0;   // 月度重点工作分数\r\n        this.list.forEach(item => {\r\n            points += Number(item.dePoints);\r\n        });\r\n        // 计算总分：基础分85 + 加减分\r\n        this.selfScore = 85 + points;\r\n      },\r\n\r\n      // 签名上传相关\r\n      uploadSignature(){\r\n        const { isEmpty, data } = this.$refs.signaturePad.saveSignature();\r\n        console.log(isEmpty,data)\r\n        if(isEmpty){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请签名!'\r\n          });\r\n          return false;\r\n        }else{\r\n          const blobBin = atob(data.split(',')[1]);\r\n          let array = [];\r\n          for (let i = 0; i < blobBin.length; i++) {\r\n            array.push(blobBin.charCodeAt(i));\r\n          }\r\n          const fileBlob = new Blob([new Uint8Array(array)], { type: 'image/png' });\r\n          const formData = new FormData();\r\n          formData.append('file', fileBlob, `${Date.now()}.png`);\r\n          fetch(this.upload.url, {\r\n            method: 'POST',\r\n            body: formData,\r\n          })\r\n          .then(response => response.json())\r\n          .then(data => {\r\n            console.log('Success:', data);\r\n            if(data.code == 200){\r\n              this.sign = {fileName:this.userInfo.name + \".png\",url:data.url};\r\n              this.submit();\r\n            }else{\r\n              this.$message({\r\n                type: 'error',\r\n                message: '签名上传失败'\r\n              });\r\n            }\r\n          })\r\n          .catch((error) => {\r\n            console.error('Error:', error);\r\n            this.$message({\r\n              type: 'error',\r\n              message: '签名上传异常'\r\n            });\r\n          });\r\n        }\r\n\r\n      },\r\n\r\n      /** 验证单个字段（失焦时调用） */\r\n      validateField(row, fieldType) {\r\n        // 清除之前的错误信息\r\n        this.$set(row, `${fieldType}Error`, '');\r\n\r\n        if (fieldType === 'performance') {\r\n          // 验证完成实绩\r\n          if (!row.performance || row.performance.trim() === '') {\r\n            this.$set(row, 'performanceError', '请填写完成实绩');\r\n            return false;\r\n          }\r\n        } else if (fieldType === 'dePoints') {\r\n          // 验证加减分\r\n          if (row.dePoints === null || row.dePoints === undefined || row.dePoints === '') {\r\n            this.$set(row, 'dePointsError', '请填写加减分');\r\n            return false;\r\n          }\r\n        } else if (fieldType === 'pointsReason') {\r\n          // 验证加减分原因\r\n          if (row.dePoints != 0 && (!row.pointsReason || row.pointsReason.trim() === '')) {\r\n            // 检查是否为月度重点工作\r\n            let noSpaceStr = row.item.replace(/\\s+/g, '');\r\n            if (!noSpaceStr.includes(\"月度重点工作\")) {\r\n              // 非月度重点工作需要填写原因\r\n              this.$set(row, 'pointsReasonError', '有加减分的请填写原因');\r\n              return false;\r\n            }\r\n          }\r\n        }\r\n\r\n        return true;\r\n      },\r\n\r\n      /** 清除所有验证错误 */\r\n      clearValidationErrors() {\r\n        this.list.forEach(item => {\r\n          this.$set(item, 'performanceError', '');\r\n          this.$set(item, 'dePointsError', '');\r\n          this.$set(item, 'pointsReasonError', '');\r\n        });\r\n      },\r\n\r\n      /** 定位到字段并显示详细错误信息 */\r\n      focusAndScrollToField(refName, rowNumber, fieldName) {\r\n        console.log(`开始定位到字段: ${refName}, 行号: ${rowNumber}, 字段名: ${fieldName}`);\r\n\r\n        this.$nextTick(() => {\r\n          // 等待DOM更新后再执行\r\n          setTimeout(() => {\r\n            try {\r\n              const field = this.$refs[refName];\r\n              console.log('找到的字段元素:', field);\r\n\r\n              let targetElement = null;\r\n              let inputElement = null;\r\n\r\n              if (field && field.length > 0) {\r\n                // 数组形式的ref（v-for中的ref）\r\n                inputElement = field[0];\r\n                targetElement = inputElement.$el || inputElement;\r\n              } else if (field) {\r\n                // 单个ref\r\n                inputElement = field;\r\n                targetElement = inputElement.$el || inputElement;\r\n              }\r\n\r\n              if (targetElement && inputElement) {\r\n                console.log('找到目标元素，开始定位');\r\n\r\n                // 1. 先滚动到目标位置\r\n                targetElement.scrollIntoView({\r\n                  behavior: 'smooth',\r\n                  block: 'center',\r\n                  inline: 'nearest'\r\n                });\r\n\r\n                // 2. 等待滚动完成后聚焦\r\n                setTimeout(() => {\r\n                  try {\r\n                    inputElement.focus();\r\n                    console.log('已聚焦到输入框');\r\n                  } catch (focusError) {\r\n                    console.warn('聚焦失败:', focusError);\r\n                  }\r\n                }, 500);\r\n\r\n                // 3. 显示错误信息\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: `第${rowNumber}行 ${fieldName} 未填写完整，请检查并填写`,\r\n                  duration: 5000\r\n                });\r\n\r\n              } else {\r\n                // 如果找不到具体字段，尝试定位到表格行\r\n                console.warn(`找不到字段 ${refName}，尝试定位到表格行`);\r\n                this.scrollToTableRow(rowNumber - 1);\r\n\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: `第${rowNumber}行 ${fieldName} 未填写完整，请检查并填写`,\r\n                  duration: 5000\r\n                });\r\n              }\r\n\r\n            } catch (error) {\r\n              console.error('定位字段时发生错误:', error);\r\n              // 降级处理：至少滚动到表格区域\r\n              this.scrollToTable();\r\n\r\n              this.$message({\r\n                type: 'warning',\r\n                message: `第${rowNumber}行 ${fieldName} 未填写完整，请检查并填写`,\r\n                duration: 5000\r\n              });\r\n            }\r\n          }, 100); // 给一点时间让错误状态更新\r\n        });\r\n      },\r\n\r\n      /** 滚动到指定表格行 */\r\n      scrollToTableRow(rowIndex) {\r\n        try {\r\n          const tableRows = this.$el.querySelectorAll('.el-table__body tr');\r\n          if (tableRows && tableRows[rowIndex]) {\r\n            tableRows[rowIndex].scrollIntoView({\r\n              behavior: 'smooth',\r\n              block: 'center'\r\n            });\r\n            console.log(`已滚动到第${rowIndex + 1}行`);\r\n          } else {\r\n            console.warn(`找不到第${rowIndex + 1}行，滚动到表格`);\r\n            this.scrollToTable();\r\n          }\r\n        } catch (error) {\r\n          console.error('滚动到表格行失败:', error);\r\n          this.scrollToTable();\r\n        }\r\n      },\r\n\r\n      /** 滚动到表格区域 */\r\n      scrollToTable() {\r\n        try {\r\n          const table = this.$el.querySelector('.el-table');\r\n          if (table) {\r\n            table.scrollIntoView({\r\n              behavior: 'smooth',\r\n              block: 'start'\r\n            });\r\n            console.log('已滚动到表格区域');\r\n          }\r\n        } catch (error) {\r\n          console.error('滚动到表格失败:', error);\r\n        }\r\n      }\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .table-striped{\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n    text-align: center;\r\n    border: 1px #888;\r\n    border-collapse: collapse;\r\n  }\r\n  .table-striped th{\r\n    height: 32px;\r\n    border: 1px solid #888;\r\n    background-color: #dedede;\r\n  }\r\n  .table-striped td{\r\n    min-height: 32px;\r\n    border: 1px solid #888;\r\n  }\r\n  .table-input .el-textarea__inner{\r\n    border: 0 !important;\r\n    resize: none !important;\r\n  }\r\n  .table-input .el-input__inner{\r\n    border: 0 !important;\r\n  }\r\n\r\n  /* 错误状态样式 */\r\n  .table-input.is-error .el-input__inner,\r\n  .table-input.is-error .el-textarea__inner {\r\n    border: 1px solid #F56C6C !important;\r\n    background-color: #fef0f0 !important;\r\n  }\r\n\r\n  /* 错误提示样式 */\r\n  .el-form-item__error {\r\n    color: #F56C6C;\r\n    font-size: 12px;\r\n    line-height: 1;\r\n    padding-top: 4px;\r\n    position: absolute;\r\n    top: 100%;\r\n    left: 0;\r\n    z-index: 1;\r\n  }\r\n\r\n  /* 表格单元格相对定位，用于错误提示定位 */\r\n  .el-table .cell {\r\n    position: relative;\r\n  }\r\n  \r\n  .reason-text {\r\n    width: 100%;\r\n    margin-top: 8px;\r\n    padding: 8px 12px;\r\n    background-color: #f8f9fa;\r\n    border-left: 3px solid #409EFF;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .reason-label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n    margin-right: 8px;\r\n  }\r\n\r\n  .reason-content {\r\n    color: #303133;\r\n    line-height: 1.6;\r\n  }\r\n  /* .myUpload .el-upload--picture-card{\r\n    display:none !important; \r\n  } */\r\n  </style>\r\n"]}]}