<template>
  <div class="app-container">
    <!-- 供应商信息展示 -->
    <div class="supplier-info-bar">
      <div class="supplier-info-content">
        <div class="supplier-basic">
          <span class="supplier-code">{{ supplierCode }}</span>
          <span class="supplier-name">{{ supplierInfo.supplyName || '未设置供应商名称' }}</span>
          <el-tag v-if="supplierInfo.status" :type="supplierInfo.status === '1' ? 'success' : 'danger'" size="mini">
            {{ supplierInfo.status === '1' ? '正常' : '停用' }}
          </el-tag>
        </div>
        <div class="supplier-contact" v-if="supplierInfo.contactPerson || supplierInfo.contactPhone">
          <span v-if="supplierInfo.contactPerson">联系人：{{ supplierInfo.contactPerson }}</span>
          <span v-if="supplierInfo.contactPhone">电话：{{ supplierInfo.contactPhone }}</span>
        </div>
      </div>
    </div>

    <!-- 查询表单 -->
    <el-form :inline="true" :model="queryParams" class="demo-form-inline" @submit.native.prevent>
      <el-form-item label="用户姓名">
        <el-input v-model="queryParams.userName" placeholder="请输入用户姓名" clearable />
      </el-form-item>
      <el-form-item label="身份证">
        <el-input v-model="queryParams.idcard" placeholder="请输入身份证" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <div style="margin-bottom: 10px;">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增人员</el-button>
      <el-button type="success" icon="el-icon-upload" @click="handleImport">导入</el-button>
      <el-button type="warning" icon="el-icon-download" @click="handleExport">导出</el-button>
    </div>

    <!-- 人员列表 -->
    <el-table :data="userList" border stripe style="width: 100%">
      <el-table-column prop="userName" label="用户姓名" min-width="100" />
      <el-table-column prop="idcard" label="身份证" min-width="160" />
      <el-table-column label="岗位识别卡" min-width="110" align="center">
        <template slot-scope="scope">
          <el-button size="mini" @click="openFacDialog(scope.row)">补充/编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column label="健康信息" min-width="100" align="center">
        <template slot-scope="scope">
          <el-button size="mini" @click="openHealthDialog(scope.row)">补充/编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column label="附件" min-width="80" align="center">
        <template slot-scope="scope">
          <el-button size="mini" @click="openFileDialog(scope.row)">管理</el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="160" align="center">
        <template slot-scope="scope">
          <div class="operation-buttons">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-download"
              @click="downloadFile(scope.row)"
              title="下载">
              下载
            </el-button>
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              title="编辑">
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              title="删除">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增/编辑人员对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户姓名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户姓名" />
        </el-form-item>
        <el-form-item label="身份证" prop="idcard">
          <el-input v-model="form.idcard" placeholder="请输入身份证" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>



    <!-- 岗位识别卡弹窗 -->
    <vxe-modal v-model="facDialogVisible" title="岗位识别卡" width="700" show-footer>
      <vxe-form
        :data="facForm"
        :items="facFormItems"
        title-align="left"
        title-width="90"
        title-colon
        border
        size="small"
      />
      <template #footer>
        <vxe-button @click="facDialogVisible = false">取消</vxe-button>
        <vxe-button status="primary" @click="submitFac">保存</vxe-button>
      </template>
    </vxe-modal>

    <!-- 健康信息弹窗 -->
    <vxe-modal v-model="healthDialogVisible" title="健康信息" width="800" show-footer>
      <vxe-form
        :data="healthForm"
        :items="healthFormItems"
        title-align="left"
        title-width="90"
        title-colon
        border
        size="small"
      />
      <template #footer>
        <vxe-button @click="healthDialogVisible = false">取消</vxe-button>
        <vxe-button status="primary" @click="submitHealth">保存</vxe-button>
      </template>
    </vxe-modal>

    <!-- 附件管理弹窗 -->
    <el-dialog
      :visible.sync="fileDialogVisible"
      title="附件管理"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="fileUpload"
          :action="uploadUrl"
          :headers="upload.headers"
          :data="uploadData"
          :on-success="handleFileUploadSuccess"
          :on-error="handleFileUploadError"
          :before-upload="beforeFileUpload"
          :show-file-list="false"
          accept=".pdf"
          drag
          class="upload-dragger"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将PDF文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持PDF格式，单个文件不超过50MB</div>
        </el-upload>
      </div>

      <!-- 文件列表 -->
      <div class="file-list-section">
        <div class="file-list-header">
          <i class="el-icon-document"></i>
          <span class="file-list-title">已上传文件</span>
          <span class="file-count">(共 {{fileList.length}} 个文件)</span>
        </div>
        <div class="file-list-content">
          <el-table
            :data="fileList"
            style="width: 100%"
            :header-cell-style="{background:'#f5f7fa',color:'#606266'}"
          >
            <el-table-column prop="filename" label="文件名" min-width="200">
              <template slot-scope="scope">
                <div class="file-info">
                  <i class="el-icon-document"></i>
                  <span class="file-name">{{scope.row.filename}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="format" label="格式" width="80" align="center">
              <template slot-scope="scope">
                <el-tag size="mini" type="info">{{scope.row.format}}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="state" label="状态" width="80" align="center">
              <template slot-scope="scope">
                <el-tag
                  size="mini"
                  :type="scope.row.state === 1 ? 'success' : 'danger'"
                >
                  {{scope.row.state === 1 ? '正常' : '异常'}}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center">
              <template slot-scope="scope">
                <div class="operation-buttons">
                  <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-download"
                    @click="downloadFileItem(scope.row)"
                  >
                    下载
                  </el-button>
                  <el-button
                    size="mini"
                    type="danger"
                    icon="el-icon-delete"
                    @click="deleteFile(scope.row)"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 人员导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="upload.isUploading" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import { getToken } from '@/utils/auth'

export default {
  name: 'SupplierInfo',
  data() {
    return {
      // 供应商信息
      supplierInfo: {},
      supplierCode: '',
      // 人员列表
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        supplyCode: null,
        userName: null,
        idcard: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        userName: [
          { required: true, message: "用户姓名不能为空", trigger: "blur" }
        ],
        idcard: [
          { required: true, message: "身份证不能为空", trigger: "blur" }
        ]
      },
      // 总条数
      total: 0,
      // 岗位识别卡
      facDialogVisible: false,
      facForm: {},
      facFormItems: [
        { field: 'userPost', title: '岗位名称', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入岗位名称', rows: 2 } } },
        { field: 'userFacClass', title: '岗位班组', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位班组' } } },
        { field: 'userDeptName', title: '所属部门', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入所属部门' } } },
        { field: 'userFacWork', title: '岗位描述', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位描述' } } },
        { field: 'userTimeBegin', title: '入厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },
        { field: 'userTimeEnd', title: '离厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },
        {
          field: 'state',
          title: '状态',
          span: 24,
          itemRender: {
            name: 'VxeSelect',
            options: [
              { label: '起草', value: 0 },
              { label: '分厂审核人', value: 1 },
              { label: '人力资源部', value: 2 },
              { label: '退回', value: -1 },
              { label: '禁用', value: 101 },
              { label: '审核通过', value: 99 },
              { label: '删除', value: 102 }
            ],
            props: { placeholder: '请选择' }
          }
        }
      ],
      // 健康信息
      healthDialogVisible: false,
      healthForm: {},
      healthFormItems: [
        { field: 'healdate', title: '体检日期', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },
        { field: 'hos', title: '医院', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入医院' } } },
        { field: 'healtz', title: '体重', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重' } } },
        { field: 'healtzzs', title: '体重指数', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重指数' } } },
        { field: 'healptt', title: '血糖', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入血糖' } } },
        { field: 'healssy', title: '收缩压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入收缩压' } } },
        { field: 'healszy', title: '舒张压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入舒张压' } } },
        { field: 'healzdgc', title: '总胆固醇', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入总胆固醇' } } },
        { field: 'healgysz', title: '甘油三酯', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入甘油三酯' } } },
        { field: 'healga', title: '谷氨酰转肽酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷氨酰转肽酶' } } },
        { field: 'healgb', title: '谷丙转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷丙转氨酶' } } },
        { field: 'healgc', title: '谷草转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷草转氨酶' } } },
        { field: 'healnsd', title: '尿素氮', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入尿素氮' } } },
        { field: 'healjg', title: '肌酐', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入肌酐' } } },
        { field: 'healxd', title: '心电图', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入心电图' } } },
        { field: 'healxj', title: '小结', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入小结', rows: 2 } } },
        { field: 'healjy', title: '建议', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入建议', rows: 2 } } },
        {
          field: 'state',
          title: '状态',
          span: 12,
          itemRender: {
            name: 'VxeSelect',
            options: [
              { label: '正常', value: 1 },
              { label: '删除', value: 101 }
            ],
            props: { placeholder: '请选择' }
          }
        }
      ],
      // 附件管理
      fileDialogVisible: false,
      fileList: [],
      uploadUrl: process.env.VUE_APP_BASE_API + '/web/supply/userfile/upload',
      currentUserId: null,
      currentUserInfo: {},
      // 导入参数
      upload: {
        // 是否显示弹出层（导入）
        open: false,
        // 弹出层标题（导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/web/supply/supplier/import"
      }
    };
  },
  computed: {
    uploadData() {
      return {
        userid: this.currentUserId
      }
    }
  },
  created() {
    this.initSupplierCode();
  },
  methods: {
    /** 初始化供应商代码 */
    initSupplierCode() {
      // 获取当前登录用户信息
      this.$store.dispatch('GetInfo').then(res => {
        const username = res.user.userName;
        // 当前用户编号的前7位为供应商编号
        this.supplierCode = username.substring(0, 7);
        this.queryParams.supplyCode = this.supplierCode;

        // 在获取到供应商代码后，再调用其他初始化方法
        this.getSupplierInfo();
        this.getList();
      }).catch(() => {
        this.$message.error('获取用户信息失败');
      });
    },
    /** 获取供应商信息 */
    getSupplierInfo() {
      if (this.supplierCode) {
        // 调用SupplyInfoController中的方法查询供应商信息
        request.get(`/web/supply/info/getByCode/${this.supplierCode}`).then(response => {
          if (response.code === 200 && response.data) {
            this.supplierInfo = response.data;
          } else {
            // 如果供应商信息不存在，显示默认信息
            this.supplierInfo = {
              supplyCode: this.supplierCode,
              supplyName: '',
              contactPerson: '',
              contactPhone: '',
              address: '',
              status: '1'
            };
          }
        }).catch(() => {
          // 如果供应商信息不存在，显示默认信息
          this.supplierInfo = {
            supplyCode: this.supplierCode,
            supplyName: '',
            contactPerson: '',
            contactPhone: '',
            address: '',
            status: '1'
          };
        });
      }
    },
    /** 查询人员列表 */
    getList() {
      // 调用供应商专用接口查询人员列表
      request.get('/web/supply/supplier/list', {
        params: this.queryParams
      }).then(response => {
        if (response.code === 200) {
          this.userList = response.rows || [];
          this.total = response.total || 0;
        } else {
          this.$message.error(response.msg || '查询失败');
          this.userList = [];
          this.total = 0;
        }
      }).catch(error => {
        this.$message.error('查询失败: ' + error.message);
        this.userList = [];
        this.total = 0;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        supplyCode: this.supplierCode,
        userName: null,
        idcard: null
      };
      this.getList();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加人员";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      // 调用供应商专用接口获取人员信息
      request.get(`/web/supply/supplier/user/${id}`).then(response => {
        if (response.code === 200) {
          this.form = response.data;
          this.open = true;
          this.title = "修改人员";
        } else {
          this.$message.error(response.msg || '获取人员信息失败');
        }
      }).catch(error => {
        this.$message.error('获取人员信息失败: ' + error.message);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            // 调用供应商专用修改接口
            request.put('/web/supply/supplier/user', this.form).then(response => {
              if (response.code === 200) {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.$message.error(response.msg || '修改失败');
              }
            }).catch(error => {
              this.$message.error('修改失败: ' + error.message);
            });
          } else {
            // 调用供应商专用新增接口
            request.post('/web/supply/supplier/user', this.form).then(response => {
              if (response.code === 200) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.$message.error(response.msg || '新增失败');
              }
            }).catch(error => {
              this.$message.error('新增失败: ' + error.message);
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除人员编号为"' + ids + '"的数据项？').then(() => {
        // 调用供应商专用删除接口
        return request.delete(`/web/supply/supplier/user/${ids}`);
      }).then(response => {
        if (response.code === 200) {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        } else {
          this.$message.error(response.msg || '删除失败');
        }
      }).catch(error => {
        if (error !== 'cancel') {
          this.$message.error('删除失败: ' + error.message);
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('web/supply/supplier/export', {
        ...this.queryParams
      }, `supplier_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "人员导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('web/supply/supplier/importTemplate', {}, `supplier_template_${new Date().getTime()}.xlsx`)
    },
    /** 文件上传中处理 */
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    /** 文件上传成功处理 */
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: null,
        supplyCode: this.supplierCode,
        userName: null,
        idcard: null
      };
      this.resetForm("form");
    },

    /** 打开岗位识别卡对话框 */
    openFacDialog(row) {
      // 调用供应商专用接口获取岗位识别卡信息
      request.get(`/web/supply/supplier/fac/${row.id}`).then(response => {
        if (response.code === 200) {
          this.facForm = response.data || { userId: row.id };
        } else {
          this.facForm = { userId: row.id };
        }
        this.facDialogVisible = true;
      }).catch(() => {
        this.facForm = { userId: row.id };
        this.facDialogVisible = true;
      });
    },
    /** 提交岗位识别卡 */
    submitFac() {
      const url = this.facForm.id ? '/web/supply/supplier/fac' : '/web/supply/supplier/fac';
      const method = this.facForm.id ? 'put' : 'post';

      request[method](url, this.facForm).then(response => {
        if (response.code === 200) {
          this.$message.success('保存成功');
          this.facDialogVisible = false;
          this.getList();
        } else {
          this.$message.error(response.msg || '保存失败');
        }
      }).catch(error => {
        this.$message.error('保存失败: ' + error.message);
      });
    },
    /** 打开健康信息对话框 */
    openHealthDialog(row) {
      // 调用供应商专用接口获取健康信息
      request.get(`/web/supply/supplier/health/${row.id}`).then(response => {
        if (response.code === 200) {
          this.healthForm = response.data || { userid: row.id };
        } else {
          this.healthForm = { userid: row.id };
        }
        this.healthDialogVisible = true;
      }).catch(() => {
        this.healthForm = { userid: row.id };
        this.healthDialogVisible = true;
      });
    },
    /** 提交健康信息 */
    submitHealth() {
      const url = this.healthForm.id ? '/web/supply/supplier/health' : '/web/supply/supplier/health';
      const method = this.healthForm.id ? 'put' : 'post';

      request[method](url, this.healthForm).then(response => {
        if (response.code === 200) {
          this.$message.success('保存成功');
          this.healthDialogVisible = false;
          this.getList();
        } else {
          this.$message.error(response.msg || '保存失败');
        }
      }).catch(error => {
        this.$message.error('保存失败: ' + error.message);
      });
    },
    /** 打开文件管理对话框 */
    openFileDialog(row) {
      this.currentUserId = row.id;
      this.currentUserInfo = row;
      this.getFileList(row.id);
      this.fileDialogVisible = true;
    },
    /** 获取文件列表 */
    getFileList(userid) {
      // 调用供应商专用接口获取文件列表
      request.get(`/web/supply/supplier/file/list/${userid}`).then(response => {
        if (response.code === 200) {
          this.fileList = response.rows || [];
        } else {
          this.fileList = [];
        }
      }).catch(() => {
        this.fileList = [];
      });
    },
    /** 文件上传成功处理 */
    handleFileUploadSuccess(response) {
      if (response.code === 200) {
        this.$message.success('文件上传成功');
        this.getFileList(this.currentUserId);
      } else {
        this.$message.error(response.msg || '文件上传失败');
      }
    },
    /** 文件上传错误处理 */
    handleFileUploadError(err) {
      this.$message.error('文件上传失败: ' + (err.message || '未知错误'));
    },
    /** 文件上传前检查 */
    beforeFileUpload(file) {
      const isPDF = file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');
      if (!isPDF) {
        this.$message.error('只能上传PDF格式文件！');
        return false;
      }

      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!');
        return false;
      }

      return true;
    },
    /** 删除文件 */
    deleteFile(row) {
      this.$confirm('确定删除该附件吗？', '提示', { type: 'warning' }).then(() => {
        // 调用供应商专用删除文件接口
        request.delete(`/web/supply/supplier/file/${row.id}`).then(response => {
          if (response.code === 200) {
            this.$message.success('删除成功');
            this.getFileList(this.currentUserId);
          } else {
            this.$message.error(response.msg || '删除失败');
          }
        }).catch(error => {
          this.$message.error('删除失败: ' + error.message);
        });
      }).catch(() => {});
    },
    /** 下载单个文件 */
    downloadFileItem(row) {
      // 调用下载接口获取文件URL
      request.get(`/web/supply/userfile/download/${row.id}`).then(response => {
        if (response.code === 200) {
          const fileUrl = response.data;
          window.open(fileUrl, '_blank');
        } else {
          this.$message.error(response.msg || '下载失败');
        }
      }).catch(error => {
        this.$message.error('下载失败: ' + error.message);
      });
    },
    /** 下载文件 */
    downloadFile(row) {
      // 调用下载接口获取文件URL
      request.get(`/web/supply/userfile/download/${row.id}`).then(response => {
        if (response.code === 200) {
          // 获取到文件URL后，在新窗口中打开下载
          const fileUrl = response.data
          window.open(fileUrl, '_blank')
        } else {
          this.$message.error(response.msg || '下载失败')
        }
      }).catch(error => {
        this.$message.error('下载失败: ' + error.message)
      })
    },
    /** 提交上传文件 */
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>

<style scoped>
.supplier-info-bar {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.supplier-info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.supplier-basic {
  display: flex;
  align-items: center;
  gap: 12px;
}

.supplier-code {
  font-size: 18px;
  font-weight: 600;
  color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
  padding: 4px 12px;
  border-radius: 4px;
  border: 1px solid rgba(64, 158, 255, 0.2);
}

.supplier-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.supplier-contact {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #606266;
}

.supplier-contact span {
  display: flex;
  align-items: center;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.operation-buttons .el-button {
  margin: 0;
}

/* 附件管理样式 */
.upload-section {
  margin-bottom: 20px;
}

.upload-dragger {
  width: 100%;
}

.upload-dragger .el-upload-dragger {
  width: 100%;
  height: 120px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.upload-dragger .el-upload-dragger:hover {
  border-color: #409EFF;
}

.upload-dragger .el-icon-upload {
  font-size: 28px;
  color: #c0c4cc;
  margin: 20px 0 16px;
  line-height: 50px;
}

.upload-dragger .el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.upload-dragger .el-upload__text em {
  color: #409EFF;
  font-style: normal;
}

.upload-dragger .el-upload__tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
  margin-top: 8px;
}

.file-list-section {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-top: 20px;
}

.file-list-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  color: #606266;
}

.file-list-title {
  margin-left: 8px;
  font-size: 16px;
}

.file-count {
  margin-left: 10px;
  font-size: 14px;
  color: #909399;
}

.file-list-content {
  background-color: #fff;
  border-radius: 4px;
  padding: 10px;
  border: 1px solid #ebeef5;
}

.file-info {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.file-name {
  margin-left: 8px;
  font-size: 14px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
}
</style>
