<template>
  <div class="app-container">
    <el-descriptions class="margin-top" title="用户信息" :column="3" border>
      <el-descriptions-item>
        <template slot="label">
          姓名
        </template>
        {{ userInfo.name }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          工号
        </template>
        {{ userInfo.workNo }}
      </el-descriptions-item>
      <!-- <el-descriptions-item>
        <template slot="label">
          身份
        </template>
        <span v-if="userInfo.assessRole == '0'">干部</span>
        <span v-if="userInfo.assessRole == '1'">一把手</span>
        <span v-if="userInfo.assessRole == '2'">条线领导</span>
      </el-descriptions-item> -->
      <el-descriptions-item>
        <template slot="label">
          部门
        </template>
        <span v-for="item,index in userInfo.deptList" v-bind:key="index">{{ item.deptName }}</span>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          职务
        </template>
        <span>{{ userInfo.job }}</span>
      </el-descriptions-item>
    </el-descriptions>

    <h4>指标配置</h4>
    <el-row :gutter="10" class="mb8" style="margin-top: 10px;">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-edit"
          @click="handleEdit"
          size="small"
        >编辑</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-upload
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :disabled="upload.isUploading"
        :action="upload.url"
        :show-file-list="false"
        :multiple="false"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess">
            <el-button size="small" type="warning" plain icon="el-icon-download">导入</el-button>
        </el-upload>
      </el-col>
      <el-col :span="1.5">
          <el-button size="small" type="info" plain icon="el-icon-link" @click="downloadTemplate">导入模板下载</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="targetList" 
      :span-method="objectSpanMethod" border>
      <el-table-column label="类型" align="center" prop="item" width="200"/>
      <el-table-column label="指标" align="center" prop="category" width="200"/>
      <el-table-column label="目标" align="center" prop="target" />
      <el-table-column label="评分标准" align="center" prop="standard" />
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    
    <!-- 导入绩效考核-自评指标配置对话框 -->
    <el-dialog :title="editTitle" :visible.sync="openEdit" width="1000px" append-to-body>
      <div style="color: red;">
        注：提交前可对内容进行修改; 鼠标按住行,拖动可变换排列顺序；确认提交后将覆盖原有配置信息。
      </div>
      <table class="table-striped">
        <thead class="thead-dark">
          <tr>
            <th scope="col">序号</th>
            <th scope="col">类型</th>
            <th scope="col">指标</th>
            <th scope="col">目标</th>
            <th scope="col">评分标准</th>
            <th scope="col">操作</th>
          </tr>
        </thead>
        <draggable v-model="editData" tag="tbody" item-key="name">
          <tr v-for="element,index in editData" v-bind:key="index">
            <td scope="row">{{ index + 1 }}</td>
            <td>
              <el-input class="table-input" type="textarea" autosize v-model="element.item" placeholder="请输入类型"></el-input>
            </td>
            <td>
              <el-input class="table-input" type="textarea" autosize v-model="element.category" placeholder="请输入指标"></el-input>
            </td>
            <td>
              <el-input class="table-input" type="textarea" autosize v-model="element.target" placeholder="请输入目标"></el-input>
            </td>
            <td>
              <el-input class="table-input" type="textarea" autosize v-model="element.standard" placeholder="请输入评分标准"></el-input>
            </td>
            <td>
              <div>
                <el-button
                  size="mini"
                  type="text"
                  @click="handleEditDelete(index)"
                >删除</el-button>
              </div>
            </td>
          </tr>
        </draggable>
      </table>
      <div>
        <el-button type="primary" 
        icon="el-icon-plus" size="mini" @click="addRow">添加行</el-button>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEdit">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { getTemplateFile } from "@/api/templateFile/list";
import { batchTarget, listTargetAll } from "@/api/assess/self/target";
import { getSelfAssessUser } from "@/api/assess/self/user";
import draggable from 'vuedraggable'

export default {
  name: "SelfAssessTarget",
  components: {
    draggable
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 绩效考核-自评指标配置表格数据
      targetList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        workNo: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 用户信息
      userInfo:{},
      // 编辑弹出框显示
      openEdit:false,
      // 导入参数
      upload: {
          // 是否禁用上传
          isUploading: false,
          // 设置上传的请求头部
          headers: { Authorization: 'Bearer ' + getToken() },
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/web/selfAssess/target/handleImport",
      },
      editTitle:"",
      // 预览/编辑配置数据
      editData:[],
      // 合并单元格信息
      spanList:{
        itemList:[],
        standardList:[]
      },
    };
  },
  created() {
    this.initPageData();
  },

  // 监听路由变化，确保每次进入页面都重新获取数据
  watch: {
    '$route'(to) {
      // 当路由发生变化时，重新初始化页面数据
      if (to.path === '/assess/self/user/detail') {
        this.initPageData();
      }
    }
  },

  // 路由更新时的钩子
  beforeRouteUpdate(to, from, next) {
    // 在当前路由改变，但是该组件被复用时调用
    this.queryParams.userId = to.query.userId;
    this.getSelfAssessUser();
    this.getList();
    next();
  },
  methods: {
    // 初始化页面数据
    initPageData() {
      // 获取路径参数
      this.queryParams.userId = this.$route.query.userId;

      // 如果没有userId参数，返回上一页或首页
      if (!this.queryParams.userId) {
        this.$message.error('缺少必要参数');
        this.$router.go(-1);
        return;
      }

      // 重置数据
      this.userInfo = {};
      this.targetList = [];
      this.editData = [];

      // 获取用户信息和指标配置
      this.getSelfAssessUser();
      this.getList();
    },

    getSelfAssessUser(){
      if (!this.queryParams.userId) {
        return;
      }
      getSelfAssessUser({id:this.queryParams.userId}).then(res => {
        this.userInfo = res.data
      }).catch(error => {
        console.error('获取用户信息失败:', error);
        this.$message.error('获取用户信息失败');
      })
    },
    /** 查询绩效考核-自评指标配置列表 */
    getList() {
      if (!this.queryParams.userId) {
        this.loading = false;
        return;
      }

      this.loading = true;
      listTargetAll(this.queryParams).then(response => {
        this.handleSpanList(response.data);
        this.targetList = response.data;
        this.loading = false;
      }).catch(error => {
        console.error('获取指标配置失败:', error);
        this.$message.error('获取指标配置失败');
        this.loading = false;
      });
    },

    // 处理列表
    handleSpanList(data){
      let itemList = [];
      let standardList = [];
      let itemFlag = 0;
      let standardFlag = 0;
      for(let i = 0; i < data.length; i++){
        // 相同考核项、评分标准合并
        if(i == 0){
          itemList.push({
            rowspan: 1,
            colspan: 1
          })
          standardList.push({
            rowspan: 1,
            colspan: 1
          })
        }else{
          // 考核项
          if(data[i - 1].item == data[i].item){
            itemList.push({
              rowspan: 0,
              colspan: 0
            })
            itemList[itemFlag].rowspan += 1;
          }else{
            itemList.push({
              rowspan: 1,
              colspan: 1
            })
            itemFlag = i;
          }
          // 评分标准
          if(data[i - 1].standard == data[i].standard){
            standardList.push({
              rowspan: 0,
              colspan: 0
            })
            standardList[standardFlag].rowspan += 1;
          }else{
            standardList.push({
              rowspan: 1,
              colspan: 1
            })
            standardFlag = i;
          }
        }
      }
      this.spanList.itemList = itemList;
      this.spanList.standardList = standardList;
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        workNo: null,
        sort: null,
        item: null,
        category: null,
        target: null,
        standard: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 编辑按钮点击事件
    handleEdit(){
      this.editData = this.handleToEditInfo(JSON.parse(JSON.stringify(this.targetList)));
      this.editTitle = "配置编辑"
      this.openEdit = true;
    },

    /** 导出按钮操作 */
    // handleExport() {
    //   this.download('selfAssess/target/export', {
    //     ...this.queryParams
    //   }, `target_${new Date().getTime()}.xlsx`)
    // },

    cancelEdit(){
      this.openEdit = false;
    },

    // 确认编辑、导入
    submitEdit(){
      console.log(this.editData)
      if(!this.verifyEdit()){
          this.$message({
            type: 'warning',
            message: '信息未填写完整'
          });
          return;
      };
      this.$confirm('确认后将覆盖原有数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.batchData();
      }).catch(() => {
          
      });
    },

    // 提交数据验证
    verifyEdit(){
      for(let i = 0; i < this.editData.length; i++){
        if(!this.editData[i].item) return false;
        // if(!this.editData[i].category) return false;
        if(!this.editData[i].target) return false;
        if(!this.editData[i].standard) return false;
      }
      return true;
    },

    // 新增数据
    batchData(){
      let data = this.handleEditData(this.editData);
      batchTarget(data).then(res => {
        if(res.code == 200){
          this.openEdit = false;
          this.editData = [];
          this.getList();
          this.$message({
            type: 'success',
            message: '提交成功!'
          });
        }
      })
    },

    // 处理提交数据
    handleEditData(data){
      for(let i = 0; i < data.length; i++){
        data[i].sort = i + 1;
        data[i].userId = this.queryParams.userId;
      }
      return data
    },

    // 处理导入内容
    handleToEditInfo(data){
        for(let i = 0; i < data.length; i++){
          if(data[i].id){
            data[i].id = null;
          }
          // 没有考核项取上一行值
          if(!data[i].item){
            data[i].item = data[i-1].item
          }
          // 没有标准取上一行值
          if(!data[i].standard){
            data[i].standard = data[i-1].standard
          }
          // 没有类别 有目标 类别取上一行内容
          if(!data[i].category && data[i].target){
            // 没有类别且没有目标，
            data[i].category = data[i-1].category
          }
          // 有类别 没有目标 目标取类别内容 类别空
          if(data[i].category && !data[i].target){
            // 没有类别且没有目标，
            data[i].target = data[i].category;
            data[i].category = ""
          }
        }
        return data;
    },


    handleFileUploadProgress(){
        this.upload.isUploading = true
    },
    handleFileSuccess(response){
        console.log(response)
        this.upload.isUploading = false
        this.editData = this.handleToEditInfo(response.data);
        this.editTitle = "导入预览";
        this.openEdit = true;
    },

    // 模板下载
    downloadTemplate(){
      getTemplateFile({id:"42"}).then(res => {
          if(res.code == 200){
            let localUrl = window.location.host;
            if(localUrl === "************:8099"){
              res.data.url = res.data.url.replace("ydxt.citicsteel.com:8099","************:8099");
            }
            let url = res.data.url;
            window.open(url);
          }
      })
    },

    // 编辑行删除
    handleEditDelete(index){
      this.editData.splice(index,1)
    },

    // 添加行
    addRow(){
      this.editData.push({
        item: null,
        category: null,
        target: null,
        standard: null,
      })
    },

    // 合并单元格方法
    objectSpanMethod({ row, rowIndex, columnIndex }) {
      // 第一列相同项合并
      if (columnIndex === 0) {
        return this.spanList.itemList[rowIndex];
      }
      // 评分标准相同合并
      if(columnIndex === 3){
        return this.spanList.standardList[rowIndex];
      }
      // 类别无内容 合并
      if(columnIndex === 1){
        if(!row.category){
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      if(columnIndex === 2){
        if(!row.category){
          return {
            rowspan: 1,
            colspan: 2
          }
        }
      }
    }
  }
};
</script>
<style>
.table-striped{
  margin-top: 10px;
  margin-bottom: 10px;
  width: 100%;
  text-align: center;
  border: 1px #888;
  border-collapse: collapse;
}
.table-striped th{
  height: 32px;
  border: 1px solid #888;
  background-color: #dedede;
}
.table-striped td{
  min-height: 32px;
  border: 1px solid #888;
}
.table-input .el-textarea__inner{
  border: 0 !important;
  resize: none !important;
}
</style>
