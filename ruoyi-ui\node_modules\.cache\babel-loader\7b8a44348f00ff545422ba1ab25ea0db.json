{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\index.vue", "mtime": 1756456493918}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_punishment", "require", "_purdchaseFactoryStock", "_ruoyi", "_suppInfoModule", "_interopRequireDefault", "_materialInfoModule", "_serviceModule", "_projectModule", "_punishmentMeasureModule", "_punishmentBasisModule", "name", "components", "SuppInfoDialog", "MaterialInfoDialog", "ServiceProjectDialog", "ProjectDialog", "PunishmentMeasureDialog", "PunishmentBasisDialog", "data", "_this", "loading", "ids", "single", "multiple", "showSearch", "total", "punishmentList", "title", "open", "isViewMode", "currentSelectType", "userGroup", "permissions", "canAdd", "canEdit", "canDelete", "canConfirm", "canExport", "<PERSON><PERSON><PERSON><PERSON>", "punishmentTypeOptions", "statusOptions", "suppTypeOptions", "getDepNameList", "happenedTimeRange", "punishmentTimeRange", "queryParams", "pageNum", "pageSize", "recCreator", "recCreateTime", "recRevisor", "recReviseTime", "userName", "serialNo", "companyCode", "deptNo", "suppId", "suppName", "itemNo", "itemName", "suppType", "punishmentType", "stateId", "punishmentReason", "punishmentBasis", "punishmentMeasure", "form", "punishmentMeasureTags", "rules", "required", "trigger", "message", "validator", "rule", "value", "callback", "trim", "Error", "happenedTime", "punishmentTime", "created", "getDictData", "getDeptList", "getUserGroupPermissions", "methods", "addDateRange", "_this2", "getDicts", "then", "response", "getList", "_this3", "showNoPermissionMessage", "console", "log", "params", "_objectSpread2", "default", "length", "listPunishment", "rows", "_this4", "_this5", "getUserGroup", "code", "setPermissions", "catch", "error", "allowedGroups", "includes", "setQueryGroupDefaults", "cancel", "reset", "id", "resetForm", "punishmentTypeFormat", "row", "column", "selectDictLabel", "stateFormat", "dateFormat", "cellValue", "year", "substring", "month", "day", "concat", "convertDateFormat", "dateValue", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "handleAdd", "$store", "getters", "getUserCompanyInfo", "handleUpdate", "_this6", "msgError", "selectedRow", "find", "getPunishment", "parseMeasureTextToTags", "handleView", "_this7", "submitForm", "_this8", "$refs", "validate", "valid", "updatePunishment", "msgSuccess", "addPunishment", "handleConfirm", "_this9", "recordsToConfirm", "selectedRows", "filter", "hasNonDraftRecord", "some", "Array", "isArray", "validationResult", "validateRequired<PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON>", "confirmIds", "join", "$confirm", "cancelButtonText", "confirmButtonText", "type", "confirmPunishment", "records", "requiredFields", "field", "i", "record", "recordIndex", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "requiredField", "fieldValue", "err", "e", "f", "handleDelete", "_this0", "delPunishment", "showSuppInfoDialog", "suppInfoDialog", "show", "showSuppInfoDialogForQuery", "handleSuppSelect", "suppInfo", "showMaterialInfoDialogForForm", "materialInfoDialog", "showMaterialInfoDialogForQuery", "handleMaterialSelect", "materialInfo", "itemId", "showServiceDialogForForm", "serviceDialog", "showServiceDialogForQuery", "handleServiceSelect", "serviceInfo", "serviceNo", "serviceName", "showProjectDialogForForm", "projectDialog", "showProjectDialogForQuery", "handleProjectSelect", "projectInfo", "projectNo", "projectName", "handleServiceProjectSelect", "handleMaterialOrServiceChange", "_this1", "$nextTick", "clearValidate", "handleQueryMaterialOrServiceChange", "showPunishmentMeasureDialog", "punishmentMeasureDialog", "handlePunishmentMeasureSelect", "measureText", "showPunishmentBasisDialog", "punishmentBasisDialog", "handlePunishmentBasisSelect", "basisText", "_this10", "measures", "split", "for<PERSON>ach", "measure", "tag", "createMeasureTag", "push", "text", "getTagType", "typeMap", "removeMeasureTag", "index", "splice", "updateMeasureText", "measureTexts", "_this11", "getUserCompany", "deptName", "rsDeptName", "nick<PERSON><PERSON>", "warn", "handleExport", "_this12", "exportPunishment", "download", "msg"], "sources": ["src/views/suppPunishment/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n      label-width=\"100px\"\r\n    >\r\n    <el-form-item label=\"编号\" prop=\"serialNo\">\r\n        <el-input\r\n          v-model=\"queryParams.serialNo\"\r\n          placeholder=\"请输入编号\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"申请部门\" prop=\"deptNo\">\r\n        <el-select\r\n          v-model=\"queryParams.deptNo\"\r\n          placeholder=\"请选择申请部门\"\r\n          clearable\r\n          size=\"small\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in getDepNameList\"\r\n            :key=\"item\"\r\n            :label=\"item\"\r\n            :value=\"item\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"填报人\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入填报人\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"确认人\" prop=\"confirmName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入确认人\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"确认部门\" prop=\"companyCode\">\r\n        <el-input\r\n          v-model=\"queryParams.companyCode\"\r\n          placeholder=\"请输入确认部门\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>     \r\n      <el-form-item label=\"供应商代码\" prop=\"suppId\">\r\n        <el-input\r\n          v-model=\"queryParams.suppId\"\r\n          placeholder=\"请输入供应商代码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        >\r\n          <i slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showSuppInfoDialogForQuery\"></i>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"供应商名称\" prop=\"suppName\">\r\n        <el-input\r\n          v-model=\"queryParams.suppName\"\r\n          placeholder=\"请输入供应商名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"处罚类型\" prop=\"punishmentType\">\r\n        <el-select v-model=\"queryParams.punishmentType\" placeholder=\"请选择处罚类型\" clearable size=\"small\">\r\n          <el-option\r\n            v-for=\"dict in punishmentTypeOptions\"\r\n            :key=\"dict.dictValue\"\r\n            :label=\"dict.dictLabel\"\r\n            :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"供应类型\" prop=\"suppType\">\r\n        <el-select v-model=\"queryParams.suppType\" placeholder=\"请选择货物、服务或工程\" clearable size=\"small\" @change=\"handleQueryMaterialOrServiceChange\">\r\n          <el-option\r\n            v-for=\"dict in suppTypeOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      \r\n      <el-form-item label=\"状态\" prop=\"stateId\">\r\n        <div style=\"display: flex; align-items: center;\">\r\n          <el-select\r\n            v-model=\"queryParams.stateId\"\r\n            placeholder=\"请选择状态\"\r\n            :clearable=\"userGroup !== 'query'\"\r\n            :disabled=\"userGroup === 'query'\"\r\n            size=\"small\"\r\n            style=\"width: 180px;\"\r\n          >\r\n            <el-option\r\n              v-for=\"dict in statusOptions\"\r\n              :key=\"dict.dictValue\"\r\n              :label=\"dict.dictLabel\"\r\n              :value=\"dict.dictValue\"\r\n            />\r\n          </el-select>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item label=\"事件发生时间\">\r\n        <el-date-picker\r\n          v-model=\"happenedTimeRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          format=\"yyyy/MM/dd\"\r\n          value-format=\"yyyy/MM/dd\"\r\n          size=\"small\"\r\n          clearable>\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"处罚执行时间\">\r\n        <el-date-picker\r\n          v-model=\"punishmentTimeRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          format=\"yyyy/MM/dd\"\r\n          value-format=\"yyyy/MM/dd\"\r\n          size=\"small\"\r\n          clearable>\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      \r\n      <el-form-item>\r\n        <el-button\r\n          type=\"cyan\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          :disabled=\"!permissions.canQuery\"\r\n          @click=\"handleQuery\"\r\n        >搜索</el-button>\r\n        <el-button\r\n          icon=\"el-icon-refresh\"\r\n          size=\"mini\"\r\n          :disabled=\"!permissions.canQuery\"\r\n          @click=\"resetQuery\"\r\n        >重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canAdd\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canEdit\">\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canConfirm\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-check\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleConfirm\"\r\n        >确认</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canDelete\">\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\" v-if=\"permissions.canExport\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n        >导出</el-button>\r\n      </el-col>\r\n\r\n\t  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 无权限提示 -->\r\n    <div v-if=\"!permissions.canQuery\" class=\"no-permission-container\">\r\n      <el-alert\r\n        title=\"权限不足\"\r\n        type=\"warning\"\r\n        description=\"您没有访问此页面的权限，请联系管理员获取相应权限。\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        center>\r\n      </el-alert>\r\n    </div>\r\n\r\n    <div v-else class=\"table-container\">\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"punishmentList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"状态\" width=\"70\" align=\"center\" prop=\"stateId\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.stateId == 1\" type=\"info\">草稿</el-tag>\r\n            <el-tag v-if=\"scope.row.stateId == 2\" type=\"success\">确认</el-tag>         \r\n          </template>\r\n        </el-table-column>\r\n        \r\n        <el-table-column label=\"编号\" width=\"140\" align=\"center\" prop=\"serialNo\" />\r\n        <el-table-column label=\"申请部门\" width=\"150\" align=\"center\" prop=\"deptNo\" />\r\n        <el-table-column label=\"填报人\" width=\"120\" align=\"center\" prop=\"userName\" />\r\n        <el-table-column label=\"确认人\" width=\"120\" align=\"center\" prop=\"confirmName\" />\r\n        <el-table-column label=\"确认部门\" width=\"150\" align=\"center\" prop=\"companyCode\" />\r\n        <el-table-column label=\"供应商代码\" width=\"120\" align=\"center\" prop=\"suppId\" />\r\n        <el-table-column label=\"供应商名称\" min-width=\"230\" align=\"center\" prop=\"suppName\" />\r\n        <el-table-column label=\"供应类型\" width=\"80\" align=\"center\" prop=\"suppType\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-if=\"scope.row.suppType == 'M'\">货物</span>\r\n            <span v-if=\"scope.row.suppType == 'S'\">服务</span>\r\n            <span v-if=\"scope.row.suppType == 'P'\">工程</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"处罚类型\" width=\"80\" align=\"center\" prop=\"punishmentType\" :formatter=\"punishmentTypeFormat\" />\r\n        <el-table-column label=\"处罚事由\" min-width=\"200\" align=\"center\" prop=\"punishmentReason\" />\r\n        <el-table-column label=\"处罚依据\" min-width=\"300\" align=\"center\" prop=\"punishmentBasis\" />\r\n        <el-table-column label=\"处罚措施\" min-width=\"300\" align=\"center\" prop=\"punishmentMeasure\" />\r\n        <el-table-column label=\"事件发生时间\" width=\"100\" align=\"center\" prop=\"happenedTime\" :formatter=\"dateFormat\" />\r\n        <el-table-column label=\"处罚执行时间\" width=\"100\" align=\"center\" prop=\"punishmentTime\" :formatter=\"dateFormat\" />\r\n        <el-table-column\r\n          label=\"操作\"\r\n          align=\"center\"\r\n          class-name=\"small-padding fixed-width\"\r\n          fixed=\"right\"\r\n          width=\"180\"\r\n        >\r\n        <template slot-scope=\"scope\">\r\n          <!-- 修改按钮：只有草稿状态(1)可以修改，且有修改权限 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 1 && permissions.canEdit\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            >修改</el-button\r\n          >\r\n          <!-- 确认按钮：只有草稿状态(1)可以确认，且有确认权限 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 1 && permissions.canConfirm\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-check\"\r\n            @click=\"handleConfirm(scope.row)\"\r\n            >确认</el-button\r\n          >\r\n          <!-- 删除按钮：只有草稿状态(1)可以删除，且有删除权限 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 1 && permissions.canDelete\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n          <!-- 查看按钮：确认状态(2)只能查看，或者没有修改权限时显示查看 -->\r\n          <el-button\r\n            v-if=\"scope.row.stateId == 2 || (scope.row.stateId == 1 && !permissions.canEdit)\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n          >查看</el-button>\r\n        </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n\r\n    <pagination\r\n      v-show=\"total>0 && permissions.canQuery\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改供应商处罚记录对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"填报人\" prop=\"userName\">\r\n              <el-input\r\n                v-model=\"form.userName\"\r\n                placeholder=\"请输入填报人\"\r\n                readonly\r\n                class=\"readonly-input\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"确认部门\" prop=\"companyCode\">\r\n              <el-input\r\n                v-model=\"form.companyCode\"\r\n                placeholder=\"请输入确认部门\"\r\n                readonly\r\n                class=\"readonly-input\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"申请部门\" prop=\"deptNo\">\r\n              <el-select\r\n                v-model=\"form.deptNo\"\r\n                clearable\r\n                placeholder=\"请选择申请部门\"\r\n                style=\"width: 100%\"\r\n                :disabled=\"isViewMode\"\r\n              >\r\n                <el-option\r\n                  v-for=\"item in getDepNameList\"\r\n                  :key=\"item\"\r\n                  :label=\"item\"\r\n                  :value=\"item\"\r\n                >\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应商代码\" prop=\"suppId\">\r\n              <el-input\r\n                v-model=\"form.suppId\"\r\n                placeholder=\"请输入供应商代码\"\r\n                :readonly=\"isViewMode\"\r\n              >\r\n                <i v-if=\"!isViewMode\" slot=\"suffix\" class=\"el-icon-search search-icon\" @click=\"showSuppInfoDialog\"></i>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应商名称\" prop=\"suppName\">\r\n              <el-input v-model=\"form.suppName\" placeholder=\"请输入供应商名称\" :readonly=\"isViewMode\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"供应类型\" prop=\"suppType\">\r\n              <el-select v-model=\"form.suppType\" placeholder=\"请选择涉及货物、服务或工程\" style=\"width: 100%\" @change=\"handleMaterialOrServiceChange\" :disabled=\"isViewMode\">\r\n                <el-option\r\n                  v-for=\"dict in suppTypeOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"处罚类型\" prop=\"punishmentType\">\r\n              <el-select v-model=\"form.punishmentType\" placeholder=\"请选择处罚类型\" style=\"width: 100%\" :disabled=\"isViewMode\">\r\n                <el-option\r\n                  v-for=\"dict in punishmentTypeOptions\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n                             \r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"事件发生时间\" prop=\"happenedTime\">\r\n              <el-date-picker\r\n                v-model=\"form.happenedTime\"\r\n                type=\"date\"\r\n                placeholder=\"请选择事件发生时间\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                :disabled=\"isViewMode\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"处罚执行时间\" prop=\"punishmentTime\">\r\n              <el-date-picker\r\n                v-model=\"form.punishmentTime\"\r\n                type=\"date\"\r\n                placeholder=\"请选择处罚执行时间\"\r\n                format=\"yyyy-MM-dd\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                style=\"width: 100%\"\r\n                :disabled=\"isViewMode\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"处罚事由\" prop=\"punishmentReason\">\r\n              <el-input v-model=\"form.punishmentReason\" type=\"textarea\" placeholder=\"请输入处罚事由\" :rows=\"3\" :readonly=\"isViewMode\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"处罚依据\" prop=\"punishmentBasis\">\r\n              <div class=\"basis-input-wrapper\">\r\n                <el-input\r\n                  v-model=\"form.punishmentBasis\"\r\n                  type=\"textarea\"\r\n                  placeholder=\"请选择处罚依据\"\r\n                  :rows=\"3\"\r\n                  class=\"basis-textarea\"\r\n                  readonly\r\n                />\r\n                <div class=\"basis-buttons\" v-if=\"!isViewMode\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    @click=\"showPunishmentBasisDialog\"\r\n                    class=\"basis-btn\"\r\n                    size=\"small\"\r\n                    title=\"选择处罚依据\"\r\n                  >\r\n                    选择\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"处罚措施\" prop=\"punishmentMeasure\">\r\n              <div class=\"measure-input-wrapper\">\r\n                <!-- 标签显示区域 -->\r\n                <div class=\"measure-tags-container\" v-if=\"punishmentMeasureTags.length > 0\">\r\n                  <el-tag\r\n                    v-for=\"(tag, index) in punishmentMeasureTags\"\r\n                    :key=\"index\"\r\n                    :type=\"getTagType(tag.type)\"\r\n                    :closable=\"!isViewMode\"\r\n                    @close=\"removeMeasureTag(index)\"\r\n                    class=\"measure-tag\"\r\n                  >\r\n                    {{ tag.text }}\r\n                  </el-tag>\r\n                </div>\r\n\r\n                <!-- 隐藏的输入框用于存储完整文本 -->\r\n                <el-input\r\n                  v-model=\"form.punishmentMeasure\"\r\n                  type=\"hidden\"\r\n                />\r\n\r\n                <!-- 占位符显示区域 -->\r\n                <div\r\n                  v-if=\"punishmentMeasureTags.length === 0\"\r\n                  class=\"measure-placeholder\"\r\n                  @click=\"!isViewMode && showPunishmentMeasureDialog()\"\r\n                  :style=\"{ cursor: isViewMode ? 'default' : 'pointer' }\"\r\n                >\r\n                  {{ isViewMode ? '暂无处罚措施' : '请选择处罚措施（至少选一个）' }}\r\n                </div>\r\n\r\n                <div class=\"measure-buttons\" v-if=\"!isViewMode\">\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    @click=\"showPunishmentMeasureDialog\"\r\n                    class=\"measure-btn\"\r\n                    size=\"small\"\r\n                    title=\"选择处罚措施\"\r\n                  >\r\n                    选择\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button v-if=\"!isViewMode\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">{{ isViewMode ? '关 闭' : '取 消' }}</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 供应商信息查询弹窗 -->\r\n    <supp-info-dialog ref=\"suppInfoDialog\" @select=\"handleSuppSelect\" />\r\n\r\n    <!-- 物料信息查询弹窗 -->\r\n    <material-info-dialog ref=\"materialInfoDialog\" @select=\"handleMaterialSelect\" />\r\n\r\n    <!-- 服务查询弹窗 -->\r\n    <service-project-dialog ref=\"serviceDialog\" @select=\"handleServiceSelect\" />\r\n    <!-- 项目查询弹窗 -->\r\n    <project-dialog ref=\"projectDialog\" @select=\"handleProjectSelect\" />\r\n\r\n    <!-- 处罚措施选择弹窗 -->\r\n    <punishment-measure-dialog ref=\"punishmentMeasureDialog\" @select=\"handlePunishmentMeasureSelect\" />\r\n\r\n    <!-- 处罚依据选择弹窗 -->\r\n    <punishment-basis-dialog ref=\"punishmentBasisDialog\" @select=\"handlePunishmentBasisSelect\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listPunishment, getPunishment, delPunishment, addPunishment, updatePunishment, exportPunishment, confirmPunishment, getUserCompany, getUserGroup } from \"@/api/suppPunishment/punishment\";\r\nimport { getDepNameList } from \"@/api/purchase/purdchaseFactoryStock\";\r\nimport { addDateRange } from \"@/utils/ruoyi\";\r\nimport SuppInfoDialog from \"./suppInfo-module.vue\";\r\nimport MaterialInfoDialog from \"./materialInfo-module.vue\";\r\nimport ServiceProjectDialog from \"./service-module.vue\";\r\nimport ProjectDialog from \"./project-module.vue\";\r\nimport PunishmentMeasureDialog from \"./punishmentMeasure-module.vue\";\r\nimport PunishmentBasisDialog from \"./punishmentBasis-module.vue\";\r\n\r\n\r\nexport default {\r\n  name: \"Punishment\",\r\n  components: {\r\n    SuppInfoDialog,\r\n    MaterialInfoDialog,\r\n    ServiceProjectDialog,\r\n    ProjectDialog,\r\n    PunishmentMeasureDialog,\r\n    PunishmentBasisDialog\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 供应商处罚记录表格数据\r\n      punishmentList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否为查看模式\r\n      isViewMode: false,\r\n      // 当前选择类型：form-表单选择，query-查询条件选择\r\n      currentSelectType: 'form',\r\n      // 用户分组权限\r\n      userGroup: '',\r\n      // 权限控制\r\n      permissions: {\r\n        canAdd: true,      // 新增权限\r\n        canEdit: true,     // 修改权限\r\n        canDelete: true,   // 删除权限\r\n        canConfirm: true,  // 确认权限\r\n        canExport: true,   // 导出权限\r\n        canQuery: true     // 查询权限\r\n      },\r\n      // 处罚类型数据字典\r\n      punishmentTypeOptions: [],\r\n      // 状态数据字典\r\n      statusOptions: [],\r\n      // 货物或服务选项\r\n      suppTypeOptions: [],\r\n      // 服务部门列表\r\n      getDepNameList: [],\r\n      // 事件发生时间范围\r\n      happenedTimeRange: [],\r\n      // 处罚执行时间范围\r\n      punishmentTimeRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        recCreator: null,\r\n        recCreateTime: null,\r\n        recRevisor: null,\r\n        recReviseTime: null,\r\n        userName: null,\r\n        serialNo: null,\r\n        companyCode: null,\r\n        deptNo: null,\r\n        suppId: null,\r\n        suppName: null,\r\n        itemNo: null,\r\n        itemName: null,\r\n        suppType: null,\r\n        punishmentType: null,\r\n        stateId: null,\r\n        punishmentReason: null,\r\n        punishmentBasis: null,\r\n        punishmentMeasure: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 处罚措施标签数组\r\n      punishmentMeasureTags: [],\r\n      // 表单校验\r\n      rules: {\r\n        suppId: [\r\n          { required: true, trigger: \"blur\", message: \"请输入供应商代码\" }\r\n        ],\r\n        suppName: [\r\n          { required: true, trigger: \"blur\", message: \"请输入供应商名称\" }\r\n        ],\r\n        suppType: [\r\n          { required: true, trigger: \"change\", message: \"请选择货物或服务\" }\r\n        ],\r\n        punishmentType: [\r\n          { required: true, trigger: \"change\", message: \"请选择处罚类型\" }\r\n        ],\r\n        deptNo: [\r\n          { required: true, trigger: \"change\", message: \"请选择申请部门\" }\r\n        ],\r\n        itemNo: [\r\n          {\r\n            required: false,\r\n            trigger: \"blur\",\r\n            validator: (rule, value, callback) => {\r\n              // 只有货物类型才必填\r\n              if (this.form.suppType === 'M') {\r\n                if (!value || value.trim() === '') {\r\n                  callback(new Error('请输入物料小类编码'));\r\n                } else {\r\n                  callback();\r\n                }\r\n              } else {\r\n                // 服务和工程类型不校验必填\r\n                callback();\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        itemName: [\r\n          {\r\n            required: false,\r\n            trigger: \"blur\",\r\n            validator: (rule, value, callback) => {\r\n              // 只有货物类型才必填\r\n              if (this.form.suppType === 'M') {\r\n                if (!value || value.trim() === '') {\r\n                  callback(new Error('请输入物料小类名称'));\r\n                } else {\r\n                  callback();\r\n                }\r\n              } else {\r\n                // 服务和工程类型不校验必填\r\n                callback();\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        happenedTime: [\r\n          { required: true, trigger: \"change\", message: \"请选择事件发生时间\" }\r\n        ],\r\n        punishmentTime: [\r\n          { required: true, trigger: \"change\", message: \"请选择处罚执行时间\" }\r\n        ],\r\n        punishmentReason: [\r\n          { required: true, trigger: \"blur\", message: \"请输入处罚事由\" }\r\n        ],\r\n        punishmentBasis: [\r\n          { required: true, trigger: \"blur\", message: \"请选择处罚依据\" }\r\n        ],\r\n        punishmentMeasure: [\r\n          { required: true, trigger: \"blur\", message: \"请选择处罚措施\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictData();\r\n    this.getDeptList();\r\n    this.getUserGroupPermissions();\r\n    // 不在这里调用getList()，而是在设置默认值后调用\r\n  },\r\n  methods: {\r\n    // 添加日期范围\r\n    addDateRange,\r\n    // 获取字典数据\r\n    getDictData() {\r\n      // 获取处罚类型\r\n      this.getDicts(\"supp_punishment_type\").then((response) => {\r\n        this.punishmentTypeOptions = response.data;\r\n      });\r\n      // 获取状态 - 如果字典不存在，使用手动定义的状态\r\n      this.getDicts(\"supp_punishment_status\").then((response) => {\r\n        this.statusOptions = response.data;\r\n      });\r\n      // 获取供应类型 - 如果字典不存在，使用手动定义的状态\r\n      this.getDicts(\"supp_type\").then((response) => {\r\n        this.suppTypeOptions = response.data;\r\n      });\r\n    },\r\n    /** 查询供应商处罚记录列表 */\r\n    getList() {\r\n      // 检查查询权限\r\n      if (!this.permissions.canQuery) {\r\n        this.loading = false;\r\n        this.showNoPermissionMessage();\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n\r\n      console.log('事件发生时间范围:', this.happenedTimeRange);\r\n      console.log('处罚执行时间范围:', this.punishmentTimeRange);\r\n\r\n      // 手动构建时间范围参数\r\n      let params = { ...this.queryParams };\r\n      if (!params.params) {\r\n        params.params = {};\r\n      }\r\n\r\n      // 处理事件发生时间范围\r\n      if (this.happenedTimeRange && this.happenedTimeRange.length === 2) {\r\n        params.params[\"happenedTimeBeginTime\"] = this.happenedTimeRange[0];\r\n        params.params[\"happenedTimeEndTime\"] = this.happenedTimeRange[1];\r\n      }\r\n\r\n      // 处理处罚执行时间范围\r\n      if (this.punishmentTimeRange && this.punishmentTimeRange.length === 2) {\r\n        params.params[\"punishmentTimeBeginTime\"] = this.punishmentTimeRange[0];\r\n        params.params[\"punishmentTimeEndTime\"] = this.punishmentTimeRange[1];\r\n      }\r\n\r\n      console.log('最终参数:', params);\r\n\r\n      listPunishment(params).then(response => {\r\n        this.punishmentList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 查询服务部门列表 */\r\n    getDeptList() {\r\n      getDepNameList().then(response => {\r\n        this.getDepNameList = response.data;\r\n        // 直接执行查询，不设置默认申请部门\r\n        this.getList();\r\n      });\r\n    },\r\n\r\n    /** 获取用户分组权限 */\r\n    getUserGroupPermissions() {\r\n      getUserGroup().then(response => {\r\n        if (response.code === 200 && response.data) {\r\n          this.userGroup = response.data.userGroup;\r\n          this.setPermissions();\r\n        } else {\r\n          // 默认设置为查阅组权限（最严格）\r\n          this.userGroup = 'query';\r\n          this.setPermissions();\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取用户分组失败:', error);\r\n        // 默认设置为查阅组权限（最严格）\r\n        this.userGroup = 'query';\r\n        this.setPermissions();\r\n      });\r\n    },\r\n\r\n    /** 根据用户分组设置权限 */\r\n    setPermissions() {\r\n      // 检查用户是否在允许的四个分组中\r\n      const allowedGroups = ['input', 'confirm', 'query', 'manage'];\r\n      if (!allowedGroups.includes(this.userGroup)) {\r\n        // 不在允许的分组中，无任何权限\r\n        this.permissions = {\r\n          canAdd: false,\r\n          canEdit: false,\r\n          canDelete: false,\r\n          canConfirm: false,\r\n          canExport: false,\r\n          canQuery: false  // 新增查询权限控制\r\n        };\r\n        this.showNoPermissionMessage();\r\n        return;\r\n      }\r\n\r\n      switch (this.userGroup) {\r\n        case 'input':\r\n          // 填报组：隐藏确认、导出按钮\r\n          this.permissions = {\r\n            canAdd: true,\r\n            canEdit: true,\r\n            canDelete: true,\r\n            canConfirm: false,\r\n            canExport: false,\r\n            canQuery: true\r\n          };\r\n          break;\r\n        case 'confirm':\r\n          // 确认组：隐藏导出按钮\r\n          this.permissions = {\r\n            canAdd: true,\r\n            canEdit: true,\r\n            canDelete: true,\r\n            canConfirm: true,\r\n            canExport: false,\r\n            canQuery: true\r\n          };\r\n          break;\r\n        case 'query':\r\n          // 查阅组：隐藏新增、修改、确认、删除按钮\r\n          this.permissions = {\r\n            canAdd: false,\r\n            canEdit: false,\r\n            canDelete: false,\r\n            canConfirm: false,\r\n            canExport: true,\r\n            canQuery: true\r\n          };\r\n          // 设置查阅组默认状态为\"确认\"\r\n          this.setQueryGroupDefaults();\r\n          break;\r\n        case 'manage':\r\n          // 管理组：具备所有功能\r\n          this.permissions = {\r\n            canAdd: true,\r\n            canEdit: true,\r\n            canDelete: true,\r\n            canConfirm: true,\r\n            canExport: true,\r\n            canQuery: true\r\n          };\r\n          break;\r\n        default:\r\n          // 不在允许的分组中，无任何权限\r\n          this.permissions = {\r\n            canAdd: false,\r\n            canEdit: false,\r\n            canDelete: false,\r\n            canConfirm: false,\r\n            canExport: false,\r\n            canQuery: false\r\n          };\r\n          this.showNoPermissionMessage();\r\n      }\r\n    },\r\n\r\n    /** 设置查阅组默认值 */\r\n    setQueryGroupDefaults() {\r\n      // 设置状态默认为\"确认\"（值为2）\r\n      this.queryParams.stateId = '2';\r\n    },\r\n\r\n    /** 显示无权限消息 */\r\n    showNoPermissionMessage() {\r\n      // this.$message.error('您没有访问此页面的权限，请联系管理员');\r\n      // 清空数据列表\r\n      this.punishmentList = [];\r\n      this.total = 0;\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        recCreator: null,\r\n        recCreateTime: null,\r\n        recRevisor: null,\r\n        recReviseTime: null,\r\n        userName: null,\r\n        serialNo: null,\r\n        companyCode: null,\r\n        deptNo: null,\r\n        suppId: null,\r\n        suppName: null,\r\n        itemNo: null,\r\n        itemName: null,\r\n        punishmentType: null,\r\n        suppType: null,\r\n        happenedTime: null,\r\n        punishmentTime: null,\r\n        punishmentReason: null,\r\n        punishmentBasis: null,\r\n        punishmentMeasure: null\r\n      };\r\n      // 重置处罚措施标签\r\n      this.punishmentMeasureTags = [];\r\n      this.resetForm(\"form\");\r\n    },\r\n    // 处罚类型字典翻译\r\n    punishmentTypeFormat(row, column) {\r\n      return this.selectDictLabel(this.punishmentTypeOptions, row.punishmentType);\r\n    },\r\n    // 状态字典翻译\r\n    stateFormat(row, column) {\r\n      return this.selectDictLabel(this.statusOptions, row.stateId);\r\n    },\r\n    // 日期格式化：将20250803转换为2025-08-03\r\n    dateFormat(row, column, cellValue) {\r\n      if (!cellValue) return '';\r\n      // 如果已经是正确格式，直接返回\r\n      if (cellValue.includes('-')) return cellValue;\r\n      // 将20250803格式转换为2025-08-03\r\n      if (cellValue.length === 8) {\r\n        const year = cellValue.substring(0, 4);\r\n        const month = cellValue.substring(4, 6);\r\n        const day = cellValue.substring(6, 8);\r\n        return `${year}-${month}-${day}`;\r\n      }\r\n      return cellValue;\r\n    },\r\n    // 日期格式转换：将20250803转换为2025/08/03（用于表单回显）\r\n    convertDateFormat(dateValue) {\r\n      if (!dateValue) return '';\r\n      // 如果已经是正确格式，直接返回\r\n      if (dateValue.includes('/') || dateValue.includes('-')) return dateValue;\r\n      // 将20250803格式转换为2025/08/03\r\n      if (dateValue.length === 8) {\r\n        const year = dateValue.substring(0, 4);\r\n        const month = dateValue.substring(4, 6);\r\n        const day = dateValue.substring(6, 8);\r\n        return `${year}/${month}/${day}`;\r\n      }\r\n      return dateValue;\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      // 检查查询权限\r\n      if (!this.permissions.canQuery) {\r\n        this.showNoPermissionMessage();\r\n        return;\r\n      }\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      // 检查查询权限\r\n      if (!this.permissions.canQuery) {\r\n        this.showNoPermissionMessage();\r\n        return;\r\n      }\r\n\r\n      this.happenedTimeRange = [];\r\n      this.punishmentTimeRange = [];\r\n      this.resetForm(\"queryForm\");\r\n\r\n      // 如果是查阅组，重置后需要重新设置默认状态\r\n      if (this.userGroup === 'query') {\r\n        this.setQueryGroupDefaults();\r\n      }\r\n\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.isViewMode = false;\r\n      // 自动填入当前登录用户\r\n      if (this.$store.getters.name) {\r\n        // 立即获取登录人信息\r\n        this.getUserCompanyInfo(this.$store.getters.name);\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加供应商处罚记录\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      let id;\r\n\r\n      if (row && row.id) {\r\n        // 单行修改：从表格行操作按钮调用\r\n        if (row.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能修改\");\r\n          return;\r\n        }\r\n        id = row.id;\r\n      } else {\r\n        // 批量修改：从顶部按钮调用\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.msgError(\"请选择要修改的记录\");\r\n          return;\r\n        }\r\n        if (this.ids.length > 1) {\r\n          this.msgError(\"修改操作只能选择一条记录\");\r\n          return;\r\n        }\r\n\r\n        // 根据选中的ID查找对应的记录\r\n        const selectedRow = this.punishmentList.find(item => item.id === this.ids[0]);\r\n        if (!selectedRow) {\r\n          this.msgError(\"无法找到选中的记录\");\r\n          return;\r\n        }\r\n        if (selectedRow.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能修改\");\r\n          return;\r\n        }\r\n        id = selectedRow.id;\r\n      }\r\n      console.log('修改操作 - id:', id);\r\n      console.log('修改操作 - row:', row);\r\n\r\n      if (!id) {\r\n        this.msgError(\"无法获取记录标识，请重新选择\");\r\n        return;\r\n      }\r\n\r\n      getPunishment(id).then(response => {\r\n        this.form = response.data;\r\n        // 转换日期格式：将20250803转换为2025/08/03\r\n        this.form.happenedTime = this.convertDateFormat(this.form.happenedTime);\r\n        this.form.punishmentTime = this.convertDateFormat(this.form.punishmentTime);\r\n        // 解析处罚措施为标签\r\n        this.parseMeasureTextToTags(this.form.punishmentMeasure);\r\n        this.isViewMode = false;\r\n        this.open = true;\r\n        this.title = \"修改供应商处罚记录\";\r\n      }).catch(error => {\r\n        console.error('获取详情失败:', error);\r\n        this.msgError(\"获取记录详情失败\");\r\n      });\r\n    },\r\n\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      console.log('查看操作 - id:', id);\r\n\r\n      if (!id) {\r\n        this.msgError(\"无法获取记录标识\");\r\n        return;\r\n      }\r\n\r\n      getPunishment(id).then(response => {\r\n        this.form = response.data;\r\n        // 转换日期格式：将20250803转换为2025/08/03\r\n        this.form.happenedTime = this.convertDateFormat(this.form.happenedTime);\r\n        this.form.punishmentTime = this.convertDateFormat(this.form.punishmentTime);\r\n        // 解析处罚措施为标签\r\n        this.parseMeasureTextToTags(this.form.punishmentMeasure);\r\n        this.isViewMode = true;\r\n        this.open = true;\r\n        this.title = \"查看供应商处罚记录\";\r\n      }).catch(error => {\r\n        console.error('获取详情失败:', error);\r\n        this.msgError(\"获取记录详情失败\");\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updatePunishment(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addPunishment(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 确认按钮操作 */\r\n    handleConfirm(row) {\r\n      let ids;\r\n      let recordsToConfirm = [];\r\n\r\n      if (row && row.id) {\r\n        // 单行确认：从表格行操作按钮调用\r\n        if (row.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能确认\");\r\n          return;\r\n        }\r\n        ids = row.id;\r\n        recordsToConfirm = [row];\r\n      } else {\r\n        // 批量确认：从顶部按钮调用\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.msgError(\"请选择要确认的记录\");\r\n          return;\r\n        }\r\n\r\n        // 检查所有选中记录的状态，只有草稿状态才能确认\r\n        const selectedRows = this.punishmentList.filter(item => this.ids.includes(item.id));\r\n        const hasNonDraftRecord = selectedRows.some(item => item.stateId != 1);\r\n        if (hasNonDraftRecord) {\r\n          this.msgError(\"只有草稿状态的记录才能确认\");\r\n          return;\r\n        }\r\n\r\n        ids = this.ids;\r\n        recordsToConfirm = selectedRows;\r\n      }\r\n\r\n      if (!ids || (Array.isArray(ids) && ids.length === 0)) {\r\n        this.msgError(\"请选择要确认的记录\");\r\n        return;\r\n      }\r\n\r\n      // 验证必填字段\r\n      const validationResult = this.validateRequiredFields(recordsToConfirm);\r\n      if (!validationResult.isValid) {\r\n        this.msgError(validationResult.message);\r\n        return;\r\n      }\r\n\r\n      const confirmIds = Array.isArray(ids) ? ids.join(',') : ids;\r\n      this.$confirm('是否确认选中的处罚记录?', \"提示\", {\r\n          cancelButtonText: \"取消\",\r\n          confirmButtonText: \"确定\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          return confirmPunishment(confirmIds);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"确认成功\");\r\n        })\r\n    },\r\n\r\n    /** 验证必填字段 */\r\n    validateRequiredFields(records) {\r\n      const requiredFields = [\r\n        { field: 'deptNo', name: '申请部门' },\r\n        { field: 'userName', name: '填报人' },\r\n        { field: 'suppId', name: '供应商代码' },\r\n        { field: 'suppName', name: '供应商名称' },\r\n        { field: 'punishmentType', name: '处罚类型' },\r\n        { field: 'punishmentReason', name: '处罚事由' },\r\n        { field: 'punishmentBasis', name: '处罚依据' },\r\n        { field: 'punishmentMeasure', name: '处罚措施' },\r\n        { field: 'happenedTime', name: '事件发生时间' },\r\n        { field: 'punishmentTime', name: '处罚执行时间' }\r\n      ];\r\n\r\n      for (let i = 0; i < records.length; i++) {\r\n        const record = records[i];\r\n        const recordIndex = i + 1;\r\n\r\n        for (const requiredField of requiredFields) {\r\n          const fieldValue = record[requiredField.field];\r\n\r\n          // 检查字段是否为空\r\n          if (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === '')) {\r\n            return {\r\n              isValid: false,\r\n              message: `【${requiredField.name}】不能为空，请完善信息后再确认`\r\n            };\r\n          }\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: true,\r\n        message: ''\r\n      };\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let ids;\r\n\r\n      if (row && row.id) {\r\n        // 单行删除：从表格行操作按钮调用\r\n        if (row.stateId != 1) {\r\n          this.msgError(\"只有草稿状态的记录才能删除\");\r\n          return;\r\n        }\r\n        ids = row.id;\r\n      } else {\r\n        // 批量删除：从顶部按钮调用\r\n        if (!this.ids || this.ids.length === 0) {\r\n          this.msgError(\"请选择要删除的记录\");\r\n          return;\r\n        }\r\n\r\n        // 检查所有选中记录的状态，只有草稿状态才能删除\r\n        const selectedRows = this.punishmentList.filter(item => this.ids.includes(item.id));\r\n        const hasNonDraftRecord = selectedRows.some(item => item.stateId != 1);\r\n        if (hasNonDraftRecord) {\r\n          this.msgError(\"只有草稿状态的记录才能删除\");\r\n          return;\r\n        }\r\n\r\n        ids = this.ids;\r\n      }\r\n      if (!ids) {\r\n        this.msgError(\"无法获取记录标识，请重新选择\");\r\n        return;\r\n      }\r\n\r\n      this.$confirm('是否确认删除?', \"警告\", {\r\n          cancelButtonText: \"取消\",\r\n          confirmButtonText: \"确定\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delPunishment(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n    },\r\n    /** 显示供应商信息查询弹窗（表单用） */\r\n    showSuppInfoDialog() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.suppInfoDialog.show();\r\n    },\r\n    /** 显示供应商信息查询弹窗（查询条件用） */\r\n    showSuppInfoDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.suppInfoDialog.show();\r\n    },\r\n    /** 处理供应商选择 */\r\n    handleSuppSelect(suppInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的供应商选择\r\n        this.form.suppId = suppInfo.suppId;\r\n        this.form.suppName = suppInfo.suppName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的供应商选择\r\n        this.queryParams.suppId = suppInfo.suppId;\r\n        this.queryParams.suppName = suppInfo.suppName;\r\n      }\r\n    },\r\n    /** 显示物料信息查询弹窗（表单用） */\r\n    showMaterialInfoDialogForForm() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.materialInfoDialog.show();\r\n    },\r\n    /** 显示物料信息查询弹窗（查询条件用） */\r\n    showMaterialInfoDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.materialInfoDialog.show();\r\n    },\r\n    /** 处理物料选择 */\r\n    handleMaterialSelect(materialInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的物料选择\r\n        this.form.itemNo = materialInfo.itemId;\r\n        this.form.itemName = materialInfo.itemName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的物料选择\r\n        this.queryParams.itemNo = materialInfo.itemId;\r\n        this.queryParams.itemName = materialInfo.itemName;\r\n      }\r\n    },\r\n    /** 显示服务查询弹窗（表单用） */\r\n    showServiceDialogForForm() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.serviceDialog.show();\r\n    },\r\n    /** 显示服务项目查询弹窗（查询条件用） */\r\n    showServiceDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.serviceDialog.show();\r\n    },\r\n    /** 处理服务选择 */\r\n    handleServiceSelect(serviceInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的服务选择\r\n        this.form.itemNo = serviceInfo.serviceNo;\r\n        this.form.itemName = serviceInfo.serviceName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的服务选择\r\n        this.queryParams.itemNo = serviceInfo.serviceNo;\r\n        this.queryParams.itemName = serviceInfo.serviceName;\r\n      }\r\n    },\r\n    /** 显示项目查询弹窗（表单用） */\r\n    showProjectDialogForForm() {\r\n      this.currentSelectType = 'form';\r\n      this.$refs.projectDialog.show();\r\n    },\r\n    /** 显示项目查询弹窗（查询条件用） */\r\n    showProjectDialogForQuery() {\r\n      this.currentSelectType = 'query';\r\n      this.$refs.projectDialog.show();\r\n    },\r\n    /** 处理项目选择 */\r\n    handleProjectSelect(projectInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的项目选择\r\n        this.form.itemNo = projectInfo.projectNo;\r\n        this.form.itemName = projectInfo.projectName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的项目选择\r\n        this.queryParams.itemNo = projectInfo.projectNo;\r\n        this.queryParams.itemName = projectInfo.projectName;\r\n      }\r\n    },\r\n    /** 处理服务项目选择 */\r\n    handleServiceProjectSelect(serviceInfo) {\r\n      if (this.currentSelectType === 'form') {\r\n        // 表单中的服务项目选择\r\n        this.form.itemNo = serviceInfo.serviceNo;\r\n        this.form.itemName = serviceInfo.serviceName;\r\n      } else if (this.currentSelectType === 'query') {\r\n        // 查询条件中的服务项目选择\r\n        this.queryParams.itemNo = serviceInfo.serviceNo;\r\n        this.queryParams.itemName = serviceInfo.serviceName;\r\n      }\r\n    },\r\n    /** 处理货物或服务选择变化 */\r\n    handleMaterialOrServiceChange(value) {\r\n      // 清空相关字段\r\n      if (value === 'M' || value === 'S' || value === 'P') {\r\n        // 选择任何类型时，都清空itemNo和itemName，让用户重新选择\r\n        this.form.itemNo = null;\r\n        this.form.itemName = null;\r\n      } else {\r\n        // 未选择时，清空所有相关字段\r\n        this.form.itemNo = null;\r\n        this.form.itemName = null;\r\n      }\r\n\r\n      // 切换类型后，清除之前的验证错误信息\r\n      this.$nextTick(() => {\r\n        if (this.$refs.form) {\r\n          this.$refs.form.clearValidate(['itemNo', 'itemName']);\r\n        }\r\n      });\r\n    },\r\n    /** 处理查询条件中货物或服务选择变化 */\r\n    handleQueryMaterialOrServiceChange(value) {\r\n      // 清空查询条件中的相关字段\r\n      if (value === 'M' || value === 'S' || value === 'P') {\r\n        // 选择任何类型时，都清空itemNo和itemName，让用户重新选择\r\n        this.queryParams.itemNo = null;\r\n        this.queryParams.itemName = null;\r\n      } else {\r\n        // 未选择时，清空所有相关字段\r\n        this.queryParams.itemNo = null;\r\n        this.queryParams.itemName = null;\r\n      }\r\n    },\r\n    /** 显示处罚措施选择弹窗 */\r\n    showPunishmentMeasureDialog() {\r\n      this.$refs.punishmentMeasureDialog.show(this.form.punishmentMeasure);\r\n    },\r\n    /** 处理处罚措施选择 */\r\n    handlePunishmentMeasureSelect(measureText) {\r\n      this.form.punishmentMeasure = measureText;\r\n      this.parseMeasureTextToTags(measureText);\r\n    },\r\n\r\n    /** 显示处罚依据选择弹窗 */\r\n    showPunishmentBasisDialog() {\r\n      console.log('显示处罚依据弹窗，当前值：', this.form.punishmentBasis);\r\n      this.$refs.punishmentBasisDialog.show(this.form.punishmentBasis);\r\n    },\r\n\r\n    /** 处理处罚依据选择 */\r\n    handlePunishmentBasisSelect(basisText) {\r\n      this.form.punishmentBasis = basisText;\r\n    },\r\n\r\n    /** 解析处罚措施文本为标签 */\r\n    parseMeasureTextToTags(measureText) {\r\n      this.punishmentMeasureTags = [];\r\n      if (!measureText) return;\r\n\r\n      const measures = measureText.split('；').filter(item => item.trim());\r\n      measures.forEach(measure => {\r\n        const tag = this.createMeasureTag(measure.trim());\r\n        if (tag) {\r\n          this.punishmentMeasureTags.push(tag);\r\n        }\r\n      });\r\n    },\r\n\r\n    /** 创建处罚措施标签 */\r\n    createMeasureTag(measureText) {\r\n      if (measureText.includes('处罚') && measureText.includes('元')) {\r\n        return { text: measureText, type: 'penalty' };\r\n      } else if (measureText === '降级') {\r\n        return { text: measureText, type: 'downgrade' };\r\n      } else if (measureText === '淘汰（禁用）') {\r\n        return { text: measureText, type: 'eliminate' };\r\n      } else if (measureText.includes('暂缓') && measureText.includes('月')) {\r\n        return { text: measureText, type: 'suspend' };\r\n      }\r\n      return null;\r\n    },\r\n\r\n    /** 获取标签类型对应的颜色 */\r\n    getTagType(type) {\r\n      const typeMap = {\r\n        'penalty': 'danger',\r\n        'downgrade': 'danger',\r\n        'eliminate': 'danger',\r\n        'suspend': 'danger'\r\n      };\r\n      return typeMap[type] || '';\r\n    },\r\n\r\n    /** 删除处罚措施标签 */\r\n    removeMeasureTag(index) {\r\n      this.punishmentMeasureTags.splice(index, 1);\r\n      this.updateMeasureText();\r\n    },\r\n\r\n    /** 更新处罚措施文本 */\r\n    updateMeasureText() {\r\n      const measureTexts = this.punishmentMeasureTags.map(tag => tag.text);\r\n      this.form.punishmentMeasure = measureTexts.join('；');\r\n    },\r\n\r\n\r\n    /** 根据填报人工号获取名字、单位信息 */\r\n    getUserCompanyInfo(userName) {\r\n      getUserCompany(userName).then(response => {\r\n        if (response.code === 200 && response.data) {\r\n          // 从SysUser对象中取rsDeptName作为确认部门和申请部门\r\n          const deptName = response.data.rsDeptName || '';\r\n          this.form.companyCode = deptName;  // 确认部门\r\n          this.form.deptNo = deptName;       // 申请部门，默认与确认部门一致\r\n          this.form.userName = response.data.nickName || '';\r\n\r\n        }\r\n      }).catch(error => {\r\n        console.warn('获取单位信息失败:', error);\r\n        // 不显示错误提示，避免影响用户体验\r\n      });\r\n    },\r\n\r\n\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.$confirm('是否确认导出所有供应商处罚记录数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          // 手动构建时间范围参数\r\n          let params = { ...this.queryParams };\r\n          if (!params.params) {\r\n            params.params = {};\r\n          }\r\n\r\n          // 处理事件发生时间范围\r\n          if (this.happenedTimeRange && this.happenedTimeRange.length === 2) {\r\n            params.params[\"happenedTimeBeginTime\"] = this.happenedTimeRange[0];\r\n            params.params[\"happenedTimeEndTime\"] = this.happenedTimeRange[1];\r\n          }\r\n\r\n          // 处理处罚执行时间范围\r\n          if (this.punishmentTimeRange && this.punishmentTimeRange.length === 2) {\r\n            params.params[\"punishmentTimeBeginTime\"] = this.punishmentTimeRange[0];\r\n            params.params[\"punishmentTimeEndTime\"] = this.punishmentTimeRange[1];\r\n          }\r\n\r\n          return exportPunishment(params);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n        })\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 日期范围选择器宽度与其他输入框一致 */\r\n.el-date-editor.el-range-editor {\r\n  width: 205px !important;\r\n  font-size: 12px !important;\r\n}\r\n\r\n/* 调整日期范围选择器内部样式 */\r\n.el-date-editor.el-range-editor .el-range-input {\r\n  width: 32% !important;\r\n  font-size: 12px !important;\r\n  text-align: center;\r\n}\r\n\r\n.el-date-editor.el-range-editor .el-range-separator {\r\n  width: 20% !important;\r\n  text-align: center;\r\n  font-size: 12px !important;\r\n  color: #C0C4CC;\r\n}\r\n\r\n/* 确保日期范围选择器的高度与其他输入框一致 */\r\n.el-date-editor.el-range-editor.el-input__inner {\r\n  height: 28px !important;\r\n  line-height: 28px !important;\r\n}\r\n\r\n/* 缩短右侧留白 */\r\n.app-container {\r\n  padding-right: 5px !important;\r\n}\r\n\r\n/* 调整表单容器宽度 */\r\n.el-form--inline {\r\n  max-width: calc(100% - 10px) !important;\r\n}\r\n\r\n/* 调整输入框上下间距相等 */\r\n.el-form--inline .el-form-item {\r\n  margin-bottom: 18px !important;\r\n  margin-top: 0 !important;\r\n}\r\n\r\n/* 确保第一行没有额外的上边距 */\r\n.el-form--inline .el-form-item:first-child {\r\n  margin-top: 0 !important;\r\n}\r\n\r\n/* 缩小输入框标题字体 */\r\n.el-form--inline .el-form-item__label {\r\n  font-size: 10px !important;\r\n}\r\n\r\n/* 更强的选择器确保字体样式生效 */\r\n.app-container .el-form--inline .el-form-item .el-form-item__label {\r\n  font-size: 10px !important;\r\n  font-weight: normal !important;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n.el-dialog__header {\r\n  text-align: center !important;\r\n}\r\n\r\n.el-dialog__header .el-dialog__title {\r\n  text-align: center !important;\r\n  width: 100% !important;\r\n  display: block !important;\r\n}\r\n\r\n/* 更强的选择器确保弹窗标题居中 */\r\n.el-dialog .el-dialog__header .el-dialog__title {\r\n  text-align: center !important;\r\n  width: 100% !important;\r\n  display: block !important;\r\n  margin: 0 auto !important;\r\n}\r\n\r\n/* 使用深度选择器确保样式穿透 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center !important;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center !important;\r\n  width: 100% !important;\r\n  display: block !important;\r\n}\r\n\r\n/* 搜索图标样式 */\r\n.search-icon {\r\n  cursor: pointer;\r\n  color: #909399;\r\n  padding: 0 8px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.search-icon:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 操作列固定宽度 */\r\n.el-table .fixed-width {\r\n  width: 200px;\r\n  min-width: 200px;\r\n}\r\n\r\n/* 小间距 */\r\n.el-table .small-padding .cell {\r\n  padding-left: 5px;\r\n  padding-right: 5px;\r\n}\r\n\r\n/* 处罚措施输入框样式 */\r\n.measure-input-wrapper {\r\n  position: relative;\r\n  min-height: 78px;\r\n  height: 78px;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  padding: 8px 12px;\r\n  background-color: #fff;\r\n}\r\n\r\n.measure-tags-container {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n  min-height: 62px;\r\n  max-height: 62px;\r\n  overflow-y: auto;\r\n  align-items: flex-start;\r\n  align-content: flex-start;\r\n}\r\n\r\n.measure-tag {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  height: 28px;\r\n  line-height: 26px;\r\n  border-radius: 14px;\r\n  padding: 0 12px;\r\n  cursor: default;\r\n  font-weight: 500;\r\n}\r\n\r\n.measure-tag .el-icon-close {\r\n  margin-left: 6px;\r\n  font-size: 12px;\r\n}\r\n\r\n.measure-placeholder {\r\n  color: #c0c4cc;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  position: absolute;\r\n  top: 8px;\r\n  left: 12px;\r\n  cursor: pointer;\r\n  user-select: none;\r\n}\r\n\r\n.measure-placeholder:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n.measure-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.measure-buttons {\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 8px;\r\n  z-index: 10;\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.measure-btn {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  min-width: 50px;\r\n  height: 28px;\r\n  line-height: 1;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.measure-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 选择按钮样式 */\r\n.measure-btn.el-button--primary {\r\n  background: rgba(64, 158, 255, 0.9);\r\n  border-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n.measure-btn.el-button--primary:hover {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n}\r\n\r\n/* 清空按钮样式 */\r\n.measure-btn.el-button--danger {\r\n  background: rgba(245, 108, 108, 0.9);\r\n  border-color: #F56C6C;\r\n  color: white;\r\n}\r\n\r\n.measure-btn.el-button--danger:hover {\r\n  background: #F56C6C;\r\n  border-color: #F56C6C;\r\n}\r\n\r\n/* 只读处罚措施输入框样式 */\r\n.measure-textarea.el-textarea.is-disabled .el-textarea__inner,\r\n.measure-textarea .el-textarea__inner[readonly] {\r\n  background-color: #f5f7fa;\r\n  border-color: #e4e7ed;\r\n  color: #606266;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* 查询表单样式优化 - 每行4个输入框 */\r\n.el-form--inline {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  align-items: flex-start;\r\n}\r\n\r\n.el-form--inline .el-form-item {\r\n  width: calc(25% - 15px);\r\n  margin-right: 20px;\r\n  margin-bottom: 15px;\r\n  flex: 0 0 auto;\r\n}\r\n\r\n/* 每行第4个元素不需要右边距 */\r\n.el-form--inline .el-form-item:nth-child(4n) {\r\n  margin-right: 0;\r\n}\r\n\r\n/* 搜索按钮区域单独处理 */\r\n.el-form--inline .el-form-item:last-child {\r\n  width: auto;\r\n  margin-left: auto;\r\n  margin-right: 0;\r\n}\r\n\r\n/* 统一输入框宽度 */\r\n.el-form--inline .el-form-item .el-input {\r\n  width: 100%;\r\n}\r\n\r\n/* 统一选择框宽度 */\r\n.el-form--inline .el-form-item .el-select {\r\n  width: 100%;\r\n}\r\n\r\n/* 统一日期选择器宽度 */\r\n.el-form--inline .el-form-item .el-date-editor {\r\n  width: 100%;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 1400px) {\r\n  .el-form--inline .el-form-item {\r\n    width: calc(33.33% - 13px);\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(4n) {\r\n    margin-right: 20px;\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(3n) {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 1000px) {\r\n  .el-form--inline .el-form-item {\r\n    width: calc(50% - 10px);\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(3n) {\r\n    margin-right: 20px;\r\n  }\r\n  .el-form--inline .el-form-item:nth-child(2n) {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n/* 表格容器样式 */\r\n.table-container {\r\n  width: 100%;\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n}\r\n\r\n/* 处罚依据输入框样式 */\r\n.basis-input-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.basis-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.basis-buttons {\r\n  position: absolute;\r\n  top: 5px;\r\n  right: 5px;\r\n  z-index: 10;\r\n}\r\n\r\n.basis-btn {\r\n  background: rgba(255, 255, 255, 0.95);\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  min-width: 50px;\r\n  height: 28px;\r\n  line-height: 1;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.basis-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* 选择按钮样式 */\r\n.basis-btn.el-button--primary {\r\n  background: rgba(64, 158, 255, 0.9);\r\n  border-color: #409EFF;\r\n  color: white;\r\n}\r\n\r\n.basis-btn.el-button--primary:hover {\r\n  background: #409EFF;\r\n  border-color: #409EFF;\r\n}\r\n\r\n/* 无权限提示样式 */\r\n.no-permission-container {\r\n  margin: 20px 0;\r\n  padding: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.no-permission-container .el-alert {\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* 滚动条样式优化 */\r\n.table-container::-webkit-scrollbar {\r\n  height: 8px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb {\r\n  background: #c1c1c1;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb:hover {\r\n  background: #a8a8a8;\r\n}\r\n\r\n\r\n\r\n/* Element UI 固定列样式优化 */\r\n::v-deep .el-table__fixed-right {\r\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 只读输入框样式 */\r\n.readonly-input ::v-deep .el-input__inner {\r\n  background-color: #f5f7fa !important;\r\n  border-color: #e4e7ed !important;\r\n  color: #909399 !important;\r\n  cursor: not-allowed !important;\r\n}\r\n\r\n.readonly-input ::v-deep .el-input__inner:hover {\r\n  border-color: #e4e7ed !important;\r\n}\r\n\r\n.readonly-input ::v-deep .el-input__inner:focus {\r\n  border-color: #e4e7ed !important;\r\n  box-shadow: none !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAoiBA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,sBAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,eAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,mBAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,cAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,cAAA,GAAAH,sBAAA,CAAAJ,OAAA;AACA,IAAAQ,wBAAA,GAAAJ,sBAAA,CAAAJ,OAAA;AACA,IAAAS,sBAAA,GAAAL,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAGA;EACAU,IAAA;EACAC,UAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,kBAAA,EAAAA,2BAAA;IACAC,oBAAA,EAAAA,sBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,uBAAA,EAAAA,gCAAA;IACAC,qBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,UAAA;MACA;MACAC,iBAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,MAAA;QAAA;QACAC,OAAA;QAAA;QACAC,SAAA;QAAA;QACAC,UAAA;QAAA;QACAC,SAAA;QAAA;QACAC,QAAA;MACA;MACA;MACAC,qBAAA;MACA;MACAC,aAAA;MACA;MACAC,eAAA;MACA;MACAC,cAAA;MACA;MACAC,iBAAA;MACA;MACAC,mBAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,aAAA;QACAC,UAAA;QACAC,aAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,QAAA;QACAC,cAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,iBAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,qBAAA;MACA;MACAC,KAAA;QACAZ,MAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,QAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,QAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,cAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,MAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,MAAA,GACA;UACAW,QAAA;UACAC,OAAA;UACAE,SAAA,WAAAA,UAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA;YACA,IAAAxD,KAAA,CAAA+C,IAAA,CAAAN,QAAA;cACA,KAAAc,KAAA,IAAAA,KAAA,CAAAE,IAAA;gBACAD,QAAA,KAAAE,KAAA;cACA;gBACAF,QAAA;cACA;YACA;cACA;cACAA,QAAA;YACA;UACA;QACA,EACA;QACAhB,QAAA,GACA;UACAU,QAAA;UACAC,OAAA;UACAE,SAAA,WAAAA,UAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA;YACA,IAAAxD,KAAA,CAAA+C,IAAA,CAAAN,QAAA;cACA,KAAAc,KAAA,IAAAA,KAAA,CAAAE,IAAA;gBACAD,QAAA,KAAAE,KAAA;cACA;gBACAF,QAAA;cACA;YACA;cACA;cACAA,QAAA;YACA;UACA;QACA,EACA;QACAG,YAAA,GACA;UAAAT,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAQ,cAAA,GACA;UAAAV,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,gBAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,eAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,iBAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAS,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,WAAA;IACA,KAAAC,uBAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,YAAA,EAAAA,mBAAA;IACA;IACAJ,WAAA,WAAAA,YAAA;MAAA,IAAAK,MAAA;MACA;MACA,KAAAC,QAAA,yBAAAC,IAAA,WAAAC,QAAA;QACAH,MAAA,CAAA/C,qBAAA,GAAAkD,QAAA,CAAAvE,IAAA;MACA;MACA;MACA,KAAAqE,QAAA,2BAAAC,IAAA,WAAAC,QAAA;QACAH,MAAA,CAAA9C,aAAA,GAAAiD,QAAA,CAAAvE,IAAA;MACA;MACA;MACA,KAAAqE,QAAA,cAAAC,IAAA,WAAAC,QAAA;QACAH,MAAA,CAAA7C,eAAA,GAAAgD,QAAA,CAAAvE,IAAA;MACA;IACA;IACA,kBACAwE,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA3D,WAAA,CAAAM,QAAA;QACA,KAAAlB,OAAA;QACA,KAAAwE,uBAAA;QACA;MACA;MAEA,KAAAxE,OAAA;MAEAyE,OAAA,CAAAC,GAAA,mBAAAnD,iBAAA;MACAkD,OAAA,CAAAC,GAAA,mBAAAlD,mBAAA;;MAEA;MACA,IAAAmD,MAAA,OAAAC,cAAA,CAAAC,OAAA,WAAApD,WAAA;MACA,KAAAkD,MAAA,CAAAA,MAAA;QACAA,MAAA,CAAAA,MAAA;MACA;;MAEA;MACA,SAAApD,iBAAA,SAAAA,iBAAA,CAAAuD,MAAA;QACAH,MAAA,CAAAA,MAAA,iCAAApD,iBAAA;QACAoD,MAAA,CAAAA,MAAA,+BAAApD,iBAAA;MACA;;MAEA;MACA,SAAAC,mBAAA,SAAAA,mBAAA,CAAAsD,MAAA;QACAH,MAAA,CAAAA,MAAA,mCAAAnD,mBAAA;QACAmD,MAAA,CAAAA,MAAA,iCAAAnD,mBAAA;MACA;MAEAiD,OAAA,CAAAC,GAAA,UAAAC,MAAA;MAEA,IAAAI,0BAAA,EAAAJ,MAAA,EAAAP,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAAjE,cAAA,GAAA+D,QAAA,CAAAW,IAAA;QACAT,MAAA,CAAAlE,KAAA,GAAAgE,QAAA,CAAAhE,KAAA;QACAkE,MAAA,CAAAvE,OAAA;MACA;IACA;IAEA,eACA8D,WAAA,WAAAA,YAAA;MAAA,IAAAmB,MAAA;MACA,IAAA3D,qCAAA,IAAA8C,IAAA,WAAAC,QAAA;QACAY,MAAA,CAAA3D,cAAA,GAAA+C,QAAA,CAAAvE,IAAA;QACA;QACAmF,MAAA,CAAAX,OAAA;MACA;IACA;IAEA,eACAP,uBAAA,WAAAA,wBAAA;MAAA,IAAAmB,MAAA;MACA,IAAAC,wBAAA,IAAAf,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAe,IAAA,YAAAf,QAAA,CAAAvE,IAAA;UACAoF,MAAA,CAAAvE,SAAA,GAAA0D,QAAA,CAAAvE,IAAA,CAAAa,SAAA;UACAuE,MAAA,CAAAG,cAAA;QACA;UACA;UACAH,MAAA,CAAAvE,SAAA;UACAuE,MAAA,CAAAG,cAAA;QACA;MACA,GAAAC,KAAA,WAAAC,KAAA;QACAd,OAAA,CAAAc,KAAA,cAAAA,KAAA;QACA;QACAL,MAAA,CAAAvE,SAAA;QACAuE,MAAA,CAAAG,cAAA;MACA;IACA;IAEA,iBACAA,cAAA,WAAAA,eAAA;MACA;MACA,IAAAG,aAAA;MACA,KAAAA,aAAA,CAAAC,QAAA,MAAA9E,SAAA;QACA;QACA,KAAAC,WAAA;UACAC,MAAA;UACAC,OAAA;UACAC,SAAA;UACAC,UAAA;UACAC,SAAA;UACAC,QAAA;QACA;QACA,KAAAsD,uBAAA;QACA;MACA;MAEA,aAAA7D,SAAA;QACA;UACA;UACA,KAAAC,WAAA;YACAC,MAAA;YACAC,OAAA;YACAC,SAAA;YACAC,UAAA;YACAC,SAAA;YACAC,QAAA;UACA;UACA;QACA;UACA;UACA,KAAAN,WAAA;YACAC,MAAA;YACAC,OAAA;YACAC,SAAA;YACAC,UAAA;YACAC,SAAA;YACAC,QAAA;UACA;UACA;QACA;UACA;UACA,KAAAN,WAAA;YACAC,MAAA;YACAC,OAAA;YACAC,SAAA;YACAC,UAAA;YACAC,SAAA;YACAC,QAAA;UACA;UACA;UACA,KAAAwE,qBAAA;UACA;QACA;UACA;UACA,KAAA9E,WAAA;YACAC,MAAA;YACAC,OAAA;YACAC,SAAA;YACAC,UAAA;YACAC,SAAA;YACAC,QAAA;UACA;UACA;QACA;UACA;UACA,KAAAN,WAAA;YACAC,MAAA;YACAC,OAAA;YACAC,SAAA;YACAC,UAAA;YACAC,SAAA;YACAC,QAAA;UACA;UACA,KAAAsD,uBAAA;MACA;IACA;IAEA,eACAkB,qBAAA,WAAAA,sBAAA;MACA;MACA,KAAAjE,WAAA,CAAAiB,OAAA;IACA;IAEA,cACA8B,uBAAA,WAAAA,wBAAA;MACA;MACA;MACA,KAAAlE,cAAA;MACA,KAAAD,KAAA;IACA;IACA;IACAsF,MAAA,WAAAA,OAAA;MACA,KAAAnF,IAAA;MACA,KAAAoF,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA9C,IAAA;QACA+C,EAAA;QACAjE,UAAA;QACAC,aAAA;QACAC,UAAA;QACAC,aAAA;QACAC,QAAA;QACAC,QAAA;QACAC,WAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAE,cAAA;QACAD,QAAA;QACAkB,YAAA;QACAC,cAAA;QACAhB,gBAAA;QACAC,eAAA;QACAC,iBAAA;MACA;MACA;MACA,KAAAE,qBAAA;MACA,KAAA+C,SAAA;IACA;IACA;IACAC,oBAAA,WAAAA,qBAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAA/E,qBAAA,EAAA6E,GAAA,CAAAvD,cAAA;IACA;IACA;IACA0D,WAAA,WAAAA,YAAAH,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,MAAA9E,aAAA,EAAA4E,GAAA,CAAAtD,OAAA;IACA;IACA;IACA0D,UAAA,WAAAA,WAAAJ,GAAA,EAAAC,MAAA,EAAAI,SAAA;MACA,KAAAA,SAAA;MACA;MACA,IAAAA,SAAA,CAAAZ,QAAA,cAAAY,SAAA;MACA;MACA,IAAAA,SAAA,CAAAvB,MAAA;QACA,IAAAwB,IAAA,GAAAD,SAAA,CAAAE,SAAA;QACA,IAAAC,KAAA,GAAAH,SAAA,CAAAE,SAAA;QACA,IAAAE,GAAA,GAAAJ,SAAA,CAAAE,SAAA;QACA,UAAAG,MAAA,CAAAJ,IAAA,OAAAI,MAAA,CAAAF,KAAA,OAAAE,MAAA,CAAAD,GAAA;MACA;MACA,OAAAJ,SAAA;IACA;IACA;IACAM,iBAAA,WAAAA,kBAAAC,SAAA;MACA,KAAAA,SAAA;MACA;MACA,IAAAA,SAAA,CAAAnB,QAAA,SAAAmB,SAAA,CAAAnB,QAAA,cAAAmB,SAAA;MACA;MACA,IAAAA,SAAA,CAAA9B,MAAA;QACA,IAAAwB,IAAA,GAAAM,SAAA,CAAAL,SAAA;QACA,IAAAC,KAAA,GAAAI,SAAA,CAAAL,SAAA;QACA,IAAAE,GAAA,GAAAG,SAAA,CAAAL,SAAA;QACA,UAAAG,MAAA,CAAAJ,IAAA,OAAAI,MAAA,CAAAF,KAAA,OAAAE,MAAA,CAAAD,GAAA;MACA;MACA,OAAAG,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA;MACA,UAAAjG,WAAA,CAAAM,QAAA;QACA,KAAAsD,uBAAA;QACA;MACA;MACA,KAAA/C,WAAA,CAAAC,OAAA;MACA,KAAA4C,OAAA;IACA;IACA,aACAwC,UAAA,WAAAA,WAAA;MACA;MACA,UAAAlG,WAAA,CAAAM,QAAA;QACA,KAAAsD,uBAAA;QACA;MACA;MAEA,KAAAjD,iBAAA;MACA,KAAAC,mBAAA;MACA,KAAAsE,SAAA;;MAEA;MACA,SAAAnF,SAAA;QACA,KAAA+E,qBAAA;MACA;MAEA,KAAAmB,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA/G,GAAA,GAAA+G,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAArB,EAAA;MAAA;MACA,KAAA3F,MAAA,GAAA8G,SAAA,CAAAlC,MAAA;MACA,KAAA3E,QAAA,IAAA6G,SAAA,CAAAlC,MAAA;IACA;IACA,aACAqC,SAAA,WAAAA,UAAA;MACA,KAAAvB,KAAA;MACA,KAAAnF,UAAA;MACA;MACA,SAAA2G,MAAA,CAAAC,OAAA,CAAA/H,IAAA;QACA;QACA,KAAAgI,kBAAA,MAAAF,MAAA,CAAAC,OAAA,CAAA/H,IAAA;MACA;MACA,KAAAkB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAgH,YAAA,WAAAA,aAAAvB,GAAA;MAAA,IAAAwB,MAAA;MACA,KAAA5B,KAAA;MACA,IAAAC,EAAA;MAEA,IAAAG,GAAA,IAAAA,GAAA,CAAAH,EAAA;QACA;QACA,IAAAG,GAAA,CAAAtD,OAAA;UACA,KAAA+E,QAAA;UACA;QACA;QACA5B,EAAA,GAAAG,GAAA,CAAAH,EAAA;MACA;QACA;QACA,UAAA5F,GAAA,SAAAA,GAAA,CAAA6E,MAAA;UACA,KAAA2C,QAAA;UACA;QACA;QACA,SAAAxH,GAAA,CAAA6E,MAAA;UACA,KAAA2C,QAAA;UACA;QACA;;QAEA;QACA,IAAAC,WAAA,QAAApH,cAAA,CAAAqH,IAAA,WAAAT,IAAA;UAAA,OAAAA,IAAA,CAAArB,EAAA,KAAA2B,MAAA,CAAAvH,GAAA;QAAA;QACA,KAAAyH,WAAA;UACA,KAAAD,QAAA;UACA;QACA;QACA,IAAAC,WAAA,CAAAhF,OAAA;UACA,KAAA+E,QAAA;UACA;QACA;QACA5B,EAAA,GAAA6B,WAAA,CAAA7B,EAAA;MACA;MACApB,OAAA,CAAAC,GAAA,eAAAmB,EAAA;MACApB,OAAA,CAAAC,GAAA,gBAAAsB,GAAA;MAEA,KAAAH,EAAA;QACA,KAAA4B,QAAA;QACA;MACA;MAEA,IAAAG,yBAAA,EAAA/B,EAAA,EAAAzB,IAAA,WAAAC,QAAA;QACAmD,MAAA,CAAA1E,IAAA,GAAAuB,QAAA,CAAAvE,IAAA;QACA;QACA0H,MAAA,CAAA1E,IAAA,CAAAY,YAAA,GAAA8D,MAAA,CAAAb,iBAAA,CAAAa,MAAA,CAAA1E,IAAA,CAAAY,YAAA;QACA8D,MAAA,CAAA1E,IAAA,CAAAa,cAAA,GAAA6D,MAAA,CAAAb,iBAAA,CAAAa,MAAA,CAAA1E,IAAA,CAAAa,cAAA;QACA;QACA6D,MAAA,CAAAK,sBAAA,CAAAL,MAAA,CAAA1E,IAAA,CAAAD,iBAAA;QACA2E,MAAA,CAAA/G,UAAA;QACA+G,MAAA,CAAAhH,IAAA;QACAgH,MAAA,CAAAjH,KAAA;MACA,GAAA+E,KAAA,WAAAC,KAAA;QACAd,OAAA,CAAAc,KAAA,YAAAA,KAAA;QACAiC,MAAA,CAAAC,QAAA;MACA;IACA;IAEA,aACAK,UAAA,WAAAA,WAAA9B,GAAA;MAAA,IAAA+B,MAAA;MACA,KAAAnC,KAAA;MACA,IAAAC,EAAA,GAAAG,GAAA,CAAAH,EAAA;MACApB,OAAA,CAAAC,GAAA,eAAAmB,EAAA;MAEA,KAAAA,EAAA;QACA,KAAA4B,QAAA;QACA;MACA;MAEA,IAAAG,yBAAA,EAAA/B,EAAA,EAAAzB,IAAA,WAAAC,QAAA;QACA0D,MAAA,CAAAjF,IAAA,GAAAuB,QAAA,CAAAvE,IAAA;QACA;QACAiI,MAAA,CAAAjF,IAAA,CAAAY,YAAA,GAAAqE,MAAA,CAAApB,iBAAA,CAAAoB,MAAA,CAAAjF,IAAA,CAAAY,YAAA;QACAqE,MAAA,CAAAjF,IAAA,CAAAa,cAAA,GAAAoE,MAAA,CAAApB,iBAAA,CAAAoB,MAAA,CAAAjF,IAAA,CAAAa,cAAA;QACA;QACAoE,MAAA,CAAAF,sBAAA,CAAAE,MAAA,CAAAjF,IAAA,CAAAD,iBAAA;QACAkF,MAAA,CAAAtH,UAAA;QACAsH,MAAA,CAAAvH,IAAA;QACAuH,MAAA,CAAAxH,KAAA;MACA,GAAA+E,KAAA,WAAAC,KAAA;QACAd,OAAA,CAAAc,KAAA,YAAAA,KAAA;QACAwC,MAAA,CAAAN,QAAA;MACA;IACA;IACA,WACAO,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAnF,IAAA,CAAA+C,EAAA;YACA,IAAAwC,4BAAA,EAAAJ,MAAA,CAAAnF,IAAA,EAAAsB,IAAA,WAAAC,QAAA;cACA4D,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAzH,IAAA;cACAyH,MAAA,CAAA3D,OAAA;YACA;UACA;YACA,IAAAiE,yBAAA,EAAAN,MAAA,CAAAnF,IAAA,EAAAsB,IAAA,WAAAC,QAAA;cACA4D,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAzH,IAAA;cACAyH,MAAA,CAAA3D,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAkE,aAAA,WAAAA,cAAAxC,GAAA;MAAA,IAAAyC,MAAA;MACA,IAAAxI,GAAA;MACA,IAAAyI,gBAAA;MAEA,IAAA1C,GAAA,IAAAA,GAAA,CAAAH,EAAA;QACA;QACA,IAAAG,GAAA,CAAAtD,OAAA;UACA,KAAA+E,QAAA;UACA;QACA;QACAxH,GAAA,GAAA+F,GAAA,CAAAH,EAAA;QACA6C,gBAAA,IAAA1C,GAAA;MACA;QACA;QACA,UAAA/F,GAAA,SAAAA,GAAA,CAAA6E,MAAA;UACA,KAAA2C,QAAA;UACA;QACA;;QAEA;QACA,IAAAkB,YAAA,QAAArI,cAAA,CAAAsI,MAAA,WAAA1B,IAAA;UAAA,OAAAuB,MAAA,CAAAxI,GAAA,CAAAwF,QAAA,CAAAyB,IAAA,CAAArB,EAAA;QAAA;QACA,IAAAgD,iBAAA,GAAAF,YAAA,CAAAG,IAAA,WAAA5B,IAAA;UAAA,OAAAA,IAAA,CAAAxE,OAAA;QAAA;QACA,IAAAmG,iBAAA;UACA,KAAApB,QAAA;UACA;QACA;QAEAxH,GAAA,QAAAA,GAAA;QACAyI,gBAAA,GAAAC,YAAA;MACA;MAEA,KAAA1I,GAAA,IAAA8I,KAAA,CAAAC,OAAA,CAAA/I,GAAA,KAAAA,GAAA,CAAA6E,MAAA;QACA,KAAA2C,QAAA;QACA;MACA;;MAEA;MACA,IAAAwB,gBAAA,QAAAC,sBAAA,CAAAR,gBAAA;MACA,KAAAO,gBAAA,CAAAE,OAAA;QACA,KAAA1B,QAAA,CAAAwB,gBAAA,CAAA9F,OAAA;QACA;MACA;MAEA,IAAAiG,UAAA,GAAAL,KAAA,CAAAC,OAAA,CAAA/I,GAAA,IAAAA,GAAA,CAAAoJ,IAAA,QAAApJ,GAAA;MACA,KAAAqJ,QAAA;QACAC,gBAAA;QACAC,iBAAA;QACAC,IAAA;MACA,GAAArF,IAAA;QACA,WAAAsF,6BAAA,EAAAN,UAAA;MACA,GAAAhF,IAAA;QACAqE,MAAA,CAAAnE,OAAA;QACAmE,MAAA,CAAAH,UAAA;MACA;IACA;IAEA,aACAY,sBAAA,WAAAA,uBAAAS,OAAA;MACA,IAAAC,cAAA,IACA;QAAAC,KAAA;QAAAvK,IAAA;MAAA,GACA;QAAAuK,KAAA;QAAAvK,IAAA;MAAA,GACA;QAAAuK,KAAA;QAAAvK,IAAA;MAAA,GACA;QAAAuK,KAAA;QAAAvK,IAAA;MAAA,GACA;QAAAuK,KAAA;QAAAvK,IAAA;MAAA,GACA;QAAAuK,KAAA;QAAAvK,IAAA;MAAA,GACA;QAAAuK,KAAA;QAAAvK,IAAA;MAAA,GACA;QAAAuK,KAAA;QAAAvK,IAAA;MAAA,GACA;QAAAuK,KAAA;QAAAvK,IAAA;MAAA,GACA;QAAAuK,KAAA;QAAAvK,IAAA;MAAA,EACA;MAEA,SAAAwK,CAAA,MAAAA,CAAA,GAAAH,OAAA,CAAA7E,MAAA,EAAAgF,CAAA;QACA,IAAAC,MAAA,GAAAJ,OAAA,CAAAG,CAAA;QACA,IAAAE,WAAA,GAAAF,CAAA;QAAA,IAAAG,SAAA,OAAAC,2BAAA,CAAArF,OAAA,EAEA+E,cAAA;UAAAO,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;YAAA,IAAAC,aAAA,GAAAJ,KAAA,CAAA7G,KAAA;YACA,IAAAkH,UAAA,GAAAT,MAAA,CAAAQ,aAAA,CAAAV,KAAA;;YAEA;YACA,KAAAW,UAAA,WAAAA,UAAA,iBAAAA,UAAA,CAAAhH,IAAA;cACA;gBACA2F,OAAA;gBACAhG,OAAA,WAAAuD,MAAA,CAAA6D,aAAA,CAAAjL,IAAA;cACA;YACA;UACA;QAAA,SAAAmL,GAAA;UAAAR,SAAA,CAAAS,CAAA,CAAAD,GAAA;QAAA;UAAAR,SAAA,CAAAU,CAAA;QAAA;MACA;MAEA;QACAxB,OAAA;QACAhG,OAAA;MACA;IACA;IAEA,aACAyH,YAAA,WAAAA,aAAA5E,GAAA;MAAA,IAAA6E,MAAA;MACA,IAAA5K,GAAA;MAEA,IAAA+F,GAAA,IAAAA,GAAA,CAAAH,EAAA;QACA;QACA,IAAAG,GAAA,CAAAtD,OAAA;UACA,KAAA+E,QAAA;UACA;QACA;QACAxH,GAAA,GAAA+F,GAAA,CAAAH,EAAA;MACA;QACA;QACA,UAAA5F,GAAA,SAAAA,GAAA,CAAA6E,MAAA;UACA,KAAA2C,QAAA;UACA;QACA;;QAEA;QACA,IAAAkB,YAAA,QAAArI,cAAA,CAAAsI,MAAA,WAAA1B,IAAA;UAAA,OAAA2D,MAAA,CAAA5K,GAAA,CAAAwF,QAAA,CAAAyB,IAAA,CAAArB,EAAA;QAAA;QACA,IAAAgD,iBAAA,GAAAF,YAAA,CAAAG,IAAA,WAAA5B,IAAA;UAAA,OAAAA,IAAA,CAAAxE,OAAA;QAAA;QACA,IAAAmG,iBAAA;UACA,KAAApB,QAAA;UACA;QACA;QAEAxH,GAAA,QAAAA,GAAA;MACA;MACA,KAAAA,GAAA;QACA,KAAAwH,QAAA;QACA;MACA;MAEA,KAAA6B,QAAA;QACAC,gBAAA;QACAC,iBAAA;QACAC,IAAA;MACA,GAAArF,IAAA;QACA,WAAA0G,yBAAA,EAAA7K,GAAA;MACA,GAAAmE,IAAA;QACAyG,MAAA,CAAAvG,OAAA;QACAuG,MAAA,CAAAvC,UAAA;MACA;IACA;IACA,uBACAyC,kBAAA,WAAAA,mBAAA;MACA,KAAArK,iBAAA;MACA,KAAAwH,KAAA,CAAA8C,cAAA,CAAAC,IAAA;IACA;IACA,yBACAC,0BAAA,WAAAA,2BAAA;MACA,KAAAxK,iBAAA;MACA,KAAAwH,KAAA,CAAA8C,cAAA,CAAAC,IAAA;IACA;IACA,cACAE,gBAAA,WAAAA,iBAAAC,QAAA;MACA,SAAA1K,iBAAA;QACA;QACA,KAAAoC,IAAA,CAAAV,MAAA,GAAAgJ,QAAA,CAAAhJ,MAAA;QACA,KAAAU,IAAA,CAAAT,QAAA,GAAA+I,QAAA,CAAA/I,QAAA;MACA,gBAAA3B,iBAAA;QACA;QACA,KAAAe,WAAA,CAAAW,MAAA,GAAAgJ,QAAA,CAAAhJ,MAAA;QACA,KAAAX,WAAA,CAAAY,QAAA,GAAA+I,QAAA,CAAA/I,QAAA;MACA;IACA;IACA,sBACAgJ,6BAAA,WAAAA,8BAAA;MACA,KAAA3K,iBAAA;MACA,KAAAwH,KAAA,CAAAoD,kBAAA,CAAAL,IAAA;IACA;IACA,wBACAM,8BAAA,WAAAA,+BAAA;MACA,KAAA7K,iBAAA;MACA,KAAAwH,KAAA,CAAAoD,kBAAA,CAAAL,IAAA;IACA;IACA,aACAO,oBAAA,WAAAA,qBAAAC,YAAA;MACA,SAAA/K,iBAAA;QACA;QACA,KAAAoC,IAAA,CAAAR,MAAA,GAAAmJ,YAAA,CAAAC,MAAA;QACA,KAAA5I,IAAA,CAAAP,QAAA,GAAAkJ,YAAA,CAAAlJ,QAAA;MACA,gBAAA7B,iBAAA;QACA;QACA,KAAAe,WAAA,CAAAa,MAAA,GAAAmJ,YAAA,CAAAC,MAAA;QACA,KAAAjK,WAAA,CAAAc,QAAA,GAAAkJ,YAAA,CAAAlJ,QAAA;MACA;IACA;IACA,oBACAoJ,wBAAA,WAAAA,yBAAA;MACA,KAAAjL,iBAAA;MACA,KAAAwH,KAAA,CAAA0D,aAAA,CAAAX,IAAA;IACA;IACA,wBACAY,yBAAA,WAAAA,0BAAA;MACA,KAAAnL,iBAAA;MACA,KAAAwH,KAAA,CAAA0D,aAAA,CAAAX,IAAA;IACA;IACA,aACAa,mBAAA,WAAAA,oBAAAC,WAAA;MACA,SAAArL,iBAAA;QACA;QACA,KAAAoC,IAAA,CAAAR,MAAA,GAAAyJ,WAAA,CAAAC,SAAA;QACA,KAAAlJ,IAAA,CAAAP,QAAA,GAAAwJ,WAAA,CAAAE,WAAA;MACA,gBAAAvL,iBAAA;QACA;QACA,KAAAe,WAAA,CAAAa,MAAA,GAAAyJ,WAAA,CAAAC,SAAA;QACA,KAAAvK,WAAA,CAAAc,QAAA,GAAAwJ,WAAA,CAAAE,WAAA;MACA;IACA;IACA,oBACAC,wBAAA,WAAAA,yBAAA;MACA,KAAAxL,iBAAA;MACA,KAAAwH,KAAA,CAAAiE,aAAA,CAAAlB,IAAA;IACA;IACA,sBACAmB,yBAAA,WAAAA,0BAAA;MACA,KAAA1L,iBAAA;MACA,KAAAwH,KAAA,CAAAiE,aAAA,CAAAlB,IAAA;IACA;IACA,aACAoB,mBAAA,WAAAA,oBAAAC,WAAA;MACA,SAAA5L,iBAAA;QACA;QACA,KAAAoC,IAAA,CAAAR,MAAA,GAAAgK,WAAA,CAAAC,SAAA;QACA,KAAAzJ,IAAA,CAAAP,QAAA,GAAA+J,WAAA,CAAAE,WAAA;MACA,gBAAA9L,iBAAA;QACA;QACA,KAAAe,WAAA,CAAAa,MAAA,GAAAgK,WAAA,CAAAC,SAAA;QACA,KAAA9K,WAAA,CAAAc,QAAA,GAAA+J,WAAA,CAAAE,WAAA;MACA;IACA;IACA,eACAC,0BAAA,WAAAA,2BAAAV,WAAA;MACA,SAAArL,iBAAA;QACA;QACA,KAAAoC,IAAA,CAAAR,MAAA,GAAAyJ,WAAA,CAAAC,SAAA;QACA,KAAAlJ,IAAA,CAAAP,QAAA,GAAAwJ,WAAA,CAAAE,WAAA;MACA,gBAAAvL,iBAAA;QACA;QACA,KAAAe,WAAA,CAAAa,MAAA,GAAAyJ,WAAA,CAAAC,SAAA;QACA,KAAAvK,WAAA,CAAAc,QAAA,GAAAwJ,WAAA,CAAAE,WAAA;MACA;IACA;IACA,kBACAS,6BAAA,WAAAA,8BAAApJ,KAAA;MAAA,IAAAqJ,MAAA;MACA;MACA,IAAArJ,KAAA,YAAAA,KAAA,YAAAA,KAAA;QACA;QACA,KAAAR,IAAA,CAAAR,MAAA;QACA,KAAAQ,IAAA,CAAAP,QAAA;MACA;QACA;QACA,KAAAO,IAAA,CAAAR,MAAA;QACA,KAAAQ,IAAA,CAAAP,QAAA;MACA;;MAEA;MACA,KAAAqK,SAAA;QACA,IAAAD,MAAA,CAAAzE,KAAA,CAAApF,IAAA;UACA6J,MAAA,CAAAzE,KAAA,CAAApF,IAAA,CAAA+J,aAAA;QACA;MACA;IACA;IACA,uBACAC,kCAAA,WAAAA,mCAAAxJ,KAAA;MACA;MACA,IAAAA,KAAA,YAAAA,KAAA,YAAAA,KAAA;QACA;QACA,KAAA7B,WAAA,CAAAa,MAAA;QACA,KAAAb,WAAA,CAAAc,QAAA;MACA;QACA;QACA,KAAAd,WAAA,CAAAa,MAAA;QACA,KAAAb,WAAA,CAAAc,QAAA;MACA;IACA;IACA,iBACAwK,2BAAA,WAAAA,4BAAA;MACA,KAAA7E,KAAA,CAAA8E,uBAAA,CAAA/B,IAAA,MAAAnI,IAAA,CAAAD,iBAAA;IACA;IACA,eACAoK,6BAAA,WAAAA,8BAAAC,WAAA;MACA,KAAApK,IAAA,CAAAD,iBAAA,GAAAqK,WAAA;MACA,KAAArF,sBAAA,CAAAqF,WAAA;IACA;IAEA,iBACAC,yBAAA,WAAAA,0BAAA;MACA1I,OAAA,CAAAC,GAAA,uBAAA5B,IAAA,CAAAF,eAAA;MACA,KAAAsF,KAAA,CAAAkF,qBAAA,CAAAnC,IAAA,MAAAnI,IAAA,CAAAF,eAAA;IACA;IAEA,eACAyK,2BAAA,WAAAA,4BAAAC,SAAA;MACA,KAAAxK,IAAA,CAAAF,eAAA,GAAA0K,SAAA;IACA;IAEA,kBACAzF,sBAAA,WAAAA,uBAAAqF,WAAA;MAAA,IAAAK,OAAA;MACA,KAAAxK,qBAAA;MACA,KAAAmK,WAAA;MAEA,IAAAM,QAAA,GAAAN,WAAA,CAAAO,KAAA,MAAA7E,MAAA,WAAA1B,IAAA;QAAA,OAAAA,IAAA,CAAA1D,IAAA;MAAA;MACAgK,QAAA,CAAAE,OAAA,WAAAC,OAAA;QACA,IAAAC,GAAA,GAAAL,OAAA,CAAAM,gBAAA,CAAAF,OAAA,CAAAnK,IAAA;QACA,IAAAoK,GAAA;UACAL,OAAA,CAAAxK,qBAAA,CAAA+K,IAAA,CAAAF,GAAA;QACA;MACA;IACA;IAEA,eACAC,gBAAA,WAAAA,iBAAAX,WAAA;MACA,IAAAA,WAAA,CAAAzH,QAAA,UAAAyH,WAAA,CAAAzH,QAAA;QACA;UAAAsI,IAAA,EAAAb,WAAA;UAAAzD,IAAA;QAAA;MACA,WAAAyD,WAAA;QACA;UAAAa,IAAA,EAAAb,WAAA;UAAAzD,IAAA;QAAA;MACA,WAAAyD,WAAA;QACA;UAAAa,IAAA,EAAAb,WAAA;UAAAzD,IAAA;QAAA;MACA,WAAAyD,WAAA,CAAAzH,QAAA,UAAAyH,WAAA,CAAAzH,QAAA;QACA;UAAAsI,IAAA,EAAAb,WAAA;UAAAzD,IAAA;QAAA;MACA;MACA;IACA;IAEA,kBACAuE,UAAA,WAAAA,WAAAvE,IAAA;MACA,IAAAwE,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAxE,IAAA;IACA;IAEA,eACAyE,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAApL,qBAAA,CAAAqL,MAAA,CAAAD,KAAA;MACA,KAAAE,iBAAA;IACA;IAEA,eACAA,iBAAA,WAAAA,kBAAA;MACA,IAAAC,YAAA,QAAAvL,qBAAA,CAAAkE,GAAA,WAAA2G,GAAA;QAAA,OAAAA,GAAA,CAAAG,IAAA;MAAA;MACA,KAAAjL,IAAA,CAAAD,iBAAA,GAAAyL,YAAA,CAAAjF,IAAA;IACA;IAGA,uBACA/B,kBAAA,WAAAA,mBAAAtF,QAAA;MAAA,IAAAuM,OAAA;MACA,IAAAC,0BAAA,EAAAxM,QAAA,EAAAoC,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAe,IAAA,YAAAf,QAAA,CAAAvE,IAAA;UACA;UACA,IAAA2O,QAAA,GAAApK,QAAA,CAAAvE,IAAA,CAAA4O,UAAA;UACAH,OAAA,CAAAzL,IAAA,CAAAZ,WAAA,GAAAuM,QAAA;UACAF,OAAA,CAAAzL,IAAA,CAAAX,MAAA,GAAAsM,QAAA;UACAF,OAAA,CAAAzL,IAAA,CAAAd,QAAA,GAAAqC,QAAA,CAAAvE,IAAA,CAAA6O,QAAA;QAEA;MACA,GAAArJ,KAAA,WAAAC,KAAA;QACAd,OAAA,CAAAmK,IAAA,cAAArJ,KAAA;QACA;MACA;IACA;IAIA,aACAsJ,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,KAAAxF,QAAA;QACAE,iBAAA;QACAD,gBAAA;QACAE,IAAA;MACA,GAAArF,IAAA;QACA;QACA,IAAAO,MAAA,OAAAC,cAAA,CAAAC,OAAA,MAAAiK,OAAA,CAAArN,WAAA;QACA,KAAAkD,MAAA,CAAAA,MAAA;UACAA,MAAA,CAAAA,MAAA;QACA;;QAEA;QACA,IAAAmK,OAAA,CAAAvN,iBAAA,IAAAuN,OAAA,CAAAvN,iBAAA,CAAAuD,MAAA;UACAH,MAAA,CAAAA,MAAA,4BAAAmK,OAAA,CAAAvN,iBAAA;UACAoD,MAAA,CAAAA,MAAA,0BAAAmK,OAAA,CAAAvN,iBAAA;QACA;;QAEA;QACA,IAAAuN,OAAA,CAAAtN,mBAAA,IAAAsN,OAAA,CAAAtN,mBAAA,CAAAsD,MAAA;UACAH,MAAA,CAAAA,MAAA,8BAAAmK,OAAA,CAAAtN,mBAAA;UACAmD,MAAA,CAAAA,MAAA,4BAAAmK,OAAA,CAAAtN,mBAAA;QACA;QAEA,WAAAuN,4BAAA,EAAApK,MAAA;MACA,GAAAP,IAAA,WAAAC,QAAA;QACAyK,OAAA,CAAAE,QAAA,CAAA3K,QAAA,CAAA4K,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}