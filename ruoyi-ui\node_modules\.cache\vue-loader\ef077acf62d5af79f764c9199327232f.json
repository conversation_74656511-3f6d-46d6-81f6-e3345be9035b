{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\index.vue?vue&type=template&id=682a92dc&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\index.vue", "mtime": 1756456493918}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}