{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\detail.vue", "mtime": 1756456282508}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "_list", "_target", "_user", "_vuedraggable", "_interopRequireDefault", "name", "components", "draggable", "data", "loading", "showSearch", "targetList", "title", "open", "queryParams", "workNo", "form", "rules", "userInfo", "openEdit", "upload", "isUploading", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "editTitle", "editData", "spanList", "itemList", "standardList", "created", "initPageData", "watch", "$route", "to", "path", "beforeRouteUpdate", "from", "next", "userId", "query", "getSelfAssessUser", "getList", "methods", "$message", "error", "$router", "go", "_this", "id", "then", "res", "catch", "console", "_this2", "listTargetAll", "response", "handleSpanList", "itemFlag", "standardFlag", "i", "length", "push", "rowspan", "colspan", "item", "standard", "cancel", "reset", "sort", "category", "target", "createBy", "createTime", "updateBy", "updateTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleEdit", "handleToEditInfo", "JSON", "parse", "stringify", "cancelEdit", "submitEdit", "_this3", "log", "verifyEdit", "type", "message", "$confirm", "confirmButtonText", "cancelButtonText", "batchData", "_this4", "handleEditData", "batchTarget", "code", "handleFileUploadProgress", "handleFileSuccess", "downloadTemplate", "getTemplateFile", "localUrl", "window", "location", "host", "replace", "handleEditDelete", "index", "splice", "addRow", "objectSpanMethod", "_ref", "row", "rowIndex", "columnIndex"], "sources": ["src/views/assess/self/config/user/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-descriptions class=\"margin-top\" title=\"用户信息\" :column=\"3\" border>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          姓名\r\n        </template>\r\n        {{ userInfo.name }}\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          工号\r\n        </template>\r\n        {{ userInfo.workNo }}\r\n      </el-descriptions-item>\r\n      <!-- <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          身份\r\n        </template>\r\n        <span v-if=\"userInfo.assessRole == '0'\">干部</span>\r\n        <span v-if=\"userInfo.assessRole == '1'\">一把手</span>\r\n        <span v-if=\"userInfo.assessRole == '2'\">条线领导</span>\r\n      </el-descriptions-item> -->\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          部门\r\n        </template>\r\n        <span v-for=\"item,index in userInfo.deptList\" v-bind:key=\"index\">{{ item.deptName }}</span>\r\n      </el-descriptions-item>\r\n      <el-descriptions-item>\r\n        <template slot=\"label\">\r\n          职务\r\n        </template>\r\n        <span>{{ userInfo.job }}</span>\r\n      </el-descriptions-item>\r\n    </el-descriptions>\r\n\r\n    <h4>指标配置</h4>\r\n    <el-row :gutter=\"10\" class=\"mb8\" style=\"margin-top: 10px;\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          @click=\"handleEdit\"\r\n          size=\"small\"\r\n        >编辑</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-upload\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :disabled=\"upload.isUploading\"\r\n        :action=\"upload.url\"\r\n        :show-file-list=\"false\"\r\n        :multiple=\"false\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\">\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\">导入</el-button>\r\n        </el-upload>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n          <el-button size=\"small\" type=\"info\" plain icon=\"el-icon-link\" @click=\"downloadTemplate\">导入模板下载</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"targetList\" \r\n      :span-method=\"objectSpanMethod\" border>\r\n      <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"200\"/>\r\n      <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"200\"/>\r\n      <el-table-column label=\"目标\" align=\"center\" prop=\"target\" />\r\n      <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n      <!-- <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column> -->\r\n    </el-table>\r\n    \r\n    <!-- 导入绩效考核-自评指标配置对话框 -->\r\n    <el-dialog :title=\"editTitle\" :visible.sync=\"openEdit\" width=\"1000px\" append-to-body>\r\n      <div style=\"color: red;\">\r\n        注：提交前可对内容进行修改; 鼠标按住行,拖动可变换排列顺序；确认提交后将覆盖原有配置信息。\r\n      </div>\r\n      <table class=\"table-striped\">\r\n        <thead class=\"thead-dark\">\r\n          <tr>\r\n            <th scope=\"col\">序号</th>\r\n            <th scope=\"col\">类型</th>\r\n            <th scope=\"col\">指标</th>\r\n            <th scope=\"col\">目标</th>\r\n            <th scope=\"col\">评分标准</th>\r\n            <th scope=\"col\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <draggable v-model=\"editData\" tag=\"tbody\" item-key=\"name\">\r\n          <tr v-for=\"element,index in editData\" v-bind:key=\"index\">\r\n            <td scope=\"row\">{{ index + 1 }}</td>\r\n            <td>\r\n              <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"element.item\" placeholder=\"请输入类型\"></el-input>\r\n            </td>\r\n            <td>\r\n              <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"element.category\" placeholder=\"请输入指标\"></el-input>\r\n            </td>\r\n            <td>\r\n              <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"element.target\" placeholder=\"请输入目标\"></el-input>\r\n            </td>\r\n            <td>\r\n              <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"element.standard\" placeholder=\"请输入评分标准\"></el-input>\r\n            </td>\r\n            <td>\r\n              <div>\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  @click=\"handleEditDelete(index)\"\r\n                >删除</el-button>\r\n              </div>\r\n            </td>\r\n          </tr>\r\n        </draggable>\r\n      </table>\r\n      <div>\r\n        <el-button type=\"primary\" \r\n        icon=\"el-icon-plus\" size=\"mini\" @click=\"addRow\">添加行</el-button>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitEdit\">确 定</el-button>\r\n        <el-button @click=\"cancelEdit\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { getTemplateFile } from \"@/api/templateFile/list\";\r\nimport { batchTarget, listTargetAll } from \"@/api/assess/self/target\";\r\nimport { getSelfAssessUser } from \"@/api/assess/self/user\";\r\nimport draggable from 'vuedraggable'\r\n\r\nexport default {\r\n  name: \"SelfAssessTarget\",\r\n  components: {\r\n    draggable\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 绩效考核-自评指标配置表格数据\r\n      targetList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        workNo: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      },\r\n      // 用户信息\r\n      userInfo:{},\r\n      // 编辑弹出框显示\r\n      openEdit:false,\r\n      // 导入参数\r\n      upload: {\r\n          // 是否禁用上传\r\n          isUploading: false,\r\n          // 设置上传的请求头部\r\n          headers: { Authorization: 'Bearer ' + getToken() },\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/web/selfAssess/target/handleImport\",\r\n      },\r\n      editTitle:\"\",\r\n      // 预览/编辑配置数据\r\n      editData:[],\r\n      // 合并单元格信息\r\n      spanList:{\r\n        itemList:[],\r\n        standardList:[]\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.initPageData();\r\n  },\r\n\r\n  // 监听路由变化，确保每次进入页面都重新获取数据\r\n  watch: {\r\n    '$route'(to) {\r\n      // 当路由发生变化时，重新初始化页面数据\r\n      if (to.path === '/assess/self/user/detail') {\r\n        this.initPageData();\r\n      }\r\n    }\r\n  },\r\n\r\n  // 路由更新时的钩子\r\n  beforeRouteUpdate(to, from, next) {\r\n    // 在当前路由改变，但是该组件被复用时调用\r\n    this.queryParams.userId = to.query.userId;\r\n    this.getSelfAssessUser();\r\n    this.getList();\r\n    next();\r\n  },\r\n  methods: {\r\n    // 初始化页面数据\r\n    initPageData() {\r\n      // 获取路径参数\r\n      this.queryParams.userId = this.$route.query.userId;\r\n\r\n      // 如果没有userId参数，返回上一页或首页\r\n      if (!this.queryParams.userId) {\r\n        this.$message.error('缺少必要参数');\r\n        this.$router.go(-1);\r\n        return;\r\n      }\r\n\r\n      // 重置数据\r\n      this.userInfo = {};\r\n      this.targetList = [];\r\n      this.editData = [];\r\n\r\n      // 获取用户信息和指标配置\r\n      this.getSelfAssessUser();\r\n      this.getList();\r\n    },\r\n\r\n    getSelfAssessUser(){\r\n      if (!this.queryParams.userId) {\r\n        return;\r\n      }\r\n      getSelfAssessUser({id:this.queryParams.userId}).then(res => {\r\n        this.userInfo = res.data\r\n      }).catch(error => {\r\n        console.error('获取用户信息失败:', error);\r\n        this.$message.error('获取用户信息失败');\r\n      })\r\n    },\r\n    /** 查询绩效考核-自评指标配置列表 */\r\n    getList() {\r\n      if (!this.queryParams.userId) {\r\n        this.loading = false;\r\n        return;\r\n      }\r\n\r\n      this.loading = true;\r\n      listTargetAll(this.queryParams).then(response => {\r\n        this.handleSpanList(response.data);\r\n        this.targetList = response.data;\r\n        this.loading = false;\r\n      }).catch(error => {\r\n        console.error('获取指标配置失败:', error);\r\n        this.$message.error('获取指标配置失败');\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    // 处理列表\r\n    handleSpanList(data){\r\n      let itemList = [];\r\n      let standardList = [];\r\n      let itemFlag = 0;\r\n      let standardFlag = 0;\r\n      for(let i = 0; i < data.length; i++){\r\n        // 相同考核项、评分标准合并\r\n        if(i == 0){\r\n          itemList.push({\r\n            rowspan: 1,\r\n            colspan: 1\r\n          })\r\n          standardList.push({\r\n            rowspan: 1,\r\n            colspan: 1\r\n          })\r\n        }else{\r\n          // 考核项\r\n          if(data[i - 1].item == data[i].item){\r\n            itemList.push({\r\n              rowspan: 0,\r\n              colspan: 0\r\n            })\r\n            itemList[itemFlag].rowspan += 1;\r\n          }else{\r\n            itemList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            itemFlag = i;\r\n          }\r\n          // 评分标准\r\n          if(data[i - 1].standard == data[i].standard){\r\n            standardList.push({\r\n              rowspan: 0,\r\n              colspan: 0\r\n            })\r\n            standardList[standardFlag].rowspan += 1;\r\n          }else{\r\n            standardList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            standardFlag = i;\r\n          }\r\n        }\r\n      }\r\n      this.spanList.itemList = itemList;\r\n      this.spanList.standardList = standardList;\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        workNo: null,\r\n        sort: null,\r\n        item: null,\r\n        category: null,\r\n        target: null,\r\n        standard: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n\r\n    // 编辑按钮点击事件\r\n    handleEdit(){\r\n      this.editData = this.handleToEditInfo(JSON.parse(JSON.stringify(this.targetList)));\r\n      this.editTitle = \"配置编辑\"\r\n      this.openEdit = true;\r\n    },\r\n\r\n    /** 导出按钮操作 */\r\n    // handleExport() {\r\n    //   this.download('selfAssess/target/export', {\r\n    //     ...this.queryParams\r\n    //   }, `target_${new Date().getTime()}.xlsx`)\r\n    // },\r\n\r\n    cancelEdit(){\r\n      this.openEdit = false;\r\n    },\r\n\r\n    // 确认编辑、导入\r\n    submitEdit(){\r\n      console.log(this.editData)\r\n      if(!this.verifyEdit()){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '信息未填写完整'\r\n          });\r\n          return;\r\n      };\r\n      this.$confirm('确认后将覆盖原有数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.batchData();\r\n      }).catch(() => {\r\n          \r\n      });\r\n    },\r\n\r\n    // 提交数据验证\r\n    verifyEdit(){\r\n      for(let i = 0; i < this.editData.length; i++){\r\n        if(!this.editData[i].item) return false;\r\n        // if(!this.editData[i].category) return false;\r\n        if(!this.editData[i].target) return false;\r\n        if(!this.editData[i].standard) return false;\r\n      }\r\n      return true;\r\n    },\r\n\r\n    // 新增数据\r\n    batchData(){\r\n      let data = this.handleEditData(this.editData);\r\n      batchTarget(data).then(res => {\r\n        if(res.code == 200){\r\n          this.openEdit = false;\r\n          this.editData = [];\r\n          this.getList();\r\n          this.$message({\r\n            type: 'success',\r\n            message: '提交成功!'\r\n          });\r\n        }\r\n      })\r\n    },\r\n\r\n    // 处理提交数据\r\n    handleEditData(data){\r\n      for(let i = 0; i < data.length; i++){\r\n        data[i].sort = i + 1;\r\n        data[i].userId = this.queryParams.userId;\r\n      }\r\n      return data\r\n    },\r\n\r\n    // 处理导入内容\r\n    handleToEditInfo(data){\r\n        for(let i = 0; i < data.length; i++){\r\n          if(data[i].id){\r\n            data[i].id = null;\r\n          }\r\n          // 没有考核项取上一行值\r\n          if(!data[i].item){\r\n            data[i].item = data[i-1].item\r\n          }\r\n          // 没有标准取上一行值\r\n          if(!data[i].standard){\r\n            data[i].standard = data[i-1].standard\r\n          }\r\n          // 没有类别 有目标 类别取上一行内容\r\n          if(!data[i].category && data[i].target){\r\n            // 没有类别且没有目标，\r\n            data[i].category = data[i-1].category\r\n          }\r\n          // 有类别 没有目标 目标取类别内容 类别空\r\n          if(data[i].category && !data[i].target){\r\n            // 没有类别且没有目标，\r\n            data[i].target = data[i].category;\r\n            data[i].category = \"\"\r\n          }\r\n        }\r\n        return data;\r\n    },\r\n\r\n\r\n    handleFileUploadProgress(){\r\n        this.upload.isUploading = true\r\n    },\r\n    handleFileSuccess(response){\r\n        console.log(response)\r\n        this.upload.isUploading = false\r\n        this.editData = this.handleToEditInfo(response.data);\r\n        this.editTitle = \"导入预览\";\r\n        this.openEdit = true;\r\n    },\r\n\r\n    // 模板下载\r\n    downloadTemplate(){\r\n      getTemplateFile({id:\"42\"}).then(res => {\r\n          if(res.code == 200){\r\n            let localUrl = window.location.host;\r\n            if(localUrl === \"************:8099\"){\r\n              res.data.url = res.data.url.replace(\"ydxt.citicsteel.com:8099\",\"************:8099\");\r\n            }\r\n            let url = res.data.url;\r\n            window.open(url);\r\n          }\r\n      })\r\n    },\r\n\r\n    // 编辑行删除\r\n    handleEditDelete(index){\r\n      this.editData.splice(index,1)\r\n    },\r\n\r\n    // 添加行\r\n    addRow(){\r\n      this.editData.push({\r\n        item: null,\r\n        category: null,\r\n        target: null,\r\n        standard: null,\r\n      })\r\n    },\r\n\r\n    // 合并单元格方法\r\n    objectSpanMethod({ row, rowIndex, columnIndex }) {\r\n      // 第一列相同项合并\r\n      if (columnIndex === 0) {\r\n        return this.spanList.itemList[rowIndex];\r\n      }\r\n      // 评分标准相同合并\r\n      if(columnIndex === 3){\r\n        return this.spanList.standardList[rowIndex];\r\n      }\r\n      // 类别无内容 合并\r\n      if(columnIndex === 1){\r\n        if(!row.category){\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          }\r\n        }\r\n      }\r\n      if(columnIndex === 2){\r\n        if(!row.category){\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style>\r\n.table-striped{\r\n  margin-top: 10px;\r\n  margin-bottom: 10px;\r\n  width: 100%;\r\n  text-align: center;\r\n  border: 1px #888;\r\n  border-collapse: collapse;\r\n}\r\n.table-striped th{\r\n  height: 32px;\r\n  border: 1px solid #888;\r\n  background-color: #dedede;\r\n}\r\n.table-striped td{\r\n  min-height: 32px;\r\n  border: 1px solid #888;\r\n}\r\n.table-input .el-textarea__inner{\r\n  border: 0 !important;\r\n  resize: none !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AAmJA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAC,sBAAA,CAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,IAAA;EACAC,UAAA;IACAC,SAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;MACA;MACAC,QAAA;MACA;MACAC,QAAA;MACA;MACAC,MAAA;QACA;QACAC,WAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACAC,SAAA;MACA;MACAC,QAAA;MACA;MACAC,QAAA;QACAC,QAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;EACA;EAEA;EACAC,KAAA;IACA,mBAAAC,OAAAC,EAAA;MACA;MACA,IAAAA,EAAA,CAAAC,IAAA;QACA,KAAAJ,YAAA;MACA;IACA;EACA;EAEA;EACAK,iBAAA,WAAAA,kBAAAF,EAAA,EAAAG,IAAA,EAAAC,IAAA;IACA;IACA,KAAA5B,WAAA,CAAA6B,MAAA,GAAAL,EAAA,CAAAM,KAAA,CAAAD,MAAA;IACA,KAAAE,iBAAA;IACA,KAAAC,OAAA;IACAJ,IAAA;EACA;EACAK,OAAA;IACA;IACAZ,YAAA,WAAAA,aAAA;MACA;MACA,KAAArB,WAAA,CAAA6B,MAAA,QAAAN,MAAA,CAAAO,KAAA,CAAAD,MAAA;;MAEA;MACA,UAAA7B,WAAA,CAAA6B,MAAA;QACA,KAAAK,QAAA,CAAAC,KAAA;QACA,KAAAC,OAAA,CAAAC,EAAA;QACA;MACA;;MAEA;MACA,KAAAjC,QAAA;MACA,KAAAP,UAAA;MACA,KAAAmB,QAAA;;MAEA;MACA,KAAAe,iBAAA;MACA,KAAAC,OAAA;IACA;IAEAD,iBAAA,WAAAA,kBAAA;MAAA,IAAAO,KAAA;MACA,UAAAtC,WAAA,CAAA6B,MAAA;QACA;MACA;MACA,IAAAE,uBAAA;QAAAQ,EAAA,OAAAvC,WAAA,CAAA6B;MAAA,GAAAW,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAlC,QAAA,GAAAqC,GAAA,CAAA/C,IAAA;MACA,GAAAgD,KAAA,WAAAP,KAAA;QACAQ,OAAA,CAAAR,KAAA,cAAAA,KAAA;QACAG,KAAA,CAAAJ,QAAA,CAAAC,KAAA;MACA;IACA;IACA,sBACAH,OAAA,WAAAA,QAAA;MAAA,IAAAY,MAAA;MACA,UAAA5C,WAAA,CAAA6B,MAAA;QACA,KAAAlC,OAAA;QACA;MACA;MAEA,KAAAA,OAAA;MACA,IAAAkD,qBAAA,OAAA7C,WAAA,EAAAwC,IAAA,WAAAM,QAAA;QACAF,MAAA,CAAAG,cAAA,CAAAD,QAAA,CAAApD,IAAA;QACAkD,MAAA,CAAA/C,UAAA,GAAAiD,QAAA,CAAApD,IAAA;QACAkD,MAAA,CAAAjD,OAAA;MACA,GAAA+C,KAAA,WAAAP,KAAA;QACAQ,OAAA,CAAAR,KAAA,cAAAA,KAAA;QACAS,MAAA,CAAAV,QAAA,CAAAC,KAAA;QACAS,MAAA,CAAAjD,OAAA;MACA;IACA;IAEA;IACAoD,cAAA,WAAAA,eAAArD,IAAA;MACA,IAAAwB,QAAA;MACA,IAAAC,YAAA;MACA,IAAA6B,QAAA;MACA,IAAAC,YAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAxD,IAAA,CAAAyD,MAAA,EAAAD,CAAA;QACA;QACA,IAAAA,CAAA;UACAhC,QAAA,CAAAkC,IAAA;YACAC,OAAA;YACAC,OAAA;UACA;UACAnC,YAAA,CAAAiC,IAAA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;UACA;UACA,IAAA5D,IAAA,CAAAwD,CAAA,MAAAK,IAAA,IAAA7D,IAAA,CAAAwD,CAAA,EAAAK,IAAA;YACArC,QAAA,CAAAkC,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACApC,QAAA,CAAA8B,QAAA,EAAAK,OAAA;UACA;YACAnC,QAAA,CAAAkC,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAN,QAAA,GAAAE,CAAA;UACA;UACA;UACA,IAAAxD,IAAA,CAAAwD,CAAA,MAAAM,QAAA,IAAA9D,IAAA,CAAAwD,CAAA,EAAAM,QAAA;YACArC,YAAA,CAAAiC,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAnC,YAAA,CAAA8B,YAAA,EAAAI,OAAA;UACA;YACAlC,YAAA,CAAAiC,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAL,YAAA,GAAAC,CAAA;UACA;QACA;MACA;MACA,KAAAjC,QAAA,CAAAC,QAAA,GAAAA,QAAA;MACA,KAAAD,QAAA,CAAAE,YAAA,GAAAA,YAAA;IACA;IAEA;IACAsC,MAAA,WAAAA,OAAA;MACA,KAAA1D,IAAA;MACA,KAAA2D,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAxD,IAAA;QACAqC,EAAA;QACAtC,MAAA;QACA0D,IAAA;QACAJ,IAAA;QACAK,QAAA;QACAC,MAAA;QACAL,QAAA;QACAM,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAnC,OAAA;IACA;IACA,aACAoC,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IAEA;IACAE,UAAA,WAAAA,WAAA;MACA,KAAArD,QAAA,QAAAsD,gBAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA5E,UAAA;MACA,KAAAkB,SAAA;MACA,KAAAV,QAAA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IAEAqE,UAAA,WAAAA,WAAA;MACA,KAAArE,QAAA;IACA;IAEA;IACAsE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACAjC,OAAA,CAAAkC,GAAA,MAAA7D,QAAA;MACA,UAAA8D,UAAA;QACA,KAAA5C,QAAA;UACA6C,IAAA;UACAC,OAAA;QACA;QACA;MACA;MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAJ,IAAA;MACA,GAAAvC,IAAA;QACAoC,MAAA,CAAAQ,SAAA;MACA,GAAA1C,KAAA,cAEA;IACA;IAEA;IACAoC,UAAA,WAAAA,WAAA;MACA,SAAA5B,CAAA,MAAAA,CAAA,QAAAlC,QAAA,CAAAmC,MAAA,EAAAD,CAAA;QACA,UAAAlC,QAAA,CAAAkC,CAAA,EAAAK,IAAA;QACA;QACA,UAAAvC,QAAA,CAAAkC,CAAA,EAAAW,MAAA;QACA,UAAA7C,QAAA,CAAAkC,CAAA,EAAAM,QAAA;MACA;MACA;IACA;IAEA;IACA4B,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAA3F,IAAA,QAAA4F,cAAA,MAAAtE,QAAA;MACA,IAAAuE,mBAAA,EAAA7F,IAAA,EAAA8C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA+C,IAAA;UACAH,MAAA,CAAAhF,QAAA;UACAgF,MAAA,CAAArE,QAAA;UACAqE,MAAA,CAAArD,OAAA;UACAqD,MAAA,CAAAnD,QAAA;YACA6C,IAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAM,cAAA,WAAAA,eAAA5F,IAAA;MACA,SAAAwD,CAAA,MAAAA,CAAA,GAAAxD,IAAA,CAAAyD,MAAA,EAAAD,CAAA;QACAxD,IAAA,CAAAwD,CAAA,EAAAS,IAAA,GAAAT,CAAA;QACAxD,IAAA,CAAAwD,CAAA,EAAArB,MAAA,QAAA7B,WAAA,CAAA6B,MAAA;MACA;MACA,OAAAnC,IAAA;IACA;IAEA;IACA4E,gBAAA,WAAAA,iBAAA5E,IAAA;MACA,SAAAwD,CAAA,MAAAA,CAAA,GAAAxD,IAAA,CAAAyD,MAAA,EAAAD,CAAA;QACA,IAAAxD,IAAA,CAAAwD,CAAA,EAAAX,EAAA;UACA7C,IAAA,CAAAwD,CAAA,EAAAX,EAAA;QACA;QACA;QACA,KAAA7C,IAAA,CAAAwD,CAAA,EAAAK,IAAA;UACA7D,IAAA,CAAAwD,CAAA,EAAAK,IAAA,GAAA7D,IAAA,CAAAwD,CAAA,MAAAK,IAAA;QACA;QACA;QACA,KAAA7D,IAAA,CAAAwD,CAAA,EAAAM,QAAA;UACA9D,IAAA,CAAAwD,CAAA,EAAAM,QAAA,GAAA9D,IAAA,CAAAwD,CAAA,MAAAM,QAAA;QACA;QACA;QACA,KAAA9D,IAAA,CAAAwD,CAAA,EAAAU,QAAA,IAAAlE,IAAA,CAAAwD,CAAA,EAAAW,MAAA;UACA;UACAnE,IAAA,CAAAwD,CAAA,EAAAU,QAAA,GAAAlE,IAAA,CAAAwD,CAAA,MAAAU,QAAA;QACA;QACA;QACA,IAAAlE,IAAA,CAAAwD,CAAA,EAAAU,QAAA,KAAAlE,IAAA,CAAAwD,CAAA,EAAAW,MAAA;UACA;UACAnE,IAAA,CAAAwD,CAAA,EAAAW,MAAA,GAAAnE,IAAA,CAAAwD,CAAA,EAAAU,QAAA;UACAlE,IAAA,CAAAwD,CAAA,EAAAU,QAAA;QACA;MACA;MACA,OAAAlE,IAAA;IACA;IAGA+F,wBAAA,WAAAA,yBAAA;MACA,KAAAnF,MAAA,CAAAC,WAAA;IACA;IACAmF,iBAAA,WAAAA,kBAAA5C,QAAA;MACAH,OAAA,CAAAkC,GAAA,CAAA/B,QAAA;MACA,KAAAxC,MAAA,CAAAC,WAAA;MACA,KAAAS,QAAA,QAAAsD,gBAAA,CAAAxB,QAAA,CAAApD,IAAA;MACA,KAAAqB,SAAA;MACA,KAAAV,QAAA;IACA;IAEA;IACAsF,gBAAA,WAAAA,iBAAA;MACA,IAAAC,qBAAA;QAAArD,EAAA;MAAA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAA+C,IAAA;UACA,IAAAK,QAAA,GAAAC,MAAA,CAAAC,QAAA,CAAAC,IAAA;UACA,IAAAH,QAAA;YACApD,GAAA,CAAA/C,IAAA,CAAAiB,GAAA,GAAA8B,GAAA,CAAA/C,IAAA,CAAAiB,GAAA,CAAAsF,OAAA;UACA;UACA,IAAAtF,GAAA,GAAA8B,GAAA,CAAA/C,IAAA,CAAAiB,GAAA;UACAmF,MAAA,CAAA/F,IAAA,CAAAY,GAAA;QACA;MACA;IACA;IAEA;IACAuF,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAAnF,QAAA,CAAAoF,MAAA,CAAAD,KAAA;IACA;IAEA;IACAE,MAAA,WAAAA,OAAA;MACA,KAAArF,QAAA,CAAAoC,IAAA;QACAG,IAAA;QACAK,QAAA;QACAC,MAAA;QACAL,QAAA;MACA;IACA;IAEA;IACA8C,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;QAAAC,WAAA,GAAAH,IAAA,CAAAG,WAAA;MACA;MACA,IAAAA,WAAA;QACA,YAAAzF,QAAA,CAAAC,QAAA,CAAAuF,QAAA;MACA;MACA;MACA,IAAAC,WAAA;QACA,YAAAzF,QAAA,CAAAE,YAAA,CAAAsF,QAAA;MACA;MACA;MACA,IAAAC,WAAA;QACA,KAAAF,GAAA,CAAA5C,QAAA;UACA;YACAP,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA,IAAAoD,WAAA;QACA,KAAAF,GAAA,CAAA5C,QAAA;UACA;YACAP,OAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}