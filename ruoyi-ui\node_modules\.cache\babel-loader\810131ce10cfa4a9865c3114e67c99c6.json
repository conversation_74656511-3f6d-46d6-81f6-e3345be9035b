{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\report.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\report.vue", "mtime": 1756456282523}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9qYXZhX3dvcmtzcGFjZS9uZXdfd29ya3NwYWNlL3hjdGcvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfdG9Db25zdW1hYmxlQXJyYXkyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi9qYXZhX3dvcmtzcGFjZS9uZXdfd29ya3NwYWNlL3hjdGcvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvdG9Db25zdW1hYmxlQXJyYXkuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmpzb24uc3RyaW5naWZ5LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLmluY2x1ZGVzLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiKTsKdmFyIF9pbmZvID0gcmVxdWlyZSgiQC9hcGkvYXNzZXNzL3NlbGYvaW5mbyIpOwp2YXIgX2RlcHQgPSByZXF1aXJlKCJAL2FwaS9hc3Nlc3MvbGF0ZXJhbC9kZXB0Iik7CnZhciBfdXNlciA9IHJlcXVpcmUoIkAvYXBpL2Fzc2Vzcy9zZWxmL3VzZXIiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJTZWxmQXNzZXNzUmVwb3J0IiwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDnu6nmlYjogIPmoLgt6Ieq6K+E5oyH5qCH6YWN572u6KGo5qC85pWw5o2uCiAgICAgIGxpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHVzZXJJZDogbnVsbCwKICAgICAgICB3b3JrTm86IG51bGwsCiAgICAgICAgZGVwdElkOiBudWxsLAogICAgICAgIGFzc2Vzc0RhdGU6IG51bGwKICAgICAgfSwKICAgICAgLy8g6ICD5qC45bm05pyI5paH5pys5pi+56S6CiAgICAgIGFzc2Vzc0RhdGVUZXh0OiBudWxsLAogICAgICAvLyDpg6jpl6jmmL7npLoKICAgICAgZGVwdE5hbWU6IG51bGwsCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7fSwKICAgICAgLy8g55So5oi35L+h5oGvCiAgICAgIHVzZXJJbmZvOiB7fSwKICAgICAgLy8g5ZCI5bm25Y2V5YWD5qC85L+h5oGvCiAgICAgIHNwYW5MaXN0OiB7CiAgICAgICAgaXRlbUxpc3Q6IFtdLAogICAgICAgIHN0YW5kYXJkTGlzdDogW10KICAgICAgfSwKICAgICAgLy8g5piv5ZCm5pi+56S66YeN572u5oyJ6ZKuCiAgICAgIHJlc2V0U2hvdzogZmFsc2UsCiAgICAgIC8vIOaYr+WQpuWPquivuwogICAgICByZWFkT25seTogZmFsc2UsCiAgICAgIC8vIOmDqOmXqOmAiemhuQogICAgICBkZXB0T3B0aW9uczogW10sCiAgICAgIC8vIOiHquivhOS/oeaBr0lkCiAgICAgIGlkOiBudWxsLAogICAgICAvLyDoh6ror4TliIbmlbAKICAgICAgc2VsZlNjb3JlOiAxMDAsCiAgICAgIC8vIOeKtuaAgQogICAgICBzdGF0dXM6ICIwIiwKICAgICAgLy8g6Ieq6K+E5L+h5oGvCiAgICAgIGluZm86IHt9LAogICAgICAvLyDmqKrlkJHooqvogIPor4Tkv6Hmga8KICAgICAgYmVBc3Nlc3NlZExpc3Q6IFtdLAogICAgICAvLyDpgIDlm57nkIbnlLEKICAgICAgcmVqZWN0UmVhc29uOiAiIgogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmluaXRQYWdlRGF0YSgpOwogIH0sCiAgLy8g55uR5ZCs6Lev55Sx5Y+Y5YyW77yM56Gu5L+d5q+P5qyh6L+b5YWl6aG16Z2i6YO96YeN5paw6I635Y+W5pWw5o2uCiAgd2F0Y2g6IHsKICAgICckcm91dGUnOiBmdW5jdGlvbiAkcm91dGUodG8pIHsKICAgICAgLy8g5b2T6Lev55Sx5Y+R55Sf5Y+Y5YyW5pe277yM6YeN5paw5Yid5aeL5YyW6aG16Z2i5pWw5o2uCiAgICAgIGlmICh0by5wYXRoID09PSAnL2Fzc2Vzcy9zZWxmL3VzZXIvcmVwb3J0JykgewogICAgICAgIHRoaXMuaW5pdFBhZ2VEYXRhKCk7CiAgICAgIH0KICAgIH0KICB9LAogIC8vIOi3r+eUseabtOaWsOaXtueahOmSqeWtkAogIGJlZm9yZVJvdXRlVXBkYXRlOiBmdW5jdGlvbiBiZWZvcmVSb3V0ZVVwZGF0ZSh0bywgZnJvbSwgbmV4dCkgewogICAgLy8g5Zyo5b2T5YmN6Lev55Sx5pS55Y+Y77yM5L2G5piv6K+l57uE5Lu26KKr5aSN55So5pe26LCD55SoCiAgICB0aGlzLmluaXRQYWdlRGF0YSgpOwogICAgbmV4dCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLy8g5Yid5aeL5YyW6aG16Z2i5pWw5o2uCiAgICBpbml0UGFnZURhdGE6IGZ1bmN0aW9uIGluaXRQYWdlRGF0YSgpIHsKICAgICAgLy8g5LuO6Lev55Sx5Lit6I635Y+W5Y+C5pWwCiAgICAgIHRoaXMucXVlcnlQYXJhbXMud29ya05vID0gdGhpcy4kcm91dGUucXVlcnkud29ya05vOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmRlcHRJZDsKCiAgICAgIC8vIOmqjOivgeW/heimgeWPguaVsAogICAgICBpZiAoIXRoaXMucXVlcnlQYXJhbXMud29ya05vIHx8ICF0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+e8uuWwkeW/heimgeWPguaVsO+8mueUqOaIt+W3peWPt+aIlumDqOmXqElEJyk7CiAgICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIC8vIOiuvue9rum7mOiupOiAg+aguOW5tOaciAogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGUgPSB0aGlzLmdldERlZmF1bHRBc3Nlc3NEYXRlKCk7CiAgICAgIHRoaXMuYXNzZXNzRGF0ZVRleHQgPSB0aGlzLnF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGUucmVwbGFjZSgiLSIsICIg5bm0ICIpICsgIiDmnIgiOwoKICAgICAgLy8g6YeN572u5pWw5o2uCiAgICAgIHRoaXMubGlzdCA9IFtdOwogICAgICB0aGlzLnVzZXJJbmZvID0ge307CiAgICAgIHRoaXMuZGVwdE9wdGlvbnMgPSBbXTsKICAgICAgdGhpcy5iZUFzc2Vzc2VkTGlzdCA9IFtdOwogICAgICB0aGlzLmlkID0gbnVsbDsKICAgICAgdGhpcy5zZWxmU2NvcmUgPSAxMDA7CiAgICAgIHRoaXMuc3RhdHVzID0gIjAiOwogICAgICB0aGlzLmluZm8gPSB7fTsKICAgICAgdGhpcy5yZWplY3RSZWFzb24gPSAiIjsKICAgICAgdGhpcy5yZWFkT25seSA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0U2hvdyA9IGZhbHNlOwoKICAgICAgLy8g5qC55o2u6Lev55Sx5Y+C5pWw55u05o6l6I635Y+W55So5oi35L+h5oGvCiAgICAgIHRoaXMuZ2V0RGVwdEluZm8oKTsKICAgICAgdGhpcy5nZXRVc2VySW5mb0J5UGFyYW1zKCk7CiAgICB9LAogICAgLy8g6I635Y+W6buY6K6k6ICD5qC45pel5pyfCiAgICBnZXREZWZhdWx0QXNzZXNzRGF0ZTogZnVuY3Rpb24gZ2V0RGVmYXVsdEFzc2Vzc0RhdGUoKSB7CiAgICAgIHZhciBub3cgPSBuZXcgRGF0ZSgpOwogICAgICB2YXIgY3VycmVudERheSA9IG5vdy5nZXREYXRlKCk7CiAgICAgIHZhciB0YXJnZXREYXRlOwogICAgICBpZiAoY3VycmVudERheSA8IDEwKSB7CiAgICAgICAgLy8g5b2T5YmN5pel5pyf5bCP5LqOMTDml6XvvIzpu5jorqTkuLrkuIrkuKrmnIgKICAgICAgICB0YXJnZXREYXRlID0gbmV3IERhdGUobm93LmdldEZ1bGxZZWFyKCksIG5vdy5nZXRNb250aCgpIC0gMSwgMSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5b2T5YmN5pel5pyf5aSn5LqO562J5LqOMTDml6XvvIzpu5jorqTkuLrlvZPmnIgKICAgICAgICB0YXJnZXREYXRlID0gbmV3IERhdGUobm93LmdldEZ1bGxZZWFyKCksIG5vdy5nZXRNb250aCgpLCAxKTsKICAgICAgfQoKICAgICAgLy8g5qC85byP5YyW5Li6IFlZWVktTSDmoLzlvI8KICAgICAgdmFyIHllYXIgPSB0YXJnZXREYXRlLmdldEZ1bGxZZWFyKCk7CiAgICAgIHZhciBtb250aCA9IHRhcmdldERhdGUuZ2V0TW9udGgoKSArIDE7CiAgICAgIHJldHVybiAiIi5jb25jYXQoeWVhciwgIi0iKS5jb25jYXQobW9udGgpOwogICAgfSwKICAgIGdldERlcHRJbmZvOiBmdW5jdGlvbiBnZXREZXB0SW5mbygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgKDAsIF9kZXB0LmdldERlcHQpKHRoaXMucXVlcnlQYXJhbXMuZGVwdElkKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAvLyDorr7nva7pg6jpl6jlkI3np7AKICAgICAgICBfdGhpcy5kZXB0TmFtZSA9IHJlcy5kYXRhLmRlcHROYW1lIHx8ICfmnKrnn6Xpg6jpl6gnOwoKICAgICAgICAvLyDorr7nva7pg6jpl6jpgInpobnvvIjlj6rljIXlkKvlvZPliY3pg6jpl6jvvIkKICAgICAgICBfdGhpcy5kZXB0T3B0aW9ucyA9IFt7CiAgICAgICAgICBkZXB0SWQ6IF90aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCwKICAgICAgICAgIGRlcHROYW1lOiBfdGhpcy5kZXB0TmFtZQogICAgICAgIH1dOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmoLnmja7ot6/nlLHlj4LmlbDnm7TmjqXojrflj5bnlKjmiLfkv6Hmga8KICAgIGdldFVzZXJJbmZvQnlQYXJhbXM6IGZ1bmN0aW9uIGdldFVzZXJJbmZvQnlQYXJhbXMoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICAvLyDmoLnmja7lt6Xlj7flkozpg6jpl6hJROiOt+WPlueUqOaIt+S/oeaBrwogICAgICAoMCwgX3VzZXIuZ2V0QnlXb3JrTm9EZXB0SWQpKHsKICAgICAgICB3b3JrTm86IHRoaXMucXVlcnlQYXJhbXMud29ya05vLAogICAgICAgIGRlcHRJZDogdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+iOt+WPlueUqOaIt+S/oeaBrzonLCByZXMpOwogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDAgJiYgcmVzLmRhdGEpIHsKICAgICAgICAgIF90aGlzMi5xdWVyeVBhcmFtcy51c2VySWQgPSByZXMuZGF0YS5pZDsKICAgICAgICAgIF90aGlzMi51c2VySW5mbyA9IHJlcy5kYXRhOwoKICAgICAgICAgIC8vIOiOt+WPluiAg+aguOaVsOaNruWSjOiiq+iAg+aguOS/oeaBrwogICAgICAgICAgX3RoaXMyLmdldExpc3QoKTsKICAgICAgICAgIF90aGlzMi5nZXRCZUFzc2Vzc2VkTGlzdCgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUqOaIt+S/oeaBr+Wksei0pScpOwogICAgICAgICAgX3RoaXMyLiRyb3V0ZXIuZ28oLTEpOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W55So5oi35L+h5oGv5aSx6LSlOicsIGVycm9yKTsKICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUqOaIt+S/oeaBr+Wksei0pScpOwogICAgICAgIF90aGlzMi4kcm91dGVyLmdvKC0xKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6I635Y+W6KKr6ICD5qC45L+h5oGvCiAgICBnZXRCZUFzc2Vzc2VkTGlzdDogZnVuY3Rpb24gZ2V0QmVBc3Nlc3NlZExpc3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICAoMCwgX2luZm8ubGlzdEJlQXNzZXNzZWQpKHsKICAgICAgICBkZXB0SWQ6IHRoaXMucXVlcnlQYXJhbXMuZGVwdElkLAogICAgICAgIGFzc2Vzc0RhdGU6IHRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICB2YXIgYmVBc3Nlc3NlZExpc3QgPSBbXTsKICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7CiAgICAgICAgICBpZiAocmVzLmRhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgYmVBc3Nlc3NlZExpc3QgPSBbXS5jb25jYXQoKDAsIF90b0NvbnN1bWFibGVBcnJheTIuZGVmYXVsdCkoYmVBc3Nlc3NlZExpc3QpLCAoMCwgX3RvQ29uc3VtYWJsZUFycmF5Mi5kZWZhdWx0KShpdGVtLmhyTGF0ZXJhbEFzc2Vzc0luZm9MaXN0KSk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBfdGhpczMuYmVBc3Nlc3NlZExpc3QgPSBiZUFzc2Vzc2VkTGlzdDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgY29uc29sZS5sb2coYmVBc3Nlc3NlZExpc3QpOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5p+l6K+i57up5pWI6ICD5qC4LeiHquivhOaMh+agh+mFjee9ruWIl+ihqCAqL2dldExpc3Q6IGZ1bmN0aW9uIGdldExpc3QoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAoMCwgX2luZm8uZ2V0SW5mb0J5RGF0ZSkodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBjb25zb2xlLmxvZyhyZXNwb25zZS5kYXRhKTsKICAgICAgICAvLyBjb25zb2xlLmxvZyh0eXBlb2YgcmVzcG9uc2UuZGF0YSk7CiAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsKICAgICAgICAgIC8vIOaMh+agh+mFjee9ruaVsOaNrgogICAgICAgICAgX3RoaXM0LmhhbmRsZVNwYW5MaXN0KHJlc3BvbnNlLmRhdGEpOwogICAgICAgICAgX3RoaXM0Lmxpc3QgPSByZXNwb25zZS5kYXRhLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICBpdGVtLnBlcmZvcm1hbmNlID0gIiI7CiAgICAgICAgICAgIC8vIOagueaNrumhueebruexu+Wei+iuvue9rum7mOiupOWIhuaVsAogICAgICAgICAgICB2YXIgbm9TcGFjZVN0ciA9IGl0ZW0uaXRlbS5yZXBsYWNlKC9ccysvZywgJycpOwogICAgICAgICAgICBpZiAobm9TcGFjZVN0ci5pbmNsdWRlcygi5pyI5bqm6YeN54K55bel5L2cIikpIHsKICAgICAgICAgICAgICBpdGVtLmRlUG9pbnRzID0gNTsgLy8g5pyI5bqm6YeN54K55bel5L2c6buY6K6kNeWIhgogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIGl0ZW0uZGVQb2ludHMgPSAwOyAvLyDlhbbku5bpobnnm67pu5jorqQw5YiGCiAgICAgICAgICAgIH0KICAgICAgICAgICAgcmV0dXJuIGl0ZW07CiAgICAgICAgICB9KTsKICAgICAgICAgIF90aGlzNC5zdGF0dXMgPSAiMCI7CiAgICAgICAgICBfdGhpczQucmVhZE9ubHkgPSBmYWxzZTsKICAgICAgICAgIF90aGlzNC5yZXNldFNob3cgPSBmYWxzZTsKICAgICAgICAgIF90aGlzNC5yZWplY3RSZWFzb24gPSBudWxsOwogICAgICAgICAgX3RoaXM0LmNhbGN1bGF0ZVNlbGZTY29yZSgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDoh6ror4Tkv6Hmga8KICAgICAgICAgIHZhciBpbmZvID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICAgIHZhciBsaXN0ID0gSlNPTi5wYXJzZShpbmZvLmNvbnRlbnQpOwogICAgICAgICAgX3RoaXM0LmhhbmRsZVNwYW5MaXN0KGxpc3QpOwogICAgICAgICAgX3RoaXM0Lmxpc3QgPSBsaXN0OwogICAgICAgICAgX3RoaXM0LmlkID0gaW5mby5pZDsKICAgICAgICAgIF90aGlzNC5zZWxmU2NvcmUgPSBpbmZvLnNlbGZTY29yZTsKICAgICAgICAgIF90aGlzNC5yZWplY3RSZWFzb24gPSBpbmZvLnJlamVjdFJlYXNvbjsKICAgICAgICAgIF90aGlzNC5zdGF0dXMgPSBpbmZvLnN0YXR1czsKICAgICAgICAgIF90aGlzNC5pbmZvID0gaW5mbzsKICAgICAgICAgIGlmIChpbmZvLnN0YXR1cyA9PSAiMCIpIHsKICAgICAgICAgICAgX3RoaXM0LnJlYWRPbmx5ID0gZmFsc2U7CiAgICAgICAgICAgIF90aGlzNC5yZXNldFNob3cgPSB0cnVlOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXM0LnJlYWRPbmx5ID0gdHJ1ZTsKICAgICAgICAgICAgX3RoaXM0LnJlc2V0U2hvdyA9IGZhbHNlOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICBfdGhpczQubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDlpITnkIbliJfooagKICAgIGhhbmRsZVNwYW5MaXN0OiBmdW5jdGlvbiBoYW5kbGVTcGFuTGlzdChkYXRhKSB7CiAgICAgIHZhciBpdGVtTGlzdCA9IFtdOwogICAgICB2YXIgc3RhbmRhcmRMaXN0ID0gW107CiAgICAgIHZhciBpdGVtRmxhZyA9IDA7CiAgICAgIHZhciBzdGFuZGFyZEZsYWcgPSAwOwogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpKyspIHsKICAgICAgICAvLyDnm7jlkIzogIPmoLjpobnjgIHor4TliIbmoIflh4blkIjlubYKICAgICAgICBpZiAoaSA9PSAwKSB7CiAgICAgICAgICBpdGVtTGlzdC5wdXNoKHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgfSk7CiAgICAgICAgICBzdGFuZGFyZExpc3QucHVzaCh7CiAgICAgICAgICAgIHJvd3NwYW46IDEsCiAgICAgICAgICAgIGNvbHNwYW46IDEKICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAvLyDogIPmoLjpobkKICAgICAgICAgIGlmIChkYXRhW2kgLSAxXS5pdGVtID09IGRhdGFbaV0uaXRlbSkgewogICAgICAgICAgICBpdGVtTGlzdC5wdXNoKHsKICAgICAgICAgICAgICByb3dzcGFuOiAwLAogICAgICAgICAgICAgIGNvbHNwYW46IDAKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGl0ZW1MaXN0W2l0ZW1GbGFnXS5yb3dzcGFuICs9IDE7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBpdGVtTGlzdC5wdXNoKHsKICAgICAgICAgICAgICByb3dzcGFuOiAxLAogICAgICAgICAgICAgIGNvbHNwYW46IDEKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGl0ZW1GbGFnID0gaTsKICAgICAgICAgIH0KICAgICAgICAgIC8vIOivhOWIhuagh+WHhgogICAgICAgICAgaWYgKGRhdGFbaSAtIDFdLnN0YW5kYXJkID09IGRhdGFbaV0uc3RhbmRhcmQpIHsKICAgICAgICAgICAgc3RhbmRhcmRMaXN0LnB1c2goewogICAgICAgICAgICAgIHJvd3NwYW46IDAsCiAgICAgICAgICAgICAgY29sc3BhbjogMAogICAgICAgICAgICB9KTsKICAgICAgICAgICAgc3RhbmRhcmRMaXN0W3N0YW5kYXJkRmxhZ10ucm93c3BhbiArPSAxOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgc3RhbmRhcmRMaXN0LnB1c2goewogICAgICAgICAgICAgIHJvd3NwYW46IDEsCiAgICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgICB9KTsKICAgICAgICAgICAgc3RhbmRhcmRGbGFnID0gaTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5zcGFuTGlzdC5pdGVtTGlzdCA9IGl0ZW1MaXN0OwogICAgICB0aGlzLnNwYW5MaXN0LnN0YW5kYXJkTGlzdCA9IHN0YW5kYXJkTGlzdDsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICAvLyDpqozor4Hlv4XopoHlj4LmlbAKICAgICAgaWYgKCF0aGlzLnF1ZXJ5UGFyYW1zLndvcmtObyB8fCAhdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfnvLrlsJHlv4XopoHlj4LmlbDvvJrnlKjmiLflt6Xlj7fmiJbpg6jpl6hJRCcpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLmFzc2Vzc0RhdGVUZXh0ID0gdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlLnJlcGxhY2UoIi0iLCAiIOW5tCAiKSArICIg5pyIIjsKCiAgICAgIC8vIOmHjee9ruebuOWFs+aVsOaNrgogICAgICB0aGlzLmlkID0gbnVsbDsKICAgICAgdGhpcy5pbmZvID0ge307CiAgICAgIHRoaXMuc2VsZlNjb3JlID0gMTAwOwogICAgICB0aGlzLmxpc3QgPSBbXTsKICAgICAgdGhpcy5iZUFzc2Vzc2VkTGlzdCA9IFtdOwogICAgICB0aGlzLnJlamVjdFJlYXNvbiA9ICIiOwogICAgICB0aGlzLnJlYWRPbmx5ID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXRTaG93ID0gZmFsc2U7CgogICAgICAvLyDph43mlrDojrflj5bmlbDmja4KICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIHRoaXMuZ2V0QmVBc3Nlc3NlZExpc3QoKTsKICAgIH0sCiAgICAvLyDkv53lrZgKICAgIHNhdmU6IGZ1bmN0aW9uIHNhdmUoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICBpZiAodGhpcy5saXN0Lmxlbmd0aCA9PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICBtZXNzYWdlOiAn5pyq6YWN572u55u45YWz5L+h5oGv77yM6K+35YWI6YWN572u5oyH5qCHJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICB2YXIgZGF0YSA9IHRoaXMuaGFuZGxlRGF0YSh0aGlzLmxpc3QpOwogICAgICB2YXIgZm9ybSA9IHsKICAgICAgICBpZDogdGhpcy5pZCwKICAgICAgICB3b3JrTm86IHRoaXMudXNlckluZm8ud29ya05vLAogICAgICAgIGFzc2Vzc0RhdGU6IHRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZSwKICAgICAgICBkZXB0SWQ6IHRoaXMucXVlcnlQYXJhbXMuZGVwdElkLAogICAgICAgIGNvbnRlbnQ6IEpTT04uc3RyaW5naWZ5KGRhdGEpLAogICAgICAgIHN0YXR1czogIjAiLAogICAgICAgIHVzZXJJZDogdGhpcy5xdWVyeVBhcmFtcy51c2VySWQsCiAgICAgICAgZGVwdE5hbWU6IHRoaXMuZGVwdE5hbWUsCiAgICAgICAgbmFtZTogdGhpcy51c2VySW5mby5uYW1lLAogICAgICAgIHNlbGZTY29yZTogdGhpcy5zZWxmU2NvcmUsCiAgICAgICAgam9iOiB0aGlzLnVzZXJJbmZvLmpvYiwKICAgICAgICBwb3N0VHlwZTogdGhpcy51c2VySW5mby5wb3N0VHlwZQogICAgICB9OwogICAgICAoMCwgX2luZm8uc2F2ZUluZm8pKGZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsKICAgICAgICAgIF90aGlzNS5nZXRMaXN0KCk7CiAgICAgICAgICBfdGhpczUuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfkv53lrZjmiJDlip8hJwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDkv53lrZjph43nva4KICAgIHJlc2V0SW5mbzogZnVuY3Rpb24gcmVzZXRJbmZvKCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKICAgICAgdGhpcy4kY29uZmlybSgn6YeN572u5ZCO5bCG5riF56m65omA5pyJ5bey5aGr5YaZ55qE5YaF5a6577yM5piv5ZCm56Gu6K6k6YeN572uPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAvLyDliKDpmaTkv53lrZjkv6Hmga8KICAgICAgICAoMCwgX2luZm8uZGVsSW5mbykoewogICAgICAgICAgaWQ6IF90aGlzNi5pZAogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgewogICAgICAgICAgICBfdGhpczYuaWQgPSBudWxsOwogICAgICAgICAgICBfdGhpczYuc2VsZlNjb3JlID0gbnVsbDsKICAgICAgICAgICAgLy8g6I635Y+W6YWN572u5L+h5oGvCiAgICAgICAgICAgIF90aGlzNi5nZXRMaXN0KCk7CiAgICAgICAgICAgIF90aGlzNi4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfph43nva7miJDlip8hJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+mHjee9ruWksei0pTonLCBlcnJvcik7CiAgICAgICAgICBfdGhpczYuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICBtZXNzYWdlOiAn6YeN572u5aSx6LSl77yM6K+36YeN6K+VJwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICAvLyDnlKjmiLflj5bmtojph43nva4KICAgICAgfSk7CiAgICB9LAogICAgLy8g5aSE55CG5o+Q5Lqk5pWw5o2uCiAgICBoYW5kbGVEYXRhOiBmdW5jdGlvbiBoYW5kbGVEYXRhKGRhdGEpIHsKICAgICAgdmFyIHJlc3VsdCA9IFtdOwogICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICB2YXIgZm9ybSA9IHsKICAgICAgICAgIGl0ZW06IGl0ZW0uaXRlbSwKICAgICAgICAgIGNhdGVnb3J5OiBpdGVtLmNhdGVnb3J5LAogICAgICAgICAgdGFyZ2V0OiBpdGVtLnRhcmdldCwKICAgICAgICAgIHN0YW5kYXJkOiBpdGVtLnN0YW5kYXJkLAogICAgICAgICAgcGVyZm9ybWFuY2U6IGl0ZW0ucGVyZm9ybWFuY2UsCiAgICAgICAgICBkZVBvaW50czogaXRlbS5kZVBvaW50cywKICAgICAgICAgIHBvaW50c1JlYXNvbjogaXRlbS5wb2ludHNSZWFzb24KICAgICAgICB9OwogICAgICAgIHJlc3VsdC5wdXNoKGZvcm0pOwogICAgICB9KTsKICAgICAgcmV0dXJuIHJlc3VsdDsKICAgIH0sCiAgICAvKiog5qCH5YeG6YWN572u6Lez6L2sICovaGFuZGxlQ29uZmlnOiBmdW5jdGlvbiBoYW5kbGVDb25maWcoKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwogICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy51c2VySWQpIHsKICAgICAgICAvLyDnm7TmjqXkvb/nlKjlvZPliY3nlKjmiLdJROi3s+i9rAogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICAgIHBhdGg6ICIvYXNzZXNzL3NlbGYvdXNlci9kZXRhaWwiLAogICAgICAgICAgcXVlcnk6IHsKICAgICAgICAgICAgdXNlcklkOiB0aGlzLnF1ZXJ5UGFyYW1zLnVzZXJJZAogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIC8vIOWmguaenOayoeacieeUqOaIt0lE77yM6YCa6L+H5bel5Y+35ZKM6YOo6ZeoSUTojrflj5YKICAgICAgICAoMCwgX3VzZXIuZ2V0QnlXb3JrTm9EZXB0SWQpKHsKICAgICAgICAgIHdvcmtObzogdGhpcy5xdWVyeVBhcmFtcy53b3JrTm8sCiAgICAgICAgICBkZXB0SWQ6IHRoaXMucXVlcnlQYXJhbXMuZGVwdElkCiAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICBjb25zb2xlLmxvZyhyZXMpOwogICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCAmJiByZXMuZGF0YSAmJiByZXMuZGF0YS5pZCkgewogICAgICAgICAgICBfdGhpczcuJHJvdXRlci5wdXNoKHsKICAgICAgICAgICAgICBwYXRoOiAiL2Fzc2Vzcy9zZWxmL3VzZXIvZGV0YWlsIiwKICAgICAgICAgICAgICBxdWVyeTogewogICAgICAgICAgICAgICAgdXNlcklkOiByZXMuZGF0YS5pZAogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczcuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUqOaIt+mFjee9ruS/oeaBr+Wksei0pScpOwogICAgICAgICAgfQogICAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W55So5oi36YWN572u5L+h5oGv5aSx6LSlOicsIGVycm9yKTsKICAgICAgICAgIF90aGlzNy4kbWVzc2FnZS5lcnJvcign6I635Y+W55So5oi36YWN572u5L+h5oGv5aSx6LSlJyk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvLyDlkIjlubbljZXlhYPmoLzmlrnms5UKICAgIG9iamVjdFNwYW5NZXRob2Q6IGZ1bmN0aW9uIG9iamVjdFNwYW5NZXRob2QoX3JlZikgewogICAgICB2YXIgcm93ID0gX3JlZi5yb3csCiAgICAgICAgcm93SW5kZXggPSBfcmVmLnJvd0luZGV4LAogICAgICAgIGNvbHVtbkluZGV4ID0gX3JlZi5jb2x1bW5JbmRleDsKICAgICAgLy8g56ys5LiA5YiX55u45ZCM6aG55ZCI5bm2CiAgICAgIGlmIChjb2x1bW5JbmRleCA9PT0gMCkgewogICAgICAgIHJldHVybiB0aGlzLnNwYW5MaXN0Lml0ZW1MaXN0W3Jvd0luZGV4XTsKICAgICAgfQogICAgICAvLyDor4TliIbmoIflh4bnm7jlkIzlkIjlubYKICAgICAgaWYgKGNvbHVtbkluZGV4ID09PSAzKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuc3Bhbkxpc3Quc3RhbmRhcmRMaXN0W3Jvd0luZGV4XTsKICAgICAgfQogICAgICAvLyDnsbvliKvml6DlhoXlrrkg5ZCI5bm2CiAgICAgIGlmIChjb2x1bW5JbmRleCA9PT0gMSkgewogICAgICAgIGlmICghcm93LmNhdGVnb3J5KSB7CiAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICByb3dzcGFuOiAwLAogICAgICAgICAgICBjb2xzcGFuOiAwCiAgICAgICAgICB9OwogICAgICAgIH0KICAgICAgfQogICAgICBpZiAoY29sdW1uSW5kZXggPT09IDIpIHsKICAgICAgICBpZiAoIXJvdy5jYXRlZ29yeSkgewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMgogICAgICAgICAgfTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvLyDooqvogIPmoLjkuovpobnngrnlh7vkuovku7YKICAgIGhhbmRsZUJlQXNzZXNzZWRDbGljazogZnVuY3Rpb24gaGFuZGxlQmVBc3Nlc3NlZENsaWNrKHJvdywgb3B0aW9uUm93LCBpbmRleCkgewogICAgICBjb25zb2xlLmxvZyhyb3cpOwogICAgICAvLyDlsIbkuovpobnloavlhaXlrozmiJDlrp7nu6nliJfvvIjlvIPnlKjvvIkKICAgICAgLy8gaWYocm93LnBlcmZvcm1hbmNlKXsKICAgICAgLy8gICB0aGlzLiRzZXQocm93LCAncGVyZm9ybWFuY2UnLCByb3cucGVyZm9ybWFuY2UgKyAi77ybIiArIG9wdGlvblJvdy5hc3Nlc3NDb250ZW50KTsKICAgICAgLy8gfWVsc2V7CiAgICAgIC8vICAgdGhpcy4kc2V0KHJvdywgJ3BlcmZvcm1hbmNlJywgb3B0aW9uUm93LmFzc2Vzc0NvbnRlbnQpOwogICAgICAvLyB9CgogICAgICAvLyDlsIbliIbmlbDloavlhaXliqDlh4/liIbliJcKICAgICAgaWYgKHJvdy5kZVBvaW50cykgewogICAgICAgIHRoaXMuJHNldChyb3csICdkZVBvaW50cycsIE51bWJlcihyb3cuZGVQb2ludHMpICsgTnVtYmVyKG9wdGlvblJvdy5kZWR1Y3Rpb25PZlBvaW50KSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kc2V0KHJvdywgJ2RlUG9pbnRzJywgTnVtYmVyKG9wdGlvblJvdy5kZWR1Y3Rpb25PZlBvaW50KSk7CiAgICAgIH0KCiAgICAgIC8vIOWwhuS6i+mhuSvliIbmlbDloavlhaXliqDlh4/liIbnkIbnlLHliJcKICAgICAgdmFyIHJlYXNvbkNvbnRlbnQgPSBvcHRpb25Sb3cuYXNzZXNzQ29udGVudCArICIoIiArIG9wdGlvblJvdy5kZWR1Y3Rpb25PZlBvaW50ICsgIuWIhikiOwogICAgICBpZiAocm93LnBvaW50c1JlYXNvbikgewogICAgICAgIHRoaXMuJHNldChyb3csICdwb2ludHNSZWFzb24nLCByb3cucG9pbnRzUmVhc29uICsgIu+8myIgKyByZWFzb25Db250ZW50KTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRzZXQocm93LCAncG9pbnRzUmVhc29uJywgcmVhc29uQ29udGVudCk7CiAgICAgIH0KICAgICAgdGhpcy4kcmVmc1sicG9wb3ZlciIuY29uY2F0KGluZGV4KV0uc2hvd1BvcHBlciA9IGZhbHNlOwogICAgICAvLyDph43mlrDorqHnrpfoh6ror4TliIbmlbAKICAgICAgdGhpcy5zY29yZUlucHV0KCk7CiAgICB9LAogICAgLy8g5Yqg5YeP5YiG6L6T5YWlCiAgICBzY29yZUlucHV0OiBmdW5jdGlvbiBzY29yZUlucHV0KCkgewogICAgICB2YXIgcm93ID0gYXJndW1lbnRzLmxlbmd0aCA+IDAgJiYgYXJndW1lbnRzWzBdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMF0gOiBudWxsOwogICAgICAvLyDpqozor4HliqDlh4/liIbop4TliJnvvIjku4XlvZPkvKDlhaVyb3flj4LmlbDml7bov5vooYzpqozor4HvvIkKICAgICAgaWYgKHJvdyAmJiByb3cuaXRlbSkgewogICAgICAgIHZhciBub1NwYWNlU3RyID0gcm93Lml0ZW0ucmVwbGFjZSgvXHMrL2csICcnKTsKICAgICAgICB2YXIgdmFsdWUgPSByb3cuZGVQb2ludHM7CiAgICAgICAgaWYgKHZhbHVlICE9PSBudWxsICYmIHZhbHVlICE9PSB1bmRlZmluZWQgJiYgdmFsdWUgIT09ICcnKSB7CiAgICAgICAgICB2YXIgbnVtVmFsdWUgPSBOdW1iZXIodmFsdWUpOwoKICAgICAgICAgIC8vIOaciOW6pumHjeeCueW3peS9nO+8muWPquiDveS4ujHjgIEz5oiWNeWIhgogICAgICAgICAgaWYgKG5vU3BhY2VTdHIuaW5jbHVkZXMoIuaciOW6pumHjeeCueW3peS9nCIpKSB7CiAgICAgICAgICAgIGlmICghWzEsIDMsIDVdLmluY2x1ZGVzKG51bVZhbHVlKSkgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+aciOW6pumHjeeCueW3peS9nOS4uuW+l+WIhuWItu+8jOWPquiDveS4ujHliIbjgIEz5YiG5oiWNeWIhicKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB0aGlzLiRzZXQocm93LCAnZGVQb2ludHMnLCBudWxsKTsKICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICAgIC8vIOWKoOWIhumhue+8mumZpOaciOW6pumHjeeCueW3peS9nOWklu+8jOWPquiDveWhqzAKICAgICAgICAgIGVsc2UgaWYgKG5vU3BhY2VTdHIuaW5jbHVkZXMoIuWKoOWIhumhuSIpKSB7CiAgICAgICAgICAgIGlmIChudW1WYWx1ZSAhPT0gMCkgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+WKoOWIhumhueWPquiDveWhqzDliIYnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgJ2RlUG9pbnRzJywgMCk7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgICAvLyDlhbbku5bnsbvlnovpobnvvJrlj6rog73loasw5oiW6LSf5pWwCiAgICAgICAgICBlbHNlIHsKICAgICAgICAgICAgaWYgKG51bVZhbHVlID4gMCkgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+ivpemhueebruWPquiDveWhqzDliIbmiJbotJ/mlbAnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy4kc2V0KHJvdywgJ2RlUG9pbnRzJywgMCk7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDph43mlrDorqHnrpfoh6ror4TliIbmlbAKICAgICAgdGhpcy5jYWxjdWxhdGVTZWxmU2NvcmUoKTsKICAgIH0sCiAgICAvKiog6K6h566X6Ieq6K+E5YiG5pWwICovY2FsY3VsYXRlU2VsZlNjb3JlOiBmdW5jdGlvbiBjYWxjdWxhdGVTZWxmU2NvcmUoKSB7CiAgICAgIHZhciBwb2ludHMgPSAwOyAvLyDliqDlh4/liIYKCiAgICAgIHRoaXMubGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcG9pbnRzICs9IE51bWJlcihpdGVtLmRlUG9pbnRzKTsKICAgICAgfSk7CiAgICAgIC8vIOiuoeeul+aAu+WIhu+8muWfuuehgOWIhjg1ICsg5Yqg5YeP5YiGCiAgICAgIHRoaXMuc2VsZlNjb3JlID0gODUgKyBwb2ludHM7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_info", "require", "_dept", "_user", "name", "data", "loading", "showSearch", "list", "title", "open", "queryParams", "userId", "workNo", "deptId", "assessDate", "assessDateText", "deptName", "form", "rules", "userInfo", "spanList", "itemList", "standardList", "resetShow", "readOnly", "deptOptions", "id", "selfScore", "status", "info", "beAssessedList", "rejectReason", "created", "initPageData", "watch", "$route", "to", "path", "beforeRouteUpdate", "from", "next", "methods", "query", "$message", "error", "$router", "go", "getDefaultAssessDate", "replace", "getDeptInfo", "getUserInfoByParams", "now", "Date", "currentDay", "getDate", "targetDate", "getFullYear", "getMonth", "year", "month", "concat", "_this", "getDept", "then", "res", "_this2", "getByWorkNoDeptId", "console", "log", "code", "getList", "getBeAssessedList", "catch", "_this3", "listBeAssessed", "length", "for<PERSON>ach", "item", "_toConsumableArray2", "default", "hrLateralAssessInfoList", "_this4", "getInfoByDate", "response", "Array", "isArray", "handleSpanList", "map", "performance", "noSpaceStr", "includes", "dePoints", "calculateSelfScore", "JSON", "parse", "content", "itemFlag", "standardFlag", "i", "push", "rowspan", "colspan", "standard", "handleQuery", "save", "_this5", "type", "message", "handleData", "stringify", "job", "postType", "saveInfo", "resetInfo", "_this6", "$confirm", "confirmButtonText", "cancelButtonText", "delInfo", "result", "category", "target", "pointsReason", "handleConfig", "_this7", "objectSpanMethod", "_ref", "row", "rowIndex", "columnIndex", "handleBeAssessedClick", "optionRow", "index", "$set", "Number", "deductionOfPoint", "reasonContent", "assessContent", "$refs", "showPopper", "scoreInput", "arguments", "undefined", "value", "numValue", "points"], "sources": ["src/views/assess/self/config/user/report.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.assessDate\"\r\n            type=\"month\"\r\n            value-format=\"yyyy-M\"\r\n            format=\"yyyy 年 M 月\"\r\n            placeholder=\"选择考核年月\"\r\n            :clearable=\"false\"\r\n            @change=\"handleQuery\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <el-select v-model=\"queryParams.deptId\" placeholder=\"请选择部门\" disabled>\r\n            <el-option\r\n              v-for=\"item in deptOptions\"\r\n              :key=\"item.deptId\"\r\n              :label=\"item.deptName\"\r\n              :value=\"item.deptId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button type=\"warning\" icon=\"el-icon-s-tools\" size=\"mini\" @click=\"handleConfig\">指标配置</el-button>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row>\r\n        <el-form-item label=\"当前状态\">\r\n          <el-tag v-if=\"status == '0' && rejectReason\" type=\"danger\">退 回</el-tag>\r\n          <el-tag v-if=\"status == '0' && !rejectReason\" type=\"info\">未提交</el-tag>\r\n            <el-tag v-else-if=\"status === '1'\" type=\"warning\">部门评分</el-tag>\r\n            <el-tag v-else-if=\"status === '2'\" type=\"warning\">事业部评分</el-tag>\r\n            <el-tag v-else-if=\"status === '3'\" type=\"warning\">运改/组织部评分</el-tag>\r\n            <el-tag v-else-if=\"status === '4' || status === '5'\" type=\"success\">已完成</el-tag>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row v-if=\"rejectReason\">\r\n        <el-form-item label=\"退回原因\" prop=\"rejectReason\">\r\n            <span class=\"el-icon-warning\" style=\"color: #f56c6c;\"></span>\r\n            {{ rejectReason }}\r\n        </el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n      <h3 style=\"text-align: center;\">月度业绩考核表</h3>\r\n      <el-descriptions class=\"margin-top\" :column=\"3\">\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            姓名\r\n          </template>\r\n          {{ userInfo.name }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            部门\r\n          </template>\r\n          {{ deptName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            考核年月\r\n          </template>\r\n          {{ assessDateText }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-table v-loading=\"loading\" :data=\"list\"\r\n        :span-method=\"objectSpanMethod\" border>\r\n        <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n        <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"140\"/>\r\n        <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"150\" />\r\n        <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n        <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.performance }}</span>\r\n                <div v-else style=\"display: flex\">\r\n                  <!-- <el-button icon=\"el-icon-search\" size=\"small\"></el-button> -->\r\n                  <el-popover\r\n                    placement=\"left\"\r\n                    width=\"636\"\r\n                    trigger=\"click\"\r\n                    :ref=\"'popover' + scope.$index\">\r\n                    <el-table :data=\"beAssessedList\">\r\n                      <el-table-column width=\"150\" property=\"assessDeptName\" label=\"提出考核单位\"></el-table-column>\r\n                      <el-table-column width=\"300\" property=\"assessContent\" label=\"事项\"></el-table-column>\r\n                      <el-table-column width=\"80\" property=\"deductionOfPoint\" label=\"加减分\"></el-table-column>\r\n                      <el-table-column width=\"80\" label=\"操作\">\r\n                        <template slot-scope=\"beAssessed\">\r\n                          <el-button\r\n                            size=\"mini\"\r\n                            type=\"text\"\r\n                            icon=\"el-icon-edit\"\r\n                            @click=\"handleBeAssessedClick(scope.row,beAssessed.row,scope.$index)\"\r\n                          >填入</el-button>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                    <el-button slot=\"reference\" icon=\"el-icon-search\" size=\"small\"></el-button>\r\n                  </el-popover>\r\n                  <el-input class=\"table-input\" type=\"textarea\" autosize v-model=\"scope.row.performance\" placeholder=\"请输入完成实绩\" />\r\n                </div>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"108\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.dePoints }}</span>\r\n                <el-input v-else class=\"table-input\" type=\"number\" autosize v-model=\"scope.row.dePoints\" placeholder=\"请输入加减分\" @input=\"scoreInput(scope.row)\"></el-input>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\"  width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.pointsReason }}</span>\r\n                <el-input v-else class=\"table-input\" type=\"textarea\" autosize v-model=\"scope.row.pointsReason\" placeholder=\"请输入加减分原因\"></el-input>\r\n            </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" style=\"margin-top: 10px;\" label-position=\"left\">\r\n            <el-form-item v-if=\"readOnly\" label=\"自评分数：\" prop=\"deptId\">\r\n              <span>{{ selfScore + \" 分\" }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-else label=\"自评分数：\" prop=\"selfScore\">\r\n              <div style=\"display: flex;width: 180px;\">\r\n                <el-input type=\"number\" autosize v-model=\"selfScore\" placeholder=\"请输入分数\" :readonly=\"readOnly\" />\r\n                <span style=\"margin-left: 8px;\">分</span>\r\n              </div>\r\n            </el-form-item>\r\n          </el-form>\r\n      <div v-if=\"!readOnly && status !== '1'\" style=\"text-align: center;\">\r\n        <el-button v-if=\"resetShow\" plain type=\"info\" @click=\"resetInfo\">重 置 </el-button>\r\n        <el-button type=\"success\" @click=\"save\">保 存</el-button>\r\n      </div>\r\n    </div>\r\n  </template>\r\n\r\n  <script>\r\n  import { getInfoByDate, saveInfo, delInfo, listBeAssessed } from \"@/api/assess/self/info\";\r\n  import { getDept } from \"@/api/assess/lateral/dept\";\r\n  import { getByWorkNoDeptId } from \"@/api/assess/self/user\";\r\n\r\n  export default {\r\n    name: \"SelfAssessReport\",\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 绩效考核-自评指标配置表格数据\r\n        list: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          userId:null,\r\n          workNo: null,\r\n          deptId:null,\r\n          assessDate: null,\r\n        },\r\n        // 考核年月文本显示\r\n        assessDateText:null,\r\n        // 部门显示\r\n        deptName:null,\r\n        // 表单参数\r\n        form: {},\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        // 用户信息\r\n        userInfo:{},\r\n        // 合并单元格信息\r\n        spanList:{\r\n          itemList:[],\r\n          standardList:[]\r\n        },\r\n        // 是否显示重置按钮\r\n        resetShow:false,\r\n        // 是否只读\r\n        readOnly:false,\r\n        // 部门选项\r\n        deptOptions:[],\r\n        // 自评信息Id\r\n        id:null,\r\n        // 自评分数\r\n        selfScore:100,\r\n        // 状态\r\n        status:\"0\",\r\n        // 自评信息\r\n        info:{},\r\n        // 横向被考评信息\r\n        beAssessedList:[],\r\n        // 退回理由\r\n        rejectReason:\"\",\r\n      };\r\n    },\r\n    created() {\r\n      this.initPageData();\r\n    },\r\n\r\n    // 监听路由变化，确保每次进入页面都重新获取数据\r\n    watch: {\r\n      '$route'(to) {\r\n        // 当路由发生变化时，重新初始化页面数据\r\n        if (to.path === '/assess/self/user/report') {\r\n          this.initPageData();\r\n        }\r\n      }\r\n    },\r\n\r\n    // 路由更新时的钩子\r\n    beforeRouteUpdate(to, from, next) {\r\n      // 在当前路由改变，但是该组件被复用时调用\r\n      this.initPageData();\r\n      next();\r\n    },\r\n    methods: {\r\n      // 初始化页面数据\r\n      initPageData() {\r\n        // 从路由中获取参数\r\n        this.queryParams.workNo = this.$route.query.workNo;\r\n        this.queryParams.deptId = this.$route.query.deptId;\r\n\r\n        // 验证必要参数\r\n        if (!this.queryParams.workNo || !this.queryParams.deptId) {\r\n          this.$message.error('缺少必要参数：用户工号或部门ID');\r\n          this.$router.go(-1);\r\n          return;\r\n        }\r\n\r\n        // 设置默认考核年月\r\n        this.queryParams.assessDate = this.getDefaultAssessDate();\r\n        this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n\r\n        // 重置数据\r\n        this.list = [];\r\n        this.userInfo = {};\r\n        this.deptOptions = [];\r\n        this.beAssessedList = [];\r\n        this.id = null;\r\n        this.selfScore = 100;\r\n        this.status = \"0\";\r\n        this.info = {};\r\n        this.rejectReason = \"\";\r\n        this.readOnly = false;\r\n        this.resetShow = false;\r\n\r\n        // 根据路由参数直接获取用户信息\r\n        this.getDeptInfo();\r\n        this.getUserInfoByParams();\r\n      },\r\n\r\n      // 获取默认考核日期\r\n      getDefaultAssessDate() {\r\n        const now = new Date();\r\n        const currentDay = now.getDate();\r\n\r\n        let targetDate;\r\n        if (currentDay < 10) {\r\n          // 当前日期小于10日，默认为上个月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n        } else {\r\n          // 当前日期大于等于10日，默认为当月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n        }\r\n\r\n        // 格式化为 YYYY-M 格式\r\n        const year = targetDate.getFullYear();\r\n        const month = targetDate.getMonth() + 1;\r\n        return `${year}-${month}`;\r\n      },\r\n\r\n        getDeptInfo(){\r\n            getDept(this.queryParams.deptId).then(res => {\r\n                // 设置部门名称\r\n                this.deptName = res.data.deptName || '未知部门';\r\n\r\n                // 设置部门选项（只包含当前部门）\r\n                this.deptOptions = [{\r\n                    deptId: this.queryParams.deptId,\r\n                    deptName: this.deptName\r\n                }];\r\n            });\r\n        },\r\n\r\n      // 根据路由参数直接获取用户信息\r\n      getUserInfoByParams() {\r\n        // 根据工号和部门ID获取用户信息\r\n        getByWorkNoDeptId({\r\n          workNo: this.queryParams.workNo,\r\n          deptId: this.queryParams.deptId\r\n        }).then(res => {\r\n          console.log('获取用户信息:', res);\r\n          if(res.code == 200 && res.data) {\r\n            this.queryParams.userId = res.data.id;\r\n            this.userInfo = res.data;\r\n\r\n\r\n            // 获取考核数据和被考核信息\r\n            this.getList();\r\n            this.getBeAssessedList();\r\n          } else {\r\n            this.$message.error('获取用户信息失败');\r\n            this.$router.go(-1);\r\n          }\r\n        }).catch(error => {\r\n          console.error('获取用户信息失败:', error);\r\n          this.$message.error('获取用户信息失败');\r\n          this.$router.go(-1);\r\n        });\r\n      },\r\n\r\n\r\n      \r\n      // 获取被考核信息\r\n      getBeAssessedList(){\r\n        listBeAssessed({deptId:this.queryParams.deptId,assessDate:this.queryParams.assessDate}).then(res =>{\r\n          let beAssessedList = [];\r\n          if(res.code == 200){\r\n            if(res.data.length > 0){\r\n              res.data.forEach(item => {\r\n                beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]\r\n              })\r\n              this.beAssessedList = beAssessedList;\r\n            }\r\n          }\r\n          console.log(beAssessedList)\r\n        })\r\n      },\r\n      /** 查询绩效考核-自评指标配置列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        getInfoByDate(this.queryParams).then(response => {\r\n          console.log(response.data);\r\n          // console.log(typeof response.data);\r\n          if (Array.isArray(response.data)) {\r\n            // 指标配置数据\r\n            this.handleSpanList(response.data);\r\n            this.list = response.data.map(item => {\r\n              item.performance = \"\";\r\n              // 根据项目类型设置默认分数\r\n              let noSpaceStr = item.item.replace(/\\s+/g, '');\r\n              if(noSpaceStr.includes(\"月度重点工作\")){\r\n                item.dePoints = 5; // 月度重点工作默认5分\r\n              } else {\r\n                item.dePoints = 0; // 其他项目默认0分\r\n              }\r\n              return item;\r\n            });\r\n            this.status = \"0\";\r\n            this.readOnly = false;\r\n            this.resetShow = false;\r\n            this.rejectReason = null;\r\n            this.calculateSelfScore();\r\n          } else {\r\n            // 自评信息\r\n            let info = response.data;\r\n            let list = JSON.parse(info.content);\r\n            this.handleSpanList(list);\r\n            this.list = list;\r\n            this.id = info.id;\r\n            this.selfScore = info.selfScore;\r\n            this.rejectReason = info.rejectReason;\r\n            this.status = info.status;\r\n            this.info = info;\r\n            if(info.status == \"0\"){\r\n              this.readOnly = false;\r\n              this.resetShow = true;\r\n            }else{\r\n              this.readOnly = true;\r\n              this.resetShow = false;\r\n            }\r\n          }\r\n          this.loading = false;\r\n        });\r\n      },\r\n\r\n      // 处理列表\r\n      handleSpanList(data){\r\n        let itemList = [];\r\n        let standardList = [];\r\n        let itemFlag = 0;\r\n        let standardFlag = 0;\r\n        for(let i = 0; i < data.length; i++){\r\n          // 相同考核项、评分标准合并\r\n          if(i == 0){\r\n            itemList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            standardList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n          }else{\r\n            // 考核项\r\n            if(data[i - 1].item == data[i].item){\r\n              itemList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              itemList[itemFlag].rowspan += 1;\r\n            }else{\r\n              itemList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              itemFlag = i;\r\n            }\r\n            // 评分标准\r\n            if(data[i - 1].standard == data[i].standard){\r\n              standardList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              standardList[standardFlag].rowspan += 1;\r\n            }else{\r\n              standardList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              standardFlag = i;\r\n            }\r\n          }\r\n        }\r\n        this.spanList.itemList = itemList;\r\n        this.spanList.standardList = standardList;\r\n      },\r\n\r\n\r\n\r\n\r\n\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        // 验证必要参数\r\n        if (!this.queryParams.workNo || !this.queryParams.deptId) {\r\n          this.$message.error('缺少必要参数：用户工号或部门ID');\r\n          return;\r\n        }\r\n\r\n        this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n\r\n        // 重置相关数据\r\n        this.id = null;\r\n        this.info = {};\r\n        this.selfScore = 100;\r\n        this.list = [];\r\n        this.beAssessedList = [];\r\n        this.rejectReason = \"\";\r\n        this.readOnly = false;\r\n        this.resetShow = false;\r\n\r\n        // 重新获取数据\r\n        this.getList();\r\n        this.getBeAssessedList();\r\n      },\r\n\r\n      // 保存\r\n      save(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"0\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType\r\n        }\r\n        saveInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.$message({\r\n              type: 'success',\r\n              message: '保存成功!'\r\n            });\r\n          }\r\n        })\r\n      },\r\n\r\n\r\n\r\n      // 保存重置\r\n      resetInfo(){\r\n        this.$confirm('重置后将清空所有已填写的内容，是否确认重置?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          // 删除保存信息\r\n          delInfo({id:this.id}).then(res => {\r\n            if(res.code == 200){\r\n              this.id = null;\r\n              this.selfScore = null;\r\n              // 获取配置信息\r\n              this.getList();\r\n              this.$message({\r\n                type: 'success',\r\n                message: '重置成功!'\r\n              });\r\n            }\r\n          }).catch(error => {\r\n            console.error('重置失败:', error);\r\n            this.$message({\r\n              type: 'error',\r\n              message: '重置失败，请重试'\r\n            });\r\n          });\r\n        }).catch(() => {\r\n          // 用户取消重置\r\n        });\r\n      },\r\n\r\n      // 处理提交数据\r\n      handleData(data){\r\n        let result = []\r\n        data.forEach(item => {\r\n          let form = {\r\n            item: item.item,\r\n            category: item.category,\r\n            target: item.target,\r\n            standard: item.standard,\r\n            performance: item.performance,\r\n            dePoints: item.dePoints,\r\n            pointsReason:item.pointsReason\r\n          };\r\n          result.push(form);\r\n        })\r\n        return result\r\n      },\r\n\r\n      /** 标准配置跳转 */\r\n      handleConfig(){\r\n        if (this.queryParams.userId) {\r\n          // 直接使用当前用户ID跳转\r\n          this.$router.push({\r\n            path:\"/assess/self/user/detail\",\r\n            query:{\r\n              userId: this.queryParams.userId\r\n            }\r\n          });\r\n        } else {\r\n          // 如果没有用户ID，通过工号和部门ID获取\r\n          getByWorkNoDeptId({\r\n            workNo: this.queryParams.workNo,\r\n            deptId: this.queryParams.deptId\r\n          }).then(res => {\r\n            console.log(res)\r\n            if(res.code == 200 && res.data && res.data.id){\r\n              this.$router.push({\r\n                path:\"/assess/self/user/detail\",\r\n                query:{\r\n                  userId: res.data.id\r\n                }\r\n              });\r\n            } else {\r\n              this.$message.error('获取用户配置信息失败');\r\n            }\r\n          }).catch(error => {\r\n            console.error('获取用户配置信息失败:', error);\r\n            this.$message.error('获取用户配置信息失败');\r\n          });\r\n        }\r\n      },\r\n\r\n\r\n      // 合并单元格方法\r\n      objectSpanMethod({ row, rowIndex, columnIndex }) {\r\n        // 第一列相同项合并\r\n        if (columnIndex === 0) {\r\n          return this.spanList.itemList[rowIndex];\r\n        }\r\n        // 评分标准相同合并\r\n        if(columnIndex === 3){\r\n          return this.spanList.standardList[rowIndex];\r\n        }\r\n        // 类别无内容 合并\r\n        if(columnIndex === 1){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 0,\r\n              colspan: 0\r\n            }\r\n          }\r\n        }\r\n        if(columnIndex === 2){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 1,\r\n              colspan: 2\r\n            }\r\n          }\r\n        }\r\n      },\r\n\r\n      // 被考核事项点击事件\r\n      handleBeAssessedClick(row,optionRow,index){\r\n        console.log(row)\r\n        // 将事项填入完成实绩列（弃用）\r\n        // if(row.performance){\r\n        //   this.$set(row, 'performance', row.performance + \"；\" + optionRow.assessContent);\r\n        // }else{\r\n        //   this.$set(row, 'performance', optionRow.assessContent);\r\n        // }\r\n        \r\n        // 将分数填入加减分列\r\n        if(row.dePoints){\r\n          this.$set(row, 'dePoints', Number(row.dePoints) + Number(optionRow.deductionOfPoint));\r\n        }else{\r\n          this.$set(row, 'dePoints', Number(optionRow.deductionOfPoint));\r\n        }\r\n        \r\n        // 将事项+分数填入加减分理由列\r\n        let reasonContent = optionRow.assessContent + \"(\" + optionRow.deductionOfPoint + \"分)\";\r\n        if(row.pointsReason){\r\n          this.$set(row, 'pointsReason', row.pointsReason + \"；\" + reasonContent);\r\n        }else{\r\n          this.$set(row, 'pointsReason', reasonContent);\r\n        }\r\n        \r\n        this.$refs[`popover${index}`].showPopper = false;\r\n        // 重新计算自评分数\r\n        this.scoreInput();\r\n      },\r\n\r\n      // 加减分输入\r\n      scoreInput(row = null){\r\n        // 验证加减分规则（仅当传入row参数时进行验证）\r\n        if (row && row.item) {\r\n          let noSpaceStr = row.item.replace(/\\s+/g, '');\r\n          let value = row.dePoints;\r\n\r\n          if (value !== null && value !== undefined && value !== '') {\r\n            let numValue = Number(value);\r\n\r\n            // 月度重点工作：只能为1、3或5分\r\n            if (noSpaceStr.includes(\"月度重点工作\")) {\r\n              if (![1, 3, 5].includes(numValue)) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '月度重点工作为得分制，只能为1分、3分或5分'\r\n                });\r\n                this.$set(row, 'dePoints', null);\r\n                return;\r\n              }\r\n            }\r\n            // 加分项：除月度重点工作外，只能填0\r\n            else if (noSpaceStr.includes(\"加分项\")) {\r\n              if (numValue !== 0) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '加分项只能填0分'\r\n                });\r\n                this.$set(row, 'dePoints', 0);\r\n                return;\r\n              }\r\n            }\r\n            // 其他类型项：只能填0或负数\r\n            else {\r\n              if (numValue > 0) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '该项目只能填0分或负数'\r\n                });\r\n                this.$set(row, 'dePoints', 0);\r\n                return;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 重新计算自评分数\r\n        this.calculateSelfScore();\r\n      },\r\n\r\n      /** 计算自评分数 */\r\n      calculateSelfScore() {\r\n        let points = 0;   // 加减分\r\n\r\n        this.list.forEach(item => {\r\n              points += Number(item.dePoints);\r\n        });\r\n        // 计算总分：基础分85 + 加减分\r\n        this.selfScore = 85 + points;\r\n      },\r\n\r\n\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .table-striped{\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n    text-align: center;\r\n    border: 1px #888;\r\n    border-collapse: collapse;\r\n  }\r\n  .table-striped th{\r\n    height: 32px;\r\n    border: 1px solid #888;\r\n    background-color: #dedede;\r\n  }\r\n  .table-striped td{\r\n    min-height: 32px;\r\n    border: 1px solid #888;\r\n  }\r\n  .table-input .el-textarea__inner{\r\n    border: 0 !important;\r\n    resize: none !important;\r\n  }\r\n  .table-input .el-input__inner{\r\n    border: 0 !important;\r\n  }\r\n  /* .myUpload .el-upload--picture-card{\r\n    display:none !important; \r\n  } */\r\n  </style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA0IA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACA;MACAC,cAAA;MACA;MACAC,QAAA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;MACA;MACAC,QAAA;MACA;MACAC,QAAA;QACAC,QAAA;QACAC,YAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;MACA;MACAC,EAAA;MACA;MACAC,SAAA;MACA;MACAC,MAAA;MACA;MACAC,IAAA;MACA;MACAC,cAAA;MACA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;EACA;EAEA;EACAC,KAAA;IACA,mBAAAC,OAAAC,EAAA;MACA;MACA,IAAAA,EAAA,CAAAC,IAAA;QACA,KAAAJ,YAAA;MACA;IACA;EACA;EAEA;EACAK,iBAAA,WAAAA,kBAAAF,EAAA,EAAAG,IAAA,EAAAC,IAAA;IACA;IACA,KAAAP,YAAA;IACAO,IAAA;EACA;EACAC,OAAA;IACA;IACAR,YAAA,WAAAA,aAAA;MACA;MACA,KAAAvB,WAAA,CAAAE,MAAA,QAAAuB,MAAA,CAAAO,KAAA,CAAA9B,MAAA;MACA,KAAAF,WAAA,CAAAG,MAAA,QAAAsB,MAAA,CAAAO,KAAA,CAAA7B,MAAA;;MAEA;MACA,UAAAH,WAAA,CAAAE,MAAA,UAAAF,WAAA,CAAAG,MAAA;QACA,KAAA8B,QAAA,CAAAC,KAAA;QACA,KAAAC,OAAA,CAAAC,EAAA;QACA;MACA;;MAEA;MACA,KAAApC,WAAA,CAAAI,UAAA,QAAAiC,oBAAA;MACA,KAAAhC,cAAA,QAAAL,WAAA,CAAAI,UAAA,CAAAkC,OAAA;;MAEA;MACA,KAAAzC,IAAA;MACA,KAAAY,QAAA;MACA,KAAAM,WAAA;MACA,KAAAK,cAAA;MACA,KAAAJ,EAAA;MACA,KAAAC,SAAA;MACA,KAAAC,MAAA;MACA,KAAAC,IAAA;MACA,KAAAE,YAAA;MACA,KAAAP,QAAA;MACA,KAAAD,SAAA;;MAEA;MACA,KAAA0B,WAAA;MACA,KAAAC,mBAAA;IACA;IAEA;IACAH,oBAAA,WAAAA,qBAAA;MACA,IAAAI,GAAA,OAAAC,IAAA;MACA,IAAAC,UAAA,GAAAF,GAAA,CAAAG,OAAA;MAEA,IAAAC,UAAA;MACA,IAAAF,UAAA;QACA;QACAE,UAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAM,QAAA;MACA;QACA;QACAF,UAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAM,QAAA;MACA;;MAEA;MACA,IAAAC,IAAA,GAAAH,UAAA,CAAAC,WAAA;MACA,IAAAG,KAAA,GAAAJ,UAAA,CAAAE,QAAA;MACA,UAAAG,MAAA,CAAAF,IAAA,OAAAE,MAAA,CAAAD,KAAA;IACA;IAEAV,WAAA,WAAAA,YAAA;MAAA,IAAAY,KAAA;MACA,IAAAC,aAAA,OAAApD,WAAA,CAAAG,MAAA,EAAAkD,IAAA,WAAAC,GAAA;QACA;QACAH,KAAA,CAAA7C,QAAA,GAAAgD,GAAA,CAAA5D,IAAA,CAAAY,QAAA;;QAEA;QACA6C,KAAA,CAAApC,WAAA;UACAZ,MAAA,EAAAgD,KAAA,CAAAnD,WAAA,CAAAG,MAAA;UACAG,QAAA,EAAA6C,KAAA,CAAA7C;QACA;MACA;IACA;IAEA;IACAkC,mBAAA,WAAAA,oBAAA;MAAA,IAAAe,MAAA;MACA;MACA,IAAAC,uBAAA;QACAtD,MAAA,OAAAF,WAAA,CAAAE,MAAA;QACAC,MAAA,OAAAH,WAAA,CAAAG;MACA,GAAAkD,IAAA,WAAAC,GAAA;QACAG,OAAA,CAAAC,GAAA,YAAAJ,GAAA;QACA,IAAAA,GAAA,CAAAK,IAAA,WAAAL,GAAA,CAAA5D,IAAA;UACA6D,MAAA,CAAAvD,WAAA,CAAAC,MAAA,GAAAqD,GAAA,CAAA5D,IAAA,CAAAsB,EAAA;UACAuC,MAAA,CAAA9C,QAAA,GAAA6C,GAAA,CAAA5D,IAAA;;UAGA;UACA6D,MAAA,CAAAK,OAAA;UACAL,MAAA,CAAAM,iBAAA;QACA;UACAN,MAAA,CAAAtB,QAAA,CAAAC,KAAA;UACAqB,MAAA,CAAApB,OAAA,CAAAC,EAAA;QACA;MACA,GAAA0B,KAAA,WAAA5B,KAAA;QACAuB,OAAA,CAAAvB,KAAA,cAAAA,KAAA;QACAqB,MAAA,CAAAtB,QAAA,CAAAC,KAAA;QACAqB,MAAA,CAAApB,OAAA,CAAAC,EAAA;MACA;IACA;IAIA;IACAyB,iBAAA,WAAAA,kBAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,oBAAA;QAAA7D,MAAA,OAAAH,WAAA,CAAAG,MAAA;QAAAC,UAAA,OAAAJ,WAAA,CAAAI;MAAA,GAAAiD,IAAA,WAAAC,GAAA;QACA,IAAAlC,cAAA;QACA,IAAAkC,GAAA,CAAAK,IAAA;UACA,IAAAL,GAAA,CAAA5D,IAAA,CAAAuE,MAAA;YACAX,GAAA,CAAA5D,IAAA,CAAAwE,OAAA,WAAAC,IAAA;cACA/C,cAAA,MAAA8B,MAAA,KAAAkB,mBAAA,CAAAC,OAAA,EAAAjD,cAAA,OAAAgD,mBAAA,CAAAC,OAAA,EAAAF,IAAA,CAAAG,uBAAA;YACA;YACAP,MAAA,CAAA3C,cAAA,GAAAA,cAAA;UACA;QACA;QACAqC,OAAA,CAAAC,GAAA,CAAAtC,cAAA;MACA;IACA;IACA,sBACAwC,OAAA,WAAAA,QAAA;MAAA,IAAAW,MAAA;MACA,KAAA5E,OAAA;MACA,IAAA6E,mBAAA,OAAAxE,WAAA,EAAAqD,IAAA,WAAAoB,QAAA;QACAhB,OAAA,CAAAC,GAAA,CAAAe,QAAA,CAAA/E,IAAA;QACA;QACA,IAAAgF,KAAA,CAAAC,OAAA,CAAAF,QAAA,CAAA/E,IAAA;UACA;UACA6E,MAAA,CAAAK,cAAA,CAAAH,QAAA,CAAA/E,IAAA;UACA6E,MAAA,CAAA1E,IAAA,GAAA4E,QAAA,CAAA/E,IAAA,CAAAmF,GAAA,WAAAV,IAAA;YACAA,IAAA,CAAAW,WAAA;YACA;YACA,IAAAC,UAAA,GAAAZ,IAAA,CAAAA,IAAA,CAAA7B,OAAA;YACA,IAAAyC,UAAA,CAAAC,QAAA;cACAb,IAAA,CAAAc,QAAA;YACA;cACAd,IAAA,CAAAc,QAAA;YACA;YACA,OAAAd,IAAA;UACA;UACAI,MAAA,CAAArD,MAAA;UACAqD,MAAA,CAAAzD,QAAA;UACAyD,MAAA,CAAA1D,SAAA;UACA0D,MAAA,CAAAlD,YAAA;UACAkD,MAAA,CAAAW,kBAAA;QACA;UACA;UACA,IAAA/D,IAAA,GAAAsD,QAAA,CAAA/E,IAAA;UACA,IAAAG,IAAA,GAAAsF,IAAA,CAAAC,KAAA,CAAAjE,IAAA,CAAAkE,OAAA;UACAd,MAAA,CAAAK,cAAA,CAAA/E,IAAA;UACA0E,MAAA,CAAA1E,IAAA,GAAAA,IAAA;UACA0E,MAAA,CAAAvD,EAAA,GAAAG,IAAA,CAAAH,EAAA;UACAuD,MAAA,CAAAtD,SAAA,GAAAE,IAAA,CAAAF,SAAA;UACAsD,MAAA,CAAAlD,YAAA,GAAAF,IAAA,CAAAE,YAAA;UACAkD,MAAA,CAAArD,MAAA,GAAAC,IAAA,CAAAD,MAAA;UACAqD,MAAA,CAAApD,IAAA,GAAAA,IAAA;UACA,IAAAA,IAAA,CAAAD,MAAA;YACAqD,MAAA,CAAAzD,QAAA;YACAyD,MAAA,CAAA1D,SAAA;UACA;YACA0D,MAAA,CAAAzD,QAAA;YACAyD,MAAA,CAAA1D,SAAA;UACA;QACA;QACA0D,MAAA,CAAA5E,OAAA;MACA;IACA;IAEA;IACAiF,cAAA,WAAAA,eAAAlF,IAAA;MACA,IAAAiB,QAAA;MACA,IAAAC,YAAA;MACA,IAAA0E,QAAA;MACA,IAAAC,YAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAA9F,IAAA,CAAAuE,MAAA,EAAAuB,CAAA;QACA;QACA,IAAAA,CAAA;UACA7E,QAAA,CAAA8E,IAAA;YACAC,OAAA;YACAC,OAAA;UACA;UACA/E,YAAA,CAAA6E,IAAA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;UACA;UACA,IAAAjG,IAAA,CAAA8F,CAAA,MAAArB,IAAA,IAAAzE,IAAA,CAAA8F,CAAA,EAAArB,IAAA;YACAxD,QAAA,CAAA8E,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAhF,QAAA,CAAA2E,QAAA,EAAAI,OAAA;UACA;YACA/E,QAAA,CAAA8E,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAL,QAAA,GAAAE,CAAA;UACA;UACA;UACA,IAAA9F,IAAA,CAAA8F,CAAA,MAAAI,QAAA,IAAAlG,IAAA,CAAA8F,CAAA,EAAAI,QAAA;YACAhF,YAAA,CAAA6E,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACA/E,YAAA,CAAA2E,YAAA,EAAAG,OAAA;UACA;YACA9E,YAAA,CAAA6E,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAJ,YAAA,GAAAC,CAAA;UACA;QACA;MACA;MACA,KAAA9E,QAAA,CAAAC,QAAA,GAAAA,QAAA;MACA,KAAAD,QAAA,CAAAE,YAAA,GAAAA,YAAA;IACA;IAMA,aACAiF,WAAA,WAAAA,YAAA;MACA;MACA,UAAA7F,WAAA,CAAAE,MAAA,UAAAF,WAAA,CAAAG,MAAA;QACA,KAAA8B,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,KAAA7B,cAAA,QAAAL,WAAA,CAAAI,UAAA,CAAAkC,OAAA;;MAEA;MACA,KAAAtB,EAAA;MACA,KAAAG,IAAA;MACA,KAAAF,SAAA;MACA,KAAApB,IAAA;MACA,KAAAuB,cAAA;MACA,KAAAC,YAAA;MACA,KAAAP,QAAA;MACA,KAAAD,SAAA;;MAEA;MACA,KAAA+C,OAAA;MACA,KAAAC,iBAAA;IACA;IAEA;IACAiC,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MACA,SAAAlG,IAAA,CAAAoE,MAAA;QACA,KAAAhC,QAAA;UACA+D,IAAA;UACAC,OAAA;QACA;QACA;MACA;MACA,IAAAvG,IAAA,QAAAwG,UAAA,MAAArG,IAAA;MACA,IAAAU,IAAA;QACAS,EAAA,OAAAA,EAAA;QACAd,MAAA,OAAAO,QAAA,CAAAP,MAAA;QACAE,UAAA,OAAAJ,WAAA,CAAAI,UAAA;QACAD,MAAA,OAAAH,WAAA,CAAAG,MAAA;QACAkF,OAAA,EAAAF,IAAA,CAAAgB,SAAA,CAAAzG,IAAA;QACAwB,MAAA;QACAjB,MAAA,OAAAD,WAAA,CAAAC,MAAA;QACAK,QAAA,OAAAA,QAAA;QACAb,IAAA,OAAAgB,QAAA,CAAAhB,IAAA;QACAwB,SAAA,OAAAA,SAAA;QACAmF,GAAA,OAAA3F,QAAA,CAAA2F,GAAA;QACAC,QAAA,OAAA5F,QAAA,CAAA4F;MACA;MACA,IAAAC,cAAA,EAAA/F,IAAA,EAAA8C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAK,IAAA;UACAoC,MAAA,CAAAnC,OAAA;UACAmC,MAAA,CAAA9D,QAAA;YACA+D,IAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;IAIA;IACAM,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAX,IAAA;MACA,GAAA3C,IAAA;QACA;QACA,IAAAuD,aAAA;UAAA5F,EAAA,EAAAwF,MAAA,CAAAxF;QAAA,GAAAqC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAK,IAAA;YACA6C,MAAA,CAAAxF,EAAA;YACAwF,MAAA,CAAAvF,SAAA;YACA;YACAuF,MAAA,CAAA5C,OAAA;YACA4C,MAAA,CAAAvE,QAAA;cACA+D,IAAA;cACAC,OAAA;YACA;UACA;QACA,GAAAnC,KAAA,WAAA5B,KAAA;UACAuB,OAAA,CAAAvB,KAAA,UAAAA,KAAA;UACAsE,MAAA,CAAAvE,QAAA;YACA+D,IAAA;YACAC,OAAA;UACA;QACA;MACA,GAAAnC,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAoC,UAAA,WAAAA,WAAAxG,IAAA;MACA,IAAAmH,MAAA;MACAnH,IAAA,CAAAwE,OAAA,WAAAC,IAAA;QACA,IAAA5D,IAAA;UACA4D,IAAA,EAAAA,IAAA,CAAAA,IAAA;UACA2C,QAAA,EAAA3C,IAAA,CAAA2C,QAAA;UACAC,MAAA,EAAA5C,IAAA,CAAA4C,MAAA;UACAnB,QAAA,EAAAzB,IAAA,CAAAyB,QAAA;UACAd,WAAA,EAAAX,IAAA,CAAAW,WAAA;UACAG,QAAA,EAAAd,IAAA,CAAAc,QAAA;UACA+B,YAAA,EAAA7C,IAAA,CAAA6C;QACA;QACAH,MAAA,CAAApB,IAAA,CAAAlF,IAAA;MACA;MACA,OAAAsG,MAAA;IACA;IAEA,aACAI,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAAlH,WAAA,CAAAC,MAAA;QACA;QACA,KAAAkC,OAAA,CAAAsD,IAAA;UACA9D,IAAA;UACAK,KAAA;YACA/B,MAAA,OAAAD,WAAA,CAAAC;UACA;QACA;MACA;QACA;QACA,IAAAuD,uBAAA;UACAtD,MAAA,OAAAF,WAAA,CAAAE,MAAA;UACAC,MAAA,OAAAH,WAAA,CAAAG;QACA,GAAAkD,IAAA,WAAAC,GAAA;UACAG,OAAA,CAAAC,GAAA,CAAAJ,GAAA;UACA,IAAAA,GAAA,CAAAK,IAAA,WAAAL,GAAA,CAAA5D,IAAA,IAAA4D,GAAA,CAAA5D,IAAA,CAAAsB,EAAA;YACAkG,MAAA,CAAA/E,OAAA,CAAAsD,IAAA;cACA9D,IAAA;cACAK,KAAA;gBACA/B,MAAA,EAAAqD,GAAA,CAAA5D,IAAA,CAAAsB;cACA;YACA;UACA;YACAkG,MAAA,CAAAjF,QAAA,CAAAC,KAAA;UACA;QACA,GAAA4B,KAAA,WAAA5B,KAAA;UACAuB,OAAA,CAAAvB,KAAA,gBAAAA,KAAA;UACAgF,MAAA,CAAAjF,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IAGA;IACAiF,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;QAAAC,WAAA,GAAAH,IAAA,CAAAG,WAAA;MACA;MACA,IAAAA,WAAA;QACA,YAAA7G,QAAA,CAAAC,QAAA,CAAA2G,QAAA;MACA;MACA;MACA,IAAAC,WAAA;QACA,YAAA7G,QAAA,CAAAE,YAAA,CAAA0G,QAAA;MACA;MACA;MACA,IAAAC,WAAA;QACA,KAAAF,GAAA,CAAAP,QAAA;UACA;YACApB,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA,IAAA4B,WAAA;QACA,KAAAF,GAAA,CAAAP,QAAA;UACA;YACApB,OAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;IAEA;IACA6B,qBAAA,WAAAA,sBAAAH,GAAA,EAAAI,SAAA,EAAAC,KAAA;MACAjE,OAAA,CAAAC,GAAA,CAAA2D,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,IAAAA,GAAA,CAAApC,QAAA;QACA,KAAA0C,IAAA,CAAAN,GAAA,cAAAO,MAAA,CAAAP,GAAA,CAAApC,QAAA,IAAA2C,MAAA,CAAAH,SAAA,CAAAI,gBAAA;MACA;QACA,KAAAF,IAAA,CAAAN,GAAA,cAAAO,MAAA,CAAAH,SAAA,CAAAI,gBAAA;MACA;;MAEA;MACA,IAAAC,aAAA,GAAAL,SAAA,CAAAM,aAAA,SAAAN,SAAA,CAAAI,gBAAA;MACA,IAAAR,GAAA,CAAAL,YAAA;QACA,KAAAW,IAAA,CAAAN,GAAA,kBAAAA,GAAA,CAAAL,YAAA,SAAAc,aAAA;MACA;QACA,KAAAH,IAAA,CAAAN,GAAA,kBAAAS,aAAA;MACA;MAEA,KAAAE,KAAA,WAAA9E,MAAA,CAAAwE,KAAA,GAAAO,UAAA;MACA;MACA,KAAAC,UAAA;IACA;IAEA;IACAA,UAAA,WAAAA,WAAA;MAAA,IAAAb,GAAA,GAAAc,SAAA,CAAAlE,MAAA,QAAAkE,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA;MACA,IAAAd,GAAA,IAAAA,GAAA,CAAAlD,IAAA;QACA,IAAAY,UAAA,GAAAsC,GAAA,CAAAlD,IAAA,CAAA7B,OAAA;QACA,IAAA+F,KAAA,GAAAhB,GAAA,CAAApC,QAAA;QAEA,IAAAoD,KAAA,aAAAA,KAAA,KAAAD,SAAA,IAAAC,KAAA;UACA,IAAAC,QAAA,GAAAV,MAAA,CAAAS,KAAA;;UAEA;UACA,IAAAtD,UAAA,CAAAC,QAAA;YACA,eAAAA,QAAA,CAAAsD,QAAA;cACA,KAAArG,QAAA;gBACA+D,IAAA;gBACAC,OAAA;cACA;cACA,KAAA0B,IAAA,CAAAN,GAAA;cACA;YACA;UACA;UACA;UAAA,KACA,IAAAtC,UAAA,CAAAC,QAAA;YACA,IAAAsD,QAAA;cACA,KAAArG,QAAA;gBACA+D,IAAA;gBACAC,OAAA;cACA;cACA,KAAA0B,IAAA,CAAAN,GAAA;cACA;YACA;UACA;UACA;UAAA,KACA;YACA,IAAAiB,QAAA;cACA,KAAArG,QAAA;gBACA+D,IAAA;gBACAC,OAAA;cACA;cACA,KAAA0B,IAAA,CAAAN,GAAA;cACA;YACA;UACA;QACA;MACA;;MAEA;MACA,KAAAnC,kBAAA;IACA;IAEA,aACAA,kBAAA,WAAAA,mBAAA;MACA,IAAAqD,MAAA;;MAEA,KAAA1I,IAAA,CAAAqE,OAAA,WAAAC,IAAA;QACAoE,MAAA,IAAAX,MAAA,CAAAzD,IAAA,CAAAc,QAAA;MACA;MACA;MACA,KAAAhE,SAAA,QAAAsH,MAAA;IACA;EAGA;AACA", "ignoreList": []}]}