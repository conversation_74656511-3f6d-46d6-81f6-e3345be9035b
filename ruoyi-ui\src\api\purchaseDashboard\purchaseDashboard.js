import request from '@/utils/request'

// 获取仪表板数据
export function showData(params) {
  return request({
    url: '/procurement/dashboard/showData',
    method: 'get',
    params: params
  })
}

// 插入/更新公式
export function showYearlyAmount(params) {
  return request({
    url: '/procurement/dashboard/showYearlyAmount',
    method: 'post',
    data: params
  })
}

// 获取实时库存金额
export function showRealTimeAmount() {
  return request({
    url: '/procurement/dashboard/showRealTimeAmount',
    method: 'post'
  })
}

// 获取矿焦煤实时库存
export function showCokingCoalAmount() {
  return request({
    url: '/procurement/dashboard/showCokingCoalAmount',
    method: 'post'
  })
}

// 获取采购关键指标
export function showKeyIndicators(params) {
  return request({
    url: '/procurement/dashboard/showKeyIndicators',
    method: 'post',
    data: params
  })
}

// 获取物资类型列表
export function showItemTypeList(params) {
  return request({
    url: '/procurement/dashboard/showItemTypeList',
    method: 'get',
    params: params
  })
}

// 获取物资列表
export function showMaterialList(params) {
  return request({
    url: '/procurement/dashboard/showMaterialList',
    method: 'get',
    params: params
  })
}

// 获取供应商列表
export function showSuppList(params) {
  return request({
    url: '/procurement/dashboard/showSuppList',
    method: 'get',
    params: params
  })
}

// 获取采购计划列表
export function showPurchasePlanList(params) {
  return request({
    url: '/procurement/dashboard/plan/showPurchasePlanList',
    method: 'get',
    params: params
  })
}

// 获取高频采购物料列表
export function showHighFrequencyMaterialList(params) {
  return request({
    url: '/procurement/dashboard/showHighFrequencyMaterialList',
    method: 'post',
    data: params
  })
}

// 获取供应商风险数据
export function showPurchaseSuppRisk(params) {
  return request({
    url: '/procurement/dashboard/showPurchaseSuppRisk',
    method: 'post',
    data: params
  })
}

// 获取物料未来价格预测
export function getMaterialFuturePrice(params) {
  return request({
    url: '/procurement/dashboard/getMaterialFuturePrice',
    method: 'post',
    data: params
  })
}

// 获取物料名称列表
export function getMaterialNameList(params) {
  return request({
    url: '/procurement/dashboard/getMaterialNameList',
    method: 'post',
    data: params
  })
}

// 获取物料价格和采购量数据
export function getPurchasePriceAndStore(params) {
  return request({
    url: '/procurement/dashboard/getPurchasePriceAndStore',
    method: 'post',
    data: params
  })
}

// 获取物资名称列表（使用新数据表）
export function getMaterialNameListFromNewTables(params) {
  return request({
    url: '/procurement/dashboard/getMaterialNameListFromNewTables',
    method: 'post',
    data: params
  })
}

// 获取物资价格和入库数量（使用新数据表）
export function getPurchasePriceAndStoreFromNewTables(params) {
  return request({
    url: '/procurement/dashboard/getPurchasePriceAndStoreFromNewTables',
    method: 'post',
    data: params
  })
}
