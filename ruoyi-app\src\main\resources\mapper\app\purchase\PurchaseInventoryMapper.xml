<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.purchase.mapper.PurchaseInventoryMapper">

    <resultMap type="com.ruoyi.app.purchase.vo.ProcurementMonthlyResultVo" id="ProcurementMonthlyResultVoResult">
        <result property="amount"    column="amount"    />
        <result property="monthIndex"    column="month_index"    />
        <result property="year"    column="year"    />
    </resultMap>

    <resultMap type="com.ruoyi.app.purchase.vo.PurchaseCokingCoalInventoryDetailVo" id="PurchaseCokingCoalInventoryDetailVoResult">
        <result property="instockDate"    column="instock_date"    />
        <result property="class2"    column="class2"    />
        <result property="class2Name"    column="class2_name"    />
        <result property="invQty"    column="inv_qty"    />
    </resultMap>

    <resultMap type="com.ruoyi.app.purchase.dto.PurchaseInventoryQueryDto" id="PurchaseInventoryQueryDtoResult">
        <result property="class1"    column="class1"    />
        <result property="class1Name"    column="class1_name"    />
        <result property="stockMoney"    column="stock_money"    />
    </resultMap>
    <resultMap type="com.ruoyi.app.purchase.domain.PurchaseTotalAmount" id="PurchaseTotalAmountResult">
        <result property="reserve1"    column="RESERVE1"    />
        <result property="reserve4"    column="RESERVE4"    />
    </resultMap>
    <select id="selectYearlyInventoryAmount" parameterType="com.ruoyi.app.purchase.dto.ProcurementYearlyQueryDto" resultMap="ProcurementMonthlyResultVoResult">
        SELECT
            SUBSTR(t.ACCOUNT_PERIOD, 1, 4) AS year,
            TO_NUMBER(SUBSTR(t.ACCOUNT_PERIOD, 5, 2)) AS month_index,
            ROUND(NVL(SUM(t.INV_ENDING_BALANCE_AMT), 0) / 10000, 1) AS amount
        FROM TPOPI66 t
        WHERE 1=1
        <if test="materialType != null and materialType != ''">
            AND t.CLASS1 = #{materialType}
        </if>
        <if test="yearList != null and yearList.size() > 0">
            AND SUBSTR(t.ACCOUNT_PERIOD, 1, 4) IN
            <foreach collection="yearList" item="year" open="(" separator="," close=")">
                #{year}
            </foreach>
        </if>
        GROUP BY SUBSTR(t.ACCOUNT_PERIOD, 1, 4), SUBSTR(t.ACCOUNT_PERIOD, 5, 2)
        ORDER BY SUBSTR(t.ACCOUNT_PERIOD, 1, 4), SUBSTR(t.ACCOUNT_PERIOD, 5, 2)
    </select>

    <select id="selectCokingCoalInventoryByDate" parameterType="string" resultMap="PurchaseCokingCoalInventoryDetailVoResult">
        SELECT t.instock_date, t.class2, tpopc03.child_label AS class2_name, SUM(t.inv_qty) inv_qty
        FROM tpopi50 t
        LEFT JOIN tpopc03
          ON t.class2 = tpopc03.child_name
        WHERE t.instock_date >= #{instockDate}
        GROUP BY t.instock_date, t.class2, tpopc03.child_label
        ORDER BY inv_qty DESC
    </select>

    <select id="selectLatestCokingCoalInventoryDate" resultType="string">
        SELECT MAX(t.instock_date)
        FROM tpopi50 t
        WHERE t.instock_date IS NOT NULL
        AND t.inv_qty IS NOT NULL
    </select>

    <!-- 中心仓库金额查询 -->
    <select id="selectCenterInventoryAmount" resultMap="PurchaseInventoryQueryDtoResult">
        SELECT t.class1, tpopc03.child_label AS class1_name, ROUND(NVL(SUM(t.stock_money), 0)/10000, 2) AS stock_money
        FROM tpopi40 t
        LEFT JOIN tpopc03
          ON t.class1 = tpopc03.child_name
        WHERE t.state_id = '01'
          AND t.inv_qty != '0'
          AND T.CLASS1 != 'F'
        GROUP BY t.class1, tpopc03.child_label
    </select>

    <!-- 机旁库金额查询 -->
    <select id="selectMachineSideInventoryAmount" resultMap="PurchaseInventoryQueryDtoResult">
        SELECT class1, '' AS class1_name, ROUND(NVL(stock_money, 0), 2) AS stock_money
        FROM v_jp_stock_g
    </select>

    <!-- 拟入库金额查询 -->
    <select id="selectPlanInventoryAmount" resultMap="PurchaseTotalAmountResult">
        select reserve1,reserve4 from TPOPI99_TOTAL_AMT where reserve1 in('01','02','03')
    </select>

</mapper>
