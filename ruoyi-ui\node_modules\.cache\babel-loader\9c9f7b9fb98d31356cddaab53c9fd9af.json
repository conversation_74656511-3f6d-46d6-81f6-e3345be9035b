{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\config\\user\\list.vue", "mtime": 1756456282511}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9qYXZhX3dvcmtzcGFjZS9uZXdfd29ya3NwYWNlL3hjdGcvcnVveWktdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0LmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5jb25jYXQuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmcuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmZvci1lYWNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIik7CnZhciBfYXV0aCA9IHJlcXVpcmUoIkAvdXRpbHMvYXV0aCIpOwp2YXIgX3VzZXIgPSByZXF1aXJlKCJAL2FwaS9hc3Nlc3Mvc2VsZi91c2VyIik7CnZhciBfZGVwdCA9IHJlcXVpcmUoIkAvYXBpL2Fzc2Vzcy9sYXRlcmFsL2RlcHQiKTsKdmFyIF92dWVUcmVlc2VsZWN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCIpKTsKcmVxdWlyZSgiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJTZWxmQXNzZXNzVXNlckxpc3QiLAogIGNvbXBvbmVudHM6IHsKICAgIFRyZWVzZWxlY3Q6IF92dWVUcmVlc2VsZWN0LmRlZmF1bHQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g57up5pWI6ICD5qC4LeW5sumDqOiHquivhOS6uuWRmOmFjee9ruihqOagvOaVsOaNrgogICAgICBzZWxmQXNzZXNzVXNlckxpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGFzc2Vzc0RhdGU6IG51bGwsCiAgICAgICAgd29ya05vOiBudWxsLAogICAgICAgIG5hbWU6IG51bGwsCiAgICAgICAgYXNzZXNzUm9sZTogbnVsbCwKICAgICAgICBiZW5lZml0TGlua0ZsYWc6IG51bGwsCiAgICAgICAgYXZlcmFnZUxpbmtGbGFnOiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7fSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7fSwKICAgICAgLy8g5a2X5YW4CiAgICAgIGRpY3RzOiB7CiAgICAgICAgc2VsZl9hc3Nlc3Nfcm9sZTogW10sCiAgICAgICAgc3lzX3llc19ubzogW10KICAgICAgfSwKICAgICAgLy8g6YOo6Zeo5LiL5ouJ5qCRCiAgICAgIGRlcHRPcHRpb25zOiBbXSwKICAgICAgLy8g5a+85YWl5Y+C5pWwCiAgICAgIHVwbG9hZDogewogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gKICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICBBdXRob3JpemF0aW9uOiAnQmVhcmVyICcgKyAoMCwgX2F1dGguZ2V0VG9rZW4pKCkKICAgICAgICB9LAogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgAogICAgICAgIHVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvd2ViL3NlbGZBc3Nlc3NVc2VyL2ltcG9ydEluZm8iCiAgICAgIH0sCiAgICAgIC8vIOWvvOWFpee7k+aenAogICAgICBpbXBvcnRSZXM6IFtdLAogICAgICBvcGVuSW1wb3J0UmVzOiBmYWxzZQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmluaXRQYWdlRGF0YSgpOwogIH0sCiAgLy8g55uR5ZCs6Lev55Sx5Y+Y5YyW77yM56Gu5L+d5q+P5qyh6L+b5YWl6aG16Z2i6YO96YeN5paw6I635Y+W5pWw5o2uCiAgd2F0Y2g6IHsKICAgICckcm91dGUnOiBmdW5jdGlvbiAkcm91dGUodG8pIHsKICAgICAgLy8g5b2T6Lev55Sx5Y+R55Sf5Y+Y5YyW5pe277yM6YeN5paw5Yid5aeL5YyW6aG16Z2i5pWw5o2uCiAgICAgIGlmICh0by5wYXRoID09PSAnL2Fzc2Vzcy9zZWxmL3VzZXIvbGlzdCcpIHsKICAgICAgICB0aGlzLmluaXRQYWdlRGF0YSgpOwogICAgICB9CiAgICB9CiAgfSwKICAvLyDot6/nlLHmm7TmlrDml7bnmoTpkqnlrZAKICBiZWZvcmVSb3V0ZVVwZGF0ZTogZnVuY3Rpb24gYmVmb3JlUm91dGVVcGRhdGUodG8sIGZyb20sIG5leHQpIHsKICAgIC8vIOWcqOW9k+WJjei3r+eUseaUueWPmO+8jOS9huaYr+ivpee7hOS7tuiiq+WkjeeUqOaXtuiwg+eUqAogICAgdGhpcy5pbml0UGFnZURhdGEoKTsKICAgIG5leHQoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWIneWni+WMlumhtemdouaVsOaNrgogICAgaW5pdFBhZ2VEYXRhOiBmdW5jdGlvbiBpbml0UGFnZURhdGEoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIC8vIOiuvue9rum7mOiupOiAg+aguOW5tOaciOS4uuW9k+WJjeW5tOaciAogICAgICB2YXIgbm93ID0gbmV3IERhdGUoKTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlID0gIiIuY29uY2F0KG5vdy5nZXRGdWxsWWVhcigpLCAiLSIpLmNvbmNhdChub3cuZ2V0TW9udGgoKSArIDEpOwoKICAgICAgLy8g6YeN572u5pWw5o2uCiAgICAgIHRoaXMuc2VsZkFzc2Vzc1VzZXJMaXN0ID0gW107CiAgICAgIHRoaXMudG90YWwgPSAwOwoKICAgICAgLy8g6I635Y+W5pWw5o2uCiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB0aGlzLmdldFRyZWVzZWxlY3QoKTsKICAgICAgdGhpcy5nZXREaWN0cygic2VsZl9hc3Nlc3Nfcm9sZSIpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMuZGljdHMuc2VsZl9hc3Nlc3Nfcm9sZSA9IF90aGlzLmZvcm1hdHRlckRpY3QocmVzcG9uc2UuZGF0YSk7CiAgICAgIH0pOwogICAgICB0aGlzLmdldERpY3RzKCJzeXNfeWVzX25vIikudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpcy5kaWN0cy5zeXNfeWVzX25vID0gX3RoaXMuZm9ybWF0dGVyRGljdChyZXNwb25zZS5kYXRhKTsKICAgICAgfSk7CiAgICB9LAogICAgZm9ybWF0dGVyRGljdDogZnVuY3Rpb24gZm9ybWF0dGVyRGljdChkaWN0KSB7CiAgICAgIHZhciByZXN1bHQgPSBbXTsKICAgICAgZGljdC5mb3JFYWNoKGZ1bmN0aW9uIChkaWN0KSB7CiAgICAgICAgcmVzdWx0LnB1c2goewogICAgICAgICAgbGFiZWw6IGRpY3QuZGljdExhYmVsLAogICAgICAgICAgdmFsdWU6IGRpY3QuZGljdFZhbHVlCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICByZXR1cm4gcmVzdWx0OwogICAgfSwKICAgIC8qKiDmn6Xor6Lnu6nmlYjogIPmoLgt5bmy6YOo6Ieq6K+E5Lq65ZGY6YWN572u5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CgogICAgICAvLyDlpoLmnpzmnInogIPmoLjlubTmnIjvvIzkvb/nlKjmibnph4/ojrflj5bnirbmgIHnmoTmjqXlj6MKICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZSkgewogICAgICAgICgwLCBfdXNlci5saXN0QXZhaWxhYmxlV2l0aFN0YXR1cykodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgIF90aGlzMi5zZWxmQXNzZXNzVXNlckxpc3QgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueUqOaIt+WIl+ihqOWksei0pTonLCBlcnJvcik7CiAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUqOaIt+WIl+ihqOWksei0pScpOwogICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDmsqHmnInogIPmoLjlubTmnIjml7bkvb/nlKjljp/mjqXlj6MKICAgICAgICAoMCwgX3VzZXIubGlzdEF2YWlsYWJsZSkodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgIF90aGlzMi5zZWxmQXNzZXNzVXNlckxpc3QgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueUqOaIt+WIl+ihqOWksei0pTonLCBlcnJvcik7CiAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlueUqOaIt+WIl+ihqOWksei0pScpOwogICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsOiBmdW5jdGlvbiBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldDogZnVuY3Rpb24gcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICB3b3JrTm86IG51bGwsCiAgICAgICAgbmFtZTogbnVsbCwKICAgICAgICBhc3Nlc3NSb2xlOiBudWxsLAogICAgICAgIGJlbmVmaXRMaW5rRmxhZzogbnVsbCwKICAgICAgICBhdmVyYWdlTGlua0ZsYWc6IG51bGwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICAvLyDph43nva7lkI7ph43mlrDorr7nva7pu5jorqTogIPmoLjlubTmnIgKICAgICAgdmFyIG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZSA9ICIiLmNvbmNhdChub3cuZ2V0RnVsbFllYXIoKSwgIi0iKS5jb25jYXQobm93LmdldE1vbnRoKCkgKyAxKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8qKiDovazmjaLmqKrlkJHor4Tku7fpg6jpl6jmlbDmja7nu5PmnoQgKi9ub3JtYWxpemVyOiBmdW5jdGlvbiBub3JtYWxpemVyKG5vZGUpIHsKICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgIW5vZGUuY2hpbGRyZW4ubGVuZ3RoKSB7CiAgICAgICAgZGVsZXRlIG5vZGUuY2hpbGRyZW47CiAgICAgIH0KICAgICAgcmV0dXJuIHsKICAgICAgICBpZDogbm9kZS5kZXB0SWQsCiAgICAgICAgbGFiZWw6IG5vZGUuZGVwdE5hbWUsCiAgICAgICAgY2hpbGRyZW46IG5vZGUuY2hpbGRyZW4KICAgICAgfTsKICAgIH0sCiAgICAvKiog5p+l6K+i5qiq5ZCR6K+E5Lu36YOo6Zeo5LiL5ouJ5qCR57uT5p6EICovZ2V0VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0VHJlZXNlbGVjdCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgICgwLCBfZGVwdC5saXN0RGVwdCkoKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzMy5kZXB0T3B0aW9ucyA9IF90aGlzMy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJkZXB0SWQiLCAicGFyZW50SWQiKTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOmFjee9rueCueWHu+S6i+S7tiAqL2hhbmRsZUNvbmZpZzogZnVuY3Rpb24gaGFuZGxlQ29uZmlnKHJvdykgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogIi9hc3Nlc3Mvc2VsZi91c2VyL2RldGFpbCIsCiAgICAgICAgcXVlcnk6IHsKICAgICAgICAgIHVzZXJJZDogcm93LmlkCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog6Ieq6K+E5aGr5YaZ54K55Ye75LqL5Lu2ICovaGFuZGxlUmVwb3J0OiBmdW5jdGlvbiBoYW5kbGVSZXBvcnQocm93KSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAiL2Fzc2Vzcy9zZWxmL3VzZXIvcmVwb3J0IiwKICAgICAgICBxdWVyeTogewogICAgICAgICAgd29ya05vOiByb3cud29ya05vLAogICAgICAgICAgZGVwdElkOiByb3cuZGVwdExpc3RbMF0uZGVwdElkCiAgICAgICAgfQogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_auth", "require", "_user", "_dept", "_vueTreeselect", "_interopRequireDefault", "name", "components", "Treeselect", "data", "loading", "showSearch", "total", "selfAssessUserList", "title", "open", "queryParams", "pageNum", "pageSize", "assessDate", "workNo", "assessRole", "benefitLinkFlag", "averageLinkFlag", "form", "rules", "dicts", "self_assess_role", "sys_yes_no", "deptOptions", "upload", "isUploading", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "importRes", "openImportRes", "created", "initPageData", "watch", "$route", "to", "path", "beforeRouteUpdate", "from", "next", "methods", "_this", "now", "Date", "concat", "getFullYear", "getMonth", "getList", "getTreeselect", "getDicts", "then", "response", "formatterDict", "dict", "result", "for<PERSON>ach", "push", "label", "dict<PERSON><PERSON>l", "value", "dict<PERSON><PERSON>ue", "_this2", "listAvailableWithStatus", "catch", "error", "console", "$message", "listAvailable", "cancel", "reset", "id", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "normalizer", "node", "children", "length", "deptId", "deptName", "_this3", "listDept", "handleTree", "handleConfig", "row", "$router", "query", "userId", "handleReport", "deptList"], "sources": ["src/views/assess/self/config/user/list.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.assessDate\"\r\n              type=\"month\"\r\n              value-format=\"yyyy-M\"\r\n              format=\"yyyy 年 M 月\"\r\n              placeholder=\"选择考核年月\"\r\n              :clearable=\"false\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"工号\" prop=\"workNo\">\r\n            <el-input\r\n              v-model=\"queryParams.workNo\"\r\n              placeholder=\"请输入工号\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入姓名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <treeselect style=\"width: 200px;\" v-model=\"queryParams.deptId\" :multiple=\"false\" :options=\"deptOptions\" :normalizer=\"normalizer\" :disable-branch-nodes=\"true\" placeholder=\"请选择部门\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n  \r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n  \r\n      <el-table v-loading=\"loading\" :data=\"selfAssessUserList\">\r\n        <!-- <el-table-column label=\"编号\" align=\"center\" prop=\"id\" /> -->\r\n        <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n        <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n        <!-- <el-table-column label=\"身份\" align=\"center\" prop=\"assessRole\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            {{ dicts.self_assess_role[scope.row.assessRole][\"label\"] }}\r\n          </template>\r\n        </el-table-column> -->\r\n        <el-table-column label=\"部门\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <span v-for=\"item, index in scope.row.deptList\" v-bind:key=\"index\">\r\n              {{ scope.row.deptList.length > 1 && index + 1 != scope.row.deptList.length ? item.deptName + \", \" : item.deptName}}\r\n            </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"填写状态\" align=\"center\" prop=\"assessStatus\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.assessStatus === '0'\" type=\"primary\">已保存</el-tag>\r\n            <el-tag v-else-if=\"scope.row.assessStatus === '1'\" type=\"warning\">部门评分</el-tag>\r\n            <el-tag v-else-if=\"scope.row.assessStatus === '2'\" type=\"warning\">事业部评分</el-tag>\r\n            <el-tag v-else-if=\"scope.row.assessStatus === '3'\" type=\"warning\">运改/组织部评分</el-tag>\r\n            <el-tag v-else-if=\"scope.row.assessStatus === '4' || scope.row.assessStatus === '5'\" type=\"success\">已完成</el-tag>\r\n            <el-tag v-else type=\"info\">未填写</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleConfig(scope.row)\"\r\n            >指标配置</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleReport(scope.row)\"\r\n            >自评填写</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n  \r\n    </div>\r\n  </template>\r\n  \r\n  <script>\r\n  import { getToken } from \"@/utils/auth\";\r\n  import { listAvailable, listAvailableWithStatus } from \"@/api/assess/self/user\";\r\n  import { listDept } from \"@/api/assess/lateral/dept\";\r\n  import Treeselect from \"@riophae/vue-treeselect\";\r\n  import \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n  \r\n  export default {\r\n    name: \"SelfAssessUserList\",\r\n    components: {\r\n      Treeselect\r\n    },\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        // 绩效考核-干部自评人员配置表格数据\r\n        selfAssessUserList: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          assessDate: null,\r\n          workNo: null,\r\n          name: null,\r\n          assessRole: null,\r\n          benefitLinkFlag: null,\r\n          averageLinkFlag: null\r\n        },\r\n        // 表单参数\r\n        form: {},\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        // 字典\r\n        dicts:{\r\n          self_assess_role:[],\r\n          sys_yes_no:[]\r\n        },\r\n        // 部门下拉树\r\n        deptOptions:[],\r\n        // 导入参数\r\n        upload: {\r\n          // 是否禁用上传\r\n          isUploading: false,\r\n          // 设置上传的请求头部\r\n          headers: { Authorization: 'Bearer ' + getToken() },\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/web/selfAssessUser/importInfo\",\r\n        },\r\n        // 导入结果\r\n        importRes:[],\r\n        openImportRes:false\r\n      };\r\n    },\r\n    created() {\r\n      this.initPageData();\r\n    },\r\n\r\n    // 监听路由变化，确保每次进入页面都重新获取数据\r\n    watch: {\r\n      '$route'(to) {\r\n        // 当路由发生变化时，重新初始化页面数据\r\n        if (to.path === '/assess/self/user/list') {\r\n          this.initPageData();\r\n        }\r\n      }\r\n    },\r\n\r\n    // 路由更新时的钩子\r\n    beforeRouteUpdate(to, from, next) {\r\n      // 在当前路由改变，但是该组件被复用时调用\r\n      this.initPageData();\r\n      next();\r\n    },\r\n    methods: {\r\n      // 初始化页面数据\r\n      initPageData() {\r\n        // 设置默认考核年月为当前年月\r\n        const now = new Date();\r\n        this.queryParams.assessDate = `${now.getFullYear()}-${now.getMonth() + 1}`;\r\n\r\n        // 重置数据\r\n        this.selfAssessUserList = [];\r\n        this.total = 0;\r\n\r\n        // 获取数据\r\n        this.getList();\r\n        this.getTreeselect();\r\n        this.getDicts(\"self_assess_role\").then(response => {\r\n          this.dicts.self_assess_role = this.formatterDict(response.data);\r\n        });\r\n        this.getDicts(\"sys_yes_no\").then(response => {\r\n          this.dicts.sys_yes_no = this.formatterDict(response.data);\r\n        });\r\n      },\r\n\r\n      formatterDict(dict){\r\n        let result = []\r\n        dict.forEach(dict => {\r\n          result.push({\r\n            label:dict.dictLabel,\r\n            value:dict.dictValue\r\n          })\r\n        });\r\n        return result;\r\n      },\r\n      /** 查询绩效考核-干部自评人员配置列表 */\r\n      getList() {\r\n        this.loading = true;\r\n\r\n        // 如果有考核年月，使用批量获取状态的接口\r\n        if (this.queryParams.assessDate) {\r\n          listAvailableWithStatus(this.queryParams).then(response => {\r\n            this.selfAssessUserList = response.data;\r\n            this.loading = false;\r\n          }).catch(error => {\r\n            console.error('获取用户列表失败:', error);\r\n            this.$message.error('获取用户列表失败');\r\n            this.loading = false;\r\n          });\r\n        } else {\r\n          // 没有考核年月时使用原接口\r\n          listAvailable(this.queryParams).then(response => {\r\n            this.selfAssessUserList = response.data;\r\n            this.loading = false;\r\n          }).catch(error => {\r\n            console.error('获取用户列表失败:', error);\r\n            this.$message.error('获取用户列表失败');\r\n            this.loading = false;\r\n          });\r\n        }\r\n      },\r\n\r\n\r\n      // 取消按钮\r\n      cancel() {\r\n        this.open = false;\r\n        this.reset();\r\n      },\r\n      // 表单重置\r\n      reset() {\r\n        this.form = {\r\n          id: null,\r\n          workNo: null,\r\n          name: null,\r\n          assessRole: null,\r\n          benefitLinkFlag: null,\r\n          averageLinkFlag: null\r\n        };\r\n        this.resetForm(\"form\");\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        // 重置后重新设置默认考核年月\r\n        const now = new Date();\r\n        this.queryParams.assessDate = `${now.getFullYear()}-${now.getMonth() + 1}`;\r\n        this.handleQuery();\r\n      },\r\n  \r\n      /** 转换横向评价部门数据结构 */\r\n      normalizer(node) {\r\n        if (node.children && !node.children.length) {\r\n          delete node.children;\r\n        }\r\n        return {\r\n          id: node.deptId,\r\n          label: node.deptName,\r\n          children: node.children\r\n        };\r\n      },\r\n        /** 查询横向评价部门下拉树结构 */\r\n      getTreeselect() {\r\n        listDept().then(response => {\r\n          this.deptOptions = this.handleTree(response.data, \"deptId\", \"parentId\");\r\n        });\r\n      },\r\n  \r\n      /** 配置点击事件 */\r\n      handleConfig(row){\r\n        this.$router.push({\r\n          path:\"/assess/self/user/detail\",\r\n          query:{\r\n            userId:row.id\r\n          }\r\n        })\r\n      },\r\n      /** 自评填写点击事件 */\r\n      handleReport(row){\r\n        this.$router.push({\r\n          path:\"/assess/self/user/report\",\r\n          query:{\r\n            workNo:row.workNo,\r\n            deptId:row.deptList[0].deptId\r\n          }\r\n        })\r\n      }\r\n      \r\n    }\r\n  };\r\n  </script>\r\n  <style>\r\n  .redtext{\r\n    color: red;\r\n  }\r\n  </style>\r\n  "], "mappings": ";;;;;;;;;;;;AAoGA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,kBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAd,IAAA;QACAe,UAAA;QACAC,eAAA;QACAC,eAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;MACA;MACAC,KAAA;QACAC,gBAAA;QACAC,UAAA;MACA;MACA;MACAC,WAAA;MACA;MACAC,MAAA;QACA;QACAC,WAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACA;MACAC,SAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;EACA;EAEA;EACAC,KAAA;IACA,mBAAAC,OAAAC,EAAA;MACA;MACA,IAAAA,EAAA,CAAAC,IAAA;QACA,KAAAJ,YAAA;MACA;IACA;EACA;EAEA;EACAK,iBAAA,WAAAA,kBAAAF,EAAA,EAAAG,IAAA,EAAAC,IAAA;IACA;IACA,KAAAP,YAAA;IACAO,IAAA;EACA;EACAC,OAAA;IACA;IACAR,YAAA,WAAAA,aAAA;MAAA,IAAAS,KAAA;MACA;MACA,IAAAC,GAAA,OAAAC,IAAA;MACA,KAAArC,WAAA,CAAAG,UAAA,MAAAmC,MAAA,CAAAF,GAAA,CAAAG,WAAA,SAAAD,MAAA,CAAAF,GAAA,CAAAI,QAAA;;MAEA;MACA,KAAA3C,kBAAA;MACA,KAAAD,KAAA;;MAEA;MACA,KAAA6C,OAAA;MACA,KAAAC,aAAA;MACA,KAAAC,QAAA,qBAAAC,IAAA,WAAAC,QAAA;QACAV,KAAA,CAAAzB,KAAA,CAAAC,gBAAA,GAAAwB,KAAA,CAAAW,aAAA,CAAAD,QAAA,CAAApD,IAAA;MACA;MACA,KAAAkD,QAAA,eAAAC,IAAA,WAAAC,QAAA;QACAV,KAAA,CAAAzB,KAAA,CAAAE,UAAA,GAAAuB,KAAA,CAAAW,aAAA,CAAAD,QAAA,CAAApD,IAAA;MACA;IACA;IAEAqD,aAAA,WAAAA,cAAAC,IAAA;MACA,IAAAC,MAAA;MACAD,IAAA,CAAAE,OAAA,WAAAF,IAAA;QACAC,MAAA,CAAAE,IAAA;UACAC,KAAA,EAAAJ,IAAA,CAAAK,SAAA;UACAC,KAAA,EAAAN,IAAA,CAAAO;QACA;MACA;MACA,OAAAN,MAAA;IACA;IACA,wBACAP,OAAA,WAAAA,QAAA;MAAA,IAAAc,MAAA;MACA,KAAA7D,OAAA;;MAEA;MACA,SAAAM,WAAA,CAAAG,UAAA;QACA,IAAAqD,6BAAA,OAAAxD,WAAA,EAAA4C,IAAA,WAAAC,QAAA;UACAU,MAAA,CAAA1D,kBAAA,GAAAgD,QAAA,CAAApD,IAAA;UACA8D,MAAA,CAAA7D,OAAA;QACA,GAAA+D,KAAA,WAAAC,KAAA;UACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;UACAH,MAAA,CAAAK,QAAA,CAAAF,KAAA;UACAH,MAAA,CAAA7D,OAAA;QACA;MACA;QACA;QACA,IAAAmE,mBAAA,OAAA7D,WAAA,EAAA4C,IAAA,WAAAC,QAAA;UACAU,MAAA,CAAA1D,kBAAA,GAAAgD,QAAA,CAAApD,IAAA;UACA8D,MAAA,CAAA7D,OAAA;QACA,GAAA+D,KAAA,WAAAC,KAAA;UACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;UACAH,MAAA,CAAAK,QAAA,CAAAF,KAAA;UACAH,MAAA,CAAA7D,OAAA;QACA;MACA;IACA;IAGA;IACAoE,MAAA,WAAAA,OAAA;MACA,KAAA/D,IAAA;MACA,KAAAgE,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAvD,IAAA;QACAwD,EAAA;QACA5D,MAAA;QACAd,IAAA;QACAe,UAAA;QACAC,eAAA;QACAC,eAAA;MACA;MACA,KAAA0D,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlE,WAAA,CAAAC,OAAA;MACA,KAAAwC,OAAA;IACA;IACA,aACA0B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA;MACA,IAAA7B,GAAA,OAAAC,IAAA;MACA,KAAArC,WAAA,CAAAG,UAAA,MAAAmC,MAAA,CAAAF,GAAA,CAAAG,WAAA,SAAAD,MAAA,CAAAF,GAAA,CAAAI,QAAA;MACA,KAAA0B,WAAA;IACA;IAEA,mBACAE,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAN,EAAA,EAAAK,IAAA,CAAAG,MAAA;QACArB,KAAA,EAAAkB,IAAA,CAAAI,QAAA;QACAH,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA,oBACA5B,aAAA,WAAAA,cAAA;MAAA,IAAAgC,MAAA;MACA,IAAAC,cAAA,IAAA/B,IAAA,WAAAC,QAAA;QACA6B,MAAA,CAAA7D,WAAA,GAAA6D,MAAA,CAAAE,UAAA,CAAA/B,QAAA,CAAApD,IAAA;MACA;IACA;IAEA,aACAoF,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAC,OAAA,CAAA7B,IAAA;QACApB,IAAA;QACAkD,KAAA;UACAC,MAAA,EAAAH,GAAA,CAAAd;QACA;MACA;IACA;IACA,eACAkB,YAAA,WAAAA,aAAAJ,GAAA;MACA,KAAAC,OAAA,CAAA7B,IAAA;QACApB,IAAA;QACAkD,KAAA;UACA5E,MAAA,EAAA0E,GAAA,CAAA1E,MAAA;UACAoE,MAAA,EAAAM,GAAA,CAAAK,QAAA,IAAAX;QACA;MACA;IACA;EAEA;AACA", "ignoreList": []}]}