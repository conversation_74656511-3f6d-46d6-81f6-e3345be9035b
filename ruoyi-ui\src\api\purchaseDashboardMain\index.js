import request from '@/utils/request'

// 获取采购销售看板数据
export function getDashboardData(params) {
  return request({
    url: '/purchase/dashboard/main/data',
    method: 'get',
    params: params
  })
}

// 获取个人消费情况数据
export function getPersonalConsumption(params) {
  return request({
    url: '/purchase/dashboard/main/personalConsumption',
    method: 'get',
    params: params
  })
}

// 获取采购分析数据
export function getPurchaseAnalysis(params) {
  return request({
    url: '/purchase/dashboard/main/purchaseAnalysis',
    method: 'get',
    params: params
  })
}

// 获取产品分析数据
export function getProductAnalysis(params) {
  return request({
    url: '/purchase/dashboard/main/productAnalysis',
    method: 'get',
    params: params
  })
}

// 获取地图数据
export function getMapData(params) {
  return request({
    url: '/purchase/dashboard/main/mapData',
    method: 'get',
    params: params
  })
}

// 获取实时数据统计
export function getRealTimeStats(params) {
  return request({
    url: '/purchase/dashboard/main/realTimeStats',
    method: 'get',
    params: params
  })
}

// 获取销售漏斗数据
export function getSalesFunnel(params) {
  return request({
    url: '/purchase/dashboard/main/salesFunnel',
    method: 'get',
    params: params
  })
}

// 获取采购趋势数据
export function getPurchaseTrend(params) {
  return request({
    url: '/purchase/dashboard/main/purchaseTrend',
    method: 'get',
    params: params
  })
}

// 获取供应商分析数据
export function getSupplierAnalysis(params) {
  return request({
    url: '/purchase/dashboard/main/supplierAnalysis',
    method: 'get',
    params: params
  })
}

// 获取库存分析数据
export function getInventoryAnalysis(params) {
  return request({
    url: '/purchase/dashboard/main/inventoryAnalysis',
    method: 'get',
    params: params
  })
}

// 获取资金管理数据
export function getAmtManage() {
  return request({
    url: '/procurement/dashboard/showAmtManage',
    method: 'get'
  })
}
