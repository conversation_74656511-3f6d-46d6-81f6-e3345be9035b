{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\leaderCheck.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\leaderCheck.vue", "mtime": 1756456282454}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RMZWFkZXJUb0NoZWNrLCBsaXN0Q2hlY2tlZCwgZ2V0SW5mbywgY2hlY2sgfSBmcm9tICJAL2FwaS9hc3Nlc3Mvc2VsZi9pbmZvIgppbXBvcnQgeyBnZXRDaGVja0RlcHRMaXN0IH0gZnJvbSAiQC9hcGkvYXNzZXNzL3NlbGYvdXNlciI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIlNlbGZBc3Nlc3NMZWFkZXJDaGVjayIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICBjaGVja2VkVG90YWw6IDAsCiAgICAgIC8vIOe7qeaViOiAg+aguC3lubLpg6joh6ror4TkurrlkZjphY3nva7ooajmoLzmlbDmja4KICAgICAgbGlzdFRvQ2hlY2s6IFtdLAogICAgICBsaXN0Q2hlY2tlZDogW10sCiAgICAgIC8vIOW8ueWHuuWxguagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgd29ya05vOiBudWxsLAogICAgICAgIG5hbWU6bnVsbCwKICAgICAgICBkZXB0SWQ6bnVsbCwKICAgICAgICBhc3Nlc3NEYXRlOm51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHsKICAgICAgICBpZDpudWxsLAogICAgICAgIC8vIOadoee6v+mihuWvvOivhOWIhgogICAgICAgIGxlYWRlclNjb3JlOm51bGwsCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICB9LAogICAgICBkZXB0T3B0aW9uczpbXSwKICAgICAgb3BlbkNoZWNrOmZhbHNlLAogICAgICBjaGVja0luZm86e30sCiAgICAgIC8vIOWQiOW5tuWNleWFg+agvAogICAgICBzcGFuTGlzdDpbXSwKICAgICAgYWN0aXZlTmFtZToidG9DaGVjayIsCiAgICAgIC8vIOW+heivhOWIhuagh+etvgogICAgICB0b0NoZWNrTGFiZWw6IuW+heivhOWIhigwKSIsCiAgICAgIC8vIOW3suivhOWIhgogICAgICBjaGVja2VkTGFiZWw6IuW3suivhOWIhigwKSIKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5xdWVyeVBhcmFtcy5hc3Nlc3NEYXRlID0gdGhpcy5nZXREZWZhdWx0QXNzZXNzRGF0ZSgpCiAgICAvLyB0aGlzLmdldFNlbGZBc3Nlc3NVc2VyKCk7CiAgICB0aGlzLmdldENoZWNrRGVwdExpc3QoKTsKICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgdGhpcy5nZXRDaGVja2VkTGlzdCgpOwogIH0sCiAgbWV0aG9kczogewoKICAgIC8vIOiOt+WPlum7mOiupOiAg+aguOaXpeacnwogICAgZ2V0RGVmYXVsdEFzc2Vzc0RhdGUoKSB7CiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7CiAgICAgIGNvbnN0IGN1cnJlbnREYXkgPSBub3cuZ2V0RGF0ZSgpOwoKICAgICAgbGV0IHRhcmdldERhdGU7CiAgICAgIGlmIChjdXJyZW50RGF5IDwgMTApIHsKICAgICAgICAvLyDlvZPliY3ml6XmnJ/lsI/kuo4xMOaXpe+8jOm7mOiupOS4uuS4iuS4quaciAogICAgICAgIHRhcmdldERhdGUgPSBuZXcgRGF0ZShub3cuZ2V0RnVsbFllYXIoKSwgbm93LmdldE1vbnRoKCkgLSAxLCAxKTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvLyDlvZPliY3ml6XmnJ/lpKfkuo7nrYnkuo4xMOaXpe+8jOm7mOiupOS4uuW9k+aciAogICAgICAgIHRhcmdldERhdGUgPSBuZXcgRGF0ZShub3cuZ2V0RnVsbFllYXIoKSwgbm93LmdldE1vbnRoKCksIDEpOwogICAgICB9CgogICAgICAvLyDmoLzlvI/ljJbkuLogWVlZWS1NIOagvOW8jwogICAgICBjb25zdCB5ZWFyID0gdGFyZ2V0RGF0ZS5nZXRGdWxsWWVhcigpOwogICAgICBjb25zdCBtb250aCA9IHRhcmdldERhdGUuZ2V0TW9udGgoKSArIDE7CiAgICAgIHJldHVybiBgJHt5ZWFyfS0ke21vbnRofWA7CiAgICB9LAoKICAgIC8vIOiOt+WPlumDqOmXqOS/oeaBrwogICAgZ2V0Q2hlY2tEZXB0TGlzdCgpewogICAgICBnZXRDaGVja0RlcHRMaXN0KCkudGhlbihyZXMgPT4gewogICAgICAgIGNvbnNvbGUubG9nKHJlcykKICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApewogICAgICAgICAgbGV0IGRlcHRPcHRpb25zID0gW107CiAgICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICBkZXB0T3B0aW9ucy5wdXNoKHsKICAgICAgICAgICAgICBkZXB0TmFtZTppdGVtLmRlcHROYW1lLAogICAgICAgICAgICAgIGRlcHRJZDppdGVtLmRlcHRJZAogICAgICAgICAgICB9KQogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuZGVwdE9wdGlvbnMgPSBkZXB0T3B0aW9uczsKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgLyoqIOafpeivoue7qeaViOiAg+aguC3lubLpg6joh6ror4TlvoXlrqHmoLjliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RMZWFkZXJUb0NoZWNrKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubGlzdFRvQ2hlY2sgPSByZXNwb25zZS5yb3dzOwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLnRvQ2hlY2tMYWJlbCA9IGDlvoXor4TliIYoJHtyZXNwb25zZS50b3RhbH0pYAogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog6I635Y+W5bey5a6h5qC45YiX6KGoICovCiAgICBnZXRDaGVja2VkTGlzdCgpewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0Q2hlY2tlZCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5saXN0Q2hlY2tlZCA9IHJlcy5yb3dzOwogICAgICAgIHRoaXMuY2hlY2tlZFRvdGFsID0gcmVzLnRvdGFsOwogICAgICAgIHRoaXMuY2hlY2tlZExhYmVsID0gYOW3suivhOWIhigke3Jlcy50b3RhbH0pYAogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KQogICAgfSwKICAgIC8vIOagh+etvumhteeCueWHu+S6i+S7tgogICAgaGFuZGxlVGFiQ2xpY2soZSl7CiAgICAgIGxldCB0eXBlID0gZS5uYW1lOwogICAgICBpZih0eXBlID09ICJjaGVja2VkIil7CiAgICAgICAgdGhpcy5nZXRDaGVja2VkTGlzdCgpOwogICAgICB9ZWxzZXsKICAgICAgICB0aGlzLmdldExpc3QoKTsKICAgICAgfQogICAgfSwKCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgaWQ6IG51bGwsCiAgICAgICAgZGVwdFNjb3JlOiBudWxsLAogICAgICAgIGJ1c2luZXNzU2NvcmU6IG51bGwsCiAgICAgICAgbGVhZGVyU2NvcmU6IG51bGwsCiAgICAgIH07CiAgICAgIC8vIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0Q2hlY2tlZExpc3QoKTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAoKICAgIHF1ZXJ5QWxsKCl7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICB0aGlzLmdldENoZWNrZWRMaXN0KCk7CiAgICB9LAoKICAgIC8vIOWuoeaJueivpuaDhQogICAgaGFuZGxlQ2hlY2tEZXRhaWwocm93KXsKICAgICAgZ2V0SW5mbyh7aWQ6cm93LmlkfSkudGhlbihyZXMgPT4gewogICAgICAgIGNvbnNvbGUubG9nKHJlcyk7CiAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsKICAgICAgICAgIHRoaXMuY2hlY2tJbmZvID0gcmVzLmRhdGE7CiAgICAgICAgICBsZXQgbGlzdCA9IEpTT04ucGFyc2UocmVzLmRhdGEuY29udGVudCk7CiAgICAgICAgICB0aGlzLmhhbmRsZVNwYW5MaXN0KGxpc3QpOwogICAgICAgICAgdGhpcy5jaGVja0luZm8ubGlzdCA9IGxpc3Q7CiAgICAgICAgfQogICAgICAgIHRoaXMub3BlbiA9IHRydWUKICAgICAgfSkKICAgIH0sCgogICAgLy8g5a6h5om55o+Q5LqkCiAgICBjaGVja1N1Ym1pdCgpewogICAgICBpZih0aGlzLnZlcmlmeSgpKXsKICAgICAgICB0aGlzLmZvcm0uaWQgPSB0aGlzLmNoZWNrSW5mby5pZDsKICAgICAgICB0aGlzLmZvcm0uc3RhdHVzID0gdGhpcy5jaGVja0luZm8uc3RhdHVzOwogICAgICAgIGNoZWNrKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgY29uc29sZS5sb2cocmVzKQogICAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfmj5DkuqTmiJDlip8hJwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdGhpcy5yZXNldCgpOwogICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5xdWVyeUFsbCgpOwogICAgICAgICAgfWVsc2V7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5pON5L2c5aSx6LSl77yM5peg5p2D6ZmQ5oiW5b2T5YmN5a6h5om554q25oCB5LiN5Yy56YWNJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9ZWxzZXsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgIG1lc3NhZ2U6ICfor7floavlhpnor4TliIYnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICB9LAoKICAgIC8vIOaVsOaNrumqjOivgQogICAgdmVyaWZ5KCl7CiAgICAgIGlmKCF0aGlzLmZvcm0ubGVhZGVyU2NvcmUpIHJldHVybiBmYWxzZTsKICAgICAgcmV0dXJuIHRydWU7CiAgICB9LAoKICAgIGhhbmRsZUxpc3RDaGFuZ2UodHlwZSl7CiAgICAgIGNvbnNvbGUubG9nKHR5cGUpCiAgICB9LAogICAgLy8g5aSE55CG5YiX6KGoCiAgICBoYW5kbGVTcGFuTGlzdChkYXRhKXsKICAgICAgbGV0IHNwYW5MaXN0ID0gW107CiAgICAgIGxldCBmbGFnID0gMDsKICAgICAgZm9yKGxldCBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpKyspewogICAgICAgIC8vIOebuOWQjOiAg+aguOmhueWQiOW5tgogICAgICAgIGlmKGkgPT0gMCl7CiAgICAgICAgICBzcGFuTGlzdC5wdXNoKHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgfSkKICAgICAgICB9ZWxzZXsKICAgICAgICAgIGlmKGRhdGFbaSAtIDFdLml0ZW0gPT0gZGF0YVtpXS5pdGVtKXsKICAgICAgICAgICAgc3Bhbkxpc3QucHVzaCh7CiAgICAgICAgICAgICAgcm93c3BhbjogMCwKICAgICAgICAgICAgICBjb2xzcGFuOiAwCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHNwYW5MaXN0W2ZsYWddLnJvd3NwYW4gKz0gMTsKICAgICAgICAgIH1lbHNlewogICAgICAgICAgICBzcGFuTGlzdC5wdXNoKHsKICAgICAgICAgICAgICByb3dzcGFuOiAxLAogICAgICAgICAgICAgIGNvbHNwYW46IDEKICAgICAgICAgICAgfSkKICAgICAgICAgICAgZmxhZyA9IGk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICAgIHRoaXMuc3Bhbkxpc3QgPSBzcGFuTGlzdDsKICAgIH0sCgogICAgLy8g5ZCI5bm25Y2V5YWD5qC85pa55rOVCiAgICBvYmplY3RTcGFuTWV0aG9kKHsgcm93LCBjb2x1bW4sIHJvd0luZGV4LCBjb2x1bW5JbmRleCB9KSB7CiAgICAgIC8vIOesrOS4gOWIl+ebuOWQjOmhueWQiOW5tgogICAgICBpZiAoY29sdW1uSW5kZXggPT09IDApIHsKICAgICAgICByZXR1cm4gdGhpcy5zcGFuTGlzdFtyb3dJbmRleF07CiAgICAgIH0KICAgICAgLy8g57G75Yir5peg5YaF5a65IOWQiOW5tgogICAgICBpZihjb2x1bW5JbmRleCA9PT0gMSl7CiAgICAgICAgaWYoIXJvdy5jYXRlZ29yeSl7CiAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICByb3dzcGFuOiAwLAogICAgICAgICAgICBjb2xzcGFuOiAwCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICAgIGlmKGNvbHVtbkluZGV4ID09PSAyKXsKICAgICAgICBpZighcm93LmNhdGVnb3J5KXsKICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIHJvd3NwYW46IDEsCiAgICAgICAgICAgIGNvbHNwYW46IDIKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["leaderCheck.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwKA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "leaderCheck.vue", "sourceRoot": "src/views/assess/self/check", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.assessDate\"\r\n              type=\"month\"\r\n              value-format=\"yyyy-M\"\r\n              format=\"yyyy 年 M 月\"\r\n              placeholder=\"选择考核年月\"\r\n              :clearable=\"false\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入姓名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <el-select v-model=\"queryParams.deptId\" placeholder=\"请选择部门\">\r\n              <el-option\r\n                v-for=\"item in deptOptions\"\r\n                :key=\"item.deptId\"\r\n                :label=\"item.deptName\"\r\n                :value=\"item.deptId\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      \r\n      <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleTabClick\">\r\n        <el-tab-pane :label=\"toCheckLabel\" name=\"toCheck\">\r\n          <el-row :gutter=\"10\" class=\"mb8\">\r\n            <span style=\"font-weight: 600;margin-left: 24px;\">待评分列表</span>\r\n            <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n          </el-row>\r\n          <el-table v-loading=\"loading\" :data=\"listToCheck\">\r\n            <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n            <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n            <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" ></el-table-column>\r\n            <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handleCheckDetail(scope.row)\"\r\n                >评分</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <pagination\r\n            v-show=\"total>0\"\r\n            :total=\"total\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getList\"\r\n          />\r\n        </el-tab-pane>\r\n        <el-tab-pane :label=\"checkedLabel\" name=\"checked\">\r\n          <el-row :gutter=\"10\" class=\"mb8\">\r\n            <span style=\"font-weight: 600;margin-left: 24px;\">已评分列表</span>\r\n            <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getCheckedList\"></right-toolbar>\r\n          </el-row>\r\n          <el-table v-loading=\"loading\" :data=\"listChecked\">\r\n            <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n            <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n            <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n            <el-table-column label=\"评分类型\" align=\"center\" prop=\"type\" >\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.type == '1'\">部门领导评分</span>\r\n                <span v-if=\"scope.row.type == '2'\">事业部领导评分</span>\r\n                <span v-if=\"scope.row.type == '3'\">运改组织部审核</span>\r\n                <span v-if=\"scope.row.type == '4'\">条线领导评分</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"评分时间\" align=\"center\" prop=\"createTime\" />\r\n            <el-table-column label=\"评分\" align=\"center\" prop=\"score\" />\r\n          </el-table>\r\n          <pagination\r\n            v-show=\"checkedTotal>0\"\r\n            :total=\"checkedTotal\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            @pagination=\"getCheckedList\"\r\n          />\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n\r\n      <el-dialog\r\n        :visible.sync=\"open\"\r\n        fullscreen>\r\n        <h3 style=\"text-align: center;\">月度业绩考核表</h3>\r\n          <el-descriptions class=\"margin-top\" :column=\"3\">\r\n            <el-descriptions-item>\r\n              <template slot=\"label\">\r\n                姓名\r\n              </template>\r\n              {{ checkInfo.name }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item>\r\n              <template slot=\"label\">\r\n                部门\r\n              </template>\r\n              {{ checkInfo.deptName }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item>\r\n              <template slot=\"label\">\r\n                考核年月\r\n              </template>\r\n              {{ checkInfo.assessDate }}\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n          <el-table v-loading=\"loading\" :data=\"checkInfo.list\"\r\n            :span-method=\"objectSpanMethod\" border>\r\n            <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n            <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"150\"/>\r\n            <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"180\"/>\r\n            <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n            <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" />\r\n            <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"150\" />\r\n            <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\" width=\"180\" />\r\n          </el-table>\r\n          <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" label-position=\"left\" style=\"margin-top: 10px;\">\r\n            <el-form-item label=\"自评分数 / 签名：\" prop=\"deptId\">\r\n              <span >{{ checkInfo.selfScore + \" 分 / \" + checkInfo.name }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.deptScore && checkInfo.deptUserName\" label=\"部门领导评分 / 签名：\" prop=\"deptId\">\r\n              <span >{{ checkInfo.deptScore + \" 分 / \" + checkInfo.deptUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.businessUserName && checkInfo.businessScore\" label=\"事业部领导评分 / 签名：\" prop=\"deptId\">\r\n              <span>{{ checkInfo.businessScore + \" 分 / \" + checkInfo.businessUserName }}</span>\r\n            </el-form-item>\r\n            <!-- <el-form-item v-if=\"checkInfo.leaderScore && checkInfo.leaderName\" label=\"总经理部领导评分 / 签名：\" prop=\"deptId\">\r\n              <span >{{ checkInfo.leaderScore + \" 分 / \" + checkInfo.leaderName }}</span>\r\n            </el-form-item> -->\r\n            <el-form-item label=\"总经理部领导评分：\">\r\n              <div style=\"display: flex;width: 180px;\">\r\n                <el-input type=\"number\" autosize v-model=\"form.leaderScore\" placeholder=\"请输入评分\" />\r\n                <span>分</span>\r\n              </div>\r\n            </el-form-item>\r\n            <el-form-item label=\"加减分理由：\">\r\n              <div style=\"display: flex;width: 180px;\">\r\n                <el-input type=\"textarea\" autosize v-model=\"form.leaderReview\" placeholder=\"请输入加减分理由\" />\r\n              </div>\r\n            </el-form-item>\r\n          </el-form>\r\n          <div style=\"text-align: center;\">\r\n            <el-button type=\"primary\" @click=\"checkSubmit\">提 交</el-button>\r\n            <el-button plain type=\"info\" @click=\"cancel\">返 回</el-button>\r\n          </div>\r\n      </el-dialog>\r\n\r\n    </div>\r\n  </template>\r\n\r\n  <script>\r\n  import { listLeaderToCheck, listChecked, getInfo, check } from \"@/api/assess/self/info\"\r\n  import { getCheckDeptList } from \"@/api/assess/self/user\";\r\n\r\n  export default {\r\n    name: \"SelfAssessLeaderCheck\",\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 总条数\r\n        total: 0,\r\n        checkedTotal: 0,\r\n        // 绩效考核-干部自评人员配置表格数据\r\n        listToCheck: [],\r\n        listChecked: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          workNo: null,\r\n          name:null,\r\n          deptId:null,\r\n          assessDate:null\r\n        },\r\n        // 表单参数\r\n        form: {\r\n          id:null,\r\n          // 条线领导评分\r\n          leaderScore:null,\r\n        },\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        deptOptions:[],\r\n        openCheck:false,\r\n        checkInfo:{},\r\n        // 合并单元格\r\n        spanList:[],\r\n        activeName:\"toCheck\",\r\n        // 待评分标签\r\n        toCheckLabel:\"待评分(0)\",\r\n        // 已评分\r\n        checkedLabel:\"已评分(0)\"\r\n      };\r\n    },\r\n    created() {\r\n      this.queryParams.assessDate = this.getDefaultAssessDate()\r\n      // this.getSelfAssessUser();\r\n      this.getCheckDeptList();\r\n      this.getList();\r\n      this.getCheckedList();\r\n    },\r\n    methods: {\r\n\r\n      // 获取默认考核日期\r\n      getDefaultAssessDate() {\r\n        const now = new Date();\r\n        const currentDay = now.getDate();\r\n\r\n        let targetDate;\r\n        if (currentDay < 10) {\r\n          // 当前日期小于10日，默认为上个月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n        } else {\r\n          // 当前日期大于等于10日，默认为当月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n        }\r\n\r\n        // 格式化为 YYYY-M 格式\r\n        const year = targetDate.getFullYear();\r\n        const month = targetDate.getMonth() + 1;\r\n        return `${year}-${month}`;\r\n      },\r\n\r\n      // 获取部门信息\r\n      getCheckDeptList(){\r\n        getCheckDeptList().then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            let deptOptions = [];\r\n            res.data.forEach(item => {\r\n              deptOptions.push({\r\n                deptName:item.deptName,\r\n                deptId:item.deptId\r\n              })\r\n            })\r\n            this.deptOptions = deptOptions;\r\n          }\r\n        })\r\n      },\r\n      /** 查询绩效考核-干部自评待审核列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listLeaderToCheck(this.queryParams).then(response => {\r\n          this.listToCheck = response.rows;\r\n          this.total = response.total;\r\n          this.toCheckLabel = `待评分(${response.total})`\r\n          this.loading = false;\r\n        });\r\n      },\r\n      /** 获取已审核列表 */\r\n      getCheckedList(){\r\n        this.loading = true;\r\n        listChecked(this.queryParams).then(res => {\r\n          this.listChecked = res.rows;\r\n          this.checkedTotal = res.total;\r\n          this.checkedLabel = `已评分(${res.total})`\r\n          this.loading = false;\r\n        })\r\n      },\r\n      // 标签页点击事件\r\n      handleTabClick(e){\r\n        let type = e.name;\r\n        if(type == \"checked\"){\r\n          this.getCheckedList();\r\n        }else{\r\n          this.getList();\r\n        }\r\n      },\r\n\r\n      // 取消按钮\r\n      cancel() {\r\n        this.open = false;\r\n        this.reset();\r\n      },\r\n      // 表单重置\r\n      reset() {\r\n        this.form = {\r\n          id: null,\r\n          deptScore: null,\r\n          businessScore: null,\r\n          leaderScore: null,\r\n        };\r\n        // this.resetForm(\"form\");\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.getCheckedList();\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n\r\n      queryAll(){\r\n        this.queryParams.pageNum = 1;\r\n        this.getList();\r\n        this.getCheckedList();\r\n      },\r\n\r\n      // 审批详情\r\n      handleCheckDetail(row){\r\n        getInfo({id:row.id}).then(res => {\r\n          console.log(res);\r\n          if(res.code == 200){\r\n            this.checkInfo = res.data;\r\n            let list = JSON.parse(res.data.content);\r\n            this.handleSpanList(list);\r\n            this.checkInfo.list = list;\r\n          }\r\n          this.open = true\r\n        })\r\n      },\r\n\r\n      // 审批提交\r\n      checkSubmit(){\r\n        if(this.verify()){\r\n          this.form.id = this.checkInfo.id;\r\n          this.form.status = this.checkInfo.status;\r\n          check(this.form).then(res => {\r\n            console.log(res)\r\n            if(res.code == 200){\r\n              this.$message({\r\n                type: 'success',\r\n                message: '提交成功!'\r\n              });\r\n              this.reset();\r\n              this.open = false;\r\n              this.queryAll();\r\n            }else{\r\n              this.$message({\r\n                type: 'warning',\r\n                message: '操作失败，无权限或当前审批状态不匹配'\r\n              });\r\n            }\r\n          })\r\n        }else{\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请填写评分'\r\n          });\r\n          return false;\r\n        }\r\n      },\r\n\r\n      // 数据验证\r\n      verify(){\r\n        if(!this.form.leaderScore) return false;\r\n        return true;\r\n      },\r\n\r\n      handleListChange(type){\r\n        console.log(type)\r\n      },\r\n      // 处理列表\r\n      handleSpanList(data){\r\n        let spanList = [];\r\n        let flag = 0;\r\n        for(let i = 0; i < data.length; i++){\r\n          // 相同考核项合并\r\n          if(i == 0){\r\n            spanList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n          }else{\r\n            if(data[i - 1].item == data[i].item){\r\n              spanList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              spanList[flag].rowspan += 1;\r\n            }else{\r\n              spanList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              flag = i;\r\n            }\r\n          }\r\n        }\r\n        this.spanList = spanList;\r\n      },\r\n\r\n      // 合并单元格方法\r\n      objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n        // 第一列相同项合并\r\n        if (columnIndex === 0) {\r\n          return this.spanList[rowIndex];\r\n        }\r\n        // 类别无内容 合并\r\n        if(columnIndex === 1){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 0,\r\n              colspan: 0\r\n            }\r\n          }\r\n        }\r\n        if(columnIndex === 2){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 1,\r\n              colspan: 2\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  };\r\n  </script>\r\n"]}]}