<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="扎口部门" prop="deptCode">
        <el-cascader
          ref="cascaderHandle"
          :options="deptList"
          clearable
          filterable
          v-model="queryParams.deptCode"
          :props="{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }"
          :show-all-levels="false"
          @change="handleQueryDept"
        >
        <span
              slot-scope="{ node, data }"
              style="margin-left: -10px; padding-left: 10px; display: block"
              @click="clickNode($event, node)"
              >{{ data.label }}</span
            >
        </el-cascader>
      </el-form-item>
      <el-form-item label="报表名称" prop="dimensionalityName">
        <el-input
          v-model="queryParams.dimensionalityName"
          placeholder="请输入名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="填报时间">
                <el-date-picker
                  v-model="queryParams.fcDate"
                  value-format="yyyy-MM-dd"
                  type="date"
                  @change="handleDateChange"
                  placeholder="选择日期">
                </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="是否在用启用" prop="isUse">
        <el-select v-model="queryParams.isUse" placeholder="请选择">
          <el-option label="启用" value="1"></el-option>
          <el-option label="停用" value="0"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新建报表</el-button
        >
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="rootList" border>
      <el-table-column label="扎口部门" align="center" prop="deptName" width="240"/>
      <!-- <el-table-column
        label="扎口部门及人员"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
              size="mini"
              type="text"
              @click="handleaAdminList(scope.row)"
              >{{scope.row.deptName}}</el-button
            >
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="报表名称" align="center" prop="dimensionalityName"/> -->
      <el-table-column
        label="报表名称"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
              size="mini"
              type="text"
              @click="handleAnswer(scope.row)"
              >{{scope.row.dimensionalityName}}</el-button
            >
        </template>
      </el-table-column>
      <el-table-column label="当期完成率" align="center" prop="countRate" width="160"/>
      <el-table-column label="当期应填数量" align="center" prop="shouldCount" width="160" />
      <!-- <el-table-column
        label="当期应填数量"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
              size="mini"
              type="text"
              @click="handlefill(scope.row)"
              >{{scope.row.shouldCount}}</el-button
            >
        </template>
      </el-table-column> -->

      <el-table-column
        label="当期未填数量"
        align="center"
        class-name="small-padding fixed-width"
        width="160"
      >
        <template slot-scope="scope">
          <el-button
              size="mini"
              type="text"
              @click="handlefill(scope.row)"
              >{{scope.row.notCount}}</el-button
            >
        </template>
      </el-table-column>

      <!-- <el-table-column label="当期应填数量" align="center" prop=""  @cell-click="handleDetail(scope.row)"/>
      <el-table-column label="当期未填数量" align="center" prop="hasCount"/> -->
      
      <!-- <el-table-column label="是否在用" align="center" prop="isUse">
        <template slot-scope="scope">
          <el-tag
            style="margin-left: 10px"
            :type="scope.row.isUse == '1'? 'success' : 'danger'"
            >{{ scope.row.isUse == "1" ? "启用" : "停用" }}</el-tag
          >
        </template>
      </el-table-column> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.ruleType != '5' "
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDetail(scope.row)"
            >维度管理</el-button
          >
          <el-button
            v-if="scope.row.ruleType != '5' && scope.row.ruleType != '0'"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDeadLine(scope.row)"
            >截止日期</el-button
          >
          <el-button
            v-if="
                  scope.row.ruleType == '1' ||
                  scope.row.ruleType == '3' ||
                  scope.row.ruleType == '4'
                 "
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="toUpdateUsers(scope.row)"
            >分配权限</el-button
          >
          <el-button
              v-if="
                  scope.row.ruleType == '0' ||
                  scope.row.ruleType == '2' ||
                  scope.row.ruleType == '4' ||
                  scope.row.ruleType == '5'
                 "
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleExport(scope.row)"
            >导出数据</el-button>
            <el-button
              v-if="
                  (scope.row.ruleType == '0' ||
                  scope.row.ruleType == '2' ||
                  scope.row.ruleType == '4' ||
                  scope.row.ruleType == '5') &&
                  aloneList(scope.row.dimensionalityName)
                 "
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleSpecial(scope.row)"
            >单周期数据导出</el-button>
            <el-button
              v-if="
                  (scope.row.ruleType == '0' ||
                  scope.row.ruleType == '2' ||
                  scope.row.ruleType == '4' ||
                  scope.row.ruleType == '5') &&
                  bespokeList(scope.row.dimensionalityName)
                 "
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleMouth(scope.row)"
            >定制化数据导出</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :pageSizes="pageSizes"
      @pagination="getList"
    />

    <el-drawer
      title="详情"
      :visible.sync="drawer"
      direction="rtl"
      size="80%"
      :before-close="handleClose"
    >
      <tree-view :node="detail" @refreshData="getDetail"></tree-view>
    </el-drawer>
    <el-dialog title="选择导出范围" :visible.sync="exportOpen" width="400px" append-to-body destroy-on-close>
      <span>数据日期范围：</span>
        <el-date-picker
          v-model="dateValue"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="onDateChange">
        </el-date-picker>        
      <div slot="footer" class="dialog-footer">
        <div class="el-upload__tip" slot="tip">
          <el-checkbox v-model="noteShow" />是否在导出内容中展示指标修正历史
        </div>
      <el-button type="success" @click="exportDataPreview">预 览</el-button>
      <el-button type="primary" @click="exportData">导 出</el-button>
      <!-- <el-button @click="exportOpen = false">取 消</el-button> -->
    </div>
    </el-dialog>

    
    <el-dialog title="选择导出范围" :visible.sync="mouthImportOpen" width="400px" append-to-body destroy-on-close>
      <span>数据日期范围：</span>
        <el-date-picker
          v-model="dateValue"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          @change="onDateChange">
        </el-date-picker>        
      <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="exportMouthDataPreview">预 览</el-button>
      <el-button type="primary" @click="exportMouthData">导 出</el-button>
      <!-- <el-button @click="mouthImportOpen = false">取 消</el-button> -->
    </div>
    </el-dialog>

    <el-dialog title="选择导出范围" :visible.sync="newOpen" width="400px" append-to-body destroy-on-close>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="名称" prop="dimensionalityName">
          <el-input
            v-model="form.dimensionalityName"
            placeholder="请输入名称"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="扎口部门" prop="deptCode">
          <el-cascader
            :options="deptList"
            clearable
            v-model="form.deptCode"
            :props="{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }"
            :show-all-levels="false"
          >
          </el-cascader>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addClick">确 定</el-button>
        <el-button @click="newOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="adminTitle" :visible.sync="adminOpen" width="1000px" append-to-body>
      <el-table v-loading="loading" :data="userList">
        <el-table-column label="用户工号" align="center" prop="workNo" />
        <el-table-column label="用户姓名" align="center" prop="userName" />
        <!-- <el-table-column label="用户姓名" align="center" prop="userName" /> -->
      </el-table>
    </el-dialog>

    <el-dialog :title="deadlineTitle" :visible.sync="deadlineOpen" width="800px" append-to-body>
      <el-form ref="deadlineForm" :model="deadlineForm" label-width="160px">
        <el-form-item label="截止日期开关" prop="deadlineSwitch">
          <el-switch
            v-model="deadlineForm.deadlineSwitch"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="1"
            inactive-value="0"
            >
          </el-switch>
        </el-form-item>
        <el-form-item v-if=" deadlineForm.deadlineSwitch == '1' " label="截止日期" prop="deadlineDate">
          <el-input type="text"
                    v-model="deadlineForm.deadlineDate" 
                    placeholder="截止日期格式为(年/月/日)"></el-input>
        </el-form-item>
        <el-form-item v-if=" deadlineForm.deadlineSwitch == '1' "  label="邮件通知开关" prop="mailSwitch">
          <el-switch
            v-model="deadlineForm.mailSwitch"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-value="1"
            inactive-value="0"
            >
          </el-switch>
        </el-form-item>
        <el-form-item v-if=" deadlineForm.mailSwitch == '1' " label="通知时间" prop="deadlineDate">
          <el-input type="text"
                    v-model="deadlineForm.countdown" 
                    placeholder="设置在截止日期前几天进行通知"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- <el-dialog
      title="单周期报表导出"
      :visible.sync="SpecialImportOpen"
      width="400px"
      append-to-body
      destroy-on-close
    >
      <span>选择导出时间：</span>
      <el-date-picker
            v-model="specialFcDate"
            value-format="yyyy-MM-dd"
            type="date"
            :default-value="new Date()"
            placeholder="选择时间"
          >
      </el-date-picker>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="downloadTemplateSpecial">数据下载</el-button>
      </div>
    </el-dialog> -->

    <el-dialog title="单周期报表导出" :visible.sync="SpecialImportOpen" width="400px" append-to-body destroy-on-close>
      <span>选择导出时间：</span>
      <el-date-picker
            v-model="specialFcDate"
            value-format="yyyy-MM-dd"
            type="date"
            :default-value="new Date()"
            placeholder="选择时间"
          >
      </el-date-picker>     
      <div slot="footer" class="dialog-footer">
      <el-button type="success" @click="downloadTemplateSpecialPreview">数据预览</el-button>
      <el-button type="primary" @click="downloadTemplateSpecial">数据下载</el-button>
    </div>
    </el-dialog>

    <el-dialog  title="数据预览"  :visible.sync="searchopen" width="1800px" >
        <div class="test">
          <vue-office-excel
              :src="customBlobContent"
              :options="xlsxOptions"
              style="height: 100vh;"
          />
        </div>
    </el-dialog>

  </div>
</template>
  
  <script>
import TreeView from "@/components/TreeView";
import {
  rootListDimensionality,
  getRootListById,
  addDimensionality,
  getStatusListWithadmin
} from "@/api/tYjy/dimensionality";

import {
  listPermission,
} from "@/api/tYjy/dimensionalitypermission";

import {
  deadlinebranch,
  updateForm,
} from "@/api/tYjy/form";

import { listDept } from "@/api/tYjy/dept";
import axios from "axios";
import * as xlsx from 'xlsx';

export default {
  name: "Dimensionality",
  components: {
    TreeView,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      newOpen:false,
      SpecialImportOpen:false,
      mouthImportOpen:false,
      searchopen:false,
      total:0,
      pageSizes:[20,50,100],
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        dimensionalityName: null,
        isUse: null,
      },
      customBlobContent: null,
      rootList: [],
      detail: {},
      rootId:null,
      drawer:false,
      query:{
        startDate:null,
        endDate:null,
        rootId:null,
        title:null,
      },
      exportOpen:false,
      deadlineOpen:false,
      deadlineTitle:"批量修改截止日期",
      deadlineForm:
      {
        dimensionalityPath:null
      },
      dateValue:null,
      deptList: [],
      form:{},
      userList:[],
      adminOpen:false,
      adminTitle:"管理员名单",
      specialFcDate:null,
      dimensionalityName:null,
      dimensionalityId:null,
      noteShow:false, //是否展示指标

      xlsxOptions:{
                // xls: false,       //预览xlsx文件设为false；预览xls文件设为true
                // minColLength: 0,  // excel最少渲染多少列，如果想实现xlsx文件内容有几列，就渲染几列，可以将此值设置为0.
                // minRowLength: 0,  // excel最少渲染多少行，如果想实现根据xlsx实际函数渲染，可以将此值设置为0.
                // widthOffset: 10,  //如果渲染出来的结果感觉单元格宽度不够，可以在默认渲染的列表宽度上再加 Npx宽
                // heightOffset: 10, //在默认渲染的列表高度上再加 Npx高
                beforeTransformData: (workbookData) => {return workbookData}, //底层通过exceljs获取excel文件内容，通过该钩子函数，可以对获取的excel文件内容进行修改，比如某个单元格的数据显示不正确，可以在此自行修改每个单元格的value值。
                transformData: (workbookData) => {return workbookData}, //将获取到的excel数据进行处理之后且渲染到页面之前，可通过transformData对即将渲染的数据及样式进行修改，此时每个单元格的text值就是即将渲染到页面上的内容
            },
    };
  },
  created() {
    this.getList();
    this.getDept();
  },
  methods: {
    clickNode($event, node) {
      $event.target.parentElement.parentElement.firstElementChild.click();
    },
    getList() {
      this.loading = true;
      getStatusListWithadmin(this.queryParams).then((res) => {
        this.rootList = res.rows;
        for(let i=0;i<this.rootList.length;i++)
        {
          if(this.rootList[i].id==273 || this.rootList[i].id==840 || this.rootList[i].id==873 || this.rootList[i].id==1077 || this.rootList[i].id==1059 || this.containsSubstring('安全责任工资',this.rootList[i].dimensionalityName))
          {
            this.rootList[i].showboot=1
          }
          else
          {
            this.rootList[i].showboot=0
          }
          if(this.containsSubstring('工装',this.rootList[i].dimensionalityName))
          {
            this.rootList[i].showMouth=1
          }
          else
          {
            this.rootList[i].showMouth=0
          }
        }
        this.total = res.total;
        this.loading = false;
      });
      // rootListDimensionality(this.queryParams).then((res) => {
      //   this.rootList = res.rows;
      //   this.total = res.total;
      //   this.loading = false;
      // });
    },
    getDept() {
      listDept().then((res) => {
        this.deptList = res.rows[0].children;
        console.log(res);
        for(let i=0;i<this.deptList.length;i++)
        {
          this.dealdeptList(this.deptList[i],0)
        }
      });
    },
    dealdeptList(row,count)
    {
       row.value=row.path
       row.label=row.deptName
       if(row.children.length>0 && count<1)
       {
          for(let i=0;i<row.children.length;i++)
          {
            this.dealdeptList(row.children[i],count+1)
          }
       }
       else
       {
          row.children=null
       }
    },
    handleQueryDept() {
      this.$refs.cascaderHandle.dropDownVisible = false;
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },


    handleAdd() {
      this.newOpen=true
      // let that = this;
      // this.$prompt("请输入名称", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      // })
      //   .then(({ value }) => {
      //     let form = {};
      //     form.dimensionalityName = value;
      //     addDimensionality(form).then((res) => {
      //       that.getList();
      //     });
      //   })
      //   .catch(() => {
      //     that.$message({
      //       type: "info",
      //       message: "取消操作",
      //     });
      //   });
    },
    handleDetail(row){
      this.rootId = row.id;
      this.rootRuleType = row.ruleType;
      this.getDetail();
      this.drawer = true;
    },
    handleDeadLine(row){
      this.deadlineForm={dimensionalityPath:null}
      this.deadlineForm.dimensionalityPath=row.path
      this.deadlineOpen=true
    },
    /** 提交按钮 */
    submitForm() {
      if(this.deadlineForm.deadlineSwitch==1)
      {
        if(this.deadlineForm.deadlineDate==null)
        {
          this.$modal.msgError("截止日期不能为空");
          return
        }
        let deadlineDateCheck=this.deadlineForm.deadlineDate.split("/")
        if(deadlineDateCheck.length!=3)
        {
          this.$modal.msgError("截止日期格式不正确，正确格式是 年/月/日 ");
          return
        }
        if(!/^-?(0|([1-9]?\d)|100)$/.test(deadlineDateCheck[0]))
        {
          this.$modal.msgError("截止日期中年应是在-100到100之间的整数");
          return
        }
        if(!/^-?(0|([0]?\d)|11|12)$/.test(deadlineDateCheck[1]))
        {
          this.$modal.msgError("截止日期中月应是在-12到12之间的整数");
          return
        }
        if(!/^-?(0|([1-2]?\d)|31|30)$/.test(deadlineDateCheck[2]))
        {
          this.$modal.msgError("截止日期中日应是在-31到31之间的整数");
          return
        }
      }
      deadlinebranch(this.deadlineForm).then((response) => 
      {
        this.msgSuccess("批量修改截止日期成功");
        this.deadlineOpen = false;
      });
    },

    cancel() {
      this.deadlineOpen = false;
    },

    getDetail(){
    getRootListById({id : this.rootId,ruleType:this.rootRuleType}).then(res => {
        this.detail = res.data;
        if(this.detail == null || this.detail == undefined)this.detail = {}
        console.log(this.detail)
        this.$forceUpdate();
      });
    },
    handleClose(){
      this.drawer = false;
      this.getList();
      this.$forceUpdate();
    },
    handleExport(row){
      this.query.rootId  = row.id;
      this.query.title = row.dimensionalityName;
      this.clickChangeTime();
      this.exportOpen = true;
    },

    addClick() {
      // this.form.deptId=parseInt(this.form.deptId.split(",")[-1])
      addDimensionality(this.form).then((res) => {
        this.newOpen = false;
        this.getList();
        this.form={};
      });
    },
    exportData() {
      if (
        this.query.startDate == null ||
        this.query.startDate == ""||
        this.query.endDate == null||
        this.query.endDate == ""
      ) {
        this.$notify.error({
          title: "错误",
          message: "导出前请先输入开始结束时间",
        });
        return;
      }
      this.query.noteShow=this.noteShow
      this.downloadFile(
        "/web/TYjy/dimensionality/exportStatistics",
        {
          ...this.query,
        },
        "(" +
          this.query.startDate +
          "-" +
          this.query.endDate +
          ")" +
          this.query.title +
          `.xlsx`
      );
    },
    exportDataPreview()
    {
      if (
        this.query.startDate == null ||
        this.query.startDate == ""||
        this.query.endDate == null||
        this.query.endDate == ""
      ) {
        this.$notify.error({
          title: "错误",
          message: "导出前请先输入开始结束时间",
        });
        return;
      }
      this.query.noteShow=this.noteShow
      this.downloadXlsx(
        "/web/TYjy/dimensionality/exportStatistics",
        {
          ...this.query,
        },
        this.dimensionalityName+"(" +this.specialFcDate+
          ")" +
          `数据.xlsx`
      ).then((blob) => {
        let reader = new FileReader();
        reader.readAsArrayBuffer(blob);
        reader.onload = (evt) => {
          this.customBlobContent=evt.target.result;

          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array
          ints = ints.slice(0, blob.size);
          let workBook = xlsx.read(ints, { type: "array" });
          


          let sheetNames = workBook.SheetNames;
          let sheetName = sheetNames[0];
          let workSheet = workBook.Sheets[sheetName];
          //获取Excle内容，并将空内容用空值保存
          let excelTable = xlsx.utils.sheet_to_json(workSheet);
          // 获取Excel头部
          let tableThead = Array.from(Object.keys(excelTable[0])).map(
            (item) => {
              return item
            }
          );
          this.excelData = excelTable;
          this.exceltitle=tableThead
          this.excelHtml= excelTable
          this.searchopen = true;
        }
      });
    },
    exportMouthDataPreview(){
      if (
        this.query.startDate == null ||
        this.query.startDate == ""||
        this.query.endDate == null||
        this.query.endDate == ""
      ) {
        this.$notify.error({
          title: "错误",
          message: "导出前请先输入开始结束时间",
        });
        return;
      }
      this.query.rootId = this.dimensionalityId
      this.query.type="1"
      this.query.isUpdate="1"
      let path="/web/TYjy/answer/exportEverymouth"
      if(this.dimensionalityId==1028)
      {
        path="/web/TYjy/answer/exportEverymouth"
      }
      if(this.dimensionalityId==748)
      {
        path="/web/TYjy/answer/exportWithTemplate1"
      }
      this.downloadXlsx(
          path,
          {
            ...this.query,
          },
          this.dimensionalityName+"(" +this.specialFcDate+
            ")" +
            `数据.xlsx`
        ).then((blob) => {
          let reader = new FileReader();
          reader.readAsArrayBuffer(blob);
          
          reader.onload = (evt) => {
            this.customBlobContent=evt.target.result;
            // let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array
            // ints = ints.slice(0, blob.size);
            // let workBook = xlsx.read(ints, { type: "array" });
            // let sheetNames = workBook.SheetNames;
            // let sheetName = sheetNames[0];
            // let workSheet = workBook.Sheets[sheetName];
            // //获取Excle内容，并将空内容用空值保存
            // let excelTable = xlsx.utils.sheet_to_json(workSheet);
            // // 获取Excel头部
            // let tableThead = Array.from(Object.keys(excelTable[0])).map(
            //   (item) => {
            //     return item
            //   }
            // );
            // this.excelData = excelTable;
            // this.exceltitle=tableThead
            // this.excelHtml= excelTable
            this.searchopen = true;
          }
        });
    },
    exportMouthData(){
      if (
        this.query.startDate == null ||
        this.query.startDate == ""||
        this.query.endDate == null||
        this.query.endDate == ""
      ) {
        this.$notify.error({
          title: "错误",
          message: "导出前请先输入开始结束时间",
        });
        return;
      }
      this.query.rootId = this.dimensionalityId
      this.query.type="1"
      // this.query.isUpdate="1"
      let path="/web/TYjy/answer/exportEverymouth"
      if(this.dimensionalityId==1028)
      {
        path="/web/TYjy/answer/exportEverymouth"
      }
      if(this.dimensionalityId==748)
      {
        path="/web/TYjy/answer/exportWithTemplate1"
      }
      this.downloadFile(
        path,
        {
          ...this.query,
        },
        "(" +
          this.query.startDate +
          "-" +
          this.query.endDate +
          ")" +
          this.dimensionalityName +
          `.xlsx`
      );
    },
    onDateChange(){
      console.log(this.dateValue)
      if(this.dateValue != null && this.dateValue != ""){
        this.query.startDate = this.dateValue[0] ;
        this.query.endDate = this.dateValue[1];
      }else{
        this.query.startDate = "";
        this.query.endDate = "";
      }
    },
    toUpdateUsers(row){
      const dimensionalityId = row.id;
      this.$router.push("/dataReport/dimensionality-auth/dimensionalityPermission/" + dimensionalityId);
      // this.$router.go(0)
    },
    handleDateChange() {
      this.getList();
    },
    handleaAdminList(row){

      const dimensionalityId = row.id;
      listPermission({dimensionalityId:dimensionalityId}).then((response) => {
        this.userList = response.rows;
        // this.total = response.total;
        // this.loading = false;
        this.adminOpen = true;
      });

      // const fcDate = this.queryParams.fcDate;
      // const dimensionalityName = row.dimensionalityName;
      // this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus', query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });
    },
    handlefill(row){
      // const dimensionalityId = row.id;
      // this.$router.push("/dataReport/adminfill-auth/adminfillstatus/" + dimensionalityId);
      const dimensionalityId = row.id;
      const fcDate = this.queryParams.fcDate;
      const dimensionalityName = row.dimensionalityName;
      this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });
    },
    handleAnswer(row){
      // const dimensionalityId = row.id;
      // this.$router.push("/dataReport/adminfill-auth/adminfillstatus/" + dimensionalityId);
      const dimensionalityId = row.id;
      const fcDate = this.queryParams.fcDate;
      const dimensionalityName= row.dimensionalityName;
      this.$router.push({ path: '/dataReport/answerShow-auth/answerShow/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });
    },

  handleSpecial(row){
      // this.query.rootId  = row.id;
      this.dimensionalityName = row.dimensionalityName;
      this.dimensionalityId=row.id;
      this.SpecialImportOpen = true;
      
    },
  handleMouth(row){
      // this.query.rootId  = row.id;
      this.dimensionalityName = row.dimensionalityName;
      this.dimensionalityId=row.id;
      this.clickChangeTime();
      this.mouthImportOpen = true;
    },
  downloadTemplateSpecialPreview(){
    if (this.specialFcDate == null ) {
        this.specialFcDate= this.queryParams.fcDate
      }

      // if (this.specialFcDate == null ) {
      //   this.$notify.error({
      //     title: "错误",
      //     message: "未选择时间",
      //   });
      //   return;
      // }
      let queryImport={}
      queryImport.rootId = this.dimensionalityId
      queryImport.fcDate = this.specialFcDate
      queryImport.type="1"
      queryImport.isUpdate="1"
      let url=""
      if(this.dimensionalityName=='研究院目标指标一览')
      {
        url="/web/TYjy/answer/exportWithTemplate"
      }
      else
      {
        url="/web/TYjy/answer/exportTemplateSpecial"
      }
      this.downloadXlsx(
          url,
          {
            ...queryImport,
          },
          this.dimensionalityName+"(" +this.specialFcDate+
            ")" +
            `数据.xlsx`
        ).then((blob) => {
          let reader = new FileReader();
          reader.readAsArrayBuffer(blob);
          
          reader.onload = (evt) => {
            this.customBlobContent=reader.result;
            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array
            ints = ints.slice(0, blob.size);
            let workBook = xlsx.read(ints, { type: "array" });
            let sheetNames = workBook.SheetNames;
            let sheetName = sheetNames[0];
            let workSheet = workBook.Sheets[sheetName];
            //获取Excle内容，并将空内容用空值保存
            let excelTable = xlsx.utils.sheet_to_json(workSheet);
            // 获取Excel头部
            let tableThead = Array.from(Object.keys(excelTable[0])).map(
              (item) => {
                return item
              }
            );
            this.excelData = excelTable;
            this.exceltitle=tableThead
            this.excelHtml= excelTable
            this.searchopen = true;
          }
        });
  },
  downloadTemplateSpecial(){
      if (this.specialFcDate == null ) {
        this.specialFcDate= this.queryParams.fcDate
      }

      // if (this.specialFcDate == null ) {
      //   this.$notify.error({
      //     title: "错误",
      //     message: "未选择时间",
      //   });
      //   return;
      // }
      let queryImport={}
      queryImport.rootId = this.dimensionalityId
      queryImport.fcDate = this.specialFcDate
      queryImport.type="1"
      let url=""
      if(this.dimensionalityName=='研究院目标指标一览')
      {
        url="/web/TYjy/answer/exportWithTemplate"
      }
      else
      {
        url="/web/TYjy/answer/exportTemplateSpecial"
      }
      this.downloadFile(
        url,
        {
          ...queryImport,
        },
        this.dimensionalityName+"(" +this.specialFcDate+
          ")" +
          `数据.xlsx`
      );
    },

    //此处为按钮判断管理
    containsSubstring(substring, string) 
    {
      return string.includes(substring);
    },

    bespokeList(string)
    {
      if(string== '2025年专利月报')
      {
        return true;
      }
      if(string== '工装上线验收合格率统计')
      {
        return true;
      }
      // if(string== '2025年经济责任制技经指标')
      // {
      //   return true;
      // }
      // if(string== '研究院技经提升指标跟踪')
      // {
      //   return true;
      // }
      return false;
    },
    //支持预览和导出
    mouthCheck(string)
    {
      if(string== '六化指标')
      {
        return true;
      }
      if(string== '工装上线验收合格率统计')
      {
        return true;
      }
      if(string== '2025年经济责任制技经指标')
      {
        return true;
      }
      if(string== '研究院技经提升指标跟踪')
      {
        return true;
      }
      return false;
    },
    //支持单周期导出
    aloneList(string) {
      if(string== '气体结算月报')
      {
        return true;
      }
      if(string== '高炉、转炉煤气月报表')
      {
        return true;
      }
      if(string== '天然气消耗月报表')
      {
        return true;
      }
      if(string== '蒸汽消耗月报表')
      {
        return true;
      }
      if(string== '电量月报表')
      {
        return true;
      }
      if(string== '2025年经济责任制考核表（特板事业部）')
      {
        return true;
      }
      if(string== '研究院目标指标一览')
      {
        return true;
      }
      if(string== '安全责任工资考核表')
      {
        return true;
      }
      if(string== '安全责任工资考核汇总')
      {
        return true;
      }
      if(string== '水处理水量报表')
      {
        return true;
      }
      return false;
    },

    //数据预览模块处理
    handlePreview() {
      let queryImport={}
      queryImport.rootId = this.queryParams.dimensionalityId
      queryImport.fcDate = this.queryParams.fcDate
      queryImport.type="1"
      queryImport.isUpdate="1"
      if(this.dimensionalityName=='研究院目标指标一览')
      {
          this.downloadXlsx(
          "/web/TYjy/answer/exportWithTemplate",
          {
            ...queryImport,
          },
          this.dimensionalityName+"(" +this.specialFcDate+
            ")" +
            `数据.xlsx`
        ).then((blob) => {
          let reader = new FileReader();
          reader.readAsArrayBuffer(blob);
          
          reader.onload = (evt) => {
            this.customBlobContent=reader.result;
            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array
            ints = ints.slice(0, blob.size);
            let workBook = xlsx.read(ints, { type: "array" });
            let sheetNames = workBook.SheetNames;
            let sheetName = sheetNames[0];
            let workSheet = workBook.Sheets[sheetName];
            //获取Excle内容，并将空内容用空值保存
            let excelTable = xlsx.utils.sheet_to_json(workSheet);
            // 获取Excel头部
            let tableThead = Array.from(Object.keys(excelTable[0])).map(
              (item) => {
                return item
              }
            );
            this.excelData = excelTable;
            this.exceltitle=tableThead
            this.excelHtml= excelTable
            this.searchopen = true;
          }
        });
      }
      else
      {
        this.downloadXlsx(
        "/web/TYjy/answer/exportTemplateSpecial",
        {
          ...queryImport,
        },
        this.dimensionalityName+"(" +this.specialFcDate+
          ")" +
          `数据.xlsx`
      ).then((blob) => {
        let reader = new FileReader();
        reader.readAsArrayBuffer(blob);
        reader.onload = (evt) => {
          this.customBlobContent=reader.result;
          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array
          ints = ints.slice(0, blob.size);
          let workBook = xlsx.read(ints, { type: "array" });
          let sheetNames = workBook.SheetNames;
          let sheetName = sheetNames[0];
          let workSheet = workBook.Sheets[sheetName];
          //获取Excle内容，并将空内容用空值保存
          let excelTable = xlsx.utils.sheet_to_json(workSheet);
          // 获取Excel头部
          let tableThead = Array.from(Object.keys(excelTable[0])).map(
            (item) => {
              return item
            }
          );
          this.excelData = excelTable;
          this.exceltitle=tableThead
          this.excelHtml= excelTable
          this.searchopen = true;
        }
      });
      }
    },
    // 时间段预览
    handlePreview1() {
      // let queryImport={}
      // queryImport.rootId = this.queryParams.dimensionalityId
      // queryImport.fcDate = this.queryParams.fcDate
      // queryImport.type="1"
        if (
        this.queryImport.startDate == null ||
        this.queryImport.startDate == ""||
        this.queryImport.endDate == null||
        this.queryImport.endDate == ""
      ) {
        this.$notify.error({
          title: "错误",
          message: "导出前请先输入开始结束时间",
        });
        return;
      }
      this.queryImport.rootId = this.queryParams.dimensionalityId
      this.queryImport.type="1"
      this.downloadXlsx(
        "/web/TYjy/answer/exportTemplateNomral",
        {
          ...this.queryImport,
        },
        this.dimensionalityName+"(" +this.specialFcDate+
          ")" +
          `数据.xlsx`
      ).then((blob) => {
        let reader = new FileReader();
        reader.readAsArrayBuffer(blob);
        
        reader.onload = (evt) => {
          this.customBlobContent=reader.result;
          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array
          ints = ints.slice(0, blob.size);
          let workBook = xlsx.read(ints, { type: "array" });
          let sheetNames = workBook.SheetNames;
          let sheetName = sheetNames[0];
          let workSheet = workBook.Sheets[sheetName];
          //获取Excle内容，并将空内容用空值保存
          let excelTable = xlsx.utils.sheet_to_json(workSheet);
          // 获取Excel头部
          let tableThead = Array.from(Object.keys(excelTable[0])).map(
            (item) => {
              return item
            }
          );
          this.excelData = excelTable;
          this.exceltitle=tableThead
          this.excelHtml= excelTable
          this.searchopen = true;
        }
      });
    },

    clickChangeTime()
    {
         let now =new Date();
         this.query.startDate=this.getFirstOfYear(now);
         this.query.endDate=this.getFirstOfMonth(now);
         this.dateValue=[];
         this.dateValue.push(this.query.startDate);
         this.dateValue.push(this.query.endDate);
    },
    // 获取时间的优化处理
    getFirstOfYear(now)
    {
      let  firstDayOfYear = new Date(now.getFullYear(), 0, 1);
      return this.formatDate(firstDayOfYear);
    },
    getFirstOfMonth(now)
    {
      let firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      return this.formatDate(firstDayOfMonth);
    },
    // 日期格式化函数（转为 yyyy-MM-dd）
    formatDate(date) 
    {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始需+1
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

  },
};
</script>
<style lang="less">
.v-modal {
  display: none;
}
</style>
  