{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardStock\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardStock\\index.vue", "mtime": 1756456493859}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_chartMethods", "_interopRequireDefault", "_chartMethodsExtended", "_screenfull", "_purchaseDashboard", "_similar", "_purdchaseFactoryStock", "name", "mixins", "chartMethods", "extendedChartMethods", "data", "_ref", "timeFilters", "id", "label", "value", "activeFilter", "currentDimensionType", "dashboardData", "purchaseStats", "topSuppliersOptions", "selectedTop<PERSON>uppliers<PERSON><PERSON>er", "selectedOrderType", "chartInstances", "originalTopSuppliersData", "selected<PERSON>ear", "selectedMaterialType", "availableYears", "currentYear", "Date", "getFullYear", "years", "year", "push", "toString", "yearlyInventoryData", "realTimeInventoryData", "cokingCoalInventoryData", "selectedCokingCoalType", "selectedMaterialCategory", "selectedMaterialItem", "materialItemOptions", "materialStatisticsData", "selectedCodeType", "selectedItemType", "highFrequencyMaterialData", "supplierRiskData", "pricePredictions", "predictionLoading", "materialNameOptions", "selectedMaterial", "_defineProperty2", "default", "computed", "isFullscreen", "$store", "state", "app", "isFullscreenMode", "groupedSimilarMaterials", "_this", "grouped", "similarMaterialsData", "for<PERSON>ach", "item", "groupKey", "concat", "itemName", "category", "priceType", "displayKey", "getCategoryName", "getPriceTypeName", "items", "Object", "keys", "key", "sort", "a", "b", "rank", "mounted", "_this2", "checkEchartsAvailability", "fetchDashboardData", "fetchYearlyInventoryData", "fetchRealTimeInventoryData", "fetchCokingCoalInventoryData", "updateMaterialItemOptions", "then", "fetchMaterialStatisticsData", "fetchHighFrequencyMaterialData", "fetchSupplierRiskData", "fetchPurchaseAmountMaterialList", "fetchMarketPriceMaterialList", "fetchFactoryDepOptions", "setupResizeObserver", "initFullscreenListener", "window", "addEventListener", "resize<PERSON>ll<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "clearAllIntervals", "removeFullscreenListener", "removeEventListener", "dispatch", "methods", "screenfull", "isEnabled", "on", "handleFullscreenChange", "off", "_this3", "$nextTick", "setTimeout", "getDashboardData", "dimensionType", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "w", "_context", "n", "showData", "v", "getItemTypeList", "itemType", "_callee2", "_context2", "showItemTypeList", "getMaterialList", "params", "_callee3", "_context3", "showMaterialList", "getSupplierList", "_callee4", "_context4", "showSuppList", "getYearlyAmount", "_callee5", "_context5", "showYearlyAmount", "getRealTimeAmount", "_callee6", "_context6", "showRealTimeAmount", "getCokingCoalAmount", "_callee7", "_context7", "showCokingCoalAmount", "getKeyIndicators", "_callee8", "_context8", "showKeyIndicators", "getHighFrequencyMaterialList", "_callee9", "_context9", "showHighFrequencyMaterialList", "getPurchaseSuppRisk", "_callee0", "_context0", "showPurchaseSuppRisk", "getTimeFlagByDimensionType", "console", "error", "document", "querySelectorAll", "el", "innerHTML", "_arguments", "arguments", "_this4", "_callee1", "dimensionTypeParam", "_yield$Promise$all", "_yield$Promise$all2", "dashboardResponse", "keyIndicatorsResponse", "_t", "_context1", "length", "undefined", "p", "Promise", "all", "_slicedToArray2", "log", "showErrorMessage", "initAllCharts", "message", "chart", "handleTimeFilterChange", "filterId", "values", "instance", "intervalId", "clearInterval", "resize", "err", "initMaterialCloud", "initTopSuppliersChart", "populateItemDropdown", "selectElementId", "dataPropertyName", "_this5", "_callee10", "response", "_t2", "_context10", "Array", "isArray", "handleTopSuppliersFilterChange", "_this6", "_callee11", "_context11", "refreshTopSuppliersChart", "handleOrderTypeChange", "_this7", "_callee12", "_context12", "_this8", "_callee13", "myChart", "newSupplierData", "_response", "_newSupplierData", "_t3", "_t4", "_context13", "topSuppliersChart", "showLoading", "orderType", "renderAndPaginateTopSuppliers", "hideLoading", "f", "itemId", "getElementById", "resizeObserver", "ResizeObserver", "entries", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "done", "entry", "charts", "target", "getInstanceByDom", "e", "card", "observe", "toggleFullscreen", "toggle", "$message", "type", "handleYearChange", "_this9", "_callee14", "_context14", "handleMaterialTypeChange", "_this0", "_callee15", "_context15", "_this1", "_callee16", "_t5", "_context16", "materialType", "yearList", "getMockYearlyData", "initMonthlyInventoryChart", "monthlyResultVoList", "monthIndex", "amount", "_this10", "_callee17", "_t6", "_context17", "getMockRealTimeData", "initRealTimeInventoryChart", "materialName", "centerInventoryAmount", "machineSideInventoryAmount", "totalInventoryAmount", "_this11", "_callee18", "_t7", "_context18", "initCokingCoalInventoryChart", "handleMaterialCategoryChange", "_this12", "_callee19", "_context19", "handleMaterialItemChange", "_this13", "_callee20", "_context20", "_this14", "_callee21", "_t8", "_context21", "parseInt", "_this15", "_callee22", "_t9", "_context22", "getMockMaterialStatisticsData", "initMaterialStatisticsChart", "inAmt", "arriveRate", "_this16", "_callee23", "_t0", "_context23", "codeType", "getMockHighFrequencyData", "inNum", "handleCodeTypeChange", "_this17", "_callee24", "_context24", "handleItemTypeChange", "_this18", "_callee25", "_context25", "_this19", "_callee26", "_t1", "_context26", "timeFlag", "initSupplierRiskChart", "fetchMultiplePricePredictions", "materialNames", "_this20", "_callee28", "predictionPromises", "results", "successCount", "totalCount", "_t11", "_context28", "map", "_ref2", "_callee27", "_t10", "_context27", "getMaterialFuturePrice", "code", "question", "prediction", "answer", "msg", "success", "_x", "apply", "filter", "r", "fetchMaterialNameList", "_this21", "_callee29", "pbMaterial", "_t12", "_context29", "getMaterialNameList", "find", "fetchPriceAndStoreData", "_this22", "_callee30", "_t13", "_context30", "getPurchasePriceAndStore", "priceAndStoreData", "initPriceTrendChart", "handleMaterialCategoryTypeChange", "_this23", "_callee31", "_context31", "handleMaterialChange", "_this24", "_callee32", "_context32", "calculateRealTimeInventoryTotal", "total", "parseFloat", "toFixed", "calculateCokingCoalTotal", "latestDate", "purchaseCokingDailyDetailList", "detail", "instockDate", "latestDetail", "invQty", "handleCokingCoalTypeChange", "_this25", "_callee33", "_context33", "_this26", "_callee34", "_t14", "_context34", "getDepNameList", "factoryDepOptions", "fetchFactoryStockData", "handleFactoryDepChange", "_this27", "_callee35", "_context35", "selectedFactoryDep", "handleFactoryMaterialTypeChange", "_this28", "_callee36", "_context36", "selectedFactoryMaterialType", "initFactoryStockChart", "_this29", "_callee37", "depName", "_t15", "_context37", "getListMonthly", "factoryStockData", "handlePurchaseAmountCategoriesChange", "_this30", "_callee38", "_context38", "purchaseAmountCategories", "selectedPurchaseAmountMaterials", "handleMarketPriceCategoriesChange", "_this31", "_callee39", "_context39", "marketPriceCategories", "selectedMarketPriceMaterials", "_this32", "_callee40", "_t16", "_context40", "categories", "curveType", "getMaterialNameListFromNewTables", "purchaseAmountMaterialOptions", "hasInitializedPriceChart", "checkAndTriggerInitialDataFetch", "_this33", "_callee41", "_t17", "_context41", "marketPriceMaterialOptions", "fetchPriceAndStoreDataForNewChart", "_this34", "_callee42", "itemList", "allSelectedMaterials", "_t18", "_context42", "warning", "fetchingPriceData", "getPurchasePriceAndStoreFromNewTables", "newPriceAndStoreData", "initNewPriceTrendChart", "_toConsumableArray2", "Set", "fetchSimilarMaterials", "itemNames", "_this35", "_callee43", "_t19", "_context43", "similarMaterialsLoading", "listSimilarByItemNames", "getRankClass", "categoryMap", "priceTypeMap", "openComparisonDialog", "_this36", "currentComparison", "_objectSpread2", "comparisonDialogVisible", "fetchComparisonData", "closeComparisonDialog", "comparisonPriceData", "comparisonChartInstance", "dispose", "_this37", "_callee44", "filteredData", "basePriceTypeName", "comparePriceTypeName", "_t20", "_context44", "comparisonChartLoading", "compareItemName", "comparePriceType", "baseItemName", "materialData", "filteredMaterialData", "procurementPriceVoList", "priceGroup", "isMatch", "priceName", "totalMaterials", "materials", "_m$procurementPriceVo", "_m$procurementPriceVo2", "priceGroupCount", "priceGroups", "renderComparisonChart", "chartDom", "init", "formatDate", "dateStr", "substring", "month", "day", "allDates", "priceList", "add", "recordDate", "from", "xAxisData", "series", "legendData", "colors", "colorIndex", "materialCount", "baseMaterial", "compareMaterial", "index", "_priceGroup$priceList", "groupIndex", "priceData", "date", "found", "price", "validDataCount", "uniqueName", "lineColor", "dataStr", "JSON", "stringify", "existingSeries", "adjustedData", "some", "smooth", "lineStyle", "width", "color", "itemStyle", "symbol", "symbolSize", "connectNulls", "z", "i", "validCount", "priceMin", "priceMax", "priceValues", "flatMap", "Math", "min", "max", "option", "backgroundColor", "tooltip", "trigger", "axisPointer", "formatter", "str", "axisValueLabel", "marker", "seriesName", "legend", "textStyle", "top", "grid", "left", "right", "bottom", "containLabel", "xAxis", "axisLabel", "interval", "uniqueMonths", "monthsCount", "size", "totalDataPoints", "idealInterval", "floor", "originalDateStr", "axisLine", "yAxis", "splitLine", "setOption", "_this38", "includes"], "sources": ["src/views/purchaseDashboardStock/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <div class=\"dashboard-header\">\r\n      <h1>采购库存看板</h1>\r\n      <div class=\"header-controls\">\r\n        <div class=\"fullscreen-btn\" @click=\"toggleFullscreen\" :title=\"isFullscreen ? '退出全屏' : '进入全屏'\">\r\n          <i :class=\"isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'\"></i>\r\n        </div>\r\n        <div class=\"time-filter\">\r\n          <button\r\n            v-for=\"filter in timeFilters\"\r\n            :key=\"filter.id\"\r\n            :class=\"['time-filter-btn', { active: filter.id === activeFilter }]\"\r\n            @click=\"handleTimeFilterChange(filter.id, filter.value)\"\r\n          >\r\n            {{ filter.label }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"dashboard-grid\">\r\n      <!-- 第一行：中心仓库月度库存金额 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          中心仓库月度库存金额\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedYear\"\r\n              @change=\"handleYearChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部年份</option>\r\n              <option v-for=\"year in availableYears\" :key=\"year\" :value=\"year\">\r\n                {{ year }}年\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedMaterialType\"\r\n              @change=\"handleMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">总和</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"monthlyInventoryChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第一行：机旁库当前库存 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          机旁库当前库存\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedFactoryDep\"\r\n              @change=\"handleFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedFactoryMaterialType\"\r\n              @change=\"handleFactoryMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"factoryStockChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第二行：矿焦煤实时库存 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          <div style=\"display: flex; align-items: center; justify-content: space-between; width: 100%;\">\r\n            <div style=\"display: flex; align-items: center; gap: 15px;\">\r\n              <span>矿焦煤实时库存</span>\r\n              <span class=\"inventory-total\">\r\n                合计: {{ calculateCokingCoalTotal() }}万吨\r\n              </span>\r\n            </div>\r\n            <div class=\"chart-filter-dropdown-container\">\r\n              <select\r\n                v-model=\"selectedCokingCoalType\"\r\n                @change=\"handleCokingCoalTypeChange\"\r\n              >\r\n                <option value=\"\">全部</option>\r\n                <option value=\"矿料类\">矿料类</option>\r\n                <option value=\"焦炭\">焦炭</option>\r\n                <option value=\"煤焦类\">煤焦类</option>\r\n                <option value=\"合金类\">合金类</option>\r\n                <option value=\"辅助类/电极\">辅助类/电极</option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n        </h2>\r\n        <div class=\"chart\" style=\"display: flex; height: 100%;\">\r\n          <div id=\"cokingCoalPieChart\" style=\"width: 25%; height: 100%;\"></div>\r\n          <div id=\"cokingCoalLineChart\" style=\"width: 75%; height: 100%;\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 物料入库统计 -->\r\n      <div class=\"card material-chart-card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          物料入库统计\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedMaterialCategory\"\r\n              @change=\"handleMaterialCategoryChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"1\">大类</option>\r\n              <option value=\"2\">中类</option>\r\n              <option value=\"3\">细类</option>\r\n              <option value=\"4\">叶类</option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedMaterialItem\"\r\n              @change=\"handleMaterialItemChange\"\r\n            >\r\n              <option value=\"\">全部</option>\r\n              <option v-for=\"item in materialItemOptions\" :key=\"item.itemId\" :value=\"item.itemId\">\r\n                {{ item.itemName }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"materialStatisticsChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- 物料价格对比弹框 -->\r\n    <el-dialog\r\n      title=\"物料价格趋势对比分析\"\r\n      :visible.sync=\"comparisonDialogVisible\"\r\n      width=\"90%\"\r\n      :before-close=\"closeComparisonDialog\"\r\n      custom-class=\"comparison-dialog\"\r\n    >\r\n      <div class=\"comparison-content\">\r\n        <div class=\"comparison-header\">\r\n          <div class=\"comparison-title\">\r\n            <span class=\"base-material\">{{ currentComparison.itemName }}</span>\r\n            <span class=\"vs-text\">VS</span>\r\n            <span class=\"compare-material\">{{ currentComparison.compareItemName }}</span>\r\n            <span class=\"similarity-info\">相似度：{{ currentComparison.score }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"comparison-chart-container\">\r\n          <div\r\n            id=\"comparisonChart\"\r\n            class=\"comparison-chart\"\r\n            v-loading=\"comparisonChartLoading\"\r\n            element-loading-text=\"正在加载对比数据...\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport chartMethods from './chartMethods'\r\nimport extendedChartMethods from './chartMethodsExtended'\r\nimport screenfull from 'screenfull'\r\nimport { showYearlyAmount, showRealTimeAmount, showCokingCoalAmount, showKeyIndicators, showItemTypeList, showMaterialList, showData, showSuppList, showHighFrequencyMaterialList, showPurchaseSuppRisk, getMaterialFuturePrice, getMaterialNameList, getPurchasePriceAndStore, getMaterialNameListFromNewTables, getPurchasePriceAndStoreFromNewTables } from '@/api/purchaseDashboard/purchaseDashboard'\r\nimport { listSimilarByItemNames } from '@/api/purchase/similar'\r\nimport { getDepNameList, getListMonthly } from '@/api/purchase/purdchaseFactoryStock'\r\n\r\nexport default {\r\n  name: 'PurchaseDashboard',\r\n  mixins: [chartMethods, extendedChartMethods],\r\n  data() {\r\n    return {\r\n      // 时间过滤器选项\r\n      timeFilters: [\r\n        { id: 'filter-3m', label: '近三个月', value: 1 },\r\n        { id: 'filter-6m', label: '近六个月', value: 2 },\r\n        { id: 'filter-1y', label: '近一年', value: 3 }\r\n      ],\r\n      activeFilter: 'filter-1y',\r\n      currentDimensionType: 3,\r\n\r\n      // 数据\r\n      dashboardData: {},\r\n      purchaseStats: {},\r\n\r\n      // 下拉选项\r\n      topSuppliersOptions: [],\r\n\r\n      // 选中的过滤器值\r\n      selectedTopSuppliersFilter: '',\r\n      selectedOrderType: 'TOP', // 排序类型，默认为TOP\r\n\r\n      // 图表实例\r\n      chartInstances: {},\r\n\r\n      // 原始数据备份\r\n      originalTopSuppliersData: [],\r\n\r\n      // 库存图表相关\r\n      selectedYear: '',\r\n      selectedMaterialType: '',\r\n      availableYears: (() => {\r\n        const currentYear = new Date().getFullYear()\r\n        const years = []\r\n        for (let year = 2020; year <= currentYear; year++) {\r\n          years.push(year.toString())\r\n        }\r\n        return years\r\n      })(),\r\n      yearlyInventoryData: [],\r\n      realTimeInventoryData: [],\r\n      cokingCoalInventoryData: [],\r\n\r\n      // 矿焦煤库存图表相关\r\n      selectedCokingCoalType: '', // 选中的矿焦煤类型，默认为空（全部）\r\n\r\n      // 物料入库统计相关\r\n      selectedMaterialCategory: '1',\r\n      selectedMaterialItem: '',\r\n      materialItemOptions: [],\r\n      materialStatisticsData: [],\r\n\r\n      // 高频采购物料相关\r\n      selectedCodeType: 'ALL',\r\n      selectedItemType: 'CLASS3',\r\n      highFrequencyMaterialData: [],\r\n\r\n      // 供应商风险数据\r\n      supplierRiskData: [],\r\n\r\n      // AI价格预测相关\r\n      pricePredictions: [], // 改为数组，支持多个物料的预测\r\n      predictionLoading: false,\r\n\r\n      // 物料价格趋势图相关\r\n      materialNameOptions: [],\r\n      selectedMaterial: 'PB块',\r\n      selectedMaterialCategory: '1', // 默认选择矿石\r\n      priceAndStoreData: null,\r\n\r\n      // 新的价格趋势图相关属性\r\n      // 采购量曲线\r\n      purchaseAmountCategories: [99], // 默认选择全部\r\n      selectedPurchaseAmountMaterials: [],\r\n      purchaseAmountMaterialOptions: [],\r\n\r\n      // 市场价曲线\r\n      marketPriceCategories: [99], // 默认选择全部\r\n      selectedMarketPriceMaterials: [],\r\n      marketPriceMaterialOptions: [],\r\n\r\n      // 获取数据状态\r\n      fetchingPriceData: false,\r\n      newPriceAndStoreData: null,\r\n\r\n      // 初始化标志\r\n      hasInitializedPriceChart: false,\r\n\r\n      // 相似物料数据\r\n      similarMaterialsData: [],\r\n      similarMaterialsLoading: false,\r\n\r\n      // 对比弹框相关\r\n      comparisonDialogVisible: false,\r\n      comparisonChartLoading: false,\r\n      currentComparison: {},\r\n      comparisonChartInstance: null,\r\n      comparisonPriceData: null,\r\n\r\n      // 机旁库当前库存相关\r\n      selectedFactoryDep: '', // 选中的分厂\r\n      selectedFactoryMaterialType: '', // 选中的物料类型\r\n      factoryDepOptions: [], // 分厂选项列表\r\n      factoryStockData: [] // 机旁库存数据\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isFullscreen() {\r\n      return this.$store.state.app.isFullscreenMode\r\n    },\r\n\r\n    // 按itemName、category、priceType联合索引分组相似物料数据\r\n    groupedSimilarMaterials() {\r\n      const grouped = {}\r\n      this.similarMaterialsData.forEach(item => {\r\n        // 创建联合索引key\r\n        const groupKey = `${item.itemName}_${item.category}_${item.priceType}`\r\n        const displayKey = `${item.itemName} (${this.getCategoryName(item.category)} - ${this.getPriceTypeName(item.priceType)})`\r\n\r\n        if (!grouped[displayKey]) {\r\n          grouped[displayKey] = {\r\n            groupKey: groupKey,\r\n            items: []\r\n          }\r\n        }\r\n        grouped[displayKey].items.push(item)\r\n      })\r\n\r\n      // 对每个组内的数据按排名排序\r\n      Object.keys(grouped).forEach(key => {\r\n        grouped[key].items.sort((a, b) => a.rank - b.rank)\r\n      })\r\n\r\n      return grouped\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.checkEchartsAvailability()\r\n    this.fetchDashboardData(3)\r\n    this.fetchYearlyInventoryData()\r\n    this.fetchRealTimeInventoryData()\r\n    this.fetchCokingCoalInventoryData()\r\n    // 初始化物料入库统计的下拉框选项和数据\r\n    this.updateMaterialItemOptions().then(() => {\r\n      this.fetchMaterialStatisticsData()\r\n    })\r\n    // 初始化高频采购物料数据\r\n    this.fetchHighFrequencyMaterialData()\r\n    // 初始化供应商风险数据\r\n    this.fetchSupplierRiskData()\r\n\r\n    // 初始化新的物料名称列表（会自动触发默认选中PB块和数据获取）\r\n    this.fetchPurchaseAmountMaterialList()\r\n    this.fetchMarketPriceMaterialList()\r\n\r\n    // 初始化机旁库存数据\r\n    this.fetchFactoryDepOptions()\r\n\r\n    this.setupResizeObserver()\r\n    this.initFullscreenListener()\r\n\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.resizeAllCharts)\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 清理定时器和事件监听器\r\n    this.clearAllIntervals()\r\n    this.removeFullscreenListener()\r\n    window.removeEventListener('resize', this.resizeAllCharts)\r\n\r\n    // 确保退出全屏模式\r\n    this.$store.dispatch('app/setFullscreenMode', false)\r\n  },\r\n\r\n  methods: {\r\n    // 初始化全屏监听器\r\n    initFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.on('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 移除全屏监听器\r\n    removeFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.off('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 处理全屏状态变化\r\n    handleFullscreenChange() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        const isFullscreen = screenfull.isFullscreen\r\n        this.$store.dispatch('app/setFullscreenMode', isFullscreen)\r\n\r\n        // 全屏状态变化后，重新调整图表大小\r\n        this.$nextTick(() => {\r\n          setTimeout(() => {\r\n            this.resizeAllCharts()\r\n          }, 300) // 给布局变化一些时间\r\n        })\r\n      }\r\n    },\r\n\r\n    // API调用方法\r\n    async getDashboardData(dimensionType) {\r\n      return await showData({ dimensionType: dimensionType })\r\n    },\r\n\r\n    async getItemTypeList(itemType) {\r\n      return await showItemTypeList({ itemType: itemType })\r\n    },\r\n\r\n    async getMaterialList(params) {\r\n      return await showMaterialList(params)\r\n    },\r\n\r\n    async getSupplierList(params) {\r\n      return await showSuppList(params)\r\n    },\r\n\r\n    async getYearlyAmount(params) {\r\n      return await showYearlyAmount(params)\r\n    },\r\n\r\n    async getRealTimeAmount() {\r\n      return await showRealTimeAmount()\r\n    },\r\n\r\n    async getCokingCoalAmount() {\r\n      return await showCokingCoalAmount()\r\n    },\r\n\r\n    async getKeyIndicators(params) {\r\n      return await showKeyIndicators(params)\r\n    },\r\n\r\n    async getHighFrequencyMaterialList(params) {\r\n      return await showHighFrequencyMaterialList(params)\r\n    },\r\n\r\n    async getPurchaseSuppRisk(params) {\r\n      return await showPurchaseSuppRisk(params)\r\n    },\r\n\r\n    // 根据dimensionType获取timeFlag\r\n    getTimeFlagByDimensionType(dimensionType) {\r\n      switch(dimensionType) {\r\n        case 1: return '03' // 近三个月\r\n        case 2: return '06' // 近六个月\r\n        case 3: return '12' // 近一年\r\n        default: return '03'\r\n      }\r\n    },\r\n\r\n    // 检查ECharts可用性\r\n    checkEchartsAvailability() {\r\n      if (!echarts) {\r\n        console.error('ECharts库未能加载，使用备用显示方式')\r\n        document.querySelectorAll('.chart').forEach(el => {\r\n          el.innerHTML = '<div class=\"chart-placeholder\">图表加载失败</div>'\r\n        })\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n\r\n    // 获取仪表板数据\r\n    async fetchDashboardData(dimensionTypeParam = 1) {\r\n      this.currentDimensionType = dimensionTypeParam\r\n\r\n      // 清除所有定时器\r\n      this.clearAllIntervals()\r\n\r\n      try {\r\n        // 并行获取仪表板数据和关键指标数据\r\n        const [dashboardResponse, keyIndicatorsResponse] = await Promise.all([\r\n          this.getDashboardData(dimensionTypeParam),\r\n          this.getKeyIndicators({ dimensionType: dimensionTypeParam })\r\n        ])\r\n\r\n        // 处理仪表板数据\r\n        if (dashboardResponse && dashboardResponse.data) {\r\n          this.dashboardData = dashboardResponse.data\r\n          console.log('获取仪表板数据成功:', this.dashboardData)\r\n        } else {\r\n          console.error('API数据格式不正确或缺少data字段', dashboardResponse)\r\n          this.showErrorMessage('API数据格式不正确或缺少data字段')\r\n        }\r\n\r\n        // 处理关键指标数据\r\n        if (keyIndicatorsResponse && keyIndicatorsResponse.data) {\r\n          this.purchaseStats = keyIndicatorsResponse.data || {}\r\n          console.log('获取关键指标数据成功:', this.purchaseStats)\r\n        } else {\r\n          console.error('获取关键指标数据失败', keyIndicatorsResponse)\r\n          this.purchaseStats = {}\r\n        }\r\n\r\n        this.initAllCharts()\r\n      } catch (error) {\r\n        console.error('API请求或数据处理失败', error)\r\n        this.showErrorMessage('数据加载失败: ' + error.message)\r\n      }\r\n    },\r\n\r\n    // 显示错误信息\r\n    showErrorMessage(message) {\r\n      document.querySelectorAll('.chart').forEach(chart => {\r\n        chart.innerHTML = `<div class=\"chart-placeholder\">${message}</div>`\r\n      })\r\n    },\r\n\r\n    // 时间过滤器变化处理\r\n    handleTimeFilterChange(filterId, dimensionType) {\r\n      this.activeFilter = filterId\r\n      this.currentDimensionType = dimensionType\r\n      console.log('选择的时间范围:', filterId, '维度:', dimensionType)\r\n\r\n      this.clearAllIntervals()\r\n      this.fetchDashboardData(dimensionType)\r\n      // 同时更新高频物料数据\r\n      this.fetchHighFrequencyMaterialData()\r\n      // 同时更新供应商风险数据\r\n      this.fetchSupplierRiskData()\r\n      // 同时更新物料入库统计数据\r\n      this.fetchMaterialStatisticsData()\r\n      // 注意：价格趋势数据只在用户主动点击按钮时获取，不在时间过滤器变化时自动获取\r\n\r\n      // 同时更新新的物料列表（用于下拉框选项），但不会自动触发数据获取\r\n      this.fetchPurchaseAmountMaterialList()\r\n      this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 清除所有定时器\r\n    clearAllIntervals() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance && instance.intervalId) {\r\n          clearInterval(instance.intervalId)\r\n          instance.intervalId = null\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重新调整所有图表大小\r\n    resizeAllCharts() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance) {\r\n          try {\r\n            instance.resize()\r\n          } catch(err) {\r\n            console.error('图表大小调整失败:', err)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 初始化所有图表\r\n    initAllCharts() {\r\n      console.log('initAllCharts started')\r\n      try {\r\n        // 注意：实时库存图表和矿焦煤库存图表会在各自数据获取完成后单独初始化\r\n        // 注意：月度库存金额图表会在fetchYearlyInventoryData完成后单独初始化\r\n        // 注意：物料入库统计图表会在fetchMaterialStatisticsData完成后单独初始化\r\n        // 注意：机旁库存图表会在fetchFactoryStockData完成后单独初始化\r\n\r\n        // 初始化物料词云图\r\n        this.initMaterialCloud()\r\n\r\n        // 初始化TOP供应商图\r\n        this.initTopSuppliersChart()\r\n        this.populateItemDropdown('topSuppliersFilter', 1, 'topSuppliersOptions')\r\n\r\n        // 注意：供应商风险图表会在fetchSupplierRiskData完成后单独初始化\r\n\r\n        // 注意：采购价格趋势图会在fetchPriceAndStoreData完成后单独初始化\r\n\r\n        console.log('所有图表初始化完成')\r\n      } catch (err) {\r\n        console.error('图表初始化主流程失败:', err)\r\n        this.showErrorMessage('图表初始化失败: ' + err.message)\r\n      }\r\n    },\r\n\r\n    // 填充物料类型下拉框\r\n    async populateItemDropdown(selectElementId, itemType, dataPropertyName) {\r\n      try {\r\n        const response = await this.getItemTypeList(itemType)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this[dataPropertyName] = response.data\r\n        } else {\r\n          console.error(`Invalid data format from showItemTypeList for itemType ${itemType}:`, response)\r\n          this[dataPropertyName] = []\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error fetching item types for ${selectElementId}:`, error)\r\n        this[dataPropertyName] = []\r\n      }\r\n    },\r\n\r\n    // 下拉框变化处理方法\r\n    async handleTopSuppliersFilterChange() {\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async handleOrderTypeChange() {\r\n      console.log('排序类型变化:', this.selectedOrderType)\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async refreshTopSuppliersChart() {\r\n      console.log(`Top supplier filter selected item ID: ${this.selectedTopSuppliersFilter}, orderType: ${this.selectedOrderType}`)\r\n      const myChart = this.chartInstances.topSuppliersChart\r\n      if (!myChart) {\r\n        console.error(\"TOP10供应商图表实例未找到\")\r\n        return\r\n      }\r\n\r\n      if (myChart.intervalId) {\r\n        clearInterval(myChart.intervalId)\r\n        myChart.intervalId = null\r\n      }\r\n\r\n      if (!this.selectedTopSuppliersFilter || this.selectedTopSuppliersFilter === \"\") {\r\n        // 使用原始数据，但需要根据orderType重新获取\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n            newSupplierData = this.originalTopSuppliersData\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          this.renderAndPaginateTopSuppliers(myChart, this.originalTopSuppliersData)\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      } else {\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            itemId: this.selectedTopSuppliersFilter,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          document.getElementById('topSuppliersChart').innerHTML = '<div class=\"chart-placeholder\">供应商数据加载失败</div>'\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      }\r\n    },\r\n\r\n    // 设置大小调整观察器\r\n    setupResizeObserver() {\r\n      const resizeObserver = new ResizeObserver(entries => {\r\n        for (let entry of entries) {\r\n          const charts = entry.target.querySelectorAll('.chart')\r\n          charts.forEach(chart => {\r\n            if (chart.id) {\r\n              const instance = echarts.getInstanceByDom(document.getElementById(chart.id))\r\n              if (instance) {\r\n                instance.resize()\r\n              }\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      document.querySelectorAll('.card').forEach(card => {\r\n        resizeObserver.observe(card)\r\n      })\r\n    },\r\n\r\n    toggleFullscreen() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.toggle()\r\n      } else {\r\n        this.$message({\r\n          message: '您的浏览器不支持全屏功能',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n\r\n    async handleYearChange() {\r\n      console.log('年份变化:', this.selectedYear)\r\n      await this.fetchYearlyInventoryData()\r\n    },\r\n\r\n    async handleMaterialTypeChange() {\r\n      console.log('物料类型变化:', this.selectedMaterialType)\r\n      await this.fetchYearlyInventoryData()\r\n    },\r\n\r\n    // 获取年度库存数据\r\n    async fetchYearlyInventoryData() {\r\n      try {\r\n        const params = {}\r\n\r\n        // 只有当materialType不为空时才传递该参数\r\n        if (this.selectedMaterialType && this.selectedMaterialType !== '') {\r\n          params.materialType = this.selectedMaterialType\r\n        }\r\n\r\n        // 如果选择了具体年份，只查询该年份，否则查询所有年份\r\n        if (this.selectedYear) {\r\n          params.yearList = [this.selectedYear]\r\n        } else {\r\n          params.yearList = this.availableYears\r\n        }\r\n\r\n        console.log('fetchYearlyInventoryData - 请求参数:', params)\r\n        const response = await this.getYearlyAmount(params)\r\n        console.log('fetchYearlyInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.yearlyInventoryData = response.data || []\r\n          console.log('fetchYearlyInventoryData - 设置的数据:', this.yearlyInventoryData)\r\n        } else {\r\n          // 使用模拟数据\r\n          this.yearlyInventoryData = this.getMockYearlyData()\r\n          console.log('fetchYearlyInventoryData - 使用模拟数据:', this.yearlyInventoryData)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取年度库存数据失败，使用模拟数据:', error)\r\n        // 使用模拟数据\r\n        this.yearlyInventoryData = this.getMockYearlyData()\r\n      }\r\n\r\n      // 重新初始化图表\r\n      this.initMonthlyInventoryChart()\r\n    },\r\n\r\n    // 生成模拟数据\r\n    getMockYearlyData() {\r\n      return [\r\n        {\r\n          year: '2023',\r\n          monthlyResultVoList: [\r\n            { monthIndex: 1, amount: 1200.50 },\r\n            { monthIndex: 2, amount: 1350.75 },\r\n            { monthIndex: 3, amount: 1180.20 },\r\n            { monthIndex: 4, amount: 1420.30 },\r\n            { monthIndex: 5, amount: 1380.90 },\r\n            { monthIndex: 6, amount: 1520.40 },\r\n            { monthIndex: 7, amount: 1650.60 },\r\n            { monthIndex: 8, amount: 1480.85 },\r\n            { monthIndex: 9, amount: 1390.25 },\r\n            { monthIndex: 10, amount: 1610.70 },\r\n            { monthIndex: 11, amount: 1580.35 },\r\n            { monthIndex: 12, amount: 1720.95 }\r\n          ]\r\n        },\r\n        {\r\n          year: '2024',\r\n          monthlyResultVoList: [\r\n            { monthIndex: 1, amount: 1320.80 },\r\n            { monthIndex: 2, amount: 1450.60 },\r\n            { monthIndex: 3, amount: 1280.40 },\r\n            { monthIndex: 4, amount: 1540.70 },\r\n            { monthIndex: 5, amount: 1480.20 },\r\n            { monthIndex: 6, amount: 1620.50 },\r\n            { monthIndex: 7, amount: 1750.30 },\r\n            { monthIndex: 8, amount: 1580.90 },\r\n            { monthIndex: 9, amount: 1490.60 },\r\n            { monthIndex: 10, amount: 1710.40 },\r\n            { monthIndex: 11, amount: 1680.80 },\r\n            { monthIndex: 12, amount: 1820.20 }\r\n          ]\r\n        }\r\n      ]\r\n    },\r\n\r\n    async fetchRealTimeInventoryData() {\r\n      try {\r\n        const response = await this.getRealTimeAmount()\r\n        console.log('fetchRealTimeInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.realTimeInventoryData = response.data || []\r\n          console.log('fetchRealTimeInventoryData - 设置的数据:', this.realTimeInventoryData)\r\n        } else {\r\n          console.error('获取实时库存数据失败，使用模拟数据', response)\r\n          // 使用模拟数据\r\n          this.realTimeInventoryData = this.getMockRealTimeData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取实时库存数据失败，使用模拟数据:', error)\r\n        // 使用模拟数据\r\n        this.realTimeInventoryData = this.getMockRealTimeData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initRealTimeInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟实时库存数据\r\n    getMockRealTimeData() {\r\n      return [\r\n        {\r\n          materialType: 'A',\r\n          materialName: '通用备件',\r\n          centerInventoryAmount: 1250.30,\r\n          machineSideInventoryAmount: 380.50,\r\n          totalInventoryAmount: 1630.80\r\n        },\r\n        {\r\n          materialType: 'B',\r\n          materialName: '专用备件',\r\n          centerInventoryAmount: 980.75,\r\n          machineSideInventoryAmount: 420.25,\r\n          totalInventoryAmount: 1401.00\r\n        },\r\n        {\r\n          materialType: 'C',\r\n          materialName: '材料类',\r\n          centerInventoryAmount: 2150.60,\r\n          machineSideInventoryAmount: 650.40,\r\n          totalInventoryAmount: 2801.00\r\n        },\r\n        {\r\n          materialType: 'D',\r\n          materialName: '原材料',\r\n          centerInventoryAmount: 3200.90,\r\n          machineSideInventoryAmount: 890.10,\r\n          totalInventoryAmount: 4091.00\r\n        },\r\n        {\r\n          materialType: 'E',\r\n          materialName: '辅耐材',\r\n          centerInventoryAmount: 1580.40,\r\n          machineSideInventoryAmount: 320.60,\r\n          totalInventoryAmount: 1901.00\r\n        },\r\n        {\r\n          materialType: 'G',\r\n          materialName: '办公',\r\n          centerInventoryAmount: 150.20,\r\n          machineSideInventoryAmount: 50.80,\r\n          totalInventoryAmount: 201.00\r\n        }\r\n      ]\r\n    },\r\n\r\n    async fetchCokingCoalInventoryData() {\r\n      try {\r\n        const response = await this.getCokingCoalAmount()\r\n        console.log('fetchCokingCoalInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.cokingCoalInventoryData = response.data || []\r\n          console.log('fetchCokingCoalInventoryData - 设置的数据:', this.cokingCoalInventoryData)\r\n        } else {\r\n          console.error('获取矿焦煤库存数据失败', response)\r\n          this.cokingCoalInventoryData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取矿焦煤库存数据失败:', error)\r\n        this.cokingCoalInventoryData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 物料入库统计相关方法\r\n    async handleMaterialCategoryChange() {\r\n      console.log('物料类别变化:', this.selectedMaterialCategory)\r\n      this.selectedMaterialItem = '' // 重置第二个下拉框\r\n      await this.updateMaterialItemOptions()\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async handleMaterialItemChange() {\r\n      console.log('物料项目变化:', this.selectedMaterialItem)\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async updateMaterialItemOptions() {\r\n      if (this.selectedMaterialCategory === '1') {\r\n        // 大类：只有全部选项\r\n        this.materialItemOptions = []\r\n      } else {\r\n        // 中类、细类、叶类：获取对应的选项\r\n        const itemType = parseInt(this.selectedMaterialCategory) - 1 // 1->0, 2->1, 3->2, 4->3\r\n        try {\r\n          const response = await this.getItemTypeList(itemType)\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            this.materialItemOptions = response.data\r\n          } else {\r\n            this.materialItemOptions = []\r\n          }\r\n        } catch (error) {\r\n          console.error('获取物料项目选项失败:', error)\r\n          this.materialItemOptions = []\r\n        }\r\n      }\r\n    },\r\n\r\n    async fetchMaterialStatisticsData() {\r\n      try {\r\n        const params = {\r\n          itemType: parseInt(this.selectedMaterialCategory),\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        // 如果选择了具体物料项目，添加itemId参数\r\n        if (this.selectedMaterialItem && this.selectedMaterialItem !== '') {\r\n          params.itemId = this.selectedMaterialItem\r\n        }\r\n\r\n        console.log('fetchMaterialStatisticsData - 请求参数:', params)\r\n        const response = await this.getMaterialList(params)\r\n        console.log('fetchMaterialStatisticsData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.materialStatisticsData = response.data || []\r\n          console.log('fetchMaterialStatisticsData - 设置的数据:', this.materialStatisticsData)\r\n        } else {\r\n          console.error('获取物料统计数据失败，使用模拟数据', response)\r\n          this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料统计数据失败，使用模拟数据:', error)\r\n        this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialStatisticsChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟物料统计数据\r\n    getMockMaterialStatisticsData() {\r\n      return [\r\n        { itemName: '通用备件', inAmt: 1250.30, arriveRate: 85.5 },\r\n        { itemName: '专用备件', inAmt: 980.75, arriveRate: 78.2 },\r\n        { itemName: '材料类', inAmt: 2150.60, arriveRate: 92.1 },\r\n        { itemName: '原材料', inAmt: 3200.90, arriveRate: 88.7 },\r\n        { itemName: '辅耐材', inAmt: 1580.40, arriveRate: 91.3 },\r\n        { itemName: '办公', inAmt: 150.20, arriveRate: 95.0 }\r\n      ]\r\n    },\r\n\r\n    async fetchHighFrequencyMaterialData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          codeType: this.selectedCodeType,\r\n          itemType: this.selectedItemType\r\n        }\r\n\r\n        console.log('fetchHighFrequencyMaterialData - 请求参数:', params)\r\n        const response = await this.getHighFrequencyMaterialList(params)\r\n        console.log('fetchHighFrequencyMaterialData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.highFrequencyMaterialData = response.data || []\r\n          console.log('fetchHighFrequencyMaterialData - 设置的数据:', this.highFrequencyMaterialData)\r\n        } else {\r\n          console.error('获取高频物料数据失败，使用模拟数据', response)\r\n          this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取高频物料数据失败，使用模拟数据:', error)\r\n        this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialCloud()\r\n      })\r\n    },\r\n\r\n    // 生成模拟高频物料数据\r\n    getMockHighFrequencyData() {\r\n      return [\r\n        { itemName: '粗粉', inAmt: 392467.2, inNum: 5421293 },\r\n        { itemName: '精粉', inAmt: 280350.5, inNum: 4250180 },\r\n        { itemName: '球团', inAmt: 195200.8, inNum: 3180970 },\r\n        { itemName: '烧结', inAmt: 150420.3, inNum: 2890540 },\r\n        { itemName: '焦炭', inAmt: 125680.7, inNum: 2350210 },\r\n        { itemName: '煤炭', inAmt: 98750.2, inNum: 1980760 }\r\n      ]\r\n    },\r\n\r\n    async handleCodeTypeChange() {\r\n      console.log('大类类型变化:', this.selectedCodeType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    async handleItemTypeChange() {\r\n      console.log('维度变化:', this.selectedItemType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    // 获取供应商风险数据\r\n    async fetchSupplierRiskData() {\r\n      try {\r\n        const params = {\r\n          timeFlag: this.getTimeFlagByDimensionType(this.currentDimensionType)\r\n        }\r\n\r\n        console.log('fetchSupplierRiskData - 请求参数:', params)\r\n        const response = await this.getPurchaseSuppRisk(params)\r\n        console.log('fetchSupplierRiskData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.supplierRiskData = response.data || []\r\n          console.log('fetchSupplierRiskData - 设置的数据:', this.supplierRiskData)\r\n        } else {\r\n          console.error('获取供应商风险数据失败', response)\r\n          this.supplierRiskData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取供应商风险数据失败:', error)\r\n        this.supplierRiskData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initSupplierRiskChart()\r\n      })\r\n    },\r\n\r\n    // 获取多个物料的AI价格预测\r\n    async fetchMultiplePricePredictions(materialNames) {\r\n      this.predictionLoading = true\r\n      this.pricePredictions = [] // 清空之前的预测结果\r\n\r\n      try {\r\n        // 并行调用所有物料的预测接口\r\n        const predictionPromises = materialNames.map(async (materialName) => {\r\n          try {\r\n            const params = {\r\n              materialName: materialName,\r\n              materialType: '1' // 默认使用矿石类型，可以根据需要调整\r\n            }\r\n\r\n            console.log(`fetchPricePrediction - ${materialName} 请求参数:`, params)\r\n            const response = await getMaterialFuturePrice(params)\r\n            console.log(`fetchPricePrediction - ${materialName} 完整响应:`, response)\r\n\r\n            if (response && response.code && response.code === 200 && response.data) {\r\n              return {\r\n                materialName: materialName,\r\n                question: response.data.question || `关于${materialName}的价格预测`,\r\n                prediction: response.data.answer || response.data.prediction || response.msg,\r\n                success: response.data.success !== false\r\n              }\r\n            } else {\r\n              console.error(`获取${materialName}价格预测数据失败`, response)\r\n              return {\r\n                materialName: materialName,\r\n                question: `关于${materialName}的价格预测`,\r\n                prediction: `获取${materialName}价格预测失败`,\r\n                success: false\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error(`获取${materialName}价格预测数据失败:`, error)\r\n            return {\r\n              materialName: materialName,\r\n              question: `关于${materialName}的价格预测`,\r\n              prediction: `获取${materialName}价格预测失败：${error.message}`,\r\n              success: false\r\n            }\r\n          }\r\n        })\r\n\r\n        // 等待所有预测结果\r\n        const results = await Promise.all(predictionPromises)\r\n        this.pricePredictions = results\r\n        console.log('fetchMultiplePricePredictions - 设置的预测数据:', this.pricePredictions)\r\n\r\n        const successCount = results.filter(r => r.success).length\r\n        const totalCount = results.length\r\n\r\n        if (successCount > 0) {\r\n          this.$message.success(`成功获取${successCount}/${totalCount}个物料的价格预测`)\r\n        } else {\r\n          this.$message.error('所有物料的价格预测获取失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('批量获取价格预测数据失败:', error)\r\n        this.$message.error('批量获取价格预测失败：' + error.message)\r\n      } finally {\r\n        this.predictionLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取物料名称列表\r\n    async fetchMaterialNameList() {\r\n      try {\r\n        const params = {\r\n          category: parseInt(this.selectedMaterialCategory)\r\n        }\r\n\r\n        const response = await getMaterialNameList(params)\r\n        console.log('fetchMaterialNameList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.materialNameOptions = response.data\r\n          console.log('fetchMaterialNameList - 设置的数据:', this.materialNameOptions)\r\n\r\n          // 设置默认选中PB块，如果存在的话\r\n          const pbMaterial = this.materialNameOptions.find(item => item.itemName === 'PB块')\r\n          if (pbMaterial) {\r\n            this.selectedMaterial = 'PB块'\r\n          } else if (this.materialNameOptions.length > 0) {\r\n            // 如果没有PB块，选择第一个\r\n            this.selectedMaterial = this.materialNameOptions[0].itemName\r\n          }\r\n\r\n          // 获取价格数据\r\n          this.fetchPriceAndStoreData()\r\n        } else {\r\n          console.error('获取物料名称列表失败', response)\r\n          this.materialNameOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料名称列表失败:', error)\r\n        this.materialNameOptions = []\r\n      }\r\n    },\r\n\r\n    // 获取物料价格和采购量数据\r\n    async fetchPriceAndStoreData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemName: this.selectedMaterial\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStore(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n          this.priceAndStoreData = response.data[0] // 取第一个元素\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.priceAndStoreData)\r\n        } else {\r\n          console.error('获取价格和采购量数据失败', response)\r\n          this.priceAndStoreData = null\r\n        }\r\n      } catch (error) {\r\n        console.error('获取价格和采购量数据失败:', error)\r\n        this.priceAndStoreData = null\r\n      }\r\n\r\n      // 数据获取完成后重新初始化价格趋势图\r\n      this.$nextTick(() => {\r\n        this.initPriceTrendChart()\r\n      })\r\n    },\r\n\r\n    // 处理物资类型切换\r\n    async handleMaterialCategoryTypeChange() {\r\n      console.log('物资类型变化:', this.selectedMaterialCategory)\r\n      // 重新获取物料名称列表\r\n      await this.fetchMaterialNameList()\r\n    },\r\n\r\n    // 处理物料选择变化\r\n    async handleMaterialChange() {\r\n      console.log('物料选择变化:', this.selectedMaterial)\r\n      await this.fetchPriceAndStoreData()\r\n      // 不再自动触发AI预测，等用户点击按钮后再触发\r\n    },\r\n\r\n    calculateRealTimeInventoryTotal() {\r\n      let total = 0\r\n      if (this.realTimeInventoryData && this.realTimeInventoryData.length > 0) {\r\n        this.realTimeInventoryData.forEach(item => {\r\n          total += parseFloat(item.totalInventoryAmount) || 0\r\n        })\r\n      }\r\n      return total.toFixed(2)\r\n    },\r\n\r\n    calculateCokingCoalTotal() {\r\n      let total = 0\r\n      if (this.cokingCoalInventoryData && this.cokingCoalInventoryData.length > 0) {\r\n        // 找到所有数据中的最新日期\r\n        let latestDate = ''\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            item.purchaseCokingDailyDetailList.forEach(detail => {\r\n              if (detail.instockDate > latestDate) {\r\n                latestDate = detail.instockDate\r\n              }\r\n            })\r\n          }\r\n        })\r\n\r\n        // 计算最新日期各个物料的库存量合计\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)\r\n            if (latestDetail) {\r\n              total += parseFloat(latestDetail.invQty) || 0\r\n            }\r\n          }\r\n        })\r\n      }\r\n      return (total / 10000).toFixed(2) // 转换为万吨\r\n    },\r\n\r\n    // 处理矿焦煤类型下拉框变化\r\n    async handleCokingCoalTypeChange() {\r\n      console.log('矿焦煤类型变化:', this.selectedCokingCoalType)\r\n      // 重新初始化图表以应用过滤\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 机旁库存相关方法\r\n    // 获取分厂选项列表\r\n    async fetchFactoryDepOptions() {\r\n      try {\r\n        const response = await getDepNameList()\r\n        console.log('fetchFactoryDepOptions - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryDepOptions = response.data\r\n          console.log('fetchFactoryDepOptions - 设置的数据:', this.factoryDepOptions)\r\n        } else {\r\n          console.error('获取分厂选项列表失败', response)\r\n          this.factoryDepOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取分厂选项列表失败:', error)\r\n        this.factoryDepOptions = []\r\n      }\r\n\r\n      // 获取默认数据（全部）\r\n      this.fetchFactoryStockData()\r\n    },\r\n\r\n    // 处理分厂选择变化\r\n    async handleFactoryDepChange() {\r\n      console.log('分厂选择变化:', this.selectedFactoryDep)\r\n      await this.fetchFactoryStockData()\r\n    },\r\n\r\n    // 处理物料类型选择变化\r\n    async handleFactoryMaterialTypeChange() {\r\n      console.log('物料类型选择变化:', this.selectedFactoryMaterialType)\r\n      // 重新初始化图表以应用筛选\r\n      this.$nextTick(() => {\r\n        this.initFactoryStockChart()\r\n      })\r\n    },\r\n\r\n    // 获取机旁库存数据\r\n    async fetchFactoryStockData() {\r\n      try {\r\n        const depName = this.selectedFactoryDep || '' // 空字符串表示全部\r\n        console.log('fetchFactoryStockData - 请求参数:', depName)\r\n\r\n        const response = await getListMonthly(depName)\r\n        console.log('fetchFactoryStockData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryStockData = response.data\r\n          console.log('fetchFactoryStockData - 设置的数据:', this.factoryStockData)\r\n        } else {\r\n          console.error('获取机旁库存数据失败', response)\r\n          this.factoryStockData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取机旁库存数据失败:', error)\r\n        this.factoryStockData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initFactoryStockChart()\r\n      })\r\n    },\r\n\r\n    // 新增方法：处理采购量曲线物料类型变化\r\n    async handlePurchaseAmountCategoriesChange() {\r\n      console.log('采购量曲线物料类型变化:', this.purchaseAmountCategories)\r\n      this.selectedPurchaseAmountMaterials = [] // 重置选中的物料\r\n      await this.fetchPurchaseAmountMaterialList()\r\n    },\r\n\r\n    // 新增方法：处理市场价曲线物料类型变化\r\n    async handleMarketPriceCategoriesChange() {\r\n      console.log('市场价曲线物料类型变化:', this.marketPriceCategories)\r\n      this.selectedMarketPriceMaterials = [] // 重置选中的物料\r\n      await this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 新增方法：获取采购量曲线物料列表\r\n    async fetchPurchaseAmountMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.purchaseAmountCategories,\r\n          curveType: 2, // 采购量曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchPurchaseAmountMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchPurchaseAmountMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.purchaseAmountMaterialOptions = response.data\r\n          console.log('fetchPurchaseAmountMaterialList - 设置的数据:', this.purchaseAmountMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedPurchaseAmountMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.purchaseAmountMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedPurchaseAmountMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 采购量曲线')\r\n\r\n              // 检查市场价曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取采购量曲线物料列表失败', response)\r\n          this.purchaseAmountMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取采购量曲线物料列表失败:', error)\r\n        this.purchaseAmountMaterialOptions = []\r\n      }\r\n    },\r\n\r\n    // 新增方法：获取市场价曲线物料列表\r\n    async fetchMarketPriceMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.marketPriceCategories,\r\n          curveType: 1, // 价格曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchMarketPriceMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchMarketPriceMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.marketPriceMaterialOptions = response.data\r\n          console.log('fetchMarketPriceMaterialList - 设置的数据:', this.marketPriceMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedMarketPriceMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.marketPriceMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedMarketPriceMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 市场价曲线')\r\n\r\n              // 检查采购量曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取市场价曲线物料列表失败', response)\r\n          this.marketPriceMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取市场价曲线物料列表失败:', error)\r\n        this.marketPriceMaterialOptions = []\r\n      }\r\n    },\r\n\r\n\r\n\r\n    // 新增方法：获取物料采购价格数据（用于新的价格趋势图）\r\n    async fetchPriceAndStoreDataForNewChart() {\r\n      if (this.selectedPurchaseAmountMaterials.length === 0 && this.selectedMarketPriceMaterials.length === 0) {\r\n        this.$message.warning('请至少选择一个物料')\r\n        return\r\n      }\r\n\r\n      this.fetchingPriceData = true\r\n      try {\r\n        // 构建itemList\r\n        const itemList = []\r\n\r\n        // 添加采购量曲线的物料\r\n        this.selectedPurchaseAmountMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 2, // 采购量曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        // 添加市场价曲线的物料\r\n        this.selectedMarketPriceMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 1, // 价格曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.newPriceAndStoreData = response.data\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.newPriceAndStoreData)\r\n\r\n          // 重新渲染图表\r\n          this.$nextTick(() => {\r\n            this.initNewPriceTrendChart()\r\n          })\r\n\r\n          // 获取所有选中物料的去重列表\r\n          const allSelectedMaterials = [...new Set([\r\n            ...this.selectedPurchaseAmountMaterials,\r\n            ...this.selectedMarketPriceMaterials\r\n          ])]\r\n\r\n          // 为每个物料调用AI预测接口\r\n          if (allSelectedMaterials.length > 0) {\r\n            this.fetchMultiplePricePredictions(allSelectedMaterials)\r\n          }\r\n\r\n          // 如果市场价曲线有选中物料，获取相似物料信息\r\n          if (this.selectedMarketPriceMaterials.length > 0) {\r\n            this.fetchSimilarMaterials(this.selectedMarketPriceMaterials)\r\n          } else {\r\n            // 清空相似物料数据\r\n            this.similarMaterialsData = []\r\n          }\r\n\r\n          this.$message.success('数据获取成功')\r\n        } else {\r\n          console.error('获取物料采购价格数据失败', response)\r\n          this.$message.error('获取数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料采购价格数据失败:', error)\r\n        this.$message.error('获取数据失败：' + error.message)\r\n      } finally {\r\n        this.fetchingPriceData = false\r\n      }\r\n    },\r\n\r\n    // 获取相似物料信息\r\n    async fetchSimilarMaterials(itemNames) {\r\n      this.similarMaterialsLoading = true\r\n      try {\r\n        const params = {\r\n          itemNames: itemNames\r\n        }\r\n\r\n        console.log('fetchSimilarMaterials - 请求参数:', params)\r\n        const response = await listSimilarByItemNames(params)\r\n        console.log('fetchSimilarMaterials - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.similarMaterialsData = response.data\r\n          console.log('fetchSimilarMaterials - 设置的数据:', this.similarMaterialsData)\r\n        } else {\r\n          console.error('获取相似物料数据失败', response)\r\n          this.similarMaterialsData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取相似物料数据失败:', error)\r\n        this.similarMaterialsData = []\r\n      } finally {\r\n        this.similarMaterialsLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取排名样式类\r\n    getRankClass(rank) {\r\n      if (rank === 1) return 'rank-first'\r\n      if (rank === 2) return 'rank-second'\r\n      if (rank === 3) return 'rank-third'\r\n      return 'rank-default'\r\n    },\r\n\r\n    // 获取商品分类名称\r\n    getCategoryName(category) {\r\n      const categoryMap = {\r\n        1: '矿石',\r\n        2: '煤炭',\r\n        3: '合金',\r\n        4: '废钢'\r\n      }\r\n      return categoryMap[category] || '未知'\r\n    },\r\n\r\n    // 获取价格类型名称\r\n    getPriceTypeName(priceType) {\r\n      const priceTypeMap = {\r\n        1: '现货价',\r\n        2: '市场采购到厂价',\r\n        3: '兴澄废钢收购价(车运)',\r\n        4: '兴澄废钢收购价(船运)',\r\n        5: '沙钢废钢收购价(车运)',\r\n        6: '沙钢废钢收购价(船运)'\r\n      }\r\n      return priceTypeMap[priceType] || '未知'\r\n    },\r\n\r\n    // 打开对比弹框\r\n    openComparisonDialog(item) {\r\n      console.log('openComparisonDialog - 传入的item数据:', item)\r\n      this.currentComparison = { ...item }\r\n      console.log('openComparisonDialog - 设置的currentComparison:', this.currentComparison)\r\n      this.comparisonDialogVisible = true\r\n\r\n      // 弹框打开后获取对比数据\r\n      this.$nextTick(() => {\r\n        this.fetchComparisonData()\r\n      })\r\n    },\r\n\r\n    // 关闭对比弹框\r\n    closeComparisonDialog() {\r\n      this.comparisonDialogVisible = false\r\n      this.currentComparison = {}\r\n      this.comparisonPriceData = null\r\n\r\n      // 清理图表实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n          this.comparisonChartInstance = null\r\n        } catch (err) {\r\n          console.error('清理对比图表实例失败:', err)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 获取对比数据（独立实现，不耦合现有趋势图）\r\n    async fetchComparisonData() {\r\n      this.comparisonChartLoading = true\r\n      try {\r\n        // 构建两个物料的对比请求，只获取价格曲线\r\n        const itemList = [\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.itemName\r\n          },\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.compareItemName\r\n          }\r\n        ]\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchComparisonData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchComparisonData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          // 对返回的数据进行筛选，确保基准物料和相似物料的指定价格类型都能被提取\r\n          const filteredData = []\r\n\r\n          // 获取基准物料和相似物料的目标价格类型名称\r\n          const basePriceTypeName = this.getPriceTypeName(this.currentComparison.priceType)\r\n          const comparePriceTypeName = this.getPriceTypeName(this.currentComparison.comparePriceType)\r\n\r\n          console.log('筛选条件:', {\r\n            baseItemName: this.currentComparison.itemName,\r\n            basePriceTypeName: basePriceTypeName,\r\n            compareItemName: this.currentComparison.compareItemName,\r\n            comparePriceTypeName: comparePriceTypeName\r\n          })\r\n\r\n          response.data.forEach(materialData => {\r\n            const filteredMaterialData = { ...materialData }\r\n\r\n            if (filteredMaterialData.procurementPriceVoList) {\r\n              // 只保留匹配的价格类型\r\n              filteredMaterialData.procurementPriceVoList = filteredMaterialData.procurementPriceVoList.filter(priceGroup => {\r\n                let isMatch = false\r\n                // 基准物料：匹配物料名称和基准价格类型\r\n                if (materialData.itemName === this.currentComparison.itemName) {\r\n                  isMatch = priceGroup.priceName === basePriceTypeName\r\n                  console.log(`基准物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${basePriceTypeName}] 匹配:${isMatch}`)\r\n                }\r\n\r\n                if(isMatch){\r\n                  return isMatch\r\n                }else{\r\n                  if (materialData.itemName === this.currentComparison.compareItemName) {\r\n                    const isMatch = priceGroup.priceName === comparePriceTypeName\r\n                    console.log(`相似物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${comparePriceTypeName}] 匹配:${isMatch}`)\r\n                    return isMatch\r\n                  }\r\n                }\r\n\r\n\r\n                return false\r\n              })\r\n\r\n              console.log(111111111)\r\n              console.log(filteredMaterialData.procurementPriceVoList)\r\n\r\n              // 只有当该物料有匹配的价格类型时才加入结果\r\n              if (filteredMaterialData.procurementPriceVoList.length > 0) {\r\n                filteredData.push(filteredMaterialData)\r\n                console.log(`添加物料[${materialData.itemName}]，包含${filteredMaterialData.procurementPriceVoList.length}个价格组`)\r\n              }\r\n            }\r\n          })\r\n\r\n          this.comparisonPriceData = filteredData\r\n          console.log('fetchComparisonData - 筛选后的数据:', this.comparisonPriceData)\r\n          console.log('筛选结果统计:', {\r\n            totalMaterials: filteredData.length,\r\n            materials: filteredData.map(m => ({\r\n              name: m.itemName,\r\n              priceGroupCount: m.procurementPriceVoList?.length || 0,\r\n              priceGroups: m.procurementPriceVoList?.map(p => p.priceName) || []\r\n            }))\r\n          })\r\n\r\n          // 渲染对比图表\r\n          this.$nextTick(() => {\r\n            this.renderComparisonChart()\r\n          })\r\n        } else {\r\n          console.error('获取对比数据失败', response)\r\n          this.$message.error('获取对比数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取对比数据失败:', error)\r\n        this.$message.error('获取对比数据失败：' + error.message)\r\n      } finally {\r\n        this.comparisonChartLoading = false\r\n      }\r\n    },\r\n\r\n    // 渲染对比图表（独立实现，不耦合现有趋势图）\r\n    renderComparisonChart() {\r\n      const chartDom = document.getElementById('comparisonChart')\r\n      if (!chartDom) {\r\n        console.error('找不到对比图表DOM元素')\r\n        return\r\n      }\r\n\r\n      // 清理现有实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n        } catch (err) {\r\n          console.error('清理现有对比图表实例失败:', err)\r\n        }\r\n      }\r\n\r\n      // 创建新的图表实例\r\n      try {\r\n        this.comparisonChartInstance = echarts.init(chartDom)\r\n      } catch (err) {\r\n        console.error('创建对比图表实例失败:', err)\r\n        return\r\n      }\r\n\r\n      if (!this.comparisonPriceData || this.comparisonPriceData.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      const formatDate = (dateStr) => {\r\n        const year = dateStr.substring(0, 4)\r\n        const month = dateStr.substring(4, 6)\r\n        const day = dateStr.substring(6, 8)\r\n        return `${year}年${month}月${day}日`\r\n      }\r\n\r\n      // 收集所有日期\r\n      let allDates = new Set()\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        if (materialData.procurementPriceVoList) {\r\n          materialData.procurementPriceVoList.forEach(priceGroup => {\r\n            if (priceGroup.priceList) {\r\n              priceGroup.priceList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      allDates = Array.from(allDates).sort()\r\n      const xAxisData = allDates.map(formatDate)\r\n\r\n      if (allDates.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      // 构建系列数据\r\n      const series = []\r\n      const legendData = []\r\n      const colors = ['#8fe9ff', '#ff9f7f', '#5fd8b6', '#ffb980']\r\n      let colorIndex = 0\r\n\r\n      console.log('=== 开始处理对比数据 ===')\r\n      console.log('对比数据总览:', {\r\n        materialCount: this.comparisonPriceData.length,\r\n        baseMaterial: this.currentComparison.itemName,\r\n        compareMaterial: this.currentComparison.compareItemName\r\n      })\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        const materialName = materialData.itemName\r\n        console.log(`\\n处理物料: ${materialName}`)\r\n\r\n        if (materialData.procurementPriceVoList) {\r\n          console.log(`  该物料有 ${materialData.procurementPriceVoList.length} 个价格组`)\r\n          materialData.procurementPriceVoList.forEach((priceGroup, index) => {\r\n            console.log(`  价格组 ${index + 1}: ${priceGroup.priceName}，数据点数量: ${priceGroup.priceList?.length || 0}`)\r\n          })\r\n\r\n          // 数据已经在fetchComparisonData中预先筛选过，这里直接处理所有匹配的价格组\r\n          materialData.procurementPriceVoList.forEach((priceGroup, groupIndex) => {\r\n            const priceData = allDates.map(date => {\r\n              const found = priceGroup.priceList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.price) : null\r\n            })\r\n\r\n            // 统计有效数据点\r\n            const validDataCount = priceData.filter(v => v !== null && v !== undefined).length\r\n            console.log(`    处理价格组[${priceGroup.priceName}]，有效数据点: ${validDataCount}/${priceData.length}`)\r\n\r\n            // 确保每条曲线都有唯一的名称和颜色，即使数据相同\r\n            const uniqueName = `${materialName}-${priceGroup.priceName}`\r\n            const lineColor = colors[colorIndex % colors.length]\r\n\r\n            // 检查是否已经有相同的数据，如果有则添加轻微偏移\r\n            const dataStr = JSON.stringify(priceData)\r\n            const existingSeries = series.find(s => JSON.stringify(s.data) === dataStr)\r\n            let adjustedData = priceData\r\n\r\n            if (existingSeries && priceData.some(v => v !== null)) {\r\n              // 为重复数据添加极小的偏移量（0.01），确保两条线都能显示\r\n              adjustedData = priceData.map(value => value !== null ? value + 0.01 : null)\r\n              console.log(`    检测到重复数据，为 ${uniqueName} 添加偏移`)\r\n            }\r\n\r\n            series.push({\r\n              name: uniqueName,\r\n              type: 'line',\r\n              data: adjustedData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 3,\r\n                color: lineColor,\r\n                // 如果是偏移的数据，使用虚线样式区分\r\n                type: adjustedData !== priceData ? 'dashed' : 'solid'\r\n              },\r\n              itemStyle: {\r\n                color: lineColor\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 6,\r\n              connectNulls: true,\r\n              // 添加z-index确保两条线都能显示\r\n              z: colorIndex + 1\r\n            })\r\n\r\n            legendData.push(uniqueName)\r\n            colorIndex++\r\n            console.log(`    ✓ 添加曲线: ${uniqueName}，颜色: ${lineColor}，有效数据: ${validDataCount}`)\r\n          })\r\n        }\r\n      })\r\n\r\n      console.log(`\\n=== 图表数据处理完成 ===`)\r\n      console.log(`总计添加 ${series.length} 条曲线:`)\r\n      series.forEach((s, i) => {\r\n        const validCount = s.data.filter(v => v !== null && v !== undefined).length\r\n        console.log(`  ${i + 1}. ${s.name} (有效数据: ${validCount})`)\r\n      })\r\n\r\n      // 计算Y轴范围\r\n      let priceMin, priceMax\r\n      const priceValues = series.flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n      if (priceValues.length > 0) {\r\n        priceMin = Math.min(...priceValues)\r\n        priceMax = Math.max(...priceValues)\r\n      }\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          formatter: function(params) {\r\n            let str = params[0].axisValueLabel + '<br/>'\r\n            params.forEach(item => {\r\n              if (item.value !== null && item.value !== undefined) {\r\n                str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`\r\n              } else {\r\n                str += `${item.marker}${item.seriesName}: -<br/>`\r\n              }\r\n            })\r\n            return str\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '12%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: function(index, value) {\r\n              if (index >= allDates.length || !allDates.length) return false\r\n\r\n              const uniqueMonths = new Set()\r\n              allDates.forEach(dateStr => {\r\n                const year = dateStr.substring(0, 4)\r\n                const month = dateStr.substring(4, 6)\r\n                uniqueMonths.add(`${year}${month}`)\r\n              })\r\n\r\n              const monthsCount = uniqueMonths.size\r\n              if (monthsCount <= 1) return true\r\n\r\n              const totalDataPoints = allDates.length\r\n              const idealInterval = Math.floor(totalDataPoints / Math.min(monthsCount, 8))\r\n\r\n              return index % Math.max(idealInterval, 1) === 0\r\n            },\r\n            formatter: function(value, index) {\r\n              if (index >= allDates.length) return ''\r\n              const originalDateStr = allDates[index]\r\n              if (!originalDateStr) return ''\r\n\r\n              const year = originalDateStr.substring(0, 4)\r\n              const month = parseInt(originalDateStr.substring(4, 6))\r\n              return `${year}.${month}`\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '价格（元/吨）',\r\n          min: priceMin,\r\n          max: priceMax,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee'\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: series\r\n      }\r\n\r\n      this.comparisonChartInstance.setOption(option, true)\r\n    },\r\n\r\n    // 检查两个曲线是否都已设置默认值，如果是则触发初始数据获取\r\n    checkAndTriggerInitialDataFetch() {\r\n      // 检查两个曲线是否都已经设置了默认的PB块\r\n      if (this.selectedPurchaseAmountMaterials.includes('PB块') &&\r\n        this.selectedMarketPriceMaterials.includes('PB块') &&\r\n        !this.hasInitializedPriceChart) {\r\n\r\n        this.hasInitializedPriceChart = true // 标记已经初始化过\r\n        console.log('两个曲线都已设置默认值，自动触发数据获取')\r\n\r\n        // 自动触发数据获取\r\n        this.$nextTick(() => {\r\n          this.fetchPriceAndStoreDataForNewChart()\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n  font-family: \"Microsoft YaHei\", sans-serif;\r\n}\r\n\r\n.dashboard-container {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  color: #fff;\r\n  overflow-x: hidden;\r\n  padding: 10px;\r\n}\r\n\r\n.dashboard-header {\r\n  text-align: center;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n  padding: 5px 0;\r\n}\r\n\r\n.dashboard-header h1 {\r\n  font-size: 24px;\r\n  position: relative;\r\n  display: inline-block;\r\n  padding: 5px 40px;\r\n}\r\n\r\n.dashboard-header::before,\r\n.dashboard-header::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 50%;\r\n  width: 30%;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.dashboard-header::before {\r\n  left: 0;\r\n}\r\n\r\n.dashboard-header::after {\r\n  right: 0;\r\n}\r\n\r\n.dashboard-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(12, 1fr);\r\n  grid-template-rows: auto auto auto auto auto auto;\r\n  gap: 10px;\r\n  min-height: calc(100vh - 80px);\r\n}\r\n\r\n.card {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 5px;\r\n  padding: 10px;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.card-title {\r\n  font-size: 14px;\r\n  margin-bottom: 5px;\r\n  font-weight: normal;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.chart-filter-dropdown-container {\r\n  z-index: 10;\r\n}\r\n\r\n.chart-filter-dropdown-container select {\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n  color: #fff;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  font-size: 12px;\r\n}\r\n\r\n.chart {\r\n  width: 100%;\r\n  height: calc(100% - 20px);\r\n  min-height: 200px;\r\n  flex: 1;\r\n}\r\n\r\n.stat-cards {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  height: 100%;\r\n  align-items: center;\r\n}\r\n\r\n.stat-card {\r\n  text-align: center;\r\n  flex-grow: 1;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 34px;\r\n  font-weight: bold;\r\n  color: #00ffff;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 18px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.chart-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: rgba(255,255,255,0.5);\r\n  font-size: 14px;\r\n}\r\n\r\n.material-chart-card {\r\n  height: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.material-chart-card .chart {\r\n  flex-grow: 1;\r\n  height: 250px;\r\n  min-height: 250px;\r\n}\r\n\r\n.time-filter {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.time-filter-btn {\r\n  padding: 6px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n}\r\n\r\n.time-filter-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.time-filter-btn.active {\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n  font-weight: 500;\r\n}\r\n\r\n.header-controls {\r\n  position: absolute;\r\n  right: 20px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  z-index: 1000;\r\n}\r\n\r\n.fullscreen-btn {\r\n  padding: 8px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 40px;\r\n  height: 32px;\r\n}\r\n\r\n.fullscreen-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n}\r\n\r\n/* AI价格预测区域样式 */\r\n.price-prediction-section {\r\n  margin-top: 15px;\r\n  margin-bottom: 15px;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-size: 13px;\r\n}\r\n\r\n.prediction-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.model-info {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 12px;\r\n}\r\n\r\n.prediction-content {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  border-left: 3px solid #00ffff;\r\n  position: relative;\r\n}\r\n\r\n.prediction-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n\r\n\r\n/* 多个预测结果的样式 */\r\n.predictions-container {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding-right: 5px;\r\n}\r\n\r\n.prediction-item {\r\n  margin-bottom: 15px;\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n  overflow: hidden;\r\n}\r\n\r\n.prediction-item.prediction-error {\r\n  border-left-color: #ff6b6b;\r\n}\r\n\r\n.prediction-material-title {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  padding: 8px 12px;\r\n  font-size: 13px;\r\n  font-weight: 600;\r\n  color: #00ffff;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-item.prediction-error .prediction-material-title {\r\n  background-color: rgba(255, 107, 107, 0.1);\r\n  color: #ff6b6b;\r\n  border-bottom-color: rgba(255, 107, 107, 0.2);\r\n}\r\n\r\n.prediction-material-title i {\r\n  margin-right: 6px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n/* 预测容器滚动条样式 */\r\n.predictions-container::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 一问一答样式 */\r\n.qa-section {\r\n  padding: 0;\r\n}\r\n\r\n.question-section, .answer-section {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.answer-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.qa-label {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  font-weight: 600;\r\n  font-size: 13px;\r\n}\r\n\r\n.question-label {\r\n  color: #ffb980;\r\n}\r\n\r\n.answer-label {\r\n  color: #00ffff;\r\n}\r\n\r\n.qa-label i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n.question-text, .answer-text {\r\n  background-color: rgba(0, 0, 0, 0.3);\r\n  border-radius: 8px;\r\n  padding: 12px 15px;\r\n  line-height: 1.6;\r\n  font-size: 13px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  white-space: pre-wrap;\r\n  word-wrap: break-word;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.question-text {\r\n  border-left: 3px solid #ffb980;\r\n}\r\n\r\n.answer-text {\r\n  border-left: 3px solid #00ffff;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding-right: 18px;\r\n}\r\n\r\n/* 问题文本样式 */\r\n.question-text {\r\n  font-style: italic;\r\n  color: rgba(255, 200, 150, 0.9);\r\n}\r\n\r\n/* 答案文本滚动条样式 */\r\n.answer-text::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 价格趋势卡片特殊样式 */\r\n.price-trend-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: auto;\r\n  min-height: 400px;\r\n}\r\n\r\n.price-trend-card .chart {\r\n  flex-shrink: 0;\r\n  height: 300px !important;\r\n  min-height: 300px;\r\n}\r\n\r\n.price-trend-card .price-prediction-section {\r\n  flex-shrink: 0;\r\n  margin-top: 15px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.inventory-total {\r\n  font-size: 12px;\r\n  color: #00ffff;\r\n  font-weight: normal;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 新增：价格趋势图控件样式 */\r\n.price-trend-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: rgba(16, 7, 33, 0.4);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.left-controls, .right-controls {\r\n  flex: 1;\r\n  max-width: 45%;\r\n}\r\n\r\n.curve-label {\r\n  font-size: 13px;\r\n  color: #00ffff;\r\n  margin-bottom: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n.left-controls .curve-label {\r\n  text-align: left;\r\n}\r\n\r\n.right-controls .curve-label {\r\n  text-align: right;\r\n}\r\n\r\n.dropdown-row {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.right-controls .dropdown-row {\r\n  justify-content: flex-end;\r\n}\r\n\r\n.fetch-data-btn-container {\r\n  text-align: right;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.modern-fetch-btn {\r\n  background: rgba(138, 43, 226, 0.7);\r\n  border: none;\r\n  border-radius: 12px;\r\n  padding: 10px 20px;\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 3px 10px rgba(138, 43, 226, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-fetch-btn:hover:not(:disabled) {\r\n  background: rgba(138, 43, 226, 0.9);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.5);\r\n}\r\n\r\n.modern-fetch-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.modern-fetch-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.modern-fetch-btn.loading {\r\n  background: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.modern-fetch-btn i {\r\n  margin-right: 8px;\r\n  animation: rotate 1s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n/* Element UI 下拉框样式覆盖 */\r\n.price-trend-controls .el-select {\r\n  background-color: transparent !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner {\r\n  background-color: #4a1c5a !important;\r\n  border: 1px solid rgba(116, 75, 162, 0.5) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 8px !important;\r\n  font-size: 13px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:hover {\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  box-shadow: 0 0 8px rgba(116, 75, 162, 0.3) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:focus {\r\n  border-color: #764ba2 !important;\r\n  box-shadow: 0 0 12px rgba(116, 75, 162, 0.5) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner::placeholder {\r\n  color: rgba(255, 255, 255, 0.7) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix i {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag {\r\n  background-color: rgba(116, 75, 162, 0.6) !important;\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 6px !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close:hover {\r\n  background-color: rgba(255, 255, 255, 0.2) !important;\r\n}\r\n\r\n/* 相似物料区域样式 */\r\n.similar-materials-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.similar-materials-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  font-size: 14px;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.similar-materials-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.section-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n.similar-materials-container {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 10px;\r\n  overflow-x: auto;\r\n}\r\n\r\n.materials-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  font-size: 13px;\r\n}\r\n\r\n.materials-table th {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  color: #00ffff;\r\n  padding: 8px 12px;\r\n  text-align: left;\r\n  border-bottom: 2px solid rgba(0, 212, 255, 0.3);\r\n  font-weight: 600;\r\n}\r\n\r\n.materials-table td {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.material-row {\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.material-row:hover {\r\n  background-color: rgba(0, 212, 255, 0.05);\r\n}\r\n\r\n.rank-cell {\r\n  text-align: center;\r\n  width: 60px;\r\n}\r\n\r\n.rank-badge {\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 50%;\r\n  color: #fff;\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n  min-width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.rank-first {\r\n  background: linear-gradient(135deg, #ffd700, #ffb347);\r\n  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.rank-second {\r\n  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);\r\n  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);\r\n}\r\n\r\n.rank-third {\r\n  background: linear-gradient(135deg, #cd7f32, #b8860b);\r\n  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);\r\n}\r\n\r\n.rank-default {\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.material-name, .compare-material-name {\r\n  font-weight: 500;\r\n  color: #fff;\r\n}\r\n\r\n.compare-material-name {\r\n  color: #00ffff;\r\n}\r\n\r\n.score-cell {\r\n  text-align: center;\r\n  width: 120px;\r\n  min-width: 120px;\r\n}\r\n\r\n.score-value {\r\n  display: inline-block;\r\n  padding: 2px 6px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 4px;\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n}\r\n\r\n.score-desc {\r\n  color: #ffb980;\r\n  font-style: italic;\r\n}\r\n\r\n.category-cell {\r\n  color: #5fd8b6;\r\n  font-weight: 500;\r\n}\r\n\r\n.similar-materials-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n.similar-materials-group {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.similar-materials-group:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.group-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n}\r\n\r\n.price-type-cell {\r\n  color: #e879ed;\r\n  font-size: 11px;\r\n  max-width: 120px;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.algorithm-desc {\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 11px;\r\n  font-style: italic;\r\n  margin-left: 8px;\r\n}\r\n\r\n.action-cell {\r\n  text-align: center;\r\n  width: 100px;\r\n}\r\n\r\n.view-comparison-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  color: #fff;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  white-space: nowrap;\r\n  min-width: 70px;\r\n}\r\n\r\n.view-comparison-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\r\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\r\n}\r\n\r\n.view-comparison-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.view-comparison-btn i {\r\n  font-size: 13px;\r\n}\r\n\r\n/* 对比弹框样式 */\r\n.comparison-dialog .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__header {\r\n  background: linear-gradient(135deg, rgba(33, 10, 56, 0.9), rgba(0, 212, 255, 0.2));\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__title {\r\n  color: #00ffff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close {\r\n  color: #00ffff;\r\n  font-size: 20px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close:hover {\r\n  color: #fff;\r\n  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n.comparison-dialog .el-dialog__body {\r\n  padding: 0;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-content {\r\n  padding: 20px;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.comparison-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 15px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.base-material {\r\n  color: #00ffff;\r\n  padding: 8px 16px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.vs-text {\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.compare-material {\r\n  color: #ea7ccc;\r\n  padding: 8px 16px;\r\n  background-color: rgba(234, 124, 204, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(234, 124, 204, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.similarity-info {\r\n  color: #ffb980;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  padding: 4px 12px;\r\n  background-color: rgba(255, 185, 128, 0.2);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 185, 128, 0.4);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-chart-container {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  overflow: hidden;\r\n  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.comparison-chart {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoLA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,qBAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,kBAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AACA,IAAAO,sBAAA,GAAAP,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAQ,IAAA;EACAC,MAAA,GAAAC,qBAAA,EAAAC,6BAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,IAAA;IACA,OAAAA,IAAA;MACA;MACAC,WAAA,GACA;QAAAC,EAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAF,EAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAF,EAAA;QAAAC,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,YAAA;MACAC,oBAAA;MAEA;MACAC,aAAA;MACAC,aAAA;MAEA;MACAC,mBAAA;MAEA;MACAC,0BAAA;MACAC,iBAAA;MAAA;;MAEA;MACAC,cAAA;MAEA;MACAC,wBAAA;MAEA;MACAC,YAAA;MACAC,oBAAA;MACAC,cAAA;QACA,IAAAC,WAAA,OAAAC,IAAA,GAAAC,WAAA;QACA,IAAAC,KAAA;QACA,SAAAC,IAAA,SAAAA,IAAA,IAAAJ,WAAA,EAAAI,IAAA;UACAD,KAAA,CAAAE,IAAA,CAAAD,IAAA,CAAAE,QAAA;QACA;QACA,OAAAH,KAAA;MACA;MACAI,mBAAA;MACAC,qBAAA;MACAC,uBAAA;MAEA;MACAC,sBAAA;MAAA;;MAEA;MACAC,wBAAA;MACAC,oBAAA;MACAC,mBAAA;MACAC,sBAAA;MAEA;MACAC,gBAAA;MACAC,gBAAA;MACAC,yBAAA;MAEA;MACAC,gBAAA;MAEA;MACAC,gBAAA;MAAA;MACAC,iBAAA;MAEA;MACAC,mBAAA;MACAC,gBAAA;IAAA,OAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzC,IAAA,8BACA,2BACA,mCAIA,0CACA,sCACA,8BAGA,uCACA,mCACA,0BAGA,gCACA,WAAAwC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzC,IAAA,8BAGA,gCAGA,gCACA,mCAGA,kCACA,6BACA,gCACA,8BACA,6BAGA,oCACA,SAAAwC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,EAAAzC,IAAA,uBACA,yBACA;EAEA;EAEA0C,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAC,gBAAA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,OAAA;MACA,KAAAC,oBAAA,CAAAC,OAAA,WAAAC,IAAA;QACA;QACA,IAAAC,QAAA,MAAAC,MAAA,CAAAF,IAAA,CAAAG,QAAA,OAAAD,MAAA,CAAAF,IAAA,CAAAI,QAAA,OAAAF,MAAA,CAAAF,IAAA,CAAAK,SAAA;QACA,IAAAC,UAAA,MAAAJ,MAAA,CAAAF,IAAA,CAAAG,QAAA,QAAAD,MAAA,CAAAN,KAAA,CAAAW,eAAA,CAAAP,IAAA,CAAAI,QAAA,UAAAF,MAAA,CAAAN,KAAA,CAAAY,gBAAA,CAAAR,IAAA,CAAAK,SAAA;QAEA,KAAAR,OAAA,CAAAS,UAAA;UACAT,OAAA,CAAAS,UAAA;YACAL,QAAA,EAAAA,QAAA;YACAQ,KAAA;UACA;QACA;QACAZ,OAAA,CAAAS,UAAA,EAAAG,KAAA,CAAAxC,IAAA,CAAA+B,IAAA;MACA;;MAEA;MACAU,MAAA,CAAAC,IAAA,CAAAd,OAAA,EAAAE,OAAA,WAAAa,GAAA;QACAf,OAAA,CAAAe,GAAA,EAAAH,KAAA,CAAAI,IAAA,WAAAC,CAAA,EAAAC,CAAA;UAAA,OAAAD,CAAA,CAAAE,IAAA,GAAAD,CAAA,CAAAC,IAAA;QAAA;MACA;MAEA,OAAAnB,OAAA;IACA;EACA;EAEAoB,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,KAAAC,wBAAA;IACA,KAAAC,kBAAA;IACA,KAAAC,wBAAA;IACA,KAAAC,0BAAA;IACA,KAAAC,4BAAA;IACA;IACA,KAAAC,yBAAA,GAAAC,IAAA;MACAP,MAAA,CAAAQ,2BAAA;IACA;IACA;IACA,KAAAC,8BAAA;IACA;IACA,KAAAC,qBAAA;;IAEA;IACA,KAAAC,+BAAA;IACA,KAAAC,4BAAA;;IAEA;IACA,KAAAC,sBAAA;IAEA,KAAAC,mBAAA;IACA,KAAAC,sBAAA;;IAEA;IACAC,MAAA,CAAAC,gBAAA,gBAAAC,eAAA;EACA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACA,KAAAC,iBAAA;IACA,KAAAC,wBAAA;IACAL,MAAA,CAAAM,mBAAA,gBAAAJ,eAAA;;IAEA;IACA,KAAA7C,MAAA,CAAAkD,QAAA;EACA;EAEAC,OAAA;IACA;IACAT,sBAAA,WAAAA,uBAAA;MACA,IAAAU,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAE,EAAA,gBAAAC,sBAAA;MACA;IACA;IAEA;IACAP,wBAAA,WAAAA,yBAAA;MACA,IAAAI,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAI,GAAA,gBAAAD,sBAAA;MACA;IACA;IAEA;IACAA,sBAAA,WAAAA,uBAAA;MAAA,IAAAE,MAAA;MACA,IAAAL,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACA,IAAAtD,YAAA,GAAAqD,mBAAA,CAAArD,YAAA;QACA,KAAAC,MAAA,CAAAkD,QAAA,0BAAAnD,YAAA;;QAEA;QACA,KAAA2D,SAAA;UACAC,UAAA;YACAF,MAAA,CAAAZ,eAAA;UACA;QACA;MACA;IACA;IAEA;IACAe,gBAAA,WAAAA,iBAAAC,aAAA;MAAA,WAAAC,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAC,QAAA;QAAA,WAAAF,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OACA,IAAAC,2BAAA;gBAAAR,aAAA,EAAAA;cAAA;YAAA;cAAA,OAAAM,QAAA,CAAA5C,CAAA,IAAA4C,QAAA,CAAAG,CAAA;UAAA;QAAA,GAAAL,OAAA;MAAA;IACA;IAEAM,eAAA,WAAAA,gBAAAC,QAAA;MAAA,WAAAV,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAS,SAAA;QAAA,WAAAV,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAQ,SAAA;UAAA,kBAAAA,SAAA,CAAAN,CAAA;YAAA;cAAAM,SAAA,CAAAN,CAAA;cAAA,OACA,IAAAO,mCAAA;gBAAAH,QAAA,EAAAA;cAAA;YAAA;cAAA,OAAAE,SAAA,CAAAnD,CAAA,IAAAmD,SAAA,CAAAJ,CAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IAEAG,eAAA,WAAAA,gBAAAC,MAAA;MAAA,WAAAf,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAc,SAAA;QAAA,WAAAf,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAa,SAAA;UAAA,kBAAAA,SAAA,CAAAX,CAAA;YAAA;cAAAW,SAAA,CAAAX,CAAA;cAAA,OACA,IAAAY,mCAAA,EAAAH,MAAA;YAAA;cAAA,OAAAE,SAAA,CAAAxD,CAAA,IAAAwD,SAAA,CAAAT,CAAA;UAAA;QAAA,GAAAQ,QAAA;MAAA;IACA;IAEAG,eAAA,WAAAA,gBAAAJ,MAAA;MAAA,WAAAf,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAkB,SAAA;QAAA,WAAAnB,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAiB,SAAA;UAAA,kBAAAA,SAAA,CAAAf,CAAA;YAAA;cAAAe,SAAA,CAAAf,CAAA;cAAA,OACA,IAAAgB,+BAAA,EAAAP,MAAA;YAAA;cAAA,OAAAM,SAAA,CAAA5D,CAAA,IAAA4D,SAAA,CAAAb,CAAA;UAAA;QAAA,GAAAY,QAAA;MAAA;IACA;IAEAG,eAAA,WAAAA,gBAAAR,MAAA;MAAA,WAAAf,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAsB,SAAA;QAAA,WAAAvB,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAqB,SAAA;UAAA,kBAAAA,SAAA,CAAAnB,CAAA;YAAA;cAAAmB,SAAA,CAAAnB,CAAA;cAAA,OACA,IAAAoB,mCAAA,EAAAX,MAAA;YAAA;cAAA,OAAAU,SAAA,CAAAhE,CAAA,IAAAgE,SAAA,CAAAjB,CAAA;UAAA;QAAA,GAAAgB,QAAA;MAAA;IACA;IAEAG,iBAAA,WAAAA,kBAAA;MAAA,WAAA3B,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA0B,SAAA;QAAA,WAAA3B,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAyB,SAAA;UAAA,kBAAAA,SAAA,CAAAvB,CAAA;YAAA;cAAAuB,SAAA,CAAAvB,CAAA;cAAA,OACA,IAAAwB,qCAAA;YAAA;cAAA,OAAAD,SAAA,CAAApE,CAAA,IAAAoE,SAAA,CAAArB,CAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IACA;IAEAG,mBAAA,WAAAA,oBAAA;MAAA,WAAA/B,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA8B,SAAA;QAAA,WAAA/B,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA6B,SAAA;UAAA,kBAAAA,SAAA,CAAA3B,CAAA;YAAA;cAAA2B,SAAA,CAAA3B,CAAA;cAAA,OACA,IAAA4B,uCAAA;YAAA;cAAA,OAAAD,SAAA,CAAAxE,CAAA,IAAAwE,SAAA,CAAAzB,CAAA;UAAA;QAAA,GAAAwB,QAAA;MAAA;IACA;IAEAG,gBAAA,WAAAA,iBAAApB,MAAA;MAAA,WAAAf,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAkC,SAAA;QAAA,WAAAnC,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAiC,SAAA;UAAA,kBAAAA,SAAA,CAAA/B,CAAA;YAAA;cAAA+B,SAAA,CAAA/B,CAAA;cAAA,OACA,IAAAgC,oCAAA,EAAAvB,MAAA;YAAA;cAAA,OAAAsB,SAAA,CAAA5E,CAAA,IAAA4E,SAAA,CAAA7B,CAAA;UAAA;QAAA,GAAA4B,QAAA;MAAA;IACA;IAEAG,4BAAA,WAAAA,6BAAAxB,MAAA;MAAA,WAAAf,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAsC,SAAA;QAAA,WAAAvC,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAqC,SAAA;UAAA,kBAAAA,SAAA,CAAAnC,CAAA;YAAA;cAAAmC,SAAA,CAAAnC,CAAA;cAAA,OACA,IAAAoC,gDAAA,EAAA3B,MAAA;YAAA;cAAA,OAAA0B,SAAA,CAAAhF,CAAA,IAAAgF,SAAA,CAAAjC,CAAA;UAAA;QAAA,GAAAgC,QAAA;MAAA;IACA;IAEAG,mBAAA,WAAAA,oBAAA5B,MAAA;MAAA,WAAAf,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA0C,SAAA;QAAA,WAAA3C,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAyC,SAAA;UAAA,kBAAAA,SAAA,CAAAvC,CAAA;YAAA;cAAAuC,SAAA,CAAAvC,CAAA;cAAA,OACA,IAAAwC,uCAAA,EAAA/B,MAAA;YAAA;cAAA,OAAA8B,SAAA,CAAApF,CAAA,IAAAoF,SAAA,CAAArC,CAAA;UAAA;QAAA,GAAAoC,QAAA;MAAA;IACA;IAEA;IACAG,0BAAA,WAAAA,2BAAAhD,aAAA;MACA,QAAAA,aAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;MACA;IACA;IAEA;IACAjC,wBAAA,WAAAA,yBAAA;MACA,KAAAvF,OAAA;QACAyK,OAAA,CAAAC,KAAA;QACAC,QAAA,CAAAC,gBAAA,WAAAzG,OAAA,WAAA0G,EAAA;UACAA,EAAA,CAAAC,SAAA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAtF,kBAAA,WAAAA,mBAAA;MAAA,IAAAuF,UAAA,GAAAC,SAAA;QAAAC,MAAA;MAAA,WAAAxD,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAuD,SAAA;QAAA,IAAAC,kBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,EAAA;QAAA,WAAA9D,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA4D,SAAA;UAAA,kBAAAA,SAAA,CAAA1D,CAAA;YAAA;cAAAoD,kBAAA,GAAAJ,UAAA,CAAAW,MAAA,QAAAX,UAAA,QAAAY,SAAA,GAAAZ,UAAA;cACAE,MAAA,CAAA5J,oBAAA,GAAA8J,kBAAA;;cAEA;cACAF,MAAA,CAAAvE,iBAAA;cAAA+E,SAAA,CAAAG,CAAA;cAAAH,SAAA,CAAA1D,CAAA;cAAA,OAIA8D,OAAA,CAAAC,GAAA,EACAb,MAAA,CAAA1D,gBAAA,CAAA4D,kBAAA,GACAF,MAAA,CAAArB,gBAAA;gBAAApC,aAAA,EAAA2D;cAAA,GACA;YAAA;cAAAC,kBAAA,GAAAK,SAAA,CAAAxD,CAAA;cAAAoD,mBAAA,OAAAU,eAAA,CAAAvI,OAAA,EAAA4H,kBAAA;cAHAE,iBAAA,GAAAD,mBAAA;cAAAE,qBAAA,GAAAF,mBAAA;cAKA;cACA,IAAAC,iBAAA,IAAAA,iBAAA,CAAAxK,IAAA;gBACAmK,MAAA,CAAA3J,aAAA,GAAAgK,iBAAA,CAAAxK,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,eAAAf,MAAA,CAAA3J,aAAA;cACA;gBACAmJ,OAAA,CAAAC,KAAA,wBAAAY,iBAAA;gBACAL,MAAA,CAAAgB,gBAAA;cACA;;cAEA;cACA,IAAAV,qBAAA,IAAAA,qBAAA,CAAAzK,IAAA;gBACAmK,MAAA,CAAA1J,aAAA,GAAAgK,qBAAA,CAAAzK,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,gBAAAf,MAAA,CAAA1J,aAAA;cACA;gBACAkJ,OAAA,CAAAC,KAAA,eAAAa,qBAAA;gBACAN,MAAA,CAAA1J,aAAA;cACA;cAEA0J,MAAA,CAAAiB,aAAA;cAAAT,SAAA,CAAA1D,CAAA;cAAA;YAAA;cAAA0D,SAAA,CAAAG,CAAA;cAAAJ,EAAA,GAAAC,SAAA,CAAAxD,CAAA;cAEAwC,OAAA,CAAAC,KAAA,iBAAAc,EAAA;cACAP,MAAA,CAAAgB,gBAAA,cAAAT,EAAA,CAAAW,OAAA;YAAA;cAAA,OAAAV,SAAA,CAAAvG,CAAA;UAAA;QAAA,GAAAgG,QAAA;MAAA;IAEA;IAEA;IACAe,gBAAA,WAAAA,iBAAAE,OAAA;MACAxB,QAAA,CAAAC,gBAAA,WAAAzG,OAAA,WAAAiI,KAAA;QACAA,KAAA,CAAAtB,SAAA,uCAAAxG,MAAA,CAAA6H,OAAA;MACA;IACA;IAEA;IACAE,sBAAA,WAAAA,uBAAAC,QAAA,EAAA9E,aAAA;MACA,KAAApG,YAAA,GAAAkL,QAAA;MACA,KAAAjL,oBAAA,GAAAmG,aAAA;MACAiD,OAAA,CAAAuB,GAAA,aAAAM,QAAA,SAAA9E,aAAA;MAEA,KAAAd,iBAAA;MACA,KAAAlB,kBAAA,CAAAgC,aAAA;MACA;MACA,KAAAzB,8BAAA;MACA;MACA,KAAAC,qBAAA;MACA;MACA,KAAAF,2BAAA;MACA;;MAEA;MACA,KAAAG,+BAAA;MACA,KAAAC,4BAAA;IACA;IAEA;IACAQ,iBAAA,WAAAA,kBAAA;MACA5B,MAAA,CAAAyH,MAAA,MAAA5K,cAAA,EAAAwC,OAAA,WAAAqI,QAAA;QACA,IAAAA,QAAA,IAAAA,QAAA,CAAAC,UAAA;UACAC,aAAA,CAAAF,QAAA,CAAAC,UAAA;UACAD,QAAA,CAAAC,UAAA;QACA;MACA;IACA;IAEA;IACAjG,eAAA,WAAAA,gBAAA;MACA1B,MAAA,CAAAyH,MAAA,MAAA5K,cAAA,EAAAwC,OAAA,WAAAqI,QAAA;QACA,IAAAA,QAAA;UACA;YACAA,QAAA,CAAAG,MAAA;UACA,SAAAC,GAAA;YACAnC,OAAA,CAAAC,KAAA,cAAAkC,GAAA;UACA;QACA;MACA;IACA;IAEA;IACAV,aAAA,WAAAA,cAAA;MACAzB,OAAA,CAAAuB,GAAA;MACA;QACA;QACA;QACA;QACA;;QAEA;QACA,KAAAa,iBAAA;;QAEA;QACA,KAAAC,qBAAA;QACA,KAAAC,oBAAA;;QAEA;;QAEA;;QAEAtC,OAAA,CAAAuB,GAAA;MACA,SAAAY,GAAA;QACAnC,OAAA,CAAAC,KAAA,gBAAAkC,GAAA;QACA,KAAAX,gBAAA,eAAAW,GAAA,CAAAT,OAAA;MACA;IACA;IAEA;IACAY,oBAAA,WAAAA,qBAAAC,eAAA,EAAA7E,QAAA,EAAA8E,gBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAzF,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAwF,UAAA;QAAA,IAAAC,QAAA,EAAAC,GAAA;QAAA,WAAA3F,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAyF,UAAA;UAAA,kBAAAA,UAAA,CAAAvF,CAAA;YAAA;cAAAuF,UAAA,CAAA1B,CAAA;cAAA0B,UAAA,CAAAvF,CAAA;cAAA,OAEAmF,MAAA,CAAAhF,eAAA,CAAAC,QAAA;YAAA;cAAAiF,QAAA,GAAAE,UAAA,CAAArF,CAAA;cAEA,IAAAmF,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAoM,MAAA,CAAAD,gBAAA,IAAAG,QAAA,CAAAtM,IAAA;cACA;gBACA2J,OAAA,CAAAC,KAAA,2DAAApG,MAAA,CAAA6D,QAAA,QAAAiF,QAAA;gBACAF,MAAA,CAAAD,gBAAA;cACA;cAAAK,UAAA,CAAAvF,CAAA;cAAA;YAAA;cAAAuF,UAAA,CAAA1B,CAAA;cAAAyB,GAAA,GAAAC,UAAA,CAAArF,CAAA;cAEAwC,OAAA,CAAAC,KAAA,kCAAApG,MAAA,CAAA0I,eAAA,QAAAK,GAAA;cACAH,MAAA,CAAAD,gBAAA;YAAA;cAAA,OAAAK,UAAA,CAAApI,CAAA;UAAA;QAAA,GAAAiI,SAAA;MAAA;IAEA;IAEA;IACAM,8BAAA,WAAAA,+BAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjG,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAgG,UAAA;QAAA,WAAAjG,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA+F,UAAA;UAAA,kBAAAA,UAAA,CAAA7F,CAAA;YAAA;cAAA6F,UAAA,CAAA7F,CAAA;cAAA,OACA2F,MAAA,CAAAG,wBAAA;YAAA;cAAA,OAAAD,UAAA,CAAA1I,CAAA;UAAA;QAAA,GAAAyI,SAAA;MAAA;IACA;IAEAG,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAtG,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAqG,UAAA;QAAA,WAAAtG,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAoG,UAAA;UAAA,kBAAAA,UAAA,CAAAlG,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,YAAA+B,MAAA,CAAArM,iBAAA;cAAAuM,UAAA,CAAAlG,CAAA;cAAA,OACAgG,MAAA,CAAAF,wBAAA;YAAA;cAAA,OAAAI,UAAA,CAAA/I,CAAA;UAAA;QAAA,GAAA8I,SAAA;MAAA;IACA;IAEAH,wBAAA,WAAAA,yBAAA;MAAA,IAAAK,MAAA;MAAA,WAAAzG,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAwG,UAAA;QAAA,IAAAC,OAAA,EAAAhB,QAAA,EAAAiB,eAAA,EAAAC,SAAA,EAAAC,gBAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,WAAA/G,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA6G,UAAA;UAAA,kBAAAA,UAAA,CAAA3G,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,0CAAA1H,MAAA,CAAA4J,MAAA,CAAAzM,0BAAA,mBAAA6C,MAAA,CAAA4J,MAAA,CAAAxM,iBAAA;cACA0M,OAAA,GAAAF,MAAA,CAAAvM,cAAA,CAAAgN,iBAAA;cAAA,IACAP,OAAA;gBAAAM,UAAA,CAAA3G,CAAA;gBAAA;cAAA;cACA0C,OAAA,CAAAC,KAAA;cAAA,OAAAgE,UAAA,CAAAxJ,CAAA;YAAA;cAIA,IAAAkJ,OAAA,CAAA3B,UAAA;gBACAC,aAAA,CAAA0B,OAAA,CAAA3B,UAAA;gBACA2B,OAAA,CAAA3B,UAAA;cACA;cAAA,MAEA,CAAAyB,MAAA,CAAAzM,0BAAA,IAAAyM,MAAA,CAAAzM,0BAAA;gBAAAiN,UAAA,CAAA3G,CAAA;gBAAA;cAAA;cACA;cACAqG,OAAA,CAAAQ,WAAA;cAAAF,UAAA,CAAA9C,CAAA;cAAA8C,UAAA,CAAA3G,CAAA;cAAA,OAEAmG,MAAA,CAAAtF,eAAA;gBACApB,aAAA,EAAA0G,MAAA,CAAA7M,oBAAA;gBACAwN,SAAA,EAAAX,MAAA,CAAAxM;cACA;YAAA;cAHA0L,QAAA,GAAAsB,UAAA,CAAAzG,CAAA;cAKAoG,eAAA;cACA,IAAAjB,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAuN,eAAA,GAAAjB,QAAA,CAAAtM,IAAA;cACA;gBACA2J,OAAA,CAAAC,KAAA,8BAAA0C,QAAA;gBACAiB,eAAA,GAAAH,MAAA,CAAAtM,wBAAA;cACA;cACAsM,MAAA,CAAAY,6BAAA,CAAAV,OAAA,EAAAC,eAAA;cAAAK,UAAA,CAAA3G,CAAA;cAAA;YAAA;cAAA2G,UAAA,CAAA9C,CAAA;cAAA4C,GAAA,GAAAE,UAAA,CAAAzG,CAAA;cAEAwC,OAAA,CAAAC,KAAA,mFAAA8D,GAAA;cACAN,MAAA,CAAAY,6BAAA,CAAAV,OAAA,EAAAF,MAAA,CAAAtM,wBAAA;YAAA;cAAA8M,UAAA,CAAA9C,CAAA;cAEAwC,OAAA,CAAAW,WAAA;cAAA,OAAAL,UAAA,CAAAM,CAAA;YAAA;cAAAN,UAAA,CAAA3G,CAAA;cAAA;YAAA;cAGAqG,OAAA,CAAAQ,WAAA;cAAAF,UAAA,CAAA9C,CAAA;cAAA8C,UAAA,CAAA3G,CAAA;cAAA,OAEAmG,MAAA,CAAAtF,eAAA;gBACApB,aAAA,EAAA0G,MAAA,CAAA7M,oBAAA;gBACA4N,MAAA,EAAAf,MAAA,CAAAzM,0BAAA;gBACAoN,SAAA,EAAAX,MAAA,CAAAxM;cACA;YAAA;cAJA0L,SAAA,GAAAsB,UAAA,CAAAzG,CAAA;cAMAoG,gBAAA;cACA,IAAAjB,SAAA,IAAAA,SAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,SAAA,CAAAtM,IAAA;gBACAuN,gBAAA,GAAAjB,SAAA,CAAAtM,IAAA;cACA;gBACA2J,OAAA,CAAAC,KAAA,8BAAA0C,SAAA;cACA;cACAc,MAAA,CAAAY,6BAAA,CAAAV,OAAA,EAAAC,gBAAA;cAAAK,UAAA,CAAA3G,CAAA;cAAA;YAAA;cAAA2G,UAAA,CAAA9C,CAAA;cAAA6C,GAAA,GAAAC,UAAA,CAAAzG,CAAA;cAEAwC,OAAA,CAAAC,KAAA,mFAAA+D,GAAA;cACA9D,QAAA,CAAAuE,cAAA,sBAAApE,SAAA;YAAA;cAAA4D,UAAA,CAAA9C,CAAA;cAEAwC,OAAA,CAAAW,WAAA;cAAA,OAAAL,UAAA,CAAAM,CAAA;YAAA;cAAA,OAAAN,UAAA,CAAAxJ,CAAA;UAAA;QAAA,GAAAiJ,SAAA;MAAA;IAGA;IAEA;IACA/H,mBAAA,WAAAA,oBAAA;MACA,IAAA+I,cAAA,OAAAC,cAAA,WAAAC,OAAA;QAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAA/L,OAAA,EACA6L,OAAA;UAAAG,KAAA;QAAA;UAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAvH,CAAA,IAAA2H,IAAA;YAAA,IAAAC,KAAA,GAAAH,KAAA,CAAArO,KAAA;YACA,IAAAyO,MAAA,GAAAD,KAAA,CAAAE,MAAA,CAAAjF,gBAAA;YACAgF,MAAA,CAAAzL,OAAA,WAAAiI,KAAA;cACA,IAAAA,KAAA,CAAAnL,EAAA;gBACA,IAAAuL,QAAA,GAAAxM,OAAA,CAAA8P,gBAAA,CAAAnF,QAAA,CAAAuE,cAAA,CAAA9C,KAAA,CAAAnL,EAAA;gBACA,IAAAuL,QAAA;kBACAA,QAAA,CAAAG,MAAA;gBACA;cACA;YACA;UACA;QAAA,SAAAC,GAAA;UAAA0C,SAAA,CAAAS,CAAA,CAAAnD,GAAA;QAAA;UAAA0C,SAAA,CAAAN,CAAA;QAAA;MACA;MAEArE,QAAA,CAAAC,gBAAA,UAAAzG,OAAA,WAAA6L,IAAA;QACAb,cAAA,CAAAc,OAAA,CAAAD,IAAA;MACA;IACA;IAEAE,gBAAA,WAAAA,iBAAA;MACA,IAAAnJ,mBAAA,IAAAA,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAoJ,MAAA;MACA;QACA,KAAAC,QAAA;UACAjE,OAAA;UACAkE,IAAA;QACA;MACA;IACA;IAEAC,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MAAA,WAAA9I,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA6I,UAAA;QAAA,WAAA9I,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA4I,UAAA;UAAA,kBAAAA,UAAA,CAAA1I,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,UAAAuE,MAAA,CAAA1O,YAAA;cAAA4O,UAAA,CAAA1I,CAAA;cAAA,OACAwI,MAAA,CAAA9K,wBAAA;YAAA;cAAA,OAAAgL,UAAA,CAAAvL,CAAA;UAAA;QAAA,GAAAsL,SAAA;MAAA;IACA;IAEAE,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAlJ,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAiJ,UAAA;QAAA,WAAAlJ,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAgJ,UAAA;UAAA,kBAAAA,UAAA,CAAA9I,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,YAAA2E,MAAA,CAAA7O,oBAAA;cAAA+O,UAAA,CAAA9I,CAAA;cAAA,OACA4I,MAAA,CAAAlL,wBAAA;YAAA;cAAA,OAAAoL,UAAA,CAAA3L,CAAA;UAAA;QAAA,GAAA0L,SAAA;MAAA;IACA;IAEA;IACAnL,wBAAA,WAAAA,yBAAA;MAAA,IAAAqL,MAAA;MAAA,WAAArJ,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAoJ,UAAA;QAAA,IAAAvI,MAAA,EAAA4E,QAAA,EAAA4D,GAAA;QAAA,WAAAtJ,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAoJ,UAAA;UAAA,kBAAAA,UAAA,CAAAlJ,CAAA;YAAA;cAAAkJ,UAAA,CAAArF,CAAA;cAEApD,MAAA,OAEA;cACA,IAAAsI,MAAA,CAAAhP,oBAAA,IAAAgP,MAAA,CAAAhP,oBAAA;gBACA0G,MAAA,CAAA0I,YAAA,GAAAJ,MAAA,CAAAhP,oBAAA;cACA;;cAEA;cACA,IAAAgP,MAAA,CAAAjP,YAAA;gBACA2G,MAAA,CAAA2I,QAAA,IAAAL,MAAA,CAAAjP,YAAA;cACA;gBACA2G,MAAA,CAAA2I,QAAA,GAAAL,MAAA,CAAA/O,cAAA;cACA;cAEA0I,OAAA,CAAAuB,GAAA,qCAAAxD,MAAA;cAAAyI,UAAA,CAAAlJ,CAAA;cAAA,OACA+I,MAAA,CAAA9H,eAAA,CAAAR,MAAA;YAAA;cAAA4E,QAAA,GAAA6D,UAAA,CAAAhJ,CAAA;cACAwC,OAAA,CAAAuB,GAAA,qCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACAgQ,MAAA,CAAAvO,mBAAA,GAAA6K,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,sCAAA8E,MAAA,CAAAvO,mBAAA;cACA;gBACA;gBACAuO,MAAA,CAAAvO,mBAAA,GAAAuO,MAAA,CAAAM,iBAAA;gBACA3G,OAAA,CAAAuB,GAAA,uCAAA8E,MAAA,CAAAvO,mBAAA;cACA;cAAA0O,UAAA,CAAAlJ,CAAA;cAAA;YAAA;cAAAkJ,UAAA,CAAArF,CAAA;cAAAoF,GAAA,GAAAC,UAAA,CAAAhJ,CAAA;cAEAwC,OAAA,CAAAC,KAAA,uBAAAsG,GAAA;cACA;cACAF,MAAA,CAAAvO,mBAAA,GAAAuO,MAAA,CAAAM,iBAAA;YAAA;cAGA;cACAN,MAAA,CAAAO,yBAAA;YAAA;cAAA,OAAAJ,UAAA,CAAA/L,CAAA;UAAA;QAAA,GAAA6L,SAAA;MAAA;IACA;IAEA;IACAK,iBAAA,WAAAA,kBAAA;MACA,QACA;QACAhP,IAAA;QACAkP,mBAAA,GACA;UAAAC,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA;MAEA,GACA;QACApP,IAAA;QACAkP,mBAAA,GACA;UAAAC,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA,GACA;UAAAD,UAAA;UAAAC,MAAA;QAAA;MAEA,EACA;IACA;IAEA9L,0BAAA,WAAAA,2BAAA;MAAA,IAAA+L,OAAA;MAAA,WAAAhK,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA+J,UAAA;QAAA,IAAAtE,QAAA,EAAAuE,GAAA;QAAA,WAAAjK,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA+J,UAAA;UAAA,kBAAAA,UAAA,CAAA7J,CAAA;YAAA;cAAA6J,UAAA,CAAAhG,CAAA;cAAAgG,UAAA,CAAA7J,CAAA;cAAA,OAEA0J,OAAA,CAAArI,iBAAA;YAAA;cAAAgE,QAAA,GAAAwE,UAAA,CAAA3J,CAAA;cACAwC,OAAA,CAAAuB,GAAA,uCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACA2Q,OAAA,CAAAjP,qBAAA,GAAA4K,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,wCAAAyF,OAAA,CAAAjP,qBAAA;cACA;gBACAiI,OAAA,CAAAC,KAAA,sBAAA0C,QAAA;gBACA;gBACAqE,OAAA,CAAAjP,qBAAA,GAAAiP,OAAA,CAAAI,mBAAA;cACA;cAAAD,UAAA,CAAA7J,CAAA;cAAA;YAAA;cAAA6J,UAAA,CAAAhG,CAAA;cAAA+F,GAAA,GAAAC,UAAA,CAAA3J,CAAA;cAEAwC,OAAA,CAAAC,KAAA,uBAAAiH,GAAA;cACA;cACAF,OAAA,CAAAjP,qBAAA,GAAAiP,OAAA,CAAAI,mBAAA;YAAA;cAGA;cACAJ,OAAA,CAAApK,SAAA;gBACAoK,OAAA,CAAAK,0BAAA;cACA;YAAA;cAAA,OAAAF,UAAA,CAAA1M,CAAA;UAAA;QAAA,GAAAwM,SAAA;MAAA;IACA;IAEA;IACAG,mBAAA,WAAAA,oBAAA;MACA,QACA;QACAX,YAAA;QACAa,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,GACA;QACAhB,YAAA;QACAa,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,GACA;QACAhB,YAAA;QACAa,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,GACA;QACAhB,YAAA;QACAa,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,GACA;QACAhB,YAAA;QACAa,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,GACA;QACAhB,YAAA;QACAa,YAAA;QACAC,qBAAA;QACAC,0BAAA;QACAC,oBAAA;MACA,EACA;IACA;IAEAvM,4BAAA,WAAAA,6BAAA;MAAA,IAAAwM,OAAA;MAAA,WAAA1K,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAyK,UAAA;QAAA,IAAAhF,QAAA,EAAAiF,GAAA;QAAA,WAAA3K,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAyK,UAAA;UAAA,kBAAAA,UAAA,CAAAvK,CAAA;YAAA;cAAAuK,UAAA,CAAA1G,CAAA;cAAA0G,UAAA,CAAAvK,CAAA;cAAA,OAEAoK,OAAA,CAAA3I,mBAAA;YAAA;cAAA4D,QAAA,GAAAkF,UAAA,CAAArK,CAAA;cACAwC,OAAA,CAAAuB,GAAA,yCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACAqR,OAAA,CAAA1P,uBAAA,GAAA2K,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,0CAAAmG,OAAA,CAAA1P,uBAAA;cACA;gBACAgI,OAAA,CAAAC,KAAA,gBAAA0C,QAAA;gBACA+E,OAAA,CAAA1P,uBAAA;cACA;cAAA6P,UAAA,CAAAvK,CAAA;cAAA;YAAA;cAAAuK,UAAA,CAAA1G,CAAA;cAAAyG,GAAA,GAAAC,UAAA,CAAArK,CAAA;cAEAwC,OAAA,CAAAC,KAAA,iBAAA2H,GAAA;cACAF,OAAA,CAAA1P,uBAAA;YAAA;cAGA;cACA0P,OAAA,CAAA9K,SAAA;gBACA8K,OAAA,CAAAI,4BAAA;cACA;YAAA;cAAA,OAAAD,UAAA,CAAApN,CAAA;UAAA;QAAA,GAAAkN,SAAA;MAAA;IACA;IAIA;IACAI,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,OAAA;MAAA,WAAAhL,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA+K,UAAA;QAAA,WAAAhL,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA8K,UAAA;UAAA,kBAAAA,UAAA,CAAA5K,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,YAAAyG,OAAA,CAAA9P,wBAAA;cACA8P,OAAA,CAAA7P,oBAAA;cAAA+P,UAAA,CAAA5K,CAAA;cAAA,OACA0K,OAAA,CAAA7M,yBAAA;YAAA;cAAA+M,UAAA,CAAA5K,CAAA;cAAA,OACA0K,OAAA,CAAA3M,2BAAA;YAAA;cAAA,OAAA6M,UAAA,CAAAzN,CAAA;UAAA;QAAA,GAAAwN,SAAA;MAAA;IACA;IAEAE,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,OAAA;MAAA,WAAApL,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAmL,UAAA;QAAA,WAAApL,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAkL,UAAA;UAAA,kBAAAA,UAAA,CAAAhL,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,YAAA6G,OAAA,CAAAjQ,oBAAA;cAAAmQ,UAAA,CAAAhL,CAAA;cAAA,OACA8K,OAAA,CAAA/M,2BAAA;YAAA;cAAA,OAAAiN,UAAA,CAAA7N,CAAA;UAAA;QAAA,GAAA4N,SAAA;MAAA;IACA;IAEAlN,yBAAA,WAAAA,0BAAA;MAAA,IAAAoN,OAAA;MAAA,WAAAvL,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAsL,UAAA;QAAA,IAAA9K,QAAA,EAAAiF,QAAA,EAAA8F,GAAA;QAAA,WAAAxL,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAsL,UAAA;UAAA,kBAAAA,UAAA,CAAApL,CAAA;YAAA;cAAA,MACAiL,OAAA,CAAArQ,wBAAA;gBAAAwQ,UAAA,CAAApL,CAAA;gBAAA;cAAA;cACA;cACAiL,OAAA,CAAAnQ,mBAAA;cAAAsQ,UAAA,CAAApL,CAAA;cAAA;YAAA;cAEA;cACAI,QAAA,GAAAiL,QAAA,CAAAJ,OAAA,CAAArQ,wBAAA;cAAAwQ,UAAA,CAAAvH,CAAA;cAAAuH,UAAA,CAAApL,CAAA;cAAA,OAEAiL,OAAA,CAAA9K,eAAA,CAAAC,QAAA;YAAA;cAAAiF,QAAA,GAAA+F,UAAA,CAAAlL,CAAA;cACA,IAAAmF,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAkS,OAAA,CAAAnQ,mBAAA,GAAAuK,QAAA,CAAAtM,IAAA;cACA;gBACAkS,OAAA,CAAAnQ,mBAAA;cACA;cAAAsQ,UAAA,CAAApL,CAAA;cAAA;YAAA;cAAAoL,UAAA,CAAAvH,CAAA;cAAAsH,GAAA,GAAAC,UAAA,CAAAlL,CAAA;cAEAwC,OAAA,CAAAC,KAAA,gBAAAwI,GAAA;cACAF,OAAA,CAAAnQ,mBAAA;YAAA;cAAA,OAAAsQ,UAAA,CAAAjO,CAAA;UAAA;QAAA,GAAA+N,SAAA;MAAA;IAGA;IAEAnN,2BAAA,WAAAA,4BAAA;MAAA,IAAAuN,OAAA;MAAA,WAAA5L,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA2L,UAAA;QAAA,IAAA9K,MAAA,EAAA4E,QAAA,EAAAmG,GAAA;QAAA,WAAA7L,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA2L,UAAA;UAAA,kBAAAA,UAAA,CAAAzL,CAAA;YAAA;cAAAyL,UAAA,CAAA5H,CAAA;cAEApD,MAAA;gBACAL,QAAA,EAAAiL,QAAA,CAAAC,OAAA,CAAA1Q,wBAAA;gBACA6E,aAAA,EAAA6L,OAAA,CAAAhS;cACA,GAEA;cACA,IAAAgS,OAAA,CAAAzQ,oBAAA,IAAAyQ,OAAA,CAAAzQ,oBAAA;gBACA4F,MAAA,CAAAyG,MAAA,GAAAoE,OAAA,CAAAzQ,oBAAA;cACA;cAEA6H,OAAA,CAAAuB,GAAA,wCAAAxD,MAAA;cAAAgL,UAAA,CAAAzL,CAAA;cAAA,OACAsL,OAAA,CAAA9K,eAAA,CAAAC,MAAA;YAAA;cAAA4E,QAAA,GAAAoG,UAAA,CAAAvL,CAAA;cACAwC,OAAA,CAAAuB,GAAA,wCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACAuS,OAAA,CAAAvQ,sBAAA,GAAAsK,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,yCAAAqH,OAAA,CAAAvQ,sBAAA;cACA;gBACA2H,OAAA,CAAAC,KAAA,sBAAA0C,QAAA;gBACAiG,OAAA,CAAAvQ,sBAAA,GAAAuQ,OAAA,CAAAI,6BAAA;cACA;cAAAD,UAAA,CAAAzL,CAAA;cAAA;YAAA;cAAAyL,UAAA,CAAA5H,CAAA;cAAA2H,GAAA,GAAAC,UAAA,CAAAvL,CAAA;cAEAwC,OAAA,CAAAC,KAAA,uBAAA6I,GAAA;cACAF,OAAA,CAAAvQ,sBAAA,GAAAuQ,OAAA,CAAAI,6BAAA;YAAA;cAGA;cACAJ,OAAA,CAAAhM,SAAA;gBACAgM,OAAA,CAAAK,2BAAA;cACA;YAAA;cAAA,OAAAF,UAAA,CAAAtO,CAAA;UAAA;QAAA,GAAAoO,SAAA;MAAA;IACA;IAEA;IACAG,6BAAA,WAAAA,8BAAA;MACA,QACA;QAAAlP,QAAA;QAAAoP,KAAA;QAAAC,UAAA;MAAA,GACA;QAAArP,QAAA;QAAAoP,KAAA;QAAAC,UAAA;MAAA,GACA;QAAArP,QAAA;QAAAoP,KAAA;QAAAC,UAAA;MAAA,GACA;QAAArP,QAAA;QAAAoP,KAAA;QAAAC,UAAA;MAAA,GACA;QAAArP,QAAA;QAAAoP,KAAA;QAAAC,UAAA;MAAA,GACA;QAAArP,QAAA;QAAAoP,KAAA;QAAAC,UAAA;MAAA,EACA;IACA;IAEA7N,8BAAA,WAAAA,+BAAA;MAAA,IAAA8N,OAAA;MAAA,WAAApM,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAmM,UAAA;QAAA,IAAAtL,MAAA,EAAA4E,QAAA,EAAA2G,GAAA;QAAA,WAAArM,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAmM,UAAA;UAAA,kBAAAA,UAAA,CAAAjM,CAAA;YAAA;cAAAiM,UAAA,CAAApI,CAAA;cAEApD,MAAA;gBACAhB,aAAA,EAAAqM,OAAA,CAAAxS,oBAAA;gBACA4S,QAAA,EAAAJ,OAAA,CAAA9Q,gBAAA;gBACAoF,QAAA,EAAA0L,OAAA,CAAA7Q;cACA;cAEAyH,OAAA,CAAAuB,GAAA,2CAAAxD,MAAA;cAAAwL,UAAA,CAAAjM,CAAA;cAAA,OACA8L,OAAA,CAAA7J,4BAAA,CAAAxB,MAAA;YAAA;cAAA4E,QAAA,GAAA4G,UAAA,CAAA/L,CAAA;cACAwC,OAAA,CAAAuB,GAAA,2CAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACA+S,OAAA,CAAA5Q,yBAAA,GAAAmK,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,4CAAA6H,OAAA,CAAA5Q,yBAAA;cACA;gBACAwH,OAAA,CAAAC,KAAA,sBAAA0C,QAAA;gBACAyG,OAAA,CAAA5Q,yBAAA,GAAA4Q,OAAA,CAAAK,wBAAA;cACA;cAAAF,UAAA,CAAAjM,CAAA;cAAA;YAAA;cAAAiM,UAAA,CAAApI,CAAA;cAAAmI,GAAA,GAAAC,UAAA,CAAA/L,CAAA;cAEAwC,OAAA,CAAAC,KAAA,uBAAAqJ,GAAA;cACAF,OAAA,CAAA5Q,yBAAA,GAAA4Q,OAAA,CAAAK,wBAAA;YAAA;cAGA;cACAL,OAAA,CAAAxM,SAAA;gBACAwM,OAAA,CAAAhH,iBAAA;cACA;YAAA;cAAA,OAAAmH,UAAA,CAAA9O,CAAA;UAAA;QAAA,GAAA4O,SAAA;MAAA;IACA;IAEA;IACAI,wBAAA,WAAAA,yBAAA;MACA,QACA;QAAA3P,QAAA;QAAAoP,KAAA;QAAAQ,KAAA;MAAA,GACA;QAAA5P,QAAA;QAAAoP,KAAA;QAAAQ,KAAA;MAAA,GACA;QAAA5P,QAAA;QAAAoP,KAAA;QAAAQ,KAAA;MAAA,GACA;QAAA5P,QAAA;QAAAoP,KAAA;QAAAQ,KAAA;MAAA,GACA;QAAA5P,QAAA;QAAAoP,KAAA;QAAAQ,KAAA;MAAA,GACA;QAAA5P,QAAA;QAAAoP,KAAA;QAAAQ,KAAA;MAAA,EACA;IACA;IAEAC,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5M,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA2M,UAAA;QAAA,WAAA5M,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA0M,UAAA;UAAA,kBAAAA,UAAA,CAAAxM,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,YAAAqI,OAAA,CAAAtR,gBAAA;cAAAwR,UAAA,CAAAxM,CAAA;cAAA,OACAsM,OAAA,CAAAtO,8BAAA;YAAA;cAAA,OAAAwO,UAAA,CAAArP,CAAA;UAAA;QAAA,GAAAoP,SAAA;MAAA;IACA;IAEAE,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAhN,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA+M,UAAA;QAAA,WAAAhN,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA8M,UAAA;UAAA,kBAAAA,UAAA,CAAA5M,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,UAAAyI,OAAA,CAAAzR,gBAAA;cAAA2R,UAAA,CAAA5M,CAAA;cAAA,OACA0M,OAAA,CAAA1O,8BAAA;YAAA;cAAA,OAAA4O,UAAA,CAAAzP,CAAA;UAAA;QAAA,GAAAwP,SAAA;MAAA;IACA;IAEA;IACA1O,qBAAA,WAAAA,sBAAA;MAAA,IAAA4O,OAAA;MAAA,WAAAnN,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAkN,UAAA;QAAA,IAAArM,MAAA,EAAA4E,QAAA,EAAA0H,GAAA;QAAA,WAAApN,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAkN,UAAA;UAAA,kBAAAA,UAAA,CAAAhN,CAAA;YAAA;cAAAgN,UAAA,CAAAnJ,CAAA;cAEApD,MAAA;gBACAwM,QAAA,EAAAJ,OAAA,CAAApK,0BAAA,CAAAoK,OAAA,CAAAvT,oBAAA;cACA;cAEAoJ,OAAA,CAAAuB,GAAA,kCAAAxD,MAAA;cAAAuM,UAAA,CAAAhN,CAAA;cAAA,OACA6M,OAAA,CAAAxK,mBAAA,CAAA5B,MAAA;YAAA;cAAA4E,QAAA,GAAA2H,UAAA,CAAA9M,CAAA;cACAwC,OAAA,CAAAuB,GAAA,kCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACA8T,OAAA,CAAA1R,gBAAA,GAAAkK,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,mCAAA4I,OAAA,CAAA1R,gBAAA;cACA;gBACAuH,OAAA,CAAAC,KAAA,gBAAA0C,QAAA;gBACAwH,OAAA,CAAA1R,gBAAA;cACA;cAAA6R,UAAA,CAAAhN,CAAA;cAAA;YAAA;cAAAgN,UAAA,CAAAnJ,CAAA;cAAAkJ,GAAA,GAAAC,UAAA,CAAA9M,CAAA;cAEAwC,OAAA,CAAAC,KAAA,iBAAAoK,GAAA;cACAF,OAAA,CAAA1R,gBAAA;YAAA;cAGA;cACA0R,OAAA,CAAAvN,SAAA;gBACAuN,OAAA,CAAAK,qBAAA;cACA;YAAA;cAAA,OAAAF,UAAA,CAAA7P,CAAA;UAAA;QAAA,GAAA2P,SAAA;MAAA;IACA;IAEA;IACAK,6BAAA,WAAAA,8BAAAC,aAAA;MAAA,IAAAC,OAAA;MAAA,WAAA3N,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA0N,UAAA;QAAA,IAAAC,kBAAA,EAAAC,OAAA,EAAAC,YAAA,EAAAC,UAAA,EAAAC,IAAA;QAAA,WAAAhO,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA8N,UAAA;UAAA,kBAAAA,UAAA,CAAA5N,CAAA;YAAA;cACAqN,OAAA,CAAAhS,iBAAA;cACAgS,OAAA,CAAAjS,gBAAA;cAAAwS,UAAA,CAAA/J,CAAA;cAGA;cACA0J,kBAAA,GAAAH,aAAA,CAAAS,GAAA;gBAAA,IAAAC,KAAA,OAAApO,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAmO,UAAA/D,YAAA;kBAAA,IAAAvJ,MAAA,EAAA4E,QAAA,EAAA2I,IAAA;kBAAA,WAAArO,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAmO,UAAA;oBAAA,kBAAAA,UAAA,CAAAjO,CAAA;sBAAA;wBAAAiO,UAAA,CAAApK,CAAA;wBAEApD,MAAA;0BACAuJ,YAAA,EAAAA,YAAA;0BACAb,YAAA;wBACA;wBAEAzG,OAAA,CAAAuB,GAAA,2BAAA1H,MAAA,CAAAyN,YAAA,iCAAAvJ,MAAA;wBAAAwN,UAAA,CAAAjO,CAAA;wBAAA,OACA,IAAAkO,yCAAA,EAAAzN,MAAA;sBAAA;wBAAA4E,QAAA,GAAA4I,UAAA,CAAA/N,CAAA;wBACAwC,OAAA,CAAAuB,GAAA,2BAAA1H,MAAA,CAAAyN,YAAA,iCAAA3E,QAAA;wBAAA,MAEAA,QAAA,IAAAA,QAAA,CAAA8I,IAAA,IAAA9I,QAAA,CAAA8I,IAAA,YAAA9I,QAAA,CAAAtM,IAAA;0BAAAkV,UAAA,CAAAjO,CAAA;0BAAA;wBAAA;wBAAA,OAAAiO,UAAA,CAAA9Q,CAAA,IACA;0BACA6M,YAAA,EAAAA,YAAA;0BACAoE,QAAA,EAAA/I,QAAA,CAAAtM,IAAA,CAAAqV,QAAA,mBAAA7R,MAAA,CAAAyN,YAAA;0BACAqE,UAAA,EAAAhJ,QAAA,CAAAtM,IAAA,CAAAuV,MAAA,IAAAjJ,QAAA,CAAAtM,IAAA,CAAAsV,UAAA,IAAAhJ,QAAA,CAAAkJ,GAAA;0BACAC,OAAA,EAAAnJ,QAAA,CAAAtM,IAAA,CAAAyV,OAAA;wBACA;sBAAA;wBAEA9L,OAAA,CAAAC,KAAA,gBAAApG,MAAA,CAAAyN,YAAA,uDAAA3E,QAAA;wBAAA,OAAA4I,UAAA,CAAA9Q,CAAA,IACA;0BACA6M,YAAA,EAAAA,YAAA;0BACAoE,QAAA,iBAAA7R,MAAA,CAAAyN,YAAA;0BACAqE,UAAA,iBAAA9R,MAAA,CAAAyN,YAAA;0BACAwE,OAAA;wBACA;sBAAA;wBAAAP,UAAA,CAAAjO,CAAA;wBAAA;sBAAA;wBAAAiO,UAAA,CAAApK,CAAA;wBAAAmK,IAAA,GAAAC,UAAA,CAAA/N,CAAA;wBAGAwC,OAAA,CAAAC,KAAA,gBAAApG,MAAA,CAAAyN,YAAA,wDAAAgE,IAAA;wBAAA,OAAAC,UAAA,CAAA9Q,CAAA,IACA;0BACA6M,YAAA,EAAAA,YAAA;0BACAoE,QAAA,iBAAA7R,MAAA,CAAAyN,YAAA;0BACAqE,UAAA,iBAAA9R,MAAA,CAAAyN,YAAA,gDAAAzN,MAAA,CAAAyR,IAAA,CAAA5J,OAAA;0BACAoK,OAAA;wBACA;sBAAA;wBAAA,OAAAP,UAAA,CAAA9Q,CAAA;oBAAA;kBAAA,GAAA4Q,SAAA;gBAAA,CAEA;gBAAA,iBAAAU,EAAA;kBAAA,OAAAX,KAAA,CAAAY,KAAA,OAAAzL,SAAA;gBAAA;cAAA,MAEA;cAAA2K,UAAA,CAAA5N,CAAA;cAAA,OACA8D,OAAA,CAAAC,GAAA,CAAAwJ,kBAAA;YAAA;cAAAC,OAAA,GAAAI,UAAA,CAAA1N,CAAA;cACAmN,OAAA,CAAAjS,gBAAA,GAAAoS,OAAA;cACA9K,OAAA,CAAAuB,GAAA,6CAAAoJ,OAAA,CAAAjS,gBAAA;cAEAqS,YAAA,GAAAD,OAAA,CAAAmB,MAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAJ,OAAA;cAAA,GAAA7K,MAAA;cACA+J,UAAA,GAAAF,OAAA,CAAA7J,MAAA;cAEA,IAAA8J,YAAA;gBACAJ,OAAA,CAAAhF,QAAA,CAAAmG,OAAA,4BAAAjS,MAAA,CAAAkR,YAAA,OAAAlR,MAAA,CAAAmR,UAAA;cACA;gBACAL,OAAA,CAAAhF,QAAA,CAAA1F,KAAA;cACA;cAAAiL,UAAA,CAAA5N,CAAA;cAAA;YAAA;cAAA4N,UAAA,CAAA/J,CAAA;cAAA8J,IAAA,GAAAC,UAAA,CAAA1N,CAAA;cAEAwC,OAAA,CAAAC,KAAA,kBAAAgL,IAAA;cACAN,OAAA,CAAAhF,QAAA,CAAA1F,KAAA,iBAAAgL,IAAA,CAAAvJ,OAAA;YAAA;cAAAwJ,UAAA,CAAA/J,CAAA;cAEAwJ,OAAA,CAAAhS,iBAAA;cAAA,OAAAuS,UAAA,CAAA3G,CAAA;YAAA;cAAA,OAAA2G,UAAA,CAAAzQ,CAAA;UAAA;QAAA,GAAAmQ,SAAA;MAAA;IAEA;IAEA;IACAuB,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,OAAA;MAAA,WAAApP,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAmP,UAAA;QAAA,IAAAtO,MAAA,EAAA4E,QAAA,EAAA2J,UAAA,EAAAC,IAAA;QAAA,WAAAtP,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAoP,UAAA;UAAA,kBAAAA,UAAA,CAAAlP,CAAA;YAAA;cAAAkP,UAAA,CAAArL,CAAA;cAEApD,MAAA;gBACAhE,QAAA,EAAA4O,QAAA,CAAAyD,OAAA,CAAAlU,wBAAA;cACA;cAAAsU,UAAA,CAAAlP,CAAA;cAAA,OAEA,IAAAmP,sCAAA,EAAA1O,MAAA;YAAA;cAAA4E,QAAA,GAAA6J,UAAA,CAAAhP,CAAA;cACAwC,OAAA,CAAAuB,GAAA,kCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACA+V,OAAA,CAAAxT,mBAAA,GAAA+J,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,mCAAA6K,OAAA,CAAAxT,mBAAA;;gBAEA;gBACA0T,UAAA,GAAAF,OAAA,CAAAxT,mBAAA,CAAA8T,IAAA,WAAA/S,IAAA;kBAAA,OAAAA,IAAA,CAAAG,QAAA;gBAAA;gBACA,IAAAwS,UAAA;kBACAF,OAAA,CAAAvT,gBAAA;gBACA,WAAAuT,OAAA,CAAAxT,mBAAA,CAAAqI,MAAA;kBACA;kBACAmL,OAAA,CAAAvT,gBAAA,GAAAuT,OAAA,CAAAxT,mBAAA,IAAAkB,QAAA;gBACA;;gBAEA;gBACAsS,OAAA,CAAAO,sBAAA;cACA;gBACA3M,OAAA,CAAAC,KAAA,eAAA0C,QAAA;gBACAyJ,OAAA,CAAAxT,mBAAA;cACA;cAAA4T,UAAA,CAAAlP,CAAA;cAAA;YAAA;cAAAkP,UAAA,CAAArL,CAAA;cAAAoL,IAAA,GAAAC,UAAA,CAAAhP,CAAA;cAEAwC,OAAA,CAAAC,KAAA,gBAAAsM,IAAA;cACAH,OAAA,CAAAxT,mBAAA;YAAA;cAAA,OAAA4T,UAAA,CAAA/R,CAAA;UAAA;QAAA,GAAA4R,SAAA;MAAA;IAEA;IAEA;IACAM,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5P,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA2P,UAAA;QAAA,IAAA9O,MAAA,EAAA4E,QAAA,EAAAmK,IAAA;QAAA,WAAA7P,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA2P,UAAA;UAAA,kBAAAA,UAAA,CAAAzP,CAAA;YAAA;cAAAyP,UAAA,CAAA5L,CAAA;cAEApD,MAAA;gBACAhB,aAAA,EAAA6P,OAAA,CAAAhW,oBAAA;gBACAkD,QAAA,EAAA8S,OAAA,CAAA/T;cACA;cAEAmH,OAAA,CAAAuB,GAAA,mCAAAxD,MAAA;cAAAgP,UAAA,CAAAzP,CAAA;cAAA,OACA,IAAA0P,2CAAA,EAAAjP,MAAA;YAAA;cAAA4E,QAAA,GAAAoK,UAAA,CAAAvP,CAAA;cACAwC,OAAA,CAAAuB,GAAA,mCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA,KAAAsM,QAAA,CAAAtM,IAAA,CAAA4K,MAAA;gBACA2L,OAAA,CAAAK,iBAAA,GAAAtK,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,oCAAAqL,OAAA,CAAAK,iBAAA;cACA;gBACAjN,OAAA,CAAAC,KAAA,iBAAA0C,QAAA;gBACAiK,OAAA,CAAAK,iBAAA;cACA;cAAAF,UAAA,CAAAzP,CAAA;cAAA;YAAA;cAAAyP,UAAA,CAAA5L,CAAA;cAAA2L,IAAA,GAAAC,UAAA,CAAAvP,CAAA;cAEAwC,OAAA,CAAAC,KAAA,kBAAA6M,IAAA;cACAF,OAAA,CAAAK,iBAAA;YAAA;cAGA;cACAL,OAAA,CAAAhQ,SAAA;gBACAgQ,OAAA,CAAAM,mBAAA;cACA;YAAA;cAAA,OAAAH,UAAA,CAAAtS,CAAA;UAAA;QAAA,GAAAoS,SAAA;MAAA;IACA;IAEA;IACAM,gCAAA,WAAAA,iCAAA;MAAA,IAAAC,OAAA;MAAA,WAAApQ,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAmQ,UAAA;QAAA,WAAApQ,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAkQ,UAAA;UAAA,kBAAAA,UAAA,CAAAhQ,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,YAAA6L,OAAA,CAAAlV,wBAAA;cACA;cAAAoV,UAAA,CAAAhQ,CAAA;cAAA,OACA8P,OAAA,CAAAjB,qBAAA;YAAA;cAAA,OAAAmB,UAAA,CAAA7S,CAAA;UAAA;QAAA,GAAA4S,SAAA;MAAA;IACA;IAEA;IACAE,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAxQ,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAuQ,UAAA;QAAA,WAAAxQ,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAsQ,UAAA;UAAA,kBAAAA,UAAA,CAAApQ,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,YAAAiM,OAAA,CAAA3U,gBAAA;cAAA6U,UAAA,CAAApQ,CAAA;cAAA,OACAkQ,OAAA,CAAAb,sBAAA;YAAA;cAAA,OAAAe,UAAA,CAAAjT,CAAA;UAAA;QAAA,GAAAgT,SAAA;MAAA;IAEA;IAEAE,+BAAA,WAAAA,gCAAA;MACA,IAAAC,KAAA;MACA,SAAA7V,qBAAA,SAAAA,qBAAA,CAAAkJ,MAAA;QACA,KAAAlJ,qBAAA,CAAA2B,OAAA,WAAAC,IAAA;UACAiU,KAAA,IAAAC,UAAA,CAAAlU,IAAA,CAAA8N,oBAAA;QACA;MACA;MACA,OAAAmG,KAAA,CAAAE,OAAA;IACA;IAEAC,wBAAA,WAAAA,yBAAA;MACA,IAAAH,KAAA;MACA,SAAA5V,uBAAA,SAAAA,uBAAA,CAAAiJ,MAAA;QACA;QACA,IAAA+M,UAAA;QACA,KAAAhW,uBAAA,CAAA0B,OAAA,WAAAC,IAAA;UACA,IAAAA,IAAA,CAAAsU,6BAAA,IAAAtU,IAAA,CAAAsU,6BAAA,CAAAhN,MAAA;YACAtH,IAAA,CAAAsU,6BAAA,CAAAvU,OAAA,WAAAwU,MAAA;cACA,IAAAA,MAAA,CAAAC,WAAA,GAAAH,UAAA;gBACAA,UAAA,GAAAE,MAAA,CAAAC,WAAA;cACA;YACA;UACA;QACA;;QAEA;QACA,KAAAnW,uBAAA,CAAA0B,OAAA,WAAAC,IAAA;UACA,IAAAA,IAAA,CAAAsU,6BAAA,IAAAtU,IAAA,CAAAsU,6BAAA,CAAAhN,MAAA;YACA,IAAAmN,YAAA,GAAAzU,IAAA,CAAAsU,6BAAA,CAAAvB,IAAA,WAAAwB,MAAA;cAAA,OAAAA,MAAA,CAAAC,WAAA,KAAAH,UAAA;YAAA;YACA,IAAAI,YAAA;cACAR,KAAA,IAAAC,UAAA,CAAAO,YAAA,CAAAC,MAAA;YACA;UACA;QACA;MACA;MACA,QAAAT,KAAA,UAAAE,OAAA;IACA;IAEA;IACAQ,0BAAA,WAAAA,2BAAA;MAAA,IAAAC,OAAA;MAAA,WAAAvR,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAsR,UAAA;QAAA,WAAAvR,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAqR,UAAA;UAAA,kBAAAA,UAAA,CAAAnR,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,aAAAgN,OAAA,CAAAtW,sBAAA;cACA;cACAsW,OAAA,CAAA3R,SAAA;gBACA2R,OAAA,CAAAzG,4BAAA;cACA;YAAA;cAAA,OAAA2G,UAAA,CAAAhU,CAAA;UAAA;QAAA,GAAA+T,SAAA;MAAA;IACA;IAEA;IACA;IACA9S,sBAAA,WAAAA,uBAAA;MAAA,IAAAgT,OAAA;MAAA,WAAA1R,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAyR,UAAA;QAAA,IAAAhM,QAAA,EAAAiM,IAAA;QAAA,WAAA3R,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAyR,UAAA;UAAA,kBAAAA,UAAA,CAAAvR,CAAA;YAAA;cAAAuR,UAAA,CAAA1N,CAAA;cAAA0N,UAAA,CAAAvR,CAAA;cAAA,OAEA,IAAAwR,qCAAA;YAAA;cAAAnM,QAAA,GAAAkM,UAAA,CAAArR,CAAA;cACAwC,OAAA,CAAAuB,GAAA,mCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAqY,OAAA,CAAAK,iBAAA,GAAApM,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,oCAAAmN,OAAA,CAAAK,iBAAA;cACA;gBACA/O,OAAA,CAAAC,KAAA,eAAA0C,QAAA;gBACA+L,OAAA,CAAAK,iBAAA;cACA;cAAAF,UAAA,CAAAvR,CAAA;cAAA;YAAA;cAAAuR,UAAA,CAAA1N,CAAA;cAAAyN,IAAA,GAAAC,UAAA,CAAArR,CAAA;cAEAwC,OAAA,CAAAC,KAAA,gBAAA2O,IAAA;cACAF,OAAA,CAAAK,iBAAA;YAAA;cAGA;cACAL,OAAA,CAAAM,qBAAA;YAAA;cAAA,OAAAH,UAAA,CAAApU,CAAA;UAAA;QAAA,GAAAkU,SAAA;MAAA;IACA;IAEA;IACAM,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAlS,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAiS,UAAA;QAAA,WAAAlS,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAgS,UAAA;UAAA,kBAAAA,UAAA,CAAA9R,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,YAAA2N,OAAA,CAAAG,kBAAA;cAAAD,UAAA,CAAA9R,CAAA;cAAA,OACA4R,OAAA,CAAAF,qBAAA;YAAA;cAAA,OAAAI,UAAA,CAAA3U,CAAA;UAAA;QAAA,GAAA0U,SAAA;MAAA;IACA;IAEA;IACAG,+BAAA,WAAAA,gCAAA;MAAA,IAAAC,OAAA;MAAA,WAAAvS,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAsS,UAAA;QAAA,WAAAvS,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAqS,UAAA;UAAA,kBAAAA,UAAA,CAAAnS,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,cAAAgO,OAAA,CAAAG,2BAAA;cACA;cACAH,OAAA,CAAA3S,SAAA;gBACA2S,OAAA,CAAAI,qBAAA;cACA;YAAA;cAAA,OAAAF,UAAA,CAAAhV,CAAA;UAAA;QAAA,GAAA+U,SAAA;MAAA;IACA;IAEA;IACAR,qBAAA,WAAAA,sBAAA;MAAA,IAAAY,OAAA;MAAA,WAAA5S,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA2S,UAAA;QAAA,IAAAC,OAAA,EAAAnN,QAAA,EAAAoN,IAAA;QAAA,WAAA9S,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA4S,UAAA;UAAA,kBAAAA,UAAA,CAAA1S,CAAA;YAAA;cAAA0S,UAAA,CAAA7O,CAAA;cAEA2O,OAAA,GAAAF,OAAA,CAAAP,kBAAA;cACArP,OAAA,CAAAuB,GAAA,kCAAAuO,OAAA;cAAAE,UAAA,CAAA1S,CAAA;cAAA,OAEA,IAAA2S,qCAAA,EAAAH,OAAA;YAAA;cAAAnN,QAAA,GAAAqN,UAAA,CAAAxS,CAAA;cACAwC,OAAA,CAAAuB,GAAA,kCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAuZ,OAAA,CAAAM,gBAAA,GAAAvN,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,mCAAAqO,OAAA,CAAAM,gBAAA;cACA;gBACAlQ,OAAA,CAAAC,KAAA,eAAA0C,QAAA;gBACAiN,OAAA,CAAAM,gBAAA;cACA;cAAAF,UAAA,CAAA1S,CAAA;cAAA;YAAA;cAAA0S,UAAA,CAAA7O,CAAA;cAAA4O,IAAA,GAAAC,UAAA,CAAAxS,CAAA;cAEAwC,OAAA,CAAAC,KAAA,gBAAA8P,IAAA;cACAH,OAAA,CAAAM,gBAAA;YAAA;cAGA;cACAN,OAAA,CAAAhT,SAAA;gBACAgT,OAAA,CAAAD,qBAAA;cACA;YAAA;cAAA,OAAAK,UAAA,CAAAvV,CAAA;UAAA;QAAA,GAAAoV,SAAA;MAAA;IACA;IAEA;IACAM,oCAAA,WAAAA,qCAAA;MAAA,IAAAC,OAAA;MAAA,WAAApT,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAmT,UAAA;QAAA,WAAApT,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAkT,UAAA;UAAA,kBAAAA,UAAA,CAAAhT,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,iBAAA6O,OAAA,CAAAG,wBAAA;cACAH,OAAA,CAAAI,+BAAA;cAAAF,UAAA,CAAAhT,CAAA;cAAA,OACA8S,OAAA,CAAA5U,+BAAA;YAAA;cAAA,OAAA8U,UAAA,CAAA7V,CAAA;UAAA;QAAA,GAAA4V,SAAA;MAAA;IACA;IAEA;IACAI,iCAAA,WAAAA,kCAAA;MAAA,IAAAC,OAAA;MAAA,WAAA1T,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAyT,UAAA;QAAA,WAAA1T,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAwT,UAAA;UAAA,kBAAAA,UAAA,CAAAtT,CAAA;YAAA;cACA0C,OAAA,CAAAuB,GAAA,iBAAAmP,OAAA,CAAAG,qBAAA;cACAH,OAAA,CAAAI,4BAAA;cAAAF,UAAA,CAAAtT,CAAA;cAAA,OACAoT,OAAA,CAAAjV,4BAAA;YAAA;cAAA,OAAAmV,UAAA,CAAAnW,CAAA;UAAA;QAAA,GAAAkW,SAAA;MAAA;IACA;IAEA;IACAnV,+BAAA,WAAAA,gCAAA;MAAA,IAAAuV,OAAA;MAAA,WAAA/T,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA8T,UAAA;QAAA,IAAAjT,MAAA,EAAA4E,QAAA,EAAA2J,UAAA,EAAA2E,IAAA;QAAA,WAAAhU,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA8T,UAAA;UAAA,kBAAAA,UAAA,CAAA5T,CAAA;YAAA;cAAA4T,UAAA,CAAA/P,CAAA;cAEApD,MAAA;gBACAoT,UAAA,EAAAJ,OAAA,CAAAR,wBAAA;gBACAa,SAAA;gBAAA;gBACArU,aAAA,EAAAgU,OAAA,CAAAna;cACA;cAEAoJ,OAAA,CAAAuB,GAAA,4CAAAxD,MAAA;cAAAmT,UAAA,CAAA5T,CAAA;cAAA,OACA,IAAA+T,mDAAA,EAAAtT,MAAA;YAAA;cAAA4E,QAAA,GAAAuO,UAAA,CAAA1T,CAAA;cACAwC,OAAA,CAAAuB,GAAA,4CAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACA0a,OAAA,CAAAO,6BAAA,GAAA3O,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,6CAAAwP,OAAA,CAAAO,6BAAA;;gBAEA;gBACA,IAAAP,OAAA,CAAAP,+BAAA,CAAAvP,MAAA,WAAA8P,OAAA,CAAAQ,wBAAA;kBACAjF,UAAA,GAAAyE,OAAA,CAAAO,6BAAA,CAAA5E,IAAA,WAAA/S,IAAA;oBAAA,OAAAA,IAAA,CAAAG,QAAA;kBAAA;kBACA,IAAAwS,UAAA;oBACAyE,OAAA,CAAAP,+BAAA;oBACAxQ,OAAA,CAAAuB,GAAA;;oBAEA;oBACAwP,OAAA,CAAAS,+BAAA;kBACA;gBACA;cACA;gBACAxR,OAAA,CAAAC,KAAA,kBAAA0C,QAAA;gBACAoO,OAAA,CAAAO,6BAAA;cACA;cAAAJ,UAAA,CAAA5T,CAAA;cAAA;YAAA;cAAA4T,UAAA,CAAA/P,CAAA;cAAA8P,IAAA,GAAAC,UAAA,CAAA1T,CAAA;cAEAwC,OAAA,CAAAC,KAAA,mBAAAgR,IAAA;cACAF,OAAA,CAAAO,6BAAA;YAAA;cAAA,OAAAJ,UAAA,CAAAzW,CAAA;UAAA;QAAA,GAAAuW,SAAA;MAAA;IAEA;IAEA;IACAvV,4BAAA,WAAAA,6BAAA;MAAA,IAAAgW,OAAA;MAAA,WAAAzU,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAwU,UAAA;QAAA,IAAA3T,MAAA,EAAA4E,QAAA,EAAA2J,UAAA,EAAAqF,IAAA;QAAA,WAAA1U,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAwU,UAAA;UAAA,kBAAAA,UAAA,CAAAtU,CAAA;YAAA;cAAAsU,UAAA,CAAAzQ,CAAA;cAEApD,MAAA;gBACAoT,UAAA,EAAAM,OAAA,CAAAZ,qBAAA;gBACAO,SAAA;gBAAA;gBACArU,aAAA,EAAA0U,OAAA,CAAA7a;cACA;cAEAoJ,OAAA,CAAAuB,GAAA,yCAAAxD,MAAA;cAAA6T,UAAA,CAAAtU,CAAA;cAAA,OACA,IAAA+T,mDAAA,EAAAtT,MAAA;YAAA;cAAA4E,QAAA,GAAAiP,UAAA,CAAApU,CAAA;cACAwC,OAAA,CAAAuB,GAAA,yCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAob,OAAA,CAAAI,0BAAA,GAAAlP,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,0CAAAkQ,OAAA,CAAAI,0BAAA;;gBAEA;gBACA,IAAAJ,OAAA,CAAAX,4BAAA,CAAA7P,MAAA,WAAAwQ,OAAA,CAAAF,wBAAA;kBACAjF,UAAA,GAAAmF,OAAA,CAAAI,0BAAA,CAAAnF,IAAA,WAAA/S,IAAA;oBAAA,OAAAA,IAAA,CAAAG,QAAA;kBAAA;kBACA,IAAAwS,UAAA;oBACAmF,OAAA,CAAAX,4BAAA;oBACA9Q,OAAA,CAAAuB,GAAA;;oBAEA;oBACAkQ,OAAA,CAAAD,+BAAA;kBACA;gBACA;cACA;gBACAxR,OAAA,CAAAC,KAAA,kBAAA0C,QAAA;gBACA8O,OAAA,CAAAI,0BAAA;cACA;cAAAD,UAAA,CAAAtU,CAAA;cAAA;YAAA;cAAAsU,UAAA,CAAAzQ,CAAA;cAAAwQ,IAAA,GAAAC,UAAA,CAAApU,CAAA;cAEAwC,OAAA,CAAAC,KAAA,mBAAA0R,IAAA;cACAF,OAAA,CAAAI,0BAAA;YAAA;cAAA,OAAAD,UAAA,CAAAnX,CAAA;UAAA;QAAA,GAAAiX,SAAA;MAAA;IAEA;IAIA;IACAI,iCAAA,WAAAA,kCAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/U,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA8U,UAAA;QAAA,IAAAC,QAAA,EAAAlU,MAAA,EAAA4E,QAAA,EAAAuP,oBAAA,EAAAC,IAAA;QAAA,WAAAlV,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAgV,UAAA;UAAA,kBAAAA,UAAA,CAAA9U,CAAA;YAAA;cAAA,MACAyU,OAAA,CAAAvB,+BAAA,CAAAvP,MAAA,UAAA8Q,OAAA,CAAAjB,4BAAA,CAAA7P,MAAA;gBAAAmR,UAAA,CAAA9U,CAAA;gBAAA;cAAA;cACAyU,OAAA,CAAApM,QAAA,CAAA0M,OAAA;cAAA,OAAAD,UAAA,CAAA3X,CAAA;YAAA;cAIAsX,OAAA,CAAAO,iBAAA;cAAAF,UAAA,CAAAjR,CAAA;cAEA;cACA8Q,QAAA,OAEA;cACAF,OAAA,CAAAvB,+BAAA,CAAA9W,OAAA,WAAAI,QAAA;gBACAmY,QAAA,CAAAra,IAAA;kBACAwZ,SAAA;kBAAA;kBACAtX,QAAA,EAAAA;gBACA;cACA;;cAEA;cACAiY,OAAA,CAAAjB,4BAAA,CAAApX,OAAA,WAAAI,QAAA;gBACAmY,QAAA,CAAAra,IAAA;kBACAwZ,SAAA;kBAAA;kBACAtX,QAAA,EAAAA;gBACA;cACA;cAEAiE,MAAA;gBACAhB,aAAA,EAAAgV,OAAA,CAAAnb,oBAAA;gBACAqb,QAAA,EAAAA;cACA;cAEAjS,OAAA,CAAAuB,GAAA,mCAAAxD,MAAA;cAAAqU,UAAA,CAAA9U,CAAA;cAAA,OACA,IAAAiV,wDAAA,EAAAxU,MAAA;YAAA;cAAA4E,QAAA,GAAAyP,UAAA,CAAA5U,CAAA;cACAwC,OAAA,CAAAuB,GAAA,mCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA;gBACA0b,OAAA,CAAAS,oBAAA,GAAA7P,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,oCAAAwQ,OAAA,CAAAS,oBAAA;;gBAEA;gBACAT,OAAA,CAAAnV,SAAA;kBACAmV,OAAA,CAAAU,sBAAA;gBACA;;gBAEA;gBACAP,oBAAA,OAAAQ,mBAAA,CAAA3Z,OAAA,MAAA4Z,GAAA,IAAA9Y,MAAA,KAAA6Y,mBAAA,CAAA3Z,OAAA,EACAgZ,OAAA,CAAAvB,+BAAA,OAAAkC,mBAAA,CAAA3Z,OAAA,EACAgZ,OAAA,CAAAjB,4BAAA,EACA,IAEA;gBACA,IAAAoB,oBAAA,CAAAjR,MAAA;kBACA8Q,OAAA,CAAAtH,6BAAA,CAAAyH,oBAAA;gBACA;;gBAEA;gBACA,IAAAH,OAAA,CAAAjB,4BAAA,CAAA7P,MAAA;kBACA8Q,OAAA,CAAAa,qBAAA,CAAAb,OAAA,CAAAjB,4BAAA;gBACA;kBACA;kBACAiB,OAAA,CAAAtY,oBAAA;gBACA;gBAEAsY,OAAA,CAAApM,QAAA,CAAAmG,OAAA;cACA;gBACA9L,OAAA,CAAAC,KAAA,iBAAA0C,QAAA;gBACAoP,OAAA,CAAApM,QAAA,CAAA1F,KAAA;cACA;cAAAmS,UAAA,CAAA9U,CAAA;cAAA;YAAA;cAAA8U,UAAA,CAAAjR,CAAA;cAAAgR,IAAA,GAAAC,UAAA,CAAA5U,CAAA;cAEAwC,OAAA,CAAAC,KAAA,kBAAAkS,IAAA;cACAJ,OAAA,CAAApM,QAAA,CAAA1F,KAAA,aAAAkS,IAAA,CAAAzQ,OAAA;YAAA;cAAA0Q,UAAA,CAAAjR,CAAA;cAEA4Q,OAAA,CAAAO,iBAAA;cAAA,OAAAF,UAAA,CAAA7N,CAAA;YAAA;cAAA,OAAA6N,UAAA,CAAA3X,CAAA;UAAA;QAAA,GAAAuX,SAAA;MAAA;IAEA;IAEA;IACAY,qBAAA,WAAAA,sBAAAC,SAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9V,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAA6V,UAAA;QAAA,IAAAhV,MAAA,EAAA4E,QAAA,EAAAqQ,IAAA;QAAA,WAAA/V,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAA6V,UAAA;UAAA,kBAAAA,UAAA,CAAA3V,CAAA;YAAA;cACAwV,OAAA,CAAAI,uBAAA;cAAAD,UAAA,CAAA9R,CAAA;cAEApD,MAAA;gBACA8U,SAAA,EAAAA;cACA;cAEA7S,OAAA,CAAAuB,GAAA,kCAAAxD,MAAA;cAAAkV,UAAA,CAAA3V,CAAA;cAAA,OACA,IAAA6V,+BAAA,EAAApV,MAAA;YAAA;cAAA4E,QAAA,GAAAsQ,UAAA,CAAAzV,CAAA;cACAwC,OAAA,CAAAuB,GAAA,kCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACAyc,OAAA,CAAArZ,oBAAA,GAAAkJ,QAAA,CAAAtM,IAAA;gBACA2J,OAAA,CAAAuB,GAAA,mCAAAuR,OAAA,CAAArZ,oBAAA;cACA;gBACAuG,OAAA,CAAAC,KAAA,eAAA0C,QAAA;gBACAmQ,OAAA,CAAArZ,oBAAA;cACA;cAAAwZ,UAAA,CAAA3V,CAAA;cAAA;YAAA;cAAA2V,UAAA,CAAA9R,CAAA;cAAA6R,IAAA,GAAAC,UAAA,CAAAzV,CAAA;cAEAwC,OAAA,CAAAC,KAAA,gBAAA+S,IAAA;cACAF,OAAA,CAAArZ,oBAAA;YAAA;cAAAwZ,UAAA,CAAA9R,CAAA;cAEA2R,OAAA,CAAAI,uBAAA;cAAA,OAAAD,UAAA,CAAA1O,CAAA;YAAA;cAAA,OAAA0O,UAAA,CAAAxY,CAAA;UAAA;QAAA,GAAAsY,SAAA;MAAA;IAEA;IAEA;IACAK,YAAA,WAAAA,aAAAzY,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA;IACA;IAEA;IACAT,eAAA,WAAAA,gBAAAH,QAAA;MACA,IAAAsZ,WAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,WAAA,CAAAtZ,QAAA;IACA;IAEA;IACAI,gBAAA,WAAAA,iBAAAH,SAAA;MACA,IAAAsZ,YAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,YAAA,CAAAtZ,SAAA;IACA;IAEA;IACAuZ,oBAAA,WAAAA,qBAAA5Z,IAAA;MAAA,IAAA6Z,OAAA;MACAxT,OAAA,CAAAuB,GAAA,sCAAA5H,IAAA;MACA,KAAA8Z,iBAAA,OAAAC,cAAA,CAAA3a,OAAA,MAAAY,IAAA;MACAqG,OAAA,CAAAuB,GAAA,sDAAAkS,iBAAA;MACA,KAAAE,uBAAA;;MAEA;MACA,KAAA/W,SAAA;QACA4W,OAAA,CAAAI,mBAAA;MACA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAA;MACA,KAAAF,uBAAA;MACA,KAAAF,iBAAA;MACA,KAAAK,mBAAA;;MAEA;MACA,SAAAC,uBAAA;QACA;UACA,KAAAA,uBAAA,CAAAC,OAAA;UACA,KAAAD,uBAAA;QACA,SAAA5R,GAAA;UACAnC,OAAA,CAAAC,KAAA,gBAAAkC,GAAA;QACA;MACA;IACA;IAEA;IACAyR,mBAAA,WAAAA,oBAAA;MAAA,IAAAK,OAAA;MAAA,WAAAjX,kBAAA,CAAAjE,OAAA,mBAAAkE,aAAA,CAAAlE,OAAA,IAAAmE,CAAA,UAAAgX,UAAA;QAAA,IAAAjC,QAAA,EAAAlU,MAAA,EAAA4E,QAAA,EAAAwR,YAAA,EAAAC,iBAAA,EAAAC,oBAAA,EAAAC,IAAA;QAAA,WAAArX,aAAA,CAAAlE,OAAA,IAAAqE,CAAA,WAAAmX,UAAA;UAAA,kBAAAA,UAAA,CAAAjX,CAAA;YAAA;cACA2W,OAAA,CAAAO,sBAAA;cAAAD,UAAA,CAAApT,CAAA;cAEA;cACA8Q,QAAA,IACA;gBACAb,SAAA;gBAAA;gBACAtX,QAAA,EAAAma,OAAA,CAAAR,iBAAA,CAAA3Z;cACA,GACA;gBACAsX,SAAA;gBAAA;gBACAtX,QAAA,EAAAma,OAAA,CAAAR,iBAAA,CAAAgB;cACA,EACA;cAEA1W,MAAA;gBACAhB,aAAA,EAAAkX,OAAA,CAAArd,oBAAA;gBACAqb,QAAA,EAAAA;cACA;cAEAjS,OAAA,CAAAuB,GAAA,gCAAAxD,MAAA;cAAAwW,UAAA,CAAAjX,CAAA;cAAA,OACA,IAAAiV,wDAAA,EAAAxU,MAAA;YAAA;cAAA4E,QAAA,GAAA4R,UAAA,CAAA/W,CAAA;cACAwC,OAAA,CAAAuB,GAAA,gCAAAoB,QAAA;cAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAtM,IAAA,IAAAyM,KAAA,CAAAC,OAAA,CAAAJ,QAAA,CAAAtM,IAAA;gBACA;gBACA8d,YAAA,OAEA;gBACAC,iBAAA,GAAAH,OAAA,CAAA9Z,gBAAA,CAAA8Z,OAAA,CAAAR,iBAAA,CAAAzZ,SAAA;gBACAqa,oBAAA,GAAAJ,OAAA,CAAA9Z,gBAAA,CAAA8Z,OAAA,CAAAR,iBAAA,CAAAiB,gBAAA;gBAEA1U,OAAA,CAAAuB,GAAA;kBACAoT,YAAA,EAAAV,OAAA,CAAAR,iBAAA,CAAA3Z,QAAA;kBACAsa,iBAAA,EAAAA,iBAAA;kBACAK,eAAA,EAAAR,OAAA,CAAAR,iBAAA,CAAAgB,eAAA;kBACAJ,oBAAA,EAAAA;gBACA;gBAEA1R,QAAA,CAAAtM,IAAA,CAAAqD,OAAA,WAAAkb,YAAA;kBACA,IAAAC,oBAAA,OAAAnB,cAAA,CAAA3a,OAAA,MAAA6b,YAAA;kBAEA,IAAAC,oBAAA,CAAAC,sBAAA;oBACA;oBACAD,oBAAA,CAAAC,sBAAA,GAAAD,oBAAA,CAAAC,sBAAA,CAAA7I,MAAA,WAAA8I,UAAA;sBACA,IAAAC,OAAA;sBACA;sBACA,IAAAJ,YAAA,CAAA9a,QAAA,KAAAma,OAAA,CAAAR,iBAAA,CAAA3Z,QAAA;wBACAkb,OAAA,GAAAD,UAAA,CAAAE,SAAA,KAAAb,iBAAA;wBACApU,OAAA,CAAAuB,GAAA,6BAAA1H,MAAA,CAAA+a,YAAA,CAAA9a,QAAA,iCAAAD,MAAA,CAAAkb,UAAA,CAAAE,SAAA,iCAAApb,MAAA,CAAAua,iBAAA,qBAAAva,MAAA,CAAAmb,OAAA;sBACA;sBAEA,IAAAA,OAAA;wBACA,OAAAA,OAAA;sBACA;wBACA,IAAAJ,YAAA,CAAA9a,QAAA,KAAAma,OAAA,CAAAR,iBAAA,CAAAgB,eAAA;0BACA,IAAAO,QAAA,GAAAD,UAAA,CAAAE,SAAA,KAAAZ,oBAAA;0BACArU,OAAA,CAAAuB,GAAA,6BAAA1H,MAAA,CAAA+a,YAAA,CAAA9a,QAAA,iCAAAD,MAAA,CAAAkb,UAAA,CAAAE,SAAA,iCAAApb,MAAA,CAAAwa,oBAAA,qBAAAxa,MAAA,CAAAmb,QAAA;0BACA,OAAAA,QAAA;wBACA;sBACA;sBAGA;oBACA;oBAEAhV,OAAA,CAAAuB,GAAA;oBACAvB,OAAA,CAAAuB,GAAA,CAAAsT,oBAAA,CAAAC,sBAAA;;oBAEA;oBACA,IAAAD,oBAAA,CAAAC,sBAAA,CAAA7T,MAAA;sBACAkT,YAAA,CAAAvc,IAAA,CAAAid,oBAAA;sBACA7U,OAAA,CAAAuB,GAAA,6BAAA1H,MAAA,CAAA+a,YAAA,CAAA9a,QAAA,yBAAAD,MAAA,CAAAgb,oBAAA,CAAAC,sBAAA,CAAA7T,MAAA;oBACA;kBACA;gBACA;gBAEAgT,OAAA,CAAAH,mBAAA,GAAAK,YAAA;gBACAnU,OAAA,CAAAuB,GAAA,kCAAA0S,OAAA,CAAAH,mBAAA;gBACA9T,OAAA,CAAAuB,GAAA;kBACA2T,cAAA,EAAAf,YAAA,CAAAlT,MAAA;kBACAkU,SAAA,EAAAhB,YAAA,CAAAhJ,GAAA,WAAAjO,CAAA;oBAAA,IAAAkY,qBAAA,EAAAC,sBAAA;oBAAA;sBACApf,IAAA,EAAAiH,CAAA,CAAApD,QAAA;sBACAwb,eAAA,IAAAF,qBAAA,GAAAlY,CAAA,CAAA4X,sBAAA,cAAAM,qBAAA,uBAAAA,qBAAA,CAAAnU,MAAA;sBACAsU,WAAA,IAAAF,sBAAA,GAAAnY,CAAA,CAAA4X,sBAAA,cAAAO,sBAAA,uBAAAA,sBAAA,CAAAlK,GAAA,WAAAhK,CAAA;wBAAA,OAAAA,CAAA,CAAA8T,SAAA;sBAAA;oBACA;kBAAA;gBACA;;gBAEA;gBACAhB,OAAA,CAAArX,SAAA;kBACAqX,OAAA,CAAAuB,qBAAA;gBACA;cACA;gBACAxV,OAAA,CAAAC,KAAA,aAAA0C,QAAA;gBACAsR,OAAA,CAAAtO,QAAA,CAAA1F,KAAA;cACA;cAAAsU,UAAA,CAAAjX,CAAA;cAAA;YAAA;cAAAiX,UAAA,CAAApT,CAAA;cAAAmT,IAAA,GAAAC,UAAA,CAAA/W,CAAA;cAEAwC,OAAA,CAAAC,KAAA,cAAAqU,IAAA;cACAL,OAAA,CAAAtO,QAAA,CAAA1F,KAAA,eAAAqU,IAAA,CAAA5S,OAAA;YAAA;cAAA6S,UAAA,CAAApT,CAAA;cAEA8S,OAAA,CAAAO,sBAAA;cAAA,OAAAD,UAAA,CAAAhQ,CAAA;YAAA;cAAA,OAAAgQ,UAAA,CAAA9Z,CAAA;UAAA;QAAA,GAAAyZ,SAAA;MAAA;IAEA;IAEA;IACAsB,qBAAA,WAAAA,sBAAA;MACA,IAAAC,QAAA,GAAAvV,QAAA,CAAAuE,cAAA;MACA,KAAAgR,QAAA;QACAzV,OAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,SAAA8T,uBAAA;QACA;UACA,KAAAA,uBAAA,CAAAC,OAAA;QACA,SAAA7R,GAAA;UACAnC,OAAA,CAAAC,KAAA,kBAAAkC,GAAA;QACA;MACA;;MAEA;MACA;QACA,KAAA4R,uBAAA,GAAAxe,OAAA,CAAAmgB,IAAA,CAAAD,QAAA;MACA,SAAAtT,GAAA;QACAnC,OAAA,CAAAC,KAAA,gBAAAkC,GAAA;QACA;MACA;MAEA,UAAA2R,mBAAA,SAAAA,mBAAA,CAAA7S,MAAA;QACAwU,QAAA,CAAApV,SAAA;QACA;MACA;MAEA,IAAAsV,UAAA,YAAAA,WAAAC,OAAA;QACA,IAAAje,IAAA,GAAAie,OAAA,CAAAC,SAAA;QACA,IAAAC,KAAA,GAAAF,OAAA,CAAAC,SAAA;QACA,IAAAE,GAAA,GAAAH,OAAA,CAAAC,SAAA;QACA,UAAAhc,MAAA,CAAAlC,IAAA,YAAAkC,MAAA,CAAAic,KAAA,YAAAjc,MAAA,CAAAkc,GAAA;MACA;;MAEA;MACA,IAAAC,QAAA,OAAArD,GAAA;MAEA,KAAAmB,mBAAA,CAAApa,OAAA,WAAAkb,YAAA;QACA,IAAAA,YAAA,CAAAE,sBAAA;UACAF,YAAA,CAAAE,sBAAA,CAAApb,OAAA,WAAAqb,UAAA;YACA,IAAAA,UAAA,CAAAkB,SAAA;cACAlB,UAAA,CAAAkB,SAAA,CAAAvc,OAAA,WAAAC,IAAA;gBACAqc,QAAA,CAAAE,GAAA,CAAAvc,IAAA,CAAAwc,UAAA;cACA;YACA;UACA;QACA;MACA;MAEAH,QAAA,GAAAlT,KAAA,CAAAsT,IAAA,CAAAJ,QAAA,EAAAxb,IAAA;MACA,IAAA6b,SAAA,GAAAL,QAAA,CAAA7K,GAAA,CAAAwK,UAAA;MAEA,IAAAK,QAAA,CAAA/U,MAAA;QACAwU,QAAA,CAAApV,SAAA;QACA;MACA;;MAEA;MACA,IAAAiW,MAAA;MACA,IAAAC,UAAA;MACA,IAAAC,MAAA;MACA,IAAAC,UAAA;MAEAzW,OAAA,CAAAuB,GAAA;MACAvB,OAAA,CAAAuB,GAAA;QACAmV,aAAA,OAAA5C,mBAAA,CAAA7S,MAAA;QACA0V,YAAA,OAAAlD,iBAAA,CAAA3Z,QAAA;QACA8c,eAAA,OAAAnD,iBAAA,CAAAgB;MACA;MAEA,KAAAX,mBAAA,CAAApa,OAAA,WAAAkb,YAAA;QACA,IAAAtN,YAAA,GAAAsN,YAAA,CAAA9a,QAAA;QACAkG,OAAA,CAAAuB,GAAA,gCAAA1H,MAAA,CAAAyN,YAAA;QAEA,IAAAsN,YAAA,CAAAE,sBAAA;UACA9U,OAAA,CAAAuB,GAAA,+BAAA1H,MAAA,CAAA+a,YAAA,CAAAE,sBAAA,CAAA7T,MAAA;UACA2T,YAAA,CAAAE,sBAAA,CAAApb,OAAA,WAAAqb,UAAA,EAAA8B,KAAA;YAAA,IAAAC,qBAAA;YACA9W,OAAA,CAAAuB,GAAA,yBAAA1H,MAAA,CAAAgd,KAAA,YAAAhd,MAAA,CAAAkb,UAAA,CAAAE,SAAA,4CAAApb,MAAA,GAAAid,qBAAA,GAAA/B,UAAA,CAAAkB,SAAA,cAAAa,qBAAA,uBAAAA,qBAAA,CAAA7V,MAAA;UACA;;UAEA;UACA2T,YAAA,CAAAE,sBAAA,CAAApb,OAAA,WAAAqb,UAAA,EAAAgC,UAAA;YACA,IAAAC,SAAA,GAAAhB,QAAA,CAAA7K,GAAA,WAAA8L,IAAA;cACA,IAAAC,KAAA,GAAAnC,UAAA,CAAAkB,SAAA,CAAAvJ,IAAA,WAAA/S,IAAA;gBAAA,OAAAA,IAAA,CAAAwc,UAAA,KAAAc,IAAA;cAAA;cACA,OAAAC,KAAA,GAAArJ,UAAA,CAAAqJ,KAAA,CAAAC,KAAA;YACA;;YAEA;YACA,IAAAC,cAAA,GAAAJ,SAAA,CAAA/K,MAAA,WAAAzO,CAAA;cAAA,OAAAA,CAAA,aAAAA,CAAA,KAAA0D,SAAA;YAAA,GAAAD,MAAA;YACAjB,OAAA,CAAAuB,GAAA,uCAAA1H,MAAA,CAAAkb,UAAA,CAAAE,SAAA,6CAAApb,MAAA,CAAAud,cAAA,OAAAvd,MAAA,CAAAmd,SAAA,CAAA/V,MAAA;;YAEA;YACA,IAAAoW,UAAA,MAAAxd,MAAA,CAAAyN,YAAA,OAAAzN,MAAA,CAAAkb,UAAA,CAAAE,SAAA;YACA,IAAAqC,SAAA,GAAAd,MAAA,CAAAC,UAAA,GAAAD,MAAA,CAAAvV,MAAA;;YAEA;YACA,IAAAsW,OAAA,GAAAC,IAAA,CAAAC,SAAA,CAAAT,SAAA;YACA,IAAAU,cAAA,GAAApB,MAAA,CAAA5J,IAAA,WAAA1H,CAAA;cAAA,OAAAwS,IAAA,CAAAC,SAAA,CAAAzS,CAAA,CAAA3O,IAAA,MAAAkhB,OAAA;YAAA;YACA,IAAAI,YAAA,GAAAX,SAAA;YAEA,IAAAU,cAAA,IAAAV,SAAA,CAAAY,IAAA,WAAApa,CAAA;cAAA,OAAAA,CAAA;YAAA;cACA;cACAma,YAAA,GAAAX,SAAA,CAAA7L,GAAA,WAAAzU,KAAA;gBAAA,OAAAA,KAAA,YAAAA,KAAA;cAAA;cACAsJ,OAAA,CAAAuB,GAAA,+DAAA1H,MAAA,CAAAwd,UAAA;YACA;YAEAf,MAAA,CAAA1e,IAAA;cACA3B,IAAA,EAAAohB,UAAA;cACAzR,IAAA;cACAvP,IAAA,EAAAshB,YAAA;cACAE,MAAA;cACAC,SAAA;gBACAC,KAAA;gBACAC,KAAA,EAAAV,SAAA;gBACA;gBACA1R,IAAA,EAAA+R,YAAA,KAAAX,SAAA;cACA;cACAiB,SAAA;gBACAD,KAAA,EAAAV;cACA;cACAY,MAAA;cACAC,UAAA;cACAC,YAAA;cACA;cACAC,CAAA,EAAA5B,UAAA;YACA;YAEAF,UAAA,CAAA3e,IAAA,CAAAyf,UAAA;YACAZ,UAAA;YACAzW,OAAA,CAAAuB,GAAA,yCAAA1H,MAAA,CAAAwd,UAAA,0BAAAxd,MAAA,CAAAyd,SAAA,sCAAAzd,MAAA,CAAAud,cAAA;UACA;QACA;MACA;MAEApX,OAAA,CAAAuB,GAAA;MACAvB,OAAA,CAAAuB,GAAA,6BAAA1H,MAAA,CAAAyc,MAAA,CAAArV,MAAA;MACAqV,MAAA,CAAA5c,OAAA,WAAAsL,CAAA,EAAAsT,CAAA;QACA,IAAAC,UAAA,GAAAvT,CAAA,CAAA3O,IAAA,CAAA4V,MAAA,WAAAzO,CAAA;UAAA,OAAAA,CAAA,aAAAA,CAAA,KAAA0D,SAAA;QAAA,GAAAD,MAAA;QACAjB,OAAA,CAAAuB,GAAA,MAAA1H,MAAA,CAAAye,CAAA,YAAAze,MAAA,CAAAmL,CAAA,CAAA/O,IAAA,kCAAA4D,MAAA,CAAA0e,UAAA;MACA;;MAEA;MACA,IAAAC,QAAA,EAAAC,QAAA;MACA,IAAAC,WAAA,GAAApC,MAAA,CAAAqC,OAAA,WAAA3T,CAAA;QAAA,OAAAA,CAAA,CAAA3O,IAAA,CAAA4V,MAAA,WAAAzO,CAAA;UAAA,OAAAA,CAAA,aAAAA,CAAA,KAAA0D,SAAA;QAAA;MAAA;MACA,IAAAwX,WAAA,CAAAzX,MAAA;QACAuX,QAAA,GAAAI,IAAA,CAAAC,GAAA,CAAA7M,KAAA,CAAA4M,IAAA,MAAAlG,mBAAA,CAAA3Z,OAAA,EAAA2f,WAAA;QACAD,QAAA,GAAAG,IAAA,CAAAE,GAAA,CAAA9M,KAAA,CAAA4M,IAAA,MAAAlG,mBAAA,CAAA3Z,OAAA,EAAA2f,WAAA;MACA;MAEA,IAAAK,MAAA;QACAC,eAAA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACAvT,IAAA;UACA;UACAwT,SAAA,WAAAA,UAAArb,MAAA;YACA,IAAAsb,GAAA,GAAAtb,MAAA,IAAAub,cAAA;YACAvb,MAAA,CAAArE,OAAA,WAAAC,IAAA;cACA,IAAAA,IAAA,CAAAjD,KAAA,aAAAiD,IAAA,CAAAjD,KAAA,KAAAwK,SAAA;gBACAmY,GAAA,OAAAxf,MAAA,CAAAF,IAAA,CAAA4f,MAAA,EAAA1f,MAAA,CAAAF,IAAA,CAAA6f,UAAA,QAAA3f,MAAA,CAAAF,IAAA,CAAAjD,KAAA;cACA;gBACA2iB,GAAA,OAAAxf,MAAA,CAAAF,IAAA,CAAA4f,MAAA,EAAA1f,MAAA,CAAAF,IAAA,CAAA6f,UAAA;cACA;YACA;YACA,OAAAH,GAAA;UACA;QACA;QACAI,MAAA;UACApjB,IAAA,EAAAkgB,UAAA;UACAmD,SAAA;YACA1B,KAAA;UACA;UACA2B,GAAA;QACA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAJ,GAAA;UACAK,YAAA;QACA;QACAC,KAAA;UACArU,IAAA;UACAvP,IAAA,EAAAggB,SAAA;UACA6D,SAAA;YACAlC,KAAA;YACAmC,QAAA,WAAAA,SAAAtD,KAAA,EAAAngB,KAAA;cACA,IAAAmgB,KAAA,IAAAb,QAAA,CAAA/U,MAAA,KAAA+U,QAAA,CAAA/U,MAAA;cAEA,IAAAmZ,YAAA,OAAAzH,GAAA;cACAqD,QAAA,CAAAtc,OAAA,WAAAkc,OAAA;gBACA,IAAAje,IAAA,GAAAie,OAAA,CAAAC,SAAA;gBACA,IAAAC,KAAA,GAAAF,OAAA,CAAAC,SAAA;gBACAuE,YAAA,CAAAlE,GAAA,IAAArc,MAAA,CAAAlC,IAAA,EAAAkC,MAAA,CAAAic,KAAA;cACA;cAEA,IAAAuE,WAAA,GAAAD,YAAA,CAAAE,IAAA;cACA,IAAAD,WAAA;cAEA,IAAAE,eAAA,GAAAvE,QAAA,CAAA/U,MAAA;cACA,IAAAuZ,aAAA,GAAA5B,IAAA,CAAA6B,KAAA,CAAAF,eAAA,GAAA3B,IAAA,CAAAC,GAAA,CAAAwB,WAAA;cAEA,OAAAxD,KAAA,GAAA+B,IAAA,CAAAE,GAAA,CAAA0B,aAAA;YACA;YACApB,SAAA,WAAAA,UAAA1iB,KAAA,EAAAmgB,KAAA;cACA,IAAAA,KAAA,IAAAb,QAAA,CAAA/U,MAAA;cACA,IAAAyZ,eAAA,GAAA1E,QAAA,CAAAa,KAAA;cACA,KAAA6D,eAAA;cAEA,IAAA/iB,IAAA,GAAA+iB,eAAA,CAAA7E,SAAA;cACA,IAAAC,KAAA,GAAAnN,QAAA,CAAA+R,eAAA,CAAA7E,SAAA;cACA,UAAAhc,MAAA,CAAAlC,IAAA,OAAAkC,MAAA,CAAAic,KAAA;YACA;UACA;UACA6E,QAAA;YACA7C,SAAA;cACAE,KAAA;YACA;UACA;QACA;QACA4C,KAAA;UACAhV,IAAA;UACA3P,IAAA;UACA4iB,GAAA,EAAAL,QAAA;UACAM,GAAA,EAAAL,QAAA;UACAkC,QAAA;YACA7C,SAAA;cACAE,KAAA;YACA;UACA;UACAkC,SAAA;YACAlC,KAAA;UACA;UACA6C,SAAA;YACA/C,SAAA;cACAE,KAAA;YACA;UACA;QACA;QACA1B,MAAA,EAAAA;MACA;MAEA,KAAAvC,uBAAA,CAAA+G,SAAA,CAAA/B,MAAA;IACA;IAEA;IACAvH,+BAAA,WAAAA,gCAAA;MAAA,IAAAuJ,OAAA;MACA;MACA,SAAAvK,+BAAA,CAAAwK,QAAA,WACA,KAAAlK,4BAAA,CAAAkK,QAAA,WACA,MAAAzJ,wBAAA;QAEA,KAAAA,wBAAA;QACAvR,OAAA,CAAAuB,GAAA;;QAEA;QACA,KAAA3E,SAAA;UACAme,OAAA,CAAAjJ,iCAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}