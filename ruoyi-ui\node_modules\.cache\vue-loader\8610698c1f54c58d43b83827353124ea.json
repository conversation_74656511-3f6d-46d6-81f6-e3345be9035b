{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\department\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\leave\\department\\index.vue", "mtime": 1756456282571}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGVwYXJ0bWVudCwgZ2V0RGVwYXJ0bWVudCwgZGVsRGVwYXJ0bWVudCwgYWRkRGVwYXJ0bWVudCwgdXBkYXRlRGVwYXJ0bWVudCwgZXhwb3J0RGVwYXJ0bWVudCB9IGZyb20gIkAvYXBpL2xlYXZlL2RlcGFydG1lbnQiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJEZXBhcnRtZW50IiwNCiAgY29tcG9uZW50czogew0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g5Ye66Zeo6K+B6YOo6Zeo77yI5Y6C5YaF5Y2V5L2N77yJ6KGo5qC85pWw5o2uDQogICAgICBkZXBhcnRtZW50TGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgdmFsaWRGbGFnOiBudWxsLA0KICAgICAgICBzdG9yZUNvZGU6IG51bGwsDQogICAgICAgIHN0b3JlTmFtZTogbnVsbCwNCiAgICAgICAgcXVlcnlXb3JkOiBudWxsLA0KICAgICAgICB0eXBlOiBudWxsLA0KICAgICAgICB1bml0TmFtZTogbnVsbCwNCiAgICAgICAgcG9zaXRpb246IG51bGwsDQogICAgICAgIGxvd2VyTGltaXQ6IG51bGwsDQogICAgICAgIHVwTGltaXQ6IG51bGwsDQogICAgICAgIG1lbW86IG51bGwsDQogICAgICAgIGZTdG9yZUNvZGU6IG51bGwsDQogICAgICAgIGZTdG9yZU5hbWU6IG51bGwsDQogICAgICAgIGxldmVsTmFtZTogbnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i5Ye66Zeo6K+B6YOo6Zeo77yI5Y6C5YaF5Y2V5L2N77yJ5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0RGVwYXJ0bWVudCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kZXBhcnRtZW50TGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICB2YWxpZEZsYWc6IG51bGwsDQogICAgICAgIHN0b3JlQ29kZTogbnVsbCwNCiAgICAgICAgc3RvcmVOYW1lOiBudWxsLA0KICAgICAgICBxdWVyeVdvcmQ6IG51bGwsDQogICAgICAgIHR5cGU6IG51bGwsDQogICAgICAgIHVuaXROYW1lOiBudWxsLA0KICAgICAgICBwb3NpdGlvbjogbnVsbCwNCiAgICAgICAgbG93ZXJMaW1pdDogbnVsbCwNCiAgICAgICAgdXBMaW1pdDogbnVsbCwNCiAgICAgICAgbWVtbzogbnVsbCwNCiAgICAgICAgZlN0b3JlQ29kZTogbnVsbCwNCiAgICAgICAgZlN0b3JlTmFtZTogbnVsbCwNCiAgICAgICAgbGV2ZWxOYW1lOiBudWxsLA0KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Ye66Zeo6K+B6YOo6Zeo77yI5Y6C5YaF5Y2V5L2N77yJIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0RGVwYXJ0bWVudChpZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55Ye66Zeo6K+B6YOo6Zeo77yI5Y6C5YaF5Y2V5L2N77yJIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZURlcGFydG1lbnQodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZERlcGFydG1lbnQodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTlh7rpl6jor4Hpg6jpl6jvvIjljoLlhoXljZXkvY3vvInnvJblj7fkuLoiJyArIGlkcyArICci55qE5pWw5o2u6aG5PycsICLorablkYoiLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgICB9KS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICAgIHJldHVybiBkZWxEZXBhcnRtZW50KGlkcyk7DQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgY29uc3QgcXVlcnlQYXJhbXMgPSB0aGlzLnF1ZXJ5UGFyYW1zOw0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5a+85Ye65omA5pyJ5Ye66Zeo6K+B6YOo6Zeo77yI5Y6C5YaF5Y2V5L2N77yJ5pWw5o2u6aG5PycsICLorablkYoiLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgICB9KS50aGVuKGZ1bmN0aW9uKCkgew0KICAgICAgICAgIHJldHVybiBleHBvcnREZXBhcnRtZW50KHF1ZXJ5UGFyYW1zKTsNCiAgICAgICAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy5kb3dubG9hZChyZXNwb25zZS5tc2cpOw0KICAgICAgICB9KQ0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsQA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/leave/department", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"状态\" prop=\"validFlag\">\r\n        <el-input\r\n          v-model=\"queryParams.validFlag\"\r\n          placeholder=\"请输入状态\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"库房编码\" prop=\"storeCode\">\r\n        <el-input\r\n          v-model=\"queryParams.storeCode\"\r\n          placeholder=\"请输入库房编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"库房名称\" prop=\"storeName\">\r\n        <el-input\r\n          v-model=\"queryParams.storeName\"\r\n          placeholder=\"请输入库房名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"拼音头缩写\" prop=\"queryWord\">\r\n        <el-input\r\n          v-model=\"queryParams.queryWord\"\r\n          placeholder=\"请输入拼音头缩写\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" prop=\"type\">\r\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" clearable size=\"small\">\r\n          <el-option label=\"请选择字典生成\" value=\"\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"单位名称\" prop=\"unitName\">\r\n        <el-input\r\n          v-model=\"queryParams.unitName\"\r\n          placeholder=\"请输入单位名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"库房位置\" prop=\"position\">\r\n        <el-input\r\n          v-model=\"queryParams.position\"\r\n          placeholder=\"请输入库房位置\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"最小限制\" prop=\"lowerLimit\">\r\n        <el-input\r\n          v-model=\"queryParams.lowerLimit\"\r\n          placeholder=\"请输入最小限制\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"最大限制\" prop=\"upLimit\">\r\n        <el-input\r\n          v-model=\"queryParams.upLimit\"\r\n          placeholder=\"请输入最大限制\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"备注信息\" prop=\"memo\">\r\n        <el-input\r\n          v-model=\"queryParams.memo\"\r\n          placeholder=\"请输入备注信息\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"上级单位编码\" prop=\"fStoreCode\">\r\n        <el-input\r\n          v-model=\"queryParams.fStoreCode\"\r\n          placeholder=\"请输入上级单位编码\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"上级单位名称\" prop=\"fStoreName\">\r\n        <el-input\r\n          v-model=\"queryParams.fStoreName\"\r\n          placeholder=\"请输入上级单位名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"单位层级\" prop=\"levelName\">\r\n        <el-input\r\n          v-model=\"queryParams.levelName\"\r\n          placeholder=\"请输入单位层级\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"cyan\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['leave:department:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['leave:department:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['leave:department:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['leave:department:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n\t  <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"departmentList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"主键\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"validFlag\" />\r\n      <el-table-column label=\"库房编码\" align=\"center\" prop=\"storeCode\" />\r\n      <el-table-column label=\"库房名称\" align=\"center\" prop=\"storeName\" />\r\n      <el-table-column label=\"拼音头缩写\" align=\"center\" prop=\"queryWord\" />\r\n      <el-table-column label=\"原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" align=\"center\" prop=\"type\" />\r\n      <el-table-column label=\"单位名称\" align=\"center\" prop=\"unitName\" />\r\n      <el-table-column label=\"库房位置\" align=\"center\" prop=\"position\" />\r\n      <el-table-column label=\"最小限制\" align=\"center\" prop=\"lowerLimit\" />\r\n      <el-table-column label=\"最大限制\" align=\"center\" prop=\"upLimit\" />\r\n      <el-table-column label=\"备注信息\" align=\"center\" prop=\"memo\" />\r\n      <el-table-column label=\"上级单位编码\" align=\"center\" prop=\"fStoreCode\" />\r\n      <el-table-column label=\"上级单位名称\" align=\"center\" prop=\"fStoreName\" />\r\n      <el-table-column label=\"单位层级\" align=\"center\" prop=\"levelName\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['leave:department:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['leave:department:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改出门证部门（厂内单位）对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"状态\" prop=\"validFlag\">\r\n          <el-input v-model=\"form.validFlag\" placeholder=\"请输入状态\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"库房编码\" prop=\"storeCode\">\r\n          <el-input v-model=\"form.storeCode\" placeholder=\"请输入库房编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"库房名称\" prop=\"storeName\">\r\n          <el-input v-model=\"form.storeName\" placeholder=\"请输入库房名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"拼音头缩写\" prop=\"queryWord\">\r\n          <el-input v-model=\"form.queryWord\" placeholder=\"请输入拼音头缩写\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\" prop=\"type\">\r\n          <el-select v-model=\"form.type\" placeholder=\"请选择原料库0，港口库为1，车站库2；3-投料库，4-完工库，5-销售库\">\r\n            <el-option label=\"请选择字典生成\" value=\"\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"单位名称\" prop=\"unitName\">\r\n          <el-input v-model=\"form.unitName\" placeholder=\"请输入单位名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"库房位置\" prop=\"position\">\r\n          <el-input v-model=\"form.position\" placeholder=\"请输入库房位置\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"最小限制\" prop=\"lowerLimit\">\r\n          <el-input v-model=\"form.lowerLimit\" placeholder=\"请输入最小限制\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"最大限制\" prop=\"upLimit\">\r\n          <el-input v-model=\"form.upLimit\" placeholder=\"请输入最大限制\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注信息\" prop=\"memo\">\r\n          <el-input v-model=\"form.memo\" placeholder=\"请输入备注信息\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"上级单位编码\" prop=\"fStoreCode\">\r\n          <el-input v-model=\"form.fStoreCode\" placeholder=\"请输入上级单位编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"上级单位名称\" prop=\"fStoreName\">\r\n          <el-input v-model=\"form.fStoreName\" placeholder=\"请输入上级单位名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"单位层级\" prop=\"levelName\">\r\n          <el-input v-model=\"form.levelName\" placeholder=\"请输入单位层级\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listDepartment, getDepartment, delDepartment, addDepartment, updateDepartment, exportDepartment } from \"@/api/leave/department\";\r\n\r\nexport default {\r\n  name: \"Department\",\r\n  components: {\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 出门证部门（厂内单位）表格数据\r\n      departmentList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        validFlag: null,\r\n        storeCode: null,\r\n        storeName: null,\r\n        queryWord: null,\r\n        type: null,\r\n        unitName: null,\r\n        position: null,\r\n        lowerLimit: null,\r\n        upLimit: null,\r\n        memo: null,\r\n        fStoreCode: null,\r\n        fStoreName: null,\r\n        levelName: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询出门证部门（厂内单位）列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDepartment(this.queryParams).then(response => {\r\n        this.departmentList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        validFlag: null,\r\n        storeCode: null,\r\n        storeName: null,\r\n        queryWord: null,\r\n        type: null,\r\n        unitName: null,\r\n        position: null,\r\n        lowerLimit: null,\r\n        upLimit: null,\r\n        memo: null,\r\n        fStoreCode: null,\r\n        fStoreName: null,\r\n        levelName: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加出门证部门（厂内单位）\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getDepartment(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改出门证部门（厂内单位）\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateDepartment(this.form).then(response => {\r\n              this.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDepartment(this.form).then(response => {\r\n              this.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除出门证部门（厂内单位）编号为\"' + ids + '\"的数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delDepartment(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有出门证部门（厂内单位）数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return exportDepartment(queryParams);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n        })\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}