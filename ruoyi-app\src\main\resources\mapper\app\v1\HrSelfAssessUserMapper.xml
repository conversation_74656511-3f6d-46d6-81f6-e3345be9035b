<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.v1.mapper.HrSelfAssessUserMapper">

    <resultMap type="HrSelfAssessUser" id="HrSelfAssessUserResult">
        <result property="id"    column="id"    />
        <result property="workNo"    column="work_no"    />
        <result property="name"    column="name"    />
        <result property="assessRole"    column="assess_role"    />
        <result property="postType"    column="post_type"    />
        <result property="job"    column="job"    />
        <result property="benefitLinkFlag"    column="benefit_link_flag"    />
        <result property="averageLinkFlag"    column="average_link_flag"    />
    </resultMap>

    <resultMap type="HrLateralAssessDept" id="HrLateralAssessDeptResult">
        <result property="deptId"    column="dept_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="showFlag"    column="show_flag"    />
        <result property="parentName" column="parent_name" />
    </resultMap>

    <sql id="selectHrSelfAssessUserVo">
        select id, work_no, name, assess_role, post_type, job, benefit_link_flag, average_link_flag from hr_self_assess_user
    </sql>

    <sql id="selectHrSelfAssessUserJoinDeptVo">
        select u.id, u.work_no, u.name, u.assess_role, u.post_type, u.job, u.benefit_link_flag, u.average_link_flag
        from hr_self_assess_user u
        left join hr_self_assess_user_dept d on d.user_id = u.id
    </sql>

    <select id="findList" parameterType="HrSelfAssessUser" resultMap="HrSelfAssessUserResult">
        <include refid="selectHrSelfAssessUserVo"/>
        <where>
            del_flag = '0'
            <if test="workNo != null  and workNo != ''"> and work_no = #{workNo}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="assessRole != null  and assessRole != ''"> and assess_role = #{assessRole}</if>
            <if test="benefitLinkFlag != null  and benefitLinkFlag != ''"> and benefit_link_flag = #{benefitLinkFlag}</if>
            <if test="averageLinkFlag != null  and averageLinkFlag != ''"> and average_link_flag = #{averageLinkFlag}</if>
            <if test="deptId != null  and deptId != ''"> and id in (select user_id from hr_self_assess_user_dept where dept_id = #{deptId} )</if>
        </where>
    </select>

    <select id="listAvailable" parameterType="HrSelfAssessUser" resultMap="HrSelfAssessUserResult">
        <include refid="selectHrSelfAssessUserVo"/>
        <where>
            del_flag = '0' and (assess_role = '0' or assess_role = '1')
            <if test="workNo != null  and workNo != ''"> and work_no = #{workNo}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="deptId != null  and deptId != ''"> and id in (select u.id from hr_self_assess_user_dept d left join hr_self_assess_user u on u.id = d.user_id where d.dept_id = #{deptId} )</if>
            <if test="deptIds != null  and deptIds != ''"> and id in (select u.id from hr_self_assess_user_dept d left join hr_self_assess_user u on u.id = d.user_id where d.dept_id in
                <foreach item="item" index="index" collection="deptIds" separator="," open="(" close=")">#{item}</foreach>)
            </if>
        </where>
    </select>

    <select id="get" parameterType="String" resultMap="HrSelfAssessUserResult">
        <include refid="selectHrSelfAssessUserVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <select id="getByWorkNo" parameterType="String" resultMap="HrSelfAssessUserResult">
        <include refid="selectHrSelfAssessUserVo"/>
        where work_no = #{workNo} and del_flag = '0' limit 1
    </select>

    <select id="getAuth" parameterType="HrSelfAssessUser" resultMap="HrSelfAssessUserResult">
        <include refid="selectHrSelfAssessUserJoinDeptVo"/>
        where u.work_no = #{workNo} and u.assess_role = #{assessRole} and d.dept_id = #{deptId} and u.del_flag = '0'
    </select>

    <select id="getByUserInfo" parameterType="HrSelfAssessUser" resultMap="HrSelfAssessUserResult">
        select u.id, work_no, name, assess_role, post_type, job, benefit_link_flag, average_link_flag
        from hr_self_assess_user u
        left join hr_self_assess_user_dept d on u.id = d.user_id
        where u.work_no = #{workNo} and u.del_flag = '0' and  d.dept_id in
        <foreach item="item" index="index" collection="deptIds" separator="," open="(" close=")">#{item}</foreach>
        limit 1
    </select>

    <select id="checkUserUnique" parameterType="HrSelfAssessUser" resultMap="HrSelfAssessUserResult">
        select u.id, work_no, name, assess_role, post_type, job, benefit_link_flag, average_link_flag
        from hr_self_assess_user u
        left join hr_self_assess_user_dept d on u.id = d.user_id
        where u.work_no = #{workNo} and u.del_flag = '0' and  d.dept_id in
        <foreach item="item" index="index" collection="deptIds" separator="," open="(" close=")">#{item}</foreach>
        <if test="id != null  and id != ''"> and u.id != #{id}</if>
        limit 1
    </select>

    <select id="getByWorkNoDeptId" parameterType="HrSelfAssessUser" resultMap="HrSelfAssessUserResult">
        select u.id, work_no, name, assess_role, post_type, job, benefit_link_flag, average_link_flag
        from hr_self_assess_user u
            left join hr_self_assess_user_dept d on u.id = d.user_id
        where u.work_no = #{workNo} and u.del_flag = '0' and  d.dept_id = #{deptId}
    </select>

    <select id="getLeaderWorkNoByName" parameterType="HrSelfAssessUser" resultMap="HrSelfAssessUserResult">
        select id, work_no, name, assess_role, post_type, job, benefit_link_flag, average_link_flag
        from hr_self_assess_user
        where assess_role = '2' and name like concat('%', #{name}, '%')
        limit 1
    </select>

    <insert id="insert" parameterType="HrSelfAssessUser" useGeneratedKeys="true" keyProperty="id">
        insert into hr_self_assess_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workNo != null">work_no,</if>
            <if test="name != null">name,</if>
            <if test="assessRole != null">assess_role,</if>
            <if test="postType != null">post_type,</if>
            <if test="job != null">job,</if>
            <if test="benefitLinkFlag != null">benefit_link_flag,</if>
            <if test="averageLinkFlag != null">average_link_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="workNo != null">#{workNo},</if>
            <if test="name != null">#{name},</if>
            <if test="assessRole != null">#{assessRole},</if>
            <if test="postType != null">#{postType},</if>
            <if test="job != null">#{job},</if>
            <if test="benefitLinkFlag != null">#{benefitLinkFlag},</if>
            <if test="averageLinkFlag != null">#{averageLinkFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="update" parameterType="HrSelfAssessUser">
        update hr_self_assess_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="workNo != null">work_no = #{workNo},</if>
            <if test="name != null">name = #{name},</if>
            <if test="assessRole != null">assess_role = #{assessRole},</if>
            <if test="postType != null">post_type = #{postType},</if>
            <if test="job != null">job = #{job},</if>
            <if test="benefitLinkFlag != null">benefit_link_flag = #{benefitLinkFlag},</if>
            <if test="averageLinkFlag != null">average_link_flag = #{averageLinkFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="delete" parameterType="HrSelfAssessUser">
        update hr_self_assess_user
        set
            del_flag = #{delFlag},
            delete_by = #{deleteBy},
            delete_time = #{deleteTime}
        where id = #{id}
    </update>



    <insert id="batch">
        insert into hr_self_assess_user( id, work_no, name, assess_role, post_type, job, benefit_link_flag, average_link_flag) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.workNo}, #{item.name}, #{item.assessRole}, #{item.postType}, #{job}, #{item.benefitLinkFlag}, #{item.averageLinkFlag})
        </foreach>
    </insert>

    <insert id="batchHrSelfAssessUserDept">
        insert into hr_self_assess_user_dept( dept_id,user_id ) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.deptId}, #{item.id} )
        </foreach>
    </insert>

    <insert id="batchHrSelfAssessUserLeader">
        insert into hr_self_assess_user_leader( user_id,work_no ) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.workNo} )
        </foreach>
    </insert>

    <delete id="deleteHrSelfAssessUserDept">
        delete from hr_self_assess_user_dept where user_id = #{id}
    </delete>

    <delete id="deleteHrSelfAssessUserLeader">
        delete from hr_self_assess_user_leader where user_id = #{id}
    </delete>

    <select id="selectHrSelfAssessUserDeptByUserId" parameterType="String" resultType="Long">
        select dept_id from hr_self_assess_user_dept where user_id = #{id}
    </select>

    <select id="selectHrSelfAssessUserLeaderByUserId" parameterType="String" resultType="String">
        select work_no from hr_self_assess_user_leader where user_id = #{id}
    </select>

    <select id="selectHrSelfAssessUsersByLeaderWorkNo" parameterType="String" resultType="String">
        select user_id from hr_self_assess_user_leader where work_no = #{workNo}
    </select>

    <select id="selectHrSelfAssessUserDeptListByUserId" parameterType="String" resultMap="HrLateralAssessDeptResult">
        select d.dept_id, d.parent_id, d.dept_name, d.del_flag,d.show_flag
        from hr_self_assess_user_dept u
            left join hr_lateral_assess_dept d on d.dept_id = u.dept_id
        where u.user_id = #{id}
    </select>

    <select id="getDeptListByUser" parameterType="String" resultMap="HrLateralAssessDeptResult">
        select d.dept_id, d.parent_id, d.dept_name, d.del_flag,d.show_flag
        from hr_self_assess_user_dept u
            left join hr_lateral_assess_dept d on d.dept_id = u.dept_id
            left join hr_self_assess_user a on a.id = u.user_id
        where a.work_no = #{workNo} and assess_role = #{assessRole}
    </select>

    <select id="getAllDeptListByWorkNo" parameterType="String" resultMap="HrLateralAssessDeptResult">
        select d.dept_id, d.parent_id, d.dept_name, d.del_flag,d.show_flag
        from hr_self_assess_user_dept u
            left join hr_lateral_assess_dept d on d.dept_id = u.dept_id
            left join hr_self_assess_user a on a.id = u.user_id
        where a.work_no = #{workNo}
    </select>

    <select id="getUserAssessStatus" parameterType="map" resultType="String">
        select status
        from hr_self_assess_info
        where work_no = #{workNo}
        and DATE_FORMAT(assess_date, '%Y-%c') = #{assessDate}
        and del_flag = '0'
        order by update_time desc
        limit 1
    </select>


</mapper>