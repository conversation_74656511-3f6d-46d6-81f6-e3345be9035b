# 采购全景数据看板 Vue 组件

## 概述
这是一个从HTML页面改造而来的Vue组件，用于显示采购相关的各种数据图表和统计信息。

## 文件结构
```
purchaseDashboard/
├── index.vue              # 主Vue组件
├── chartMethods.js        # 基础图表方法 (Vue mixin)
├── chartMethodsExtended.js # 扩展图表方法 (Vue mixin)
└── README.md              # 说明文档
```

## 功能特性

### 1. 时间维度过滤
- 近三个月
- 近六个月  
- 近一年

### 2. 图表展示
- **物料跟踪金额饼图**: 显示出库、入库、在途总额
- **采购关键指标**: 合同到期提醒、供应商风险提醒、到货完成度
- **大类物料入库到货统计**: 柱状图展示
- **中类物料入库到货统计**: 带筛选功能的柱状图
- **细类物料入库到货统计**: 带筛选功能的柱状图  
- **叶类物料入库到货统计**: 带筛选功能的柱状图
- **TOP10供应商供货金额**: 轮播显示的柱状图
- **TOP10供应商风险提醒**: 风险等级颜色编码
- **高频采购物料词云**: 交互式文字云
- **PB块采购价格趋势图**: 价格与采购数量双轴图

### 3. 交互功能
- 时间维度切换
- 物料类型过滤
- 图表自动轮播
- 鼠标悬停效果
- 响应式布局

## 使用方法

### 1. 在路由中注册
```javascript
// router/index.js
{
  path: '/purchase-dashboard',
  component: () => import('@/views/purchaseDashboard/index.vue'),
  name: 'PurchaseDashboard',
  meta: { title: '采购全景数据看板' }
}
```

### 2. 在页面中使用
```vue
<template>
  <div>
    <PurchaseDashboard />
  </div>
</template>

<script>
import PurchaseDashboard from '@/views/purchaseDashboard/index.vue'

export default {
  components: {
    PurchaseDashboard
  }
}
</script>
```

## API 接口

组件直接使用fetch调用以下API接口（使用完整域名）：

### 1. 获取仪表板数据
```
GET https://ydxt.citicsteel.com:8099/prod-api/procurement/dashboard/showData?dimensionType={1|2|3}
```

### 2. 获取物料类型列表
```
GET https://ydxt.citicsteel.com:8099/prod-api/procurement/dashboard/showItemTypeList?itemType={1|2|3}
```

### 3. 获取物料列表
```
GET https://ydxt.citicsteel.com:8099/prod-api/procurement/dashboard/showMaterialList?dimensionType={dimensionType}&itemType={itemType}&itemId={itemId}
```

### 4. 获取供应商列表
```
GET https://ydxt.citicsteel.com:8099/prod-api/procurement/dashboard/showSuppList?dimensionType={dimensionType}&itemId={itemId}
```

## 依赖项

- Vue 2.6+
- ECharts 4.9.0
- 原生 fetch API（无需额外的HTTP客户端库）

## 特色功能

### 1. 图表轮播
部分图表支持自动轮播功能，每15秒切换一次显示内容。

### 2. 响应式设计
图表会根据容器大小自动调整，支持窗口大小变化。

### 3. 错误处理
包含完善的错误处理机制，API失败时会显示友好的错误信息。

### 4. 内存管理
组件销毁时会自动清理定时器和事件监听器，避免内存泄漏。

### 5. 直接API调用
使用原生fetch API直接调用完整的域名地址，无需依赖项目的request工具。

## 样式特点

- 深色主题设计，使用紫色渐变背景
- 卡片式布局，每个图表独立展示
- 炫光边框效果
- 统一的颜色方案和字体

## 技术架构

### Vue Mixins
组件使用Vue mixins来组织图表方法：
- `chartMethods.js`: 基础图表方法（饼图、柱状图等）
- `chartMethodsExtended.js`: 扩展图表方法（词云、趋势图等）

### API调用方式
```javascript
// 在Vue组件中直接使用fetch
async getDashboardData(dimensionType) {
  const url = `${this.baseApiUrl}/procurement/dashboard/showData?dimensionType=${dimensionType}`
  const response = await fetch(url)
  return await response.json()
}
```

## 注意事项

1. 确保后端API接口正常工作并支持跨域访问
2. ECharts版本需要与项目兼容
3. 组件需要足够的空间来正确显示
4. 建议在大屏显示器上使用以获得最佳体验
5. API域名 `https://ydxt.citicsteel.com:8099` 需要在生产环境中可访问 