{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\form\\admin.vue", "mtime": 1756456493827}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVHJlZVZpZXcgZnJvbSAiQC9jb21wb25lbnRzL1RyZWVWaWV3IjsNCmltcG9ydCB7DQogIHJvb3RMaXN0RGltZW5zaW9uYWxpdHksDQogIGdldFJvb3RMaXN0QnlJZCwNCiAgYWRkRGltZW5zaW9uYWxpdHksDQogIGdldFN0YXR1c0xpc3RXaXRoYWRtaW4NCn0gZnJvbSAiQC9hcGkvdFlqeS9kaW1lbnNpb25hbGl0eSI7DQoNCmltcG9ydCB7DQogIGxpc3RQZXJtaXNzaW9uLA0KfSBmcm9tICJAL2FwaS90WWp5L2RpbWVuc2lvbmFsaXR5cGVybWlzc2lvbiI7DQoNCmltcG9ydCB7DQogIGRlYWRsaW5lYnJhbmNoLA0KICB1cGRhdGVGb3JtLA0KfSBmcm9tICJAL2FwaS90WWp5L2Zvcm0iOw0KDQppbXBvcnQgeyBsaXN0RGVwdCB9IGZyb20gIkAvYXBpL3RZankvZGVwdCI7DQppbXBvcnQgYXhpb3MgZnJvbSAiYXhpb3MiOw0KaW1wb3J0ICogYXMgeGxzeCBmcm9tICd4bHN4JzsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiRGltZW5zaW9uYWxpdHkiLA0KICBjb21wb25lbnRzOiB7DQogICAgVHJlZVZpZXcsDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIG5ld09wZW46ZmFsc2UsDQogICAgICBTcGVjaWFsSW1wb3J0T3BlbjpmYWxzZSwNCiAgICAgIG1vdXRoSW1wb3J0T3BlbjpmYWxzZSwNCiAgICAgIHNlYXJjaG9wZW46ZmFsc2UsDQogICAgICB0b3RhbDowLA0KICAgICAgcGFnZVNpemVzOlsyMCw1MCwxMDBdLA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDIwLA0KICAgICAgICBkaW1lbnNpb25hbGl0eU5hbWU6IG51bGwsDQogICAgICAgIGlzVXNlOiBudWxsLA0KICAgICAgfSwNCiAgICAgIGN1c3RvbUJsb2JDb250ZW50OiBudWxsLA0KICAgICAgcm9vdExpc3Q6IFtdLA0KICAgICAgZGV0YWlsOiB7fSwNCiAgICAgIHJvb3RJZDpudWxsLA0KICAgICAgZHJhd2VyOmZhbHNlLA0KICAgICAgcXVlcnk6ew0KICAgICAgICBzdGFydERhdGU6bnVsbCwNCiAgICAgICAgZW5kRGF0ZTpudWxsLA0KICAgICAgICByb290SWQ6bnVsbCwNCiAgICAgICAgdGl0bGU6bnVsbCwNCiAgICAgIH0sDQogICAgICBleHBvcnRPcGVuOmZhbHNlLA0KICAgICAgZGVhZGxpbmVPcGVuOmZhbHNlLA0KICAgICAgZGVhZGxpbmVUaXRsZToi5om56YeP5L+u5pS55oiq5q2i5pel5pyfIiwNCiAgICAgIGRlYWRsaW5lRm9ybToNCiAgICAgIHsNCiAgICAgICAgZGltZW5zaW9uYWxpdHlQYXRoOm51bGwNCiAgICAgIH0sDQogICAgICBkYXRlVmFsdWU6bnVsbCwNCiAgICAgIGRlcHRMaXN0OiBbXSwNCiAgICAgIGZvcm06e30sDQogICAgICB1c2VyTGlzdDpbXSwNCiAgICAgIGFkbWluT3BlbjpmYWxzZSwNCiAgICAgIGFkbWluVGl0bGU6IueuoeeQhuWRmOWQjeWNlSIsDQogICAgICBzcGVjaWFsRmNEYXRlOm51bGwsDQogICAgICBkaW1lbnNpb25hbGl0eU5hbWU6bnVsbCwNCiAgICAgIGRpbWVuc2lvbmFsaXR5SWQ6bnVsbCwNCiAgICAgIG5vdGVTaG93OmZhbHNlLCAvL+aYr+WQpuWxleekuuaMh+aghw0KDQogICAgICB4bHN4T3B0aW9uczp7DQogICAgICAgICAgICAgICAgLy8geGxzOiBmYWxzZSwgICAgICAgLy/pooTop4h4bHN45paH5Lu26K6+5Li6ZmFsc2XvvJvpooTop4h4bHPmlofku7borr7kuLp0cnVlDQogICAgICAgICAgICAgICAgLy8gbWluQ29sTGVuZ3RoOiAwLCAgLy8gZXhjZWzmnIDlsJHmuLLmn5PlpJrlsJHliJfvvIzlpoLmnpzmg7Plrp7njrB4bHN45paH5Lu25YaF5a655pyJ5Yeg5YiX77yM5bCx5riy5p+T5Yeg5YiX77yM5Y+v5Lul5bCG5q2k5YC86K6+572u5Li6MC4NCiAgICAgICAgICAgICAgICAvLyBtaW5Sb3dMZW5ndGg6IDAsICAvLyBleGNlbOacgOWwkea4suafk+WkmuWwkeihjO+8jOWmguaenOaDs+WunueOsOagueaNrnhsc3jlrp7pmYXlh73mlbDmuLLmn5PvvIzlj6/ku6XlsIbmraTlgLzorr7nva7kuLowLg0KICAgICAgICAgICAgICAgIC8vIHdpZHRoT2Zmc2V0OiAxMCwgIC8v5aaC5p6c5riy5p+T5Ye65p2l55qE57uT5p6c5oSf6KeJ5Y2V5YWD5qC85a695bqm5LiN5aSf77yM5Y+v5Lul5Zyo6buY6K6k5riy5p+T55qE5YiX6KGo5a695bqm5LiK5YaN5YqgIE5weOWuvQ0KICAgICAgICAgICAgICAgIC8vIGhlaWdodE9mZnNldDogMTAsIC8v5Zyo6buY6K6k5riy5p+T55qE5YiX6KGo6auY5bqm5LiK5YaN5YqgIE5weOmrmA0KICAgICAgICAgICAgICAgIGJlZm9yZVRyYW5zZm9ybURhdGE6ICh3b3JrYm9va0RhdGEpID0+IHtyZXR1cm4gd29ya2Jvb2tEYXRhfSwgLy/lupXlsYLpgJrov4dleGNlbGpz6I635Y+WZXhjZWzmlofku7blhoXlrrnvvIzpgJrov4for6XpkqnlrZDlh73mlbDvvIzlj6/ku6Xlr7nojrflj5bnmoRleGNlbOaWh+S7tuWGheWuuei/m+ihjOS/ruaUue+8jOavlOWmguafkOS4quWNleWFg+agvOeahOaVsOaNruaYvuekuuS4jeato+ehru+8jOWPr+S7peWcqOatpOiHquihjOS/ruaUueavj+S4quWNleWFg+agvOeahHZhbHVl5YC844CCDQogICAgICAgICAgICAgICAgdHJhbnNmb3JtRGF0YTogKHdvcmtib29rRGF0YSkgPT4ge3JldHVybiB3b3JrYm9va0RhdGF9LCAvL+WwhuiOt+WPluWIsOeahGV4Y2Vs5pWw5o2u6L+b6KGM5aSE55CG5LmL5ZCO5LiU5riy5p+T5Yiw6aG16Z2i5LmL5YmN77yM5Y+v6YCa6L+HdHJhbnNmb3JtRGF0YeWvueWNs+Wwhua4suafk+eahOaVsOaNruWPiuagt+W8j+i/m+ihjOS/ruaUue+8jOatpOaXtuavj+S4quWNleWFg+agvOeahHRleHTlgLzlsLHmmK/ljbPlsIbmuLLmn5PliLDpobXpnaLkuIrnmoTlhoXlrrkNCiAgICAgICAgICAgIH0sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICB0aGlzLmdldERlcHQoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGNsaWNrTm9kZSgkZXZlbnQsIG5vZGUpIHsNCiAgICAgICRldmVudC50YXJnZXQucGFyZW50RWxlbWVudC5wYXJlbnRFbGVtZW50LmZpcnN0RWxlbWVudENoaWxkLmNsaWNrKCk7DQogICAgfSwNCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGdldFN0YXR1c0xpc3RXaXRoYWRtaW4odGhpcy5xdWVyeVBhcmFtcykudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMucm9vdExpc3QgPSByZXMucm93czsNCiAgICAgICAgZm9yKGxldCBpPTA7aTx0aGlzLnJvb3RMaXN0Lmxlbmd0aDtpKyspDQogICAgICAgIHsNCiAgICAgICAgICBpZih0aGlzLnJvb3RMaXN0W2ldLmlkPT0yNzMgfHwgdGhpcy5yb290TGlzdFtpXS5pZD09ODQwIHx8IHRoaXMucm9vdExpc3RbaV0uaWQ9PTg3MyB8fCB0aGlzLnJvb3RMaXN0W2ldLmlkPT0xMDc3IHx8IHRoaXMucm9vdExpc3RbaV0uaWQ9PTEwNTkgfHwgdGhpcy5jb250YWluc1N1YnN0cmluZygn5a6J5YWo6LSj5Lu75bel6LWEJyx0aGlzLnJvb3RMaXN0W2ldLmRpbWVuc2lvbmFsaXR5TmFtZSkpDQogICAgICAgICAgew0KICAgICAgICAgICAgdGhpcy5yb290TGlzdFtpXS5zaG93Ym9vdD0xDQogICAgICAgICAgfQ0KICAgICAgICAgIGVsc2UNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aGlzLnJvb3RMaXN0W2ldLnNob3dib290PTANCiAgICAgICAgICB9DQogICAgICAgICAgaWYodGhpcy5jb250YWluc1N1YnN0cmluZygn5bel6KOFJyx0aGlzLnJvb3RMaXN0W2ldLmRpbWVuc2lvbmFsaXR5TmFtZSkpDQogICAgICAgICAgew0KICAgICAgICAgICAgdGhpcy5yb290TGlzdFtpXS5zaG93TW91dGg9MQ0KICAgICAgICAgIH0NCiAgICAgICAgICBlbHNlDQogICAgICAgICAgew0KICAgICAgICAgICAgdGhpcy5yb290TGlzdFtpXS5zaG93TW91dGg9MA0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgICAgLy8gcm9vdExpc3REaW1lbnNpb25hbGl0eSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXMpID0+IHsNCiAgICAgIC8vICAgdGhpcy5yb290TGlzdCA9IHJlcy5yb3dzOw0KICAgICAgLy8gICB0aGlzLnRvdGFsID0gcmVzLnRvdGFsOw0KICAgICAgLy8gICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIC8vIH0pOw0KICAgIH0sDQogICAgZ2V0RGVwdCgpIHsNCiAgICAgIGxpc3REZXB0KCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMuZGVwdExpc3QgPSByZXMucm93c1swXS5jaGlsZHJlbjsNCiAgICAgICAgY29uc29sZS5sb2cocmVzKTsNCiAgICAgICAgZm9yKGxldCBpPTA7aTx0aGlzLmRlcHRMaXN0Lmxlbmd0aDtpKyspDQogICAgICAgIHsNCiAgICAgICAgICB0aGlzLmRlYWxkZXB0TGlzdCh0aGlzLmRlcHRMaXN0W2ldLDApDQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZGVhbGRlcHRMaXN0KHJvdyxjb3VudCkNCiAgICB7DQogICAgICAgcm93LnZhbHVlPXJvdy5wYXRoDQogICAgICAgcm93LmxhYmVsPXJvdy5kZXB0TmFtZQ0KICAgICAgIGlmKHJvdy5jaGlsZHJlbi5sZW5ndGg+MCAmJiBjb3VudDwxKQ0KICAgICAgIHsNCiAgICAgICAgICBmb3IobGV0IGk9MDtpPHJvdy5jaGlsZHJlbi5sZW5ndGg7aSsrKQ0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHRoaXMuZGVhbGRlcHRMaXN0KHJvdy5jaGlsZHJlbltpXSxjb3VudCsxKQ0KICAgICAgICAgIH0NCiAgICAgICB9DQogICAgICAgZWxzZQ0KICAgICAgIHsNCiAgICAgICAgICByb3cuY2hpbGRyZW49bnVsbA0KICAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVF1ZXJ5RGVwdCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuY2FzY2FkZXJIYW5kbGUuZHJvcERvd25WaXNpYmxlID0gZmFsc2U7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCg0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQoNCg0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMubmV3T3Blbj10cnVlDQogICAgICAvLyBsZXQgdGhhdCA9IHRoaXM7DQogICAgICAvLyB0aGlzLiRwcm9tcHQoIuivt+i+k+WFpeWQjeensCIsICLmj5DnpLoiLCB7DQogICAgICAvLyAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgIC8vICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAvLyB9KQ0KICAgICAgLy8gICAudGhlbigoeyB2YWx1ZSB9KSA9PiB7DQogICAgICAvLyAgICAgbGV0IGZvcm0gPSB7fTsNCiAgICAgIC8vICAgICBmb3JtLmRpbWVuc2lvbmFsaXR5TmFtZSA9IHZhbHVlOw0KICAgICAgLy8gICAgIGFkZERpbWVuc2lvbmFsaXR5KGZvcm0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgLy8gICAgICAgdGhhdC5nZXRMaXN0KCk7DQogICAgICAvLyAgICAgfSk7DQogICAgICAvLyAgIH0pDQogICAgICAvLyAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAvLyAgICAgdGhhdC4kbWVzc2FnZSh7DQogICAgICAvLyAgICAgICB0eXBlOiAiaW5mbyIsDQogICAgICAvLyAgICAgICBtZXNzYWdlOiAi5Y+W5raI5pON5L2cIiwNCiAgICAgIC8vICAgICB9KTsNCiAgICAgIC8vICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVEZXRhaWwocm93KXsNCiAgICAgIHRoaXMucm9vdElkID0gcm93LmlkOw0KICAgICAgdGhpcy5yb290UnVsZVR5cGUgPSByb3cucnVsZVR5cGU7DQogICAgICB0aGlzLmdldERldGFpbCgpOw0KICAgICAgdGhpcy5kcmF3ZXIgPSB0cnVlOw0KICAgIH0sDQogICAgaGFuZGxlRGVhZExpbmUocm93KXsNCiAgICAgIHRoaXMuZGVhZGxpbmVGb3JtPXtkaW1lbnNpb25hbGl0eVBhdGg6bnVsbH0NCiAgICAgIHRoaXMuZGVhZGxpbmVGb3JtLmRpbWVuc2lvbmFsaXR5UGF0aD1yb3cucGF0aA0KICAgICAgdGhpcy5kZWFkbGluZU9wZW49dHJ1ZQ0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICBpZih0aGlzLmRlYWRsaW5lRm9ybS5kZWFkbGluZVN3aXRjaD09MSkNCiAgICAgIHsNCiAgICAgICAgaWYodGhpcy5kZWFkbGluZUZvcm0uZGVhZGxpbmVEYXRlPT1udWxsKQ0KICAgICAgICB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuaIquatouaXpeacn+S4jeiDveS4uuepuiIpOw0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGxldCBkZWFkbGluZURhdGVDaGVjaz10aGlzLmRlYWRsaW5lRm9ybS5kZWFkbGluZURhdGUuc3BsaXQoIi8iKQ0KICAgICAgICBpZihkZWFkbGluZURhdGVDaGVjay5sZW5ndGghPTMpDQogICAgICAgIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5oiq5q2i5pel5pyf5qC85byP5LiN5q2j56Gu77yM5q2j56Gu5qC85byP5pivIOW5tC/mnIgv5pelICIpOw0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGlmKCEvXi0/KDB8KFsxLTldP1xkKXwxMDApJC8udGVzdChkZWFkbGluZURhdGVDaGVja1swXSkpDQogICAgICAgIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5oiq5q2i5pel5pyf5Lit5bm05bqU5piv5ZyoLTEwMOWIsDEwMOS5i+mXtOeahOaVtOaVsCIpOw0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGlmKCEvXi0/KDB8KFswXT9cZCl8MTF8MTIpJC8udGVzdChkZWFkbGluZURhdGVDaGVja1sxXSkpDQogICAgICAgIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5oiq5q2i5pel5pyf5Lit5pyI5bqU5piv5ZyoLTEy5YiwMTLkuYvpl7TnmoTmlbTmlbAiKTsNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgICBpZighL14tPygwfChbMS0yXT9cZCl8MzF8MzApJC8udGVzdChkZWFkbGluZURhdGVDaGVja1syXSkpDQogICAgICAgIHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5oiq5q2i5pel5pyf5Lit5pel5bqU5piv5ZyoLTMx5YiwMzHkuYvpl7TnmoTmlbTmlbAiKTsNCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgZGVhZGxpbmVicmFuY2godGhpcy5kZWFkbGluZUZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiANCiAgICAgIHsNCiAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLmibnph4/kv67mlLnmiKrmraLml6XmnJ/miJDlip8iKTsNCiAgICAgICAgdGhpcy5kZWFkbGluZU9wZW4gPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLmRlYWRsaW5lT3BlbiA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICBnZXREZXRhaWwoKXsNCiAgICBnZXRSb290TGlzdEJ5SWQoe2lkIDogdGhpcy5yb290SWQscnVsZVR5cGU6dGhpcy5yb290UnVsZVR5cGV9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMuZGV0YWlsID0gcmVzLmRhdGE7DQogICAgICAgIGlmKHRoaXMuZGV0YWlsID09IG51bGwgfHwgdGhpcy5kZXRhaWwgPT0gdW5kZWZpbmVkKXRoaXMuZGV0YWlsID0ge30NCiAgICAgICAgY29uc29sZS5sb2codGhpcy5kZXRhaWwpDQogICAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZUNsb3NlKCl7DQogICAgICB0aGlzLmRyYXdlciA9IGZhbHNlOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgIH0sDQogICAgaGFuZGxlRXhwb3J0KHJvdyl7DQogICAgICB0aGlzLnF1ZXJ5LnJvb3RJZCAgPSByb3cuaWQ7DQogICAgICB0aGlzLnF1ZXJ5LnRpdGxlID0gcm93LmRpbWVuc2lvbmFsaXR5TmFtZTsNCiAgICAgIHRoaXMuY2xpY2tDaGFuZ2VUaW1lKCk7DQogICAgICB0aGlzLmV4cG9ydE9wZW4gPSB0cnVlOw0KICAgIH0sDQoNCiAgICBhZGRDbGljaygpIHsNCiAgICAgIC8vIHRoaXMuZm9ybS5kZXB0SWQ9cGFyc2VJbnQodGhpcy5mb3JtLmRlcHRJZC5zcGxpdCgiLCIpWy0xXSkNCiAgICAgIGFkZERpbWVuc2lvbmFsaXR5KHRoaXMuZm9ybSkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMubmV3T3BlbiA9IGZhbHNlOw0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy5mb3JtPXt9Ow0KICAgICAgfSk7DQogICAgfSwNCiAgICBleHBvcnREYXRhKCkgew0KICAgICAgaWYgKA0KICAgICAgICB0aGlzLnF1ZXJ5LnN0YXJ0RGF0ZSA9PSBudWxsIHx8DQogICAgICAgIHRoaXMucXVlcnkuc3RhcnREYXRlID09ICIifHwNCiAgICAgICAgdGhpcy5xdWVyeS5lbmREYXRlID09IG51bGx8fA0KICAgICAgICB0aGlzLnF1ZXJ5LmVuZERhdGUgPT0gIiINCiAgICAgICkgew0KICAgICAgICB0aGlzLiRub3RpZnkuZXJyb3Ioew0KICAgICAgICAgIHRpdGxlOiAi6ZSZ6K+vIiwNCiAgICAgICAgICBtZXNzYWdlOiAi5a+85Ye65YmN6K+35YWI6L6T5YWl5byA5aeL57uT5p2f5pe26Ze0IiwNCiAgICAgICAgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMucXVlcnkubm90ZVNob3c9dGhpcy5ub3RlU2hvdw0KICAgICAgdGhpcy5kb3dubG9hZEZpbGUoDQogICAgICAgICIvd2ViL1RZankvZGltZW5zaW9uYWxpdHkvZXhwb3J0U3RhdGlzdGljcyIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi50aGlzLnF1ZXJ5LA0KICAgICAgICB9LA0KICAgICAgICAiKCIgKw0KICAgICAgICAgIHRoaXMucXVlcnkuc3RhcnREYXRlICsNCiAgICAgICAgICAiLSIgKw0KICAgICAgICAgIHRoaXMucXVlcnkuZW5kRGF0ZSArDQogICAgICAgICAgIikiICsNCiAgICAgICAgICB0aGlzLnF1ZXJ5LnRpdGxlICsNCiAgICAgICAgICBgLnhsc3hgDQogICAgICApOw0KICAgIH0sDQogICAgZXhwb3J0RGF0YVByZXZpZXcoKQ0KICAgIHsNCiAgICAgIGlmICgNCiAgICAgICAgdGhpcy5xdWVyeS5zdGFydERhdGUgPT0gbnVsbCB8fA0KICAgICAgICB0aGlzLnF1ZXJ5LnN0YXJ0RGF0ZSA9PSAiInx8DQogICAgICAgIHRoaXMucXVlcnkuZW5kRGF0ZSA9PSBudWxsfHwNCiAgICAgICAgdGhpcy5xdWVyeS5lbmREYXRlID09ICIiDQogICAgICApIHsNCiAgICAgICAgdGhpcy4kbm90aWZ5LmVycm9yKHsNCiAgICAgICAgICB0aXRsZTogIumUmeivryIsDQogICAgICAgICAgbWVzc2FnZTogIuWvvOWHuuWJjeivt+WFiOi+k+WFpeW8gOWni+e7k+adn+aXtumXtCIsDQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLnF1ZXJ5Lm5vdGVTaG93PXRoaXMubm90ZVNob3cNCiAgICAgIHRoaXMuZG93bmxvYWRYbHN4KA0KICAgICAgICAiL3dlYi9UWWp5L2RpbWVuc2lvbmFsaXR5L2V4cG9ydFN0YXRpc3RpY3MiLA0KICAgICAgICB7DQogICAgICAgICAgLi4udGhpcy5xdWVyeSwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWUrIigiICt0aGlzLnNwZWNpYWxGY0RhdGUrDQogICAgICAgICAgIikiICsNCiAgICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgICApLnRoZW4oKGJsb2IpID0+IHsNCiAgICAgICAgbGV0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7DQogICAgICAgIHJlYWRlci5yZWFkQXNBcnJheUJ1ZmZlcihibG9iKTsNCiAgICAgICAgcmVhZGVyLm9ubG9hZCA9IChldnQpID0+IHsNCiAgICAgICAgICB0aGlzLmN1c3RvbUJsb2JDb250ZW50PWV2dC50YXJnZXQucmVzdWx0Ow0KDQogICAgICAgICAgbGV0IGludHMgPSBuZXcgVWludDhBcnJheShldnQudGFyZ2V0LnJlc3VsdCk7IC8v6KaB5L2/55So6K+75Y+W55qE5YaF5a6577yM5omA5Lul5bCG6K+75Y+W5YaF5a656L2s5YyW5oiQVWludDhBcnJheQ0KICAgICAgICAgIGludHMgPSBpbnRzLnNsaWNlKDAsIGJsb2Iuc2l6ZSk7DQogICAgICAgICAgbGV0IHdvcmtCb29rID0geGxzeC5yZWFkKGludHMsIHsgdHlwZTogImFycmF5IiB9KTsNCiAgICAgICAgICANCg0KDQogICAgICAgICAgbGV0IHNoZWV0TmFtZXMgPSB3b3JrQm9vay5TaGVldE5hbWVzOw0KICAgICAgICAgIGxldCBzaGVldE5hbWUgPSBzaGVldE5hbWVzWzBdOw0KICAgICAgICAgIGxldCB3b3JrU2hlZXQgPSB3b3JrQm9vay5TaGVldHNbc2hlZXROYW1lXTsNCiAgICAgICAgICAvL+iOt+WPlkV4Y2xl5YaF5a6577yM5bm25bCG56m65YaF5a6555So56m65YC85L+d5a2YDQogICAgICAgICAgbGV0IGV4Y2VsVGFibGUgPSB4bHN4LnV0aWxzLnNoZWV0X3RvX2pzb24od29ya1NoZWV0KTsNCiAgICAgICAgICAvLyDojrflj5ZFeGNlbOWktOmDqA0KICAgICAgICAgIGxldCB0YWJsZVRoZWFkID0gQXJyYXkuZnJvbShPYmplY3Qua2V5cyhleGNlbFRhYmxlWzBdKSkubWFwKA0KICAgICAgICAgICAgKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICApOw0KICAgICAgICAgIHRoaXMuZXhjZWxEYXRhID0gZXhjZWxUYWJsZTsNCiAgICAgICAgICB0aGlzLmV4Y2VsdGl0bGU9dGFibGVUaGVhZA0KICAgICAgICAgIHRoaXMuZXhjZWxIdG1sPSBleGNlbFRhYmxlDQogICAgICAgICAgdGhpcy5zZWFyY2hvcGVuID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBleHBvcnRNb3V0aERhdGFQcmV2aWV3KCl7DQogICAgICBpZiAoDQogICAgICAgIHRoaXMucXVlcnkuc3RhcnREYXRlID09IG51bGwgfHwNCiAgICAgICAgdGhpcy5xdWVyeS5zdGFydERhdGUgPT0gIiJ8fA0KICAgICAgICB0aGlzLnF1ZXJ5LmVuZERhdGUgPT0gbnVsbHx8DQogICAgICAgIHRoaXMucXVlcnkuZW5kRGF0ZSA9PSAiIg0KICAgICAgKSB7DQogICAgICAgIHRoaXMuJG5vdGlmeS5lcnJvcih7DQogICAgICAgICAgdGl0bGU6ICLplJnor68iLA0KICAgICAgICAgIG1lc3NhZ2U6ICLlr7zlh7rliY3or7flhYjovpPlhaXlvIDlp4vnu5PmnZ/ml7bpl7QiLA0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5xdWVyeS5yb290SWQgPSB0aGlzLmRpbWVuc2lvbmFsaXR5SWQNCiAgICAgIHRoaXMucXVlcnkudHlwZT0iMSINCiAgICAgIHRoaXMucXVlcnkuaXNVcGRhdGU9IjEiDQogICAgICBsZXQgcGF0aD0iL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRFdmVyeW1vdXRoIg0KICAgICAgaWYodGhpcy5kaW1lbnNpb25hbGl0eUlkPT0xMDI4KQ0KICAgICAgew0KICAgICAgICBwYXRoPSIvd2ViL1RZankvYW5zd2VyL2V4cG9ydEV2ZXJ5bW91dGgiDQogICAgICB9DQogICAgICBpZih0aGlzLmRpbWVuc2lvbmFsaXR5SWQ9PTc0OCkNCiAgICAgIHsNCiAgICAgICAgcGF0aD0iL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRXaXRoVGVtcGxhdGUxIg0KICAgICAgfQ0KICAgICAgdGhpcy5kb3dubG9hZFhsc3goDQogICAgICAgICAgcGF0aCwNCiAgICAgICAgICB7DQogICAgICAgICAgICAuLi50aGlzLnF1ZXJ5LA0KICAgICAgICAgIH0sDQogICAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWUrIigiICt0aGlzLnNwZWNpYWxGY0RhdGUrDQogICAgICAgICAgICAiKSIgKw0KICAgICAgICAgICAgYOaVsOaNri54bHN4YA0KICAgICAgICApLnRoZW4oKGJsb2IpID0+IHsNCiAgICAgICAgICBsZXQgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTsNCiAgICAgICAgICByZWFkZXIucmVhZEFzQXJyYXlCdWZmZXIoYmxvYik7DQogICAgICAgICAgDQogICAgICAgICAgcmVhZGVyLm9ubG9hZCA9IChldnQpID0+IHsNCiAgICAgICAgICAgIHRoaXMuY3VzdG9tQmxvYkNvbnRlbnQ9ZXZ0LnRhcmdldC5yZXN1bHQ7DQogICAgICAgICAgICAvLyBsZXQgaW50cyA9IG5ldyBVaW50OEFycmF5KGV2dC50YXJnZXQucmVzdWx0KTsgLy/opoHkvb/nlKjor7vlj5bnmoTlhoXlrrnvvIzmiYDku6XlsIbor7vlj5blhoXlrrnovazljJbmiJBVaW50OEFycmF5DQogICAgICAgICAgICAvLyBpbnRzID0gaW50cy5zbGljZSgwLCBibG9iLnNpemUpOw0KICAgICAgICAgICAgLy8gbGV0IHdvcmtCb29rID0geGxzeC5yZWFkKGludHMsIHsgdHlwZTogImFycmF5IiB9KTsNCiAgICAgICAgICAgIC8vIGxldCBzaGVldE5hbWVzID0gd29ya0Jvb2suU2hlZXROYW1lczsNCiAgICAgICAgICAgIC8vIGxldCBzaGVldE5hbWUgPSBzaGVldE5hbWVzWzBdOw0KICAgICAgICAgICAgLy8gbGV0IHdvcmtTaGVldCA9IHdvcmtCb29rLlNoZWV0c1tzaGVldE5hbWVdOw0KICAgICAgICAgICAgLy8gLy/ojrflj5ZFeGNsZeWGheWuue+8jOW5tuWwhuepuuWGheWuueeUqOepuuWAvOS/neWtmA0KICAgICAgICAgICAgLy8gbGV0IGV4Y2VsVGFibGUgPSB4bHN4LnV0aWxzLnNoZWV0X3RvX2pzb24od29ya1NoZWV0KTsNCiAgICAgICAgICAgIC8vIC8vIOiOt+WPlkV4Y2Vs5aS06YOoDQogICAgICAgICAgICAvLyBsZXQgdGFibGVUaGVhZCA9IEFycmF5LmZyb20oT2JqZWN0LmtleXMoZXhjZWxUYWJsZVswXSkpLm1hcCgNCiAgICAgICAgICAgIC8vICAgKGl0ZW0pID0+IHsNCiAgICAgICAgICAgIC8vICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgLy8gICB9DQogICAgICAgICAgICAvLyApOw0KICAgICAgICAgICAgLy8gdGhpcy5leGNlbERhdGEgPSBleGNlbFRhYmxlOw0KICAgICAgICAgICAgLy8gdGhpcy5leGNlbHRpdGxlPXRhYmxlVGhlYWQNCiAgICAgICAgICAgIC8vIHRoaXMuZXhjZWxIdG1sPSBleGNlbFRhYmxlDQogICAgICAgICAgICB0aGlzLnNlYXJjaG9wZW4gPSB0cnVlOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgfSwNCiAgICBleHBvcnRNb3V0aERhdGEoKXsNCiAgICAgIGlmICgNCiAgICAgICAgdGhpcy5xdWVyeS5zdGFydERhdGUgPT0gbnVsbCB8fA0KICAgICAgICB0aGlzLnF1ZXJ5LnN0YXJ0RGF0ZSA9PSAiInx8DQogICAgICAgIHRoaXMucXVlcnkuZW5kRGF0ZSA9PSBudWxsfHwNCiAgICAgICAgdGhpcy5xdWVyeS5lbmREYXRlID09ICIiDQogICAgICApIHsNCiAgICAgICAgdGhpcy4kbm90aWZ5LmVycm9yKHsNCiAgICAgICAgICB0aXRsZTogIumUmeivryIsDQogICAgICAgICAgbWVzc2FnZTogIuWvvOWHuuWJjeivt+WFiOi+k+WFpeW8gOWni+e7k+adn+aXtumXtCIsDQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLnF1ZXJ5LnJvb3RJZCA9IHRoaXMuZGltZW5zaW9uYWxpdHlJZA0KICAgICAgdGhpcy5xdWVyeS50eXBlPSIxIg0KICAgICAgLy8gdGhpcy5xdWVyeS5pc1VwZGF0ZT0iMSINCiAgICAgIGxldCBwYXRoPSIvd2ViL1RZankvYW5zd2VyL2V4cG9ydEV2ZXJ5bW91dGgiDQogICAgICBpZih0aGlzLmRpbWVuc2lvbmFsaXR5SWQ9PTEwMjgpDQogICAgICB7DQogICAgICAgIHBhdGg9Ii93ZWIvVFlqeS9hbnN3ZXIvZXhwb3J0RXZlcnltb3V0aCINCiAgICAgIH0NCiAgICAgIGlmKHRoaXMuZGltZW5zaW9uYWxpdHlJZD09NzQ4KQ0KICAgICAgew0KICAgICAgICBwYXRoPSIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFdpdGhUZW1wbGF0ZTEiDQogICAgICB9DQogICAgICB0aGlzLmRvd25sb2FkRmlsZSgNCiAgICAgICAgcGF0aCwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnRoaXMucXVlcnksDQogICAgICAgIH0sDQogICAgICAgICIoIiArDQogICAgICAgICAgdGhpcy5xdWVyeS5zdGFydERhdGUgKw0KICAgICAgICAgICItIiArDQogICAgICAgICAgdGhpcy5xdWVyeS5lbmREYXRlICsNCiAgICAgICAgICAiKSIgKw0KICAgICAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lICsNCiAgICAgICAgICBgLnhsc3hgDQogICAgICApOw0KICAgIH0sDQogICAgb25EYXRlQ2hhbmdlKCl7DQogICAgICBjb25zb2xlLmxvZyh0aGlzLmRhdGVWYWx1ZSkNCiAgICAgIGlmKHRoaXMuZGF0ZVZhbHVlICE9IG51bGwgJiYgdGhpcy5kYXRlVmFsdWUgIT0gIiIpew0KICAgICAgICB0aGlzLnF1ZXJ5LnN0YXJ0RGF0ZSA9IHRoaXMuZGF0ZVZhbHVlWzBdIDsNCiAgICAgICAgdGhpcy5xdWVyeS5lbmREYXRlID0gdGhpcy5kYXRlVmFsdWVbMV07DQogICAgICB9ZWxzZXsNCiAgICAgICAgdGhpcy5xdWVyeS5zdGFydERhdGUgPSAiIjsNCiAgICAgICAgdGhpcy5xdWVyeS5lbmREYXRlID0gIiI7DQogICAgICB9DQogICAgfSwNCiAgICB0b1VwZGF0ZVVzZXJzKHJvdyl7DQogICAgICBjb25zdCBkaW1lbnNpb25hbGl0eUlkID0gcm93LmlkOw0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9kYXRhUmVwb3J0L2RpbWVuc2lvbmFsaXR5LWF1dGgvZGltZW5zaW9uYWxpdHlQZXJtaXNzaW9uLyIgKyBkaW1lbnNpb25hbGl0eUlkKTsNCiAgICAgIC8vIHRoaXMuJHJvdXRlci5nbygwKQ0KICAgIH0sDQogICAgaGFuZGxlRGF0ZUNoYW5nZSgpIHsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgaGFuZGxlYUFkbWluTGlzdChyb3cpew0KDQogICAgICBjb25zdCBkaW1lbnNpb25hbGl0eUlkID0gcm93LmlkOw0KICAgICAgbGlzdFBlcm1pc3Npb24oe2RpbWVuc2lvbmFsaXR5SWQ6ZGltZW5zaW9uYWxpdHlJZH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMudXNlckxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICAvLyB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIC8vIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLmFkbWluT3BlbiA9IHRydWU7DQogICAgICB9KTsNCg0KICAgICAgLy8gY29uc3QgZmNEYXRlID0gdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGU7DQogICAgICAvLyBjb25zdCBkaW1lbnNpb25hbGl0eU5hbWUgPSByb3cuZGltZW5zaW9uYWxpdHlOYW1lOw0KICAgICAgLy8gdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAnL2RhdGFSZXBvcnQvYWRtaW5maWxsLWF1dGgvYWRtaW5maWxsc3RhdHVzJywgcXVlcnk6IHsgZGltZW5zaW9uYWxpdHlJZDpkaW1lbnNpb25hbGl0eUlkLCBmY0RhdGU6ZmNEYXRlLGRpbWVuc2lvbmFsaXR5TmFtZTpkaW1lbnNpb25hbGl0eU5hbWV9IH0pOw0KICAgIH0sDQogICAgaGFuZGxlZmlsbChyb3cpew0KICAgICAgLy8gY29uc3QgZGltZW5zaW9uYWxpdHlJZCA9IHJvdy5pZDsNCiAgICAgIC8vIHRoaXMuJHJvdXRlci5wdXNoKCIvZGF0YVJlcG9ydC9hZG1pbmZpbGwtYXV0aC9hZG1pbmZpbGxzdGF0dXMvIiArIGRpbWVuc2lvbmFsaXR5SWQpOw0KICAgICAgY29uc3QgZGltZW5zaW9uYWxpdHlJZCA9IHJvdy5pZDsNCiAgICAgIGNvbnN0IGZjRGF0ZSA9IHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlOw0KICAgICAgY29uc3QgZGltZW5zaW9uYWxpdHlOYW1lID0gcm93LmRpbWVuc2lvbmFsaXR5TmFtZTsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJy9kYXRhUmVwb3J0L2FkbWluZmlsbC1hdXRoL2FkbWluZmlsbHN0YXR1cy8nKyBkaW1lbnNpb25hbGl0eUlkLCBxdWVyeTogeyBkaW1lbnNpb25hbGl0eUlkOmRpbWVuc2lvbmFsaXR5SWQsIGZjRGF0ZTpmY0RhdGUsZGltZW5zaW9uYWxpdHlOYW1lOmRpbWVuc2lvbmFsaXR5TmFtZX0gfSk7DQogICAgfSwNCiAgICBoYW5kbGVBbnN3ZXIocm93KXsNCiAgICAgIC8vIGNvbnN0IGRpbWVuc2lvbmFsaXR5SWQgPSByb3cuaWQ7DQogICAgICAvLyB0aGlzLiRyb3V0ZXIucHVzaCgiL2RhdGFSZXBvcnQvYWRtaW5maWxsLWF1dGgvYWRtaW5maWxsc3RhdHVzLyIgKyBkaW1lbnNpb25hbGl0eUlkKTsNCiAgICAgIGNvbnN0IGRpbWVuc2lvbmFsaXR5SWQgPSByb3cuaWQ7DQogICAgICBjb25zdCBmY0RhdGUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmZjRGF0ZTsNCiAgICAgIGNvbnN0IGRpbWVuc2lvbmFsaXR5TmFtZT0gcm93LmRpbWVuc2lvbmFsaXR5TmFtZTsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJy9kYXRhUmVwb3J0L2Fuc3dlclNob3ctYXV0aC9hbnN3ZXJTaG93LycrIGRpbWVuc2lvbmFsaXR5SWQsIHF1ZXJ5OiB7IGRpbWVuc2lvbmFsaXR5SWQ6ZGltZW5zaW9uYWxpdHlJZCwgZmNEYXRlOmZjRGF0ZSxkaW1lbnNpb25hbGl0eU5hbWU6ZGltZW5zaW9uYWxpdHlOYW1lfSB9KTsNCiAgICB9LA0KDQogIGhhbmRsZVNwZWNpYWwocm93KXsNCiAgICAgIC8vIHRoaXMucXVlcnkucm9vdElkICA9IHJvdy5pZDsNCiAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lID0gcm93LmRpbWVuc2lvbmFsaXR5TmFtZTsNCiAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlJZD1yb3cuaWQ7DQogICAgICB0aGlzLlNwZWNpYWxJbXBvcnRPcGVuID0gdHJ1ZTsNCiAgICAgIA0KICAgIH0sDQogIGhhbmRsZU1vdXRoKHJvdyl7DQogICAgICAvLyB0aGlzLnF1ZXJ5LnJvb3RJZCAgPSByb3cuaWQ7DQogICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZSA9IHJvdy5kaW1lbnNpb25hbGl0eU5hbWU7DQogICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5SWQ9cm93LmlkOw0KICAgICAgdGhpcy5jbGlja0NoYW5nZVRpbWUoKTsNCiAgICAgIHRoaXMubW91dGhJbXBvcnRPcGVuID0gdHJ1ZTsNCiAgICB9LA0KICBkb3dubG9hZFRlbXBsYXRlU3BlY2lhbFByZXZpZXcoKXsNCiAgICBpZiAodGhpcy5zcGVjaWFsRmNEYXRlID09IG51bGwgKSB7DQogICAgICAgIHRoaXMuc3BlY2lhbEZjRGF0ZT0gdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGUNCiAgICAgIH0NCg0KICAgICAgLy8gaWYgKHRoaXMuc3BlY2lhbEZjRGF0ZSA9PSBudWxsICkgew0KICAgICAgLy8gICB0aGlzLiRub3RpZnkuZXJyb3Ioew0KICAgICAgLy8gICAgIHRpdGxlOiAi6ZSZ6K+vIiwNCiAgICAgIC8vICAgICBtZXNzYWdlOiAi5pyq6YCJ5oup5pe26Ze0IiwNCiAgICAgIC8vICAgfSk7DQogICAgICAvLyAgIHJldHVybjsNCiAgICAgIC8vIH0NCiAgICAgIGxldCBxdWVyeUltcG9ydD17fQ0KICAgICAgcXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICBxdWVyeUltcG9ydC5mY0RhdGUgPSB0aGlzLnNwZWNpYWxGY0RhdGUNCiAgICAgIHF1ZXJ5SW1wb3J0LnR5cGU9IjEiDQogICAgICBxdWVyeUltcG9ydC5pc1VwZGF0ZT0iMSINCiAgICAgIGxldCB1cmw9IiINCiAgICAgIGlmKHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lPT0n56CU56m26Zmi55uu5qCH5oyH5qCH5LiA6KeIJykNCiAgICAgIHsNCiAgICAgICAgdXJsPSIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFdpdGhUZW1wbGF0ZSINCiAgICAgIH0NCiAgICAgIGVsc2UNCiAgICAgIHsNCiAgICAgICAgdXJsPSIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFRlbXBsYXRlU3BlY2lhbCINCiAgICAgIH0NCiAgICAgIHRoaXMuZG93bmxvYWRYbHN4KA0KICAgICAgICAgIHVybCwNCiAgICAgICAgICB7DQogICAgICAgICAgICAuLi5xdWVyeUltcG9ydCwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lKyIoIiArdGhpcy5zcGVjaWFsRmNEYXRlKw0KICAgICAgICAgICAgIikiICsNCiAgICAgICAgICAgIGDmlbDmja4ueGxzeGANCiAgICAgICAgKS50aGVuKChibG9iKSA9PiB7DQogICAgICAgICAgbGV0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7DQogICAgICAgICAgcmVhZGVyLnJlYWRBc0FycmF5QnVmZmVyKGJsb2IpOw0KICAgICAgICAgIA0KICAgICAgICAgIHJlYWRlci5vbmxvYWQgPSAoZXZ0KSA9PiB7DQogICAgICAgICAgICB0aGlzLmN1c3RvbUJsb2JDb250ZW50PXJlYWRlci5yZXN1bHQ7DQogICAgICAgICAgICBsZXQgaW50cyA9IG5ldyBVaW50OEFycmF5KGV2dC50YXJnZXQucmVzdWx0KTsgLy/opoHkvb/nlKjor7vlj5bnmoTlhoXlrrnvvIzmiYDku6XlsIbor7vlj5blhoXlrrnovazljJbmiJBVaW50OEFycmF5DQogICAgICAgICAgICBpbnRzID0gaW50cy5zbGljZSgwLCBibG9iLnNpemUpOw0KICAgICAgICAgICAgbGV0IHdvcmtCb29rID0geGxzeC5yZWFkKGludHMsIHsgdHlwZTogImFycmF5IiB9KTsNCiAgICAgICAgICAgIGxldCBzaGVldE5hbWVzID0gd29ya0Jvb2suU2hlZXROYW1lczsNCiAgICAgICAgICAgIGxldCBzaGVldE5hbWUgPSBzaGVldE5hbWVzWzBdOw0KICAgICAgICAgICAgbGV0IHdvcmtTaGVldCA9IHdvcmtCb29rLlNoZWV0c1tzaGVldE5hbWVdOw0KICAgICAgICAgICAgLy/ojrflj5ZFeGNsZeWGheWuue+8jOW5tuWwhuepuuWGheWuueeUqOepuuWAvOS/neWtmA0KICAgICAgICAgICAgbGV0IGV4Y2VsVGFibGUgPSB4bHN4LnV0aWxzLnNoZWV0X3RvX2pzb24od29ya1NoZWV0KTsNCiAgICAgICAgICAgIC8vIOiOt+WPlkV4Y2Vs5aS06YOoDQogICAgICAgICAgICBsZXQgdGFibGVUaGVhZCA9IEFycmF5LmZyb20oT2JqZWN0LmtleXMoZXhjZWxUYWJsZVswXSkpLm1hcCgNCiAgICAgICAgICAgICAgKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICApOw0KICAgICAgICAgICAgdGhpcy5leGNlbERhdGEgPSBleGNlbFRhYmxlOw0KICAgICAgICAgICAgdGhpcy5leGNlbHRpdGxlPXRhYmxlVGhlYWQNCiAgICAgICAgICAgIHRoaXMuZXhjZWxIdG1sPSBleGNlbFRhYmxlDQogICAgICAgICAgICB0aGlzLnNlYXJjaG9wZW4gPSB0cnVlOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogIH0sDQogIGRvd25sb2FkVGVtcGxhdGVTcGVjaWFsKCl7DQogICAgICBpZiAodGhpcy5zcGVjaWFsRmNEYXRlID09IG51bGwgKSB7DQogICAgICAgIHRoaXMuc3BlY2lhbEZjRGF0ZT0gdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGUNCiAgICAgIH0NCg0KICAgICAgLy8gaWYgKHRoaXMuc3BlY2lhbEZjRGF0ZSA9PSBudWxsICkgew0KICAgICAgLy8gICB0aGlzLiRub3RpZnkuZXJyb3Ioew0KICAgICAgLy8gICAgIHRpdGxlOiAi6ZSZ6K+vIiwNCiAgICAgIC8vICAgICBtZXNzYWdlOiAi5pyq6YCJ5oup5pe26Ze0IiwNCiAgICAgIC8vICAgfSk7DQogICAgICAvLyAgIHJldHVybjsNCiAgICAgIC8vIH0NCiAgICAgIGxldCBxdWVyeUltcG9ydD17fQ0KICAgICAgcXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICBxdWVyeUltcG9ydC5mY0RhdGUgPSB0aGlzLnNwZWNpYWxGY0RhdGUNCiAgICAgIHF1ZXJ5SW1wb3J0LnR5cGU9IjEiDQogICAgICBsZXQgdXJsPSIiDQogICAgICBpZih0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZT09J+eglOeptumZouebruagh+aMh+agh+S4gOiniCcpDQogICAgICB7DQogICAgICAgIHVybD0iL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRXaXRoVGVtcGxhdGUiDQogICAgICB9DQogICAgICBlbHNlDQogICAgICB7DQogICAgICAgIHVybD0iL3dlYi9UWWp5L2Fuc3dlci9leHBvcnRUZW1wbGF0ZVNwZWNpYWwiDQogICAgICB9DQogICAgICB0aGlzLmRvd25sb2FkRmlsZSgNCiAgICAgICAgdXJsLA0KICAgICAgICB7DQogICAgICAgICAgLi4ucXVlcnlJbXBvcnQsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lKyIoIiArdGhpcy5zcGVjaWFsRmNEYXRlKw0KICAgICAgICAgICIpIiArDQogICAgICAgICAgYOaVsOaNri54bHN4YA0KICAgICAgKTsNCiAgICB9LA0KDQogICAgLy/mraTlpITkuLrmjInpkq7liKTmlq3nrqHnkIYNCiAgICBjb250YWluc1N1YnN0cmluZyhzdWJzdHJpbmcsIHN0cmluZykgDQogICAgew0KICAgICAgcmV0dXJuIHN0cmluZy5pbmNsdWRlcyhzdWJzdHJpbmcpOw0KICAgIH0sDQoNCiAgICBiZXNwb2tlTGlzdChzdHJpbmcpDQogICAgew0KICAgICAgaWYoc3RyaW5nPT0gJzIwMjXlubTkuJPliKnmnIjmiqUnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICflt6Xoo4XkuIrnur/pqozmlLblkIjmoLznjofnu5/orqEnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIC8vIGlmKHN0cmluZz09ICcyMDI15bm057uP5rWO6LSj5Lu75Yi25oqA57uP5oyH5qCHJykNCiAgICAgIC8vIHsNCiAgICAgIC8vICAgcmV0dXJuIHRydWU7DQogICAgICAvLyB9DQogICAgICAvLyBpZihzdHJpbmc9PSAn56CU56m26Zmi5oqA57uP5o+Q5Y2H5oyH5qCH6Lef6LiqJykNCiAgICAgIC8vIHsNCiAgICAgIC8vICAgcmV0dXJuIHRydWU7DQogICAgICAvLyB9DQogICAgICByZXR1cm4gZmFsc2U7DQogICAgfSwNCiAgICAvL+aUr+aMgemihOiniOWSjOWvvOWHug0KICAgIG1vdXRoQ2hlY2soc3RyaW5nKQ0KICAgIHsNCiAgICAgIGlmKHN0cmluZz09ICflha3ljJbmjIfmoIcnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICflt6Xoo4XkuIrnur/pqozmlLblkIjmoLznjofnu5/orqEnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICcyMDI15bm057uP5rWO6LSj5Lu75Yi25oqA57uP5oyH5qCHJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn56CU56m26Zmi5oqA57uP5o+Q5Y2H5oyH5qCH6Lef6LiqJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICByZXR1cm4gZmFsc2U7DQogICAgfSwNCiAgICAvL+aUr+aMgeWNleWRqOacn+WvvOWHug0KICAgIGFsb25lTGlzdChzdHJpbmcpIHsNCiAgICAgIGlmKHN0cmluZz09ICfmsJTkvZPnu5PnrpfmnIjmiqUnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICfpq5jngonjgIHovazngonnhaTmsJTmnIjmiqXooagnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICflpKnnhLbmsJTmtojogJfmnIjmiqXooagnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICfokrjmsb3mtojogJfmnIjmiqXooagnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICfnlLXph4/mnIjmiqXooagnKQ0KICAgICAgew0KICAgICAgICByZXR1cm4gdHJ1ZTsNCiAgICAgIH0NCiAgICAgIGlmKHN0cmluZz09ICcyMDI15bm057uP5rWO6LSj5Lu75Yi26ICD5qC46KGo77yI54m55p2/5LqL5Lia6YOo77yJJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn56CU56m26Zmi55uu5qCH5oyH5qCH5LiA6KeIJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn5a6J5YWo6LSj5Lu75bel6LWE6ICD5qC46KGoJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn5a6J5YWo6LSj5Lu75bel6LWE6ICD5qC45rGH5oC7JykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn5rC05aSE55CG5rC06YeP5oql6KGoJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICByZXR1cm4gZmFsc2U7DQogICAgfSwNCg0KICAgIC8v5pWw5o2u6aKE6KeI5qih5Z2X5aSE55CGDQogICAgaGFuZGxlUHJldmlldygpIHsNCiAgICAgIGxldCBxdWVyeUltcG9ydD17fQ0KICAgICAgcXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICBxdWVyeUltcG9ydC5mY0RhdGUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmZjRGF0ZQ0KICAgICAgcXVlcnlJbXBvcnQudHlwZT0iMSINCiAgICAgIHF1ZXJ5SW1wb3J0LmlzVXBkYXRlPSIxIg0KICAgICAgaWYodGhpcy5kaW1lbnNpb25hbGl0eU5hbWU9PSfnoJTnqbbpmaLnm67moIfmjIfmoIfkuIDop4gnKQ0KICAgICAgew0KICAgICAgICAgIHRoaXMuZG93bmxvYWRYbHN4KA0KICAgICAgICAgICIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFdpdGhUZW1wbGF0ZSIsDQogICAgICAgICAgew0KICAgICAgICAgICAgLi4ucXVlcnlJbXBvcnQsDQogICAgICAgICAgfSwNCiAgICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZSsiKCIgK3RoaXMuc3BlY2lhbEZjRGF0ZSsNCiAgICAgICAgICAgICIpIiArDQogICAgICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgICAgICkudGhlbigoYmxvYikgPT4gew0KICAgICAgICAgIGxldCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpOw0KICAgICAgICAgIHJlYWRlci5yZWFkQXNBcnJheUJ1ZmZlcihibG9iKTsNCiAgICAgICAgICANCiAgICAgICAgICByZWFkZXIub25sb2FkID0gKGV2dCkgPT4gew0KICAgICAgICAgICAgdGhpcy5jdXN0b21CbG9iQ29udGVudD1yZWFkZXIucmVzdWx0Ow0KICAgICAgICAgICAgbGV0IGludHMgPSBuZXcgVWludDhBcnJheShldnQudGFyZ2V0LnJlc3VsdCk7IC8v6KaB5L2/55So6K+75Y+W55qE5YaF5a6577yM5omA5Lul5bCG6K+75Y+W5YaF5a656L2s5YyW5oiQVWludDhBcnJheQ0KICAgICAgICAgICAgaW50cyA9IGludHMuc2xpY2UoMCwgYmxvYi5zaXplKTsNCiAgICAgICAgICAgIGxldCB3b3JrQm9vayA9IHhsc3gucmVhZChpbnRzLCB7IHR5cGU6ICJhcnJheSIgfSk7DQogICAgICAgICAgICBsZXQgc2hlZXROYW1lcyA9IHdvcmtCb29rLlNoZWV0TmFtZXM7DQogICAgICAgICAgICBsZXQgc2hlZXROYW1lID0gc2hlZXROYW1lc1swXTsNCiAgICAgICAgICAgIGxldCB3b3JrU2hlZXQgPSB3b3JrQm9vay5TaGVldHNbc2hlZXROYW1lXTsNCiAgICAgICAgICAgIC8v6I635Y+WRXhjbGXlhoXlrrnvvIzlubblsIbnqbrlhoXlrrnnlKjnqbrlgLzkv53lrZgNCiAgICAgICAgICAgIGxldCBleGNlbFRhYmxlID0geGxzeC51dGlscy5zaGVldF90b19qc29uKHdvcmtTaGVldCk7DQogICAgICAgICAgICAvLyDojrflj5ZFeGNlbOWktOmDqA0KICAgICAgICAgICAgbGV0IHRhYmxlVGhlYWQgPSBBcnJheS5mcm9tKE9iamVjdC5rZXlzKGV4Y2VsVGFibGVbMF0pKS5tYXAoDQogICAgICAgICAgICAgIChpdGVtKSA9PiB7DQogICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIHRoaXMuZXhjZWxEYXRhID0gZXhjZWxUYWJsZTsNCiAgICAgICAgICAgIHRoaXMuZXhjZWx0aXRsZT10YWJsZVRoZWFkDQogICAgICAgICAgICB0aGlzLmV4Y2VsSHRtbD0gZXhjZWxUYWJsZQ0KICAgICAgICAgICAgdGhpcy5zZWFyY2hvcGVuID0gdHJ1ZTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgZWxzZQ0KICAgICAgew0KICAgICAgICB0aGlzLmRvd25sb2FkWGxzeCgNCiAgICAgICAgIi93ZWIvVFlqeS9hbnN3ZXIvZXhwb3J0VGVtcGxhdGVTcGVjaWFsIiwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnF1ZXJ5SW1wb3J0LA0KICAgICAgICB9LA0KICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZSsiKCIgK3RoaXMuc3BlY2lhbEZjRGF0ZSsNCiAgICAgICAgICAiKSIgKw0KICAgICAgICAgIGDmlbDmja4ueGxzeGANCiAgICAgICkudGhlbigoYmxvYikgPT4gew0KICAgICAgICBsZXQgcmVhZGVyID0gbmV3IEZpbGVSZWFkZXIoKTsNCiAgICAgICAgcmVhZGVyLnJlYWRBc0FycmF5QnVmZmVyKGJsb2IpOw0KICAgICAgICByZWFkZXIub25sb2FkID0gKGV2dCkgPT4gew0KICAgICAgICAgIHRoaXMuY3VzdG9tQmxvYkNvbnRlbnQ9cmVhZGVyLnJlc3VsdDsNCiAgICAgICAgICBsZXQgaW50cyA9IG5ldyBVaW50OEFycmF5KGV2dC50YXJnZXQucmVzdWx0KTsgLy/opoHkvb/nlKjor7vlj5bnmoTlhoXlrrnvvIzmiYDku6XlsIbor7vlj5blhoXlrrnovazljJbmiJBVaW50OEFycmF5DQogICAgICAgICAgaW50cyA9IGludHMuc2xpY2UoMCwgYmxvYi5zaXplKTsNCiAgICAgICAgICBsZXQgd29ya0Jvb2sgPSB4bHN4LnJlYWQoaW50cywgeyB0eXBlOiAiYXJyYXkiIH0pOw0KICAgICAgICAgIGxldCBzaGVldE5hbWVzID0gd29ya0Jvb2suU2hlZXROYW1lczsNCiAgICAgICAgICBsZXQgc2hlZXROYW1lID0gc2hlZXROYW1lc1swXTsNCiAgICAgICAgICBsZXQgd29ya1NoZWV0ID0gd29ya0Jvb2suU2hlZXRzW3NoZWV0TmFtZV07DQogICAgICAgICAgLy/ojrflj5ZFeGNsZeWGheWuue+8jOW5tuWwhuepuuWGheWuueeUqOepuuWAvOS/neWtmA0KICAgICAgICAgIGxldCBleGNlbFRhYmxlID0geGxzeC51dGlscy5zaGVldF90b19qc29uKHdvcmtTaGVldCk7DQogICAgICAgICAgLy8g6I635Y+WRXhjZWzlpLTpg6gNCiAgICAgICAgICBsZXQgdGFibGVUaGVhZCA9IEFycmF5LmZyb20oT2JqZWN0LmtleXMoZXhjZWxUYWJsZVswXSkpLm1hcCgNCiAgICAgICAgICAgIChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICB9DQogICAgICAgICAgKTsNCiAgICAgICAgICB0aGlzLmV4Y2VsRGF0YSA9IGV4Y2VsVGFibGU7DQogICAgICAgICAgdGhpcy5leGNlbHRpdGxlPXRhYmxlVGhlYWQNCiAgICAgICAgICB0aGlzLmV4Y2VsSHRtbD0gZXhjZWxUYWJsZQ0KICAgICAgICAgIHRoaXMuc2VhcmNob3BlbiA9IHRydWU7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5pe26Ze05q616aKE6KeIDQogICAgaGFuZGxlUHJldmlldzEoKSB7DQogICAgICAvLyBsZXQgcXVlcnlJbXBvcnQ9e30NCiAgICAgIC8vIHF1ZXJ5SW1wb3J0LnJvb3RJZCA9IHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZA0KICAgICAgLy8gcXVlcnlJbXBvcnQuZmNEYXRlID0gdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGUNCiAgICAgIC8vIHF1ZXJ5SW1wb3J0LnR5cGU9IjEiDQogICAgICAgIGlmICgNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5zdGFydERhdGUgPT0gbnVsbCB8fA0KICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnN0YXJ0RGF0ZSA9PSAiInx8DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuZW5kRGF0ZSA9PSBudWxsfHwNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5lbmREYXRlID09ICIiDQogICAgICApIHsNCiAgICAgICAgdGhpcy4kbm90aWZ5LmVycm9yKHsNCiAgICAgICAgICB0aXRsZTogIumUmeivryIsDQogICAgICAgICAgbWVzc2FnZTogIuWvvOWHuuWJjeivt+WFiOi+k+WFpeW8gOWni+e7k+adn+aXtumXtCIsDQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnJvb3RJZCA9IHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZA0KICAgICAgdGhpcy5xdWVyeUltcG9ydC50eXBlPSIxIg0KICAgICAgdGhpcy5kb3dubG9hZFhsc3goDQogICAgICAgICIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFRlbXBsYXRlTm9tcmFsIiwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnRoaXMucXVlcnlJbXBvcnQsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lKyIoIiArdGhpcy5zcGVjaWFsRmNEYXRlKw0KICAgICAgICAgICIpIiArDQogICAgICAgICAgYOaVsOaNri54bHN4YA0KICAgICAgKS50aGVuKChibG9iKSA9PiB7DQogICAgICAgIGxldCByZWFkZXIgPSBuZXcgRmlsZVJlYWRlcigpOw0KICAgICAgICByZWFkZXIucmVhZEFzQXJyYXlCdWZmZXIoYmxvYik7DQogICAgICAgIA0KICAgICAgICByZWFkZXIub25sb2FkID0gKGV2dCkgPT4gew0KICAgICAgICAgIHRoaXMuY3VzdG9tQmxvYkNvbnRlbnQ9cmVhZGVyLnJlc3VsdDsNCiAgICAgICAgICBsZXQgaW50cyA9IG5ldyBVaW50OEFycmF5KGV2dC50YXJnZXQucmVzdWx0KTsgLy/opoHkvb/nlKjor7vlj5bnmoTlhoXlrrnvvIzmiYDku6XlsIbor7vlj5blhoXlrrnovazljJbmiJBVaW50OEFycmF5DQogICAgICAgICAgaW50cyA9IGludHMuc2xpY2UoMCwgYmxvYi5zaXplKTsNCiAgICAgICAgICBsZXQgd29ya0Jvb2sgPSB4bHN4LnJlYWQoaW50cywgeyB0eXBlOiAiYXJyYXkiIH0pOw0KICAgICAgICAgIGxldCBzaGVldE5hbWVzID0gd29ya0Jvb2suU2hlZXROYW1lczsNCiAgICAgICAgICBsZXQgc2hlZXROYW1lID0gc2hlZXROYW1lc1swXTsNCiAgICAgICAgICBsZXQgd29ya1NoZWV0ID0gd29ya0Jvb2suU2hlZXRzW3NoZWV0TmFtZV07DQogICAgICAgICAgLy/ojrflj5ZFeGNsZeWGheWuue+8jOW5tuWwhuepuuWGheWuueeUqOepuuWAvOS/neWtmA0KICAgICAgICAgIGxldCBleGNlbFRhYmxlID0geGxzeC51dGlscy5zaGVldF90b19qc29uKHdvcmtTaGVldCk7DQogICAgICAgICAgLy8g6I635Y+WRXhjZWzlpLTpg6gNCiAgICAgICAgICBsZXQgdGFibGVUaGVhZCA9IEFycmF5LmZyb20oT2JqZWN0LmtleXMoZXhjZWxUYWJsZVswXSkpLm1hcCgNCiAgICAgICAgICAgIChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICB9DQogICAgICAgICAgKTsNCiAgICAgICAgICB0aGlzLmV4Y2VsRGF0YSA9IGV4Y2VsVGFibGU7DQogICAgICAgICAgdGhpcy5leGNlbHRpdGxlPXRhYmxlVGhlYWQNCiAgICAgICAgICB0aGlzLmV4Y2VsSHRtbD0gZXhjZWxUYWJsZQ0KICAgICAgICAgIHRoaXMuc2VhcmNob3BlbiA9IHRydWU7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBjbGlja0NoYW5nZVRpbWUoKQ0KICAgIHsNCiAgICAgICAgIGxldCBub3cgPW5ldyBEYXRlKCk7DQogICAgICAgICB0aGlzLnF1ZXJ5LnN0YXJ0RGF0ZT10aGlzLmdldEZpcnN0T2ZZZWFyKG5vdyk7DQogICAgICAgICB0aGlzLnF1ZXJ5LmVuZERhdGU9dGhpcy5nZXRGaXJzdE9mTW9udGgobm93KTsNCiAgICAgICAgIHRoaXMuZGF0ZVZhbHVlPVtdOw0KICAgICAgICAgdGhpcy5kYXRlVmFsdWUucHVzaCh0aGlzLnF1ZXJ5LnN0YXJ0RGF0ZSk7DQogICAgICAgICB0aGlzLmRhdGVWYWx1ZS5wdXNoKHRoaXMucXVlcnkuZW5kRGF0ZSk7DQogICAgfSwNCiAgICAvLyDojrflj5bml7bpl7TnmoTkvJjljJblpITnkIYNCiAgICBnZXRGaXJzdE9mWWVhcihub3cpDQogICAgew0KICAgICAgbGV0ICBmaXJzdERheU9mWWVhciA9IG5ldyBEYXRlKG5vdy5nZXRGdWxsWWVhcigpLCAwLCAxKTsNCiAgICAgIHJldHVybiB0aGlzLmZvcm1hdERhdGUoZmlyc3REYXlPZlllYXIpOw0KICAgIH0sDQogICAgZ2V0Rmlyc3RPZk1vbnRoKG5vdykNCiAgICB7DQogICAgICBsZXQgZmlyc3REYXlPZk1vbnRoID0gbmV3IERhdGUobm93LmdldEZ1bGxZZWFyKCksIG5vdy5nZXRNb250aCgpLCAxKTsNCiAgICAgIHJldHVybiB0aGlzLmZvcm1hdERhdGUoZmlyc3REYXlPZk1vbnRoKTsNCiAgICB9LA0KICAgIC8vIOaXpeacn+agvOW8j+WMluWHveaVsO+8iOi9rOS4uiB5eXl5LU1NLWRk77yJDQogICAgZm9ybWF0RGF0ZShkYXRlKSANCiAgICB7DQogICAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3QgbW9udGggPSBTdHJpbmcoZGF0ZS5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsgLy8g5pyI5Lu95LuOMOW8gOWni+mcgCsxDQogICAgICBjb25zdCBkYXkgPSBTdHJpbmcoZGF0ZS5nZXREYXRlKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICByZXR1cm4gYCR7eWVhcn0tJHttb250aH0tJHtkYXl9YDsNCiAgICB9LA0KDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["admin.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2ZA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "admin.vue", "sourceRoot": "src/views/dataReport/form", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      label-width=\"68px\"\r\n    >\r\n      <el-form-item label=\"扎口部门\" prop=\"deptCode\">\r\n        <el-cascader\r\n          ref=\"cascaderHandle\"\r\n          :options=\"deptList\"\r\n          clearable\r\n          filterable\r\n          v-model=\"queryParams.deptCode\"\r\n          :props=\"{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }\"\r\n          :show-all-levels=\"false\"\r\n          @change=\"handleQueryDept\"\r\n        >\r\n        <span\r\n              slot-scope=\"{ node, data }\"\r\n              style=\"margin-left: -10px; padding-left: 10px; display: block\"\r\n              @click=\"clickNode($event, node)\"\r\n              >{{ data.label }}</span\r\n            >\r\n        </el-cascader>\r\n      </el-form-item>\r\n      <el-form-item label=\"报表名称\" prop=\"dimensionalityName\">\r\n        <el-input\r\n          v-model=\"queryParams.dimensionalityName\"\r\n          placeholder=\"请输入名称\"\r\n          clearable\r\n          size=\"small\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"填报时间\">\r\n                <el-date-picker\r\n                  v-model=\"queryParams.fcDate\"\r\n                  value-format=\"yyyy-MM-dd\"\r\n                  type=\"date\"\r\n                  @change=\"handleDateChange\"\r\n                  placeholder=\"选择日期\">\r\n                </el-date-picker>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"是否在用启用\" prop=\"isUse\">\r\n        <el-select v-model=\"queryParams.isUse\" placeholder=\"请选择\">\r\n          <el-option label=\"启用\" value=\"1\"></el-option>\r\n          <el-option label=\"停用\" value=\"0\"></el-option>\r\n        </el-select>\r\n      </el-form-item> -->\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"cyan\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          >新建报表</el-button\r\n        >\r\n      </el-col>\r\n    </el-row>\r\n    <el-table v-loading=\"loading\" :data=\"rootList\" border>\r\n      <el-table-column label=\"扎口部门\" align=\"center\" prop=\"deptName\" width=\"240\"/>\r\n      <!-- <el-table-column\r\n        label=\"扎口部门及人员\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleaAdminList(scope.row)\"\r\n              >{{scope.row.deptName}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column> -->\r\n      <!-- <el-table-column label=\"报表名称\" align=\"center\" prop=\"dimensionalityName\"/> -->\r\n      <el-table-column\r\n        label=\"报表名称\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handleAnswer(scope.row)\"\r\n              >{{scope.row.dimensionalityName}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"当期完成率\" align=\"center\" prop=\"countRate\" width=\"160\"/>\r\n      <el-table-column label=\"当期应填数量\" align=\"center\" prop=\"shouldCount\" width=\"160\" />\r\n      <!-- <el-table-column\r\n        label=\"当期应填数量\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handlefill(scope.row)\"\r\n              >{{scope.row.shouldCount}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column> -->\r\n\r\n      <el-table-column\r\n        label=\"当期未填数量\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n        width=\"160\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              @click=\"handlefill(scope.row)\"\r\n              >{{scope.row.notCount}}</el-button\r\n            >\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <!-- <el-table-column label=\"当期应填数量\" align=\"center\" prop=\"\"  @cell-click=\"handleDetail(scope.row)\"/>\r\n      <el-table-column label=\"当期未填数量\" align=\"center\" prop=\"hasCount\"/> -->\r\n      \r\n      <!-- <el-table-column label=\"是否在用\" align=\"center\" prop=\"isUse\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            style=\"margin-left: 10px\"\r\n            :type=\"scope.row.isUse == '1'? 'success' : 'danger'\"\r\n            >{{ scope.row.isUse == \"1\" ? \"启用\" : \"停用\" }}</el-tag\r\n          >\r\n        </template>\r\n      </el-table-column> -->\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"center\"\r\n        class-name=\"small-padding fixed-width\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-if=\"scope.row.ruleType != '5' \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            >维度管理</el-button\r\n          >\r\n          <el-button\r\n            v-if=\"scope.row.ruleType != '5' && scope.row.ruleType != '0'\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleDeadLine(scope.row)\"\r\n            >截止日期</el-button\r\n          >\r\n          <el-button\r\n            v-if=\"\r\n                  scope.row.ruleType == '1' ||\r\n                  scope.row.ruleType == '3' ||\r\n                  scope.row.ruleType == '4'\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"toUpdateUsers(scope.row)\"\r\n            >分配权限</el-button\r\n          >\r\n          <el-button\r\n              v-if=\"\r\n                  scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5'\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleExport(scope.row)\"\r\n            >导出数据</el-button>\r\n            <el-button\r\n              v-if=\"\r\n                  (scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5') &&\r\n                  aloneList(scope.row.dimensionalityName)\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleSpecial(scope.row)\"\r\n            >单周期数据导出</el-button>\r\n            <el-button\r\n              v-if=\"\r\n                  (scope.row.ruleType == '0' ||\r\n                  scope.row.ruleType == '2' ||\r\n                  scope.row.ruleType == '4' ||\r\n                  scope.row.ruleType == '5') &&\r\n                  bespokeList(scope.row.dimensionalityName)\r\n                 \"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleMouth(scope.row)\"\r\n            >定制化数据导出</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      :pageSizes=\"pageSizes\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <el-drawer\r\n      title=\"详情\"\r\n      :visible.sync=\"drawer\"\r\n      direction=\"rtl\"\r\n      size=\"80%\"\r\n      :before-close=\"handleClose\"\r\n    >\r\n      <tree-view :node=\"detail\" @refreshData=\"getDetail\"></tree-view>\r\n    </el-drawer>\r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"exportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>数据日期范围：</span>\r\n        <el-date-picker\r\n          v-model=\"dateValue\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"onDateChange\">\r\n        </el-date-picker>        \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div class=\"el-upload__tip\" slot=\"tip\">\r\n          <el-checkbox v-model=\"noteShow\" />是否在导出内容中展示指标修正历史\r\n        </div>\r\n      <el-button type=\"success\" @click=\"exportDataPreview\">预 览</el-button>\r\n      <el-button type=\"primary\" @click=\"exportData\">导 出</el-button>\r\n      <!-- <el-button @click=\"exportOpen = false\">取 消</el-button> -->\r\n    </div>\r\n    </el-dialog>\r\n\r\n    \r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"mouthImportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>数据日期范围：</span>\r\n        <el-date-picker\r\n          v-model=\"dateValue\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"onDateChange\">\r\n        </el-date-picker>        \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"exportMouthDataPreview\">预 览</el-button>\r\n      <el-button type=\"primary\" @click=\"exportMouthData\">导 出</el-button>\r\n      <!-- <el-button @click=\"mouthImportOpen = false\">取 消</el-button> -->\r\n    </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"选择导出范围\" :visible.sync=\"newOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"名称\" prop=\"dimensionalityName\">\r\n          <el-input\r\n            v-model=\"form.dimensionalityName\"\r\n            placeholder=\"请输入名称\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"扎口部门\" prop=\"deptCode\">\r\n          <el-cascader\r\n            :options=\"deptList\"\r\n            clearable\r\n            v-model=\"form.deptCode\"\r\n            :props=\"{ expandTrigger: 'hover', emitPath: false,checkStrictly: true }\"\r\n            :show-all-levels=\"false\"\r\n          >\r\n          </el-cascader>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"addClick\">确 定</el-button>\r\n        <el-button @click=\"newOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"adminTitle\" :visible.sync=\"adminOpen\" width=\"1000px\" append-to-body>\r\n      <el-table v-loading=\"loading\" :data=\"userList\">\r\n        <el-table-column label=\"用户工号\" align=\"center\" prop=\"workNo\" />\r\n        <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" />\r\n        <!-- <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" /> -->\r\n      </el-table>\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"deadlineTitle\" :visible.sync=\"deadlineOpen\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"deadlineForm\" :model=\"deadlineForm\" label-width=\"160px\">\r\n        <el-form-item label=\"截止日期开关\" prop=\"deadlineSwitch\">\r\n          <el-switch\r\n            v-model=\"deadlineForm.deadlineSwitch\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n            >\r\n          </el-switch>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.deadlineSwitch == '1' \" label=\"截止日期\" prop=\"deadlineDate\">\r\n          <el-input type=\"text\"\r\n                    v-model=\"deadlineForm.deadlineDate\" \r\n                    placeholder=\"截止日期格式为(年/月/日)\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.deadlineSwitch == '1' \"  label=\"邮件通知开关\" prop=\"mailSwitch\">\r\n          <el-switch\r\n            v-model=\"deadlineForm.mailSwitch\"\r\n            active-color=\"#13ce66\"\r\n            inactive-color=\"#ff4949\"\r\n            active-value=\"1\"\r\n            inactive-value=\"0\"\r\n            >\r\n          </el-switch>\r\n        </el-form-item>\r\n        <el-form-item v-if=\" deadlineForm.mailSwitch == '1' \" label=\"通知时间\" prop=\"deadlineDate\">\r\n          <el-input type=\"text\"\r\n                    v-model=\"deadlineForm.countdown\" \r\n                    placeholder=\"设置在截止日期前几天进行通知\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- <el-dialog\r\n      title=\"单周期报表导出\"\r\n      :visible.sync=\"SpecialImportOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"downloadTemplateSpecial\">数据下载</el-button>\r\n      </div>\r\n    </el-dialog> -->\r\n\r\n    <el-dialog title=\"单周期报表导出\" :visible.sync=\"SpecialImportOpen\" width=\"400px\" append-to-body destroy-on-close>\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>     \r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"success\" @click=\"downloadTemplateSpecialPreview\">数据预览</el-button>\r\n      <el-button type=\"primary\" @click=\"downloadTemplateSpecial\">数据下载</el-button>\r\n    </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog  title=\"数据预览\"  :visible.sync=\"searchopen\" width=\"1800px\" >\r\n        <div class=\"test\">\r\n          <vue-office-excel\r\n              :src=\"customBlobContent\"\r\n              :options=\"xlsxOptions\"\r\n              style=\"height: 100vh;\"\r\n          />\r\n        </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport TreeView from \"@/components/TreeView\";\r\nimport {\r\n  rootListDimensionality,\r\n  getRootListById,\r\n  addDimensionality,\r\n  getStatusListWithadmin\r\n} from \"@/api/tYjy/dimensionality\";\r\n\r\nimport {\r\n  listPermission,\r\n} from \"@/api/tYjy/dimensionalitypermission\";\r\n\r\nimport {\r\n  deadlinebranch,\r\n  updateForm,\r\n} from \"@/api/tYjy/form\";\r\n\r\nimport { listDept } from \"@/api/tYjy/dept\";\r\nimport axios from \"axios\";\r\nimport * as xlsx from 'xlsx';\r\n\r\nexport default {\r\n  name: \"Dimensionality\",\r\n  components: {\r\n    TreeView,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      newOpen:false,\r\n      SpecialImportOpen:false,\r\n      mouthImportOpen:false,\r\n      searchopen:false,\r\n      total:0,\r\n      pageSizes:[20,50,100],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        dimensionalityName: null,\r\n        isUse: null,\r\n      },\r\n      customBlobContent: null,\r\n      rootList: [],\r\n      detail: {},\r\n      rootId:null,\r\n      drawer:false,\r\n      query:{\r\n        startDate:null,\r\n        endDate:null,\r\n        rootId:null,\r\n        title:null,\r\n      },\r\n      exportOpen:false,\r\n      deadlineOpen:false,\r\n      deadlineTitle:\"批量修改截止日期\",\r\n      deadlineForm:\r\n      {\r\n        dimensionalityPath:null\r\n      },\r\n      dateValue:null,\r\n      deptList: [],\r\n      form:{},\r\n      userList:[],\r\n      adminOpen:false,\r\n      adminTitle:\"管理员名单\",\r\n      specialFcDate:null,\r\n      dimensionalityName:null,\r\n      dimensionalityId:null,\r\n      noteShow:false, //是否展示指标\r\n\r\n      xlsxOptions:{\r\n                // xls: false,       //预览xlsx文件设为false；预览xls文件设为true\r\n                // minColLength: 0,  // excel最少渲染多少列，如果想实现xlsx文件内容有几列，就渲染几列，可以将此值设置为0.\r\n                // minRowLength: 0,  // excel最少渲染多少行，如果想实现根据xlsx实际函数渲染，可以将此值设置为0.\r\n                // widthOffset: 10,  //如果渲染出来的结果感觉单元格宽度不够，可以在默认渲染的列表宽度上再加 Npx宽\r\n                // heightOffset: 10, //在默认渲染的列表高度上再加 Npx高\r\n                beforeTransformData: (workbookData) => {return workbookData}, //底层通过exceljs获取excel文件内容，通过该钩子函数，可以对获取的excel文件内容进行修改，比如某个单元格的数据显示不正确，可以在此自行修改每个单元格的value值。\r\n                transformData: (workbookData) => {return workbookData}, //将获取到的excel数据进行处理之后且渲染到页面之前，可通过transformData对即将渲染的数据及样式进行修改，此时每个单元格的text值就是即将渲染到页面上的内容\r\n            },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getDept();\r\n  },\r\n  methods: {\r\n    clickNode($event, node) {\r\n      $event.target.parentElement.parentElement.firstElementChild.click();\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      getStatusListWithadmin(this.queryParams).then((res) => {\r\n        this.rootList = res.rows;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.rootList[i].id==273 || this.rootList[i].id==840 || this.rootList[i].id==873 || this.rootList[i].id==1077 || this.rootList[i].id==1059 || this.containsSubstring('安全责任工资',this.rootList[i].dimensionalityName))\r\n          {\r\n            this.rootList[i].showboot=1\r\n          }\r\n          else\r\n          {\r\n            this.rootList[i].showboot=0\r\n          }\r\n          if(this.containsSubstring('工装',this.rootList[i].dimensionalityName))\r\n          {\r\n            this.rootList[i].showMouth=1\r\n          }\r\n          else\r\n          {\r\n            this.rootList[i].showMouth=0\r\n          }\r\n        }\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n      // rootListDimensionality(this.queryParams).then((res) => {\r\n      //   this.rootList = res.rows;\r\n      //   this.total = res.total;\r\n      //   this.loading = false;\r\n      // });\r\n    },\r\n    getDept() {\r\n      listDept().then((res) => {\r\n        this.deptList = res.rows[0].children;\r\n        console.log(res);\r\n        for(let i=0;i<this.deptList.length;i++)\r\n        {\r\n          this.dealdeptList(this.deptList[i],0)\r\n        }\r\n      });\r\n    },\r\n    dealdeptList(row,count)\r\n    {\r\n       row.value=row.path\r\n       row.label=row.deptName\r\n       if(row.children.length>0 && count<1)\r\n       {\r\n          for(let i=0;i<row.children.length;i++)\r\n          {\r\n            this.dealdeptList(row.children[i],count+1)\r\n          }\r\n       }\r\n       else\r\n       {\r\n          row.children=null\r\n       }\r\n    },\r\n    handleQueryDept() {\r\n      this.$refs.cascaderHandle.dropDownVisible = false;\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n\r\n\r\n    handleAdd() {\r\n      this.newOpen=true\r\n      // let that = this;\r\n      // this.$prompt(\"请输入名称\", \"提示\", {\r\n      //   confirmButtonText: \"确定\",\r\n      //   cancelButtonText: \"取消\",\r\n      // })\r\n      //   .then(({ value }) => {\r\n      //     let form = {};\r\n      //     form.dimensionalityName = value;\r\n      //     addDimensionality(form).then((res) => {\r\n      //       that.getList();\r\n      //     });\r\n      //   })\r\n      //   .catch(() => {\r\n      //     that.$message({\r\n      //       type: \"info\",\r\n      //       message: \"取消操作\",\r\n      //     });\r\n      //   });\r\n    },\r\n    handleDetail(row){\r\n      this.rootId = row.id;\r\n      this.rootRuleType = row.ruleType;\r\n      this.getDetail();\r\n      this.drawer = true;\r\n    },\r\n    handleDeadLine(row){\r\n      this.deadlineForm={dimensionalityPath:null}\r\n      this.deadlineForm.dimensionalityPath=row.path\r\n      this.deadlineOpen=true\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      if(this.deadlineForm.deadlineSwitch==1)\r\n      {\r\n        if(this.deadlineForm.deadlineDate==null)\r\n        {\r\n          this.$modal.msgError(\"截止日期不能为空\");\r\n          return\r\n        }\r\n        let deadlineDateCheck=this.deadlineForm.deadlineDate.split(\"/\")\r\n        if(deadlineDateCheck.length!=3)\r\n        {\r\n          this.$modal.msgError(\"截止日期格式不正确，正确格式是 年/月/日 \");\r\n          return\r\n        }\r\n        if(!/^-?(0|([1-9]?\\d)|100)$/.test(deadlineDateCheck[0]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中年应是在-100到100之间的整数\");\r\n          return\r\n        }\r\n        if(!/^-?(0|([0]?\\d)|11|12)$/.test(deadlineDateCheck[1]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中月应是在-12到12之间的整数\");\r\n          return\r\n        }\r\n        if(!/^-?(0|([1-2]?\\d)|31|30)$/.test(deadlineDateCheck[2]))\r\n        {\r\n          this.$modal.msgError(\"截止日期中日应是在-31到31之间的整数\");\r\n          return\r\n        }\r\n      }\r\n      deadlinebranch(this.deadlineForm).then((response) => \r\n      {\r\n        this.msgSuccess(\"批量修改截止日期成功\");\r\n        this.deadlineOpen = false;\r\n      });\r\n    },\r\n\r\n    cancel() {\r\n      this.deadlineOpen = false;\r\n    },\r\n\r\n    getDetail(){\r\n    getRootListById({id : this.rootId,ruleType:this.rootRuleType}).then(res => {\r\n        this.detail = res.data;\r\n        if(this.detail == null || this.detail == undefined)this.detail = {}\r\n        console.log(this.detail)\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    handleClose(){\r\n      this.drawer = false;\r\n      this.getList();\r\n      this.$forceUpdate();\r\n    },\r\n    handleExport(row){\r\n      this.query.rootId  = row.id;\r\n      this.query.title = row.dimensionalityName;\r\n      this.clickChangeTime();\r\n      this.exportOpen = true;\r\n    },\r\n\r\n    addClick() {\r\n      // this.form.deptId=parseInt(this.form.deptId.split(\",\")[-1])\r\n      addDimensionality(this.form).then((res) => {\r\n        this.newOpen = false;\r\n        this.getList();\r\n        this.form={};\r\n      });\r\n    },\r\n    exportData() {\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.noteShow=this.noteShow\r\n      this.downloadFile(\r\n        \"/web/TYjy/dimensionality/exportStatistics\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        \"(\" +\r\n          this.query.startDate +\r\n          \"-\" +\r\n          this.query.endDate +\r\n          \")\" +\r\n          this.query.title +\r\n          `.xlsx`\r\n      );\r\n    },\r\n    exportDataPreview()\r\n    {\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.noteShow=this.noteShow\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/dimensionality/exportStatistics\",\r\n        {\r\n          ...this.query,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=evt.target.result;\r\n\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          \r\n\r\n\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n    exportMouthDataPreview(){\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.rootId = this.dimensionalityId\r\n      this.query.type=\"1\"\r\n      this.query.isUpdate=\"1\"\r\n      let path=\"/web/TYjy/answer/exportEverymouth\"\r\n      if(this.dimensionalityId==1028)\r\n      {\r\n        path=\"/web/TYjy/answer/exportEverymouth\"\r\n      }\r\n      if(this.dimensionalityId==748)\r\n      {\r\n        path=\"/web/TYjy/answer/exportWithTemplate1\"\r\n      }\r\n      this.downloadXlsx(\r\n          path,\r\n          {\r\n            ...this.query,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=evt.target.result;\r\n            // let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            // ints = ints.slice(0, blob.size);\r\n            // let workBook = xlsx.read(ints, { type: \"array\" });\r\n            // let sheetNames = workBook.SheetNames;\r\n            // let sheetName = sheetNames[0];\r\n            // let workSheet = workBook.Sheets[sheetName];\r\n            // //获取Excle内容，并将空内容用空值保存\r\n            // let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // // 获取Excel头部\r\n            // let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            //   (item) => {\r\n            //     return item\r\n            //   }\r\n            // );\r\n            // this.excelData = excelTable;\r\n            // this.exceltitle=tableThead\r\n            // this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n    },\r\n    exportMouthData(){\r\n      if (\r\n        this.query.startDate == null ||\r\n        this.query.startDate == \"\"||\r\n        this.query.endDate == null||\r\n        this.query.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.query.rootId = this.dimensionalityId\r\n      this.query.type=\"1\"\r\n      // this.query.isUpdate=\"1\"\r\n      let path=\"/web/TYjy/answer/exportEverymouth\"\r\n      if(this.dimensionalityId==1028)\r\n      {\r\n        path=\"/web/TYjy/answer/exportEverymouth\"\r\n      }\r\n      if(this.dimensionalityId==748)\r\n      {\r\n        path=\"/web/TYjy/answer/exportWithTemplate1\"\r\n      }\r\n      this.downloadFile(\r\n        path,\r\n        {\r\n          ...this.query,\r\n        },\r\n        \"(\" +\r\n          this.query.startDate +\r\n          \"-\" +\r\n          this.query.endDate +\r\n          \")\" +\r\n          this.dimensionalityName +\r\n          `.xlsx`\r\n      );\r\n    },\r\n    onDateChange(){\r\n      console.log(this.dateValue)\r\n      if(this.dateValue != null && this.dateValue != \"\"){\r\n        this.query.startDate = this.dateValue[0] ;\r\n        this.query.endDate = this.dateValue[1];\r\n      }else{\r\n        this.query.startDate = \"\";\r\n        this.query.endDate = \"\";\r\n      }\r\n    },\r\n    toUpdateUsers(row){\r\n      const dimensionalityId = row.id;\r\n      this.$router.push(\"/dataReport/dimensionality-auth/dimensionalityPermission/\" + dimensionalityId);\r\n      // this.$router.go(0)\r\n    },\r\n    handleDateChange() {\r\n      this.getList();\r\n    },\r\n    handleaAdminList(row){\r\n\r\n      const dimensionalityId = row.id;\r\n      listPermission({dimensionalityId:dimensionalityId}).then((response) => {\r\n        this.userList = response.rows;\r\n        // this.total = response.total;\r\n        // this.loading = false;\r\n        this.adminOpen = true;\r\n      });\r\n\r\n      // const fcDate = this.queryParams.fcDate;\r\n      // const dimensionalityName = row.dimensionalityName;\r\n      // this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus', query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n    handlefill(row){\r\n      // const dimensionalityId = row.id;\r\n      // this.$router.push(\"/dataReport/adminfill-auth/adminfillstatus/\" + dimensionalityId);\r\n      const dimensionalityId = row.id;\r\n      const fcDate = this.queryParams.fcDate;\r\n      const dimensionalityName = row.dimensionalityName;\r\n      this.$router.push({ path: '/dataReport/adminfill-auth/adminfillstatus/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n    handleAnswer(row){\r\n      // const dimensionalityId = row.id;\r\n      // this.$router.push(\"/dataReport/adminfill-auth/adminfillstatus/\" + dimensionalityId);\r\n      const dimensionalityId = row.id;\r\n      const fcDate = this.queryParams.fcDate;\r\n      const dimensionalityName= row.dimensionalityName;\r\n      this.$router.push({ path: '/dataReport/answerShow-auth/answerShow/'+ dimensionalityId, query: { dimensionalityId:dimensionalityId, fcDate:fcDate,dimensionalityName:dimensionalityName} });\r\n    },\r\n\r\n  handleSpecial(row){\r\n      // this.query.rootId  = row.id;\r\n      this.dimensionalityName = row.dimensionalityName;\r\n      this.dimensionalityId=row.id;\r\n      this.SpecialImportOpen = true;\r\n      \r\n    },\r\n  handleMouth(row){\r\n      // this.query.rootId  = row.id;\r\n      this.dimensionalityName = row.dimensionalityName;\r\n      this.dimensionalityId=row.id;\r\n      this.clickChangeTime();\r\n      this.mouthImportOpen = true;\r\n    },\r\n  downloadTemplateSpecialPreview(){\r\n    if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"1\"\r\n      queryImport.isUpdate=\"1\"\r\n      let url=\"\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        url=\"/web/TYjy/answer/exportWithTemplate\"\r\n      }\r\n      else\r\n      {\r\n        url=\"/web/TYjy/answer/exportTemplateSpecial\"\r\n      }\r\n      this.downloadXlsx(\r\n          url,\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n  },\r\n  downloadTemplateSpecial(){\r\n      if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (this.specialFcDate == null ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"未选择时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"1\"\r\n      let url=\"\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        url=\"/web/TYjy/answer/exportWithTemplate\"\r\n      }\r\n      else\r\n      {\r\n        url=\"/web/TYjy/answer/exportTemplateSpecial\"\r\n      }\r\n      this.downloadFile(\r\n        url,\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n    },\r\n\r\n    //此处为按钮判断管理\r\n    containsSubstring(substring, string) \r\n    {\r\n      return string.includes(substring);\r\n    },\r\n\r\n    bespokeList(string)\r\n    {\r\n      if(string== '2025年专利月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '工装上线验收合格率统计')\r\n      {\r\n        return true;\r\n      }\r\n      // if(string== '2025年经济责任制技经指标')\r\n      // {\r\n      //   return true;\r\n      // }\r\n      // if(string== '研究院技经提升指标跟踪')\r\n      // {\r\n      //   return true;\r\n      // }\r\n      return false;\r\n    },\r\n    //支持预览和导出\r\n    mouthCheck(string)\r\n    {\r\n      if(string== '六化指标')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '工装上线验收合格率统计')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '2025年经济责任制技经指标')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院技经提升指标跟踪')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n    //支持单周期导出\r\n    aloneList(string) {\r\n      if(string== '气体结算月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '高炉、转炉煤气月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '天然气消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '蒸汽消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '电量月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '2025年经济责任制考核表（特板事业部）')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '研究院目标指标一览')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '安全责任工资考核汇总')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '水处理水量报表')\r\n      {\r\n        return true;\r\n      }\r\n      return false;\r\n    },\r\n\r\n    //数据预览模块处理\r\n    handlePreview() {\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.queryParams.fcDate\r\n      queryImport.type=\"1\"\r\n      queryImport.isUpdate=\"1\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n          this.downloadXlsx(\r\n          \"/web/TYjy/answer/exportWithTemplate\",\r\n          {\r\n            ...queryImport,\r\n          },\r\n          this.dimensionalityName+\"(\" +this.specialFcDate+\r\n            \")\" +\r\n            `数据.xlsx`\r\n        ).then((blob) => {\r\n          let reader = new FileReader();\r\n          reader.readAsArrayBuffer(blob);\r\n          \r\n          reader.onload = (evt) => {\r\n            this.customBlobContent=reader.result;\r\n            let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n            ints = ints.slice(0, blob.size);\r\n            let workBook = xlsx.read(ints, { type: \"array\" });\r\n            let sheetNames = workBook.SheetNames;\r\n            let sheetName = sheetNames[0];\r\n            let workSheet = workBook.Sheets[sheetName];\r\n            //获取Excle内容，并将空内容用空值保存\r\n            let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n            // 获取Excel头部\r\n            let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n              (item) => {\r\n                return item\r\n              }\r\n            );\r\n            this.excelData = excelTable;\r\n            this.exceltitle=tableThead\r\n            this.excelHtml= excelTable\r\n            this.searchopen = true;\r\n          }\r\n        });\r\n      }\r\n      else\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n    },\r\n    // 时间段预览\r\n    handlePreview1() {\r\n      // let queryImport={}\r\n      // queryImport.rootId = this.queryParams.dimensionalityId\r\n      // queryImport.fcDate = this.queryParams.fcDate\r\n      // queryImport.type=\"1\"\r\n        if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.queryImport.type=\"1\"\r\n      this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateNomral\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        \r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n    },\r\n\r\n    clickChangeTime()\r\n    {\r\n         let now =new Date();\r\n         this.query.startDate=this.getFirstOfYear(now);\r\n         this.query.endDate=this.getFirstOfMonth(now);\r\n         this.dateValue=[];\r\n         this.dateValue.push(this.query.startDate);\r\n         this.dateValue.push(this.query.endDate);\r\n    },\r\n    // 获取时间的优化处理\r\n    getFirstOfYear(now)\r\n    {\r\n      let  firstDayOfYear = new Date(now.getFullYear(), 0, 1);\r\n      return this.formatDate(firstDayOfYear);\r\n    },\r\n    getFirstOfMonth(now)\r\n    {\r\n      let firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\r\n      return this.formatDate(firstDayOfMonth);\r\n    },\r\n    // 日期格式化函数（转为 yyyy-MM-dd）\r\n    formatDate(date) \r\n    {\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始需+1\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\">\r\n.v-modal {\r\n  display: none;\r\n}\r\n</style>\r\n  "]}]}