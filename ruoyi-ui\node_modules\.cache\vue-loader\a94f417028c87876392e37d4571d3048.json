{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPlan\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPlan\\index.vue", "mtime": 1756456493847}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQppbXBvcnQgY2hhcnRNZXRob2RzIGZyb20gJy4vY2hhcnRNZXRob2RzJw0KaW1wb3J0IGV4dGVuZGVkQ2hhcnRNZXRob2RzIGZyb20gJy4vY2hhcnRNZXRob2RzRXh0ZW5kZWQnDQppbXBvcnQgc2NyZWVuZnVsbCBmcm9tICdzY3JlZW5mdWxsJw0KaW1wb3J0IHsgc2hvd1llYXJseUFtb3VudCwgc2hvd1JlYWxUaW1lQW1vdW50LCBzaG93Q29raW5nQ29hbEFtb3VudCwgc2hvd0tleUluZGljYXRvcnMsIHNob3dJdGVtVHlwZUxpc3QsIHNob3dNYXRlcmlhbExpc3QsIHNob3dEYXRhLCBzaG93U3VwcExpc3QsIHNob3dQdXJjaGFzZVBsYW5MaXN0LCBzaG93SGlnaEZyZXF1ZW5jeU1hdGVyaWFsTGlzdCwgc2hvd1B1cmNoYXNlU3VwcFJpc2ssIGdldE1hdGVyaWFsRnV0dXJlUHJpY2UsIGdldE1hdGVyaWFsTmFtZUxpc3QsIGdldFB1cmNoYXNlUHJpY2VBbmRTdG9yZSwgZ2V0TWF0ZXJpYWxOYW1lTGlzdEZyb21OZXdUYWJsZXMsIGdldFB1cmNoYXNlUHJpY2VBbmRTdG9yZUZyb21OZXdUYWJsZXMgfSBmcm9tICdAL2FwaS9wdXJjaGFzZURhc2hib2FyZC9wdXJjaGFzZURhc2hib2FyZCcNCmltcG9ydCB7IGxpc3RTaW1pbGFyQnlJdGVtTmFtZXMgfSBmcm9tICdAL2FwaS9wdXJjaGFzZS9zaW1pbGFyJw0KaW1wb3J0IHsgZ2V0RGVwTmFtZUxpc3QsIGdldExpc3RNb250aGx5IH0gZnJvbSAnQC9hcGkvcHVyY2hhc2UvcHVyZGNoYXNlRmFjdG9yeVN0b2NrJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdQdXJjaGFzZURhc2hib2FyZCcsDQogIG1peGluczogW2NoYXJ0TWV0aG9kcywgZXh0ZW5kZWRDaGFydE1ldGhvZHNdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgIA0KDQogICAgICAvLyDmlbDmja4NCiAgICAgIGRhc2hib2FyZERhdGE6IHt9LA0KICAgICAgcHVyY2hhc2VTdGF0czoge30sDQoNCiAgICAgIC8vIOS4i+aLiemAiemhuQ0KICAgICAgdG9wU3VwcGxpZXJzT3B0aW9uczogW10sDQoNCiAgICAgIC8vIOmAieS4reeahOi/h+a7pOWZqOWAvA0KICAgICAgc2VsZWN0ZWRUb3BTdXBwbGllcnNGaWx0ZXI6ICcnLA0KICAgICAgc2VsZWN0ZWRPcmRlclR5cGU6ICdUT1AnLCAvLyDmjpLluo/nsbvlnovvvIzpu5jorqTkuLpUT1ANCg0KICAgICAgLy8g5Zu+6KGo5a6e5L6LDQogICAgICBjaGFydEluc3RhbmNlczoge30sDQoNCiAgICAgIC8vIOWOn+Wni+aVsOaNruWkh+S7vQ0KICAgICAgb3JpZ2luYWxUb3BTdXBwbGllcnNEYXRhOiBbXSwNCg0KICAgICAgLy8g6K6i5Y2V6Iez5YWl5bqT5aSp5pWw55u45YWzDQogICAgICBzZWxlY3RlZE9yZGVyRmFjdG9yeURlcDogJycsIC8vIOmAieS4reeahOWIhuWOgg0KICAgICAgc2VsZWN0ZWRPcmRlck1hdGVyaWFsVHlwZTogJycsIC8vIOmAieS4reeahOeJqeaWmeexu+Weiw0KICAgICAgb3JkZXJUb1JlY2VpcHREYXRhOiBbXSwgLy8g6K6i5Y2V6Iez5YWl5bqT5aSp5pWw5pWw5o2uDQoNCiAgICAgIC8vIOesrOS6jOS4quiuouWNleiHs+WFpeW6k+WkqeaVsOaooeWdl+ebuOWFsw0KICAgICAgc2VsZWN0ZWRDb2tpbmdDb2FsRmFjdG9yeURlcDogJycsIC8vIOmAieS4reeahOWIhuWOgg0KICAgICAgc2VsZWN0ZWRDb2tpbmdDb2FsTWF0ZXJpYWxUeXBlOiAnJywgLy8g6YCJ5Lit55qE54mp5paZ57G75Z6LDQoNCiAgICAgIC8vIOesrOS4ieS4quiuouWNleiHs+WFpeW6k+WkqeaVsOaooeWdl+ebuOWFsw0KICAgICAgc2VsZWN0ZWRNYXRlcmlhbEZhY3RvcnlEZXA6ICcnLCAvLyDpgInkuK3nmoTliIbljoINCiAgICAgIHNlbGVjdGVkTWF0ZXJpYWxNYXRlcmlhbFR5cGU6ICcnLCAvLyDpgInkuK3nmoTnianmlpnnsbvlnosNCiAgICAgIHJlYWxUaW1lSW52ZW50b3J5RGF0YTogW10sDQogICAgICBjb2tpbmdDb2FsSW52ZW50b3J5RGF0YTogW10sDQoNCiAgICAgIC8vIOefv+eEpueFpOW6k+WtmOWbvuihqOebuOWFsw0KICAgICAgc2VsZWN0ZWRDb2tpbmdDb2FsVHlwZTogJycsIC8vIOmAieS4reeahOefv+eEpueFpOexu+Wei++8jOm7mOiupOS4uuepuu+8iOWFqOmDqO+8iQ0KDQogICAgICAvLyDnianmlpnlhaXlupPnu5/orqHnm7jlhbMNCiAgICAgIHNlbGVjdGVkTWF0ZXJpYWxDYXRlZ29yeTogJzEnLA0KICAgICAgc2VsZWN0ZWRNYXRlcmlhbEl0ZW06ICcnLA0KICAgICAgbWF0ZXJpYWxJdGVtT3B0aW9uczogW10sDQogICAgICBtYXRlcmlhbFN0YXRpc3RpY3NEYXRhOiBbXSwNCg0KICAgICAgLy8g6auY6aKR6YeH6LSt54mp5paZ55u45YWzDQogICAgICBzZWxlY3RlZENvZGVUeXBlOiAnQUxMJywNCiAgICAgIHNlbGVjdGVkSXRlbVR5cGU6ICdDTEFTUzMnLA0KICAgICAgaGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YTogW10sDQoNCiAgICAgIC8vIOS+m+W6lOWVhumjjumZqeaVsOaNrg0KICAgICAgc3VwcGxpZXJSaXNrRGF0YTogW10sDQoNCiAgICAgIC8vIEFJ5Lu35qC86aKE5rWL55u45YWzDQogICAgICBwcmljZVByZWRpY3Rpb25zOiBbXSwgLy8g5pS55Li65pWw57uE77yM5pSv5oyB5aSa5Liq54mp5paZ55qE6aKE5rWLDQogICAgICBwcmVkaWN0aW9uTG9hZGluZzogZmFsc2UsDQoNCiAgICAgIC8vIOeJqeaWmeS7t+agvOi2i+WKv+WbvuebuOWFsw0KICAgICAgbWF0ZXJpYWxOYW1lT3B0aW9uczogW10sDQogICAgICBzZWxlY3RlZE1hdGVyaWFsOiAnUELlnZcnLA0KICAgICAgc2VsZWN0ZWRNYXRlcmlhbENhdGVnb3J5OiAnMScsIC8vIOm7mOiupOmAieaLqeefv+efsw0KICAgICAgcHJpY2VBbmRTdG9yZURhdGE6IG51bGwsDQoNCiAgICAgIC8vIOaWsOeahOS7t+agvOi2i+WKv+WbvuebuOWFs+WxnuaApw0KICAgICAgLy8g6YeH6LSt6YeP5puy57q/DQogICAgICBwdXJjaGFzZUFtb3VudENhdGVnb3JpZXM6IFs5OV0sIC8vIOm7mOiupOmAieaLqeWFqOmDqA0KICAgICAgc2VsZWN0ZWRQdXJjaGFzZUFtb3VudE1hdGVyaWFsczogW10sDQogICAgICBwdXJjaGFzZUFtb3VudE1hdGVyaWFsT3B0aW9uczogW10sDQoNCiAgICAgIC8vIOW4guWcuuS7t+absue6vw0KICAgICAgbWFya2V0UHJpY2VDYXRlZ29yaWVzOiBbOTldLCAvLyDpu5jorqTpgInmi6nlhajpg6gNCiAgICAgIHNlbGVjdGVkTWFya2V0UHJpY2VNYXRlcmlhbHM6IFtdLA0KICAgICAgbWFya2V0UHJpY2VNYXRlcmlhbE9wdGlvbnM6IFtdLA0KDQogICAgICAvLyDojrflj5bmlbDmja7nirbmgIENCiAgICAgIGZldGNoaW5nUHJpY2VEYXRhOiBmYWxzZSwNCiAgICAgIG5ld1ByaWNlQW5kU3RvcmVEYXRhOiBudWxsLA0KDQogICAgICAvLyDliJ3lp4vljJbmoIflv5cNCiAgICAgIGhhc0luaXRpYWxpemVkUHJpY2VDaGFydDogZmFsc2UsDQoNCiAgICAgIC8vIOebuOS8vOeJqeaWmeaVsOaNrg0KICAgICAgc2ltaWxhck1hdGVyaWFsc0RhdGE6IFtdLA0KICAgICAgc2ltaWxhck1hdGVyaWFsc0xvYWRpbmc6IGZhbHNlLA0KDQogICAgICAvLyDlr7nmr5TlvLnmoYbnm7jlhbMNCiAgICAgIGNvbXBhcmlzb25EaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNvbXBhcmlzb25DaGFydExvYWRpbmc6IGZhbHNlLA0KICAgICAgY3VycmVudENvbXBhcmlzb246IHt9LA0KICAgICAgY29tcGFyaXNvbkNoYXJ0SW5zdGFuY2U6IG51bGwsDQogICAgICBjb21wYXJpc29uUHJpY2VEYXRhOiBudWxsLA0KDQogICAgICAvLyDmnLrml4HlupPlvZPliY3lupPlrZjnm7jlhbMNCiAgICAgIHNlbGVjdGVkRmFjdG9yeURlcDogJycsIC8vIOmAieS4reeahOWIhuWOgg0KICAgICAgc2VsZWN0ZWRGYWN0b3J5TWF0ZXJpYWxUeXBlOiAnJywgLy8g6YCJ5Lit55qE54mp5paZ57G75Z6LDQogICAgICBmYWN0b3J5RGVwT3B0aW9uczogW10sIC8vIOWIhuWOgumAiemhueWIl+ihqA0KICAgICAgZmFjdG9yeVN0b2NrRGF0YTogW10sIC8vIOacuuaXgeW6k+WtmOaVsOaNrg0KDQogICAgICAvLyDlrp7ml7botoXmnJ/mlbDnm7jlhbMNCiAgICAgIHNlbGVjdGVkT3ZlcmR1ZUZhY3RvcnlEZXA6ICcnLCAvLyDpgInkuK3nmoTliIbljoINCiAgICAgIG92ZXJkdWVEYXRhOiBbXSAvLyDotoXmnJ/mlbDmja4NCiAgICB9DQogIH0sDQoNCiAgY29tcHV0ZWQ6IHsNCiAgICBpc0Z1bGxzY3JlZW4oKSB7DQogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuYXBwLmlzRnVsbHNjcmVlbk1vZGUNCiAgICB9LA0KDQogICAgLy8g5oyJaXRlbU5hbWXjgIFjYXRlZ29yeeOAgXByaWNlVHlwZeiBlOWQiOe0ouW8leWIhue7hOebuOS8vOeJqeaWmeaVsOaNrg0KICAgIGdyb3VwZWRTaW1pbGFyTWF0ZXJpYWxzKCkgew0KICAgICAgY29uc3QgZ3JvdXBlZCA9IHt9DQogICAgICB0aGlzLnNpbWlsYXJNYXRlcmlhbHNEYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgIC8vIOWIm+W7uuiBlOWQiOe0ouW8lWtleQ0KICAgICAgICBjb25zdCBncm91cEtleSA9IGAke2l0ZW0uaXRlbU5hbWV9XyR7aXRlbS5jYXRlZ29yeX1fJHtpdGVtLnByaWNlVHlwZX1gDQogICAgICAgIGNvbnN0IGRpc3BsYXlLZXkgPSBgJHtpdGVtLml0ZW1OYW1lfSAoJHt0aGlzLmdldENhdGVnb3J5TmFtZShpdGVtLmNhdGVnb3J5KX0gLSAke3RoaXMuZ2V0UHJpY2VUeXBlTmFtZShpdGVtLnByaWNlVHlwZSl9KWANCg0KICAgICAgICBpZiAoIWdyb3VwZWRbZGlzcGxheUtleV0pIHsNCiAgICAgICAgICBncm91cGVkW2Rpc3BsYXlLZXldID0gew0KICAgICAgICAgICAgZ3JvdXBLZXk6IGdyb3VwS2V5LA0KICAgICAgICAgICAgaXRlbXM6IFtdDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGdyb3VwZWRbZGlzcGxheUtleV0uaXRlbXMucHVzaChpdGVtKQ0KICAgICAgfSkNCg0KICAgICAgLy8g5a+55q+P5Liq57uE5YaF55qE5pWw5o2u5oyJ5o6S5ZCN5o6S5bqPDQogICAgICBPYmplY3Qua2V5cyhncm91cGVkKS5mb3JFYWNoKGtleSA9PiB7DQogICAgICAgIGdyb3VwZWRba2V5XS5pdGVtcy5zb3J0KChhLCBiKSA9PiBhLnJhbmsgLSBiLnJhbmspDQogICAgICB9KQ0KDQogICAgICByZXR1cm4gZ3JvdXBlZA0KICAgIH0NCiAgfSwNCg0KICBtb3VudGVkKCkgew0KICAgIHRoaXMuY2hlY2tFY2hhcnRzQXZhaWxhYmlsaXR5KCkNCiAgICB0aGlzLmZldGNoRGFzaGJvYXJkRGF0YSgzKQ0KDQogICAgdGhpcy5mZXRjaFJlYWxUaW1lSW52ZW50b3J5RGF0YSgpDQogICAgdGhpcy5mZXRjaENva2luZ0NvYWxJbnZlbnRvcnlEYXRhKCkNCiAgICAvLyDliJ3lp4vljJbnianmlpnlhaXlupPnu5/orqHnmoTkuIvmi4nmoYbpgInpobnlkozmlbDmja4NCiAgICB0aGlzLnVwZGF0ZU1hdGVyaWFsSXRlbU9wdGlvbnMoKS50aGVuKCgpID0+IHsNCiAgICAgIHRoaXMuZmV0Y2hNYXRlcmlhbFN0YXRpc3RpY3NEYXRhKCkNCiAgICB9KQ0KICAgIC8vIOWIneWni+WMlumrmOmikemHh+i0reeJqeaWmeaVsOaNrg0KICAgIHRoaXMuZmV0Y2hIaWdoRnJlcXVlbmN5TWF0ZXJpYWxEYXRhKCkNCiAgICAvLyDliJ3lp4vljJbkvpvlupTllYbpo47pmanmlbDmja4NCiAgICB0aGlzLmZldGNoU3VwcGxpZXJSaXNrRGF0YSgpDQoNCiAgICAvLyDliJ3lp4vljJbmlrDnmoTnianmlpnlkI3np7DliJfooajvvIjkvJroh6rliqjop6blj5Hpu5jorqTpgInkuK1QQuWdl+WSjOaVsOaNruiOt+WPlu+8iQ0KICAgIHRoaXMuZmV0Y2hQdXJjaGFzZUFtb3VudE1hdGVyaWFsTGlzdCgpDQogICAgdGhpcy5mZXRjaE1hcmtldFByaWNlTWF0ZXJpYWxMaXN0KCkNCg0KICAgIC8vIOWIneWni+WMluacuuaXgeW6k+WtmOaVsOaNrg0KICAgIHRoaXMuZmV0Y2hGYWN0b3J5RGVwT3B0aW9ucygpDQoNCiAgICAvLyDliJ3lp4vljJborqLljZXoh7PlhaXlupPlpKnmlbDmlbDmja4NCiAgICB0aGlzLmZldGNoT3JkZXJUb1JlY2VpcHREYXRhKCkNCg0KICAgIC8vIOWIneWni+WMlui2heacn+aVsOaNrg0KICAgIHRoaXMuZmV0Y2hPdmVyZHVlRGF0YSgpDQoNCiAgICB0aGlzLnNldHVwUmVzaXplT2JzZXJ2ZXIoKQ0KICAgIHRoaXMuaW5pdEZ1bGxzY3JlZW5MaXN0ZW5lcigpDQoNCiAgICAvLyDnm5HlkKznqpflj6PlpKflsI/lj5jljJYNCiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5yZXNpemVBbGxDaGFydHMpDQogIH0sDQoNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDmuIXnkIblrprml7blmajlkozkuovku7bnm5HlkKzlmagNCiAgICB0aGlzLmNsZWFyQWxsSW50ZXJ2YWxzKCkNCiAgICB0aGlzLnJlbW92ZUZ1bGxzY3JlZW5MaXN0ZW5lcigpDQogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVzaXplQWxsQ2hhcnRzKQ0KDQogICAgLy8g56Gu5L+d6YCA5Ye65YWo5bGP5qih5byPDQogICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC9zZXRGdWxsc2NyZWVuTW9kZScsIGZhbHNlKQ0KICB9LA0KDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliJ3lp4vljJblhajlsY/nm5HlkKzlmagNCiAgICBpbml0RnVsbHNjcmVlbkxpc3RlbmVyKCkgew0KICAgICAgaWYgKHNjcmVlbmZ1bGwgJiYgc2NyZWVuZnVsbC5pc0VuYWJsZWQpIHsNCiAgICAgICAgc2NyZWVuZnVsbC5vbignY2hhbmdlJywgdGhpcy5oYW5kbGVGdWxsc2NyZWVuQ2hhbmdlKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDnp7vpmaTlhajlsY/nm5HlkKzlmagNCiAgICByZW1vdmVGdWxsc2NyZWVuTGlzdGVuZXIoKSB7DQogICAgICBpZiAoc2NyZWVuZnVsbCAmJiBzY3JlZW5mdWxsLmlzRW5hYmxlZCkgew0KICAgICAgICBzY3JlZW5mdWxsLm9mZignY2hhbmdlJywgdGhpcy5oYW5kbGVGdWxsc2NyZWVuQ2hhbmdlKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIblhajlsY/nirbmgIHlj5jljJYNCiAgICBoYW5kbGVGdWxsc2NyZWVuQ2hhbmdlKCkgew0KICAgICAgaWYgKHNjcmVlbmZ1bGwgJiYgc2NyZWVuZnVsbC5pc0VuYWJsZWQpIHsNCiAgICAgICAgY29uc3QgaXNGdWxsc2NyZWVuID0gc2NyZWVuZnVsbC5pc0Z1bGxzY3JlZW4NCiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC9zZXRGdWxsc2NyZWVuTW9kZScsIGlzRnVsbHNjcmVlbikNCg0KICAgICAgICAvLyDlhajlsY/nirbmgIHlj5jljJblkI7vvIzph43mlrDosIPmlbTlm77ooajlpKflsI8NCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5yZXNpemVBbGxDaGFydHMoKQ0KICAgICAgICAgIH0sIDMwMCkgLy8g57uZ5biD5bGA5Y+Y5YyW5LiA5Lqb5pe26Ze0DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIEFQSeiwg+eUqOaWueazlQ0KICAgIGFzeW5jIGdldERhc2hib2FyZERhdGEoZGltZW5zaW9uVHlwZSkgew0KICAgICAgcmV0dXJuIGF3YWl0IHNob3dEYXRhKHsgZGltZW5zaW9uVHlwZTogZGltZW5zaW9uVHlwZSB9KQ0KICAgIH0sDQoNCiAgICBhc3luYyBnZXRJdGVtVHlwZUxpc3QoaXRlbVR5cGUpIHsNCiAgICAgIHJldHVybiBhd2FpdCBzaG93SXRlbVR5cGVMaXN0KHsgaXRlbVR5cGU6IGl0ZW1UeXBlIH0pDQogICAgfSwNCg0KICAgIGFzeW5jIGdldE1hdGVyaWFsTGlzdChwYXJhbXMpIHsNCiAgICAgIHJldHVybiBhd2FpdCBzaG93TWF0ZXJpYWxMaXN0KHBhcmFtcykNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0U3VwcGxpZXJMaXN0KHBhcmFtcykgew0KICAgICAgcmV0dXJuIGF3YWl0IHNob3dTdXBwTGlzdChwYXJhbXMpDQogICAgfSwNCg0KICAgIGFzeW5jIGdldFllYXJseUFtb3VudChwYXJhbXMpIHsNCiAgICAgIHJldHVybiBhd2FpdCBzaG93WWVhcmx5QW1vdW50KHBhcmFtcykNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0UmVhbFRpbWVBbW91bnQoKSB7DQogICAgICByZXR1cm4gYXdhaXQgc2hvd1JlYWxUaW1lQW1vdW50KCkNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0Q29raW5nQ29hbEFtb3VudCgpIHsNCiAgICAgIHJldHVybiBhd2FpdCBzaG93Q29raW5nQ29hbEFtb3VudCgpDQogICAgfSwNCg0KICAgIGFzeW5jIGdldEtleUluZGljYXRvcnMocGFyYW1zKSB7DQogICAgICByZXR1cm4gYXdhaXQgc2hvd0tleUluZGljYXRvcnMocGFyYW1zKQ0KICAgIH0sDQoNCiAgICBhc3luYyBnZXRIaWdoRnJlcXVlbmN5TWF0ZXJpYWxMaXN0KHBhcmFtcykgew0KICAgICAgcmV0dXJuIGF3YWl0IHNob3dIaWdoRnJlcXVlbmN5TWF0ZXJpYWxMaXN0KHBhcmFtcykNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0UHVyY2hhc2VTdXBwUmlzayhwYXJhbXMpIHsNCiAgICAgIHJldHVybiBhd2FpdCBzaG93UHVyY2hhc2VTdXBwUmlzayhwYXJhbXMpDQogICAgfSwNCg0KICAgIC8vIOagueaNrmRpbWVuc2lvblR5cGXojrflj5Z0aW1lRmxhZw0KICAgIGdldFRpbWVGbGFnQnlEaW1lbnNpb25UeXBlKGRpbWVuc2lvblR5cGUpIHsNCiAgICAgIHN3aXRjaChkaW1lbnNpb25UeXBlKSB7DQogICAgICAgIGNhc2UgMTogcmV0dXJuICcwMycgLy8g6L+R5LiJ5Liq5pyIDQogICAgICAgIGNhc2UgMjogcmV0dXJuICcwNicgLy8g6L+R5YWt5Liq5pyIDQogICAgICAgIGNhc2UgMzogcmV0dXJuICcxMicgLy8g6L+R5LiA5bm0DQogICAgICAgIGRlZmF1bHQ6IHJldHVybiAnMDMnDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOajgOafpUVDaGFydHPlj6/nlKjmgKcNCiAgICBjaGVja0VjaGFydHNBdmFpbGFiaWxpdHkoKSB7DQogICAgICBpZiAoIWVjaGFydHMpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcignRUNoYXJ0c+W6k+acquiDveWKoOi9ve+8jOS9v+eUqOWkh+eUqOaYvuekuuaWueW8jycpDQogICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5jaGFydCcpLmZvckVhY2goZWwgPT4gew0KICAgICAgICAgIGVsLmlubmVySFRNTCA9ICc8ZGl2IGNsYXNzPSJjaGFydC1wbGFjZWhvbGRlciI+5Zu+6KGo5Yqg6L295aSx6LSlPC9kaXY+Jw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIHJldHVybiB0cnVlDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluS7quihqOadv+aVsOaNrg0KICAgIGFzeW5jIGZldGNoRGFzaGJvYXJkRGF0YShkaW1lbnNpb25UeXBlUGFyYW0gPSAxKSB7DQogICAgICB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlID0gZGltZW5zaW9uVHlwZVBhcmFtDQoNCiAgICAgIC8vIOa4hemZpOaJgOacieWumuaXtuWZqA0KICAgICAgdGhpcy5jbGVhckFsbEludGVydmFscygpDQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOW5tuihjOiOt+WPluS7quihqOadv+aVsOaNruWSjOWFs+mUruaMh+agh+aVsOaNrg0KICAgICAgICBjb25zdCBbZGFzaGJvYXJkUmVzcG9uc2UsIGtleUluZGljYXRvcnNSZXNwb25zZV0gPSBhd2FpdCBQcm9taXNlLmFsbChbDQogICAgICAgICAgdGhpcy5nZXREYXNoYm9hcmREYXRhKGRpbWVuc2lvblR5cGVQYXJhbSksDQogICAgICAgICAgdGhpcy5nZXRLZXlJbmRpY2F0b3JzKHsgZGltZW5zaW9uVHlwZTogZGltZW5zaW9uVHlwZVBhcmFtIH0pDQogICAgICAgIF0pDQoNCiAgICAgICAgLy8g5aSE55CG5Luq6KGo5p2/5pWw5o2uDQogICAgICAgIGlmIChkYXNoYm9hcmRSZXNwb25zZSAmJiBkYXNoYm9hcmRSZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5kYXNoYm9hcmREYXRhID0gZGFzaGJvYXJkUmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCfojrflj5bku6rooajmnb/mlbDmja7miJDlip86JywgdGhpcy5kYXNoYm9hcmREYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FQSeaVsOaNruagvOW8j+S4jeato+ehruaIlue8uuWwkWRhdGHlrZfmrrUnLCBkYXNoYm9hcmRSZXNwb25zZSkNCiAgICAgICAgICB0aGlzLnNob3dFcnJvck1lc3NhZ2UoJ0FQSeaVsOaNruagvOW8j+S4jeato+ehruaIlue8uuWwkWRhdGHlrZfmrrUnKQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aSE55CG5YWz6ZSu5oyH5qCH5pWw5o2uDQogICAgICAgIGlmIChrZXlJbmRpY2F0b3JzUmVzcG9uc2UgJiYga2V5SW5kaWNhdG9yc1Jlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICB0aGlzLnB1cmNoYXNlU3RhdHMgPSBrZXlJbmRpY2F0b3JzUmVzcG9uc2UuZGF0YSB8fCB7fQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCfojrflj5blhbPplK7mjIfmoIfmlbDmja7miJDlip86JywgdGhpcy5wdXJjaGFzZVN0YXRzKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWFs+mUruaMh+agh+aVsOaNruWksei0pScsIGtleUluZGljYXRvcnNSZXNwb25zZSkNCiAgICAgICAgICB0aGlzLnB1cmNoYXNlU3RhdHMgPSB7fQ0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy5pbml0QWxsQ2hhcnRzKCkNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FQSeivt+axguaIluaVsOaNruWkhOeQhuWksei0pScsIGVycm9yKQ0KICAgICAgICB0aGlzLnNob3dFcnJvck1lc3NhZ2UoJ+aVsOaNruWKoOi9veWksei0pTogJyArIGVycm9yLm1lc3NhZ2UpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOaYvuekuumUmeivr+S/oeaBrw0KICAgIHNob3dFcnJvck1lc3NhZ2UobWVzc2FnZSkgew0KICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLmNoYXJ0JykuZm9yRWFjaChjaGFydCA9PiB7DQogICAgICAgIGNoYXJ0LmlubmVySFRNTCA9IGA8ZGl2IGNsYXNzPSJjaGFydC1wbGFjZWhvbGRlciI+JHttZXNzYWdlfTwvZGl2PmANCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOaXtumXtOi/h+a7pOWZqOWPmOWMluWkhOeQhg0KICAgIGhhbmRsZVRpbWVGaWx0ZXJDaGFuZ2UoZmlsdGVySWQsIGRpbWVuc2lvblR5cGUpIHsNCiAgICAgIHRoaXMuYWN0aXZlRmlsdGVyID0gZmlsdGVySWQNCiAgICAgIHRoaXMuY3VycmVudERpbWVuc2lvblR5cGUgPSBkaW1lbnNpb25UeXBlDQogICAgICBjb25zb2xlLmxvZygn6YCJ5oup55qE5pe26Ze06IyD5Zu0OicsIGZpbHRlcklkLCAn57u05bqmOicsIGRpbWVuc2lvblR5cGUpDQoNCiAgICAgIHRoaXMuY2xlYXJBbGxJbnRlcnZhbHMoKQ0KICAgICAgdGhpcy5mZXRjaERhc2hib2FyZERhdGEoZGltZW5zaW9uVHlwZSkNCiAgICAgIC8vIOWQjOaXtuabtOaWsOmrmOmikeeJqeaWmeaVsOaNrg0KICAgICAgdGhpcy5mZXRjaEhpZ2hGcmVxdWVuY3lNYXRlcmlhbERhdGEoKQ0KICAgICAgLy8g5ZCM5pe25pu05paw5L6b5bqU5ZWG6aOO6Zmp5pWw5o2uDQogICAgICB0aGlzLmZldGNoU3VwcGxpZXJSaXNrRGF0YSgpDQogICAgICAvLyDlkIzml7bmm7TmlrDnianmlpnlhaXlupPnu5/orqHmlbDmja4NCiAgICAgIHRoaXMuZmV0Y2hNYXRlcmlhbFN0YXRpc3RpY3NEYXRhKCkNCiAgICAgIC8vIOazqOaEj++8muS7t+agvOi2i+WKv+aVsOaNruWPquWcqOeUqOaIt+S4u+WKqOeCueWHu+aMiemSruaXtuiOt+WPlu+8jOS4jeWcqOaXtumXtOi/h+a7pOWZqOWPmOWMluaXtuiHquWKqOiOt+WPlg0KDQogICAgICAvLyDlkIzml7bmm7TmlrDmlrDnmoTnianmlpnliJfooajvvIjnlKjkuo7kuIvmi4nmoYbpgInpobnvvInvvIzkvYbkuI3kvJroh6rliqjop6blj5HmlbDmja7ojrflj5YNCiAgICAgIHRoaXMuZmV0Y2hQdXJjaGFzZUFtb3VudE1hdGVyaWFsTGlzdCgpDQogICAgICB0aGlzLmZldGNoTWFya2V0UHJpY2VNYXRlcmlhbExpc3QoKQ0KICAgIH0sDQoNCiAgICAvLyDmuIXpmaTmiYDmnInlrprml7blmagNCiAgICBjbGVhckFsbEludGVydmFscygpIHsNCiAgICAgIE9iamVjdC52YWx1ZXModGhpcy5jaGFydEluc3RhbmNlcykuZm9yRWFjaChpbnN0YW5jZSA9PiB7DQogICAgICAgIGlmIChpbnN0YW5jZSAmJiBpbnN0YW5jZS5pbnRlcnZhbElkKSB7DQogICAgICAgICAgY2xlYXJJbnRlcnZhbChpbnN0YW5jZS5pbnRlcnZhbElkKQ0KICAgICAgICAgIGluc3RhbmNlLmludGVydmFsSWQgPSBudWxsDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOmHjeaWsOiwg+aVtOaJgOacieWbvuihqOWkp+Wwjw0KICAgIHJlc2l6ZUFsbENoYXJ0cygpIHsNCiAgICAgIE9iamVjdC52YWx1ZXModGhpcy5jaGFydEluc3RhbmNlcykuZm9yRWFjaChpbnN0YW5jZSA9PiB7DQogICAgICAgIGlmIChpbnN0YW5jZSkgew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBpbnN0YW5jZS5yZXNpemUoKQ0KICAgICAgICAgIH0gY2F0Y2goZXJyKSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCflm77ooajlpKflsI/osIPmlbTlpLHotKU6JywgZXJyKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5Yid5aeL5YyW5omA5pyJ5Zu+6KGoDQogICAgaW5pdEFsbENoYXJ0cygpIHsNCiAgICAgIGNvbnN0IHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9Mb2NhbGVUaW1lU3RyaW5nKCkNCiAgICAgIGNvbnNvbGUubG9nKGBpbml0QWxsQ2hhcnRzIHN0YXJ0ZWQgWyR7dGltZXN0YW1wfV1gKQ0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5rOo5oSP77ya5a6e5pe25bqT5a2Y5Zu+6KGo5ZKM55+/54Sm54Wk5bqT5a2Y5Zu+6KGo5Lya5Zyo5ZCE6Ieq5pWw5o2u6I635Y+W5a6M5oiQ5ZCO5Y2V54us5Yid5aeL5YyWDQogICAgICAgIC8vIOazqOaEj++8muesrOS4gOS4quiuouWNleiHs+WFpeW6k+WkqeaVsOWbvuihqOS9v+eUqOecn+WunuaVsOaNru+8jOS8muWcqGZldGNoT3JkZXJUb1JlY2VpcHREYXRh5a6M5oiQ5ZCO5Y2V54us5Yid5aeL5YyWDQogICAgICAgIC8vIOazqOaEj++8mueJqeaWmeWFpeW6k+e7n+iuoeWbvuihqOS8muWcqGZldGNoTWF0ZXJpYWxTdGF0aXN0aWNzRGF0YeWujOaIkOWQjuWNleeLrOWIneWni+WMlg0KICAgICAgICAvLyDms6jmhI/vvJrmnLrml4HlupPlrZjlm77ooajkvJrlnKhmZXRjaEZhY3RvcnlTdG9ja0RhdGHlrozmiJDlkI7ljZXni6zliJ3lp4vljJYNCg0KICAgICAgICAvLyDliJ3lp4vljJblhbbku5borqLljZXoh7PlhaXlupPlpKnmlbDlm77ooajvvIjnrKzkuIDkuKrkvJrlnKjmlbDmja7ojrflj5blrozmiJDlkI7ljZXni6zliJ3lp4vljJbvvIkNCiAgICAgICAgdGhpcy5pbml0RmFjdG9yeVN0b2NrQ2hhcnQoKQ0KICAgICAgICB0aGlzLmluaXRDb2tpbmdDb2FsTGluZUNoYXJ0KCkNCiAgICAgICAgdGhpcy5pbml0TWF0ZXJpYWxTdGF0aXN0aWNzQ2hhcnQoKQ0KDQogICAgICAgIC8vIOWIneWni+WMlueJqeaWmeivjeS6keWbvg0KICAgICAgICB0aGlzLmluaXRNYXRlcmlhbENsb3VkKCkNCg0KICAgICAgICAvLyDliJ3lp4vljJZUT1DkvpvlupTllYblm74NCiAgICAgICAgdGhpcy5pbml0VG9wU3VwcGxpZXJzQ2hhcnQoKQ0KICAgICAgICB0aGlzLnBvcHVsYXRlSXRlbURyb3Bkb3duKCd0b3BTdXBwbGllcnNGaWx0ZXInLCAxLCAndG9wU3VwcGxpZXJzT3B0aW9ucycpDQoNCiAgICAgICAgLy8g5rOo5oSP77ya5L6b5bqU5ZWG6aOO6Zmp5Zu+6KGo5Lya5ZyoZmV0Y2hTdXBwbGllclJpc2tEYXRh5a6M5oiQ5ZCO5Y2V54us5Yid5aeL5YyWDQoNCiAgICAgICAgLy8g5rOo5oSP77ya5a6e5pe26LaF5pyf5pWw5Zu+6KGo5Lya5ZyoZmV0Y2hPdmVyZHVlRGF0YeWujOaIkOWQjuWNleeLrOWIneWni+WMlg0KDQogICAgICAgIC8vIOazqOaEj++8mumHh+i0reS7t+agvOi2i+WKv+WbvuS8muWcqGZldGNoUHJpY2VBbmRTdG9yZURhdGHlrozmiJDlkI7ljZXni6zliJ3lp4vljJYNCg0KICAgICAgICBjb25zb2xlLmxvZygn5omA5pyJ5Zu+6KGo5Yid5aeL5YyW5a6M5oiQJykNCiAgICAgIH0gY2F0Y2ggKGVycikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCflm77ooajliJ3lp4vljJbkuLvmtYHnqIvlpLHotKU6JywgZXJyKQ0KICAgICAgICB0aGlzLnNob3dFcnJvck1lc3NhZ2UoJ+WbvuihqOWIneWni+WMluWksei0pTogJyArIGVyci5tZXNzYWdlKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDloavlhYXnianmlpnnsbvlnovkuIvmi4nmoYYNCiAgICBhc3luYyBwb3B1bGF0ZUl0ZW1Ecm9wZG93bihzZWxlY3RFbGVtZW50SWQsIGl0ZW1UeXBlLCBkYXRhUHJvcGVydHlOYW1lKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuZ2V0SXRlbVR5cGVMaXN0KGl0ZW1UeXBlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICB0aGlzW2RhdGFQcm9wZXJ0eU5hbWVdID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEludmFsaWQgZGF0YSBmb3JtYXQgZnJvbSBzaG93SXRlbVR5cGVMaXN0IGZvciBpdGVtVHlwZSAke2l0ZW1UeXBlfTpgLCByZXNwb25zZSkNCiAgICAgICAgICB0aGlzW2RhdGFQcm9wZXJ0eU5hbWVdID0gW10NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcihgRXJyb3IgZmV0Y2hpbmcgaXRlbSB0eXBlcyBmb3IgJHtzZWxlY3RFbGVtZW50SWR9OmAsIGVycm9yKQ0KICAgICAgICB0aGlzW2RhdGFQcm9wZXJ0eU5hbWVdID0gW10NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5LiL5ouJ5qGG5Y+Y5YyW5aSE55CG5pa55rOVDQogICAgYXN5bmMgaGFuZGxlVG9wU3VwcGxpZXJzRmlsdGVyQ2hhbmdlKCkgew0KICAgICAgYXdhaXQgdGhpcy5yZWZyZXNoVG9wU3VwcGxpZXJzQ2hhcnQoKQ0KICAgIH0sDQoNCiAgICBhc3luYyBoYW5kbGVPcmRlclR5cGVDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn5o6S5bqP57G75Z6L5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRPcmRlclR5cGUpDQogICAgICBhd2FpdCB0aGlzLnJlZnJlc2hUb3BTdXBwbGllcnNDaGFydCgpDQogICAgfSwNCg0KICAgIGFzeW5jIHJlZnJlc2hUb3BTdXBwbGllcnNDaGFydCgpIHsNCiAgICAgIGNvbnNvbGUubG9nKGBUb3Agc3VwcGxpZXIgZmlsdGVyIHNlbGVjdGVkIGl0ZW0gSUQ6ICR7dGhpcy5zZWxlY3RlZFRvcFN1cHBsaWVyc0ZpbHRlcn0sIG9yZGVyVHlwZTogJHt0aGlzLnNlbGVjdGVkT3JkZXJUeXBlfWApDQogICAgICBjb25zdCBteUNoYXJ0ID0gdGhpcy5jaGFydEluc3RhbmNlcy50b3BTdXBwbGllcnNDaGFydA0KICAgICAgaWYgKCFteUNoYXJ0KSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIlRPUDEw5L6b5bqU5ZWG5Zu+6KGo5a6e5L6L5pyq5om+5YiwIikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIGlmIChteUNoYXJ0LmludGVydmFsSWQpIHsNCiAgICAgICAgY2xlYXJJbnRlcnZhbChteUNoYXJ0LmludGVydmFsSWQpDQogICAgICAgIG15Q2hhcnQuaW50ZXJ2YWxJZCA9IG51bGwNCiAgICAgIH0NCg0KICAgICAgaWYgKCF0aGlzLnNlbGVjdGVkVG9wU3VwcGxpZXJzRmlsdGVyIHx8IHRoaXMuc2VsZWN0ZWRUb3BTdXBwbGllcnNGaWx0ZXIgPT09ICIiKSB7DQogICAgICAgIC8vIOS9v+eUqOWOn+Wni+aVsOaNru+8jOS9humcgOimgeagueaNrm9yZGVyVHlwZemHjeaWsOiOt+WPlg0KICAgICAgICBteUNoYXJ0LnNob3dMb2FkaW5nKCkNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuZ2V0U3VwcGxpZXJMaXN0KHsNCiAgICAgICAgICAgIGRpbWVuc2lvblR5cGU6IHRoaXMuY3VycmVudERpbWVuc2lvblR5cGUsDQogICAgICAgICAgICBvcmRlclR5cGU6IHRoaXMuc2VsZWN0ZWRPcmRlclR5cGUNCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgbGV0IG5ld1N1cHBsaWVyRGF0YSA9IFtdDQogICAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgew0KICAgICAgICAgICAgbmV3U3VwcGxpZXJEYXRhID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCfku45zaG93U3VwcExpc3QgQVBJ6I635Y+W55qE5pWw5o2u5peg5pWIOicsIHJlc3BvbnNlKQ0KICAgICAgICAgICAgbmV3U3VwcGxpZXJEYXRhID0gdGhpcy5vcmlnaW5hbFRvcFN1cHBsaWVyc0RhdGENCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5yZW5kZXJBbmRQYWdpbmF0ZVRvcFN1cHBsaWVycyhteUNoYXJ0LCBuZXdTdXBwbGllckRhdGEpDQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcihg5Li6dG9wU3VwcGxpZXJzQ2hhcnTojrflj5bkvpvlupTllYbliJfooajlpLHotKU6YCwgZXJyb3IpDQogICAgICAgICAgdGhpcy5yZW5kZXJBbmRQYWdpbmF0ZVRvcFN1cHBsaWVycyhteUNoYXJ0LCB0aGlzLm9yaWdpbmFsVG9wU3VwcGxpZXJzRGF0YSkNCiAgICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgICBteUNoYXJ0LmhpZGVMb2FkaW5nKCkNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbXlDaGFydC5zaG93TG9hZGluZygpDQogICAgICAgIHRyeSB7DQogICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldFN1cHBsaWVyTGlzdCh7DQogICAgICAgICAgICBkaW1lbnNpb25UeXBlOiB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlLA0KICAgICAgICAgICAgaXRlbUlkOiB0aGlzLnNlbGVjdGVkVG9wU3VwcGxpZXJzRmlsdGVyLA0KICAgICAgICAgICAgb3JkZXJUeXBlOiB0aGlzLnNlbGVjdGVkT3JkZXJUeXBlDQogICAgICAgICAgfSkNCg0KICAgICAgICAgIGxldCBuZXdTdXBwbGllckRhdGEgPSBbXQ0KICAgICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICAgIG5ld1N1cHBsaWVyRGF0YSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY29uc29sZS5lcnJvcign5LuOc2hvd1N1cHBMaXN0IEFQSeiOt+WPlueahOaVsOaNruaXoOaViDonLCByZXNwb25zZSkNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5yZW5kZXJBbmRQYWdpbmF0ZVRvcFN1cHBsaWVycyhteUNoYXJ0LCBuZXdTdXBwbGllckRhdGEpDQogICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcihg5Li6dG9wU3VwcGxpZXJzQ2hhcnTojrflj5bkvpvlupTllYbliJfooajlpLHotKU6YCwgZXJyb3IpDQogICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3RvcFN1cHBsaWVyc0NoYXJ0JykuaW5uZXJIVE1MID0gJzxkaXYgY2xhc3M9ImNoYXJ0LXBsYWNlaG9sZGVyIj7kvpvlupTllYbmlbDmja7liqDovb3lpLHotKU8L2Rpdj4nDQogICAgICAgIH0gZmluYWxseSB7DQogICAgICAgICAgbXlDaGFydC5oaWRlTG9hZGluZygpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6K6+572u5aSn5bCP6LCD5pW06KeC5a+f5ZmoDQogICAgc2V0dXBSZXNpemVPYnNlcnZlcigpIHsNCiAgICAgIGNvbnN0IHJlc2l6ZU9ic2VydmVyID0gbmV3IFJlc2l6ZU9ic2VydmVyKGVudHJpZXMgPT4gew0KICAgICAgICBmb3IgKGxldCBlbnRyeSBvZiBlbnRyaWVzKSB7DQogICAgICAgICAgY29uc3QgY2hhcnRzID0gZW50cnkudGFyZ2V0LnF1ZXJ5U2VsZWN0b3JBbGwoJy5jaGFydCcpDQogICAgICAgICAgY2hhcnRzLmZvckVhY2goY2hhcnQgPT4gew0KICAgICAgICAgICAgaWYgKGNoYXJ0LmlkKSB7DQogICAgICAgICAgICAgIGNvbnN0IGluc3RhbmNlID0gZWNoYXJ0cy5nZXRJbnN0YW5jZUJ5RG9tKGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGNoYXJ0LmlkKSkNCiAgICAgICAgICAgICAgaWYgKGluc3RhbmNlKSB7DQogICAgICAgICAgICAgICAgaW5zdGFuY2UucmVzaXplKCkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5jYXJkJykuZm9yRWFjaChjYXJkID0+IHsNCiAgICAgICAgcmVzaXplT2JzZXJ2ZXIub2JzZXJ2ZShjYXJkKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgdG9nZ2xlRnVsbHNjcmVlbigpIHsNCiAgICAgIGlmIChzY3JlZW5mdWxsICYmIHNjcmVlbmZ1bGwuaXNFbmFibGVkKSB7DQogICAgICAgIHNjcmVlbmZ1bGwudG9nZ2xlKCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfmgqjnmoTmtY/op4jlmajkuI3mlK/mjIHlhajlsY/lip/og70nLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBhc3luYyBoYW5kbGVPcmRlckZhY3RvcnlEZXBDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn6K6i5Y2V6Iez5YWl5bqT5aSp5pWwIC0g5YiG5Y6C5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRPcmRlckZhY3RvcnlEZXApDQogICAgICAvLyDojrflj5bmlrDmlbDmja7lubbmm7TmlrDlm77ooagNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEoKQ0KICAgIH0sDQoNCiAgICBhc3luYyBoYW5kbGVPcmRlck1hdGVyaWFsVHlwZUNoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCforqLljZXoh7PlhaXlupPlpKnmlbAgLSDnianmlpnnsbvlnovlj5jljJY6JywgdGhpcy5zZWxlY3RlZE9yZGVyTWF0ZXJpYWxUeXBlKQ0KICAgICAgLy8g6I635Y+W5paw5pWw5o2u5bm25pu05paw5Zu+6KGoDQogICAgICBhd2FpdCB0aGlzLmZldGNoT3JkZXJUb1JlY2VpcHREYXRhKCkNCiAgICB9LA0KDQogICAgLy8g6I635Y+W6K6i5Y2V6Iez5YWl5bqT5aSp5pWw5pWw5o2uDQogICAgYXN5bmMgZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEgLSDlvIDlp4vojrflj5bmlbDmja4nKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEgLSDosIPnlKhBUEk6IHNob3dQdXJjaGFzZVBsYW5MaXN0JykNCg0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHNob3dQdXJjaGFzZVBsYW5MaXN0KHsNCiAgICAgICAgICBkaW1lbnNpb25UeXBlOiAzIC8vIOS9v+eUqOWbuuWumueahOe7tOW6puexu+Weiw0KICAgICAgICB9KQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEgLSBBUEnosIPnlKjmiJDlip8nKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEgLSDlrozmlbTlk43lupQ6JywgcmVzcG9uc2UpDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgew0KICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaE9yZGVyVG9SZWNlaXB0RGF0YSAtIOWOn+Wni+aVsOaNrumVv+W6pjonLCByZXNwb25zZS5kYXRhLmxlbmd0aCkNCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEgLSDljp/lp4vmlbDmja7liY0z5p2hOicsIHJlc3BvbnNlLmRhdGEuc2xpY2UoMCwgMykpDQoNCiAgICAgICAgICAvLyDmo4Dmn6XmlbDmja7nu5PmnoQNCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEgLSDmo4Dmn6XmlbDmja7nu5PmnoQnKQ0KDQogICAgICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJY291bnRUeXBl5a2X5q61DQogICAgICAgICAgY29uc3QgaGFzQ291bnRUeXBlID0gcmVzcG9uc2UuZGF0YS5zb21lKGl0ZW0gPT4gaXRlbS5jb3VudFR5cGUgIT09IHVuZGVmaW5lZCkNCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEgLSDmmK/lkKbmnIljb3VudFR5cGXlrZfmrrU6JywgaGFzQ291bnRUeXBlKQ0KDQogICAgICAgICAgbGV0IGRhdGFUb1Byb2Nlc3MgPSByZXNwb25zZS5kYXRhDQoNCiAgICAgICAgICBpZiAoaGFzQ291bnRUeXBlKSB7DQogICAgICAgICAgICAvLyDmo4Dmn6XmiYDmnInlj6/og73nmoRjb3VudFR5cGXlgLwNCiAgICAgICAgICAgIGNvbnN0IGNvdW50VHlwZXMgPSBbLi4ubmV3IFNldChyZXNwb25zZS5kYXRhLm1hcChpdGVtID0+IGl0ZW0uY291bnRUeXBlKSldDQogICAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEgLSDmiYDmnIljb3VudFR5cGXlgLw6JywgY291bnRUeXBlcykNCg0KICAgICAgICAgICAgLy8g562b6YCJY291bnRUeXBlPSJCIueahOaVsOaNrg0KICAgICAgICAgICAgY29uc3QgZmlsdGVyZWREYXRhID0gcmVzcG9uc2UuZGF0YS5maWx0ZXIoaXRlbSA9PiBpdGVtLmNvdW50VHlwZSA9PT0gJ0InKQ0KICAgICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoT3JkZXJUb1JlY2VpcHREYXRhIC0g562b6YCJ5ZCO55qE5pWw5o2uOicsIGZpbHRlcmVkRGF0YSkNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaE9yZGVyVG9SZWNlaXB0RGF0YSAtIOetm+mAieWQjuaVsOaNrumVv+W6pjonLCBmaWx0ZXJlZERhdGEubGVuZ3RoKQ0KDQogICAgICAgICAgICBpZiAoZmlsdGVyZWREYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ2ZldGNoT3JkZXJUb1JlY2VpcHREYXRhIC0g5rKh5pyJ5om+5YiwY291bnRUeXBlPSJCIueahOaVsOaNru+8jOS9v+eUqOaJgOacieaVsOaNricpDQogICAgICAgICAgICAgIGRhdGFUb1Byb2Nlc3MgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBkYXRhVG9Qcm9jZXNzID0gZmlsdGVyZWREYXRhDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaE9yZGVyVG9SZWNlaXB0RGF0YSAtIOayoeaciWNvdW50VHlwZeWtl+aute+8jOS9v+eUqOaJgOacieaVsOaNricpDQogICAgICAgICAgfQ0KDQogICAgICAgICAgaWYgKGRhdGFUb1Byb2Nlc3MubGVuZ3RoID09PSAwKSB7DQogICAgICAgICAgICBjb25zb2xlLndhcm4oJ2ZldGNoT3JkZXJUb1JlY2VpcHREYXRhIC0g5rKh5pyJ5Y+v55So5pWw5o2u77yM5LiN5pi+56S65Zu+6KGoJykNCiAgICAgICAgICAgIHRoaXMub3JkZXJUb1JlY2VpcHREYXRhID0gW10NCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgLy8g5qOA5p+l56ys5LiA5p2h5pWw5o2u55qE5omA5pyJ5a2X5q61DQogICAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEgLSDnrKzkuIDmnaHmlbDmja7nmoTmiYDmnInlrZfmrrU6JywgT2JqZWN0LmtleXMoZGF0YVRvUHJvY2Vzc1swXSkpDQogICAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hPcmRlclRvUmVjZWlwdERhdGEgLSDnrKzkuIDmnaHmlbDmja7lhoXlrrk6JywgZGF0YVRvUHJvY2Vzc1swXSkNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaE9yZGVyVG9SZWNlaXB0RGF0YSAtIOesrOS4gOadoeaVsOaNrueahG1pZERheXPlrZfmrrU6JywgZGF0YVRvUHJvY2Vzc1swXS5taWREYXlzKQ0KDQogICAgICAgICAgICAvLyDmjIlwZXJpb2TmjpLluo/vvIjku47lsI/liLDlpKfvvIkNCiAgICAgICAgICAgIGNvbnN0IHNvcnRlZERhdGEgPSBkYXRhVG9Qcm9jZXNzLnNvcnQoKGEsIGIpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgcGVyaW9kQSA9IHBhcnNlSW50KGEucGVyaW9kKSB8fCAwDQogICAgICAgICAgICAgIGNvbnN0IHBlcmlvZEIgPSBwYXJzZUludChiLnBlcmlvZCkgfHwgMA0KICAgICAgICAgICAgICByZXR1cm4gcGVyaW9kQSAtIHBlcmlvZEINCiAgICAgICAgICAgIH0pDQoNCiAgICAgICAgICAgIC8vIOi9rOaNouaVsOaNruagvOW8j++8jOWwneivleWkmuS4quWPr+iDveeahOWtl+auteWQjQ0KICAgICAgICAgICAgdGhpcy5vcmRlclRvUmVjZWlwdERhdGEgPSBzb3J0ZWREYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgICAgLy8g5bCd6K+V5aSa5Liq5Y+v6IO955qE5bmz5Z2H5aSp5pWw5a2X5q615ZCNDQogICAgICAgICAgICAgIGxldCBhdmdEYXlzID0gMA0KICAgICAgICAgICAgICBpZiAoaXRlbS5hdmdEYXlzICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICBhdmdEYXlzID0gcGFyc2VGbG9hdChpdGVtLmF2Z0RheXMpIHx8IDANCiAgICAgICAgICAgICAgfSBlbHNlIGlmIChpdGVtLmF2Z0RheSAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgYXZnRGF5cyA9IHBhcnNlRmxvYXQoaXRlbS5hdmdEYXkpIHx8IDANCiAgICAgICAgICAgICAgfSBlbHNlIGlmIChpdGVtLmF2ZXJhZ2VEYXlzICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICBhdmdEYXlzID0gcGFyc2VGbG9hdChpdGVtLmF2ZXJhZ2VEYXlzKSB8fCAwDQogICAgICAgICAgICAgIH0gZWxzZSBpZiAoaXRlbS5kYXlzICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICBhdmdEYXlzID0gcGFyc2VGbG9hdChpdGVtLmRheXMpIHx8IDANCiAgICAgICAgICAgICAgfSBlbHNlIGlmIChpdGVtLmRheUNvdW50ICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICBhdmdEYXlzID0gcGFyc2VGbG9hdChpdGVtLmRheUNvdW50KSB8fCAwDQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAvLyDmj5Dlj5bkuK3kvY3mlbDlrZfmrrUNCiAgICAgICAgICAgICAgbGV0IG1pZERheXMgPSAwDQogICAgICAgICAgICAgIGlmIChpdGVtLm1pZERheXMgIT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICAgIG1pZERheXMgPSBwYXJzZUZsb2F0KGl0ZW0ubWlkRGF5cykgfHwgMA0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKGl0ZW0ubWlkRGF5ICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICBtaWREYXlzID0gcGFyc2VGbG9hdChpdGVtLm1pZERheSkgfHwgMA0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKGl0ZW0ubWVkaWFuRGF5cyAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgbWlkRGF5cyA9IHBhcnNlRmxvYXQoaXRlbS5tZWRpYW5EYXlzKSB8fCAwDQogICAgICAgICAgICAgIH0gZWxzZSBpZiAoaXRlbS5tZWRpYW4gIT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICAgIG1pZERheXMgPSBwYXJzZUZsb2F0KGl0ZW0ubWVkaWFuKSB8fCAwDQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg5aSE55CG5pWw5o2u6aG5ICR7aXRlbS5wZXJpb2R9OiDlubPlnYflpKnmlbAgPSAke2F2Z0RheXN9LCDkuK3kvY3mlbAgPSAke21pZERheXN9YCkNCg0KICAgICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIHBlcmlvZDogdGhpcy5mb3JtYXRQZXJpb2QoaXRlbS5wZXJpb2QpLA0KICAgICAgICAgICAgICAgIGF2Z0RheXM6IGF2Z0RheXMsDQogICAgICAgICAgICAgICAgbWlkRGF5czogbWlkRGF5cywNCiAgICAgICAgICAgICAgICAvLyDmt7vliqDljp/lp4vmlbDmja7nlKjkuo7osIPor5UNCiAgICAgICAgICAgICAgICBvcmlnaW5hbERhdGE6IGl0ZW0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoT3JkZXJUb1JlY2VpcHREYXRhIC0g5aSE55CG5ZCO55qE5pWw5o2uOicsIHRoaXMub3JkZXJUb1JlY2VpcHREYXRhKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5borqLljZXoh7PlhaXlupPlpKnmlbDmlbDmja7lpLHotKXvvIzkuI3mmL7npLrlm77ooagnLCByZXNwb25zZSkNCiAgICAgICAgICB0aGlzLm9yZGVyVG9SZWNlaXB0RGF0YSA9IFtdDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluiuouWNleiHs+WFpeW6k+WkqeaVsOaVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgICAgY29uc29sZS5lcnJvcign6ZSZ6K+v6K+m5oOFOicsIGVycm9yLm1lc3NhZ2UpDQogICAgICAgIHRoaXMub3JkZXJUb1JlY2VpcHREYXRhID0gW10NCiAgICAgIH0NCg0KICAgICAgLy8g5pWw5o2u6I635Y+W5a6M5oiQ5ZCO6YeN5paw5Yid5aeL5YyW5Zu+6KGoDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuaW5pdE1vbnRobHlJbnZlbnRvcnlDaGFydCgpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDmoLzlvI/ljJbmnJ/pl7TvvIgyMDI1MDggLT4gMjAyNS4477yJDQogICAgZm9ybWF0UGVyaW9kKHBlcmlvZCkgew0KICAgICAgaWYgKCFwZXJpb2QpIHJldHVybiAnJw0KICAgICAgY29uc3QgcGVyaW9kU3RyID0gcGVyaW9kLnRvU3RyaW5nKCkNCiAgICAgIGlmIChwZXJpb2RTdHIubGVuZ3RoID09PSA2KSB7DQogICAgICAgIGNvbnN0IHllYXIgPSBwZXJpb2RTdHIuc3Vic3RyaW5nKDAsIDQpDQogICAgICAgIGNvbnN0IG1vbnRoID0gcGFyc2VJbnQocGVyaW9kU3RyLnN1YnN0cmluZyg0LCA2KSkNCiAgICAgICAgcmV0dXJuIGAke3llYXJ9LiR7bW9udGh9YA0KICAgICAgfQ0KICAgICAgcmV0dXJuIHBlcmlvZFN0cg0KICAgIH0sDQoNCg0KDQogICAgLy8g56ys5LqM5Liq6K6i5Y2V6Iez5YWl5bqT5aSp5pWw5qih5Z2X55qE5LqL5Lu25aSE55CGDQogICAgYXN5bmMgaGFuZGxlQ29raW5nQ29hbEZhY3RvcnlEZXBDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn56ys5LqM5Liq6K6i5Y2V6Iez5YWl5bqT5aSp5pWwIC0g5YiG5Y6C5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRDb2tpbmdDb2FsRmFjdG9yeURlcCkNCiAgICAgIC8vIOaaguaXtuS4jeWPluaVsOaNru+8jOWPquabtOaWsOWbvuihqOaYvuekug0KICAgICAgdGhpcy5pbml0Q29raW5nQ29hbExpbmVDaGFydCgpDQogICAgfSwNCg0KICAgIGFzeW5jIGhhbmRsZUNva2luZ0NvYWxNYXRlcmlhbFR5cGVDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn56ys5LqM5Liq6K6i5Y2V6Iez5YWl5bqT5aSp5pWwIC0g54mp5paZ57G75Z6L5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRDb2tpbmdDb2FsTWF0ZXJpYWxUeXBlKQ0KICAgICAgLy8g5pqC5pe25LiN5Y+W5pWw5o2u77yM5Y+q5pu05paw5Zu+6KGo5pi+56S6DQogICAgICB0aGlzLmluaXRDb2tpbmdDb2FsTGluZUNoYXJ0KCkNCiAgICB9LA0KDQogICAgLy8g56ys5LiJ5Liq6K6i5Y2V6Iez5YWl5bqT5aSp5pWw5qih5Z2X55qE5LqL5Lu25aSE55CGDQogICAgYXN5bmMgaGFuZGxlTWF0ZXJpYWxGYWN0b3J5RGVwQ2hhbmdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+esrOS4ieS4quiuouWNleiHs+WFpeW6k+WkqeaVsCAtIOWIhuWOguWPmOWMljonLCB0aGlzLnNlbGVjdGVkTWF0ZXJpYWxGYWN0b3J5RGVwKQ0KICAgICAgLy8g5pqC5pe25LiN5Y+W5pWw5o2u77yM5Y+q5pu05paw5Zu+6KGo5pi+56S6DQogICAgICB0aGlzLmluaXRNYXRlcmlhbFN0YXRpc3RpY3NDaGFydCgpDQogICAgfSwNCg0KICAgIGFzeW5jIGhhbmRsZU1hdGVyaWFsTWF0ZXJpYWxUeXBlQ2hhbmdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+esrOS4ieS4quiuouWNleiHs+WFpeW6k+WkqeaVsCAtIOeJqeaWmeexu+Wei+WPmOWMljonLCB0aGlzLnNlbGVjdGVkTWF0ZXJpYWxNYXRlcmlhbFR5cGUpDQogICAgICAvLyDmmoLml7bkuI3lj5bmlbDmja7vvIzlj6rmm7TmlrDlm77ooajmmL7npLoNCiAgICAgIHRoaXMuaW5pdE1hdGVyaWFsU3RhdGlzdGljc0NoYXJ0KCkNCiAgICB9LA0KDQogICAgLy8g5a6e5pe26LaF5pyf5pWw55u45YWz5pa55rOVDQogICAgYXN5bmMgaGFuZGxlT3ZlcmR1ZUZhY3RvcnlEZXBDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn5a6e5pe26LaF5pyf5pWwIC0g5YiG5Y6C5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRPdmVyZHVlRmFjdG9yeURlcCkNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hPdmVyZHVlRGF0YSgpDQogICAgfSwNCg0KICAgIGFzeW5jIGZldGNoT3ZlcmR1ZURhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDov5nph4zlj6/ku6XmoLnmja5zZWxlY3RlZE92ZXJkdWVGYWN0b3J5RGVw6I635Y+W55yf5a6e5pWw5o2uDQogICAgICAgIC8vIOaaguaXtuS9v+eUqOaooeaLn+aVsOaNrg0KICAgICAgICB0aGlzLm92ZXJkdWVEYXRhID0gdGhpcy5nZXRNb2NrT3ZlcmR1ZURhdGEoKQ0KDQogICAgICAgIC8vIOaVsOaNruiOt+WPluWujOaIkOWQjumHjeaWsOWIneWni+WMluWbvuihqA0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy5pbml0T3ZlcmR1ZUNoYXJ0KCkNCiAgICAgICAgfSkNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlui2heacn+aVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5vdmVyZHVlRGF0YSA9IHRoaXMuZ2V0TW9ja092ZXJkdWVEYXRhKCkNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHRoaXMuaW5pdE92ZXJkdWVDaGFydCgpDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOeUn+aIkOaooeaLn+i2heacn+aVsOaNrg0KICAgIGdldE1vY2tPdmVyZHVlRGF0YSgpIHsNCiAgICAgIHJldHVybiBbDQogICAgICAgIHsgbWF0ZXJpYWxUeXBlOiAn5Y6f5p2Q5paZJywgb3ZlcmR1ZU5vdFJlY2VpdmVkOiAyNSwgb3ZlcmR1ZU5vdFVzZWQ6IDE4IH0sDQogICAgICAgIHsgbWF0ZXJpYWxUeXBlOiAn6L6F6ICQ5p2QJywgb3ZlcmR1ZU5vdFJlY2VpdmVkOiAxMiwgb3ZlcmR1ZU5vdFVzZWQ6IDggfSwNCiAgICAgICAgeyBtYXRlcmlhbFR5cGU6ICfmnZDmlpnnsbsnLCBvdmVyZHVlTm90UmVjZWl2ZWQ6IDM1LCBvdmVyZHVlTm90VXNlZDogMjIgfSwNCiAgICAgICAgeyBtYXRlcmlhbFR5cGU6ICfpgJrnlKjlpIfku7YnLCBvdmVyZHVlTm90UmVjZWl2ZWQ6IDE4LCBvdmVyZHVlTm90VXNlZDogMTUgfSwNCiAgICAgICAgeyBtYXRlcmlhbFR5cGU6ICfkuJPnlKjlpIfku7YnLCBvdmVyZHVlTm90UmVjZWl2ZWQ6IDI4LCBvdmVyZHVlTm90VXNlZDogMjAgfSwNCiAgICAgICAgeyBtYXRlcmlhbFR5cGU6ICflip7lhawnLCBvdmVyZHVlTm90UmVjZWl2ZWQ6IDUsIG92ZXJkdWVOb3RVc2VkOiAzIH0NCiAgICAgIF0NCiAgICB9LA0KDQoNCg0KDQoNCiAgICBhc3luYyBmZXRjaFJlYWxUaW1lSW52ZW50b3J5RGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5nZXRSZWFsVGltZUFtb3VudCgpDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFJlYWxUaW1lSW52ZW50b3J5RGF0YSAtIOWujOaVtOWTjeW6lDonLCByZXNwb25zZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIHRoaXMucmVhbFRpbWVJbnZlbnRvcnlEYXRhID0gcmVzcG9uc2UuZGF0YSB8fCBbXQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFJlYWxUaW1lSW52ZW50b3J5RGF0YSAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLnJlYWxUaW1lSW52ZW50b3J5RGF0YSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5blrp7ml7blupPlrZjmlbDmja7lpLHotKXvvIzkvb/nlKjmqKHmi5/mlbDmja4nLCByZXNwb25zZSkNCiAgICAgICAgICAvLyDkvb/nlKjmqKHmi5/mlbDmja4NCiAgICAgICAgICB0aGlzLnJlYWxUaW1lSW52ZW50b3J5RGF0YSA9IHRoaXMuZ2V0TW9ja1JlYWxUaW1lRGF0YSgpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWunuaXtuW6k+WtmOaVsOaNruWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNrjonLCBlcnJvcikNCiAgICAgICAgLy8g5L2/55So5qih5ouf5pWw5o2uDQogICAgICAgIHRoaXMucmVhbFRpbWVJbnZlbnRvcnlEYXRhID0gdGhpcy5nZXRNb2NrUmVhbFRpbWVEYXRhKCkNCiAgICAgIH0NCg0KICAgICAgLy8g5pWw5o2u6I635Y+W5a6M5oiQ5ZCO6YeN5paw5Yid5aeL5YyW5Zu+6KGoDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuaW5pdFJlYWxUaW1lSW52ZW50b3J5Q2hhcnQoKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g55Sf5oiQ5qih5ouf5a6e5pe25bqT5a2Y5pWw5o2uDQogICAgZ2V0TW9ja1JlYWxUaW1lRGF0YSgpIHsNCiAgICAgIHJldHVybiBbDQogICAgICAgIHsNCiAgICAgICAgICBtYXRlcmlhbFR5cGU6ICdBJywNCiAgICAgICAgICBtYXRlcmlhbE5hbWU6ICfpgJrnlKjlpIfku7YnLA0KICAgICAgICAgIGNlbnRlckludmVudG9yeUFtb3VudDogMTI1MC4zMCwNCiAgICAgICAgICBtYWNoaW5lU2lkZUludmVudG9yeUFtb3VudDogMzgwLjUwLA0KICAgICAgICAgIHRvdGFsSW52ZW50b3J5QW1vdW50OiAxNjMwLjgwDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBtYXRlcmlhbFR5cGU6ICdCJywNCiAgICAgICAgICBtYXRlcmlhbE5hbWU6ICfkuJPnlKjlpIfku7YnLA0KICAgICAgICAgIGNlbnRlckludmVudG9yeUFtb3VudDogOTgwLjc1LA0KICAgICAgICAgIG1hY2hpbmVTaWRlSW52ZW50b3J5QW1vdW50OiA0MjAuMjUsDQogICAgICAgICAgdG90YWxJbnZlbnRvcnlBbW91bnQ6IDE0MDEuMDANCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG1hdGVyaWFsVHlwZTogJ0MnLA0KICAgICAgICAgIG1hdGVyaWFsTmFtZTogJ+adkOaWmeexuycsDQogICAgICAgICAgY2VudGVySW52ZW50b3J5QW1vdW50OiAyMTUwLjYwLA0KICAgICAgICAgIG1hY2hpbmVTaWRlSW52ZW50b3J5QW1vdW50OiA2NTAuNDAsDQogICAgICAgICAgdG90YWxJbnZlbnRvcnlBbW91bnQ6IDI4MDEuMDANCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG1hdGVyaWFsVHlwZTogJ0QnLA0KICAgICAgICAgIG1hdGVyaWFsTmFtZTogJ+WOn+adkOaWmScsDQogICAgICAgICAgY2VudGVySW52ZW50b3J5QW1vdW50OiAzMjAwLjkwLA0KICAgICAgICAgIG1hY2hpbmVTaWRlSW52ZW50b3J5QW1vdW50OiA4OTAuMTAsDQogICAgICAgICAgdG90YWxJbnZlbnRvcnlBbW91bnQ6IDQwOTEuMDANCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG1hdGVyaWFsVHlwZTogJ0UnLA0KICAgICAgICAgIG1hdGVyaWFsTmFtZTogJ+i+heiAkOadkCcsDQogICAgICAgICAgY2VudGVySW52ZW50b3J5QW1vdW50OiAxNTgwLjQwLA0KICAgICAgICAgIG1hY2hpbmVTaWRlSW52ZW50b3J5QW1vdW50OiAzMjAuNjAsDQogICAgICAgICAgdG90YWxJbnZlbnRvcnlBbW91bnQ6IDE5MDEuMDANCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG1hdGVyaWFsVHlwZTogJ0cnLA0KICAgICAgICAgIG1hdGVyaWFsTmFtZTogJ+WKnuWFrCcsDQogICAgICAgICAgY2VudGVySW52ZW50b3J5QW1vdW50OiAxNTAuMjAsDQogICAgICAgICAgbWFjaGluZVNpZGVJbnZlbnRvcnlBbW91bnQ6IDUwLjgwLA0KICAgICAgICAgIHRvdGFsSW52ZW50b3J5QW1vdW50OiAyMDEuMDANCiAgICAgICAgfQ0KICAgICAgXQ0KICAgIH0sDQoNCiAgICBhc3luYyBmZXRjaENva2luZ0NvYWxJbnZlbnRvcnlEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldENva2luZ0NvYWxBbW91bnQoKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hDb2tpbmdDb2FsSW52ZW50b3J5RGF0YSAtIOWujOaVtOWTjeW6lDonLCByZXNwb25zZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIHRoaXMuY29raW5nQ29hbEludmVudG9yeURhdGEgPSByZXNwb25zZS5kYXRhIHx8IFtdDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoQ29raW5nQ29hbEludmVudG9yeURhdGEgLSDorr7nva7nmoTmlbDmja46JywgdGhpcy5jb2tpbmdDb2FsSW52ZW50b3J5RGF0YSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnn7/nhKbnhaTlupPlrZjmlbDmja7lpLHotKUnLCByZXNwb25zZSkNCiAgICAgICAgICB0aGlzLmNva2luZ0NvYWxJbnZlbnRvcnlEYXRhID0gW10NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W55+/54Sm54Wk5bqT5a2Y5pWw5o2u5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLmNva2luZ0NvYWxJbnZlbnRvcnlEYXRhID0gW10NCiAgICAgIH0NCg0KICAgICAgLy8g5pWw5o2u6I635Y+W5a6M5oiQ5ZCO6YeN5paw5Yid5aeL5YyW5Zu+6KGoDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuaW5pdENva2luZ0NvYWxJbnZlbnRvcnlDaGFydCgpDQogICAgICB9KQ0KICAgIH0sDQoNCg0KDQogICAgLy8g54mp5paZ5YWl5bqT57uf6K6h55u45YWz5pa55rOVDQogICAgYXN5bmMgaGFuZGxlTWF0ZXJpYWxDYXRlZ29yeUNoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfnianmlpnnsbvliKvlj5jljJY6JywgdGhpcy5zZWxlY3RlZE1hdGVyaWFsQ2F0ZWdvcnkpDQogICAgICB0aGlzLnNlbGVjdGVkTWF0ZXJpYWxJdGVtID0gJycgLy8g6YeN572u56ys5LqM5Liq5LiL5ouJ5qGGDQogICAgICBhd2FpdCB0aGlzLnVwZGF0ZU1hdGVyaWFsSXRlbU9wdGlvbnMoKQ0KICAgICAgYXdhaXQgdGhpcy5mZXRjaE1hdGVyaWFsU3RhdGlzdGljc0RhdGEoKQ0KICAgIH0sDQoNCiAgICBhc3luYyBoYW5kbGVNYXRlcmlhbEl0ZW1DaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn54mp5paZ6aG555uu5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRNYXRlcmlhbEl0ZW0pDQogICAgICBhd2FpdCB0aGlzLmZldGNoTWF0ZXJpYWxTdGF0aXN0aWNzRGF0YSgpDQogICAgfSwNCg0KICAgIGFzeW5jIHVwZGF0ZU1hdGVyaWFsSXRlbU9wdGlvbnMoKSB7DQogICAgICBpZiAodGhpcy5zZWxlY3RlZE1hdGVyaWFsQ2F0ZWdvcnkgPT09ICcxJykgew0KICAgICAgICAvLyDlpKfnsbvvvJrlj6rmnInlhajpg6jpgInpobkNCiAgICAgICAgdGhpcy5tYXRlcmlhbEl0ZW1PcHRpb25zID0gW10NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOS4reexu+OAgee7huexu+OAgeWPtuexu++8muiOt+WPluWvueW6lOeahOmAiemhuQ0KICAgICAgICBjb25zdCBpdGVtVHlwZSA9IHBhcnNlSW50KHRoaXMuc2VsZWN0ZWRNYXRlcmlhbENhdGVnb3J5KSAtIDEgLy8gMS0+MCwgMi0+MSwgMy0+MiwgNC0+Mw0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5nZXRJdGVtVHlwZUxpc3QoaXRlbVR5cGUpDQogICAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgew0KICAgICAgICAgICAgdGhpcy5tYXRlcmlhbEl0ZW1PcHRpb25zID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLm1hdGVyaWFsSXRlbU9wdGlvbnMgPSBbXQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnianmlpnpobnnm67pgInpobnlpLHotKU6JywgZXJyb3IpDQogICAgICAgICAgdGhpcy5tYXRlcmlhbEl0ZW1PcHRpb25zID0gW10NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBhc3luYyBmZXRjaE1hdGVyaWFsU3RhdGlzdGljc0RhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgaXRlbVR5cGU6IHBhcnNlSW50KHRoaXMuc2VsZWN0ZWRNYXRlcmlhbENhdGVnb3J5KSwNCiAgICAgICAgICBkaW1lbnNpb25UeXBlOiB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlDQogICAgICAgIH0NCg0KICAgICAgICAvLyDlpoLmnpzpgInmi6nkuoblhbfkvZPnianmlpnpobnnm67vvIzmt7vliqBpdGVtSWTlj4LmlbANCiAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRNYXRlcmlhbEl0ZW0gJiYgdGhpcy5zZWxlY3RlZE1hdGVyaWFsSXRlbSAhPT0gJycpIHsNCiAgICAgICAgICBwYXJhbXMuaXRlbUlkID0gdGhpcy5zZWxlY3RlZE1hdGVyaWFsSXRlbQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoTWF0ZXJpYWxTdGF0aXN0aWNzRGF0YSAtIOivt+axguWPguaVsDonLCBwYXJhbXMpDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5nZXRNYXRlcmlhbExpc3QocGFyYW1zKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hNYXRlcmlhbFN0YXRpc3RpY3NEYXRhIC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5tYXRlcmlhbFN0YXRpc3RpY3NEYXRhID0gcmVzcG9uc2UuZGF0YSB8fCBbXQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaE1hdGVyaWFsU3RhdGlzdGljc0RhdGEgLSDorr7nva7nmoTmlbDmja46JywgdGhpcy5tYXRlcmlhbFN0YXRpc3RpY3NEYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueJqeaWmee7n+iuoeaVsOaNruWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNricsIHJlc3BvbnNlKQ0KICAgICAgICAgIHRoaXMubWF0ZXJpYWxTdGF0aXN0aWNzRGF0YSA9IHRoaXMuZ2V0TW9ja01hdGVyaWFsU3RhdGlzdGljc0RhdGEoKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnianmlpnnu5/orqHmlbDmja7lpLHotKXvvIzkvb/nlKjmqKHmi5/mlbDmja46JywgZXJyb3IpDQogICAgICAgIHRoaXMubWF0ZXJpYWxTdGF0aXN0aWNzRGF0YSA9IHRoaXMuZ2V0TW9ja01hdGVyaWFsU3RhdGlzdGljc0RhdGEoKQ0KICAgICAgfQ0KDQogICAgICAvLyDmlbDmja7ojrflj5blrozmiJDlkI7ph43mlrDliJ3lp4vljJblm77ooagNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5pbml0TWF0ZXJpYWxTdGF0aXN0aWNzQ2hhcnQoKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g55Sf5oiQ5qih5ouf54mp5paZ57uf6K6h5pWw5o2uDQogICAgZ2V0TW9ja01hdGVyaWFsU3RhdGlzdGljc0RhdGEoKSB7DQogICAgICByZXR1cm4gWw0KICAgICAgICB7IGl0ZW1OYW1lOiAn6YCa55So5aSH5Lu2JywgaW5BbXQ6IDEyNTAuMzAsIGFycml2ZVJhdGU6IDg1LjUgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+S4k+eUqOWkh+S7ticsIGluQW10OiA5ODAuNzUsIGFycml2ZVJhdGU6IDc4LjIgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+adkOaWmeexuycsIGluQW10OiAyMTUwLjYwLCBhcnJpdmVSYXRlOiA5Mi4xIH0sDQogICAgICAgIHsgaXRlbU5hbWU6ICfljp/mnZDmlpknLCBpbkFtdDogMzIwMC45MCwgYXJyaXZlUmF0ZTogODguNyB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn6L6F6ICQ5p2QJywgaW5BbXQ6IDE1ODAuNDAsIGFycml2ZVJhdGU6IDkxLjMgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+WKnuWFrCcsIGluQW10OiAxNTAuMjAsIGFycml2ZVJhdGU6IDk1LjAgfQ0KICAgICAgXQ0KICAgIH0sDQoNCiAgICBhc3luYyBmZXRjaEhpZ2hGcmVxdWVuY3lNYXRlcmlhbERhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgZGltZW5zaW9uVHlwZTogdGhpcy5jdXJyZW50RGltZW5zaW9uVHlwZSwNCiAgICAgICAgICBjb2RlVHlwZTogdGhpcy5zZWxlY3RlZENvZGVUeXBlLA0KICAgICAgICAgIGl0ZW1UeXBlOiB0aGlzLnNlbGVjdGVkSXRlbVR5cGUNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaEhpZ2hGcmVxdWVuY3lNYXRlcmlhbERhdGEgLSDor7fmsYLlj4LmlbA6JywgcGFyYW1zKQ0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuZ2V0SGlnaEZyZXF1ZW5jeU1hdGVyaWFsTGlzdChwYXJhbXMpDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaEhpZ2hGcmVxdWVuY3lNYXRlcmlhbERhdGEgLSDlrozmlbTlk43lupQ6JywgcmVzcG9uc2UpDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICB0aGlzLmhpZ2hGcmVxdWVuY3lNYXRlcmlhbERhdGEgPSByZXNwb25zZS5kYXRhIHx8IFtdDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoSGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YSAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLmhpZ2hGcmVxdWVuY3lNYXRlcmlhbERhdGEpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W6auY6aKR54mp5paZ5pWw5o2u5aSx6LSl77yM5L2/55So5qih5ouf5pWw5o2uJywgcmVzcG9uc2UpDQogICAgICAgICAgdGhpcy5oaWdoRnJlcXVlbmN5TWF0ZXJpYWxEYXRhID0gdGhpcy5nZXRNb2NrSGlnaEZyZXF1ZW5jeURhdGEoKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bpq5jpopHnianmlpnmlbDmja7lpLHotKXvvIzkvb/nlKjmqKHmi5/mlbDmja46JywgZXJyb3IpDQogICAgICAgIHRoaXMuaGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YSA9IHRoaXMuZ2V0TW9ja0hpZ2hGcmVxdWVuY3lEYXRhKCkNCiAgICAgIH0NCg0KICAgICAgLy8g5pWw5o2u6I635Y+W5a6M5oiQ5ZCO6YeN5paw5Yid5aeL5YyW5Zu+6KGoDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuaW5pdE1hdGVyaWFsQ2xvdWQoKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g55Sf5oiQ5qih5ouf6auY6aKR54mp5paZ5pWw5o2uDQogICAgZ2V0TW9ja0hpZ2hGcmVxdWVuY3lEYXRhKCkgew0KICAgICAgcmV0dXJuIFsNCiAgICAgICAgeyBpdGVtTmFtZTogJ+eyl+eyiScsIGluQW10OiAzOTI0NjcuMiwgaW5OdW06IDU0MjEyOTMgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+eyvueyiScsIGluQW10OiAyODAzNTAuNSwgaW5OdW06IDQyNTAxODAgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+eQg+WboicsIGluQW10OiAxOTUyMDAuOCwgaW5OdW06IDMxODA5NzAgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+eDp+e7kycsIGluQW10OiAxNTA0MjAuMywgaW5OdW06IDI4OTA1NDAgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+eEpueCrScsIGluQW10OiAxMjU2ODAuNywgaW5OdW06IDIzNTAyMTAgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+eFpOeCrScsIGluQW10OiA5ODc1MC4yLCBpbk51bTogMTk4MDc2MCB9DQogICAgICBdDQogICAgfSwNCg0KICAgIGFzeW5jIGhhbmRsZUNvZGVUeXBlQ2hhbmdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+Wkp+exu+exu+Wei+WPmOWMljonLCB0aGlzLnNlbGVjdGVkQ29kZVR5cGUpDQogICAgICBhd2FpdCB0aGlzLmZldGNoSGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YSgpDQogICAgfSwNCg0KICAgIGFzeW5jIGhhbmRsZUl0ZW1UeXBlQ2hhbmdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+e7tOW6puWPmOWMljonLCB0aGlzLnNlbGVjdGVkSXRlbVR5cGUpDQogICAgICBhd2FpdCB0aGlzLmZldGNoSGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YSgpDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluS+m+W6lOWVhumjjumZqeaVsOaNrg0KICAgIGFzeW5jIGZldGNoU3VwcGxpZXJSaXNrRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICB0aW1lRmxhZzogdGhpcy5nZXRUaW1lRmxhZ0J5RGltZW5zaW9uVHlwZSh0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlKQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoU3VwcGxpZXJSaXNrRGF0YSAtIOivt+axguWPguaVsDonLCBwYXJhbXMpDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5nZXRQdXJjaGFzZVN1cHBSaXNrKHBhcmFtcykNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoU3VwcGxpZXJSaXNrRGF0YSAtIOWujOaVtOWTjeW6lDonLCByZXNwb25zZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIHRoaXMuc3VwcGxpZXJSaXNrRGF0YSA9IHJlc3BvbnNlLmRhdGEgfHwgW10NCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hTdXBwbGllclJpc2tEYXRhIC0g6K6+572u55qE5pWw5o2uOicsIHRoaXMuc3VwcGxpZXJSaXNrRGF0YSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bkvpvlupTllYbpo47pmanmlbDmja7lpLHotKUnLCByZXNwb25zZSkNCiAgICAgICAgICB0aGlzLnN1cHBsaWVyUmlza0RhdGEgPSBbXQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bkvpvlupTllYbpo47pmanmlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuc3VwcGxpZXJSaXNrRGF0YSA9IFtdDQogICAgICB9DQoNCiAgICAgIC8vIOaVsOaNruiOt+WPluWujOaIkOWQjumHjeaWsOWIneWni+WMluWbvuihqA0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLmluaXRTdXBwbGllclJpc2tDaGFydCgpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5blpJrkuKrnianmlpnnmoRBSeS7t+agvOmihOa1iw0KICAgIGFzeW5jIGZldGNoTXVsdGlwbGVQcmljZVByZWRpY3Rpb25zKG1hdGVyaWFsTmFtZXMpIHsNCiAgICAgIHRoaXMucHJlZGljdGlvbkxvYWRpbmcgPSB0cnVlDQogICAgICB0aGlzLnByaWNlUHJlZGljdGlvbnMgPSBbXSAvLyDmuIXnqbrkuYvliY3nmoTpooTmtYvnu5PmnpwNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5bm26KGM6LCD55So5omA5pyJ54mp5paZ55qE6aKE5rWL5o6l5Y+jDQogICAgICAgIGNvbnN0IHByZWRpY3Rpb25Qcm9taXNlcyA9IG1hdGVyaWFsTmFtZXMubWFwKGFzeW5jIChtYXRlcmlhbE5hbWUpID0+IHsNCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgICAgICBtYXRlcmlhbE5hbWU6IG1hdGVyaWFsTmFtZSwNCiAgICAgICAgICAgICAgbWF0ZXJpYWxUeXBlOiAnMScgLy8g6buY6K6k5L2/55So55+/55+z57G75Z6L77yM5Y+v5Lul5qC55o2u6ZyA6KaB6LCD5pW0DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBmZXRjaFByaWNlUHJlZGljdGlvbiAtICR7bWF0ZXJpYWxOYW1lfSDor7fmsYLlj4LmlbA6YCwgcGFyYW1zKQ0KICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRNYXRlcmlhbEZ1dHVyZVByaWNlKHBhcmFtcykNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBmZXRjaFByaWNlUHJlZGljdGlvbiAtICR7bWF0ZXJpYWxOYW1lfSDlrozmlbTlk43lupQ6YCwgcmVzcG9uc2UpDQoNCiAgICAgICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5jb2RlICYmIHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgbWF0ZXJpYWxOYW1lOiBtYXRlcmlhbE5hbWUsDQogICAgICAgICAgICAgICAgcXVlc3Rpb246IHJlc3BvbnNlLmRhdGEucXVlc3Rpb24gfHwgYOWFs+S6jiR7bWF0ZXJpYWxOYW1lfeeahOS7t+agvOmihOa1i2AsDQogICAgICAgICAgICAgICAgcHJlZGljdGlvbjogcmVzcG9uc2UuZGF0YS5hbnN3ZXIgfHwgcmVzcG9uc2UuZGF0YS5wcmVkaWN0aW9uIHx8IHJlc3BvbnNlLm1zZywNCiAgICAgICAgICAgICAgICBzdWNjZXNzOiByZXNwb25zZS5kYXRhLnN1Y2Nlc3MgIT09IGZhbHNlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOiOt+WPliR7bWF0ZXJpYWxOYW1lfeS7t+agvOmihOa1i+aVsOaNruWksei0pWAsIHJlc3BvbnNlKQ0KICAgICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIG1hdGVyaWFsTmFtZTogbWF0ZXJpYWxOYW1lLA0KICAgICAgICAgICAgICAgIHF1ZXN0aW9uOiBg5YWz5LqOJHttYXRlcmlhbE5hbWV955qE5Lu35qC86aKE5rWLYCwNCiAgICAgICAgICAgICAgICBwcmVkaWN0aW9uOiBg6I635Y+WJHttYXRlcmlhbE5hbWV95Lu35qC86aKE5rWL5aSx6LSlYCwNCiAgICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOiOt+WPliR7bWF0ZXJpYWxOYW1lfeS7t+agvOmihOa1i+aVsOaNruWksei0pTpgLCBlcnJvcikNCiAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgIG1hdGVyaWFsTmFtZTogbWF0ZXJpYWxOYW1lLA0KICAgICAgICAgICAgICBxdWVzdGlvbjogYOWFs+S6jiR7bWF0ZXJpYWxOYW1lfeeahOS7t+agvOmihOa1i2AsDQogICAgICAgICAgICAgIHByZWRpY3Rpb246IGDojrflj5Yke21hdGVyaWFsTmFtZX3ku7fmoLzpooTmtYvlpLHotKXvvJoke2Vycm9yLm1lc3NhZ2V9YCwNCiAgICAgICAgICAgICAgc3VjY2VzczogZmFsc2UNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQoNCiAgICAgICAgLy8g562J5b6F5omA5pyJ6aKE5rWL57uT5p6cDQogICAgICAgIGNvbnN0IHJlc3VsdHMgPSBhd2FpdCBQcm9taXNlLmFsbChwcmVkaWN0aW9uUHJvbWlzZXMpDQogICAgICAgIHRoaXMucHJpY2VQcmVkaWN0aW9ucyA9IHJlc3VsdHMNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoTXVsdGlwbGVQcmljZVByZWRpY3Rpb25zIC0g6K6+572u55qE6aKE5rWL5pWw5o2uOicsIHRoaXMucHJpY2VQcmVkaWN0aW9ucykNCg0KICAgICAgICBjb25zdCBzdWNjZXNzQ291bnQgPSByZXN1bHRzLmZpbHRlcihyID0+IHIuc3VjY2VzcykubGVuZ3RoDQogICAgICAgIGNvbnN0IHRvdGFsQ291bnQgPSByZXN1bHRzLmxlbmd0aA0KDQogICAgICAgIGlmIChzdWNjZXNzQ291bnQgPiAwKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKGDmiJDlip/ojrflj5Yke3N1Y2Nlc3NDb3VudH0vJHt0b3RhbENvdW50feS4queJqeaWmeeahOS7t+agvOmihOa1i2ApDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5omA5pyJ54mp5paZ55qE5Lu35qC86aKE5rWL6I635Y+W5aSx6LSlJykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5om56YeP6I635Y+W5Lu35qC86aKE5rWL5pWw5o2u5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmibnph4/ojrflj5bku7fmoLzpooTmtYvlpLHotKXvvJonICsgZXJyb3IubWVzc2FnZSkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMucHJlZGljdGlvbkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5bnianmlpnlkI3np7DliJfooagNCiAgICBhc3luYyBmZXRjaE1hdGVyaWFsTmFtZUxpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgY2F0ZWdvcnk6IHBhcnNlSW50KHRoaXMuc2VsZWN0ZWRNYXRlcmlhbENhdGVnb3J5KQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRNYXRlcmlhbE5hbWVMaXN0KHBhcmFtcykNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoTWF0ZXJpYWxOYW1lTGlzdCAtIOWujOaVtOWTjeW6lDonLCByZXNwb25zZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgdGhpcy5tYXRlcmlhbE5hbWVPcHRpb25zID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaE1hdGVyaWFsTmFtZUxpc3QgLSDorr7nva7nmoTmlbDmja46JywgdGhpcy5tYXRlcmlhbE5hbWVPcHRpb25zKQ0KDQogICAgICAgICAgLy8g6K6+572u6buY6K6k6YCJ5LitUELlnZfvvIzlpoLmnpzlrZjlnKjnmoTor50NCiAgICAgICAgICBjb25zdCBwYk1hdGVyaWFsID0gdGhpcy5tYXRlcmlhbE5hbWVPcHRpb25zLmZpbmQoaXRlbSA9PiBpdGVtLml0ZW1OYW1lID09PSAnUELlnZcnKQ0KICAgICAgICAgIGlmIChwYk1hdGVyaWFsKSB7DQogICAgICAgICAgICB0aGlzLnNlbGVjdGVkTWF0ZXJpYWwgPSAnUELlnZcnDQogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLm1hdGVyaWFsTmFtZU9wdGlvbnMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgLy8g5aaC5p6c5rKh5pyJUELlnZfvvIzpgInmi6nnrKzkuIDkuKoNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRNYXRlcmlhbCA9IHRoaXMubWF0ZXJpYWxOYW1lT3B0aW9uc1swXS5pdGVtTmFtZQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOiOt+WPluS7t+agvOaVsOaNrg0KICAgICAgICAgIHRoaXMuZmV0Y2hQcmljZUFuZFN0b3JlRGF0YSgpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W54mp5paZ5ZCN56ew5YiX6KGo5aSx6LSlJywgcmVzcG9uc2UpDQogICAgICAgICAgdGhpcy5tYXRlcmlhbE5hbWVPcHRpb25zID0gW10NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W54mp5paZ5ZCN56ew5YiX6KGo5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLm1hdGVyaWFsTmFtZU9wdGlvbnMgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5bnianmlpnku7fmoLzlkozph4fotK3ph4/mlbDmja4NCiAgICBhc3luYyBmZXRjaFByaWNlQW5kU3RvcmVEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIGRpbWVuc2lvblR5cGU6IHRoaXMuY3VycmVudERpbWVuc2lvblR5cGUsDQogICAgICAgICAgaXRlbU5hbWU6IHRoaXMuc2VsZWN0ZWRNYXRlcmlhbA0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoUHJpY2VBbmRTdG9yZURhdGEgLSDor7fmsYLlj4LmlbA6JywgcGFyYW1zKQ0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFB1cmNoYXNlUHJpY2VBbmRTdG9yZShwYXJhbXMpDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFByaWNlQW5kU3RvcmVEYXRhIC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkgJiYgcmVzcG9uc2UuZGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhpcy5wcmljZUFuZFN0b3JlRGF0YSA9IHJlc3BvbnNlLmRhdGFbMF0gLy8g5Y+W56ys5LiA5Liq5YWD57SgDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoUHJpY2VBbmRTdG9yZURhdGEgLSDorr7nva7nmoTmlbDmja46JywgdGhpcy5wcmljZUFuZFN0b3JlRGF0YSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bku7fmoLzlkozph4fotK3ph4/mlbDmja7lpLHotKUnLCByZXNwb25zZSkNCiAgICAgICAgICB0aGlzLnByaWNlQW5kU3RvcmVEYXRhID0gbnVsbA0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bku7fmoLzlkozph4fotK3ph4/mlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMucHJpY2VBbmRTdG9yZURhdGEgPSBudWxsDQogICAgICB9DQoNCiAgICAgIC8vIOaVsOaNruiOt+WPluWujOaIkOWQjumHjeaWsOWIneWni+WMluS7t+agvOi2i+WKv+Wbvg0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLmluaXRQcmljZVRyZW5kQ2hhcnQoKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5aSE55CG54mp6LWE57G75Z6L5YiH5o2iDQogICAgYXN5bmMgaGFuZGxlTWF0ZXJpYWxDYXRlZ29yeVR5cGVDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn54mp6LWE57G75Z6L5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRNYXRlcmlhbENhdGVnb3J5KQ0KICAgICAgLy8g6YeN5paw6I635Y+W54mp5paZ5ZCN56ew5YiX6KGoDQogICAgICBhd2FpdCB0aGlzLmZldGNoTWF0ZXJpYWxOYW1lTGlzdCgpDQogICAgfSwNCg0KICAgIC8vIOWkhOeQhueJqeaWmemAieaLqeWPmOWMlg0KICAgIGFzeW5jIGhhbmRsZU1hdGVyaWFsQ2hhbmdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+eJqeaWmemAieaLqeWPmOWMljonLCB0aGlzLnNlbGVjdGVkTWF0ZXJpYWwpDQogICAgICBhd2FpdCB0aGlzLmZldGNoUHJpY2VBbmRTdG9yZURhdGEoKQ0KICAgICAgLy8g5LiN5YaN6Ieq5Yqo6Kem5Y+RQUnpooTmtYvvvIznrYnnlKjmiLfngrnlh7vmjInpkq7lkI7lho3op6blj5ENCiAgICB9LA0KDQogICAgY2FsY3VsYXRlUmVhbFRpbWVJbnZlbnRvcnlUb3RhbCgpIHsNCiAgICAgIGxldCB0b3RhbCA9IDANCiAgICAgIGlmICh0aGlzLnJlYWxUaW1lSW52ZW50b3J5RGF0YSAmJiB0aGlzLnJlYWxUaW1lSW52ZW50b3J5RGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMucmVhbFRpbWVJbnZlbnRvcnlEYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgdG90YWwgKz0gcGFyc2VGbG9hdChpdGVtLnRvdGFsSW52ZW50b3J5QW1vdW50KSB8fCAwDQogICAgICAgIH0pDQogICAgICB9DQogICAgICByZXR1cm4gdG90YWwudG9GaXhlZCgyKQ0KICAgIH0sDQoNCiAgICBjYWxjdWxhdGVDb2tpbmdDb2FsVG90YWwoKSB7DQogICAgICBsZXQgdG90YWwgPSAwDQogICAgICBpZiAodGhpcy5jb2tpbmdDb2FsSW52ZW50b3J5RGF0YSAmJiB0aGlzLmNva2luZ0NvYWxJbnZlbnRvcnlEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgLy8g5om+5Yiw5omA5pyJ5pWw5o2u5Lit55qE5pyA5paw5pel5pyfDQogICAgICAgIGxldCBsYXRlc3REYXRlID0gJycNCiAgICAgICAgdGhpcy5jb2tpbmdDb2FsSW52ZW50b3J5RGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmIChpdGVtLnB1cmNoYXNlQ29raW5nRGFpbHlEZXRhaWxMaXN0ICYmIGl0ZW0ucHVyY2hhc2VDb2tpbmdEYWlseURldGFpbExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgaXRlbS5wdXJjaGFzZUNva2luZ0RhaWx5RGV0YWlsTGlzdC5mb3JFYWNoKGRldGFpbCA9PiB7DQogICAgICAgICAgICAgIGlmIChkZXRhaWwuaW5zdG9ja0RhdGUgPiBsYXRlc3REYXRlKSB7DQogICAgICAgICAgICAgICAgbGF0ZXN0RGF0ZSA9IGRldGFpbC5pbnN0b2NrRGF0ZQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCg0KICAgICAgICAvLyDorqHnrpfmnIDmlrDml6XmnJ/lkITkuKrnianmlpnnmoTlupPlrZjph4/lkIjorqENCiAgICAgICAgdGhpcy5jb2tpbmdDb2FsSW52ZW50b3J5RGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmIChpdGVtLnB1cmNoYXNlQ29raW5nRGFpbHlEZXRhaWxMaXN0ICYmIGl0ZW0ucHVyY2hhc2VDb2tpbmdEYWlseURldGFpbExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgY29uc3QgbGF0ZXN0RGV0YWlsID0gaXRlbS5wdXJjaGFzZUNva2luZ0RhaWx5RGV0YWlsTGlzdC5maW5kKGRldGFpbCA9PiBkZXRhaWwuaW5zdG9ja0RhdGUgPT09IGxhdGVzdERhdGUpDQogICAgICAgICAgICBpZiAobGF0ZXN0RGV0YWlsKSB7DQogICAgICAgICAgICAgIHRvdGFsICs9IHBhcnNlRmxvYXQobGF0ZXN0RGV0YWlsLmludlF0eSkgfHwgMA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIHJldHVybiAodG90YWwgLyAxMDAwMCkudG9GaXhlZCgyKSAvLyDovazmjaLkuLrkuIflkKgNCiAgICB9LA0KDQogICAgLy8g5aSE55CG55+/54Sm54Wk57G75Z6L5LiL5ouJ5qGG5Y+Y5YyWDQogICAgYXN5bmMgaGFuZGxlQ29raW5nQ29hbFR5cGVDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn55+/54Sm54Wk57G75Z6L5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRDb2tpbmdDb2FsVHlwZSkNCiAgICAgIC8vIOmHjeaWsOWIneWni+WMluWbvuihqOS7peW6lOeUqOi/h+a7pA0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLmluaXRDb2tpbmdDb2FsSW52ZW50b3J5Q2hhcnQoKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5py65peB5bqT5a2Y55u45YWz5pa55rOVDQogICAgLy8g6I635Y+W5YiG5Y6C6YCJ6aG55YiX6KGoDQogICAgYXN5bmMgZmV0Y2hGYWN0b3J5RGVwT3B0aW9ucygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0RGVwTmFtZUxpc3QoKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hGYWN0b3J5RGVwT3B0aW9ucyAtIOWujOaVtOWTjeW6lDonLCByZXNwb25zZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgdGhpcy5mYWN0b3J5RGVwT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hGYWN0b3J5RGVwT3B0aW9ucyAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLmZhY3RvcnlEZXBPcHRpb25zKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWIhuWOgumAiemhueWIl+ihqOWksei0pScsIHJlc3BvbnNlKQ0KICAgICAgICAgIHRoaXMuZmFjdG9yeURlcE9wdGlvbnMgPSBbXQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bliIbljoLpgInpobnliJfooajlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuZmFjdG9yeURlcE9wdGlvbnMgPSBbXQ0KICAgICAgfQ0KDQogICAgICAvLyDliIbljoLpgInpobnliqDovb3lrozmiJDlkI7vvIzlm77ooajkvJrlnKhpbml0QWxsQ2hhcnRz5Lit5Yid5aeL5YyWDQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuWIhuWOgumAieaLqeWPmOWMlg0KICAgIGFzeW5jIGhhbmRsZUZhY3RvcnlEZXBDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn6K6i5Y2V6Iez5YWl5bqT5aSp5pWwIC0g5YiG5Y6C6YCJ5oup5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRGYWN0b3J5RGVwKQ0KICAgICAgLy8g5pqC5pe25LiN5Y+W5pWw5o2u77yM5Y+q5pu05paw5Zu+6KGo5pi+56S6DQogICAgICB0aGlzLmluaXRGYWN0b3J5U3RvY2tDaGFydCgpDQogICAgfSwNCg0KICAgIC8vIOWkhOeQhueJqeaWmeexu+Wei+mAieaLqeWPmOWMlg0KICAgIGFzeW5jIGhhbmRsZUZhY3RvcnlNYXRlcmlhbFR5cGVDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn6K6i5Y2V6Iez5YWl5bqT5aSp5pWwIC0g54mp5paZ57G75Z6L6YCJ5oup5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRGYWN0b3J5TWF0ZXJpYWxUeXBlKQ0KICAgICAgLy8g5pqC5pe25LiN5Y+W5pWw5o2u77yM5Y+q5pu05paw5Zu+6KGo5pi+56S6DQogICAgICB0aGlzLmluaXRGYWN0b3J5U3RvY2tDaGFydCgpDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluacuuaXgeW6k+WtmOaVsOaNrg0KICAgIGFzeW5jIGZldGNoRmFjdG9yeVN0b2NrRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IGRlcE5hbWUgPSB0aGlzLnNlbGVjdGVkRmFjdG9yeURlcCB8fCAnJyAvLyDnqbrlrZfnrKbkuLLooajnpLrlhajpg6gNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoRmFjdG9yeVN0b2NrRGF0YSAtIOivt+axguWPguaVsDonLCBkZXBOYW1lKQ0KDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0TGlzdE1vbnRobHkoZGVwTmFtZSkNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoRmFjdG9yeVN0b2NrRGF0YSAtIOWujOaVtOWTjeW6lDonLCByZXNwb25zZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgdGhpcy5mYWN0b3J5U3RvY2tEYXRhID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaEZhY3RvcnlTdG9ja0RhdGEgLSDorr7nva7nmoTmlbDmja46JywgdGhpcy5mYWN0b3J5U3RvY2tEYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluacuuaXgeW6k+WtmOaVsOaNruWksei0pScsIHJlc3BvbnNlKQ0KICAgICAgICAgIHRoaXMuZmFjdG9yeVN0b2NrRGF0YSA9IFtdDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluacuuaXgeW6k+WtmOaVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5mYWN0b3J5U3RvY2tEYXRhID0gW10NCiAgICAgIH0NCg0KICAgICAgLy8g5pWw5o2u6I635Y+W5a6M5oiQ5ZCO6YeN5paw5Yid5aeL5YyW5Zu+6KGoDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuaW5pdEZhY3RvcnlTdG9ja0NoYXJ0KCkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOaWsOWinuaWueazle+8muWkhOeQhumHh+i0remHj+absue6v+eJqeaWmeexu+Wei+WPmOWMlg0KICAgIGFzeW5jIGhhbmRsZVB1cmNoYXNlQW1vdW50Q2F0ZWdvcmllc0NoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfph4fotK3ph4/mm7Lnur/nianmlpnnsbvlnovlj5jljJY6JywgdGhpcy5wdXJjaGFzZUFtb3VudENhdGVnb3JpZXMpDQogICAgICB0aGlzLnNlbGVjdGVkUHVyY2hhc2VBbW91bnRNYXRlcmlhbHMgPSBbXSAvLyDph43nva7pgInkuK3nmoTnianmlpkNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hQdXJjaGFzZUFtb3VudE1hdGVyaWFsTGlzdCgpDQogICAgfSwNCg0KICAgIC8vIOaWsOWinuaWueazle+8muWkhOeQhuW4guWcuuS7t+absue6v+eJqeaWmeexu+Wei+WPmOWMlg0KICAgIGFzeW5jIGhhbmRsZU1hcmtldFByaWNlQ2F0ZWdvcmllc0NoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfluILlnLrku7fmm7Lnur/nianmlpnnsbvlnovlj5jljJY6JywgdGhpcy5tYXJrZXRQcmljZUNhdGVnb3JpZXMpDQogICAgICB0aGlzLnNlbGVjdGVkTWFya2V0UHJpY2VNYXRlcmlhbHMgPSBbXSAvLyDph43nva7pgInkuK3nmoTnianmlpkNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hNYXJrZXRQcmljZU1hdGVyaWFsTGlzdCgpDQogICAgfSwNCg0KICAgIC8vIOaWsOWinuaWueazle+8muiOt+WPlumHh+i0remHj+absue6v+eJqeaWmeWIl+ihqA0KICAgIGFzeW5jIGZldGNoUHVyY2hhc2VBbW91bnRNYXRlcmlhbExpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgY2F0ZWdvcmllczogdGhpcy5wdXJjaGFzZUFtb3VudENhdGVnb3JpZXMsDQogICAgICAgICAgY3VydmVUeXBlOiAyLCAvLyDph4fotK3ph4/mm7Lnur8NCiAgICAgICAgICBkaW1lbnNpb25UeXBlOiB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlDQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hQdXJjaGFzZUFtb3VudE1hdGVyaWFsTGlzdCAtIOivt+axguWPguaVsDonLCBwYXJhbXMpDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0TWF0ZXJpYWxOYW1lTGlzdEZyb21OZXdUYWJsZXMocGFyYW1zKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hQdXJjaGFzZUFtb3VudE1hdGVyaWFsTGlzdCAtIOWujOaVtOWTjeW6lDonLCByZXNwb25zZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgdGhpcy5wdXJjaGFzZUFtb3VudE1hdGVyaWFsT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hQdXJjaGFzZUFtb3VudE1hdGVyaWFsTGlzdCAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLnB1cmNoYXNlQW1vdW50TWF0ZXJpYWxPcHRpb25zKQ0KDQogICAgICAgICAgLy8g5Y+q5Zyo6aG16Z2i5Yid5aeL5YyW5pe277yI56ys5LiA5qyh5Yqg6L295LiU5peg6YCJ5Lit54mp5paZ5pe277yJ6K6+572u6buY6K6k6YCJ5LitUELlnZcNCiAgICAgICAgICBpZiAodGhpcy5zZWxlY3RlZFB1cmNoYXNlQW1vdW50TWF0ZXJpYWxzLmxlbmd0aCA9PT0gMCAmJiAhdGhpcy5oYXNJbml0aWFsaXplZFByaWNlQ2hhcnQpIHsNCiAgICAgICAgICAgIGNvbnN0IHBiTWF0ZXJpYWwgPSB0aGlzLnB1cmNoYXNlQW1vdW50TWF0ZXJpYWxPcHRpb25zLmZpbmQoaXRlbSA9PiBpdGVtLml0ZW1OYW1lID09PSAnUELlnZcnKQ0KICAgICAgICAgICAgaWYgKHBiTWF0ZXJpYWwpIHsNCiAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFB1cmNoYXNlQW1vdW50TWF0ZXJpYWxzID0gWydQQuWdlyddDQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfpu5jorqTpgInkuK1QQuWdlyAtIOmHh+i0remHj+absue6vycpDQoNCiAgICAgICAgICAgICAgLy8g5qOA5p+l5biC5Zy65Lu35puy57q/5piv5ZCm5Lmf5bey57uP6K6+572u5aW96buY6K6k5YC877yM5aaC5p6c5piv5YiZ6Kem5Y+R5pWw5o2u6I635Y+WDQogICAgICAgICAgICAgIHRoaXMuY2hlY2tBbmRUcmlnZ2VySW5pdGlhbERhdGFGZXRjaCgpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumHh+i0remHj+absue6v+eJqeaWmeWIl+ihqOWksei0pScsIHJlc3BvbnNlKQ0KICAgICAgICAgIHRoaXMucHVyY2hhc2VBbW91bnRNYXRlcmlhbE9wdGlvbnMgPSBbXQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bph4fotK3ph4/mm7Lnur/nianmlpnliJfooajlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMucHVyY2hhc2VBbW91bnRNYXRlcmlhbE9wdGlvbnMgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmlrDlop7mlrnms5XvvJrojrflj5bluILlnLrku7fmm7Lnur/nianmlpnliJfooagNCiAgICBhc3luYyBmZXRjaE1hcmtldFByaWNlTWF0ZXJpYWxMaXN0KCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIGNhdGVnb3JpZXM6IHRoaXMubWFya2V0UHJpY2VDYXRlZ29yaWVzLA0KICAgICAgICAgIGN1cnZlVHlwZTogMSwgLy8g5Lu35qC85puy57q/DQogICAgICAgICAgZGltZW5zaW9uVHlwZTogdGhpcy5jdXJyZW50RGltZW5zaW9uVHlwZQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoTWFya2V0UHJpY2VNYXRlcmlhbExpc3QgLSDor7fmsYLlj4LmlbA6JywgcGFyYW1zKQ0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldE1hdGVyaWFsTmFtZUxpc3RGcm9tTmV3VGFibGVzKHBhcmFtcykNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoTWFya2V0UHJpY2VNYXRlcmlhbExpc3QgLSDlrozmlbTlk43lupQ6JywgcmVzcG9uc2UpDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgew0KICAgICAgICAgIHRoaXMubWFya2V0UHJpY2VNYXRlcmlhbE9wdGlvbnMgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoTWFya2V0UHJpY2VNYXRlcmlhbExpc3QgLSDorr7nva7nmoTmlbDmja46JywgdGhpcy5tYXJrZXRQcmljZU1hdGVyaWFsT3B0aW9ucykNCg0KICAgICAgICAgIC8vIOWPquWcqOmhtemdouWIneWni+WMluaXtu+8iOesrOS4gOasoeWKoOi9veS4lOaXoOmAieS4reeJqeaWmeaXtu+8ieiuvue9rum7mOiupOmAieS4rVBC5Z2XDQogICAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRNYXJrZXRQcmljZU1hdGVyaWFscy5sZW5ndGggPT09IDAgJiYgIXRoaXMuaGFzSW5pdGlhbGl6ZWRQcmljZUNoYXJ0KSB7DQogICAgICAgICAgICBjb25zdCBwYk1hdGVyaWFsID0gdGhpcy5tYXJrZXRQcmljZU1hdGVyaWFsT3B0aW9ucy5maW5kKGl0ZW0gPT4gaXRlbS5pdGVtTmFtZSA9PT0gJ1BC5Z2XJykNCiAgICAgICAgICAgIGlmIChwYk1hdGVyaWFsKSB7DQogICAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRNYXJrZXRQcmljZU1hdGVyaWFscyA9IFsnUELlnZcnXQ0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygn6buY6K6k6YCJ5LitUELlnZcgLSDluILlnLrku7fmm7Lnur8nKQ0KDQogICAgICAgICAgICAgIC8vIOajgOafpemHh+i0remHj+absue6v+aYr+WQpuS5n+W3sue7j+iuvue9ruWlvem7mOiupOWAvO+8jOWmguaenOaYr+WImeinpuWPkeaVsOaNruiOt+WPlg0KICAgICAgICAgICAgICB0aGlzLmNoZWNrQW5kVHJpZ2dlckluaXRpYWxEYXRhRmV0Y2goKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bluILlnLrku7fmm7Lnur/nianmlpnliJfooajlpLHotKUnLCByZXNwb25zZSkNCiAgICAgICAgICB0aGlzLm1hcmtldFByaWNlTWF0ZXJpYWxPcHRpb25zID0gW10NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5biC5Zy65Lu35puy57q/54mp5paZ5YiX6KGo5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLm1hcmtldFByaWNlTWF0ZXJpYWxPcHRpb25zID0gW10NCiAgICAgIH0NCiAgICB9LA0KDQoNCg0KICAgIC8vIOaWsOWinuaWueazle+8muiOt+WPlueJqeaWmemHh+i0reS7t+agvOaVsOaNru+8iOeUqOS6juaWsOeahOS7t+agvOi2i+WKv+Wbvu+8iQ0KICAgIGFzeW5jIGZldGNoUHJpY2VBbmRTdG9yZURhdGFGb3JOZXdDaGFydCgpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUHVyY2hhc2VBbW91bnRNYXRlcmlhbHMubGVuZ3RoID09PSAwICYmIHRoaXMuc2VsZWN0ZWRNYXJrZXRQcmljZU1hdGVyaWFscy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7foh7PlsJHpgInmi6nkuIDkuKrnianmlpknKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy5mZXRjaGluZ1ByaWNlRGF0YSA9IHRydWUNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOaehOW7uml0ZW1MaXN0DQogICAgICAgIGNvbnN0IGl0ZW1MaXN0ID0gW10NCg0KICAgICAgICAvLyDmt7vliqDph4fotK3ph4/mm7Lnur/nmoTnianmlpkNCiAgICAgICAgdGhpcy5zZWxlY3RlZFB1cmNoYXNlQW1vdW50TWF0ZXJpYWxzLmZvckVhY2goaXRlbU5hbWUgPT4gew0KICAgICAgICAgIGl0ZW1MaXN0LnB1c2goew0KICAgICAgICAgICAgY3VydmVUeXBlOiAyLCAvLyDph4fotK3ph4/mm7Lnur8NCiAgICAgICAgICAgIGl0ZW1OYW1lOiBpdGVtTmFtZQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQoNCiAgICAgICAgLy8g5re75Yqg5biC5Zy65Lu35puy57q/55qE54mp5paZDQogICAgICAgIHRoaXMuc2VsZWN0ZWRNYXJrZXRQcmljZU1hdGVyaWFscy5mb3JFYWNoKGl0ZW1OYW1lID0+IHsNCiAgICAgICAgICBpdGVtTGlzdC5wdXNoKHsNCiAgICAgICAgICAgIGN1cnZlVHlwZTogMSwgLy8g5Lu35qC85puy57q/DQogICAgICAgICAgICBpdGVtTmFtZTogaXRlbU5hbWUNCiAgICAgICAgICB9KQ0KICAgICAgICB9KQ0KDQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICBkaW1lbnNpb25UeXBlOiB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlLA0KICAgICAgICAgIGl0ZW1MaXN0OiBpdGVtTGlzdA0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoUHJpY2VBbmRTdG9yZURhdGEgLSDor7fmsYLlj4LmlbA6JywgcGFyYW1zKQ0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFB1cmNoYXNlUHJpY2VBbmRTdG9yZUZyb21OZXdUYWJsZXMocGFyYW1zKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hQcmljZUFuZFN0b3JlRGF0YSAtIOWujOaVtOWTjeW6lDonLCByZXNwb25zZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIHRoaXMubmV3UHJpY2VBbmRTdG9yZURhdGEgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoUHJpY2VBbmRTdG9yZURhdGEgLSDorr7nva7nmoTmlbDmja46JywgdGhpcy5uZXdQcmljZUFuZFN0b3JlRGF0YSkNCg0KICAgICAgICAgIC8vIOmHjeaWsOa4suafk+WbvuihqA0KICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuaW5pdE5ld1ByaWNlVHJlbmRDaGFydCgpDQogICAgICAgICAgfSkNCg0KICAgICAgICAgIC8vIOiOt+WPluaJgOaciemAieS4reeJqeaWmeeahOWOu+mHjeWIl+ihqA0KICAgICAgICAgIGNvbnN0IGFsbFNlbGVjdGVkTWF0ZXJpYWxzID0gWy4uLm5ldyBTZXQoWw0KICAgICAgICAgICAgLi4udGhpcy5zZWxlY3RlZFB1cmNoYXNlQW1vdW50TWF0ZXJpYWxzLA0KICAgICAgICAgICAgLi4udGhpcy5zZWxlY3RlZE1hcmtldFByaWNlTWF0ZXJpYWxzDQogICAgICAgICAgXSldDQoNCiAgICAgICAgICAvLyDkuLrmr4/kuKrnianmlpnosIPnlKhBSemihOa1i+aOpeWPow0KICAgICAgICAgIGlmIChhbGxTZWxlY3RlZE1hdGVyaWFscy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLmZldGNoTXVsdGlwbGVQcmljZVByZWRpY3Rpb25zKGFsbFNlbGVjdGVkTWF0ZXJpYWxzKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOWmguaenOW4guWcuuS7t+absue6v+aciemAieS4reeJqeaWme+8jOiOt+WPluebuOS8vOeJqeaWmeS/oeaBrw0KICAgICAgICAgIGlmICh0aGlzLnNlbGVjdGVkTWFya2V0UHJpY2VNYXRlcmlhbHMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy5mZXRjaFNpbWlsYXJNYXRlcmlhbHModGhpcy5zZWxlY3RlZE1hcmtldFByaWNlTWF0ZXJpYWxzKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDmuIXnqbrnm7jkvLznianmlpnmlbDmja4NCiAgICAgICAgICAgIHRoaXMuc2ltaWxhck1hdGVyaWFsc0RhdGEgPSBbXQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5pWw5o2u6I635Y+W5oiQ5YqfJykNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnianmlpnph4fotK3ku7fmoLzmlbDmja7lpLHotKUnLCByZXNwb25zZSkNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bmlbDmja7lpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnianmlpnph4fotK3ku7fmoLzmlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluaVsOaNruWksei0pe+8micgKyBlcnJvci5tZXNzYWdlKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5mZXRjaGluZ1ByaWNlRGF0YSA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluebuOS8vOeJqeaWmeS/oeaBrw0KICAgIGFzeW5jIGZldGNoU2ltaWxhck1hdGVyaWFscyhpdGVtTmFtZXMpIHsNCiAgICAgIHRoaXMuc2ltaWxhck1hdGVyaWFsc0xvYWRpbmcgPSB0cnVlDQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgaXRlbU5hbWVzOiBpdGVtTmFtZXMNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFNpbWlsYXJNYXRlcmlhbHMgLSDor7fmsYLlj4LmlbA6JywgcGFyYW1zKQ0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGxpc3RTaW1pbGFyQnlJdGVtTmFtZXMocGFyYW1zKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hTaW1pbGFyTWF0ZXJpYWxzIC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICB0aGlzLnNpbWlsYXJNYXRlcmlhbHNEYXRhID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFNpbWlsYXJNYXRlcmlhbHMgLSDorr7nva7nmoTmlbDmja46JywgdGhpcy5zaW1pbGFyTWF0ZXJpYWxzRGF0YSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnm7jkvLznianmlpnmlbDmja7lpLHotKUnLCByZXNwb25zZSkNCiAgICAgICAgICB0aGlzLnNpbWlsYXJNYXRlcmlhbHNEYXRhID0gW10NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W55u45Ly854mp5paZ5pWw5o2u5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLnNpbWlsYXJNYXRlcmlhbHNEYXRhID0gW10NCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuc2ltaWxhck1hdGVyaWFsc0xvYWRpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5bmjpLlkI3moLflvI/nsbsNCiAgICBnZXRSYW5rQ2xhc3MocmFuaykgew0KICAgICAgaWYgKHJhbmsgPT09IDEpIHJldHVybiAncmFuay1maXJzdCcNCiAgICAgIGlmIChyYW5rID09PSAyKSByZXR1cm4gJ3Jhbmstc2Vjb25kJw0KICAgICAgaWYgKHJhbmsgPT09IDMpIHJldHVybiAncmFuay10aGlyZCcNCiAgICAgIHJldHVybiAncmFuay1kZWZhdWx0Jw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bllYblk4HliIbnsbvlkI3np7ANCiAgICBnZXRDYXRlZ29yeU5hbWUoY2F0ZWdvcnkpIHsNCiAgICAgIGNvbnN0IGNhdGVnb3J5TWFwID0gew0KICAgICAgICAxOiAn55+/55+zJywNCiAgICAgICAgMjogJ+eFpOeCrScsDQogICAgICAgIDM6ICflkIjph5EnLA0KICAgICAgICA0OiAn5bqf6ZKiJw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGNhdGVnb3J5TWFwW2NhdGVnb3J5XSB8fCAn5pyq55+lJw0KICAgIH0sDQoNCiAgICAvLyDojrflj5bku7fmoLznsbvlnovlkI3np7ANCiAgICBnZXRQcmljZVR5cGVOYW1lKHByaWNlVHlwZSkgew0KICAgICAgY29uc3QgcHJpY2VUeXBlTWFwID0gew0KICAgICAgICAxOiAn546w6LSn5Lu3JywNCiAgICAgICAgMjogJ+W4guWcuumHh+i0reWIsOWOguS7tycsDQogICAgICAgIDM6ICflhbTmvoTlup/pkqLmlLbotK3ku7co6L2m6L+QKScsDQogICAgICAgIDQ6ICflhbTmvoTlup/pkqLmlLbotK3ku7co6Ii56L+QKScsDQogICAgICAgIDU6ICfmspnpkqLlup/pkqLmlLbotK3ku7co6L2m6L+QKScsDQogICAgICAgIDY6ICfmspnpkqLlup/pkqLmlLbotK3ku7co6Ii56L+QKScNCiAgICAgIH0NCiAgICAgIHJldHVybiBwcmljZVR5cGVNYXBbcHJpY2VUeXBlXSB8fCAn5pyq55+lJw0KICAgIH0sDQoNCiAgICAvLyDmiZPlvIDlr7nmr5TlvLnmoYYNCiAgICBvcGVuQ29tcGFyaXNvbkRpYWxvZyhpdGVtKSB7DQogICAgICBjb25zb2xlLmxvZygnb3BlbkNvbXBhcmlzb25EaWFsb2cgLSDkvKDlhaXnmoRpdGVt5pWw5o2uOicsIGl0ZW0pDQogICAgICB0aGlzLmN1cnJlbnRDb21wYXJpc29uID0geyAuLi5pdGVtIH0NCiAgICAgIGNvbnNvbGUubG9nKCdvcGVuQ29tcGFyaXNvbkRpYWxvZyAtIOiuvue9rueahGN1cnJlbnRDb21wYXJpc29uOicsIHRoaXMuY3VycmVudENvbXBhcmlzb24pDQogICAgICB0aGlzLmNvbXBhcmlzb25EaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KDQogICAgICAvLyDlvLnmoYbmiZPlvIDlkI7ojrflj5blr7nmr5TmlbDmja4NCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5mZXRjaENvbXBhcmlzb25EYXRhKCkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOWFs+mXreWvueavlOW8ueahhg0KICAgIGNsb3NlQ29tcGFyaXNvbkRpYWxvZygpIHsNCiAgICAgIHRoaXMuY29tcGFyaXNvbkRpYWxvZ1Zpc2libGUgPSBmYWxzZQ0KICAgICAgdGhpcy5jdXJyZW50Q29tcGFyaXNvbiA9IHt9DQogICAgICB0aGlzLmNvbXBhcmlzb25QcmljZURhdGEgPSBudWxsDQoNCiAgICAgIC8vIOa4heeQhuWbvuihqOWunuS+iw0KICAgICAgaWYgKHRoaXMuY29tcGFyaXNvbkNoYXJ0SW5zdGFuY2UpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmNvbXBhcmlzb25DaGFydEluc3RhbmNlLmRpc3Bvc2UoKQ0KICAgICAgICAgIHRoaXMuY29tcGFyaXNvbkNoYXJ0SW5zdGFuY2UgPSBudWxsDQogICAgICAgIH0gY2F0Y2ggKGVycikgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+a4heeQhuWvueavlOWbvuihqOWunuS+i+Wksei0pTonLCBlcnIpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6I635Y+W5a+55q+U5pWw5o2u77yI54us56uL5a6e546w77yM5LiN6ICm5ZCI546w5pyJ6LaL5Yq/5Zu+77yJDQogICAgYXN5bmMgZmV0Y2hDb21wYXJpc29uRGF0YSgpIHsNCiAgICAgIHRoaXMuY29tcGFyaXNvbkNoYXJ0TG9hZGluZyA9IHRydWUNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOaehOW7uuS4pOS4queJqeaWmeeahOWvueavlOivt+axgu+8jOWPquiOt+WPluS7t+agvOabsue6vw0KICAgICAgICBjb25zdCBpdGVtTGlzdCA9IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBjdXJ2ZVR5cGU6IDEsIC8vIOS7t+agvOabsue6vw0KICAgICAgICAgICAgaXRlbU5hbWU6IHRoaXMuY3VycmVudENvbXBhcmlzb24uaXRlbU5hbWUNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGN1cnZlVHlwZTogMSwgLy8g5Lu35qC85puy57q/DQogICAgICAgICAgICBpdGVtTmFtZTogdGhpcy5jdXJyZW50Q29tcGFyaXNvbi5jb21wYXJlSXRlbU5hbWUNCiAgICAgICAgICB9DQogICAgICAgIF0NCg0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgZGltZW5zaW9uVHlwZTogdGhpcy5jdXJyZW50RGltZW5zaW9uVHlwZSwNCiAgICAgICAgICBpdGVtTGlzdDogaXRlbUxpc3QNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaENvbXBhcmlzb25EYXRhIC0g6K+35rGC5Y+C5pWwOicsIHBhcmFtcykNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRQdXJjaGFzZVByaWNlQW5kU3RvcmVGcm9tTmV3VGFibGVzKHBhcmFtcykNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoQ29tcGFyaXNvbkRhdGEgLSDlrozmlbTlk43lupQ6JywgcmVzcG9uc2UpDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgew0KICAgICAgICAgIC8vIOWvuei/lOWbnueahOaVsOaNrui/m+ihjOetm+mAie+8jOehruS/neWfuuWHhueJqeaWmeWSjOebuOS8vOeJqeaWmeeahOaMh+WumuS7t+agvOexu+Wei+mDveiDveiiq+aPkOWPlg0KICAgICAgICAgIGNvbnN0IGZpbHRlcmVkRGF0YSA9IFtdDQoNCiAgICAgICAgICAvLyDojrflj5bln7rlh4bnianmlpnlkoznm7jkvLznianmlpnnmoTnm67moIfku7fmoLznsbvlnovlkI3np7ANCiAgICAgICAgICBjb25zdCBiYXNlUHJpY2VUeXBlTmFtZSA9IHRoaXMuZ2V0UHJpY2VUeXBlTmFtZSh0aGlzLmN1cnJlbnRDb21wYXJpc29uLnByaWNlVHlwZSkNCiAgICAgICAgICBjb25zdCBjb21wYXJlUHJpY2VUeXBlTmFtZSA9IHRoaXMuZ2V0UHJpY2VUeXBlTmFtZSh0aGlzLmN1cnJlbnRDb21wYXJpc29uLmNvbXBhcmVQcmljZVR5cGUpDQoNCiAgICAgICAgICBjb25zb2xlLmxvZygn562b6YCJ5p2h5Lu2OicsIHsNCiAgICAgICAgICAgIGJhc2VJdGVtTmFtZTogdGhpcy5jdXJyZW50Q29tcGFyaXNvbi5pdGVtTmFtZSwNCiAgICAgICAgICAgIGJhc2VQcmljZVR5cGVOYW1lOiBiYXNlUHJpY2VUeXBlTmFtZSwNCiAgICAgICAgICAgIGNvbXBhcmVJdGVtTmFtZTogdGhpcy5jdXJyZW50Q29tcGFyaXNvbi5jb21wYXJlSXRlbU5hbWUsDQogICAgICAgICAgICBjb21wYXJlUHJpY2VUeXBlTmFtZTogY29tcGFyZVByaWNlVHlwZU5hbWUNCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKG1hdGVyaWFsRGF0YSA9PiB7DQogICAgICAgICAgICBjb25zdCBmaWx0ZXJlZE1hdGVyaWFsRGF0YSA9IHsgLi4ubWF0ZXJpYWxEYXRhIH0NCg0KICAgICAgICAgICAgaWYgKGZpbHRlcmVkTWF0ZXJpYWxEYXRhLnByb2N1cmVtZW50UHJpY2VWb0xpc3QpIHsNCiAgICAgICAgICAgICAgLy8g5Y+q5L+d55WZ5Yy56YWN55qE5Lu35qC857G75Z6LDQogICAgICAgICAgICAgIGZpbHRlcmVkTWF0ZXJpYWxEYXRhLnByb2N1cmVtZW50UHJpY2VWb0xpc3QgPSBmaWx0ZXJlZE1hdGVyaWFsRGF0YS5wcm9jdXJlbWVudFByaWNlVm9MaXN0LmZpbHRlcihwcmljZUdyb3VwID0+IHsNCiAgICAgICAgICAgICAgICBsZXQgaXNNYXRjaCA9IGZhbHNlDQogICAgICAgICAgICAgICAgLy8g5Z+65YeG54mp5paZ77ya5Yy56YWN54mp5paZ5ZCN56ew5ZKM5Z+65YeG5Lu35qC857G75Z6LDQogICAgICAgICAgICAgICAgaWYgKG1hdGVyaWFsRGF0YS5pdGVtTmFtZSA9PT0gdGhpcy5jdXJyZW50Q29tcGFyaXNvbi5pdGVtTmFtZSkgew0KICAgICAgICAgICAgICAgICAgaXNNYXRjaCA9IHByaWNlR3JvdXAucHJpY2VOYW1lID09PSBiYXNlUHJpY2VUeXBlTmFtZQ0KICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coYOWfuuWHhueJqeaWmVske21hdGVyaWFsRGF0YS5pdGVtTmFtZX1dIOS7t+agvOexu+Wei1ske3ByaWNlR3JvdXAucHJpY2VOYW1lfV0g55uu5qCH57G75Z6LWyR7YmFzZVByaWNlVHlwZU5hbWV9XSDljLnphY06JHtpc01hdGNofWApDQogICAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgICAgaWYoaXNNYXRjaCl7DQogICAgICAgICAgICAgICAgICByZXR1cm4gaXNNYXRjaA0KICAgICAgICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgICAgICAgaWYgKG1hdGVyaWFsRGF0YS5pdGVtTmFtZSA9PT0gdGhpcy5jdXJyZW50Q29tcGFyaXNvbi5jb21wYXJlSXRlbU5hbWUpIHsNCiAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNNYXRjaCA9IHByaWNlR3JvdXAucHJpY2VOYW1lID09PSBjb21wYXJlUHJpY2VUeXBlTmFtZQ0KICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg55u45Ly854mp5paZWyR7bWF0ZXJpYWxEYXRhLml0ZW1OYW1lfV0g5Lu35qC857G75Z6LWyR7cHJpY2VHcm91cC5wcmljZU5hbWV9XSDnm67moIfnsbvlnotbJHtjb21wYXJlUHJpY2VUeXBlTmFtZX1dIOWMuemFjToke2lzTWF0Y2h9YCkNCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGlzTWF0Y2gNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQoNCg0KICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKDExMTExMTExMSkNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coZmlsdGVyZWRNYXRlcmlhbERhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdCkNCg0KICAgICAgICAgICAgICAvLyDlj6rmnInlvZPor6XnianmlpnmnInljLnphY3nmoTku7fmoLznsbvlnovml7bmiY3liqDlhaXnu5PmnpwNCiAgICAgICAgICAgICAgaWYgKGZpbHRlcmVkTWF0ZXJpYWxEYXRhLnByb2N1cmVtZW50UHJpY2VWb0xpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgIGZpbHRlcmVkRGF0YS5wdXNoKGZpbHRlcmVkTWF0ZXJpYWxEYXRhKQ0KICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDmt7vliqDnianmlplbJHttYXRlcmlhbERhdGEuaXRlbU5hbWV9Xe+8jOWMheWQqyR7ZmlsdGVyZWRNYXRlcmlhbERhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdC5sZW5ndGh95Liq5Lu35qC857uEYCkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICB0aGlzLmNvbXBhcmlzb25QcmljZURhdGEgPSBmaWx0ZXJlZERhdGENCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hDb21wYXJpc29uRGF0YSAtIOetm+mAieWQjueahOaVsOaNrjonLCB0aGlzLmNvbXBhcmlzb25QcmljZURhdGEpDQogICAgICAgICAgY29uc29sZS5sb2coJ+etm+mAiee7k+aenOe7n+iuoTonLCB7DQogICAgICAgICAgICB0b3RhbE1hdGVyaWFsczogZmlsdGVyZWREYXRhLmxlbmd0aCwNCiAgICAgICAgICAgIG1hdGVyaWFsczogZmlsdGVyZWREYXRhLm1hcChtID0+ICh7DQogICAgICAgICAgICAgIG5hbWU6IG0uaXRlbU5hbWUsDQogICAgICAgICAgICAgIHByaWNlR3JvdXBDb3VudDogbS5wcm9jdXJlbWVudFByaWNlVm9MaXN0Py5sZW5ndGggfHwgMCwNCiAgICAgICAgICAgICAgcHJpY2VHcm91cHM6IG0ucHJvY3VyZW1lbnRQcmljZVZvTGlzdD8ubWFwKHAgPT4gcC5wcmljZU5hbWUpIHx8IFtdDQogICAgICAgICAgICB9KSkNCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgLy8g5riy5p+T5a+55q+U5Zu+6KGoDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5yZW5kZXJDb21wYXJpc29uQ2hhcnQoKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a+55q+U5pWw5o2u5aSx6LSlJywgcmVzcG9uc2UpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5a+55q+U5pWw5o2u5aSx6LSlJykNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a+55q+U5pWw5o2u5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5blr7nmr5TmlbDmja7lpLHotKXvvJonICsgZXJyb3IubWVzc2FnZSkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuY29tcGFyaXNvbkNoYXJ0TG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOa4suafk+WvueavlOWbvuihqO+8iOeLrOeri+WunueOsO+8jOS4jeiApuWQiOeOsOaciei2i+WKv+Wbvu+8iQ0KICAgIHJlbmRlckNvbXBhcmlzb25DaGFydCgpIHsNCiAgICAgIGNvbnN0IGNoYXJ0RG9tID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2NvbXBhcmlzb25DaGFydCcpDQogICAgICBpZiAoIWNoYXJ0RG9tKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aJvuS4jeWIsOWvueavlOWbvuihqERPTeWFg+e0oCcpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDmuIXnkIbnjrDmnInlrp7kvosNCiAgICAgIGlmICh0aGlzLmNvbXBhcmlzb25DaGFydEluc3RhbmNlKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgdGhpcy5jb21wYXJpc29uQ2hhcnRJbnN0YW5jZS5kaXNwb3NlKCkNCiAgICAgICAgfSBjYXRjaCAoZXJyKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5riF55CG546w5pyJ5a+55q+U5Zu+6KGo5a6e5L6L5aSx6LSlOicsIGVycikNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyDliJvlu7rmlrDnmoTlm77ooajlrp7kvosNCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuY29tcGFyaXNvbkNoYXJ0SW5zdGFuY2UgPSBlY2hhcnRzLmluaXQoY2hhcnREb20pDQogICAgICB9IGNhdGNoIChlcnIpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yib5bu65a+55q+U5Zu+6KGo5a6e5L6L5aSx6LSlOicsIGVycikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIGlmICghdGhpcy5jb21wYXJpc29uUHJpY2VEYXRhIHx8IHRoaXMuY29tcGFyaXNvblByaWNlRGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgY2hhcnREb20uaW5uZXJIVE1MID0gJzxkaXYgY2xhc3M9ImNoYXJ0LXBsYWNlaG9sZGVyIj7mmoLml6Dlr7nmr5TmlbDmja48L2Rpdj4nDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBjb25zdCBmb3JtYXREYXRlID0gKGRhdGVTdHIpID0+IHsNCiAgICAgICAgY29uc3QgeWVhciA9IGRhdGVTdHIuc3Vic3RyaW5nKDAsIDQpDQogICAgICAgIGNvbnN0IG1vbnRoID0gZGF0ZVN0ci5zdWJzdHJpbmcoNCwgNikNCiAgICAgICAgY29uc3QgZGF5ID0gZGF0ZVN0ci5zdWJzdHJpbmcoNiwgOCkNCiAgICAgICAgcmV0dXJuIGAke3llYXJ95bm0JHttb250aH3mnIgke2RheX3ml6VgDQogICAgICB9DQoNCiAgICAgIC8vIOaUtumbhuaJgOacieaXpeacnw0KICAgICAgbGV0IGFsbERhdGVzID0gbmV3IFNldCgpDQoNCiAgICAgIHRoaXMuY29tcGFyaXNvblByaWNlRGF0YS5mb3JFYWNoKG1hdGVyaWFsRGF0YSA9PiB7DQogICAgICAgIGlmIChtYXRlcmlhbERhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdCkgew0KICAgICAgICAgIG1hdGVyaWFsRGF0YS5wcm9jdXJlbWVudFByaWNlVm9MaXN0LmZvckVhY2gocHJpY2VHcm91cCA9PiB7DQogICAgICAgICAgICBpZiAocHJpY2VHcm91cC5wcmljZUxpc3QpIHsNCiAgICAgICAgICAgICAgcHJpY2VHcm91cC5wcmljZUxpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgICBhbGxEYXRlcy5hZGQoaXRlbS5yZWNvcmREYXRlKQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIGFsbERhdGVzID0gQXJyYXkuZnJvbShhbGxEYXRlcykuc29ydCgpDQogICAgICBjb25zdCB4QXhpc0RhdGEgPSBhbGxEYXRlcy5tYXAoZm9ybWF0RGF0ZSkNCg0KICAgICAgaWYgKGFsbERhdGVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICBjaGFydERvbS5pbm5lckhUTUwgPSAnPGRpdiBjbGFzcz0iY2hhcnQtcGxhY2Vob2xkZXIiPuaaguaXoOWvueavlOaVsOaNrjwvZGl2PicNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOaehOW7uuezu+WIl+aVsOaNrg0KICAgICAgY29uc3Qgc2VyaWVzID0gW10NCiAgICAgIGNvbnN0IGxlZ2VuZERhdGEgPSBbXQ0KICAgICAgY29uc3QgY29sb3JzID0gWycjOGZlOWZmJywgJyNmZjlmN2YnLCAnIzVmZDhiNicsICcjZmZiOTgwJ10NCiAgICAgIGxldCBjb2xvckluZGV4ID0gMA0KDQogICAgICBjb25zb2xlLmxvZygnPT09IOW8gOWni+WkhOeQhuWvueavlOaVsOaNriA9PT0nKQ0KICAgICAgY29uc29sZS5sb2coJ+WvueavlOaVsOaNruaAu+iniDonLCB7DQogICAgICAgIG1hdGVyaWFsQ291bnQ6IHRoaXMuY29tcGFyaXNvblByaWNlRGF0YS5sZW5ndGgsDQogICAgICAgIGJhc2VNYXRlcmlhbDogdGhpcy5jdXJyZW50Q29tcGFyaXNvbi5pdGVtTmFtZSwNCiAgICAgICAgY29tcGFyZU1hdGVyaWFsOiB0aGlzLmN1cnJlbnRDb21wYXJpc29uLmNvbXBhcmVJdGVtTmFtZQ0KICAgICAgfSkNCg0KICAgICAgdGhpcy5jb21wYXJpc29uUHJpY2VEYXRhLmZvckVhY2gobWF0ZXJpYWxEYXRhID0+IHsNCiAgICAgICAgY29uc3QgbWF0ZXJpYWxOYW1lID0gbWF0ZXJpYWxEYXRhLml0ZW1OYW1lDQogICAgICAgIGNvbnNvbGUubG9nKGBcbuWkhOeQhueJqeaWmTogJHttYXRlcmlhbE5hbWV9YCkNCg0KICAgICAgICBpZiAobWF0ZXJpYWxEYXRhLnByb2N1cmVtZW50UHJpY2VWb0xpc3QpIHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhgICDor6XnianmlpnmnIkgJHttYXRlcmlhbERhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdC5sZW5ndGh9IOS4quS7t+agvOe7hGApDQogICAgICAgICAgbWF0ZXJpYWxEYXRhLnByb2N1cmVtZW50UHJpY2VWb0xpc3QuZm9yRWFjaCgocHJpY2VHcm91cCwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKGAgIOS7t+agvOe7hCAke2luZGV4ICsgMX06ICR7cHJpY2VHcm91cC5wcmljZU5hbWV977yM5pWw5o2u54K55pWw6YePOiAke3ByaWNlR3JvdXAucHJpY2VMaXN0Py5sZW5ndGggfHwgMH1gKQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICAvLyDmlbDmja7lt7Lnu4/lnKhmZXRjaENvbXBhcmlzb25EYXRh5Lit6aKE5YWI562b6YCJ6L+H77yM6L+Z6YeM55u05o6l5aSE55CG5omA5pyJ5Yy56YWN55qE5Lu35qC857uEDQogICAgICAgICAgbWF0ZXJpYWxEYXRhLnByb2N1cmVtZW50UHJpY2VWb0xpc3QuZm9yRWFjaCgocHJpY2VHcm91cCwgZ3JvdXBJbmRleCkgPT4gew0KICAgICAgICAgICAgY29uc3QgcHJpY2VEYXRhID0gYWxsRGF0ZXMubWFwKGRhdGUgPT4gew0KICAgICAgICAgICAgICBjb25zdCBmb3VuZCA9IHByaWNlR3JvdXAucHJpY2VMaXN0LmZpbmQoaXRlbSA9PiBpdGVtLnJlY29yZERhdGUgPT09IGRhdGUpDQogICAgICAgICAgICAgIHJldHVybiBmb3VuZCA/IHBhcnNlRmxvYXQoZm91bmQucHJpY2UpIDogbnVsbA0KICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgLy8g57uf6K6h5pyJ5pWI5pWw5o2u54K5DQogICAgICAgICAgICBjb25zdCB2YWxpZERhdGFDb3VudCA9IHByaWNlRGF0YS5maWx0ZXIodiA9PiB2ICE9PSBudWxsICYmIHYgIT09IHVuZGVmaW5lZCkubGVuZ3RoDQogICAgICAgICAgICBjb25zb2xlLmxvZyhgICAgIOWkhOeQhuS7t+agvOe7hFske3ByaWNlR3JvdXAucHJpY2VOYW1lfV3vvIzmnInmlYjmlbDmja7ngrk6ICR7dmFsaWREYXRhQ291bnR9LyR7cHJpY2VEYXRhLmxlbmd0aH1gKQ0KDQogICAgICAgICAgICAvLyDnoa7kv53mr4/mnaHmm7Lnur/pg73mnInllK/kuIDnmoTlkI3np7DlkozpopzoibLvvIzljbPkvb/mlbDmja7nm7jlkIwNCiAgICAgICAgICAgIGNvbnN0IHVuaXF1ZU5hbWUgPSBgJHttYXRlcmlhbE5hbWV9LSR7cHJpY2VHcm91cC5wcmljZU5hbWV9YA0KICAgICAgICAgICAgY29uc3QgbGluZUNvbG9yID0gY29sb3JzW2NvbG9ySW5kZXggJSBjb2xvcnMubGVuZ3RoXQ0KDQogICAgICAgICAgICAvLyDmo4Dmn6XmmK/lkKblt7Lnu4/mnInnm7jlkIznmoTmlbDmja7vvIzlpoLmnpzmnInliJnmt7vliqDovbvlvq7lgY/np7sNCiAgICAgICAgICAgIGNvbnN0IGRhdGFTdHIgPSBKU09OLnN0cmluZ2lmeShwcmljZURhdGEpDQogICAgICAgICAgICBjb25zdCBleGlzdGluZ1NlcmllcyA9IHNlcmllcy5maW5kKHMgPT4gSlNPTi5zdHJpbmdpZnkocy5kYXRhKSA9PT0gZGF0YVN0cikNCiAgICAgICAgICAgIGxldCBhZGp1c3RlZERhdGEgPSBwcmljZURhdGENCg0KICAgICAgICAgICAgaWYgKGV4aXN0aW5nU2VyaWVzICYmIHByaWNlRGF0YS5zb21lKHYgPT4gdiAhPT0gbnVsbCkpIHsNCiAgICAgICAgICAgICAgLy8g5Li66YeN5aSN5pWw5o2u5re75Yqg5p6B5bCP55qE5YGP56e76YeP77yIMC4wMe+8ie+8jOehruS/neS4pOadoee6v+mDveiDveaYvuekug0KICAgICAgICAgICAgICBhZGp1c3RlZERhdGEgPSBwcmljZURhdGEubWFwKHZhbHVlID0+IHZhbHVlICE9PSBudWxsID8gdmFsdWUgKyAwLjAxIDogbnVsbCkNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coYCAgICDmo4DmtYvliLDph43lpI3mlbDmja7vvIzkuLogJHt1bmlxdWVOYW1lfSDmt7vliqDlgY/np7tgKQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICBzZXJpZXMucHVzaCh7DQogICAgICAgICAgICAgIG5hbWU6IHVuaXF1ZU5hbWUsDQogICAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgICAgZGF0YTogYWRqdXN0ZWREYXRhLA0KICAgICAgICAgICAgICBzbW9vdGg6IHRydWUsDQogICAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICAgIHdpZHRoOiAzLA0KICAgICAgICAgICAgICAgIGNvbG9yOiBsaW5lQ29sb3IsDQogICAgICAgICAgICAgICAgLy8g5aaC5p6c5piv5YGP56e755qE5pWw5o2u77yM5L2/55So6Jma57q/5qC35byP5Yy65YiGDQogICAgICAgICAgICAgICAgdHlwZTogYWRqdXN0ZWREYXRhICE9PSBwcmljZURhdGEgPyAnZGFzaGVkJyA6ICdzb2xpZCcNCiAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgICAgY29sb3I6IGxpbmVDb2xvcg0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICBzeW1ib2w6ICdjaXJjbGUnLA0KICAgICAgICAgICAgICBzeW1ib2xTaXplOiA2LA0KICAgICAgICAgICAgICBjb25uZWN0TnVsbHM6IHRydWUsDQogICAgICAgICAgICAgIC8vIOa3u+WKoHotaW5kZXjnoa7kv53kuKTmnaHnur/pg73og73mmL7npLoNCiAgICAgICAgICAgICAgejogY29sb3JJbmRleCArIDENCiAgICAgICAgICAgIH0pDQoNCiAgICAgICAgICAgIGxlZ2VuZERhdGEucHVzaCh1bmlxdWVOYW1lKQ0KICAgICAgICAgICAgY29sb3JJbmRleCsrDQogICAgICAgICAgICBjb25zb2xlLmxvZyhgICAgIOKckyDmt7vliqDmm7Lnur86ICR7dW5pcXVlTmFtZX3vvIzpopzoibI6ICR7bGluZUNvbG9yfe+8jOacieaViOaVsOaNrjogJHt2YWxpZERhdGFDb3VudH1gKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIGNvbnNvbGUubG9nKGBcbj09PSDlm77ooajmlbDmja7lpITnkIblrozmiJAgPT09YCkNCiAgICAgIGNvbnNvbGUubG9nKGDmgLvorqHmt7vliqAgJHtzZXJpZXMubGVuZ3RofSDmnaHmm7Lnur86YCkNCiAgICAgIHNlcmllcy5mb3JFYWNoKChzLCBpKSA9PiB7DQogICAgICAgIGNvbnN0IHZhbGlkQ291bnQgPSBzLmRhdGEuZmlsdGVyKHYgPT4gdiAhPT0gbnVsbCAmJiB2ICE9PSB1bmRlZmluZWQpLmxlbmd0aA0KICAgICAgICBjb25zb2xlLmxvZyhgICAke2kgKyAxfS4gJHtzLm5hbWV9ICjmnInmlYjmlbDmja46ICR7dmFsaWRDb3VudH0pYCkNCiAgICAgIH0pDQoNCiAgICAgIC8vIOiuoeeul1novbTojIPlm7QNCiAgICAgIGxldCBwcmljZU1pbiwgcHJpY2VNYXgNCiAgICAgIGNvbnN0IHByaWNlVmFsdWVzID0gc2VyaWVzLmZsYXRNYXAocyA9PiBzLmRhdGEuZmlsdGVyKHYgPT4gdiAhPT0gbnVsbCAmJiB2ICE9PSB1bmRlZmluZWQpKQ0KICAgICAgaWYgKHByaWNlVmFsdWVzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgcHJpY2VNaW4gPSBNYXRoLm1pbiguLi5wcmljZVZhbHVlcykNCiAgICAgICAgcHJpY2VNYXggPSBNYXRoLm1heCguLi5wcmljZVZhbHVlcykNCiAgICAgIH0NCg0KICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsDQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgYXhpc1BvaW50ZXI6IHsNCiAgICAgICAgICAgIHR5cGU6ICdjcm9zcycNCiAgICAgICAgICB9LA0KICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7DQogICAgICAgICAgICBsZXQgc3RyID0gcGFyYW1zWzBdLmF4aXNWYWx1ZUxhYmVsICsgJzxici8+Jw0KICAgICAgICAgICAgcGFyYW1zLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgICAgIGlmIChpdGVtLnZhbHVlICE9PSBudWxsICYmIGl0ZW0udmFsdWUgIT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICAgIHN0ciArPSBgJHtpdGVtLm1hcmtlcn0ke2l0ZW0uc2VyaWVzTmFtZX06ICR7aXRlbS52YWx1ZX0g5YWDL+WQqDxici8+YA0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHN0ciArPSBgJHtpdGVtLm1hcmtlcn0ke2l0ZW0uc2VyaWVzTmFtZX06IC08YnIvPmANCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIHJldHVybiBzdHINCiAgICAgICAgICB9DQogICAgICAgIH0sDQogICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgIGRhdGE6IGxlZ2VuZERhdGEsDQogICAgICAgICAgdGV4dFN0eWxlOiB7DQogICAgICAgICAgICBjb2xvcjogJyNmZmYnDQogICAgICAgICAgfSwNCiAgICAgICAgICB0b3A6ICc1JScNCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogew0KICAgICAgICAgIGxlZnQ6ICczJScsDQogICAgICAgICAgcmlnaHQ6ICc0JScsDQogICAgICAgICAgYm90dG9tOiAnMTIlJywNCiAgICAgICAgICB0b3A6ICcyMCUnLA0KICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogeEF4aXNEYXRhLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgY29sb3I6ICcjZWVlJywNCiAgICAgICAgICAgIGludGVydmFsOiBmdW5jdGlvbihpbmRleCwgdmFsdWUpIHsNCiAgICAgICAgICAgICAgaWYgKGluZGV4ID49IGFsbERhdGVzLmxlbmd0aCB8fCAhYWxsRGF0ZXMubGVuZ3RoKSByZXR1cm4gZmFsc2UNCg0KICAgICAgICAgICAgICBjb25zdCB1bmlxdWVNb250aHMgPSBuZXcgU2V0KCkNCiAgICAgICAgICAgICAgYWxsRGF0ZXMuZm9yRWFjaChkYXRlU3RyID0+IHsNCiAgICAgICAgICAgICAgICBjb25zdCB5ZWFyID0gZGF0ZVN0ci5zdWJzdHJpbmcoMCwgNCkNCiAgICAgICAgICAgICAgICBjb25zdCBtb250aCA9IGRhdGVTdHIuc3Vic3RyaW5nKDQsIDYpDQogICAgICAgICAgICAgICAgdW5pcXVlTW9udGhzLmFkZChgJHt5ZWFyfSR7bW9udGh9YCkNCiAgICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgICBjb25zdCBtb250aHNDb3VudCA9IHVuaXF1ZU1vbnRocy5zaXplDQogICAgICAgICAgICAgIGlmIChtb250aHNDb3VudCA8PSAxKSByZXR1cm4gdHJ1ZQ0KDQogICAgICAgICAgICAgIGNvbnN0IHRvdGFsRGF0YVBvaW50cyA9IGFsbERhdGVzLmxlbmd0aA0KICAgICAgICAgICAgICBjb25zdCBpZGVhbEludGVydmFsID0gTWF0aC5mbG9vcih0b3RhbERhdGFQb2ludHMgLyBNYXRoLm1pbihtb250aHNDb3VudCwgOCkpDQoNCiAgICAgICAgICAgICAgcmV0dXJuIGluZGV4ICUgTWF0aC5tYXgoaWRlYWxJbnRlcnZhbCwgMSkgPT09IDANCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uKHZhbHVlLCBpbmRleCkgew0KICAgICAgICAgICAgICBpZiAoaW5kZXggPj0gYWxsRGF0ZXMubGVuZ3RoKSByZXR1cm4gJycNCiAgICAgICAgICAgICAgY29uc3Qgb3JpZ2luYWxEYXRlU3RyID0gYWxsRGF0ZXNbaW5kZXhdDQogICAgICAgICAgICAgIGlmICghb3JpZ2luYWxEYXRlU3RyKSByZXR1cm4gJycNCg0KICAgICAgICAgICAgICBjb25zdCB5ZWFyID0gb3JpZ2luYWxEYXRlU3RyLnN1YnN0cmluZygwLCA0KQ0KICAgICAgICAgICAgICBjb25zdCBtb250aCA9IHBhcnNlSW50KG9yaWdpbmFsRGF0ZVN0ci5zdWJzdHJpbmcoNCwgNikpDQogICAgICAgICAgICAgIHJldHVybiBgJHt5ZWFyfS4ke21vbnRofWANCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjZWVlJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgIG5hbWU6ICfku7fmoLzvvIjlhYMv5ZCo77yJJywNCiAgICAgICAgICBtaW46IHByaWNlTWluLA0KICAgICAgICAgIG1heDogcHJpY2VNYXgsDQogICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyNlZWUnDQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnI2VlZScNCiAgICAgICAgICB9LA0KICAgICAgICAgIHNwbGl0TGluZTogew0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjEpJw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBzZXJpZXMNCiAgICAgIH0NCg0KICAgICAgdGhpcy5jb21wYXJpc29uQ2hhcnRJbnN0YW5jZS5zZXRPcHRpb24ob3B0aW9uLCB0cnVlKQ0KICAgIH0sDQoNCiAgICAvLyDmo4Dmn6XkuKTkuKrmm7Lnur/mmK/lkKbpg73lt7Lorr7nva7pu5jorqTlgLzvvIzlpoLmnpzmmK/liJnop6blj5HliJ3lp4vmlbDmja7ojrflj5YNCiAgICBjaGVja0FuZFRyaWdnZXJJbml0aWFsRGF0YUZldGNoKCkgew0KICAgICAgLy8g5qOA5p+l5Lik5Liq5puy57q/5piv5ZCm6YO95bey57uP6K6+572u5LqG6buY6K6k55qEUELlnZcNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUHVyY2hhc2VBbW91bnRNYXRlcmlhbHMuaW5jbHVkZXMoJ1BC5Z2XJykgJiYNCiAgICAgICAgdGhpcy5zZWxlY3RlZE1hcmtldFByaWNlTWF0ZXJpYWxzLmluY2x1ZGVzKCdQQuWdlycpICYmDQogICAgICAgICF0aGlzLmhhc0luaXRpYWxpemVkUHJpY2VDaGFydCkgew0KDQogICAgICAgIHRoaXMuaGFzSW5pdGlhbGl6ZWRQcmljZUNoYXJ0ID0gdHJ1ZSAvLyDmoIforrDlt7Lnu4/liJ3lp4vljJbov4cNCiAgICAgICAgY29uc29sZS5sb2coJ+S4pOS4quabsue6v+mDveW3suiuvue9rum7mOiupOWAvO+8jOiHquWKqOinpuWPkeaVsOaNruiOt+WPlicpDQoNCiAgICAgICAgLy8g6Ieq5Yqo6Kem5Y+R5pWw5o2u6I635Y+WDQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmZldGNoUHJpY2VBbmRTdG9yZURhdGFGb3JOZXdDaGFydCgpDQogICAgICAgIH0pDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkJA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/purchaseDashboardPlan", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <div class=\"dashboard-header\">\r\n      <h1>采购计划看板</h1>\r\n      <div class=\"header-controls\">\r\n        <div class=\"fullscreen-btn\" @click=\"toggleFullscreen\" :title=\"isFullscreen ? '退出全屏' : '进入全屏'\">\r\n          <i :class=\"isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'\"></i>\r\n        </div>\r\n        <div class=\"time-filter\">\r\n          <button\r\n            v-for=\"filter in timeFilters\"\r\n            :key=\"filter.id\"\r\n            :class=\"['time-filter-btn', { active: filter.id === activeFilter }]\"\r\n            @click=\"handleTimeFilterChange(filter.id, filter.value)\"\r\n          >\r\n            {{ filter.label }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"dashboard-grid\">\r\n      <!-- 第一行：订单至入库天数 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          订单至入库天数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedOrderFactoryDep\"\r\n              @change=\"handleOrderFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedOrderMaterialType\"\r\n              @change=\"handleOrderMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"monthlyInventoryChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第一行：商务部接收至挂单天数 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          商务部接收至挂单天数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedFactoryDep\"\r\n              @change=\"handleFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedFactoryMaterialType\"\r\n              @change=\"handleFactoryMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"factoryStockChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 入库至领用天数 -->\r\n      <div class=\"card material-chart-card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          入库至领用天数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedMaterialFactoryDep\"\r\n              @change=\"handleMaterialFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedMaterialMaterialType\"\r\n              @change=\"handleMaterialMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"materialStatisticsChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第二行：实时超期数 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          实时超期数\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedOverdueFactoryDep\"\r\n              @change=\"handleOverdueFactoryDepChange\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"overdueChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      \r\n\r\n    </div>\r\n\r\n    \r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport chartMethods from './chartMethods'\r\nimport extendedChartMethods from './chartMethodsExtended'\r\nimport screenfull from 'screenfull'\r\nimport { showYearlyAmount, showRealTimeAmount, showCokingCoalAmount, showKeyIndicators, showItemTypeList, showMaterialList, showData, showSuppList, showPurchasePlanList, showHighFrequencyMaterialList, showPurchaseSuppRisk, getMaterialFuturePrice, getMaterialNameList, getPurchasePriceAndStore, getMaterialNameListFromNewTables, getPurchasePriceAndStoreFromNewTables } from '@/api/purchaseDashboard/purchaseDashboard'\r\nimport { listSimilarByItemNames } from '@/api/purchase/similar'\r\nimport { getDepNameList, getListMonthly } from '@/api/purchase/purdchaseFactoryStock'\r\n\r\nexport default {\r\n  name: 'PurchaseDashboard',\r\n  mixins: [chartMethods, extendedChartMethods],\r\n  data() {\r\n    return {\r\n     \r\n\r\n      // 数据\r\n      dashboardData: {},\r\n      purchaseStats: {},\r\n\r\n      // 下拉选项\r\n      topSuppliersOptions: [],\r\n\r\n      // 选中的过滤器值\r\n      selectedTopSuppliersFilter: '',\r\n      selectedOrderType: 'TOP', // 排序类型，默认为TOP\r\n\r\n      // 图表实例\r\n      chartInstances: {},\r\n\r\n      // 原始数据备份\r\n      originalTopSuppliersData: [],\r\n\r\n      // 订单至入库天数相关\r\n      selectedOrderFactoryDep: '', // 选中的分厂\r\n      selectedOrderMaterialType: '', // 选中的物料类型\r\n      orderToReceiptData: [], // 订单至入库天数数据\r\n\r\n      // 第二个订单至入库天数模块相关\r\n      selectedCokingCoalFactoryDep: '', // 选中的分厂\r\n      selectedCokingCoalMaterialType: '', // 选中的物料类型\r\n\r\n      // 第三个订单至入库天数模块相关\r\n      selectedMaterialFactoryDep: '', // 选中的分厂\r\n      selectedMaterialMaterialType: '', // 选中的物料类型\r\n      realTimeInventoryData: [],\r\n      cokingCoalInventoryData: [],\r\n\r\n      // 矿焦煤库存图表相关\r\n      selectedCokingCoalType: '', // 选中的矿焦煤类型，默认为空（全部）\r\n\r\n      // 物料入库统计相关\r\n      selectedMaterialCategory: '1',\r\n      selectedMaterialItem: '',\r\n      materialItemOptions: [],\r\n      materialStatisticsData: [],\r\n\r\n      // 高频采购物料相关\r\n      selectedCodeType: 'ALL',\r\n      selectedItemType: 'CLASS3',\r\n      highFrequencyMaterialData: [],\r\n\r\n      // 供应商风险数据\r\n      supplierRiskData: [],\r\n\r\n      // AI价格预测相关\r\n      pricePredictions: [], // 改为数组，支持多个物料的预测\r\n      predictionLoading: false,\r\n\r\n      // 物料价格趋势图相关\r\n      materialNameOptions: [],\r\n      selectedMaterial: 'PB块',\r\n      selectedMaterialCategory: '1', // 默认选择矿石\r\n      priceAndStoreData: null,\r\n\r\n      // 新的价格趋势图相关属性\r\n      // 采购量曲线\r\n      purchaseAmountCategories: [99], // 默认选择全部\r\n      selectedPurchaseAmountMaterials: [],\r\n      purchaseAmountMaterialOptions: [],\r\n\r\n      // 市场价曲线\r\n      marketPriceCategories: [99], // 默认选择全部\r\n      selectedMarketPriceMaterials: [],\r\n      marketPriceMaterialOptions: [],\r\n\r\n      // 获取数据状态\r\n      fetchingPriceData: false,\r\n      newPriceAndStoreData: null,\r\n\r\n      // 初始化标志\r\n      hasInitializedPriceChart: false,\r\n\r\n      // 相似物料数据\r\n      similarMaterialsData: [],\r\n      similarMaterialsLoading: false,\r\n\r\n      // 对比弹框相关\r\n      comparisonDialogVisible: false,\r\n      comparisonChartLoading: false,\r\n      currentComparison: {},\r\n      comparisonChartInstance: null,\r\n      comparisonPriceData: null,\r\n\r\n      // 机旁库当前库存相关\r\n      selectedFactoryDep: '', // 选中的分厂\r\n      selectedFactoryMaterialType: '', // 选中的物料类型\r\n      factoryDepOptions: [], // 分厂选项列表\r\n      factoryStockData: [], // 机旁库存数据\r\n\r\n      // 实时超期数相关\r\n      selectedOverdueFactoryDep: '', // 选中的分厂\r\n      overdueData: [] // 超期数据\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isFullscreen() {\r\n      return this.$store.state.app.isFullscreenMode\r\n    },\r\n\r\n    // 按itemName、category、priceType联合索引分组相似物料数据\r\n    groupedSimilarMaterials() {\r\n      const grouped = {}\r\n      this.similarMaterialsData.forEach(item => {\r\n        // 创建联合索引key\r\n        const groupKey = `${item.itemName}_${item.category}_${item.priceType}`\r\n        const displayKey = `${item.itemName} (${this.getCategoryName(item.category)} - ${this.getPriceTypeName(item.priceType)})`\r\n\r\n        if (!grouped[displayKey]) {\r\n          grouped[displayKey] = {\r\n            groupKey: groupKey,\r\n            items: []\r\n          }\r\n        }\r\n        grouped[displayKey].items.push(item)\r\n      })\r\n\r\n      // 对每个组内的数据按排名排序\r\n      Object.keys(grouped).forEach(key => {\r\n        grouped[key].items.sort((a, b) => a.rank - b.rank)\r\n      })\r\n\r\n      return grouped\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.checkEchartsAvailability()\r\n    this.fetchDashboardData(3)\r\n\r\n    this.fetchRealTimeInventoryData()\r\n    this.fetchCokingCoalInventoryData()\r\n    // 初始化物料入库统计的下拉框选项和数据\r\n    this.updateMaterialItemOptions().then(() => {\r\n      this.fetchMaterialStatisticsData()\r\n    })\r\n    // 初始化高频采购物料数据\r\n    this.fetchHighFrequencyMaterialData()\r\n    // 初始化供应商风险数据\r\n    this.fetchSupplierRiskData()\r\n\r\n    // 初始化新的物料名称列表（会自动触发默认选中PB块和数据获取）\r\n    this.fetchPurchaseAmountMaterialList()\r\n    this.fetchMarketPriceMaterialList()\r\n\r\n    // 初始化机旁库存数据\r\n    this.fetchFactoryDepOptions()\r\n\r\n    // 初始化订单至入库天数数据\r\n    this.fetchOrderToReceiptData()\r\n\r\n    // 初始化超期数据\r\n    this.fetchOverdueData()\r\n\r\n    this.setupResizeObserver()\r\n    this.initFullscreenListener()\r\n\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.resizeAllCharts)\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 清理定时器和事件监听器\r\n    this.clearAllIntervals()\r\n    this.removeFullscreenListener()\r\n    window.removeEventListener('resize', this.resizeAllCharts)\r\n\r\n    // 确保退出全屏模式\r\n    this.$store.dispatch('app/setFullscreenMode', false)\r\n  },\r\n\r\n  methods: {\r\n    // 初始化全屏监听器\r\n    initFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.on('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 移除全屏监听器\r\n    removeFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.off('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 处理全屏状态变化\r\n    handleFullscreenChange() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        const isFullscreen = screenfull.isFullscreen\r\n        this.$store.dispatch('app/setFullscreenMode', isFullscreen)\r\n\r\n        // 全屏状态变化后，重新调整图表大小\r\n        this.$nextTick(() => {\r\n          setTimeout(() => {\r\n            this.resizeAllCharts()\r\n          }, 300) // 给布局变化一些时间\r\n        })\r\n      }\r\n    },\r\n\r\n    // API调用方法\r\n    async getDashboardData(dimensionType) {\r\n      return await showData({ dimensionType: dimensionType })\r\n    },\r\n\r\n    async getItemTypeList(itemType) {\r\n      return await showItemTypeList({ itemType: itemType })\r\n    },\r\n\r\n    async getMaterialList(params) {\r\n      return await showMaterialList(params)\r\n    },\r\n\r\n    async getSupplierList(params) {\r\n      return await showSuppList(params)\r\n    },\r\n\r\n    async getYearlyAmount(params) {\r\n      return await showYearlyAmount(params)\r\n    },\r\n\r\n    async getRealTimeAmount() {\r\n      return await showRealTimeAmount()\r\n    },\r\n\r\n    async getCokingCoalAmount() {\r\n      return await showCokingCoalAmount()\r\n    },\r\n\r\n    async getKeyIndicators(params) {\r\n      return await showKeyIndicators(params)\r\n    },\r\n\r\n    async getHighFrequencyMaterialList(params) {\r\n      return await showHighFrequencyMaterialList(params)\r\n    },\r\n\r\n    async getPurchaseSuppRisk(params) {\r\n      return await showPurchaseSuppRisk(params)\r\n    },\r\n\r\n    // 根据dimensionType获取timeFlag\r\n    getTimeFlagByDimensionType(dimensionType) {\r\n      switch(dimensionType) {\r\n        case 1: return '03' // 近三个月\r\n        case 2: return '06' // 近六个月\r\n        case 3: return '12' // 近一年\r\n        default: return '03'\r\n      }\r\n    },\r\n\r\n    // 检查ECharts可用性\r\n    checkEchartsAvailability() {\r\n      if (!echarts) {\r\n        console.error('ECharts库未能加载，使用备用显示方式')\r\n        document.querySelectorAll('.chart').forEach(el => {\r\n          el.innerHTML = '<div class=\"chart-placeholder\">图表加载失败</div>'\r\n        })\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n\r\n    // 获取仪表板数据\r\n    async fetchDashboardData(dimensionTypeParam = 1) {\r\n      this.currentDimensionType = dimensionTypeParam\r\n\r\n      // 清除所有定时器\r\n      this.clearAllIntervals()\r\n\r\n      try {\r\n        // 并行获取仪表板数据和关键指标数据\r\n        const [dashboardResponse, keyIndicatorsResponse] = await Promise.all([\r\n          this.getDashboardData(dimensionTypeParam),\r\n          this.getKeyIndicators({ dimensionType: dimensionTypeParam })\r\n        ])\r\n\r\n        // 处理仪表板数据\r\n        if (dashboardResponse && dashboardResponse.data) {\r\n          this.dashboardData = dashboardResponse.data\r\n          console.log('获取仪表板数据成功:', this.dashboardData)\r\n        } else {\r\n          console.error('API数据格式不正确或缺少data字段', dashboardResponse)\r\n          this.showErrorMessage('API数据格式不正确或缺少data字段')\r\n        }\r\n\r\n        // 处理关键指标数据\r\n        if (keyIndicatorsResponse && keyIndicatorsResponse.data) {\r\n          this.purchaseStats = keyIndicatorsResponse.data || {}\r\n          console.log('获取关键指标数据成功:', this.purchaseStats)\r\n        } else {\r\n          console.error('获取关键指标数据失败', keyIndicatorsResponse)\r\n          this.purchaseStats = {}\r\n        }\r\n\r\n        this.initAllCharts()\r\n      } catch (error) {\r\n        console.error('API请求或数据处理失败', error)\r\n        this.showErrorMessage('数据加载失败: ' + error.message)\r\n      }\r\n    },\r\n\r\n    // 显示错误信息\r\n    showErrorMessage(message) {\r\n      document.querySelectorAll('.chart').forEach(chart => {\r\n        chart.innerHTML = `<div class=\"chart-placeholder\">${message}</div>`\r\n      })\r\n    },\r\n\r\n    // 时间过滤器变化处理\r\n    handleTimeFilterChange(filterId, dimensionType) {\r\n      this.activeFilter = filterId\r\n      this.currentDimensionType = dimensionType\r\n      console.log('选择的时间范围:', filterId, '维度:', dimensionType)\r\n\r\n      this.clearAllIntervals()\r\n      this.fetchDashboardData(dimensionType)\r\n      // 同时更新高频物料数据\r\n      this.fetchHighFrequencyMaterialData()\r\n      // 同时更新供应商风险数据\r\n      this.fetchSupplierRiskData()\r\n      // 同时更新物料入库统计数据\r\n      this.fetchMaterialStatisticsData()\r\n      // 注意：价格趋势数据只在用户主动点击按钮时获取，不在时间过滤器变化时自动获取\r\n\r\n      // 同时更新新的物料列表（用于下拉框选项），但不会自动触发数据获取\r\n      this.fetchPurchaseAmountMaterialList()\r\n      this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 清除所有定时器\r\n    clearAllIntervals() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance && instance.intervalId) {\r\n          clearInterval(instance.intervalId)\r\n          instance.intervalId = null\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重新调整所有图表大小\r\n    resizeAllCharts() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance) {\r\n          try {\r\n            instance.resize()\r\n          } catch(err) {\r\n            console.error('图表大小调整失败:', err)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 初始化所有图表\r\n    initAllCharts() {\r\n      const timestamp = new Date().toLocaleTimeString()\r\n      console.log(`initAllCharts started [${timestamp}]`)\r\n      try {\r\n        // 注意：实时库存图表和矿焦煤库存图表会在各自数据获取完成后单独初始化\r\n        // 注意：第一个订单至入库天数图表使用真实数据，会在fetchOrderToReceiptData完成后单独初始化\r\n        // 注意：物料入库统计图表会在fetchMaterialStatisticsData完成后单独初始化\r\n        // 注意：机旁库存图表会在fetchFactoryStockData完成后单独初始化\r\n\r\n        // 初始化其他订单至入库天数图表（第一个会在数据获取完成后单独初始化）\r\n        this.initFactoryStockChart()\r\n        this.initCokingCoalLineChart()\r\n        this.initMaterialStatisticsChart()\r\n\r\n        // 初始化物料词云图\r\n        this.initMaterialCloud()\r\n\r\n        // 初始化TOP供应商图\r\n        this.initTopSuppliersChart()\r\n        this.populateItemDropdown('topSuppliersFilter', 1, 'topSuppliersOptions')\r\n\r\n        // 注意：供应商风险图表会在fetchSupplierRiskData完成后单独初始化\r\n\r\n        // 注意：实时超期数图表会在fetchOverdueData完成后单独初始化\r\n\r\n        // 注意：采购价格趋势图会在fetchPriceAndStoreData完成后单独初始化\r\n\r\n        console.log('所有图表初始化完成')\r\n      } catch (err) {\r\n        console.error('图表初始化主流程失败:', err)\r\n        this.showErrorMessage('图表初始化失败: ' + err.message)\r\n      }\r\n    },\r\n\r\n    // 填充物料类型下拉框\r\n    async populateItemDropdown(selectElementId, itemType, dataPropertyName) {\r\n      try {\r\n        const response = await this.getItemTypeList(itemType)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this[dataPropertyName] = response.data\r\n        } else {\r\n          console.error(`Invalid data format from showItemTypeList for itemType ${itemType}:`, response)\r\n          this[dataPropertyName] = []\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error fetching item types for ${selectElementId}:`, error)\r\n        this[dataPropertyName] = []\r\n      }\r\n    },\r\n\r\n    // 下拉框变化处理方法\r\n    async handleTopSuppliersFilterChange() {\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async handleOrderTypeChange() {\r\n      console.log('排序类型变化:', this.selectedOrderType)\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async refreshTopSuppliersChart() {\r\n      console.log(`Top supplier filter selected item ID: ${this.selectedTopSuppliersFilter}, orderType: ${this.selectedOrderType}`)\r\n      const myChart = this.chartInstances.topSuppliersChart\r\n      if (!myChart) {\r\n        console.error(\"TOP10供应商图表实例未找到\")\r\n        return\r\n      }\r\n\r\n      if (myChart.intervalId) {\r\n        clearInterval(myChart.intervalId)\r\n        myChart.intervalId = null\r\n      }\r\n\r\n      if (!this.selectedTopSuppliersFilter || this.selectedTopSuppliersFilter === \"\") {\r\n        // 使用原始数据，但需要根据orderType重新获取\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n            newSupplierData = this.originalTopSuppliersData\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          this.renderAndPaginateTopSuppliers(myChart, this.originalTopSuppliersData)\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      } else {\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            itemId: this.selectedTopSuppliersFilter,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          document.getElementById('topSuppliersChart').innerHTML = '<div class=\"chart-placeholder\">供应商数据加载失败</div>'\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      }\r\n    },\r\n\r\n    // 设置大小调整观察器\r\n    setupResizeObserver() {\r\n      const resizeObserver = new ResizeObserver(entries => {\r\n        for (let entry of entries) {\r\n          const charts = entry.target.querySelectorAll('.chart')\r\n          charts.forEach(chart => {\r\n            if (chart.id) {\r\n              const instance = echarts.getInstanceByDom(document.getElementById(chart.id))\r\n              if (instance) {\r\n                instance.resize()\r\n              }\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      document.querySelectorAll('.card').forEach(card => {\r\n        resizeObserver.observe(card)\r\n      })\r\n    },\r\n\r\n    toggleFullscreen() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.toggle()\r\n      } else {\r\n        this.$message({\r\n          message: '您的浏览器不支持全屏功能',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n\r\n    async handleOrderFactoryDepChange() {\r\n      console.log('订单至入库天数 - 分厂变化:', this.selectedOrderFactoryDep)\r\n      // 获取新数据并更新图表\r\n      await this.fetchOrderToReceiptData()\r\n    },\r\n\r\n    async handleOrderMaterialTypeChange() {\r\n      console.log('订单至入库天数 - 物料类型变化:', this.selectedOrderMaterialType)\r\n      // 获取新数据并更新图表\r\n      await this.fetchOrderToReceiptData()\r\n    },\r\n\r\n    // 获取订单至入库天数数据\r\n    async fetchOrderToReceiptData() {\r\n      try {\r\n        console.log('fetchOrderToReceiptData - 开始获取数据')\r\n        console.log('fetchOrderToReceiptData - 调用API: showPurchasePlanList')\r\n\r\n        const response = await showPurchasePlanList({\r\n          dimensionType: 3 // 使用固定的维度类型\r\n        })\r\n        console.log('fetchOrderToReceiptData - API调用成功')\r\n        console.log('fetchOrderToReceiptData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          console.log('fetchOrderToReceiptData - 原始数据长度:', response.data.length)\r\n          console.log('fetchOrderToReceiptData - 原始数据前3条:', response.data.slice(0, 3))\r\n\r\n          // 检查数据结构\r\n          console.log('fetchOrderToReceiptData - 检查数据结构')\r\n\r\n          // 检查是否有countType字段\r\n          const hasCountType = response.data.some(item => item.countType !== undefined)\r\n          console.log('fetchOrderToReceiptData - 是否有countType字段:', hasCountType)\r\n\r\n          let dataToProcess = response.data\r\n\r\n          if (hasCountType) {\r\n            // 检查所有可能的countType值\r\n            const countTypes = [...new Set(response.data.map(item => item.countType))]\r\n            console.log('fetchOrderToReceiptData - 所有countType值:', countTypes)\r\n\r\n            // 筛选countType=\"B\"的数据\r\n            const filteredData = response.data.filter(item => item.countType === 'B')\r\n            console.log('fetchOrderToReceiptData - 筛选后的数据:', filteredData)\r\n            console.log('fetchOrderToReceiptData - 筛选后数据长度:', filteredData.length)\r\n\r\n            if (filteredData.length === 0) {\r\n              console.warn('fetchOrderToReceiptData - 没有找到countType=\"B\"的数据，使用所有数据')\r\n              dataToProcess = response.data\r\n            } else {\r\n              dataToProcess = filteredData\r\n            }\r\n          } else {\r\n            console.log('fetchOrderToReceiptData - 没有countType字段，使用所有数据')\r\n          }\r\n\r\n          if (dataToProcess.length === 0) {\r\n            console.warn('fetchOrderToReceiptData - 没有可用数据，不显示图表')\r\n            this.orderToReceiptData = []\r\n          } else {\r\n            // 检查第一条数据的所有字段\r\n            console.log('fetchOrderToReceiptData - 第一条数据的所有字段:', Object.keys(dataToProcess[0]))\r\n            console.log('fetchOrderToReceiptData - 第一条数据内容:', dataToProcess[0])\r\n            console.log('fetchOrderToReceiptData - 第一条数据的midDays字段:', dataToProcess[0].midDays)\r\n\r\n            // 按period排序（从小到大）\r\n            const sortedData = dataToProcess.sort((a, b) => {\r\n              const periodA = parseInt(a.period) || 0\r\n              const periodB = parseInt(b.period) || 0\r\n              return periodA - periodB\r\n            })\r\n\r\n            // 转换数据格式，尝试多个可能的字段名\r\n            this.orderToReceiptData = sortedData.map(item => {\r\n              // 尝试多个可能的平均天数字段名\r\n              let avgDays = 0\r\n              if (item.avgDays !== undefined) {\r\n                avgDays = parseFloat(item.avgDays) || 0\r\n              } else if (item.avgDay !== undefined) {\r\n                avgDays = parseFloat(item.avgDay) || 0\r\n              } else if (item.averageDays !== undefined) {\r\n                avgDays = parseFloat(item.averageDays) || 0\r\n              } else if (item.days !== undefined) {\r\n                avgDays = parseFloat(item.days) || 0\r\n              } else if (item.dayCount !== undefined) {\r\n                avgDays = parseFloat(item.dayCount) || 0\r\n              }\r\n\r\n              // 提取中位数字段\r\n              let midDays = 0\r\n              if (item.midDays !== undefined) {\r\n                midDays = parseFloat(item.midDays) || 0\r\n              } else if (item.midDay !== undefined) {\r\n                midDays = parseFloat(item.midDay) || 0\r\n              } else if (item.medianDays !== undefined) {\r\n                midDays = parseFloat(item.medianDays) || 0\r\n              } else if (item.median !== undefined) {\r\n                midDays = parseFloat(item.median) || 0\r\n              }\r\n\r\n              console.log(`处理数据项 ${item.period}: 平均天数 = ${avgDays}, 中位数 = ${midDays}`)\r\n\r\n              return {\r\n                period: this.formatPeriod(item.period),\r\n                avgDays: avgDays,\r\n                midDays: midDays,\r\n                // 添加原始数据用于调试\r\n                originalData: item\r\n              }\r\n            })\r\n\r\n            console.log('fetchOrderToReceiptData - 处理后的数据:', this.orderToReceiptData)\r\n          }\r\n        } else {\r\n          console.error('获取订单至入库天数数据失败，不显示图表', response)\r\n          this.orderToReceiptData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取订单至入库天数数据失败:', error)\r\n        console.error('错误详情:', error.message)\r\n        this.orderToReceiptData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMonthlyInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 格式化期间（202508 -> 2025.8）\r\n    formatPeriod(period) {\r\n      if (!period) return ''\r\n      const periodStr = period.toString()\r\n      if (periodStr.length === 6) {\r\n        const year = periodStr.substring(0, 4)\r\n        const month = parseInt(periodStr.substring(4, 6))\r\n        return `${year}.${month}`\r\n      }\r\n      return periodStr\r\n    },\r\n\r\n\r\n\r\n    // 第二个订单至入库天数模块的事件处理\r\n    async handleCokingCoalFactoryDepChange() {\r\n      console.log('第二个订单至入库天数 - 分厂变化:', this.selectedCokingCoalFactoryDep)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initCokingCoalLineChart()\r\n    },\r\n\r\n    async handleCokingCoalMaterialTypeChange() {\r\n      console.log('第二个订单至入库天数 - 物料类型变化:', this.selectedCokingCoalMaterialType)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initCokingCoalLineChart()\r\n    },\r\n\r\n    // 第三个订单至入库天数模块的事件处理\r\n    async handleMaterialFactoryDepChange() {\r\n      console.log('第三个订单至入库天数 - 分厂变化:', this.selectedMaterialFactoryDep)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initMaterialStatisticsChart()\r\n    },\r\n\r\n    async handleMaterialMaterialTypeChange() {\r\n      console.log('第三个订单至入库天数 - 物料类型变化:', this.selectedMaterialMaterialType)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initMaterialStatisticsChart()\r\n    },\r\n\r\n    // 实时超期数相关方法\r\n    async handleOverdueFactoryDepChange() {\r\n      console.log('实时超期数 - 分厂变化:', this.selectedOverdueFactoryDep)\r\n      await this.fetchOverdueData()\r\n    },\r\n\r\n    async fetchOverdueData() {\r\n      try {\r\n        // 这里可以根据selectedOverdueFactoryDep获取真实数据\r\n        // 暂时使用模拟数据\r\n        this.overdueData = this.getMockOverdueData()\r\n\r\n        // 数据获取完成后重新初始化图表\r\n        this.$nextTick(() => {\r\n          this.initOverdueChart()\r\n        })\r\n      } catch (error) {\r\n        console.error('获取超期数据失败:', error)\r\n        this.overdueData = this.getMockOverdueData()\r\n        this.$nextTick(() => {\r\n          this.initOverdueChart()\r\n        })\r\n      }\r\n    },\r\n\r\n    // 生成模拟超期数据\r\n    getMockOverdueData() {\r\n      return [\r\n        { materialType: '原材料', overdueNotReceived: 25, overdueNotUsed: 18 },\r\n        { materialType: '辅耐材', overdueNotReceived: 12, overdueNotUsed: 8 },\r\n        { materialType: '材料类', overdueNotReceived: 35, overdueNotUsed: 22 },\r\n        { materialType: '通用备件', overdueNotReceived: 18, overdueNotUsed: 15 },\r\n        { materialType: '专用备件', overdueNotReceived: 28, overdueNotUsed: 20 },\r\n        { materialType: '办公', overdueNotReceived: 5, overdueNotUsed: 3 }\r\n      ]\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    async fetchRealTimeInventoryData() {\r\n      try {\r\n        const response = await this.getRealTimeAmount()\r\n        console.log('fetchRealTimeInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.realTimeInventoryData = response.data || []\r\n          console.log('fetchRealTimeInventoryData - 设置的数据:', this.realTimeInventoryData)\r\n        } else {\r\n          console.error('获取实时库存数据失败，使用模拟数据', response)\r\n          // 使用模拟数据\r\n          this.realTimeInventoryData = this.getMockRealTimeData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取实时库存数据失败，使用模拟数据:', error)\r\n        // 使用模拟数据\r\n        this.realTimeInventoryData = this.getMockRealTimeData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initRealTimeInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟实时库存数据\r\n    getMockRealTimeData() {\r\n      return [\r\n        {\r\n          materialType: 'A',\r\n          materialName: '通用备件',\r\n          centerInventoryAmount: 1250.30,\r\n          machineSideInventoryAmount: 380.50,\r\n          totalInventoryAmount: 1630.80\r\n        },\r\n        {\r\n          materialType: 'B',\r\n          materialName: '专用备件',\r\n          centerInventoryAmount: 980.75,\r\n          machineSideInventoryAmount: 420.25,\r\n          totalInventoryAmount: 1401.00\r\n        },\r\n        {\r\n          materialType: 'C',\r\n          materialName: '材料类',\r\n          centerInventoryAmount: 2150.60,\r\n          machineSideInventoryAmount: 650.40,\r\n          totalInventoryAmount: 2801.00\r\n        },\r\n        {\r\n          materialType: 'D',\r\n          materialName: '原材料',\r\n          centerInventoryAmount: 3200.90,\r\n          machineSideInventoryAmount: 890.10,\r\n          totalInventoryAmount: 4091.00\r\n        },\r\n        {\r\n          materialType: 'E',\r\n          materialName: '辅耐材',\r\n          centerInventoryAmount: 1580.40,\r\n          machineSideInventoryAmount: 320.60,\r\n          totalInventoryAmount: 1901.00\r\n        },\r\n        {\r\n          materialType: 'G',\r\n          materialName: '办公',\r\n          centerInventoryAmount: 150.20,\r\n          machineSideInventoryAmount: 50.80,\r\n          totalInventoryAmount: 201.00\r\n        }\r\n      ]\r\n    },\r\n\r\n    async fetchCokingCoalInventoryData() {\r\n      try {\r\n        const response = await this.getCokingCoalAmount()\r\n        console.log('fetchCokingCoalInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.cokingCoalInventoryData = response.data || []\r\n          console.log('fetchCokingCoalInventoryData - 设置的数据:', this.cokingCoalInventoryData)\r\n        } else {\r\n          console.error('获取矿焦煤库存数据失败', response)\r\n          this.cokingCoalInventoryData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取矿焦煤库存数据失败:', error)\r\n        this.cokingCoalInventoryData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 物料入库统计相关方法\r\n    async handleMaterialCategoryChange() {\r\n      console.log('物料类别变化:', this.selectedMaterialCategory)\r\n      this.selectedMaterialItem = '' // 重置第二个下拉框\r\n      await this.updateMaterialItemOptions()\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async handleMaterialItemChange() {\r\n      console.log('物料项目变化:', this.selectedMaterialItem)\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async updateMaterialItemOptions() {\r\n      if (this.selectedMaterialCategory === '1') {\r\n        // 大类：只有全部选项\r\n        this.materialItemOptions = []\r\n      } else {\r\n        // 中类、细类、叶类：获取对应的选项\r\n        const itemType = parseInt(this.selectedMaterialCategory) - 1 // 1->0, 2->1, 3->2, 4->3\r\n        try {\r\n          const response = await this.getItemTypeList(itemType)\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            this.materialItemOptions = response.data\r\n          } else {\r\n            this.materialItemOptions = []\r\n          }\r\n        } catch (error) {\r\n          console.error('获取物料项目选项失败:', error)\r\n          this.materialItemOptions = []\r\n        }\r\n      }\r\n    },\r\n\r\n    async fetchMaterialStatisticsData() {\r\n      try {\r\n        const params = {\r\n          itemType: parseInt(this.selectedMaterialCategory),\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        // 如果选择了具体物料项目，添加itemId参数\r\n        if (this.selectedMaterialItem && this.selectedMaterialItem !== '') {\r\n          params.itemId = this.selectedMaterialItem\r\n        }\r\n\r\n        console.log('fetchMaterialStatisticsData - 请求参数:', params)\r\n        const response = await this.getMaterialList(params)\r\n        console.log('fetchMaterialStatisticsData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.materialStatisticsData = response.data || []\r\n          console.log('fetchMaterialStatisticsData - 设置的数据:', this.materialStatisticsData)\r\n        } else {\r\n          console.error('获取物料统计数据失败，使用模拟数据', response)\r\n          this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料统计数据失败，使用模拟数据:', error)\r\n        this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialStatisticsChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟物料统计数据\r\n    getMockMaterialStatisticsData() {\r\n      return [\r\n        { itemName: '通用备件', inAmt: 1250.30, arriveRate: 85.5 },\r\n        { itemName: '专用备件', inAmt: 980.75, arriveRate: 78.2 },\r\n        { itemName: '材料类', inAmt: 2150.60, arriveRate: 92.1 },\r\n        { itemName: '原材料', inAmt: 3200.90, arriveRate: 88.7 },\r\n        { itemName: '辅耐材', inAmt: 1580.40, arriveRate: 91.3 },\r\n        { itemName: '办公', inAmt: 150.20, arriveRate: 95.0 }\r\n      ]\r\n    },\r\n\r\n    async fetchHighFrequencyMaterialData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          codeType: this.selectedCodeType,\r\n          itemType: this.selectedItemType\r\n        }\r\n\r\n        console.log('fetchHighFrequencyMaterialData - 请求参数:', params)\r\n        const response = await this.getHighFrequencyMaterialList(params)\r\n        console.log('fetchHighFrequencyMaterialData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.highFrequencyMaterialData = response.data || []\r\n          console.log('fetchHighFrequencyMaterialData - 设置的数据:', this.highFrequencyMaterialData)\r\n        } else {\r\n          console.error('获取高频物料数据失败，使用模拟数据', response)\r\n          this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取高频物料数据失败，使用模拟数据:', error)\r\n        this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialCloud()\r\n      })\r\n    },\r\n\r\n    // 生成模拟高频物料数据\r\n    getMockHighFrequencyData() {\r\n      return [\r\n        { itemName: '粗粉', inAmt: 392467.2, inNum: 5421293 },\r\n        { itemName: '精粉', inAmt: 280350.5, inNum: 4250180 },\r\n        { itemName: '球团', inAmt: 195200.8, inNum: 3180970 },\r\n        { itemName: '烧结', inAmt: 150420.3, inNum: 2890540 },\r\n        { itemName: '焦炭', inAmt: 125680.7, inNum: 2350210 },\r\n        { itemName: '煤炭', inAmt: 98750.2, inNum: 1980760 }\r\n      ]\r\n    },\r\n\r\n    async handleCodeTypeChange() {\r\n      console.log('大类类型变化:', this.selectedCodeType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    async handleItemTypeChange() {\r\n      console.log('维度变化:', this.selectedItemType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    // 获取供应商风险数据\r\n    async fetchSupplierRiskData() {\r\n      try {\r\n        const params = {\r\n          timeFlag: this.getTimeFlagByDimensionType(this.currentDimensionType)\r\n        }\r\n\r\n        console.log('fetchSupplierRiskData - 请求参数:', params)\r\n        const response = await this.getPurchaseSuppRisk(params)\r\n        console.log('fetchSupplierRiskData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.supplierRiskData = response.data || []\r\n          console.log('fetchSupplierRiskData - 设置的数据:', this.supplierRiskData)\r\n        } else {\r\n          console.error('获取供应商风险数据失败', response)\r\n          this.supplierRiskData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取供应商风险数据失败:', error)\r\n        this.supplierRiskData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initSupplierRiskChart()\r\n      })\r\n    },\r\n\r\n    // 获取多个物料的AI价格预测\r\n    async fetchMultiplePricePredictions(materialNames) {\r\n      this.predictionLoading = true\r\n      this.pricePredictions = [] // 清空之前的预测结果\r\n\r\n      try {\r\n        // 并行调用所有物料的预测接口\r\n        const predictionPromises = materialNames.map(async (materialName) => {\r\n          try {\r\n            const params = {\r\n              materialName: materialName,\r\n              materialType: '1' // 默认使用矿石类型，可以根据需要调整\r\n            }\r\n\r\n            console.log(`fetchPricePrediction - ${materialName} 请求参数:`, params)\r\n            const response = await getMaterialFuturePrice(params)\r\n            console.log(`fetchPricePrediction - ${materialName} 完整响应:`, response)\r\n\r\n            if (response && response.code && response.code === 200 && response.data) {\r\n              return {\r\n                materialName: materialName,\r\n                question: response.data.question || `关于${materialName}的价格预测`,\r\n                prediction: response.data.answer || response.data.prediction || response.msg,\r\n                success: response.data.success !== false\r\n              }\r\n            } else {\r\n              console.error(`获取${materialName}价格预测数据失败`, response)\r\n              return {\r\n                materialName: materialName,\r\n                question: `关于${materialName}的价格预测`,\r\n                prediction: `获取${materialName}价格预测失败`,\r\n                success: false\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error(`获取${materialName}价格预测数据失败:`, error)\r\n            return {\r\n              materialName: materialName,\r\n              question: `关于${materialName}的价格预测`,\r\n              prediction: `获取${materialName}价格预测失败：${error.message}`,\r\n              success: false\r\n            }\r\n          }\r\n        })\r\n\r\n        // 等待所有预测结果\r\n        const results = await Promise.all(predictionPromises)\r\n        this.pricePredictions = results\r\n        console.log('fetchMultiplePricePredictions - 设置的预测数据:', this.pricePredictions)\r\n\r\n        const successCount = results.filter(r => r.success).length\r\n        const totalCount = results.length\r\n\r\n        if (successCount > 0) {\r\n          this.$message.success(`成功获取${successCount}/${totalCount}个物料的价格预测`)\r\n        } else {\r\n          this.$message.error('所有物料的价格预测获取失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('批量获取价格预测数据失败:', error)\r\n        this.$message.error('批量获取价格预测失败：' + error.message)\r\n      } finally {\r\n        this.predictionLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取物料名称列表\r\n    async fetchMaterialNameList() {\r\n      try {\r\n        const params = {\r\n          category: parseInt(this.selectedMaterialCategory)\r\n        }\r\n\r\n        const response = await getMaterialNameList(params)\r\n        console.log('fetchMaterialNameList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.materialNameOptions = response.data\r\n          console.log('fetchMaterialNameList - 设置的数据:', this.materialNameOptions)\r\n\r\n          // 设置默认选中PB块，如果存在的话\r\n          const pbMaterial = this.materialNameOptions.find(item => item.itemName === 'PB块')\r\n          if (pbMaterial) {\r\n            this.selectedMaterial = 'PB块'\r\n          } else if (this.materialNameOptions.length > 0) {\r\n            // 如果没有PB块，选择第一个\r\n            this.selectedMaterial = this.materialNameOptions[0].itemName\r\n          }\r\n\r\n          // 获取价格数据\r\n          this.fetchPriceAndStoreData()\r\n        } else {\r\n          console.error('获取物料名称列表失败', response)\r\n          this.materialNameOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料名称列表失败:', error)\r\n        this.materialNameOptions = []\r\n      }\r\n    },\r\n\r\n    // 获取物料价格和采购量数据\r\n    async fetchPriceAndStoreData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemName: this.selectedMaterial\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStore(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n          this.priceAndStoreData = response.data[0] // 取第一个元素\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.priceAndStoreData)\r\n        } else {\r\n          console.error('获取价格和采购量数据失败', response)\r\n          this.priceAndStoreData = null\r\n        }\r\n      } catch (error) {\r\n        console.error('获取价格和采购量数据失败:', error)\r\n        this.priceAndStoreData = null\r\n      }\r\n\r\n      // 数据获取完成后重新初始化价格趋势图\r\n      this.$nextTick(() => {\r\n        this.initPriceTrendChart()\r\n      })\r\n    },\r\n\r\n    // 处理物资类型切换\r\n    async handleMaterialCategoryTypeChange() {\r\n      console.log('物资类型变化:', this.selectedMaterialCategory)\r\n      // 重新获取物料名称列表\r\n      await this.fetchMaterialNameList()\r\n    },\r\n\r\n    // 处理物料选择变化\r\n    async handleMaterialChange() {\r\n      console.log('物料选择变化:', this.selectedMaterial)\r\n      await this.fetchPriceAndStoreData()\r\n      // 不再自动触发AI预测，等用户点击按钮后再触发\r\n    },\r\n\r\n    calculateRealTimeInventoryTotal() {\r\n      let total = 0\r\n      if (this.realTimeInventoryData && this.realTimeInventoryData.length > 0) {\r\n        this.realTimeInventoryData.forEach(item => {\r\n          total += parseFloat(item.totalInventoryAmount) || 0\r\n        })\r\n      }\r\n      return total.toFixed(2)\r\n    },\r\n\r\n    calculateCokingCoalTotal() {\r\n      let total = 0\r\n      if (this.cokingCoalInventoryData && this.cokingCoalInventoryData.length > 0) {\r\n        // 找到所有数据中的最新日期\r\n        let latestDate = ''\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            item.purchaseCokingDailyDetailList.forEach(detail => {\r\n              if (detail.instockDate > latestDate) {\r\n                latestDate = detail.instockDate\r\n              }\r\n            })\r\n          }\r\n        })\r\n\r\n        // 计算最新日期各个物料的库存量合计\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)\r\n            if (latestDetail) {\r\n              total += parseFloat(latestDetail.invQty) || 0\r\n            }\r\n          }\r\n        })\r\n      }\r\n      return (total / 10000).toFixed(2) // 转换为万吨\r\n    },\r\n\r\n    // 处理矿焦煤类型下拉框变化\r\n    async handleCokingCoalTypeChange() {\r\n      console.log('矿焦煤类型变化:', this.selectedCokingCoalType)\r\n      // 重新初始化图表以应用过滤\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 机旁库存相关方法\r\n    // 获取分厂选项列表\r\n    async fetchFactoryDepOptions() {\r\n      try {\r\n        const response = await getDepNameList()\r\n        console.log('fetchFactoryDepOptions - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryDepOptions = response.data\r\n          console.log('fetchFactoryDepOptions - 设置的数据:', this.factoryDepOptions)\r\n        } else {\r\n          console.error('获取分厂选项列表失败', response)\r\n          this.factoryDepOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取分厂选项列表失败:', error)\r\n        this.factoryDepOptions = []\r\n      }\r\n\r\n      // 分厂选项加载完成后，图表会在initAllCharts中初始化\r\n    },\r\n\r\n    // 处理分厂选择变化\r\n    async handleFactoryDepChange() {\r\n      console.log('订单至入库天数 - 分厂选择变化:', this.selectedFactoryDep)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initFactoryStockChart()\r\n    },\r\n\r\n    // 处理物料类型选择变化\r\n    async handleFactoryMaterialTypeChange() {\r\n      console.log('订单至入库天数 - 物料类型选择变化:', this.selectedFactoryMaterialType)\r\n      // 暂时不取数据，只更新图表显示\r\n      this.initFactoryStockChart()\r\n    },\r\n\r\n    // 获取机旁库存数据\r\n    async fetchFactoryStockData() {\r\n      try {\r\n        const depName = this.selectedFactoryDep || '' // 空字符串表示全部\r\n        console.log('fetchFactoryStockData - 请求参数:', depName)\r\n\r\n        const response = await getListMonthly(depName)\r\n        console.log('fetchFactoryStockData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryStockData = response.data\r\n          console.log('fetchFactoryStockData - 设置的数据:', this.factoryStockData)\r\n        } else {\r\n          console.error('获取机旁库存数据失败', response)\r\n          this.factoryStockData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取机旁库存数据失败:', error)\r\n        this.factoryStockData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initFactoryStockChart()\r\n      })\r\n    },\r\n\r\n    // 新增方法：处理采购量曲线物料类型变化\r\n    async handlePurchaseAmountCategoriesChange() {\r\n      console.log('采购量曲线物料类型变化:', this.purchaseAmountCategories)\r\n      this.selectedPurchaseAmountMaterials = [] // 重置选中的物料\r\n      await this.fetchPurchaseAmountMaterialList()\r\n    },\r\n\r\n    // 新增方法：处理市场价曲线物料类型变化\r\n    async handleMarketPriceCategoriesChange() {\r\n      console.log('市场价曲线物料类型变化:', this.marketPriceCategories)\r\n      this.selectedMarketPriceMaterials = [] // 重置选中的物料\r\n      await this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 新增方法：获取采购量曲线物料列表\r\n    async fetchPurchaseAmountMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.purchaseAmountCategories,\r\n          curveType: 2, // 采购量曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchPurchaseAmountMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchPurchaseAmountMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.purchaseAmountMaterialOptions = response.data\r\n          console.log('fetchPurchaseAmountMaterialList - 设置的数据:', this.purchaseAmountMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedPurchaseAmountMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.purchaseAmountMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedPurchaseAmountMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 采购量曲线')\r\n\r\n              // 检查市场价曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取采购量曲线物料列表失败', response)\r\n          this.purchaseAmountMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取采购量曲线物料列表失败:', error)\r\n        this.purchaseAmountMaterialOptions = []\r\n      }\r\n    },\r\n\r\n    // 新增方法：获取市场价曲线物料列表\r\n    async fetchMarketPriceMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.marketPriceCategories,\r\n          curveType: 1, // 价格曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchMarketPriceMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchMarketPriceMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.marketPriceMaterialOptions = response.data\r\n          console.log('fetchMarketPriceMaterialList - 设置的数据:', this.marketPriceMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedMarketPriceMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.marketPriceMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedMarketPriceMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 市场价曲线')\r\n\r\n              // 检查采购量曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取市场价曲线物料列表失败', response)\r\n          this.marketPriceMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取市场价曲线物料列表失败:', error)\r\n        this.marketPriceMaterialOptions = []\r\n      }\r\n    },\r\n\r\n\r\n\r\n    // 新增方法：获取物料采购价格数据（用于新的价格趋势图）\r\n    async fetchPriceAndStoreDataForNewChart() {\r\n      if (this.selectedPurchaseAmountMaterials.length === 0 && this.selectedMarketPriceMaterials.length === 0) {\r\n        this.$message.warning('请至少选择一个物料')\r\n        return\r\n      }\r\n\r\n      this.fetchingPriceData = true\r\n      try {\r\n        // 构建itemList\r\n        const itemList = []\r\n\r\n        // 添加采购量曲线的物料\r\n        this.selectedPurchaseAmountMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 2, // 采购量曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        // 添加市场价曲线的物料\r\n        this.selectedMarketPriceMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 1, // 价格曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.newPriceAndStoreData = response.data\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.newPriceAndStoreData)\r\n\r\n          // 重新渲染图表\r\n          this.$nextTick(() => {\r\n            this.initNewPriceTrendChart()\r\n          })\r\n\r\n          // 获取所有选中物料的去重列表\r\n          const allSelectedMaterials = [...new Set([\r\n            ...this.selectedPurchaseAmountMaterials,\r\n            ...this.selectedMarketPriceMaterials\r\n          ])]\r\n\r\n          // 为每个物料调用AI预测接口\r\n          if (allSelectedMaterials.length > 0) {\r\n            this.fetchMultiplePricePredictions(allSelectedMaterials)\r\n          }\r\n\r\n          // 如果市场价曲线有选中物料，获取相似物料信息\r\n          if (this.selectedMarketPriceMaterials.length > 0) {\r\n            this.fetchSimilarMaterials(this.selectedMarketPriceMaterials)\r\n          } else {\r\n            // 清空相似物料数据\r\n            this.similarMaterialsData = []\r\n          }\r\n\r\n          this.$message.success('数据获取成功')\r\n        } else {\r\n          console.error('获取物料采购价格数据失败', response)\r\n          this.$message.error('获取数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料采购价格数据失败:', error)\r\n        this.$message.error('获取数据失败：' + error.message)\r\n      } finally {\r\n        this.fetchingPriceData = false\r\n      }\r\n    },\r\n\r\n    // 获取相似物料信息\r\n    async fetchSimilarMaterials(itemNames) {\r\n      this.similarMaterialsLoading = true\r\n      try {\r\n        const params = {\r\n          itemNames: itemNames\r\n        }\r\n\r\n        console.log('fetchSimilarMaterials - 请求参数:', params)\r\n        const response = await listSimilarByItemNames(params)\r\n        console.log('fetchSimilarMaterials - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.similarMaterialsData = response.data\r\n          console.log('fetchSimilarMaterials - 设置的数据:', this.similarMaterialsData)\r\n        } else {\r\n          console.error('获取相似物料数据失败', response)\r\n          this.similarMaterialsData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取相似物料数据失败:', error)\r\n        this.similarMaterialsData = []\r\n      } finally {\r\n        this.similarMaterialsLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取排名样式类\r\n    getRankClass(rank) {\r\n      if (rank === 1) return 'rank-first'\r\n      if (rank === 2) return 'rank-second'\r\n      if (rank === 3) return 'rank-third'\r\n      return 'rank-default'\r\n    },\r\n\r\n    // 获取商品分类名称\r\n    getCategoryName(category) {\r\n      const categoryMap = {\r\n        1: '矿石',\r\n        2: '煤炭',\r\n        3: '合金',\r\n        4: '废钢'\r\n      }\r\n      return categoryMap[category] || '未知'\r\n    },\r\n\r\n    // 获取价格类型名称\r\n    getPriceTypeName(priceType) {\r\n      const priceTypeMap = {\r\n        1: '现货价',\r\n        2: '市场采购到厂价',\r\n        3: '兴澄废钢收购价(车运)',\r\n        4: '兴澄废钢收购价(船运)',\r\n        5: '沙钢废钢收购价(车运)',\r\n        6: '沙钢废钢收购价(船运)'\r\n      }\r\n      return priceTypeMap[priceType] || '未知'\r\n    },\r\n\r\n    // 打开对比弹框\r\n    openComparisonDialog(item) {\r\n      console.log('openComparisonDialog - 传入的item数据:', item)\r\n      this.currentComparison = { ...item }\r\n      console.log('openComparisonDialog - 设置的currentComparison:', this.currentComparison)\r\n      this.comparisonDialogVisible = true\r\n\r\n      // 弹框打开后获取对比数据\r\n      this.$nextTick(() => {\r\n        this.fetchComparisonData()\r\n      })\r\n    },\r\n\r\n    // 关闭对比弹框\r\n    closeComparisonDialog() {\r\n      this.comparisonDialogVisible = false\r\n      this.currentComparison = {}\r\n      this.comparisonPriceData = null\r\n\r\n      // 清理图表实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n          this.comparisonChartInstance = null\r\n        } catch (err) {\r\n          console.error('清理对比图表实例失败:', err)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 获取对比数据（独立实现，不耦合现有趋势图）\r\n    async fetchComparisonData() {\r\n      this.comparisonChartLoading = true\r\n      try {\r\n        // 构建两个物料的对比请求，只获取价格曲线\r\n        const itemList = [\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.itemName\r\n          },\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.compareItemName\r\n          }\r\n        ]\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchComparisonData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchComparisonData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          // 对返回的数据进行筛选，确保基准物料和相似物料的指定价格类型都能被提取\r\n          const filteredData = []\r\n\r\n          // 获取基准物料和相似物料的目标价格类型名称\r\n          const basePriceTypeName = this.getPriceTypeName(this.currentComparison.priceType)\r\n          const comparePriceTypeName = this.getPriceTypeName(this.currentComparison.comparePriceType)\r\n\r\n          console.log('筛选条件:', {\r\n            baseItemName: this.currentComparison.itemName,\r\n            basePriceTypeName: basePriceTypeName,\r\n            compareItemName: this.currentComparison.compareItemName,\r\n            comparePriceTypeName: comparePriceTypeName\r\n          })\r\n\r\n          response.data.forEach(materialData => {\r\n            const filteredMaterialData = { ...materialData }\r\n\r\n            if (filteredMaterialData.procurementPriceVoList) {\r\n              // 只保留匹配的价格类型\r\n              filteredMaterialData.procurementPriceVoList = filteredMaterialData.procurementPriceVoList.filter(priceGroup => {\r\n                let isMatch = false\r\n                // 基准物料：匹配物料名称和基准价格类型\r\n                if (materialData.itemName === this.currentComparison.itemName) {\r\n                  isMatch = priceGroup.priceName === basePriceTypeName\r\n                  console.log(`基准物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${basePriceTypeName}] 匹配:${isMatch}`)\r\n                }\r\n\r\n                if(isMatch){\r\n                  return isMatch\r\n                }else{\r\n                  if (materialData.itemName === this.currentComparison.compareItemName) {\r\n                    const isMatch = priceGroup.priceName === comparePriceTypeName\r\n                    console.log(`相似物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${comparePriceTypeName}] 匹配:${isMatch}`)\r\n                    return isMatch\r\n                  }\r\n                }\r\n\r\n\r\n                return false\r\n              })\r\n\r\n              console.log(111111111)\r\n              console.log(filteredMaterialData.procurementPriceVoList)\r\n\r\n              // 只有当该物料有匹配的价格类型时才加入结果\r\n              if (filteredMaterialData.procurementPriceVoList.length > 0) {\r\n                filteredData.push(filteredMaterialData)\r\n                console.log(`添加物料[${materialData.itemName}]，包含${filteredMaterialData.procurementPriceVoList.length}个价格组`)\r\n              }\r\n            }\r\n          })\r\n\r\n          this.comparisonPriceData = filteredData\r\n          console.log('fetchComparisonData - 筛选后的数据:', this.comparisonPriceData)\r\n          console.log('筛选结果统计:', {\r\n            totalMaterials: filteredData.length,\r\n            materials: filteredData.map(m => ({\r\n              name: m.itemName,\r\n              priceGroupCount: m.procurementPriceVoList?.length || 0,\r\n              priceGroups: m.procurementPriceVoList?.map(p => p.priceName) || []\r\n            }))\r\n          })\r\n\r\n          // 渲染对比图表\r\n          this.$nextTick(() => {\r\n            this.renderComparisonChart()\r\n          })\r\n        } else {\r\n          console.error('获取对比数据失败', response)\r\n          this.$message.error('获取对比数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取对比数据失败:', error)\r\n        this.$message.error('获取对比数据失败：' + error.message)\r\n      } finally {\r\n        this.comparisonChartLoading = false\r\n      }\r\n    },\r\n\r\n    // 渲染对比图表（独立实现，不耦合现有趋势图）\r\n    renderComparisonChart() {\r\n      const chartDom = document.getElementById('comparisonChart')\r\n      if (!chartDom) {\r\n        console.error('找不到对比图表DOM元素')\r\n        return\r\n      }\r\n\r\n      // 清理现有实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n        } catch (err) {\r\n          console.error('清理现有对比图表实例失败:', err)\r\n        }\r\n      }\r\n\r\n      // 创建新的图表实例\r\n      try {\r\n        this.comparisonChartInstance = echarts.init(chartDom)\r\n      } catch (err) {\r\n        console.error('创建对比图表实例失败:', err)\r\n        return\r\n      }\r\n\r\n      if (!this.comparisonPriceData || this.comparisonPriceData.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      const formatDate = (dateStr) => {\r\n        const year = dateStr.substring(0, 4)\r\n        const month = dateStr.substring(4, 6)\r\n        const day = dateStr.substring(6, 8)\r\n        return `${year}年${month}月${day}日`\r\n      }\r\n\r\n      // 收集所有日期\r\n      let allDates = new Set()\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        if (materialData.procurementPriceVoList) {\r\n          materialData.procurementPriceVoList.forEach(priceGroup => {\r\n            if (priceGroup.priceList) {\r\n              priceGroup.priceList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      allDates = Array.from(allDates).sort()\r\n      const xAxisData = allDates.map(formatDate)\r\n\r\n      if (allDates.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      // 构建系列数据\r\n      const series = []\r\n      const legendData = []\r\n      const colors = ['#8fe9ff', '#ff9f7f', '#5fd8b6', '#ffb980']\r\n      let colorIndex = 0\r\n\r\n      console.log('=== 开始处理对比数据 ===')\r\n      console.log('对比数据总览:', {\r\n        materialCount: this.comparisonPriceData.length,\r\n        baseMaterial: this.currentComparison.itemName,\r\n        compareMaterial: this.currentComparison.compareItemName\r\n      })\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        const materialName = materialData.itemName\r\n        console.log(`\\n处理物料: ${materialName}`)\r\n\r\n        if (materialData.procurementPriceVoList) {\r\n          console.log(`  该物料有 ${materialData.procurementPriceVoList.length} 个价格组`)\r\n          materialData.procurementPriceVoList.forEach((priceGroup, index) => {\r\n            console.log(`  价格组 ${index + 1}: ${priceGroup.priceName}，数据点数量: ${priceGroup.priceList?.length || 0}`)\r\n          })\r\n\r\n          // 数据已经在fetchComparisonData中预先筛选过，这里直接处理所有匹配的价格组\r\n          materialData.procurementPriceVoList.forEach((priceGroup, groupIndex) => {\r\n            const priceData = allDates.map(date => {\r\n              const found = priceGroup.priceList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.price) : null\r\n            })\r\n\r\n            // 统计有效数据点\r\n            const validDataCount = priceData.filter(v => v !== null && v !== undefined).length\r\n            console.log(`    处理价格组[${priceGroup.priceName}]，有效数据点: ${validDataCount}/${priceData.length}`)\r\n\r\n            // 确保每条曲线都有唯一的名称和颜色，即使数据相同\r\n            const uniqueName = `${materialName}-${priceGroup.priceName}`\r\n            const lineColor = colors[colorIndex % colors.length]\r\n\r\n            // 检查是否已经有相同的数据，如果有则添加轻微偏移\r\n            const dataStr = JSON.stringify(priceData)\r\n            const existingSeries = series.find(s => JSON.stringify(s.data) === dataStr)\r\n            let adjustedData = priceData\r\n\r\n            if (existingSeries && priceData.some(v => v !== null)) {\r\n              // 为重复数据添加极小的偏移量（0.01），确保两条线都能显示\r\n              adjustedData = priceData.map(value => value !== null ? value + 0.01 : null)\r\n              console.log(`    检测到重复数据，为 ${uniqueName} 添加偏移`)\r\n            }\r\n\r\n            series.push({\r\n              name: uniqueName,\r\n              type: 'line',\r\n              data: adjustedData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 3,\r\n                color: lineColor,\r\n                // 如果是偏移的数据，使用虚线样式区分\r\n                type: adjustedData !== priceData ? 'dashed' : 'solid'\r\n              },\r\n              itemStyle: {\r\n                color: lineColor\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 6,\r\n              connectNulls: true,\r\n              // 添加z-index确保两条线都能显示\r\n              z: colorIndex + 1\r\n            })\r\n\r\n            legendData.push(uniqueName)\r\n            colorIndex++\r\n            console.log(`    ✓ 添加曲线: ${uniqueName}，颜色: ${lineColor}，有效数据: ${validDataCount}`)\r\n          })\r\n        }\r\n      })\r\n\r\n      console.log(`\\n=== 图表数据处理完成 ===`)\r\n      console.log(`总计添加 ${series.length} 条曲线:`)\r\n      series.forEach((s, i) => {\r\n        const validCount = s.data.filter(v => v !== null && v !== undefined).length\r\n        console.log(`  ${i + 1}. ${s.name} (有效数据: ${validCount})`)\r\n      })\r\n\r\n      // 计算Y轴范围\r\n      let priceMin, priceMax\r\n      const priceValues = series.flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n      if (priceValues.length > 0) {\r\n        priceMin = Math.min(...priceValues)\r\n        priceMax = Math.max(...priceValues)\r\n      }\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          formatter: function(params) {\r\n            let str = params[0].axisValueLabel + '<br/>'\r\n            params.forEach(item => {\r\n              if (item.value !== null && item.value !== undefined) {\r\n                str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`\r\n              } else {\r\n                str += `${item.marker}${item.seriesName}: -<br/>`\r\n              }\r\n            })\r\n            return str\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '12%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: function(index, value) {\r\n              if (index >= allDates.length || !allDates.length) return false\r\n\r\n              const uniqueMonths = new Set()\r\n              allDates.forEach(dateStr => {\r\n                const year = dateStr.substring(0, 4)\r\n                const month = dateStr.substring(4, 6)\r\n                uniqueMonths.add(`${year}${month}`)\r\n              })\r\n\r\n              const monthsCount = uniqueMonths.size\r\n              if (monthsCount <= 1) return true\r\n\r\n              const totalDataPoints = allDates.length\r\n              const idealInterval = Math.floor(totalDataPoints / Math.min(monthsCount, 8))\r\n\r\n              return index % Math.max(idealInterval, 1) === 0\r\n            },\r\n            formatter: function(value, index) {\r\n              if (index >= allDates.length) return ''\r\n              const originalDateStr = allDates[index]\r\n              if (!originalDateStr) return ''\r\n\r\n              const year = originalDateStr.substring(0, 4)\r\n              const month = parseInt(originalDateStr.substring(4, 6))\r\n              return `${year}.${month}`\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '价格（元/吨）',\r\n          min: priceMin,\r\n          max: priceMax,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee'\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: series\r\n      }\r\n\r\n      this.comparisonChartInstance.setOption(option, true)\r\n    },\r\n\r\n    // 检查两个曲线是否都已设置默认值，如果是则触发初始数据获取\r\n    checkAndTriggerInitialDataFetch() {\r\n      // 检查两个曲线是否都已经设置了默认的PB块\r\n      if (this.selectedPurchaseAmountMaterials.includes('PB块') &&\r\n        this.selectedMarketPriceMaterials.includes('PB块') &&\r\n        !this.hasInitializedPriceChart) {\r\n\r\n        this.hasInitializedPriceChart = true // 标记已经初始化过\r\n        console.log('两个曲线都已设置默认值，自动触发数据获取')\r\n\r\n        // 自动触发数据获取\r\n        this.$nextTick(() => {\r\n          this.fetchPriceAndStoreDataForNewChart()\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n  font-family: \"Microsoft YaHei\", sans-serif;\r\n}\r\n\r\n.dashboard-container {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  color: #fff;\r\n  overflow-x: hidden;\r\n  padding: 10px;\r\n}\r\n\r\n.dashboard-header {\r\n  text-align: center;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n  padding: 5px 0;\r\n}\r\n\r\n.dashboard-header h1 {\r\n  font-size: 24px;\r\n  position: relative;\r\n  display: inline-block;\r\n  padding: 5px 40px;\r\n}\r\n\r\n.dashboard-header::before,\r\n.dashboard-header::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 50%;\r\n  width: 30%;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.dashboard-header::before {\r\n  left: 0;\r\n}\r\n\r\n.dashboard-header::after {\r\n  right: 0;\r\n}\r\n\r\n.dashboard-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(12, 1fr);\r\n  grid-template-rows: auto auto auto auto auto auto;\r\n  gap: 10px;\r\n  min-height: calc(100vh - 80px);\r\n}\r\n\r\n.card {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 5px;\r\n  padding: 10px;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.card-title {\r\n  font-size: 14px;\r\n  margin-bottom: 5px;\r\n  font-weight: normal;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.chart-filter-dropdown-container {\r\n  z-index: 10;\r\n}\r\n\r\n.chart-filter-dropdown-container select {\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n  color: #fff;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  font-size: 12px;\r\n}\r\n\r\n.chart {\r\n  width: 100%;\r\n  height: calc(100% - 20px);\r\n  min-height: 200px;\r\n  flex: 1;\r\n}\r\n\r\n.stat-cards {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  height: 100%;\r\n  align-items: center;\r\n}\r\n\r\n.stat-card {\r\n  text-align: center;\r\n  flex-grow: 1;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 34px;\r\n  font-weight: bold;\r\n  color: #00ffff;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 18px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.chart-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: rgba(255,255,255,0.5);\r\n  font-size: 14px;\r\n}\r\n\r\n.material-chart-card {\r\n  height: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.material-chart-card .chart {\r\n  flex-grow: 1;\r\n  height: 250px;\r\n  min-height: 250px;\r\n}\r\n\r\n.time-filter {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.time-filter-btn {\r\n  padding: 6px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n}\r\n\r\n.time-filter-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.time-filter-btn.active {\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n  font-weight: 500;\r\n}\r\n\r\n.header-controls {\r\n  position: absolute;\r\n  right: 20px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  z-index: 1000;\r\n}\r\n\r\n.fullscreen-btn {\r\n  padding: 8px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 40px;\r\n  height: 32px;\r\n}\r\n\r\n.fullscreen-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n}\r\n\r\n/* AI价格预测区域样式 */\r\n.price-prediction-section {\r\n  margin-top: 15px;\r\n  margin-bottom: 15px;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-size: 13px;\r\n}\r\n\r\n.prediction-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.model-info {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 12px;\r\n}\r\n\r\n.prediction-content {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  border-left: 3px solid #00ffff;\r\n  position: relative;\r\n}\r\n\r\n.prediction-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n\r\n\r\n/* 多个预测结果的样式 */\r\n.predictions-container {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding-right: 5px;\r\n}\r\n\r\n.prediction-item {\r\n  margin-bottom: 15px;\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n  overflow: hidden;\r\n}\r\n\r\n.prediction-item.prediction-error {\r\n  border-left-color: #ff6b6b;\r\n}\r\n\r\n.prediction-material-title {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  padding: 8px 12px;\r\n  font-size: 13px;\r\n  font-weight: 600;\r\n  color: #00ffff;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-item.prediction-error .prediction-material-title {\r\n  background-color: rgba(255, 107, 107, 0.1);\r\n  color: #ff6b6b;\r\n  border-bottom-color: rgba(255, 107, 107, 0.2);\r\n}\r\n\r\n.prediction-material-title i {\r\n  margin-right: 6px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n/* 预测容器滚动条样式 */\r\n.predictions-container::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 一问一答样式 */\r\n.qa-section {\r\n  padding: 0;\r\n}\r\n\r\n.question-section, .answer-section {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.answer-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.qa-label {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  font-weight: 600;\r\n  font-size: 13px;\r\n}\r\n\r\n.question-label {\r\n  color: #ffb980;\r\n}\r\n\r\n.answer-label {\r\n  color: #00ffff;\r\n}\r\n\r\n.qa-label i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n.question-text, .answer-text {\r\n  background-color: rgba(0, 0, 0, 0.3);\r\n  border-radius: 8px;\r\n  padding: 12px 15px;\r\n  line-height: 1.6;\r\n  font-size: 13px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  white-space: pre-wrap;\r\n  word-wrap: break-word;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.question-text {\r\n  border-left: 3px solid #ffb980;\r\n}\r\n\r\n.answer-text {\r\n  border-left: 3px solid #00ffff;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding-right: 18px;\r\n}\r\n\r\n/* 问题文本样式 */\r\n.question-text {\r\n  font-style: italic;\r\n  color: rgba(255, 200, 150, 0.9);\r\n}\r\n\r\n/* 答案文本滚动条样式 */\r\n.answer-text::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 价格趋势卡片特殊样式 */\r\n.price-trend-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: auto;\r\n  min-height: 400px;\r\n}\r\n\r\n.price-trend-card .chart {\r\n  flex-shrink: 0;\r\n  height: 300px !important;\r\n  min-height: 300px;\r\n}\r\n\r\n.price-trend-card .price-prediction-section {\r\n  flex-shrink: 0;\r\n  margin-top: 15px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.inventory-total {\r\n  font-size: 12px;\r\n  color: #00ffff;\r\n  font-weight: normal;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 新增：价格趋势图控件样式 */\r\n.price-trend-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: rgba(16, 7, 33, 0.4);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.left-controls, .right-controls {\r\n  flex: 1;\r\n  max-width: 45%;\r\n}\r\n\r\n.curve-label {\r\n  font-size: 13px;\r\n  color: #00ffff;\r\n  margin-bottom: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n.left-controls .curve-label {\r\n  text-align: left;\r\n}\r\n\r\n.right-controls .curve-label {\r\n  text-align: right;\r\n}\r\n\r\n.dropdown-row {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.right-controls .dropdown-row {\r\n  justify-content: flex-end;\r\n}\r\n\r\n.fetch-data-btn-container {\r\n  text-align: right;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.modern-fetch-btn {\r\n  background: rgba(138, 43, 226, 0.7);\r\n  border: none;\r\n  border-radius: 12px;\r\n  padding: 10px 20px;\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 3px 10px rgba(138, 43, 226, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-fetch-btn:hover:not(:disabled) {\r\n  background: rgba(138, 43, 226, 0.9);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.5);\r\n}\r\n\r\n.modern-fetch-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.modern-fetch-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.modern-fetch-btn.loading {\r\n  background: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.modern-fetch-btn i {\r\n  margin-right: 8px;\r\n  animation: rotate 1s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n/* Element UI 下拉框样式覆盖 */\r\n.price-trend-controls .el-select {\r\n  background-color: transparent !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner {\r\n  background-color: #4a1c5a !important;\r\n  border: 1px solid rgba(116, 75, 162, 0.5) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 8px !important;\r\n  font-size: 13px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:hover {\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  box-shadow: 0 0 8px rgba(116, 75, 162, 0.3) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:focus {\r\n  border-color: #764ba2 !important;\r\n  box-shadow: 0 0 12px rgba(116, 75, 162, 0.5) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner::placeholder {\r\n  color: rgba(255, 255, 255, 0.7) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix i {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag {\r\n  background-color: rgba(116, 75, 162, 0.6) !important;\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 6px !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close:hover {\r\n  background-color: rgba(255, 255, 255, 0.2) !important;\r\n}\r\n\r\n/* 相似物料区域样式 */\r\n.similar-materials-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.similar-materials-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  font-size: 14px;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.similar-materials-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.section-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n.similar-materials-container {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 10px;\r\n  overflow-x: auto;\r\n}\r\n\r\n.materials-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  font-size: 13px;\r\n}\r\n\r\n.materials-table th {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  color: #00ffff;\r\n  padding: 8px 12px;\r\n  text-align: left;\r\n  border-bottom: 2px solid rgba(0, 212, 255, 0.3);\r\n  font-weight: 600;\r\n}\r\n\r\n.materials-table td {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.material-row {\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.material-row:hover {\r\n  background-color: rgba(0, 212, 255, 0.05);\r\n}\r\n\r\n.rank-cell {\r\n  text-align: center;\r\n  width: 60px;\r\n}\r\n\r\n.rank-badge {\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 50%;\r\n  color: #fff;\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n  min-width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.rank-first {\r\n  background: linear-gradient(135deg, #ffd700, #ffb347);\r\n  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.rank-second {\r\n  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);\r\n  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);\r\n}\r\n\r\n.rank-third {\r\n  background: linear-gradient(135deg, #cd7f32, #b8860b);\r\n  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);\r\n}\r\n\r\n.rank-default {\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.material-name, .compare-material-name {\r\n  font-weight: 500;\r\n  color: #fff;\r\n}\r\n\r\n.compare-material-name {\r\n  color: #00ffff;\r\n}\r\n\r\n.score-cell {\r\n  text-align: center;\r\n  width: 120px;\r\n  min-width: 120px;\r\n}\r\n\r\n.score-value {\r\n  display: inline-block;\r\n  padding: 2px 6px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 4px;\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n}\r\n\r\n.score-desc {\r\n  color: #ffb980;\r\n  font-style: italic;\r\n}\r\n\r\n.category-cell {\r\n  color: #5fd8b6;\r\n  font-weight: 500;\r\n}\r\n\r\n.similar-materials-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n.similar-materials-group {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.similar-materials-group:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.group-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n}\r\n\r\n.price-type-cell {\r\n  color: #e879ed;\r\n  font-size: 11px;\r\n  max-width: 120px;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.algorithm-desc {\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 11px;\r\n  font-style: italic;\r\n  margin-left: 8px;\r\n}\r\n\r\n.action-cell {\r\n  text-align: center;\r\n  width: 100px;\r\n}\r\n\r\n.view-comparison-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  color: #fff;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  white-space: nowrap;\r\n  min-width: 70px;\r\n}\r\n\r\n.view-comparison-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\r\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\r\n}\r\n\r\n.view-comparison-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.view-comparison-btn i {\r\n  font-size: 13px;\r\n}\r\n\r\n/* 对比弹框样式 */\r\n.comparison-dialog .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__header {\r\n  background: linear-gradient(135deg, rgba(33, 10, 56, 0.9), rgba(0, 212, 255, 0.2));\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__title {\r\n  color: #00ffff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close {\r\n  color: #00ffff;\r\n  font-size: 20px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close:hover {\r\n  color: #fff;\r\n  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n.comparison-dialog .el-dialog__body {\r\n  padding: 0;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-content {\r\n  padding: 20px;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.comparison-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 15px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.base-material {\r\n  color: #00ffff;\r\n  padding: 8px 16px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.vs-text {\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.compare-material {\r\n  color: #ea7ccc;\r\n  padding: 8px 16px;\r\n  background-color: rgba(234, 124, 204, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(234, 124, 204, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.similarity-info {\r\n  color: #ffb980;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  padding: 4px 12px;\r\n  background-color: rgba(255, 185, 128, 0.2);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 185, 128, 0.4);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-chart-container {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  overflow: hidden;\r\n  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.comparison-chart {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n</style>\r\n"]}]}