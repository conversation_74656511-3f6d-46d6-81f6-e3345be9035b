{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\list.vue", "mtime": 1756456282469}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_info", "require", "_user", "name", "data", "loading", "showSearch", "showBusiness", "total", "checkedTotal", "listToCheck", "listChecked", "title", "open", "queryParams", "pageNum", "pageSize", "workNo", "deptId", "assessDate", "checkedQueryParams", "form", "id", "deptAddScore", "businessAddScore", "deptScoreReason", "businessScoreReason", "rules", "deptOptions", "openCheck", "checkInfo", "spanList", "toCheckLabel", "submitting", "multipleSelection", "single", "multiple", "quickScoreDialogVisible", "batchQuickScoreForm", "score", "undefined", "ids", "batchQuickScoreRules", "required", "message", "trigger", "type", "batchQuickScoreOpen", "selectedRows", "computed", "canSubmitBatchScore", "length", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "n", "done", "row", "value", "quickAddScore", "err", "e", "f", "created", "getDefaultAssessDate", "getCheckDeptList", "getList", "getCheckedList", "methods", "now", "Date", "currentDay", "getDate", "targetDate", "getFullYear", "getMonth", "year", "month", "concat", "_this", "then", "res", "console", "log", "code", "for<PERSON>ach", "item", "push", "deptName", "_this2", "response", "rows", "shouldBusinessDisplay", "some", "_this3", "cancel", "reset", "handleQuery", "reset<PERSON><PERSON>y", "resetForm", "handleCheckDetail", "_this4", "getInfo", "list", "JSON", "parse", "content", "handleSpanList", "checkSubmit", "_this5", "verify", "point", "getDeptScoreFromForm", "status", "getBusinessScoreFromForm", "$confirm", "confirmButtonText", "cancelButtonText", "onCheck", "catch", "_this6", "deptScore", "businessScore", "check", "$message", "handleListChange", "flag", "i", "rowspan", "colspan", "objectSpanMethod", "_ref", "column", "rowIndex", "columnIndex", "category", "handleQuickSubmit", "_this7", "quickScore", "warning", "$set", "success", "finally", "handleSelectionChange", "selection", "map", "handleBatchQuickScore", "$modal", "msgError", "validationResult", "validateBatchQuickScore", "<PERSON><PERSON><PERSON><PERSON>", "cancelBatchQuickScore", "submitBatchQuickScore", "_this8", "submitData", "finalScore", "getDeptScore", "getBusinessScore", "parseFloat", "quickReason", "confirm", "batchQuickScore", "msgSuccess", "handleCheckedDetail", "_this9", "infoId", "error", "selfScore", "addScore", "result", "Math", "max", "min", "toFixed"], "sources": ["src/views/assess/self/check/list.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.assessDate\"\r\n              type=\"month\"\r\n              value-format=\"yyyy-M\"\r\n              format=\"yyyy 年 M 月\"\r\n              placeholder=\"选择考核年月\"\r\n              :clearable=\"false\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入姓名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <el-select v-model=\"queryParams.deptId\" placeholder=\"请选择部门\">\r\n              <el-option\r\n                v-for=\"item in deptOptions\"\r\n                :key=\"item.deptId\"\r\n                :label=\"item.deptName\"\r\n                :value=\"item.deptId\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      \r\n      <!-- 待评分列表 -->\r\n      <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n            <i class=\"el-icon-s-order\"></i>\r\n            {{ toCheckLabel }}\r\n          </span>\r\n        </div>\r\n        \r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              @click=\"handleBatchQuickScore\"\r\n            >批量快速评分</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n          <el-table v-loading=\"loading\" :data=\"listToCheck\" @selection-change=\"handleSelectionChange\">\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n            <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n            <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" ></el-table-column>\r\n            <el-table-column label=\"自评分\" align=\"center\" prop=\"selfScore\"></el-table-column>\r\n            <el-table-column label=\"部门领导评分\" align=\"center\" prop=\"deptScore\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.status == '1'\" style=\"font-weight: bold; color: #409EFF;\">{{ getDeptScore(scope.row) }}</span>\r\n                <span v-else-if=\"scope.row.deptScore\">{{ scope.row.deptScore }}</span>\r\n                <span v-else></span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column v-if=\"showBusiness\" label=\"事业部评分\" align=\"center\" prop=\"businessScore\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.status == '2'\" style=\"font-weight: bold; color: #409EFF;\">{{ getBusinessScore(scope.row) }}</span>\r\n                <span v-else-if=\"scope.row.businessScore\">{{ scope.row.businessScore }}</span>\r\n                <span v-else></span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"加减分\" align=\"center\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number \r\n                  v-if=\"scope.row.status == '1' || scope.row.status == '2'\"\r\n                  v-model=\"scope.row.quickAddScore\" \r\n                  :min=\"-100\" \r\n                  :max=\"100\" \r\n                  size=\"mini\"\r\n                  :precision=\"1\"\r\n                  style=\"width: 100px\"\r\n                  placeholder=\"加减分\">\r\n                </el-input-number>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"加减分原因\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input\r\n                  v-model=\"scope.row.quickReason\"\r\n                  type=\"textarea\"\r\n                  :autosize=\"{ minRows: 1, maxRows: 4}\"\r\n                  size=\"mini\"\r\n                  style=\"width: 150px\"\r\n                  placeholder=\"请输入加减分原因\">\r\n                </el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <!-- <el-table-column label=\"运改组织部评分\" align=\"center\" prop=\"organizationScore\">\r\n                <el-input-number \r\n                  v-if=\"scope.row.status == '3'\"\r\n                  v-model=\"scope.row.quickScore\" \r\n                  :min=\"0\" \r\n                  :max=\"100\" \r\n                  size=\"mini\"\r\n                  style=\"width: 120px\"\r\n                  placeholder=\"请输入分数\">\r\n                </el-input-number>\r\n                <span v-else-if=\"scope.row.businessScore\">{{ businessScore }}</span>\r\n                <span v-else></span>\r\n            </el-table-column> -->\r\n            <!-- <el-table-column label=\"快速评分\" align=\"center\" width=\"280\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number \r\n                  v-model=\"scope.row.quickScore\" \r\n                  :min=\"0\" \r\n                  :max=\"100\" \r\n                  size=\"mini\"\r\n                  style=\"width: 120px\"\r\n                  placeholder=\"请输入分数\">\r\n                </el-input-number>\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"handleQuickSubmit(scope.row)\"\r\n                  :loading=\"scope.row.submitting\"\r\n                  style=\"margin-left: 10px\">\r\n                  提交评分\r\n                </el-button>\r\n              </template>\r\n            </el-table-column> -->\r\n            <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handleCheckDetail(scope.row)\"\r\n                >详细评分</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 评分记录 -->\r\n      <el-card class=\"box-card\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n            <i class=\"el-icon-document\"></i>\r\n            评分记录({{ checkedTotal }})\r\n          </span>\r\n        </div>\r\n        \r\n        <el-table v-loading=\"loading\" :data=\"listChecked\">\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n          <el-table-column label=\"职务\" align=\"center\" prop=\"job\" width=\"150\"/>\r\n          <el-table-column label=\"评分类型\" align=\"center\" prop=\"type\" >\r\n            <template slot-scope=\"scope\">\r\n              <el-tag v-if=\"scope.row.type == '1'\" type=\"primary\" size=\"small\">部门领导评分</el-tag>\r\n              <el-tag v-if=\"scope.row.type == '2'\" type=\"warning\" size=\"small\">事业部领导评分</el-tag>\r\n              <el-tag v-if=\"scope.row.type == '3'\" type=\"success\" size=\"small\">运改组织部审核</el-tag>\r\n              <el-tag v-if=\"scope.row.type == '4'\" type=\"info\" size=\"small\">条线领导评分</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"评分时间\" align=\"center\" prop=\"createTime\" width=\"160\"/>\r\n          <el-table-column label=\"评分\" align=\"center\" prop=\"score\" width=\"100\"/>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handleCheckedDetail(scope.row)\"\r\n              >查看详情</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        \r\n        <pagination\r\n          v-show=\"checkedTotal>0\"\r\n          :total=\"checkedTotal\"\r\n          :page.sync=\"checkedQueryParams.pageNum\"\r\n          :limit.sync=\"checkedQueryParams.pageSize\"\r\n          @pagination=\"getCheckedList\"\r\n          style=\"margin-top: 20px;\"\r\n        />\r\n      </el-card>\r\n\r\n          <el-dialog\r\n      :visible.sync=\"open\"\r\n      fullscreen\r\n      class=\"assessment-detail-dialog\">\r\n      <div class=\"detail-container\">\r\n        <div class=\"detail-header\">\r\n          <h2 style=\"text-align: center; color: #303133; margin-bottom: 20px;\">\r\n            <i class=\"el-icon-document\"></i>\r\n            月度业绩考核表\r\n          </h2>\r\n          <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n            <el-descriptions class=\"margin-top\" :column=\"3\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-user\"></i> 姓名\r\n                </template>\r\n                {{ checkInfo.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-office-building\"></i> 部门\r\n                </template>\r\n                {{ checkInfo.deptName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-date\"></i> 考核年月\r\n                </template>\r\n                {{ checkInfo.assessDate }}\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </el-card>\r\n        </div>\r\n        \r\n        <el-card shadow=\"never\" class=\"assessment-table-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n              <i class=\"el-icon-s-data\"></i>\r\n              考核详情\r\n            </span>\r\n          </div>\r\n          <el-table v-loading=\"loading\" :data=\"checkInfo.list\"\r\n            :span-method=\"objectSpanMethod\" border stripe>\r\n            <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n            <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"150\"/>\r\n            <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"180\"/>\r\n            <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n            <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" />\r\n            <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"150\" />\r\n            <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\" width=\"180\" />\r\n          </el-table>\r\n        </el-card>\r\n        \r\n        <el-card shadow=\"never\" class=\"signature-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              评分记录\r\n            </span>\r\n          </div>\r\n          <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" label-position=\"left\">\r\n            <!-- 自评分 -->\r\n            <el-form-item>\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  自评分数 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.selfScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.name }}</span>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 部门领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.deptScore && checkInfo.deptUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  部门领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.deptScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.deptUserName }}</span>\r\n                <div v-if=\"checkInfo.deptScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.deptScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 事业部领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.businessUserName && checkInfo.businessScore\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  事业部领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.businessScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.businessUserName }}</span>\r\n                <div v-if=\"checkInfo.businessScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.businessScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 运改组织部评分 -->\r\n            <el-form-item v-if=\"checkInfo.organizationScore && checkInfo.organizationUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  运改组织部评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.organizationScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.organizationUserName }}</span>\r\n                <div v-if=\"checkInfo.organizationScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.organizationScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 当前状态评分输入 -->\r\n            <el-form-item v-if=\"checkInfo.status == '1'\" label=\"加减分：\">\r\n              <el-input-number v-model=\"form.deptAddScore\" :min=\"-100\" :max=\"100\" :precision=\"1\" placeholder=\"请输入加减分\" style=\"width: 150px;\" />\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '1'\" label=\"部门领导评分：\">\r\n              <span style=\"font-weight: bold; color: #409EFF; font-size: 16px;\">{{ getDeptScoreFromForm() }}分</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '1'\" label=\"加减分理由：\">\r\n              <el-input type=\"textarea\" :autosize=\"{ minRows: 2, maxRows: 4}\" v-model=\"form.deptScoreReason\" placeholder=\"请输入加减分理由\" style=\"width: 400px;\" />\r\n            </el-form-item>\r\n            \r\n            <el-form-item v-if=\"checkInfo.status == '2'\" label=\"加减分：\">\r\n              <el-input-number v-model=\"form.businessAddScore\" :min=\"-100\" :max=\"100\" :precision=\"1\" placeholder=\"请输入加减分\" style=\"width: 150px;\" />\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '2'\" label=\"事业部领导评分：\">\r\n              <span style=\"font-weight: bold; color: #409EFF; font-size: 16px;\">{{ getBusinessScoreFromForm() }}分</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '2'\" label=\"加减分理由：\">\r\n              <el-input type=\"textarea\" :autosize=\"{ minRows: 2, maxRows: 4}\" v-model=\"form.businessScoreReason\" placeholder=\"请输入加减分理由\" style=\"width: 400px;\" />\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n        \r\n        <div class=\"dialog-footer\" style=\"text-align: center; margin-top: 30px; padding: 20px;\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"checkSubmit\">\r\n            <i class=\"el-icon-check\"></i> 提 交\r\n          </el-button>\r\n          <el-button plain type=\"info\" size=\"medium\" @click=\"cancel\">\r\n            <i class=\"el-icon-close\"></i> 返 回\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n      <!-- 批量快速评分对话框 -->\r\n      <el-dialog :title=\"'批量快速评分确认'\" :visible.sync=\"batchQuickScoreOpen\" width=\"800px\" append-to-body>\r\n        <el-alert\r\n          title=\"请确认以下人员的评分信息\"\r\n          type=\"warning\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          class=\"mb20\"\r\n        />\r\n        <el-table :data=\"selectedRows\" size=\"small\" border>\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" />\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" />\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n          <el-table-column label=\"岗位\" align=\"center\" prop=\"job\" />\r\n          <el-table-column label=\"加减分\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.quickAddScore || 0 }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"最终评分\" align=\"center\" prop=\"quickScore\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.status == '1'\" style=\"font-weight: bold; color: #409EFF;\">{{ getDeptScore(scope.row) }}</span>\r\n              <span v-else-if=\"scope.row.status == '2'\" style=\"font-weight: bold; color: #409EFF;\">{{ getBusinessScore(scope.row) }}</span>\r\n              <span v-else :class=\"{'text-red': !scope.row.quickScore}\">{{ scope.row.quickScore || '未填写' }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"加减分理由\" align=\"center\" prop=\"quickReason\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.quickReason || '' }}</span>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitBatchQuickScore\" :disabled=\"!canSubmitBatchScore\">确 定</el-button>\r\n          <el-button @click=\"cancelBatchQuickScore\">取 消</el-button>\r\n          </div>\r\n      </el-dialog>\r\n\r\n    </div>\r\n  </template>\r\n\r\n  <script>\r\n  import { listToCheck, listChecked, getInfo, check, batchQuickScore } from \"@/api/assess/self/info\"\r\n  import { getCheckDeptList } from \"@/api/assess/self/user\";\r\n\r\n  export default {\r\n    name: \"SelfAssessCheck\",\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 显示列表事业部评分\r\n        showBusiness:false,\r\n        // 总条数\r\n        total: 0,\r\n        checkedTotal: 0,\r\n        // 绩效考核-干部自评人员配置表格数据\r\n        listToCheck: [],\r\n        listChecked: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          workNo: null,\r\n          name:null,\r\n          deptId:null,\r\n          assessDate:null\r\n        },\r\n        // 评分记录查询参数\r\n        checkedQueryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          workNo: null,\r\n          name:null,\r\n          deptId:null,\r\n          assessDate:null\r\n        },\r\n        // 表单参数\r\n        form: {\r\n          id:null,\r\n          // 部门领导加减分\r\n          deptAddScore:null,\r\n          // 事业部加减分\r\n          businessAddScore:null,\r\n          // 部门领导评分理由\r\n          deptScoreReason:null,\r\n          // 事业部评分理由\r\n          businessScoreReason:null,\r\n        },\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        deptOptions:[],\r\n        openCheck:false,\r\n        checkInfo:{},\r\n        // 合并单元格\r\n        spanList:[],\r\n        // 待评分标签\r\n        toCheckLabel:\"待评分(0)\",\r\n        // 快速评分提交状态\r\n        submitting: false,\r\n        // 选中数组\r\n        multipleSelection: [],\r\n        // 非单个禁用\r\n        single: true,\r\n        // 非多个禁用\r\n        multiple: true,\r\n        // 批量快速评分对话框显示状态\r\n        quickScoreDialogVisible: false,\r\n        // 批量快速评分表单参数\r\n        batchQuickScoreForm: {\r\n          score: undefined,\r\n          ids: []\r\n        },\r\n        // 批量快速评分表单验证规则\r\n        batchQuickScoreRules: {\r\n          score: [\r\n            { required: true, message: \"评分不能为空\", trigger: \"blur\" },\r\n            { type: 'number', message: \"评分必须为数字\", trigger: \"blur\" }\r\n          ]\r\n        },\r\n        // 批量快速评分对话框\r\n        batchQuickScoreOpen: false,\r\n        // 选中数组\r\n        ids: [],\r\n        // 选中的行数据\r\n        selectedRows: [],\r\n      };\r\n    },\r\n      computed: {\r\n    // 是否可以提交批量评分（基础检查）\r\n    canSubmitBatchScore() {\r\n      if (this.selectedRows.length === 0) return false;\r\n      \r\n      // 基础检查：是否所有行都填写了加减分\r\n      for (let row of this.selectedRows) {\r\n        if (row.quickAddScore === null || row.quickAddScore === undefined) {\r\n          return false;\r\n        }\r\n      }\r\n      \r\n      return true;\r\n    }\r\n  },\r\n    created() {\r\n      this.queryParams.assessDate = this.getDefaultAssessDate()\r\n      this.checkedQueryParams.assessDate = this.getDefaultAssessDate()\r\n      // this.getSelfAssessUser();\r\n      this.getCheckDeptList();\r\n      this.getList();\r\n      this.getCheckedList();\r\n    },\r\n    methods: {\r\n\r\n      // 获取默认考核日期\r\n      getDefaultAssessDate() {\r\n        const now = new Date();\r\n        const currentDay = now.getDate();\r\n\r\n        let targetDate;\r\n        if (currentDay < 10) {\r\n          // 当前日期小于10日，默认为上个月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n        } else {\r\n          // 当前日期大于等于10日，默认为当月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n        }\r\n\r\n        // 格式化为 YYYY-M 格式\r\n        const year = targetDate.getFullYear();\r\n        const month = targetDate.getMonth() + 1;\r\n        return `${year}-${month}`;\r\n      },\r\n\r\n      // 获取部门信息\r\n      getCheckDeptList(){\r\n        getCheckDeptList().then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            let deptOptions = [];\r\n            res.data.forEach(item => {\r\n              deptOptions.push({\r\n                deptName:item.deptName,\r\n                deptId:item.deptId\r\n              })\r\n            })\r\n            this.deptOptions = deptOptions;\r\n          }\r\n        })\r\n      },\r\n      /** 查询绩效考核-干部自评待审核列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listToCheck(this.queryParams).then(response => {\r\n          this.listToCheck = response.rows;\r\n          this.total = response.total;\r\n          this.toCheckLabel = `待评分(${response.total})`\r\n          this.loading = false;\r\n          this.shouldBusinessDisplay();\r\n        });\r\n      },\r\n\r\n      shouldBusinessDisplay(){\r\n        this.showBusiness = this.listToCheck.some(row => row[\"status\"] == '2')\r\n      },\r\n      \r\n      /** 获取已审核列表 */\r\n      getCheckedList(){\r\n        this.loading = true;\r\n        listChecked(this.checkedQueryParams).then(res => {\r\n          this.listChecked = res.rows;\r\n          this.checkedTotal = res.total;\r\n          this.loading = false;\r\n        })\r\n      },\r\n\r\n      // 取消按钮\r\n      cancel() {\r\n        this.open = false;\r\n        this.reset();\r\n      },\r\n      // 表单重置\r\n      reset() {\r\n        this.form = {\r\n          id: null,\r\n          deptAddScore: null,\r\n          businessAddScore: null,\r\n          deptScoreReason: null,\r\n          businessScoreReason: null,\r\n        };\r\n        // this.resetForm(\"form\");\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.checkedQueryParams.pageNum = 1;\r\n        // 同步搜索条件\r\n        this.checkedQueryParams.name = this.queryParams.name;\r\n        this.checkedQueryParams.deptId = this.queryParams.deptId;\r\n        this.checkedQueryParams.assessDate = this.queryParams.assessDate;\r\n        this.getCheckedList();\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n\r\n      // 审批详情\r\n      handleCheckDetail(row){\r\n        getInfo({id:row.id}).then(res => {\r\n          console.log(res);\r\n          if(res.code == 200){\r\n            this.checkInfo = res.data;\r\n            let list = JSON.parse(res.data.content);\r\n            this.handleSpanList(list);\r\n            this.checkInfo.list = list;\r\n          }\r\n          this.open = true\r\n        })\r\n      },\r\n\r\n      // 审批提交\r\n      checkSubmit(){\r\n        if(this.verify()){\r\n          let point = this.getDeptScoreFromForm();\r\n          if(this.checkInfo.status == '2') point = this.getBusinessScoreFromForm();\r\n          this.$confirm('是否确认' + this.checkInfo.name + '评分为：' + point + '分', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.onCheck();\r\n          }).catch(() => {\r\n\r\n          });\r\n        }\r\n      },\r\n\r\n      onCheck(){\r\n        this.form.id = this.checkInfo.id;\r\n        this.form.status = this.checkInfo.status;\r\n        \r\n        // 计算最终评分\r\n        if(this.checkInfo.status == '1') {\r\n          this.form.deptScore = this.getDeptScoreFromForm();\r\n        }\r\n        if(this.checkInfo.status == '2') {\r\n          this.form.businessScore = this.getBusinessScoreFromForm();\r\n        }\r\n        \r\n        check(this.form).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            });\r\n            this.reset();\r\n            this.open = false;\r\n            this.getList();\r\n            this.getCheckedList();\r\n          }else{\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '操作失败，无权限或当前审批状态不匹配'\r\n            });\r\n          }\r\n        })\r\n      },\r\n\r\n      // 数据验证\r\n      verify(){\r\n        if(this.checkInfo.status == '1' && this.form.deptAddScore === null){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请填写加减分'\r\n          });\r\n          return false;\r\n        }\r\n        if(this.checkInfo.status == '2' && this.form.businessAddScore === null){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请填写加减分'\r\n          });\r\n          return false;\r\n        }\r\n        if(this.checkInfo.status == '1' && this.form.deptAddScore !== 0 && !this.form.deptScoreReason){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '有加减分时请填写加减分理由'\r\n          });\r\n          return false;\r\n        } \r\n        if(this.checkInfo.status == '2' && this.form.businessAddScore !== 0 && !this.form.businessScoreReason){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '有加减分时请填写加减分理由'\r\n          });\r\n          return false;\r\n        }\r\n        return true;\r\n      },\r\n\r\n      handleListChange(type){\r\n        console.log(type)\r\n      },\r\n      // 处理列表\r\n      handleSpanList(data){\r\n        let spanList = [];\r\n        let flag = 0;\r\n        for(let i = 0; i < data.length; i++){\r\n          // 相同考核项合并\r\n          if(i == 0){\r\n            spanList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n          }else{\r\n            if(data[i - 1].item == data[i].item){\r\n              spanList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              spanList[flag].rowspan += 1;\r\n            }else{\r\n              spanList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              flag = i;\r\n            }\r\n          }\r\n        }\r\n        this.spanList = spanList;\r\n      },\r\n\r\n      // 合并单元格方法\r\n      objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n        // 第一列相同项合并\r\n        if (columnIndex === 0) {\r\n          return this.spanList[rowIndex];\r\n        }\r\n        // 类别无内容 合并\r\n        if(columnIndex === 1){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 0,\r\n              colspan: 0\r\n            }\r\n          }\r\n        }\r\n        if(columnIndex === 2){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 1,\r\n              colspan: 2\r\n            }\r\n          }\r\n        }\r\n      },\r\n\r\n      /** 快速评分提交 */\r\n      handleQuickSubmit(row) {\r\n        if (!row.quickScore) {\r\n          this.$message.warning('请输入评分');\r\n          return;\r\n        }\r\n        this.$confirm('确认提交该评分吗？', \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          this.$set(row, 'submitting', true);\r\n          const data = {\r\n            id: row.id,\r\n            score: row.quickScore,\r\n            type: row.type\r\n          };\r\n          check(data).then(response => {\r\n            this.$message.success('评分提交成功');\r\n            this.getList();\r\n            this.getCheckedList();\r\n          }).finally(() => {\r\n            this.$set(row, 'submitting', false);\r\n          });\r\n        });\r\n      },\r\n\r\n      /** 选择条数改变 */\r\n      handleSelectionChange(selection) {\r\n        this.ids = selection.map(item => item.id)\r\n        this.selectedRows = selection\r\n        this.single = selection.length !== 1\r\n        this.multiple = !selection.length\r\n      },\r\n\r\n      /** 批量快速评分按钮操作 */\r\n      handleBatchQuickScore() {\r\n        if (this.ids.length === 0) {\r\n          this.$modal.msgError(\"请选择需要评分的数据\");\r\n          return;\r\n        }\r\n        \r\n        // 验证评分一致性和理由必填\r\n        const validationResult = this.validateBatchQuickScore();\r\n        if (!validationResult.isValid) {\r\n          this.$modal.msgError(validationResult.message);\r\n          return;\r\n        }\r\n\r\n        this.batchQuickScoreOpen = true;\r\n      },\r\n\r\n      /** 取消批量快速评分操作 */\r\n      cancelBatchQuickScore() {\r\n        this.batchQuickScoreOpen = false;\r\n      },\r\n\r\n      /** 提交批量快速评分 */\r\n      submitBatchQuickScore() {\r\n        // 准备提交数据\r\n        const submitData = this.selectedRows.map(row => {\r\n          let finalScore;\r\n          if (row.status == '1') {\r\n            // 部门领导评分 = 自评分 + 加减分\r\n            finalScore = this.getDeptScore(row);\r\n          } else if (row.status == '2') {\r\n            // 事业部评分 = 部门领导评分 + 加减分\r\n            finalScore = this.getBusinessScore(row);\r\n          }\r\n          \r\n          return {\r\n            id: row.id,\r\n            quickScore: parseFloat(finalScore),\r\n            quickAddScore: row.quickAddScore,\r\n            quickReason: row.quickReason\r\n          };\r\n        });\r\n\r\n        this.$modal.confirm('是否确认提交选中人员的快速评分？').then(() => {\r\n          return batchQuickScore(submitData);\r\n        }).then(() => {\r\n          this.$modal.msgSuccess(\"批量评分成功\");\r\n          this.batchQuickScoreOpen = false;\r\n          this.getList();\r\n          this.getCheckedList();\r\n        }).catch(() => {});\r\n      },\r\n\r\n      /** 验证批量快速评分 */\r\n      validateBatchQuickScore() {\r\n        for (let i = 0; i < this.selectedRows.length; i++) {\r\n          const row = this.selectedRows[i];\r\n          \r\n          // 检查是否填写了加减分（允许为0）\r\n          if (row.quickAddScore === null || row.quickAddScore === undefined) {\r\n            return {\r\n              isValid: false,\r\n              message: `第${i + 1}行 ${row.name} 请填写加减分`\r\n            };\r\n          }\r\n\r\n          // 检查加减分不为0时是否填写了理由\r\n          if (parseFloat(row.quickAddScore) !== 0 && !row.quickReason) {\r\n            return {\r\n              isValid: false,\r\n              message: `第${i + 1}行 ${row.name} 有加减分时请填写加减分理由`\r\n            };\r\n          }\r\n        }\r\n\r\n        return { isValid: true };\r\n      },\r\n\r\n      /** 查看评分记录详情 */\r\n      handleCheckedDetail(row) {\r\n        getInfo({id: row.infoId}).then(res => {\r\n          console.log(res);\r\n          if(res.code == 200){\r\n            this.checkInfo = res.data;\r\n            let list = JSON.parse(res.data.content);\r\n            this.handleSpanList(list);\r\n            this.checkInfo.list = list;\r\n            this.open = true;\r\n          }\r\n        }).catch(error => {\r\n          this.$message.error('获取详情失败');\r\n        });\r\n      },\r\n\r\n      // 计算部门领导评分（快速评分表格用）\r\n      getDeptScore(row) {\r\n        const selfScore = parseFloat(row.selfScore) || 0;\r\n        const addScore = parseFloat(row.quickAddScore) || 0;\r\n        const result = selfScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n\r\n      // 计算事业部评分（快速评分表格用）\r\n      getBusinessScore(row) {\r\n        const deptScore = parseFloat(row.deptScore) || 0;\r\n        const addScore = parseFloat(row.quickAddScore) || 0;\r\n        const result = deptScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n\r\n      // 计算部门领导评分（详细评分表单用）\r\n      getDeptScoreFromForm() {\r\n        const selfScore = parseFloat(this.checkInfo.selfScore) || 0;\r\n        const addScore = parseFloat(this.form.deptAddScore) || 0;\r\n        const result = selfScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n\r\n      // 计算事业部评分（详细评分表单用）\r\n      getBusinessScoreFromForm() {\r\n        const deptScore = parseFloat(this.checkInfo.deptScore) || 0;\r\n        const addScore = parseFloat(this.form.businessAddScore) || 0;\r\n        const result = deptScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n    }\r\n  };\r\n  </script>\r\n\r\n  <style scoped>\r\n  .assessment-detail-dialog .detail-container {\r\n    padding: 20px;\r\n    background-color: #f5f7fa;\r\n    min-height: 100vh;\r\n  }\r\n\r\n  .assessment-detail-dialog .detail-header h2 {\r\n    background: linear-gradient(135deg, #409EFF, #67C23A);\r\n    background-clip: text;\r\n    -webkit-background-clip: text;\r\n    color: transparent;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .assessment-detail-dialog .assessment-table-card {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .assessment-detail-dialog .signature-card {\r\n    background: #ffffff;\r\n  }\r\n\r\n  .signature-content {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .score-text {\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .separator {\r\n    color: #909399;\r\n    margin: 0 4px;\r\n  }\r\n\r\n  .signature-name {\r\n    color: #303133;\r\n  }\r\n\r\n  .reason-text {\r\n    width: 100%;\r\n    margin-top: 8px;\r\n    padding: 8px 12px;\r\n    background-color: #f8f9fa;\r\n    border-left: 3px solid #409EFF;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .reason-label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n    margin-right: 8px;\r\n  }\r\n\r\n  .reason-content {\r\n    color: #303133;\r\n    line-height: 1.6;\r\n  }\r\n\r\n  .dialog-footer {\r\n    border-top: 1px solid #e4e7ed;\r\n    background-color: #ffffff;\r\n    border-radius: 0 0 6px 6px;\r\n  }\r\n\r\n  .assessment-detail-dialog .el-card {\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .assessment-detail-dialog .el-descriptions {\r\n    background-color: #ffffff;\r\n  }\r\n\r\n  .assessment-detail-dialog .el-table {\r\n    border-radius: 6px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .text-red {\r\n    color: #F56C6C;\r\n  }\r\n  </style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA2ZA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;MACAC,YAAA;MACA;MACAC,WAAA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAd,IAAA;QACAe,MAAA;QACAC,UAAA;MACA;MACA;MACAC,kBAAA;QACAL,OAAA;QACAC,QAAA;QACAC,MAAA;QACAd,IAAA;QACAe,MAAA;QACAC,UAAA;MACA;MACA;MACAE,IAAA;QACAC,EAAA;QACA;QACAC,YAAA;QACA;QACAC,gBAAA;QACA;QACAC,eAAA;QACA;QACAC,mBAAA;MACA;MACA;MACAC,KAAA,GACA;MACAC,WAAA;MACAC,SAAA;MACAC,SAAA;MACA;MACAC,QAAA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACA;MACAC,iBAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,uBAAA;MACA;MACAC,mBAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,GAAA;MACA;MACA;MACAC,oBAAA;QACAH,KAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,IAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAE,mBAAA;MACA;MACAN,GAAA;MACA;MACAO,YAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,SAAAF,YAAA,CAAAG,MAAA;;MAEA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACA,KAAAN,YAAA;QAAAO,KAAA;MAAA;QAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAK,CAAA,IAAAC,IAAA;UAAA,IAAAC,GAAA,GAAAJ,KAAA,CAAAK,KAAA;UACA,IAAAD,GAAA,CAAAE,aAAA,aAAAF,GAAA,CAAAE,aAAA,KAAArB,SAAA;YACA;UACA;QACA;MAAA,SAAAsB,GAAA;QAAAV,SAAA,CAAAW,CAAA,CAAAD,GAAA;MAAA;QAAAV,SAAA,CAAAY,CAAA;MAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAnD,WAAA,CAAAK,UAAA,QAAA+C,oBAAA;IACA,KAAA9C,kBAAA,CAAAD,UAAA,QAAA+C,oBAAA;IACA;IACA,KAAAC,gBAAA;IACA,KAAAC,OAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IAEA;IACAJ,oBAAA,WAAAA,qBAAA;MACA,IAAAK,GAAA,OAAAC,IAAA;MACA,IAAAC,UAAA,GAAAF,GAAA,CAAAG,OAAA;MAEA,IAAAC,UAAA;MACA,IAAAF,UAAA;QACA;QACAE,UAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAM,QAAA;MACA;QACA;QACAF,UAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAM,QAAA;MACA;;MAEA;MACA,IAAAC,IAAA,GAAAH,UAAA,CAAAC,WAAA;MACA,IAAAG,KAAA,GAAAJ,UAAA,CAAAE,QAAA;MACA,UAAAG,MAAA,CAAAF,IAAA,OAAAE,MAAA,CAAAD,KAAA;IACA;IAEA;IACAZ,gBAAA,WAAAA,iBAAA;MAAA,IAAAc,KAAA;MACA,IAAAd,sBAAA,IAAAe,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACA,IAAA1D,WAAA;UACAuD,GAAA,CAAA/E,IAAA,CAAAmF,OAAA,WAAAC,IAAA;YACA5D,WAAA,CAAA6D,IAAA;cACAC,QAAA,EAAAF,IAAA,CAAAE,QAAA;cACAxE,MAAA,EAAAsE,IAAA,CAAAtE;YACA;UACA;UACA+D,KAAA,CAAArD,WAAA,GAAAA,WAAA;QACA;MACA;IACA;IACA,uBACAwC,OAAA,WAAAA,QAAA;MAAA,IAAAuB,MAAA;MACA,KAAAtF,OAAA;MACA,IAAAK,iBAAA,OAAAI,WAAA,EAAAoE,IAAA,WAAAU,QAAA;QACAD,MAAA,CAAAjF,WAAA,GAAAkF,QAAA,CAAAC,IAAA;QACAF,MAAA,CAAAnF,KAAA,GAAAoF,QAAA,CAAApF,KAAA;QACAmF,MAAA,CAAA3D,YAAA,yBAAAgD,MAAA,CAAAY,QAAA,CAAApF,KAAA;QACAmF,MAAA,CAAAtF,OAAA;QACAsF,MAAA,CAAAG,qBAAA;MACA;IACA;IAEAA,qBAAA,WAAAA,sBAAA;MACA,KAAAvF,YAAA,QAAAG,WAAA,CAAAqF,IAAA,WAAApC,GAAA;QAAA,OAAAA,GAAA;MAAA;IACA;IAEA,cACAU,cAAA,WAAAA,eAAA;MAAA,IAAA2B,MAAA;MACA,KAAA3F,OAAA;MACA,IAAAM,iBAAA,OAAAS,kBAAA,EAAA8D,IAAA,WAAAC,GAAA;QACAa,MAAA,CAAArF,WAAA,GAAAwE,GAAA,CAAAU,IAAA;QACAG,MAAA,CAAAvF,YAAA,GAAA0E,GAAA,CAAA3E,KAAA;QACAwF,MAAA,CAAA3F,OAAA;MACA;IACA;IAEA;IACA4F,MAAA,WAAAA,OAAA;MACA,KAAApF,IAAA;MACA,KAAAqF,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA7E,IAAA;QACAC,EAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,mBAAA;MACA;MACA;IACA;IACA,aACAyE,WAAA,WAAAA,YAAA;MACA,KAAArF,WAAA,CAAAC,OAAA;MACA,KAAAK,kBAAA,CAAAL,OAAA;MACA;MACA,KAAAK,kBAAA,CAAAjB,IAAA,QAAAW,WAAA,CAAAX,IAAA;MACA,KAAAiB,kBAAA,CAAAF,MAAA,QAAAJ,WAAA,CAAAI,MAAA;MACA,KAAAE,kBAAA,CAAAD,UAAA,QAAAL,WAAA,CAAAK,UAAA;MACA,KAAAkD,cAAA;MACA,KAAAD,OAAA;IACA;IACA,aACAgC,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAF,WAAA;IACA;IAEA;IACAG,iBAAA,WAAAA,kBAAA3C,GAAA;MAAA,IAAA4C,MAAA;MACA,IAAAC,aAAA;QAAAlF,EAAA,EAAAqC,GAAA,CAAArC;MAAA,GAAA4D,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAiB,MAAA,CAAAzE,SAAA,GAAAqD,GAAA,CAAA/E,IAAA;UACA,IAAAqG,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAxB,GAAA,CAAA/E,IAAA,CAAAwG,OAAA;UACAL,MAAA,CAAAM,cAAA,CAAAJ,IAAA;UACAF,MAAA,CAAAzE,SAAA,CAAA2E,IAAA,GAAAA,IAAA;QACA;QACAF,MAAA,CAAA1F,IAAA;MACA;IACA;IAEA;IACAiG,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAAC,MAAA;QACA,IAAAC,KAAA,QAAAC,oBAAA;QACA,SAAApF,SAAA,CAAAqF,MAAA,SAAAF,KAAA,QAAAG,wBAAA;QACA,KAAAC,QAAA,eAAAvF,SAAA,CAAA3B,IAAA,YAAA8G,KAAA;UACAK,iBAAA;UACAC,gBAAA;UACAzE,IAAA;QACA,GAAAoC,IAAA;UACA6B,MAAA,CAAAS,OAAA;QACA,GAAAC,KAAA,cAEA;MACA;IACA;IAEAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,KAAArG,IAAA,CAAAC,EAAA,QAAAQ,SAAA,CAAAR,EAAA;MACA,KAAAD,IAAA,CAAA8F,MAAA,QAAArF,SAAA,CAAAqF,MAAA;;MAEA;MACA,SAAArF,SAAA,CAAAqF,MAAA;QACA,KAAA9F,IAAA,CAAAsG,SAAA,QAAAT,oBAAA;MACA;MACA,SAAApF,SAAA,CAAAqF,MAAA;QACA,KAAA9F,IAAA,CAAAuG,aAAA,QAAAR,wBAAA;MACA;MAEA,IAAAS,WAAA,OAAAxG,IAAA,EAAA6D,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAoC,MAAA,CAAAI,QAAA;YACAhF,IAAA;YACAF,OAAA;UACA;UACA8E,MAAA,CAAAxB,KAAA;UACAwB,MAAA,CAAA7G,IAAA;UACA6G,MAAA,CAAAtD,OAAA;UACAsD,MAAA,CAAArD,cAAA;QACA;UACAqD,MAAA,CAAAI,QAAA;YACAhF,IAAA;YACAF,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAoE,MAAA,WAAAA,OAAA;MACA,SAAAlF,SAAA,CAAAqF,MAAA,gBAAA9F,IAAA,CAAAE,YAAA;QACA,KAAAuG,QAAA;UACAhF,IAAA;UACAF,OAAA;QACA;QACA;MACA;MACA,SAAAd,SAAA,CAAAqF,MAAA,gBAAA9F,IAAA,CAAAG,gBAAA;QACA,KAAAsG,QAAA;UACAhF,IAAA;UACAF,OAAA;QACA;QACA;MACA;MACA,SAAAd,SAAA,CAAAqF,MAAA,gBAAA9F,IAAA,CAAAE,YAAA,gBAAAF,IAAA,CAAAI,eAAA;QACA,KAAAqG,QAAA;UACAhF,IAAA;UACAF,OAAA;QACA;QACA;MACA;MACA,SAAAd,SAAA,CAAAqF,MAAA,gBAAA9F,IAAA,CAAAG,gBAAA,gBAAAH,IAAA,CAAAK,mBAAA;QACA,KAAAoG,QAAA;UACAhF,IAAA;UACAF,OAAA;QACA;QACA;MACA;MACA;IACA;IAEAmF,gBAAA,WAAAA,iBAAAjF,IAAA;MACAsC,OAAA,CAAAC,GAAA,CAAAvC,IAAA;IACA;IACA;IACA+D,cAAA,WAAAA,eAAAzG,IAAA;MACA,IAAA2B,QAAA;MACA,IAAAiG,IAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAA7H,IAAA,CAAA+C,MAAA,EAAA8E,CAAA;QACA;QACA,IAAAA,CAAA;UACAlG,QAAA,CAAA0D,IAAA;YACAyC,OAAA;YACAC,OAAA;UACA;QACA;UACA,IAAA/H,IAAA,CAAA6H,CAAA,MAAAzC,IAAA,IAAApF,IAAA,CAAA6H,CAAA,EAAAzC,IAAA;YACAzD,QAAA,CAAA0D,IAAA;cACAyC,OAAA;cACAC,OAAA;YACA;YACApG,QAAA,CAAAiG,IAAA,EAAAE,OAAA;UACA;YACAnG,QAAA,CAAA0D,IAAA;cACAyC,OAAA;cACAC,OAAA;YACA;YACAH,IAAA,GAAAC,CAAA;UACA;QACA;MACA;MACA,KAAAlG,QAAA,GAAAA,QAAA;IACA;IAEA;IACAqG,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAA1E,GAAA,GAAA0E,IAAA,CAAA1E,GAAA;QAAA2E,MAAA,GAAAD,IAAA,CAAAC,MAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;QAAAC,WAAA,GAAAH,IAAA,CAAAG,WAAA;MACA;MACA,IAAAA,WAAA;QACA,YAAAzG,QAAA,CAAAwG,QAAA;MACA;MACA;MACA,IAAAC,WAAA;QACA,KAAA7E,GAAA,CAAA8E,QAAA;UACA;YACAP,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA,IAAAK,WAAA;QACA,KAAA7E,GAAA,CAAA8E,QAAA;UACA;YACAP,OAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;IAEA,aACAO,iBAAA,WAAAA,kBAAA/E,GAAA;MAAA,IAAAgF,MAAA;MACA,KAAAhF,GAAA,CAAAiF,UAAA;QACA,KAAAd,QAAA,CAAAe,OAAA;QACA;MACA;MACA,KAAAxB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAzE,IAAA;MACA,GAAAoC,IAAA;QACAyD,MAAA,CAAAG,IAAA,CAAAnF,GAAA;QACA,IAAAvD,IAAA;UACAkB,EAAA,EAAAqC,GAAA,CAAArC,EAAA;UACAiB,KAAA,EAAAoB,GAAA,CAAAiF,UAAA;UACA9F,IAAA,EAAAa,GAAA,CAAAb;QACA;QACA,IAAA+E,WAAA,EAAAzH,IAAA,EAAA8E,IAAA,WAAAU,QAAA;UACA+C,MAAA,CAAAb,QAAA,CAAAiB,OAAA;UACAJ,MAAA,CAAAvE,OAAA;UACAuE,MAAA,CAAAtE,cAAA;QACA,GAAA2E,OAAA;UACAL,MAAA,CAAAG,IAAA,CAAAnF,GAAA;QACA;MACA;IACA;IAEA,aACAsF,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzG,GAAA,GAAAyG,SAAA,CAAAC,GAAA,WAAA3D,IAAA;QAAA,OAAAA,IAAA,CAAAlE,EAAA;MAAA;MACA,KAAA0B,YAAA,GAAAkG,SAAA;MACA,KAAA/G,MAAA,GAAA+G,SAAA,CAAA/F,MAAA;MACA,KAAAf,QAAA,IAAA8G,SAAA,CAAA/F,MAAA;IACA;IAEA,iBACAiG,qBAAA,WAAAA,sBAAA;MACA,SAAA3G,GAAA,CAAAU,MAAA;QACA,KAAAkG,MAAA,CAAAC,QAAA;QACA;MACA;;MAEA;MACA,IAAAC,gBAAA,QAAAC,uBAAA;MACA,KAAAD,gBAAA,CAAAE,OAAA;QACA,KAAAJ,MAAA,CAAAC,QAAA,CAAAC,gBAAA,CAAA3G,OAAA;QACA;MACA;MAEA,KAAAG,mBAAA;IACA;IAEA,iBACA2G,qBAAA,WAAAA,sBAAA;MACA,KAAA3G,mBAAA;IACA;IAEA,eACA4G,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,UAAA,QAAA7G,YAAA,CAAAmG,GAAA,WAAAxF,GAAA;QACA,IAAAmG,UAAA;QACA,IAAAnG,GAAA,CAAAwD,MAAA;UACA;UACA2C,UAAA,GAAAF,MAAA,CAAAG,YAAA,CAAApG,GAAA;QACA,WAAAA,GAAA,CAAAwD,MAAA;UACA;UACA2C,UAAA,GAAAF,MAAA,CAAAI,gBAAA,CAAArG,GAAA;QACA;QAEA;UACArC,EAAA,EAAAqC,GAAA,CAAArC,EAAA;UACAsH,UAAA,EAAAqB,UAAA,CAAAH,UAAA;UACAjG,aAAA,EAAAF,GAAA,CAAAE,aAAA;UACAqG,WAAA,EAAAvG,GAAA,CAAAuG;QACA;MACA;MAEA,KAAAb,MAAA,CAAAc,OAAA,qBAAAjF,IAAA;QACA,WAAAkF,qBAAA,EAAAP,UAAA;MACA,GAAA3E,IAAA;QACA0E,MAAA,CAAAP,MAAA,CAAAgB,UAAA;QACAT,MAAA,CAAA7G,mBAAA;QACA6G,MAAA,CAAAxF,OAAA;QACAwF,MAAA,CAAAvF,cAAA;MACA,GAAAoD,KAAA;IACA;IAEA,eACA+B,uBAAA,WAAAA,wBAAA;MACA,SAAAvB,CAAA,MAAAA,CAAA,QAAAjF,YAAA,CAAAG,MAAA,EAAA8E,CAAA;QACA,IAAAtE,GAAA,QAAAX,YAAA,CAAAiF,CAAA;;QAEA;QACA,IAAAtE,GAAA,CAAAE,aAAA,aAAAF,GAAA,CAAAE,aAAA,KAAArB,SAAA;UACA;YACAiH,OAAA;YACA7G,OAAA,WAAAoC,MAAA,CAAAiD,CAAA,iBAAAjD,MAAA,CAAArB,GAAA,CAAAxD,IAAA;UACA;QACA;;QAEA;QACA,IAAA8J,UAAA,CAAAtG,GAAA,CAAAE,aAAA,YAAAF,GAAA,CAAAuG,WAAA;UACA;YACAT,OAAA;YACA7G,OAAA,WAAAoC,MAAA,CAAAiD,CAAA,iBAAAjD,MAAA,CAAArB,GAAA,CAAAxD,IAAA;UACA;QACA;MACA;MAEA;QAAAsJ,OAAA;MAAA;IACA;IAEA,eACAa,mBAAA,WAAAA,oBAAA3G,GAAA;MAAA,IAAA4G,MAAA;MACA,IAAA/D,aAAA;QAAAlF,EAAA,EAAAqC,GAAA,CAAA6G;MAAA,GAAAtF,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAiF,MAAA,CAAAzI,SAAA,GAAAqD,GAAA,CAAA/E,IAAA;UACA,IAAAqG,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAxB,GAAA,CAAA/E,IAAA,CAAAwG,OAAA;UACA2D,MAAA,CAAA1D,cAAA,CAAAJ,IAAA;UACA8D,MAAA,CAAAzI,SAAA,CAAA2E,IAAA,GAAAA,IAAA;UACA8D,MAAA,CAAA1J,IAAA;QACA;MACA,GAAA4G,KAAA,WAAAgD,KAAA;QACAF,MAAA,CAAAzC,QAAA,CAAA2C,KAAA;MACA;IACA;IAEA;IACAV,YAAA,WAAAA,aAAApG,GAAA;MACA,IAAA+G,SAAA,GAAAT,UAAA,CAAAtG,GAAA,CAAA+G,SAAA;MACA,IAAAC,QAAA,GAAAV,UAAA,CAAAtG,GAAA,CAAAE,aAAA;MACA,IAAA+G,MAAA,GAAAF,SAAA,GAAAC,QAAA;MACA;MACA,OAAAE,IAAA,CAAAC,GAAA,IAAAD,IAAA,CAAAE,GAAA,MAAAH,MAAA,GAAAI,OAAA;IACA;IAEA;IACAhB,gBAAA,WAAAA,iBAAArG,GAAA;MACA,IAAAgE,SAAA,GAAAsC,UAAA,CAAAtG,GAAA,CAAAgE,SAAA;MACA,IAAAgD,QAAA,GAAAV,UAAA,CAAAtG,GAAA,CAAAE,aAAA;MACA,IAAA+G,MAAA,GAAAjD,SAAA,GAAAgD,QAAA;MACA;MACA,OAAAE,IAAA,CAAAC,GAAA,IAAAD,IAAA,CAAAE,GAAA,MAAAH,MAAA,GAAAI,OAAA;IACA;IAEA;IACA9D,oBAAA,WAAAA,qBAAA;MACA,IAAAwD,SAAA,GAAAT,UAAA,MAAAnI,SAAA,CAAA4I,SAAA;MACA,IAAAC,QAAA,GAAAV,UAAA,MAAA5I,IAAA,CAAAE,YAAA;MACA,IAAAqJ,MAAA,GAAAF,SAAA,GAAAC,QAAA;MACA;MACA,OAAAE,IAAA,CAAAC,GAAA,IAAAD,IAAA,CAAAE,GAAA,MAAAH,MAAA,GAAAI,OAAA;IACA;IAEA;IACA5D,wBAAA,WAAAA,yBAAA;MACA,IAAAO,SAAA,GAAAsC,UAAA,MAAAnI,SAAA,CAAA6F,SAAA;MACA,IAAAgD,QAAA,GAAAV,UAAA,MAAA5I,IAAA,CAAAG,gBAAA;MACA,IAAAoJ,MAAA,GAAAjD,SAAA,GAAAgD,QAAA;MACA;MACA,OAAAE,IAAA,CAAAC,GAAA,IAAAD,IAAA,CAAAE,GAAA,MAAAH,MAAA,GAAAI,OAAA;IACA;EACA;AACA", "ignoreList": []}]}