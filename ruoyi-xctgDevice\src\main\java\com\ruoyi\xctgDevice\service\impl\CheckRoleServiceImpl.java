package com.ruoyi.xctgDevice.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.WXUserMapper;
import com.ruoyi.xctgDevice.domain.CheckRoleDevice;
import com.ruoyi.xctgDevice.mapper.CheckRoleDeviceMapper;
import com.ruoyi.xctgDevice.mapper.CheckWorknoRoleMapper;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import com.ruoyi.xctgDevice.mapper.CheckRoleMapper;
import com.ruoyi.xctgDevice.domain.CheckRole;
import com.ruoyi.xctgDevice.service.ICheckRoleService;

/**
 * 角色信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-06-07
 */
@Service
@Component("CheckRoleServiceImpl")
public class CheckRoleServiceImpl implements ICheckRoleService
{
    @Autowired
    private CheckRoleMapper checkRoleMapper;

    @Autowired
    private CheckRoleDeviceMapper checkRoleDeviceMapper;

    @Autowired
    private CheckWorknoRoleMapper checkWorknoRoleMapper;


    /**
     * 查询角色信息
     * 
     * @param roleId 角色信息ID
     * @return 角色信息
     */
    @Override
    public CheckRole selectCheckRoleById(Long roleId)
    {
        return checkRoleMapper.selectCheckRoleById(roleId);
    }

    @Override
    public List<CheckRoleDevice> selectRoleDevices(Long roleId) {
        return checkRoleDeviceMapper.selectCheckRoleDeviceById(roleId);
    }

    @Override
    public int updateRoleStatus(CheckRole checkRole) {
        return checkRoleMapper.updateCheckRole(checkRole);
    }

    /**
     * 校验角色是否允许操作
     *
     * @param checkRole 角色信息
     */
    @Override
    public void checkRoleAllowed(CheckRole checkRole)
    {
        if (StringUtils.isNotNull(checkRole.getRoleId()) && checkRole.isSuperAdmin()) {
            throw new CustomException("不允许操作超级管理员角色");
        }
    }

    /**
     * 查询角色信息列表
     * 
     * @param checkRole 角色信息
     * @return 角色信息
     */
    @Override
    public List<CheckRole> selectCheckRoleList(CheckRole checkRole)
    {
        return checkRoleMapper.selectCheckRoleList(checkRole);
    }

    @Override
    public List<CheckRole> selectCheckRoleAll() {
        return checkRoleMapper.selectCheckRoleAll();
    }

    @Override
    public int saveRoleDevice(CheckRole checkRole) {
        int del = checkRoleDeviceMapper.deleteCheckRoleDeviceById(checkRole.getRoleId());
        int ins = 0;
        Long device [] = checkRole.getDevices();
        for(int i=0;i<device.length;i++){
            CheckRoleDevice checkRoleDevice = new CheckRoleDevice();
            checkRoleDevice.setRoleId(checkRole.getRoleId());
            checkRoleDevice.setDevice(device[i]);
            int t = checkRoleDeviceMapper.insertCheckRoleDevice(checkRoleDevice);
            if(t==1)ins=1;
        }
        if(del==0&&ins==0)return 0;
        else  return 1;

    }

    /**
     * 新增角色信息
     * 
     * @param checkRole 角色信息
     * @return 结果
     */
    @Override
    public int insertCheckRole(CheckRole checkRole)
    {
        checkRole.setCreateTime(DateUtils.getNowDate());
        return checkRoleMapper.insertCheckRole(checkRole);
    }

    /**
     * 修改角色信息
     * 
     * @param checkRole 角色信息
     * @return 结果
     */
    @Override
    public int updateCheckRole(CheckRole checkRole)
    {
        checkRole.setUpdateTime(DateUtils.getNowDate());
        return checkRoleMapper.updateCheckRole(checkRole);
    }

    /**
     * 批量删除角色信息
     * 
     * @param roleIds 需要删除的角色信息ID
     * @return 结果
     */
    @Override
    public int deleteCheckRoleByIds(Long[] roleIds)
    {
        if(roleIds.length>0){
            checkRoleDeviceMapper.deleteCheckRoleDeviceByIds(roleIds);
            checkWorknoRoleMapper.deleteCheckWorknoRoleByIds(roleIds);
        }
        return checkRoleMapper.deleteCheckRoleByIds(roleIds);
    }

    /**
     * 删除角色信息信息
     * 
     * @param roleId 角色信息ID
     * @return 结果
     */
    @Override
    public int deleteCheckRoleById(Long roleId)
    {
        return checkRoleMapper.deleteCheckRoleById(roleId);
    }

    /**
     * 定时提醒点检
     *
     * @return 结果
     */
    @Override
    public void remindCheck() throws ParseException {
        return;
    }

}
