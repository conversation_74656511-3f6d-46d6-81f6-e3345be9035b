{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerForm.vue", "mtime": 1756456493810}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBhbnN3ZXJMaXN0UGx1c0FsbCxmb3JtRnJlcXVlbmN5IH0gZnJvbSAiQC9hcGkvdFlqeS9mb3JtIjsNCmltcG9ydCBhbnN3ZXJJbnB1dCBmcm9tICIuL2lucHV0IjsNCmltcG9ydCB7IG5ld0FkZCwgYWRkQWxvbmUgfSBmcm9tICJAL2FwaS90WWp5L2Fuc3dlciI7DQppbXBvcnQgeyBnZXRBbGxSb290TGlzdEZvckFuc3dlciB9IGZyb20gIkAvYXBpL3RZankvZGltZW5zaW9uYWxpdHkiOw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOw0KaW1wb3J0IGF4aW9zIGZyb20gImF4aW9zIjsNCmltcG9ydCAqIGFzIHhsc3ggZnJvbSAneGxzeCc7DQoNCi8v5byV5YWl55u45YWz5qC35byPDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkFuc3dlciIsDQogIGNvbXBvbmVudHM6IHsgYW5zd2VySW5wdXQgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcGlja2VyT3B0aW9uczogew0KICAgICAgICBkaXNhYmxlZERhdGUodGltZSkgew0KICAgICAgICAgIHJldHVybiB0aW1lLmdldFRpbWUoKSA+IERhdGUubm93KCk7DQogICAgICAgIH0sDQogICAgICB9LA0KICAgICAgZnJlcXVlbmN5T3B0aW9uczogW10sDQoNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIGZvcm1RdWVzdGlvbjogdW5kZWZpbmVkLA0KICAgICAgICBmY0RhdGU6IHVuZGVmaW5lZCwNCiAgICAgICAgZGltZW5zaW9uYWxpdHlJZDogdW5kZWZpbmVkLA0KICAgICAgICBmb3JtUXVlc3Rpb246IHVuZGVmaW5lZCwNCiAgICAgIH0sDQoNCiAgICAgIGZvcm1UeXBlOiBudWxsLA0KICAgICAgZGltZW5zaW9uYWxpdHlOYW1lczogbnVsbCwNCiAgICAgIGRyYXdlclNob3c6IGZhbHNlLA0KICAgICAgc3RpY2t5VG9wOiAwLA0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgYW5zd2VyTGlzdDogW10sDQogICAgICBmb3JtRGF0YToge30sDQogICAgICByb3c6IHt9LA0KICAgICAgcm9vdExpc3Q6IFtdLA0KICAgICAgdXNlckluZm86IHt9LA0KICAgICAgZGF0ZXNhdmU6e30sDQogICAgICBwYXRoc2F2ZTp7fSwNCiAgICAgIGNvdW50OjEsDQogICAgICBkZXB0TmFtZTpudWxsLA0KICAgICAgZGltZW5zaW9uYWxpdHlOYW1lOm51bGwsDQogICAgICBkYXRlVmFsdWU6IG51bGwsDQogICAgICBzcGVjaWFsRmNEYXRlOm51bGwsDQogICAgICBxdWVyeUltcG9ydDogew0KICAgICAgICBzdGFydERhdGU6IG51bGwsDQogICAgICAgIGVuZERhdGU6IG51bGwsDQogICAgICAgIHJvb3RJZDogbnVsbCwNCiAgICAgIH0sDQogICAgICBpbXBvcnRPcGVuOmZhbHNlLA0KICAgICAgU3BlY2lhbEltcG9ydE9wZW46ZmFsc2UsDQogICAgICAvLyDlr7zlhaXlj4LmlbANCiAgICAgIHVwbG9hZDogew0KICAgICAgICAvLyDmmK/lkKbnpoHnlKjkuIrkvKANCiAgICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICAvLyDorr7nva7kuIrkvKDnmoTor7fmsYLlpLTpg6gNCiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0sDQogICAgICAgIC8vIOS4iuS8oOeahOWcsOWdgA0KICAgICAgICB1cmw6DQogICAgICAgICAgcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArDQogICAgICAgICAgIi93ZWIvVFlqeS9hbnN3ZXIvaW1wb3J0RGF0YSIsDQogICAgICB9LA0KDQoNCiAgICAgIHVwbG9hZFNwZWNpYWw6IHsNCiAgICAgICAgLy8g5piv5ZCm56aB55So5LiK5LygDQogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwNCiAgICAgICAgLy8g6K6+572u5LiK5Lyg55qE6K+35rGC5aS06YOoDQogICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSB9LA0KICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYANCiAgICAgICAgdXJsOg0KICAgICAgICAgIHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKw0KICAgICAgICAgICIvd2ViL1RZankvYW5zd2VyL2ltcG9ydERhdGFTcGVjaWFsIiwNCg0KICAgICAgfSwNCiAgICAgIGV4Y2VsSHRtbDoiIiwNCiAgICAgIHNlYXJjaG9wZW46ZmFsc2UsDQogICAgICBleGNlbERhdGE6IFtdLCAvLyDlrZjlgqggRXhjZWwg5pWw5o2uDQogICAgICBleGNlbHRpdGxlOiBbXSwNCiAgICAgIGN1c3RvbUJsb2JDb250ZW50OiIiDQogICAgfTsNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLnVzZXJJbmZvID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLiRzdG9yZS5zdGF0ZS51c2VyKSk7DQogIH0sDQoNCiAgY3JlYXRlZCgpIHsNCiAgICBjb25zdCBkaW1lbnNpb25hbGl0eUlkID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZGltZW5zaW9uYWxpdHlJZDsNCiAgICBjb25zdCBmY0RhdGUgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5mY0RhdGU7DQogICAgdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkPWRpbWVuc2lvbmFsaXR5SWQNCiAgICB0aGlzLnF1ZXJ5UGFyYW1zLmZjRGF0ZT1mY0RhdGUNCiAgICB0aGlzLmluaXREYXRhKCk7DQoNCg0KICAgIC8vIGlmKHRoaXMuJHJvdXRlLnF1ZXJ5KQ0KICAgIC8vIHsNCiAgICAvLyAgIGNvbnN0IGRpbWVuc2lvbmFsaXR5SWQgPSB0aGlzLiRyb3V0ZS5xdWVyeSAmJiB0aGlzLiRyb3V0ZS5xdWVyeS5kaW1lbnNpb25hbGl0eUlkOw0KICAgIC8vICAgLy8gY29uc3QgZGltZW5zaW9uYWxpdHlOYW1lID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZGltZW5zaW9uYWxpdHlOYW1lOw0KICAgIC8vICAgY29uc3QgZmNEYXRlID0gdGhpcy4kcm91dGUucXVlcnkgJiYgdGhpcy4kcm91dGUucXVlcnkuZmNEYXRlOw0KICAgIC8vICAgdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkPWRpbWVuc2lvbmFsaXR5SWQNCiAgICAvLyAgIC8vIHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlOYW1lPWRpbWVuc2lvbmFsaXR5TmFtZQ0KICAgIC8vICAgdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGU9ZmNEYXRlDQogICAgLy8gICB0aGlzLmluaXREYXRhMSgpOw0KICAgIC8vIH0NCiAgICAvLyBlbHNlDQogICAgLy8gew0KICAgIC8vICAgdGhpcy5pbml0RGF0YSgpOw0KICAgIC8vIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIG9uRGF0ZUNoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZGF0ZVZhbHVlKTsNCiAgICAgIGlmICh0aGlzLmRhdGVWYWx1ZSAhPSBudWxsICYmIHRoaXMuZGF0ZVZhbHVlICE9ICIiKSB7DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuc3RhcnREYXRlID0gdGhpcy5kYXRlVmFsdWVbMF07DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuZW5kRGF0ZSA9IHRoaXMuZGF0ZVZhbHVlWzFdOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5zdGFydERhdGUgPSAiIjsNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5lbmREYXRlID0gIiI7DQogICAgICB9DQogICAgfSwNCiAgICBjbGlja05vZGUoJGV2ZW50LCBub2RlKSB7DQogICAgICAkZXZlbnQudGFyZ2V0LnBhcmVudEVsZW1lbnQucGFyZW50RWxlbWVudC5maXJzdEVsZW1lbnRDaGlsZC5jbGljaygpOw0KICAgIH0sDQogICAgY2hhbmdlRXZlbnQocGFyYW1zKSB7DQogICAgICBjb25zdCAkZm9ybSA9IHRoaXMuJHJlZnMuZm9ybVJlZjsNCiAgICAgIGlmICgkZm9ybSkgew0KICAgICAgICAkZm9ybS51cGRhdGVTdGF0dXMocGFyYW1zKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGRpc2FibGVkRGF0ZSh0aW1lKSB7DQogICAgICByZXR1cm4gdGltZS5nZXRUaW1lKCkgPCBEYXRlLm5vdygpIC0gOC42NGU3OyAvLyA4LjY0ZTcg5q+r56eS5pWw5Luj6KGo5LiA5aSpDQogICAgfSwNCiAgICBpbnB1dENoYW5nZSh2YWwsIHJvdykgew0KICAgICAgcm93LmZvcm1WYWx1ZSA9IHZhbDsNCiAgICB9LA0KICAgIGhhbmRsZVNjcm9sbCgpIHsNCiAgICAgIHRoaXMuaXNTdGlja3kgPSB3aW5kb3cuc2Nyb2xsWSA+PSB0aGlzLnN0aWNreVRvcDsNCiAgICB9LA0KICAgIGluaXREYXRhKCkgew0KICAgICAgZ2V0QWxsUm9vdExpc3RGb3JBbnN3ZXIoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5yb290TGlzdCA9IHJlcy5kYXRhOw0KICAgICAgICBpZih0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQ9PW51bGwpDQogICAgICAgIHsNCiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPSB0aGlzLnJvb3RMaXN0WzBdLnZhbHVlOw0KICAgICAgICAgIHRoaXMuZGVwdE5hbWU9IHRoaXMucm9vdExpc3RbMF0uZGVwdE5hbWU7DQogICAgICAgICAgdGhpcy5kZXB0Q29kZT0gdGhpcy5yb290TGlzdFswXS5kZXB0Q29kZTsNCiAgICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZT10aGlzLnJvb3RMaXN0WzBdLmxhYmVsDQogICAgICAgIH0NCiAgICAgICAgZWxzZQ0KICAgICAgICB7DQogICAgICAgICAgLy8gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkID0gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkOw0KICAgICAgICAgIGZvcihsZXQgaT0wO2k8dGhpcy5yb290TGlzdC5sZW5ndGg7aSsrKQ0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGlmKHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZCA9PSB0aGlzLnJvb3RMaXN0W2ldLnZhbHVlKQ0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPSB0aGlzLnJvb3RMaXN0W2ldLnZhbHVlOw0KICAgICAgICAgICAgICB0aGlzLmRlcHROYW1lPSB0aGlzLnJvb3RMaXN0W2ldLmRlcHROYW1lOw0KICAgICAgICAgICAgICB0aGlzLmRlcHRDb2RlPSB0aGlzLnJvb3RMaXN0W2ldLmRlcHRDb2RlOw0KICAgICAgICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZT10aGlzLnJvb3RMaXN0W2ldLmxhYmVsDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBpbml0RGF0YTEoKSB7DQogICAgICBnZXRBbGxSb290TGlzdEZvckFuc3dlcigpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLnJvb3RMaXN0ID0gcmVzLmRhdGE7DQogICAgICAgIGZvcihsZXQgaT0wO2k8dGhpcy5yb290TGlzdC5sZW5ndGg7aSsrKQ0KICAgICAgICB7DQogICAgICAgICAgaWYodGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkID09IHRoaXMucm9vdExpc3RbaV0udmFsdWUpDQogICAgICAgICAgew0KICAgICAgICAgICAgdGhpcy5kZXB0TmFtZT0gdGhpcy5yb290TGlzdFswXS5kZXB0TmFtZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNvbnRhaW5zU3Vic3RyaW5nKHN1YnN0cmluZywgc3RyaW5nKSB7DQogICAgICByZXR1cm4gc3RyaW5nLmluY2x1ZGVzKHN1YnN0cmluZyk7DQogICAgfSwNCiAgICBhbG9uZUxpc3Qoc3RyaW5nKSB7DQogICAgICBpZihzdHJpbmc9PSAn5rCU5L2T57uT566X5pyI5oqlJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn6auY54KJ44CB6L2s54KJ54Wk5rCU5pyI5oql6KGoJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn5aSp54S25rCU5raI6ICX5pyI5oql6KGoJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn6JK45rG95raI6ICX5pyI5oql6KGoJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAn55S16YeP5pyI5oql6KGoJykNCiAgICAgIHsNCiAgICAgICAgcmV0dXJuIHRydWU7DQogICAgICB9DQogICAgICBpZihzdHJpbmc9PSAnMjAyNeW5tOe7j+a1jui0o+S7u+WItuiAg+aguOihqO+8iOeJueadv+S6i+S4mumDqO+8iScpDQogICAgICB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KICAgICAgaWYoc3RyaW5nPT0gJ+awtOWkhOeQhuawtOmHj+aKpeihqCcpDQogICAgICB7DQogICAgICAgIHJldHVybiB0cnVlOw0KICAgICAgfQ0KICAgICAgDQogICAgICAvLyBpZihzdHJpbmc9PSAn56CU56m26Zmi55uu5qCH5oyH5qCH5LiA6KeIJykNCiAgICAgIC8vIHsNCiAgICAgIC8vICAgcmV0dXJuIHRydWU7DQogICAgICAvLyB9DQogICAgICByZXR1cm4gZmFsc2U7DQogICAgfSwNCg0KICAgIC8qKiDmn6Xor6JUWWp5QW5zd2Vy5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIGZvcm1GcmVxdWVuY3koe2RpbWVuc2lvbmFsaXR5SWQ6IHRoaXMucXVlcnlQYXJhbXMuZGltZW5zaW9uYWxpdHlJZH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICBpZih0aGlzLmNvdW50IT1yZXMuZGF0YSkNCiAgICAgICAgICAgew0KICAgICAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGU9dW5kZWZpbmVkDQogICAgICAgICAgIH0NCiAgICAgICAgICAgdGhpcy5jb3VudD1yZXMuZGF0YQ0KICAgICAgfSk7DQoNCiAgICAgIHRoaXMuYW5zd2VyTGlzdCA9IFtdOw0KICAgICAgYW5zd2VyTGlzdFBsdXNBbGwoew0KICAgICAgICBmY0RhdGU6IHRoaXMucXVlcnlQYXJhbXMuZmNEYXRlLA0KICAgICAgICBkaW1lbnNpb25hbGl0eUlkOiB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQsDQogICAgICAgIGZvcm1RdWVzdGlvbjogdGhpcy5xdWVyeVBhcmFtcy5mb3JtUXVlc3Rpb24sDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgbGV0IGFuc3dlckxpc3QgPSBbXTsNCiAgICAgICAgbGV0IGxpc3QgPSByZXMuZGF0YTsNCiAgICAgICAgZm9yKGxldCBpPTA7aTx0aGlzLnJvb3RMaXN0Lmxlbmd0aDtpKyspDQogICAgICAgIHsNCiAgICAgICAgICBpZih0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPT0gdGhpcy5yb290TGlzdFtpXS52YWx1ZSkNCiAgICAgICAgICB7DQogICAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPSB0aGlzLnJvb3RMaXN0W2ldLnZhbHVlOw0KICAgICAgICAgICAgdGhpcy5kZXB0TmFtZT0gdGhpcy5yb290TGlzdFtpXS5kZXB0TmFtZTsNCiAgICAgICAgICAgIHRoaXMuZGVwdENvZGU9IHRoaXMucm9vdExpc3RbaV0uZGVwdENvZGU7DQogICAgICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZT10aGlzLnJvb3RMaXN0W2ldLmxhYmVsDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmKHRoaXMuY29udGFpbnNTdWJzdHJpbmcoJ+WuieWFqOi0o+S7u+W3pei1hOiAg+aguOihqCcsdGhpcy5kaW1lbnNpb25hbGl0eU5hbWUpKQ0KICAgICAgICB7DQogICAgICAgICAgY29uc29sZS5sb2coInRlc3QxIikNCiAgICAgICAgICBsZXQgbnVtPTANCiAgICAgICAgICBmb3IobGV0IGk9MDtpPGxpc3QubGVuZ3RoO2krKykNCiAgICAgICAgICB7DQogICAgICAgICAgICBpZihsaXN0W2ldLmZvcm1RdWVzdGlvbiE9J+iHquivhOaAu+WIhicgJiYgbGlzdFtpXS5mb3JtUXVlc3Rpb24hPSfljoLplb/or4TliIYnKQ0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBudW09MTsNCiAgICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmKG51bT09MCkNCiAgICAgICAgICB7DQogICAgICAgICAgICBmb3IobGV0IGk9MDtpPGxpc3QubGVuZ3RoO2krKykNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbGlzdFtpXS5kaW1lbnNpb25hbGl0eU5hbWU9bGlzdFtpXS5kaW1lbnNpb25hbGl0eU5hbWUucmVwbGFjZSgnL+S4g+OAgeiAg+aguOivhOWIhicsICcnKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgY29uc29sZS5sb2coInRlc3QzIixsaXN0KQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICANCiAgICAgICAgZm9yKGxldCBpPTA7aTxsaXN0Lmxlbmd0aDtpKyspDQogICAgICAgIHsNCiAgICAgICAgICAgIHRoaXMuZGF0ZXNhdmVbbGlzdFtpXS5mb3JtSWRdPWxpc3RbaV0uZm9ybVZhbHVlDQogICAgICAgICAgICB0aGlzLnBhdGhzYXZlW2xpc3RbaV0uZGltZW5zaW9uYWxpdHlOYW1lXT1saXN0W2ldLmRpbWVuc2lvbmFsaXR5UGF0aA0KICAgICAgICB9DQogICAgICAgIC8vIOS9v+eUqCBtYXAg5o+Q5Y+WIGRpbWVuc2lvbmFsaXR5TmFtZSDlsZ7mgKfliLDkuIDkuKrmlbDnu4QNCiAgICAgICAgbGV0IGRpbWVuc2lvbmFsaXR5TmFtZXMgPSBsaXN0Lm1hcCgoeCkgPT4geC5kaW1lbnNpb25hbGl0eU5hbWUpOw0KDQogICAgICAgIC8vIOaPkOWPliAvIOWQjueahOWJjeS4ieS9jeWtl+espu+8jOW5tuS4juWOn+Wtl+espuS4sumFjeWvuQ0KICAgICAgICBkaW1lbnNpb25hbGl0eU5hbWVzID0gZGltZW5zaW9uYWxpdHlOYW1lcy5tYXAoKG5hbWUpID0+IHsNCiAgICAgICAgICAvLyBsZXQga2V5ID0gbmFtZS5pbmNsdWRlcygiLyIpID8gbmFtZS5zcGxpdCgiLyIpWzFdLnNsaWNlKDAsIDMpIDogIiI7DQogICAgICAgICAgbGV0IGtleSA9IHRoaXMucGF0aHNhdmVbbmFtZV07DQogICAgICAgICAgcmV0dXJuIHsgb3JpZ2luYWxOYW1lOiBuYW1lLCBzb3J0S2V5OiBrZXkgfTsNCiAgICAgICAgfSk7DQoNCiAgICAgICAgLy8g5oyJ54Wn5o+Q5Y+W5Ye655qE5YmN5LiJ5a2X56ym5o6S5bqPDQogICAgICAgIGRpbWVuc2lvbmFsaXR5TmFtZXMuc29ydCgoYSwgYikgPT4gYS5zb3J0S2V5LmxvY2FsZUNvbXBhcmUoYi5zb3J0S2V5KSk7DQogICAgICAgIC8vIGNvbnNvbGUubG9nKCJ0ZXN0MCIsZGltZW5zaW9uYWxpdHlOYW1lcykNCiAgICAgICAgLy8g5aaC5p6c6ZyA6KaB77yM5Y+v5Lul5o+Q5Y+W5o6S5bqP5ZCO55qE5Y6f5aeL5ZCN5a2XDQogICAgICAgIGRpbWVuc2lvbmFsaXR5TmFtZXMgPSBkaW1lbnNpb25hbGl0eU5hbWVzLm1hcCgNCiAgICAgICAgICAoaXRlbSkgPT4gaXRlbS5vcmlnaW5hbE5hbWUNCiAgICAgICAgKTsNCg0KICAgICAgICAvLyDkvb/nlKggU2V0IOWOu+mHjQ0KICAgICAgICBsZXQgdW5pcXVlRGltZW5zaW9uYWxpdHlOYW1lcyA9IFsuLi5uZXcgU2V0KGRpbWVuc2lvbmFsaXR5TmFtZXMpXTsNCg0KICAgICAgICB1bmlxdWVEaW1lbnNpb25hbGl0eU5hbWVzLmZvckVhY2goKHRpdGxlKSA9PiB7DQogICAgICAgICAgbGV0IGdyb3VwID0gew0KICAgICAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICAgICAgbGlzdDogW10sDQogICAgICAgICAgfTsNCiAgICAgICAgICBncm91cC50aXRsZSA9IHRpdGxlOw0KICAgICAgICAgIGdyb3VwLmxpc3QgPSBsaXN0LmZpbHRlcigoaXRlbSkgPT4gaXRlbS5kaW1lbnNpb25hbGl0eU5hbWUgPT09IHRpdGxlKTsNCiAgICAgICAgICAvLyDlgYforr7kvaDmnInkuIDkuKrmlbDnu4TmnaXlrZjlgqjmiYDmnInnmoTnu4QNCiAgICAgICAgICBhbnN3ZXJMaXN0LnB1c2goZ3JvdXApOyAvLyDlsIbnlJ/miJDnmoTnu4Tmt7vliqDliLBncm91cHPmlbDnu4TkuK0NCiAgICAgICAgfSk7DQogICAgICAgIHRoaXMuYW5zd2VyTGlzdCA9IGFuc3dlckxpc3Q7DQogICAgICAgIC8vIGNvbnNvbGUubG9nKCJ0ZXN0MTExIixhbnN3ZXJMaXN0KQ0KICAgICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIGZvcihsZXQgaT0wO2k8dGhpcy5yb290TGlzdC5sZW5ndGg7aSsrKQ0KICAgICAgew0KICAgICAgICBpZih0aGlzLnF1ZXJ5UGFyYW1zLmRpbWVuc2lvbmFsaXR5SWQgPT0gdGhpcy5yb290TGlzdFtpXS52YWx1ZSkNCiAgICAgICAgew0KICAgICAgICAgIHRoaXMuZGVwdE5hbWU9IHRoaXMucm9vdExpc3RbaV0uZGVwdE5hbWU7DQogICAgICAgICAgdGhpcy5kZXB0Q29kZT0gdGhpcy5yb290TGlzdFtpXS5kZXB0Q29kZTsNCiAgICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZT10aGlzLnJvb3RMaXN0W2ldLmxhYmVsDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgaGFuZGxlUHJldmlldygpIHsNCiAgICAgIGxldCBxdWVyeUltcG9ydD17fQ0KICAgICAgcXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICBxdWVyeUltcG9ydC5mY0RhdGUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmZjRGF0ZQ0KICAgICAgcXVlcnlJbXBvcnQudHlwZT0iMCINCiAgICAgIGlmKHRoaXMuZGltZW5zaW9uYWxpdHlOYW1lPT0n56CU56m26Zmi55uu5qCH5oyH5qCH5LiA6KeIJykNCiAgICAgIHsNCiAgICAgICAgdGhpcy5kb3dubG9hZFhsc3goDQogICAgICAgICIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFdpdGhUZW1wbGF0ZSIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi5xdWVyeUltcG9ydCwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWUrIigiICt0aGlzLnNwZWNpYWxGY0RhdGUrDQogICAgICAgICAgIikiICsNCiAgICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgICApLnRoZW4oKGJsb2IpID0+IHsNCiAgICAgICAgbGV0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7DQogICAgICAgIHJlYWRlci5yZWFkQXNBcnJheUJ1ZmZlcihibG9iKTsNCiAgICAgICAgcmVhZGVyLm9ubG9hZCA9IChldnQpID0+IHsNCiAgICAgICAgICB0aGlzLmN1c3RvbUJsb2JDb250ZW50PXJlYWRlci5yZXN1bHQ7DQogICAgICAgICAgbGV0IGludHMgPSBuZXcgVWludDhBcnJheShldnQudGFyZ2V0LnJlc3VsdCk7IC8v6KaB5L2/55So6K+75Y+W55qE5YaF5a6577yM5omA5Lul5bCG6K+75Y+W5YaF5a656L2s5YyW5oiQVWludDhBcnJheQ0KICAgICAgICAgIGludHMgPSBpbnRzLnNsaWNlKDAsIGJsb2Iuc2l6ZSk7DQogICAgICAgICAgbGV0IHdvcmtCb29rID0geGxzeC5yZWFkKGludHMsIHsgdHlwZTogImFycmF5IiB9KTsNCiAgICAgICAgICBsZXQgc2hlZXROYW1lcyA9IHdvcmtCb29rLlNoZWV0TmFtZXM7DQogICAgICAgICAgbGV0IHNoZWV0TmFtZSA9IHNoZWV0TmFtZXNbMF07DQogICAgICAgICAgbGV0IHdvcmtTaGVldCA9IHdvcmtCb29rLlNoZWV0c1tzaGVldE5hbWVdOw0KICAgICAgICAgIC8v6I635Y+WRXhjbGXlhoXlrrnvvIzlubblsIbnqbrlhoXlrrnnlKjnqbrlgLzkv53lrZgNCiAgICAgICAgICBsZXQgZXhjZWxUYWJsZSA9IHhsc3gudXRpbHMuc2hlZXRfdG9fanNvbih3b3JrU2hlZXQpOw0KICAgICAgICAgIC8vIOiOt+WPlkV4Y2Vs5aS06YOoDQogICAgICAgICAgbGV0IHRhYmxlVGhlYWQgPSBBcnJheS5mcm9tKE9iamVjdC5rZXlzKGV4Y2VsVGFibGVbMF0pKS5tYXAoDQogICAgICAgICAgICAoaXRlbSkgPT4gew0KICAgICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICk7DQogICAgICAgICAgdGhpcy5leGNlbERhdGEgPSBleGNlbFRhYmxlOw0KICAgICAgICAgIHRoaXMuZXhjZWx0aXRsZT10YWJsZVRoZWFkDQogICAgICAgICAgdGhpcy5leGNlbEh0bWw9IGV4Y2VsVGFibGUNCiAgICAgICAgICB0aGlzLnNlYXJjaG9wZW4gPSB0cnVlOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIH0NCiAgICAgIGVsc2UNCiAgICAgIHsNCiAgICAgICAgdGhpcy5kb3dubG9hZFhsc3goDQogICAgICAgICIvd2ViL1RZankvYW5zd2VyL2V4cG9ydFRlbXBsYXRlU3BlY2lhbCIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi5xdWVyeUltcG9ydCwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5kaW1lbnNpb25hbGl0eU5hbWUrIigiICt0aGlzLnNwZWNpYWxGY0RhdGUrDQogICAgICAgICAgIikiICsNCiAgICAgICAgICBg5pWw5o2uLnhsc3hgDQogICAgICApLnRoZW4oKGJsb2IpID0+IHsNCiAgICAgICAgbGV0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7DQogICAgICAgIHJlYWRlci5yZWFkQXNBcnJheUJ1ZmZlcihibG9iKTsNCiAgICAgICAgcmVhZGVyLm9ubG9hZCA9IChldnQpID0+IHsNCiAgICAgICAgICB0aGlzLmN1c3RvbUJsb2JDb250ZW50PXJlYWRlci5yZXN1bHQ7DQogICAgICAgICAgbGV0IGludHMgPSBuZXcgVWludDhBcnJheShldnQudGFyZ2V0LnJlc3VsdCk7IC8v6KaB5L2/55So6K+75Y+W55qE5YaF5a6577yM5omA5Lul5bCG6K+75Y+W5YaF5a656L2s5YyW5oiQVWludDhBcnJheQ0KICAgICAgICAgIGludHMgPSBpbnRzLnNsaWNlKDAsIGJsb2Iuc2l6ZSk7DQogICAgICAgICAgbGV0IHdvcmtCb29rID0geGxzeC5yZWFkKGludHMsIHsgdHlwZTogImFycmF5IiB9KTsNCiAgICAgICAgICBsZXQgc2hlZXROYW1lcyA9IHdvcmtCb29rLlNoZWV0TmFtZXM7DQogICAgICAgICAgbGV0IHNoZWV0TmFtZSA9IHNoZWV0TmFtZXNbMF07DQogICAgICAgICAgbGV0IHdvcmtTaGVldCA9IHdvcmtCb29rLlNoZWV0c1tzaGVldE5hbWVdOw0KICAgICAgICAgIC8v6I635Y+WRXhjbGXlhoXlrrnvvIzlubblsIbnqbrlhoXlrrnnlKjnqbrlgLzkv53lrZgNCiAgICAgICAgICBsZXQgZXhjZWxUYWJsZSA9IHhsc3gudXRpbHMuc2hlZXRfdG9fanNvbih3b3JrU2hlZXQpOw0KICAgICAgICAgIC8vIOiOt+WPlkV4Y2Vs5aS06YOoDQogICAgICAgICAgbGV0IHRhYmxlVGhlYWQgPSBBcnJheS5mcm9tKE9iamVjdC5rZXlzKGV4Y2VsVGFibGVbMF0pKS5tYXAoDQogICAgICAgICAgICAoaXRlbSkgPT4gew0KICAgICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICk7DQogICAgICAgICAgdGhpcy5leGNlbERhdGEgPSBleGNlbFRhYmxlOw0KICAgICAgICAgIHRoaXMuZXhjZWx0aXRsZT10YWJsZVRoZWFkDQogICAgICAgICAgdGhpcy5leGNlbEh0bWw9IGV4Y2VsVGFibGUNCiAgICAgICAgICB0aGlzLnNlYXJjaG9wZW4gPSB0cnVlOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIA0KICAgIGhhbmRsZURhdGVDaGFuZ2UoKSB7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmZjRGF0ZSA9IHVuZGVmaW5lZDsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZm9ybVF1ZXN0aW9uID0gdW5kZWZpbmVkOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkID0gdW5kZWZpbmVkOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBoYW5kbGVVcGxvYWQoeyBmaWxlIH0pIHsNCiAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7DQogICAgICBmb3JtRGF0YS5hcHBlbmQoImZpbGUiLCBmaWxlKTsNCiAgICAgIHJldHVybiBheGlvcw0KICAgICAgICAucG9zdCgNCiAgICAgICAgICBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9jb21tb24vdXBsb2FkTWluaW9EYXRhUmVwb3J0IiwNCiAgICAgICAgICBmb3JtRGF0YQ0KICAgICAgICApDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgLi4ucmVzLmRhdGEsDQogICAgICAgICAgfTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgaGFuZGxlU3VibWl0KCkgew0KICAgICAgY29uc29sZS5sb2codGhpcy5hbnN3ZXJMaXN0KTsNCiAgICAgIGNvbnNvbGUubG9nKCJ0ZXN0MSIsdGhpcy5kYXRlc2F2ZSk7DQogICAgICAvLyDpppblhYjlr7kgYW5zd2VyTGlzdCDov5vooYzlpITnkIbvvJrlkIjlubbjgIHov4fmu6TlkozovazmjaINCiAgICAgIGxldCBwcm9jZXNzZWRMaXN0cyA9IHRoaXMuYW5zd2VyTGlzdA0KICAgICAgICAucmVkdWNlKChhY2MsIGN1cnJlbnQpID0+IHsNCiAgICAgICAgICByZXR1cm4gYWNjLmNvbmNhdChjdXJyZW50Lmxpc3QpOw0KICAgICAgICB9LCBbXSkNCiAgICAgICAgLmZpbHRlcigoeCkgPT4gew0KICAgICAgICAgIC8vIOi/h+a7pOadoeS7tg0KICAgICAgICAgIGNvbnNvbGUubG9nKCJ0ZXN0MSIseC5zdGF0dXMpOw0KICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuZGF0ZXNhdmVbeC5mb3JtSWRdKTsNCiAgICAgICAgICBjb25zb2xlLmxvZyh4LmZvcm1WYWx1ZSk7DQogICAgICAgICAgcmV0dXJuICgNCiAgICAgICAgICAgIHguZm9ybVZhbHVlICE9IG51bGwgJiYNCiAgICAgICAgICAgIHguZm9ybVZhbHVlICE9ICIiICYmDQogICAgICAgICAgICAoKCFbIjEiXS5pbmNsdWRlcyh4LnN0YXR1cykpJiYNCiAgICAgICAgICAgICgNCiAgICAgICAgICAgICAgKFsiMCIsIjIiLCIzIl0uaW5jbHVkZXMoeC5zdGF0dXMpICYmIHRoaXMuZGF0ZXNhdmVbeC5mb3JtSWRdIT14LmZvcm1WYWx1ZSkpDQogICAgICAgICAgICAgIHx8KFsiNCJdLmluY2x1ZGVzKHguc3RhdHVzKSkNCiAgICAgICAgICAgICkNCiAgICAgICAgICApOw0KICAgICAgICB9KTsNCg0KICAgICAgLy8g5a+556ym5ZCI5p2h5Lu255qE5YWD57Sg6L+b6KGMIGZvcm1WYWx1ZSDnmoTovazmjaINCiAgICAgIHByb2Nlc3NlZExpc3RzLmZvckVhY2goKHgpID0+IHsNCiAgICAgICAgaWYgKFsiMCIsICIxIl0uaW5jbHVkZXMoeC5mb3JtVHlwZSkpIHsNCiAgICAgICAgICB4LmZvcm1WYWx1ZSA9IHBhcnNlRmxvYXQoeC5mb3JtVmFsdWUpOw0KICAgICAgICB9DQogICAgICAgIHguZmNEYXRlID0gdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGU7DQogICAgICB9KTsNCg0KICAgICAgLy8g5pyA5ZCO6L+b6KGM5rex5ou36LSdDQogICAgICBsZXQgYWxsTGlzdHMgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHByb2Nlc3NlZExpc3RzKSk7DQoNCiAgICAgIC8vIGNvbnNvbGUubG9nKCJhbGxMaXN0czoiLCBhbGxMaXN0cyk7DQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOw0KICAgICAgDQogICAgICBsZXQgZGF0ZXN0cj0i6K+356Gu5a6a5piv5ZCm6KaB5o+Q5Lqk5pWw5o2uIg0KICAgICAgaWYodGhpcy5xdWVyeVBhcmFtcy5mY0RhdGU9PW51bGwpDQogICAgICB7DQogICAgICAgIC8vIOiOt+WPluW5tOaciOaXpQ0KICAgICAgICBjb25zdCB5ZWFyID0gbm93LmdldEZ1bGxZZWFyKCk7DQogICAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKG5vdy5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsgLy8g5pyI5Lu95LuOMOW8gOWni++8jOmcgOimgSsxDQogICAgICAgIGNvbnN0IGRheSA9IFN0cmluZyhub3cuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpOw0KDQogICAgICAgIC8vIOagvOW8j+WMluS4uiB5eXl55bm0TU3mnIhkZOaXpQ0KICAgICAgICBjb25zdCBmb3JtYXQxID0gYCR7eWVhcn3lubQke21vbnRofeaciCR7ZGF5feaXpWA7DQoNCiAgICAgICAgLy8g5qC85byP5YyW5Li6IHl5eXnlubRNTeaciA0KICAgICAgICBjb25zdCBmb3JtYXQyID0gYCR7eWVhcn3lubQke21vbnRofeaciGA7DQogICAgICAgIA0KICAgICAgICBpZih0aGlzLmNvdW50PT0nMScpDQogICAgICAgIHsNCiAgICAgICAgICBkYXRlc3RyPSfmgqjmnKrpgInmi6nml7bpl7Qs6K+356Gu5a6a5piv5ZCm6KaB5o+Q5LqkJytmb3JtYXQxKyfnmoTmlbDmja4/Jw0KICAgICAgICB9DQogICAgICAgIGVsc2UNCiAgICAgICAgew0KICAgICAgICAgIGRhdGVzdHI9J+aCqOacqumAieaLqeaXtumXtCzor7fnoa7lrprmmK/lkKbopoHmj5DkuqQnK2Zvcm1hdDIrJ+eahOaVsOaNrj8nDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuJGNvbmZpcm0oDQogICAgICAgIGRhdGVzdHIsDQogICAgICAgIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KDQogICAgICAgIH0NCiAgICAgICkudGhlbigoKT0+ew0KICAgICAgICBuZXdBZGQoYWxsTGlzdHMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv53lrZjmiJDlip8iKTsNCiAgICAgIH0pOw0KICAgICAgfSkuY2F0Y2goKCk9Pnt9KTsNCg0KICAgICAgLy8gbmV3QWRkKGFsbExpc3RzKS50aGVuKChyZXMpID0+IHsNCiAgICAgIC8vICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAvLyAgIHRoaXMubXNnU3VjY2Vzcygi5L+d5a2Y5oiQ5YqfIik7DQogICAgICAvLyB9KTsNCiAgICB9LA0KICAgIA0KICAgIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzcygpIHsNCiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsNCiAgICB9LA0KICAgIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlKSB7DQogICAgICBjb25zb2xlLmxvZyhyZXNwb25zZSkNCiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkuIrkvKDmiJDlip8iKTsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuaW1wb3J0T3BlbiA9IGZhbHNlOw0KICAgICAgICB0aGlzLlNwZWNpYWxJbXBvcnRPcGVuID0gZmFsc2U7DQogICAgICB9DQogICAgICBlbHNlIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuS4iuS8oOWksei0pSIpDQogICAgICB9DQogICAgICB0aGlzLnVwbG9hZC5pc1VwbG9hZGluZyA9IGZhbHNlOw0KICAgIH0sDQogICAgLy8g5qih5p2/5LiL6L29DQogICAgZG93bmxvYWRUZW1wbGF0ZSgpew0KICAgIA0KICAgICAgaWYgKA0KICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnN0YXJ0RGF0ZSA9PSBudWxsIHx8DQogICAgICAgIHRoaXMucXVlcnlJbXBvcnQuc3RhcnREYXRlID09ICIifHwNCiAgICAgICAgdGhpcy5xdWVyeUltcG9ydC5lbmREYXRlID09IG51bGx8fA0KICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LmVuZERhdGUgPT0gIiINCiAgICAgICkgew0KICAgICAgICB0aGlzLiRub3RpZnkuZXJyb3Ioew0KICAgICAgICAgIHRpdGxlOiAi6ZSZ6K+vIiwNCiAgICAgICAgICBtZXNzYWdlOiAi5a+85Ye65YmN6K+35YWI6L6T5YWl5byA5aeL57uT5p2f5pe26Ze0IiwNCiAgICAgICAgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMucXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICB0aGlzLmRvd25sb2FkRmlsZSgNCiAgICAgICAgIi93ZWIvVFlqeS9hbnN3ZXIvZXhwb3J0VGVtcGxhdGUiLA0KICAgICAgICB7DQogICAgICAgICAgLi4udGhpcy5xdWVyeUltcG9ydCwNCiAgICAgICAgfSwNCiAgICAgICAgIigiICsNCiAgICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LnN0YXJ0RGF0ZSArDQogICAgICAgICAgIi0iICsNCiAgICAgICAgICB0aGlzLnF1ZXJ5SW1wb3J0LmVuZERhdGUgKw0KICAgICAgICAgICIpIiArDQogICAgICAgICAgYOaVsOaNri54bHN4YA0KICAgICAgKTsNCiAgICANCiAgICB9LA0KDQogICAgICAgIC8vIOaooeadv+S4i+i9vQ0KICAgIGRvd25sb2FkVGVtcGxhdGVTcGVjaWFsKCl7DQogICAgICBpZiAodGhpcy5zcGVjaWFsRmNEYXRlID09IG51bGwgKSB7DQogICAgICAgIHRoaXMuc3BlY2lhbEZjRGF0ZT0gdGhpcy5xdWVyeVBhcmFtcy5mY0RhdGUNCiAgICAgIH0NCg0KICAgICAgLy8gaWYgKA0KICAgICAgLy8gICB0aGlzLnNwZWNpYWxGY0RhdGUgPT0gbnVsbCANCiAgICAgIC8vICkgew0KICAgICAgLy8gICB0aGlzLiRub3RpZnkuZXJyb3Ioew0KICAgICAgLy8gICAgIHRpdGxlOiAi6ZSZ6K+vIiwNCiAgICAgIC8vICAgICBtZXNzYWdlOiAi5a+85Ye65YmN6K+35YWI6L6T5YWl5byA5aeL57uT5p2f5pe26Ze0IiwNCiAgICAgIC8vICAgfSk7DQogICAgICAvLyAgIHJldHVybjsNCiAgICAgIC8vIH0NCiAgICAgIGxldCBxdWVyeUltcG9ydD17fQ0KICAgICAgcXVlcnlJbXBvcnQucm9vdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kaW1lbnNpb25hbGl0eUlkDQogICAgICBxdWVyeUltcG9ydC5mY0RhdGUgPSB0aGlzLnNwZWNpYWxGY0RhdGUNCiAgICAgIHF1ZXJ5SW1wb3J0LnR5cGU9IjAiDQogICAgICB0aGlzLmRvd25sb2FkRmlsZSgNCiAgICAgICAgIi93ZWIvVFlqeS9hbnN3ZXIvZXhwb3J0VGVtcGxhdGVTcGVjaWFsIiwNCiAgICAgICAgew0KICAgICAgICAgIC4uLnF1ZXJ5SW1wb3J0LA0KICAgICAgICB9LA0KICAgICAgICB0aGlzLmRpbWVuc2lvbmFsaXR5TmFtZSsiKCIgK3RoaXMuc3BlY2lhbEZjRGF0ZSsNCiAgICAgICAgICAiKSIgKw0KICAgICAgICAgIGDmlbDmja4ueGxzeGANCiAgICAgICk7DQogIA0KICAgIH0sDQoNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["answerForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw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file": "answerForm.vue", "sourceRoot": "src/views/dataReport/answer", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      :model=\"queryParams\"\r\n      ref=\"queryForm\"\r\n      :inline=\"true\"\r\n      v-show=\"showSearch\"\r\n    >\r\n      <el-row :gutter=\"20\" style=\"margin: 20px\">\r\n        <el-form-item label=\"报表名称\" prop=\"dimensionalityId\">\r\n          <el-select\r\n            v-model=\"queryParams.dimensionalityId\"\r\n            placeholder=\"请选择\"\r\n            filterable\r\n            @change=\"handleQuery\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in rootList\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '1'\"  >\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"填报时间\" v-if=\"count == '0'\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.fcDate\"\r\n            value-format=\"yyyy-MM-01\"\r\n            type=\"month\"\r\n            :default-value=\"new Date()\"\r\n            :picker-options=\"pickerOptions\"\r\n            placeholder=\"选择时间\"\r\n            @change=\"handleDateChange\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n\r\n\r\n        <el-form-item label=\"填报问题\" prop=\"formQuestion\">\r\n          <el-input\r\n            v-model=\"queryParams.formQuestion\"\r\n            placeholder=\"请输入填报问题\"\r\n            clearable\r\n            size=\"small\"\r\n            @keyup.enter.native=\"handleQuery\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button\r\n            type=\"cyan\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handleQuery\"\r\n            >搜索</el-button\r\n          >\r\n        </el-form-item>\r\n        </el-row>\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-form-item>\r\n          <el-button  v-if=\" !containsSubstring('安全责任工资考核表',dimensionalityName) \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"importOpen = true\"\r\n            >数据导入导出</el-button\r\n          >\r\n          \r\n          <el-button v-if=\" aloneList(dimensionalityName) || containsSubstring('安全责任工资考核表',dimensionalityName)\"\r\n            type=\"danger\"\r\n            icon=\"el-icon-download\"\r\n            size=\"mini\"\r\n            @click=\"SpecialImportOpen = true\"\r\n            >单周期报表导入导出</el-button>\r\n\r\n          <el-button v-if=\"aloneList(dimensionalityName)  || containsSubstring('安全责任工资考核表',dimensionalityName) \"\r\n            type=\"warning\"\r\n            icon=\"el-icon-search\"\r\n            size=\"mini\"\r\n            @click=\"handlePreview\"\r\n            >数据预览</el-button\r\n          >\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">报表名称：{{dimensionalityName}}</el-col>\r\n        \r\n        <right-toolbar\r\n          :showSearch.sync=\"showSearch\"\r\n          @queryTable=\"getList\"\r\n        ></right-toolbar>\r\n      </el-row>\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">管理部门：{{deptName}}</el-col>\r\n        <right-toolbar\r\n          :showSearch.sync=\"showSearch\"\r\n          @queryTable=\"getList\"\r\n        ></right-toolbar>\r\n      </el-row>\r\n    </el-form>\r\n    <vxe-form\r\n      ref=\"formRef\"\r\n      :data=\"formData\"\r\n      @submit=\"handleSubmit\"\r\n      border\r\n      title-background\r\n      vertical-align=\"center\"\r\n      title-width=\"300\"\r\n      title-bold\r\n    >\r\n      <vxe-form-group\r\n        v-for=\"(group, index) in answerList\"\r\n        :key=\"index\"\r\n        span=\"24\"\r\n        :title=\"group.title\"\r\n        title-bold\r\n        vertical\r\n      >\r\n        <vxe-form-item\r\n          v-for=\"(question, qIndex) in group.list\"\r\n          :key=\"qIndex\"\r\n          :title=\"question.formQuestion\"\r\n          :field=\"answerList[index].list[qIndex].formValue\"\r\n          :span=\"question.formType == '3' ? 24 : 12\"\r\n          :item-render=\"{}\"\r\n        >\r\n          <template #default=\"params\">\r\n            <vxe-tag\r\n              v-if=\"question.status == '0'\"\r\n              status=\"primary\"\r\n              content=\"主要颜色\"\r\n              >待审核 （审核人姓名：{{question.checkWorkNo}}  审核人工号：{{question.checkUserName}}）</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '1'\"\r\n              status=\"warning\"\r\n              content=\"信息颜色\"\r\n              >审核中</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '2'\"\r\n              status=\"success\"\r\n              content=\"信息颜色\"\r\n              >审核完成</vxe-tag\r\n            >\r\n            <vxe-tag\r\n              v-if=\"question.status == '3'\"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >驳回理由: {{ question.assessment }}</vxe-tag\r\n            >\r\n\r\n            <vxe-input\r\n              v-if=\"question.formType == '0'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"integer\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '1'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"'float'\"\r\n            ></vxe-input>\r\n            <vxe-input\r\n              v-if=\"question.formType == '2'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              @change=\"changeEvent(params)\"\r\n              type=\"text\"\r\n            ></vxe-input>\r\n            <vxe-textarea\r\n              v-if=\"question.formType == '3'\"\r\n              v-model=\"answerList[index].list[qIndex].formValue\"\r\n              :placeholder=\"question.formQuestion\"\r\n            ></vxe-textarea>\r\n            <vxe-text v-if=\"question.formNote != null\" status=\"warning\"\r\n              >指标:{{ question.formNote }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formNote1 != null\" status=\"warning\"\r\n              >备注:{{ question.formNote1 }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.maximum != null\" status=\"primary\"\r\n              >最大值:{{ question.maximum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.minimum != null\" status=\"primary\"\r\n              >最小值:{{ question.minimum }}<br\r\n            /></vxe-text>\r\n            <vxe-text v-if=\"question.formUnit != null\" status=\"primary\"\r\n              >单位:{{ question.formUnit }}<br\r\n            /></vxe-text>\r\n            <vxe-tag\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              status=\"danger\"\r\n              content=\"警告颜色\"\r\n              >输入值超出预计范围，请输入原因和改进措施</vxe-tag\r\n            >\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].reason\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请填写原因\"\r\n            ></vxe-textarea>\r\n            <vxe-textarea\r\n              v-if=\"\r\n                question.formValue != null &&\r\n                question.formValue != '' &&\r\n                (question.formType == '0' || question.formType == '1') &&\r\n                ((question.minimum != null &&\r\n                  question.formValue < question.minimum) ||\r\n                  (question.maximum != null &&\r\n                    question.formValue > question.maximum))\r\n              \"\r\n              v-model=\"answerList[index].list[qIndex].measure\"\r\n              @change=\"changeEvent(params)\"\r\n              placeholder=\"请输入改进措施\"\r\n            ></vxe-textarea>\r\n          </template>\r\n        </vxe-form-item>\r\n      </vxe-form-group>\r\n\r\n      <vxe-form-item align=\"center\" span=\"24\" :item-render=\"{}\">\r\n        <template #default>\r\n          <vxe-button\r\n            type=\"submit\"\r\n            status=\"primary\"\r\n            content=\"提交\"\r\n          ></vxe-button>\r\n        </template>\r\n      </vxe-form-item>\r\n    </vxe-form>\r\n\r\n    <el-dialog\r\n      title=\"选择导出范围\"\r\n      :visible.sync=\"importOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>数据日期范围：</span>\r\n      <el-date-picker\r\n        v-model=\"dateValue\"\r\n        type=\"daterange\"\r\n        range-separator=\"至\"\r\n        start-placeholder=\"开始日期\"\r\n        end-placeholder=\"结束日期\"\r\n        value-format=\"yyyy-MM-dd\"\r\n        @change=\"onDateChange\"\r\n      >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplate\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"upload.headers\"\r\n            :disabled=\"upload.isUploading\"\r\n            :action=\"upload.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"importOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    <el-dialog\r\n      title=\"单位时间报表导出\"\r\n      :visible.sync=\"SpecialImportOpen\"\r\n      width=\"400px\"\r\n      append-to-body\r\n      destroy-on-close\r\n    >\r\n      <span>选择导出时间：</span>\r\n      <el-date-picker\r\n            v-model=\"specialFcDate\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            type=\"date\"\r\n            :default-value=\"new Date()\"\r\n            placeholder=\"选择时间\"\r\n          >\r\n      </el-date-picker>\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n       \r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            size=\"small\"\r\n            type=\"info\"\r\n            plain\r\n            icon=\"el-icon-link\"\r\n            @click=\"downloadTemplateSpecial\"\r\n            >数据下载</el-button\r\n          >\r\n        </el-col>\r\n         <el-col :span=\"1.5\" >\r\n          <el-upload\r\n            accept=\".xlsx, .xls\"\r\n            :headers=\"uploadSpecial.headers\"\r\n            :disabled=\"uploadSpecial.isUploading\"\r\n            :action=\"uploadSpecial.url\"\r\n            :show-file-list=\"false\"\r\n            :multiple=\"false\"\r\n            :on-progress=\"handleFileUploadProgress\"\r\n            :on-success=\"handleFileSuccess\"\r\n          >\r\n            <el-button size=\"small\" type=\"warning\" plain icon=\"el-icon-download\"\r\n              >表格导入</el-button\r\n            >\r\n          </el-upload>\r\n        </el-col>\r\n      </el-row>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"SpecialImportOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog  title=\"文件预览\"  :visible.sync=\"searchopen\" width=\"1800px\" >\r\n        <div class=\"test\">\r\n          <vue-office-excel\r\n              :src=\"customBlobContent\"\r\n              style=\"height: 100vh;\"\r\n          />\r\n        </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n  \r\n  <script>\r\nimport { answerListPlusAll,formFrequency } from \"@/api/tYjy/form\";\r\nimport answerInput from \"./input\";\r\nimport { newAdd, addAlone } from \"@/api/tYjy/answer\";\r\nimport { getAllRootListForAnswer } from \"@/api/tYjy/dimensionality\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport axios from \"axios\";\r\nimport * as xlsx from 'xlsx';\r\n\r\n//引入相关样式\r\n\r\nexport default {\r\n  name: \"Answer\",\r\n  components: { answerInput },\r\n  data() {\r\n    return {\r\n      pickerOptions: {\r\n        disabledDate(time) {\r\n          return time.getTime() > Date.now();\r\n        },\r\n      },\r\n      frequencyOptions: [],\r\n\r\n      queryParams: {\r\n        formQuestion: undefined,\r\n        fcDate: undefined,\r\n        dimensionalityId: undefined,\r\n        formQuestion: undefined,\r\n      },\r\n\r\n      formType: null,\r\n      dimensionalityNames: null,\r\n      drawerShow: false,\r\n      stickyTop: 0,\r\n      loading: false,\r\n      showSearch: true,\r\n      answerList: [],\r\n      formData: {},\r\n      row: {},\r\n      rootList: [],\r\n      userInfo: {},\r\n      datesave:{},\r\n      pathsave:{},\r\n      count:1,\r\n      deptName:null,\r\n      dimensionalityName:null,\r\n      dateValue: null,\r\n      specialFcDate:null,\r\n      queryImport: {\r\n        startDate: null,\r\n        endDate: null,\r\n        rootId: null,\r\n      },\r\n      importOpen:false,\r\n      SpecialImportOpen:false,\r\n      // 导入参数\r\n      upload: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importData\",\r\n      },\r\n\r\n\r\n      uploadSpecial: {\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url:\r\n          process.env.VUE_APP_BASE_API +\r\n          \"/web/TYjy/answer/importDataSpecial\",\r\n\r\n      },\r\n      excelHtml:\"\",\r\n      searchopen:false,\r\n      excelData: [], // 存储 Excel 数据\r\n      exceltitle: [],\r\n      customBlobContent:\"\"\r\n    };\r\n  },\r\n  mounted() {\r\n    this.userInfo = JSON.parse(JSON.stringify(this.$store.state.user));\r\n  },\r\n\r\n  created() {\r\n    const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    const fcDate = this.$route.query && this.$route.query.fcDate;\r\n    this.queryParams.dimensionalityId=dimensionalityId\r\n    this.queryParams.fcDate=fcDate\r\n    this.initData();\r\n\r\n\r\n    // if(this.$route.query)\r\n    // {\r\n    //   const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;\r\n    //   // const dimensionalityName = this.$route.query && this.$route.query.dimensionalityName;\r\n    //   const fcDate = this.$route.query && this.$route.query.fcDate;\r\n    //   this.queryParams.dimensionalityId=dimensionalityId\r\n    //   // this.queryParams.dimensionalityName=dimensionalityName\r\n    //   this.queryParams.fcDate=fcDate\r\n    //   this.initData1();\r\n    // }\r\n    // else\r\n    // {\r\n    //   this.initData();\r\n    // }\r\n  },\r\n  methods: {\r\n    onDateChange() {\r\n      console.log(this.dateValue);\r\n      if (this.dateValue != null && this.dateValue != \"\") {\r\n        this.queryImport.startDate = this.dateValue[0];\r\n        this.queryImport.endDate = this.dateValue[1];\r\n      } else {\r\n        this.queryImport.startDate = \"\";\r\n        this.queryImport.endDate = \"\";\r\n      }\r\n    },\r\n    clickNode($event, node) {\r\n      $event.target.parentElement.parentElement.firstElementChild.click();\r\n    },\r\n    changeEvent(params) {\r\n      const $form = this.$refs.formRef;\r\n      if ($form) {\r\n        $form.updateStatus(params);\r\n      }\r\n    },\r\n    disabledDate(time) {\r\n      return time.getTime() < Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天\r\n    },\r\n    inputChange(val, row) {\r\n      row.formValue = val;\r\n    },\r\n    handleScroll() {\r\n      this.isSticky = window.scrollY >= this.stickyTop;\r\n    },\r\n    initData() {\r\n      getAllRootListForAnswer().then((res) => {\r\n        this.rootList = res.data;\r\n        if(this.queryParams.dimensionalityId==null)\r\n        {\r\n          this.queryParams.dimensionalityId = this.rootList[0].value;\r\n          this.deptName= this.rootList[0].deptName;\r\n          this.deptCode= this.rootList[0].deptCode;\r\n          this.dimensionalityName=this.rootList[0].label\r\n        }\r\n        else\r\n        {\r\n          // this.queryParams.dimensionalityId = this.queryParams.dimensionalityId;\r\n          for(let i=0;i<this.rootList.length;i++)\r\n          {\r\n            if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n            {\r\n              this.queryParams.dimensionalityId = this.rootList[i].value;\r\n              this.deptName= this.rootList[i].deptName;\r\n              this.deptCode= this.rootList[i].deptCode;\r\n              this.dimensionalityName=this.rootList[i].label\r\n            }\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n    initData1() {\r\n      getAllRootListForAnswer().then((res) => {\r\n        this.rootList = res.data;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n          {\r\n            this.deptName= this.rootList[0].deptName;\r\n          }\r\n        }\r\n        this.getList();\r\n      });\r\n    },\r\n    containsSubstring(substring, string) {\r\n      return string.includes(substring);\r\n    },\r\n    aloneList(string) {\r\n      if(string== '气体结算月报')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '高炉、转炉煤气月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '天然气消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '蒸汽消耗月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '电量月报表')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '2025年经济责任制考核表（特板事业部）')\r\n      {\r\n        return true;\r\n      }\r\n      if(string== '水处理水量报表')\r\n      {\r\n        return true;\r\n      }\r\n      \r\n      // if(string== '研究院目标指标一览')\r\n      // {\r\n      //   return true;\r\n      // }\r\n      return false;\r\n    },\r\n\r\n    /** 查询TYjyAnswer列表 */\r\n    getList() {\r\n      formFrequency({dimensionalityId: this.queryParams.dimensionalityId}).then((res) => {\r\n           if(this.count!=res.data)\r\n           {\r\n            this.queryParams.fcDate=undefined\r\n           }\r\n           this.count=res.data\r\n      });\r\n\r\n      this.answerList = [];\r\n      answerListPlusAll({\r\n        fcDate: this.queryParams.fcDate,\r\n        dimensionalityId: this.queryParams.dimensionalityId,\r\n        formQuestion: this.queryParams.formQuestion,\r\n      }).then((res) => {\r\n        let answerList = [];\r\n        let list = res.data;\r\n        for(let i=0;i<this.rootList.length;i++)\r\n        {\r\n          if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n          {\r\n            this.queryParams.dimensionalityId = this.rootList[i].value;\r\n            this.deptName= this.rootList[i].deptName;\r\n            this.deptCode= this.rootList[i].deptCode;\r\n            this.dimensionalityName=this.rootList[i].label\r\n          }\r\n        }\r\n        if(this.containsSubstring('安全责任工资考核表',this.dimensionalityName))\r\n        {\r\n          console.log(\"test1\")\r\n          let num=0\r\n          for(let i=0;i<list.length;i++)\r\n          {\r\n            if(list[i].formQuestion!='自评总分' && list[i].formQuestion!='厂长评分')\r\n            {\r\n              num=1;\r\n              break;\r\n            }\r\n          }\r\n          if(num==0)\r\n          {\r\n            for(let i=0;i<list.length;i++)\r\n            {\r\n              list[i].dimensionalityName=list[i].dimensionalityName.replace('/七、考核评分', '')\r\n            }\r\n            console.log(\"test3\",list)\r\n          }\r\n        }\r\n        \r\n        for(let i=0;i<list.length;i++)\r\n        {\r\n            this.datesave[list[i].formId]=list[i].formValue\r\n            this.pathsave[list[i].dimensionalityName]=list[i].dimensionalityPath\r\n        }\r\n        // 使用 map 提取 dimensionalityName 属性到一个数组\r\n        let dimensionalityNames = list.map((x) => x.dimensionalityName);\r\n\r\n        // 提取 / 后的前三位字符，并与原字符串配对\r\n        dimensionalityNames = dimensionalityNames.map((name) => {\r\n          // let key = name.includes(\"/\") ? name.split(\"/\")[1].slice(0, 3) : \"\";\r\n          let key = this.pathsave[name];\r\n          return { originalName: name, sortKey: key };\r\n        });\r\n\r\n        // 按照提取出的前三字符排序\r\n        dimensionalityNames.sort((a, b) => a.sortKey.localeCompare(b.sortKey));\r\n        // console.log(\"test0\",dimensionalityNames)\r\n        // 如果需要，可以提取排序后的原始名字\r\n        dimensionalityNames = dimensionalityNames.map(\r\n          (item) => item.originalName\r\n        );\r\n\r\n        // 使用 Set 去重\r\n        let uniqueDimensionalityNames = [...new Set(dimensionalityNames)];\r\n\r\n        uniqueDimensionalityNames.forEach((title) => {\r\n          let group = {\r\n            title: \"\",\r\n            list: [],\r\n          };\r\n          group.title = title;\r\n          group.list = list.filter((item) => item.dimensionalityName === title);\r\n          // 假设你有一个数组来存储所有的组\r\n          answerList.push(group); // 将生成的组添加到groups数组中\r\n        });\r\n        this.answerList = answerList;\r\n        // console.log(\"test111\",answerList)\r\n        this.$forceUpdate();\r\n      });\r\n    },\r\n    handleQuery() {\r\n      for(let i=0;i<this.rootList.length;i++)\r\n      {\r\n        if(this.queryParams.dimensionalityId == this.rootList[i].value)\r\n        {\r\n          this.deptName= this.rootList[i].deptName;\r\n          this.deptCode= this.rootList[i].deptCode;\r\n          this.dimensionalityName=this.rootList[i].label\r\n        }\r\n      }\r\n      this.getList();\r\n    },\r\n    handlePreview() {\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.queryParams.fcDate\r\n      queryImport.type=\"0\"\r\n      if(this.dimensionalityName=='研究院目标指标一览')\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportWithTemplate\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n      else\r\n      {\r\n        this.downloadXlsx(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      ).then((blob) => {\r\n        let reader = new FileReader();\r\n        reader.readAsArrayBuffer(blob);\r\n        reader.onload = (evt) => {\r\n          this.customBlobContent=reader.result;\r\n          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array\r\n          ints = ints.slice(0, blob.size);\r\n          let workBook = xlsx.read(ints, { type: \"array\" });\r\n          let sheetNames = workBook.SheetNames;\r\n          let sheetName = sheetNames[0];\r\n          let workSheet = workBook.Sheets[sheetName];\r\n          //获取Excle内容，并将空内容用空值保存\r\n          let excelTable = xlsx.utils.sheet_to_json(workSheet);\r\n          // 获取Excel头部\r\n          let tableThead = Array.from(Object.keys(excelTable[0])).map(\r\n            (item) => {\r\n              return item\r\n            }\r\n          );\r\n          this.excelData = excelTable;\r\n          this.exceltitle=tableThead\r\n          this.excelHtml= excelTable\r\n          this.searchopen = true;\r\n        }\r\n      });\r\n      }\r\n    },\r\n    \r\n    handleDateChange() {\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.fcDate = undefined;\r\n      this.queryParams.formQuestion = undefined;\r\n      this.queryParams.dimensionalityId = undefined;\r\n      this.getList();\r\n    },\r\n    handleUpload({ file }) {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", file);\r\n      return axios\r\n        .post(\r\n          process.env.VUE_APP_BASE_API + \"/common/uploadMinioDataReport\",\r\n          formData\r\n        )\r\n        .then((res) => {\r\n          return {\r\n            ...res.data,\r\n          };\r\n        });\r\n    },\r\n    /** 提交按钮 */\r\n    handleSubmit() {\r\n      console.log(this.answerList);\r\n      console.log(\"test1\",this.datesave);\r\n      // 首先对 answerList 进行处理：合并、过滤和转换\r\n      let processedLists = this.answerList\r\n        .reduce((acc, current) => {\r\n          return acc.concat(current.list);\r\n        }, [])\r\n        .filter((x) => {\r\n          // 过滤条件\r\n          console.log(\"test1\",x.status);\r\n          console.log(this.datesave[x.formId]);\r\n          console.log(x.formValue);\r\n          return (\r\n            x.formValue != null &&\r\n            x.formValue != \"\" &&\r\n            ((![\"1\"].includes(x.status))&&\r\n            (\r\n              ([\"0\",\"2\",\"3\"].includes(x.status) && this.datesave[x.formId]!=x.formValue))\r\n              ||([\"4\"].includes(x.status))\r\n            )\r\n          );\r\n        });\r\n\r\n      // 对符合条件的元素进行 formValue 的转换\r\n      processedLists.forEach((x) => {\r\n        if ([\"0\", \"1\"].includes(x.formType)) {\r\n          x.formValue = parseFloat(x.formValue);\r\n        }\r\n        x.fcDate = this.queryParams.fcDate;\r\n      });\r\n\r\n      // 最后进行深拷贝\r\n      let allLists = JSON.parse(JSON.stringify(processedLists));\r\n\r\n      // console.log(\"allLists:\", allLists);\r\n      const now = new Date();\r\n      \r\n      let datestr=\"请确定是否要提交数据\"\r\n      if(this.queryParams.fcDate==null)\r\n      {\r\n        // 获取年月日\r\n        const year = now.getFullYear();\r\n        const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要+1\r\n        const day = String(now.getDate()).padStart(2, '0');\r\n\r\n        // 格式化为 yyyy年MM月dd日\r\n        const format1 = `${year}年${month}月${day}日`;\r\n\r\n        // 格式化为 yyyy年MM月\r\n        const format2 = `${year}年${month}月`;\r\n        \r\n        if(this.count=='1')\r\n        {\r\n          datestr='您未选择时间,请确定是否要提交'+format1+'的数据?'\r\n        }\r\n        else\r\n        {\r\n          datestr='您未选择时间,请确定是否要提交'+format2+'的数据?'\r\n        }\r\n      }\r\n      this.$confirm(\r\n        datestr,\r\n        {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n\r\n        }\r\n      ).then(()=>{\r\n        newAdd(allLists).then((res) => {\r\n        this.getList();\r\n        this.msgSuccess(\"保存成功\");\r\n      });\r\n      }).catch(()=>{});\r\n\r\n      // newAdd(allLists).then((res) => {\r\n      //   this.getList();\r\n      //   this.msgSuccess(\"保存成功\");\r\n      // });\r\n    },\r\n    \r\n    handleFileUploadProgress() {\r\n      this.upload.isUploading = true;\r\n    },\r\n    handleFileSuccess(response) {\r\n      console.log(response)\r\n      if (response.code == 200) {\r\n        this.$modal.msgSuccess(\"上传成功\");\r\n        this.getList();\r\n        this.importOpen = false;\r\n        this.SpecialImportOpen = false;\r\n      }\r\n      else {\r\n        this.$modal.msgError(\"上传失败\")\r\n      }\r\n      this.upload.isUploading = false;\r\n    },\r\n    // 模板下载\r\n    downloadTemplate(){\r\n    \r\n      if (\r\n        this.queryImport.startDate == null ||\r\n        this.queryImport.startDate == \"\"||\r\n        this.queryImport.endDate == null||\r\n        this.queryImport.endDate == \"\"\r\n      ) {\r\n        this.$notify.error({\r\n          title: \"错误\",\r\n          message: \"导出前请先输入开始结束时间\",\r\n        });\r\n        return;\r\n      }\r\n      this.queryImport.rootId = this.queryParams.dimensionalityId\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportTemplate\",\r\n        {\r\n          ...this.queryImport,\r\n        },\r\n        \"(\" +\r\n          this.queryImport.startDate +\r\n          \"-\" +\r\n          this.queryImport.endDate +\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n    \r\n    },\r\n\r\n        // 模板下载\r\n    downloadTemplateSpecial(){\r\n      if (this.specialFcDate == null ) {\r\n        this.specialFcDate= this.queryParams.fcDate\r\n      }\r\n\r\n      // if (\r\n      //   this.specialFcDate == null \r\n      // ) {\r\n      //   this.$notify.error({\r\n      //     title: \"错误\",\r\n      //     message: \"导出前请先输入开始结束时间\",\r\n      //   });\r\n      //   return;\r\n      // }\r\n      let queryImport={}\r\n      queryImport.rootId = this.queryParams.dimensionalityId\r\n      queryImport.fcDate = this.specialFcDate\r\n      queryImport.type=\"0\"\r\n      this.downloadFile(\r\n        \"/web/TYjy/answer/exportTemplateSpecial\",\r\n        {\r\n          ...queryImport,\r\n        },\r\n        this.dimensionalityName+\"(\" +this.specialFcDate+\r\n          \")\" +\r\n          `数据.xlsx`\r\n      );\r\n  \r\n    },\r\n\r\n  },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n.test{\r\n  height: 100vh;\r\n}\r\n\r\n.excel-preview {\r\n  margin-top: 20px;\r\n  overflow: auto;\r\n  max-height: 500px;\r\n  border: 1px solid #ddd;\r\n  padding: 10px;\r\n}\r\n\r\n/* 设置滚动条的样式 */\r\n::-webkit-scrollbar {\r\n  width: 10px;\r\n  /* 竖向滚动条宽度 */\r\n  height: 15px;\r\n  /* 横向滚动条宽度 */\r\n  background-color: #ffffff;\r\n}\r\n\r\n/* 滚动槽 */\r\n::-webkit-scrollbar-track {\r\n  /* 其实直接在  ::-webkit-scrollbar 中设置也能达到同样的视觉效果*/\r\n  /* -webkit-box-shadow: inset 0 0 6px rgba(177, 223, 117, 0.7); */\r\n  background-color: #e4e4e4;\r\n  border-radius: 10px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 5px;\r\n  -webkit-box-shadow: inset 0 0 6px rgba(158, 156, 156, 0.616);\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(139, 138, 138, 0.616);\r\n  -webkit-box-shadow: unset;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:window-inactive {\r\n  /* 容器不被激活时的样式 */\r\n  background: #bdbdbd66;\r\n}\r\n\r\n::-webkit-scrollbar-corner {\r\n  /* 两个滚动条交汇处边角的样式 */\r\n  background-color: #cacaca66;\r\n}\r\n\r\n.sticky {\r\n  padding: 20px;\r\n  position: -webkit-sticky;\r\n  position: sticky;\r\n  top: 0; /* 粘性定位的起始位置 */\r\n  z-index: 100; /* 确保按钮在卡片之上 */\r\n}\r\n.el-tabs--card {\r\n  height: calc(100vh - 110px);\r\n}\r\n.el-tab-pane {\r\n  height: calc(100vh - 110px);\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n  "]}]}