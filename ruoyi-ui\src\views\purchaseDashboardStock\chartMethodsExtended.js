import * as echarts from 'echarts'

export default {
  methods: {
    // 物料词云
    initMaterialCloud() {
      console.log('initMaterialCloud started with data:', JSON.stringify(this.highFrequencyMaterialData))

      const chartDom = document.getElementById('materialCloudChart')
      if (!chartDom) {
        console.error('Cannot find materialCloudChart DOM element')
        return
      }

      chartDom.innerHTML = ''

      const rawMaterialList = this.highFrequencyMaterialData
      if (!rawMaterialList || rawMaterialList.length === 0) {
        chartDom.innerHTML = '<div class="chart-placeholder">无高频采购物料数据</div>'
        console.log('No high frequency materials to display for materialCloudChart.')
        return
      }

      // 按入库金额排序，而不是入库数量
      const highFrequencyMaterials = rawMaterialList.sort((a, b) => (b.inAmt || 0) - (a.inAmt || 0))

      const container = document.createElement('div')
      container.style.width = '100%'
      container.style.height = '100%'
      container.style.position = 'relative'
      container.style.overflow = 'hidden'

      const colors = [
        '#4fc3f7', '#a5d6a7', '#ffcc80', '#ef9a9a', '#ce93d8',
        '#90caf9', '#80deea', '#c5e1a5', '#fff59d', '#ffab91'
      ]

      const maxFontSize = 50
      const minFontSize = 12

      highFrequencyMaterials.forEach((item, index) => {
        let fontSize = maxFontSize - (index * 2)
        if (fontSize < minFontSize) {
          fontSize = minFontSize
        }

        const div = document.createElement('div')
        div.textContent = item.itemName
        div.style.position = 'absolute'
        div.style.fontSize = `${fontSize}px`
        div.style.fontWeight = 'bold'
        div.style.color = colors[index % colors.length]
        div.style.transform = `rotate(${Math.random() * 50 - 25}deg)`
        div.style.left = `${5 + Math.random() * 70}%`
        div.style.top = `${5 + Math.random() * 70}%`
        div.style.whiteSpace = 'nowrap'
        div.style.textShadow = '1px 1px 3px rgba(0,0,0,0.3)'
        div.style.transition = 'all 0.3s ease'
        div.style.cursor = 'pointer'
        div.style.zIndex = highFrequencyMaterials.length - index

        div.addEventListener('mouseover', function() {
          this.style.transform = `rotate(0deg) scale(1.3)`
          this.style.zIndex = (highFrequencyMaterials.length * 2).toString()
          this.style.textShadow = '2px 2px 5px rgba(0,0,0,0.5)'

          const tooltip = document.createElement('div')
          // 显示入库金额而不是入库数量
          tooltip.textContent = `入库金额: ${(item.inAmt || 0).toFixed(2)} 万元`
          tooltip.style.position = 'absolute'
          tooltip.style.backgroundColor = 'rgba(0,0,0,0.7)'
          tooltip.style.color = '#fff'
          tooltip.style.padding = '3px 8px'
          tooltip.style.borderRadius = '4px'
          tooltip.style.fontSize = '12px'
          tooltip.style.top = 'calc(100% + 5px)'
          tooltip.style.left = '50%'
          tooltip.style.transform = 'translateX(-50%)'
          tooltip.style.whiteSpace = 'nowrap'
          tooltip.style.zIndex = (highFrequencyMaterials.length * 2 + 1).toString()
          tooltip.className = 'material-tooltip'
          this.appendChild(tooltip)
        })

        div.addEventListener('mouseout', function() {
          this.style.transform = `rotate(${Math.random() * 50 - 25}deg) scale(1)`
          this.style.zIndex = (highFrequencyMaterials.length - index).toString()
          this.style.textShadow = '1px 1px 3px rgba(0,0,0,0.3)'

          const tooltip = this.querySelector('.material-tooltip')
          if (tooltip) {
            this.removeChild(tooltip)
          }
        })

        container.appendChild(div)
      })

      chartDom.appendChild(container)
      console.log(`Material cloud chart updated with ${highFrequencyMaterials.length} items.`)
    },

    // TOP供应商图表
    async initTopSuppliersChart() {
      console.log('initTopSuppliersChart started')
      const myChart = this.reinitChart('topSuppliersChart')
      if (!myChart) return

      // 使用API获取数据，传递orderType参数
      myChart.showLoading()
      try {
        const response = await this.getSupplierList({
          dimensionType: this.currentDimensionType,
          orderType: this.selectedOrderType
        })
        
        let supplierData = []
        if (response && response.data && Array.isArray(response.data)) {
          supplierData = response.data
        } else {
          console.error('从showSuppList API获取的数据无效:', response)
          // 如果API失败，使用仪表板原始数据作为备用
          supplierData = this.dashboardData.purchaseProviderTotalAmountList || []
        }
        
        this.originalTopSuppliersData = [...supplierData]
        this.renderAndPaginateTopSuppliers(myChart, supplierData)
      } catch (error) {
        console.error('初始化TOP供应商图表失败:', error)
        // 如果API失败，使用仪表板原始数据作为备用
        this.originalTopSuppliersData = this.dashboardData.purchaseProviderTotalAmountList 
          ? [...this.dashboardData.purchaseProviderTotalAmountList] 
          : []
        this.renderAndPaginateTopSuppliers(myChart, this.originalTopSuppliersData)
      } finally {
        myChart.hideLoading()
      }
    },

    renderAndPaginateTopSuppliers(chartInstance, suppliersDataToRender) {
      if (!chartInstance) return

      if (chartInstance.intervalId) {
        clearInterval(chartInstance.intervalId)
        chartInstance.intervalId = null
      }

      const suppliersData = suppliersDataToRender.sort((a, b) => b.ddze - a.ddze)

      let currentIndex = 0
      const groupSize = 10

      const updateChart = () => {
        if (suppliersData.length === 0) {
          chartInstance.setOption({
            title: {
              text: '暂无数据',
              left: 'center',
              top: 'center',
              textStyle: { color: 'rgba(255,255,255,0.7)' }
            },
            xAxis: { show: false },
            yAxis: { show: false },
            series: []
          }, true)
          return
        }

        const endIndex = Math.min(currentIndex + groupSize, suppliersData.length)
        let displayData = suppliersData.slice(currentIndex, endIndex)
        displayData = displayData.reverse()

        let maxDdze = 0
        if (displayData.length > 0) {
          maxDdze = Math.max(...displayData.map(item => parseFloat(item.ddze) || 0))
        }

        let unit = '万元'
        let valueDiv = 1
        let fractionDigits = 1

        if (maxDdze > 100000) {
          unit = '亿元'
          valueDiv = 10000
          fractionDigits = 2
        }

        const colors = ['#83bff6', '#ea7ccc', '#5fd8b6', '#ffb980', '#e879ed', '#ffdb5c', '#ff9a9e', '#a18cd1', '#fbc2eb', '#84fab0']

        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: function(params) {
              const item = params[0]
              const originalValue = parseFloat(item.value)
              return `${item.name}: ${originalValue.toLocaleString(undefined, { minimumFractionDigits: fractionDigits, maximumFractionDigits: fractionDigits })}${unit}`
            }
          },
          grid: {
            left: '3%',
            right: '15%',
            bottom: '3%',
            top: '3%',
            containLabel: true
          },
          xAxis: {
            show: true,
            type: 'value',
            axisLabel: {
              color: '#eee',
              formatter: function(value) {
                return parseFloat(value).toFixed(0) + unit
              }
            },
            axisLine: {
              lineStyle: {
                color: '#eee'
              }
            },
            splitLine: {
              show: false
            }
          },
          yAxis: {
            show: true,
            type: 'category',
            data: displayData.map(item => {
              const maxLength = 36
              let name = item.suppName
              if (name.length > maxLength) {
                name = name.substring(0, maxLength - 3) + "..."
              }
              return name
            }),
            axisLabel: {
              color: '#eee',
              interval: 0
            },
            axisLine: {
              lineStyle: {
                color: '#eee'
              }
            }
          },
          series: [
            {
              name: '供货金额',
              type: 'bar',
              data: displayData.map((item, index) => {
                return {
                  value: (parseFloat(item.ddze) || 0) / valueDiv,
                  itemStyle: {
                    color: colors[index % colors.length],
                    borderRadius: [0, 5, 5, 0]
                  }
                }
              }),
              label: {
                show: true,
                position: 'right',
                formatter: function(params) {
                  return parseFloat(params.value).toLocaleString(undefined, { minimumFractionDigits: fractionDigits, maximumFractionDigits: fractionDigits }) + ' ' + unit
                },
                color: '#fff'
              }
            }
          ]
        }
        chartInstance.setOption(option, true)
      }

      updateChart()

      if (suppliersData.length > groupSize) {
        chartInstance.intervalId = setInterval(() => {
          currentIndex += groupSize
          if (currentIndex >= suppliersData.length) {
            currentIndex = 0
          }
          updateChart()
        }, 15000)
      }
    },

    // TOP10供应商风险提醒图表
    initSupplierRiskChart() {
      console.log('initSupplierRiskChart started with data:', JSON.stringify(this.supplierRiskData))
      const myChart = this.reinitChart('supplierRiskChart')
      if (!myChart) return

      const riskData = this.supplierRiskData || []

      const top10RiskSuppliers = riskData
        .sort((a, b) => b.riskNum - a.riskNum)
        .slice(0, 10)
        .reverse()

      if (top10RiskSuppliers.length === 0) {
        document.getElementById('supplierRiskChart').innerHTML = '<div class="chart-placeholder">无供应商风险数据</div>'
        return
      }

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function (params) {
            const item = params[0]
            return `${item.name}: ${item.value}<br/><span style="color: #00d4ff;">点击查看详情</span>`
          }
        },
        grid: {
          left: '3%',
          right: '10%',
          bottom: '3%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            color: '#eee',
            formatter: '{value}'
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: top10RiskSuppliers.map(item => item.suppName),
          axisLabel: {
            color: '#eee',
            interval: 0
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          }
        },
        series: [
          {
            name: '风险数量',
            type: 'bar',
            data: top10RiskSuppliers.map((item, index) => {
              let barColor
              if (index >= 7) {
                barColor = '#FF4136'
              } else if (index >= 3) {
                barColor = '#FF851B'
              } else {
                barColor = '#FFDC00'
              }

              return {
                value: item.riskNum,
                suppId: item.suppId, // 保存供应商ID用于点击跳转
                itemStyle: {
                  color: barColor,
                  borderRadius: [0, 5, 5, 0]
                },
                emphasis: {
                  itemStyle: {
                    color: barColor,
                    borderRadius: [0, 5, 5, 0],
                    shadowBlur: 10,
                    shadowColor: 'rgba(255, 255, 255, 0.5)',
                    borderWidth: 2,
                    borderColor: '#fff'
                  }
                }
              }
            }),
            label: {
              show: true,
              position: 'right',
              formatter: '{c}',
              color: '#fff'
            }
          }
        ]
      }
      
      // 添加点击事件监听器
      myChart.off('click') // 先移除之前的点击事件
      myChart.on('click', function(params) {
        if (params.componentType === 'series' && params.seriesType === 'bar') {
          const suppId = params.data.suppId
          if (suppId) {
            // 获取当前页面URL并替换路径
            const currentUrl = window.location.href
            const baseUrl = currentUrl.replace('purchaseDashboard', 'supplierCertificate')
            
            // 时间维度映射：采购看板的currentDimensionType → 供应商证书页面的dimensionType
            const dimensionTypeMapping = {
              1: 3,  // 近三个月：看板值1 → 证书页面值3
              2: 6,  // 近六个月：看板值2 → 证书页面值6  
              3: 12  // 近一年：看板值3 → 证书页面值12
            }
            const mappedDimensionType = dimensionTypeMapping[this.currentDimensionType] || 12
            
            // 构建完整的跳转URL，包含supplierId和mappedDimensionType参数
            const url = `${baseUrl}?supplierId=${suppId}&dimensionType=${mappedDimensionType}`
            console.log('跳转到供应商详情页面:', url)
            console.log('跳转参数:', { 
              supplierId: suppId, 
              原始dimensionType: this.currentDimensionType,
              映射后dimensionType: mappedDimensionType 
            })
            
            // 在新窗口中打开页面
            window.open(url, '_blank')
          } else {
            console.warn('供应商ID不存在，无法跳转')
          }
        }
      }.bind(this))
      
      myChart.setOption(option, true)
      
      // 设置鼠标样式，表示可点击
      myChart.getZr().on('mousemove', function(params) {
        const pointInPixel = [params.offsetX, params.offsetY]
        if (myChart.containPixel('grid', pointInPixel)) {
          myChart.getZr().setCursorStyle('pointer')
        } else {
          myChart.getZr().setCursorStyle('default')
        }
      })
    },

    // 采购价格趋势图（旧版本）
    initPriceTrendChart() {
      const myChart = this.reinitChart('priceTrendChart')
      if (!myChart) return

      // 使用新的数据结构
      const priceAndStoreData = this.priceAndStoreData
      if (!priceAndStoreData) {
        document.getElementById('priceTrendChart').innerHTML = '<div class="chart-placeholder">暂无价格趋势数据</div>'
        return
      }

      const formatDate = (dateStr) => {
        const year = dateStr.substring(0, 4)
        const month = dateStr.substring(4, 6)
        const day = dateStr.substring(6, 8)
        return `${year}年${month}月${day}日`
      }

      // 收集所有日期
      let allDates = new Set()
      
      // 从价格数据中收集日期
      if (priceAndStoreData.procurementPriceVoList) {
        priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {
          if (priceGroup.priceList) {
            priceGroup.priceList.forEach(item => {
              allDates.add(item.recordDate)
            })
          }
        })
      }

      // 从采购量数据中收集日期
      if (priceAndStoreData.procurementPurchaseAmountVoList) {
        priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {
          if (amountGroup.amountList) {
            amountGroup.amountList.forEach(item => {
              allDates.add(item.recordDate)
            })
          }
        })
      }

      // 转换为排序的数组
      allDates = Array.from(allDates).sort()
      const xAxisData = allDates.map(formatDate)

      if (allDates.length === 0) {
        document.getElementById('priceTrendChart').innerHTML = '<div class="chart-placeholder">暂无价格趋势数据</div>'
        return
      }

      // 构建系列数据
      const series = []
      const legendData = []
      const colors = ['#8fe9ff', '#ff9f7f', '#5fd8b6', '#ffb980', '#e879ed', '#ffdb5c', '#83bff6', '#ea7ccc']
      let colorIndex = 0

      // 构建价格系列
      if (priceAndStoreData.procurementPriceVoList) {
        priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {
          const priceData = allDates.map(date => {
            const found = priceGroup.priceList.find(item => item.recordDate === date)
            return found ? parseFloat(found.price) : null
          })

          series.push({
            name: priceGroup.priceName,
            type: 'line',
            yAxisIndex: 0,
            data: priceData,
            smooth: true,
            lineStyle: {
              width: 3,
              color: colors[colorIndex % colors.length]
            },
            itemStyle: {
              color: colors[colorIndex % colors.length]
            },
            symbol: 'circle',
            symbolSize: 6,
            connectNulls: true
          })
          
          legendData.push(priceGroup.priceName)
          colorIndex++
        })
      }

      // 构建采购量系列
      if (priceAndStoreData.procurementPurchaseAmountVoList) {
        priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {
          const amountData = allDates.map(date => {
            const found = amountGroup.amountList.find(item => item.recordDate === date)
            return found ? parseFloat(found.amount) : null
          })

          series.push({
            name: amountGroup.amountName,
            type: 'line',
            yAxisIndex: 1,
            data: amountData,
            smooth: true,
            lineStyle: {
              width: 3,
              color: colors[colorIndex % colors.length]
            },
            itemStyle: {
              color: colors[colorIndex % colors.length]
            },
            symbol: 'circle',
            symbolSize: 6,
            connectNulls: true
          })
          
          legendData.push(amountGroup.amountName)
          colorIndex++
        })
      }

             // 计算Y轴范围
       let priceMin, priceMax, amountMin, amountMax
       
       // 计算价格轴范围（左轴）
       const priceValues = series.filter(s => s.yAxisIndex === 0)
         .flatMap(s => s.data.filter(v => v !== null && v !== undefined))
       if (priceValues.length > 0) {
         priceMin = Math.min(...priceValues)
         priceMax = Math.max(...priceValues)
       }
       
       // 计算采购量轴范围（右轴）
       const amountValues = series.filter(s => s.yAxisIndex === 1)
         .flatMap(s => s.data.filter(v => v !== null && v !== undefined))
       if (amountValues.length > 0) {
         amountMin = Math.min(...amountValues)
         amountMax = Math.max(...amountValues)
       }

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let str = params[0].axisValueLabel + '<br/>'
            params.forEach(item => {
              if (item.value !== null && item.value !== undefined) {
                if (item.seriesName.includes('价格') || item.seriesName.includes('价')) {
                  str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`
                } else {
                  str += `${item.marker}${item.seriesName}: ${parseFloat(item.value).toFixed(2)} 万吨<br/>`
                }
              } else {
                str += `${item.marker}${item.seriesName}: -<br/>`
              }
            })
            return str
          }
        },
        legend: {
          data: legendData,
          textStyle: {
            color: '#fff'
          },
          top: '5%'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '12%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            color: '#eee',
            interval: function(index, value) {
              // 计算所有月份，确保均匀分布显示标签
              if (index >= allDates.length || !allDates.length) return false
              
              // 获取所有不重复的月份
              const uniqueMonths = new Set()
              allDates.forEach(dateStr => {
                const year = dateStr.substring(0, 4)
                const month = dateStr.substring(4, 6)
                uniqueMonths.add(`${year}${month}`)
              })
              
              const monthsCount = uniqueMonths.size
              if (monthsCount <= 1) return true
              
              // 计算间隔，确保标签均匀分布
              const totalDataPoints = allDates.length
              const idealInterval = Math.floor(totalDataPoints / Math.min(monthsCount, 8)) // 最多显示8个标签
              
              return index % Math.max(idealInterval, 1) === 0
            },
            formatter: function(value, index) {
              // 格式化为 2026.6 的形式（去掉.1）
              if (index >= allDates.length) return ''
              const originalDateStr = allDates[index]
              if (!originalDateStr) return ''
              
              const year = originalDateStr.substring(0, 4)
              const month = parseInt(originalDateStr.substring(4, 6))
              return `${year}.${month}`
            }
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '价格（元/吨）',
            position: 'left',
            min: priceMin,
            max: priceMax,
            axisLine: {
              lineStyle: {
                color: '#8fe9ff'
              }
            },
            axisLabel: {
              color: '#eee'
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255,255,255,0.1)'
              }
            }
          },
          {
            type: 'value',
            name: '采购量 (万吨)',
            position: 'right',
            min: amountMin,
            max: amountMax,
            axisLine: {
              lineStyle: {
                color: '#ff9f7f'
              }
            },
            axisLabel: {
              color: '#eee',
              formatter: function(value) {
                return parseFloat(value).toFixed(0)
              }
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: series
      }

      myChart.setOption(option, true)
    },

    // 新的采购价格趋势图
    initNewPriceTrendChart() {
      const myChart = this.reinitChart('priceTrendChart')
      if (!myChart) return

      // 使用新的数据结构
      const newPriceAndStoreDataList = this.newPriceAndStoreData
      if (!newPriceAndStoreDataList || !Array.isArray(newPriceAndStoreDataList) || newPriceAndStoreDataList.length === 0) {
        document.getElementById('priceTrendChart').innerHTML = '<div class="chart-placeholder">暂无价格趋势数据</div>'
        return
      }

      const formatDate = (dateStr) => {
        const year = dateStr.substring(0, 4)
        const month = dateStr.substring(4, 6)
        const day = dateStr.substring(6, 8)
        return `${year}年${month}月${day}日`
      }

      // 收集所有日期
      let allDates = new Set()
      
      // 从所有物料的数据中收集日期
      newPriceAndStoreDataList.forEach(materialData => {
        // 从价格数据中收集日期
        if (materialData.procurementPriceVoList) {
          materialData.procurementPriceVoList.forEach(priceGroup => {
            if (priceGroup.priceList) {
              priceGroup.priceList.forEach(item => {
                allDates.add(item.recordDate)
              })
            }
          })
        }

        // 从采购量数据中收集日期
        if (materialData.procurementPurchaseAmountVoList) {
          materialData.procurementPurchaseAmountVoList.forEach(amountGroup => {
            if (amountGroup.amountList) {
              amountGroup.amountList.forEach(item => {
                allDates.add(item.recordDate)
              })
            }
          })
        }
      })

      // 转换为排序的数组
      allDates = Array.from(allDates).sort()
      const xAxisData = allDates.map(formatDate)

      if (allDates.length === 0) {
        document.getElementById('priceTrendChart').innerHTML = '<div class="chart-placeholder">暂无价格趋势数据</div>'
        return
      }

      // 构建系列数据
      const series = []
      const legendData = []
      const colors = ['#8fe9ff', '#ff9f7f', '#5fd8b6', '#ffb980', '#e879ed', '#ffdb5c', '#83bff6', '#ea7ccc', '#a18cd1', '#fbc2eb']
      let colorIndex = 0

      // 遍历每个物料的数据
      newPriceAndStoreDataList.forEach(materialData => {
        const materialName = materialData.itemName

        // 构建价格系列
        if (materialData.procurementPriceVoList) {
          materialData.procurementPriceVoList.forEach(priceGroup => {
            const priceData = allDates.map(date => {
              const found = priceGroup.priceList.find(item => item.recordDate === date)
              return found ? parseFloat(found.price) : null
            })

            series.push({
              name: `${materialName}-${priceGroup.priceName}`,
              type: 'line',
              yAxisIndex: 0,
              data: priceData,
              smooth: true,
              lineStyle: {
                width: 3,
                color: colors[colorIndex % colors.length]
              },
              itemStyle: {
                color: colors[colorIndex % colors.length]
              },
              symbol: 'circle',
              symbolSize: 6,
              connectNulls: true
            })
            
            legendData.push(`${materialName}-${priceGroup.priceName}`)
            colorIndex++
          })
        }

        // 构建采购量系列
        if (materialData.procurementPurchaseAmountVoList) {
          materialData.procurementPurchaseAmountVoList.forEach(amountGroup => {
            const amountData = allDates.map(date => {
              const found = amountGroup.amountList.find(item => item.recordDate === date)
              return found ? parseFloat(found.amount) : null
            })

            series.push({
              name: `${materialName}-${amountGroup.amountName}`,
              type: 'line',
              yAxisIndex: 1,
              data: amountData,
              smooth: true,
              lineStyle: {
                width: 3,
                color: colors[colorIndex % colors.length]
              },
              itemStyle: {
                color: colors[colorIndex % colors.length]
              },
              symbol: 'circle',
              symbolSize: 6,
              connectNulls: true
            })
            
            legendData.push(`${materialName}-${amountGroup.amountName}`)
            colorIndex++
          })
        }
      })

      // 计算Y轴范围
      let priceMin, priceMax, amountMin, amountMax
      
      // 计算价格轴范围（左轴）
      const priceValues = series.filter(s => s.yAxisIndex === 0)
        .flatMap(s => s.data.filter(v => v !== null && v !== undefined))
      if (priceValues.length > 0) {
        priceMin = Math.min(...priceValues)
        priceMax = Math.max(...priceValues)
      }
      
      // 计算采购量轴范围（右轴）
      const amountValues = series.filter(s => s.yAxisIndex === 1)
        .flatMap(s => s.data.filter(v => v !== null && v !== undefined))
      if (amountValues.length > 0) {
        amountMin = Math.min(...amountValues)
        amountMax = Math.max(...amountValues)
      }

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let str = params[0].axisValueLabel + '<br/>'
            params.forEach(item => {
              if (item.value !== null && item.value !== undefined) {
                if (item.seriesName.includes('价格') || item.seriesName.includes('价')) {
                  str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`
                } else {
                  str += `${item.marker}${item.seriesName}: ${parseFloat(item.value).toFixed(2)} 吨<br/>`
                }
              } else {
                str += `${item.marker}${item.seriesName}: -<br/>`
              }
            })
            return str
          }
        },
        legend: {
          data: legendData,
          textStyle: {
            color: '#fff'
          },
          top: '5%',
          type: 'scroll',
          pageButtonItemGap: 5,
          pageButtonGap: 30,
          pageButtonPosition: 'end',
          pageFormatter: '{current}/{total}',
          pageIconColor: '#2ec7c9',
          pageIconInactiveColor: '#aaa',
          pageIconSize: 15,
          pageTextStyle: {
            color: '#fff'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '12%',
          top: '25%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            color: '#eee',
            interval: function(index, value) {
              // 计算所有月份，确保均匀分布显示标签
              if (index >= allDates.length || !allDates.length) return false
              
              // 获取所有不重复的月份
              const uniqueMonths = new Set()
              allDates.forEach(dateStr => {
                const year = dateStr.substring(0, 4)
                const month = dateStr.substring(4, 6)
                uniqueMonths.add(`${year}${month}`)
              })
              
              const monthsCount = uniqueMonths.size
              if (monthsCount <= 1) return true
              
              // 计算间隔，确保标签均匀分布
              const totalDataPoints = allDates.length
              const idealInterval = Math.floor(totalDataPoints / Math.min(monthsCount, 8)) // 最多显示8个标签
              
              return index % Math.max(idealInterval, 1) === 0
            },
            formatter: function(value, index) {
              // 格式化为 2026.6 的形式（去掉.1）
              if (index >= allDates.length) return ''
              const originalDateStr = allDates[index]
              if (!originalDateStr) return ''
              
              const year = originalDateStr.substring(0, 4)
              const month = parseInt(originalDateStr.substring(4, 6))
              return `${year}.${month}`
            }
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '价格（元/吨）',
            position: 'left',
            min: priceMin,
            max: priceMax,
            axisLine: {
              lineStyle: {
                color: '#8fe9ff'
              }
            },
            axisLabel: {
              color: '#eee'
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255,255,255,0.1)'
              }
            }
          },
          {
            type: 'value',
            name: '采购量 (吨)',
            position: 'right',
            min: amountMin,
            max: amountMax,
            axisLine: {
              lineStyle: {
                color: '#ff9f7f'
              }
            },
            axisLabel: {
              color: '#eee',
              formatter: function(value) {
                return parseFloat(value).toFixed(0)
              }
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: series
      }

      myChart.setOption(option, true)
    }
  }
} 