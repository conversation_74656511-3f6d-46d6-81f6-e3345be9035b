{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardStock\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardStock\\index.vue", "mtime": 1756456493859}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQppbXBvcnQgY2hhcnRNZXRob2RzIGZyb20gJy4vY2hhcnRNZXRob2RzJw0KaW1wb3J0IGV4dGVuZGVkQ2hhcnRNZXRob2RzIGZyb20gJy4vY2hhcnRNZXRob2RzRXh0ZW5kZWQnDQppbXBvcnQgc2NyZWVuZnVsbCBmcm9tICdzY3JlZW5mdWxsJw0KaW1wb3J0IHsgc2hvd1llYXJseUFtb3VudCwgc2hvd1JlYWxUaW1lQW1vdW50LCBzaG93Q29raW5nQ29hbEFtb3VudCwgc2hvd0tleUluZGljYXRvcnMsIHNob3dJdGVtVHlwZUxpc3QsIHNob3dNYXRlcmlhbExpc3QsIHNob3dEYXRhLCBzaG93U3VwcExpc3QsIHNob3dIaWdoRnJlcXVlbmN5TWF0ZXJpYWxMaXN0LCBzaG93UHVyY2hhc2VTdXBwUmlzaywgZ2V0TWF0ZXJpYWxGdXR1cmVQcmljZSwgZ2V0TWF0ZXJpYWxOYW1lTGlzdCwgZ2V0UHVyY2hhc2VQcmljZUFuZFN0b3JlLCBnZXRNYXRlcmlhbE5hbWVMaXN0RnJvbU5ld1RhYmxlcywgZ2V0UHVyY2hhc2VQcmljZUFuZFN0b3JlRnJvbU5ld1RhYmxlcyB9IGZyb20gJ0AvYXBpL3B1cmNoYXNlRGFzaGJvYXJkL3B1cmNoYXNlRGFzaGJvYXJkJw0KaW1wb3J0IHsgbGlzdFNpbWlsYXJCeUl0ZW1OYW1lcyB9IGZyb20gJ0AvYXBpL3B1cmNoYXNlL3NpbWlsYXInDQppbXBvcnQgeyBnZXREZXBOYW1lTGlzdCwgZ2V0TGlzdE1vbnRobHkgfSBmcm9tICdAL2FwaS9wdXJjaGFzZS9wdXJkY2hhc2VGYWN0b3J5U3RvY2snDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1B1cmNoYXNlRGFzaGJvYXJkJywNCiAgbWl4aW5zOiBbY2hhcnRNZXRob2RzLCBleHRlbmRlZENoYXJ0TWV0aG9kc10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOaXtumXtOi/h+a7pOWZqOmAiemhuQ0KICAgICAgdGltZUZpbHRlcnM6IFsNCiAgICAgICAgeyBpZDogJ2ZpbHRlci0zbScsIGxhYmVsOiAn6L+R5LiJ5Liq5pyIJywgdmFsdWU6IDEgfSwNCiAgICAgICAgeyBpZDogJ2ZpbHRlci02bScsIGxhYmVsOiAn6L+R5YWt5Liq5pyIJywgdmFsdWU6IDIgfSwNCiAgICAgICAgeyBpZDogJ2ZpbHRlci0xeScsIGxhYmVsOiAn6L+R5LiA5bm0JywgdmFsdWU6IDMgfQ0KICAgICAgXSwNCiAgICAgIGFjdGl2ZUZpbHRlcjogJ2ZpbHRlci0xeScsDQogICAgICBjdXJyZW50RGltZW5zaW9uVHlwZTogMywNCg0KICAgICAgLy8g5pWw5o2uDQogICAgICBkYXNoYm9hcmREYXRhOiB7fSwNCiAgICAgIHB1cmNoYXNlU3RhdHM6IHt9LA0KDQogICAgICAvLyDkuIvmi4npgInpobkNCiAgICAgIHRvcFN1cHBsaWVyc09wdGlvbnM6IFtdLA0KDQogICAgICAvLyDpgInkuK3nmoTov4fmu6TlmajlgLwNCiAgICAgIHNlbGVjdGVkVG9wU3VwcGxpZXJzRmlsdGVyOiAnJywNCiAgICAgIHNlbGVjdGVkT3JkZXJUeXBlOiAnVE9QJywgLy8g5o6S5bqP57G75Z6L77yM6buY6K6k5Li6VE9QDQoNCiAgICAgIC8vIOWbvuihqOWunuS+iw0KICAgICAgY2hhcnRJbnN0YW5jZXM6IHt9LA0KDQogICAgICAvLyDljp/lp4vmlbDmja7lpIfku70NCiAgICAgIG9yaWdpbmFsVG9wU3VwcGxpZXJzRGF0YTogW10sDQoNCiAgICAgIC8vIOW6k+WtmOWbvuihqOebuOWFsw0KICAgICAgc2VsZWN0ZWRZZWFyOiAnJywNCiAgICAgIHNlbGVjdGVkTWF0ZXJpYWxUeXBlOiAnJywNCiAgICAgIGF2YWlsYWJsZVllYXJzOiAoKCkgPT4gew0KICAgICAgICBjb25zdCBjdXJyZW50WWVhciA9IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKQ0KICAgICAgICBjb25zdCB5ZWFycyA9IFtdDQogICAgICAgIGZvciAobGV0IHllYXIgPSAyMDIwOyB5ZWFyIDw9IGN1cnJlbnRZZWFyOyB5ZWFyKyspIHsNCiAgICAgICAgICB5ZWFycy5wdXNoKHllYXIudG9TdHJpbmcoKSkNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4geWVhcnMNCiAgICAgIH0pKCksDQogICAgICB5ZWFybHlJbnZlbnRvcnlEYXRhOiBbXSwNCiAgICAgIHJlYWxUaW1lSW52ZW50b3J5RGF0YTogW10sDQogICAgICBjb2tpbmdDb2FsSW52ZW50b3J5RGF0YTogW10sDQoNCiAgICAgIC8vIOefv+eEpueFpOW6k+WtmOWbvuihqOebuOWFsw0KICAgICAgc2VsZWN0ZWRDb2tpbmdDb2FsVHlwZTogJycsIC8vIOmAieS4reeahOefv+eEpueFpOexu+Wei++8jOm7mOiupOS4uuepuu+8iOWFqOmDqO+8iQ0KDQogICAgICAvLyDnianmlpnlhaXlupPnu5/orqHnm7jlhbMNCiAgICAgIHNlbGVjdGVkTWF0ZXJpYWxDYXRlZ29yeTogJzEnLA0KICAgICAgc2VsZWN0ZWRNYXRlcmlhbEl0ZW06ICcnLA0KICAgICAgbWF0ZXJpYWxJdGVtT3B0aW9uczogW10sDQogICAgICBtYXRlcmlhbFN0YXRpc3RpY3NEYXRhOiBbXSwNCg0KICAgICAgLy8g6auY6aKR6YeH6LSt54mp5paZ55u45YWzDQogICAgICBzZWxlY3RlZENvZGVUeXBlOiAnQUxMJywNCiAgICAgIHNlbGVjdGVkSXRlbVR5cGU6ICdDTEFTUzMnLA0KICAgICAgaGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YTogW10sDQoNCiAgICAgIC8vIOS+m+W6lOWVhumjjumZqeaVsOaNrg0KICAgICAgc3VwcGxpZXJSaXNrRGF0YTogW10sDQoNCiAgICAgIC8vIEFJ5Lu35qC86aKE5rWL55u45YWzDQogICAgICBwcmljZVByZWRpY3Rpb25zOiBbXSwgLy8g5pS55Li65pWw57uE77yM5pSv5oyB5aSa5Liq54mp5paZ55qE6aKE5rWLDQogICAgICBwcmVkaWN0aW9uTG9hZGluZzogZmFsc2UsDQoNCiAgICAgIC8vIOeJqeaWmeS7t+agvOi2i+WKv+WbvuebuOWFsw0KICAgICAgbWF0ZXJpYWxOYW1lT3B0aW9uczogW10sDQogICAgICBzZWxlY3RlZE1hdGVyaWFsOiAnUELlnZcnLA0KICAgICAgc2VsZWN0ZWRNYXRlcmlhbENhdGVnb3J5OiAnMScsIC8vIOm7mOiupOmAieaLqeefv+efsw0KICAgICAgcHJpY2VBbmRTdG9yZURhdGE6IG51bGwsDQoNCiAgICAgIC8vIOaWsOeahOS7t+agvOi2i+WKv+WbvuebuOWFs+WxnuaApw0KICAgICAgLy8g6YeH6LSt6YeP5puy57q/DQogICAgICBwdXJjaGFzZUFtb3VudENhdGVnb3JpZXM6IFs5OV0sIC8vIOm7mOiupOmAieaLqeWFqOmDqA0KICAgICAgc2VsZWN0ZWRQdXJjaGFzZUFtb3VudE1hdGVyaWFsczogW10sDQogICAgICBwdXJjaGFzZUFtb3VudE1hdGVyaWFsT3B0aW9uczogW10sDQoNCiAgICAgIC8vIOW4guWcuuS7t+absue6vw0KICAgICAgbWFya2V0UHJpY2VDYXRlZ29yaWVzOiBbOTldLCAvLyDpu5jorqTpgInmi6nlhajpg6gNCiAgICAgIHNlbGVjdGVkTWFya2V0UHJpY2VNYXRlcmlhbHM6IFtdLA0KICAgICAgbWFya2V0UHJpY2VNYXRlcmlhbE9wdGlvbnM6IFtdLA0KDQogICAgICAvLyDojrflj5bmlbDmja7nirbmgIENCiAgICAgIGZldGNoaW5nUHJpY2VEYXRhOiBmYWxzZSwNCiAgICAgIG5ld1ByaWNlQW5kU3RvcmVEYXRhOiBudWxsLA0KDQogICAgICAvLyDliJ3lp4vljJbmoIflv5cNCiAgICAgIGhhc0luaXRpYWxpemVkUHJpY2VDaGFydDogZmFsc2UsDQoNCiAgICAgIC8vIOebuOS8vOeJqeaWmeaVsOaNrg0KICAgICAgc2ltaWxhck1hdGVyaWFsc0RhdGE6IFtdLA0KICAgICAgc2ltaWxhck1hdGVyaWFsc0xvYWRpbmc6IGZhbHNlLA0KDQogICAgICAvLyDlr7nmr5TlvLnmoYbnm7jlhbMNCiAgICAgIGNvbXBhcmlzb25EaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNvbXBhcmlzb25DaGFydExvYWRpbmc6IGZhbHNlLA0KICAgICAgY3VycmVudENvbXBhcmlzb246IHt9LA0KICAgICAgY29tcGFyaXNvbkNoYXJ0SW5zdGFuY2U6IG51bGwsDQogICAgICBjb21wYXJpc29uUHJpY2VEYXRhOiBudWxsLA0KDQogICAgICAvLyDmnLrml4HlupPlvZPliY3lupPlrZjnm7jlhbMNCiAgICAgIHNlbGVjdGVkRmFjdG9yeURlcDogJycsIC8vIOmAieS4reeahOWIhuWOgg0KICAgICAgc2VsZWN0ZWRGYWN0b3J5TWF0ZXJpYWxUeXBlOiAnJywgLy8g6YCJ5Lit55qE54mp5paZ57G75Z6LDQogICAgICBmYWN0b3J5RGVwT3B0aW9uczogW10sIC8vIOWIhuWOgumAiemhueWIl+ihqA0KICAgICAgZmFjdG9yeVN0b2NrRGF0YTogW10gLy8g5py65peB5bqT5a2Y5pWw5o2uDQogICAgfQ0KICB9LA0KDQogIGNvbXB1dGVkOiB7DQogICAgaXNGdWxsc2NyZWVuKCkgew0KICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLmFwcC5pc0Z1bGxzY3JlZW5Nb2RlDQogICAgfSwNCg0KICAgIC8vIOaMiWl0ZW1OYW1l44CBY2F0ZWdvcnnjgIFwcmljZVR5cGXogZTlkIjntKLlvJXliIbnu4Tnm7jkvLznianmlpnmlbDmja4NCiAgICBncm91cGVkU2ltaWxhck1hdGVyaWFscygpIHsNCiAgICAgIGNvbnN0IGdyb3VwZWQgPSB7fQ0KICAgICAgdGhpcy5zaW1pbGFyTWF0ZXJpYWxzRGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAvLyDliJvlu7rogZTlkIjntKLlvJVrZXkNCiAgICAgICAgY29uc3QgZ3JvdXBLZXkgPSBgJHtpdGVtLml0ZW1OYW1lfV8ke2l0ZW0uY2F0ZWdvcnl9XyR7aXRlbS5wcmljZVR5cGV9YA0KICAgICAgICBjb25zdCBkaXNwbGF5S2V5ID0gYCR7aXRlbS5pdGVtTmFtZX0gKCR7dGhpcy5nZXRDYXRlZ29yeU5hbWUoaXRlbS5jYXRlZ29yeSl9IC0gJHt0aGlzLmdldFByaWNlVHlwZU5hbWUoaXRlbS5wcmljZVR5cGUpfSlgDQoNCiAgICAgICAgaWYgKCFncm91cGVkW2Rpc3BsYXlLZXldKSB7DQogICAgICAgICAgZ3JvdXBlZFtkaXNwbGF5S2V5XSA9IHsNCiAgICAgICAgICAgIGdyb3VwS2V5OiBncm91cEtleSwNCiAgICAgICAgICAgIGl0ZW1zOiBbXQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBncm91cGVkW2Rpc3BsYXlLZXldLml0ZW1zLnB1c2goaXRlbSkNCiAgICAgIH0pDQoNCiAgICAgIC8vIOWvueavj+S4que7hOWGheeahOaVsOaNruaMieaOkuWQjeaOkuW6jw0KICAgICAgT2JqZWN0LmtleXMoZ3JvdXBlZCkuZm9yRWFjaChrZXkgPT4gew0KICAgICAgICBncm91cGVkW2tleV0uaXRlbXMuc29ydCgoYSwgYikgPT4gYS5yYW5rIC0gYi5yYW5rKQ0KICAgICAgfSkNCg0KICAgICAgcmV0dXJuIGdyb3VwZWQNCiAgICB9DQogIH0sDQoNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmNoZWNrRWNoYXJ0c0F2YWlsYWJpbGl0eSgpDQogICAgdGhpcy5mZXRjaERhc2hib2FyZERhdGEoMykNCiAgICB0aGlzLmZldGNoWWVhcmx5SW52ZW50b3J5RGF0YSgpDQogICAgdGhpcy5mZXRjaFJlYWxUaW1lSW52ZW50b3J5RGF0YSgpDQogICAgdGhpcy5mZXRjaENva2luZ0NvYWxJbnZlbnRvcnlEYXRhKCkNCiAgICAvLyDliJ3lp4vljJbnianmlpnlhaXlupPnu5/orqHnmoTkuIvmi4nmoYbpgInpobnlkozmlbDmja4NCiAgICB0aGlzLnVwZGF0ZU1hdGVyaWFsSXRlbU9wdGlvbnMoKS50aGVuKCgpID0+IHsNCiAgICAgIHRoaXMuZmV0Y2hNYXRlcmlhbFN0YXRpc3RpY3NEYXRhKCkNCiAgICB9KQ0KICAgIC8vIOWIneWni+WMlumrmOmikemHh+i0reeJqeaWmeaVsOaNrg0KICAgIHRoaXMuZmV0Y2hIaWdoRnJlcXVlbmN5TWF0ZXJpYWxEYXRhKCkNCiAgICAvLyDliJ3lp4vljJbkvpvlupTllYbpo47pmanmlbDmja4NCiAgICB0aGlzLmZldGNoU3VwcGxpZXJSaXNrRGF0YSgpDQoNCiAgICAvLyDliJ3lp4vljJbmlrDnmoTnianmlpnlkI3np7DliJfooajvvIjkvJroh6rliqjop6blj5Hpu5jorqTpgInkuK1QQuWdl+WSjOaVsOaNruiOt+WPlu+8iQ0KICAgIHRoaXMuZmV0Y2hQdXJjaGFzZUFtb3VudE1hdGVyaWFsTGlzdCgpDQogICAgdGhpcy5mZXRjaE1hcmtldFByaWNlTWF0ZXJpYWxMaXN0KCkNCg0KICAgIC8vIOWIneWni+WMluacuuaXgeW6k+WtmOaVsOaNrg0KICAgIHRoaXMuZmV0Y2hGYWN0b3J5RGVwT3B0aW9ucygpDQoNCiAgICB0aGlzLnNldHVwUmVzaXplT2JzZXJ2ZXIoKQ0KICAgIHRoaXMuaW5pdEZ1bGxzY3JlZW5MaXN0ZW5lcigpDQoNCiAgICAvLyDnm5HlkKznqpflj6PlpKflsI/lj5jljJYNCiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5yZXNpemVBbGxDaGFydHMpDQogIH0sDQoNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICAvLyDmuIXnkIblrprml7blmajlkozkuovku7bnm5HlkKzlmagNCiAgICB0aGlzLmNsZWFyQWxsSW50ZXJ2YWxzKCkNCiAgICB0aGlzLnJlbW92ZUZ1bGxzY3JlZW5MaXN0ZW5lcigpDQogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVzaXplQWxsQ2hhcnRzKQ0KDQogICAgLy8g56Gu5L+d6YCA5Ye65YWo5bGP5qih5byPDQogICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC9zZXRGdWxsc2NyZWVuTW9kZScsIGZhbHNlKQ0KICB9LA0KDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliJ3lp4vljJblhajlsY/nm5HlkKzlmagNCiAgICBpbml0RnVsbHNjcmVlbkxpc3RlbmVyKCkgew0KICAgICAgaWYgKHNjcmVlbmZ1bGwgJiYgc2NyZWVuZnVsbC5pc0VuYWJsZWQpIHsNCiAgICAgICAgc2NyZWVuZnVsbC5vbignY2hhbmdlJywgdGhpcy5oYW5kbGVGdWxsc2NyZWVuQ2hhbmdlKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDnp7vpmaTlhajlsY/nm5HlkKzlmagNCiAgICByZW1vdmVGdWxsc2NyZWVuTGlzdGVuZXIoKSB7DQogICAgICBpZiAoc2NyZWVuZnVsbCAmJiBzY3JlZW5mdWxsLmlzRW5hYmxlZCkgew0KICAgICAgICBzY3JlZW5mdWxsLm9mZignY2hhbmdlJywgdGhpcy5oYW5kbGVGdWxsc2NyZWVuQ2hhbmdlKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIblhajlsY/nirbmgIHlj5jljJYNCiAgICBoYW5kbGVGdWxsc2NyZWVuQ2hhbmdlKCkgew0KICAgICAgaWYgKHNjcmVlbmZ1bGwgJiYgc2NyZWVuZnVsbC5pc0VuYWJsZWQpIHsNCiAgICAgICAgY29uc3QgaXNGdWxsc2NyZWVuID0gc2NyZWVuZnVsbC5pc0Z1bGxzY3JlZW4NCiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC9zZXRGdWxsc2NyZWVuTW9kZScsIGlzRnVsbHNjcmVlbikNCg0KICAgICAgICAvLyDlhajlsY/nirbmgIHlj5jljJblkI7vvIzph43mlrDosIPmlbTlm77ooajlpKflsI8NCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5yZXNpemVBbGxDaGFydHMoKQ0KICAgICAgICAgIH0sIDMwMCkgLy8g57uZ5biD5bGA5Y+Y5YyW5LiA5Lqb5pe26Ze0DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIEFQSeiwg+eUqOaWueazlQ0KICAgIGFzeW5jIGdldERhc2hib2FyZERhdGEoZGltZW5zaW9uVHlwZSkgew0KICAgICAgcmV0dXJuIGF3YWl0IHNob3dEYXRhKHsgZGltZW5zaW9uVHlwZTogZGltZW5zaW9uVHlwZSB9KQ0KICAgIH0sDQoNCiAgICBhc3luYyBnZXRJdGVtVHlwZUxpc3QoaXRlbVR5cGUpIHsNCiAgICAgIHJldHVybiBhd2FpdCBzaG93SXRlbVR5cGVMaXN0KHsgaXRlbVR5cGU6IGl0ZW1UeXBlIH0pDQogICAgfSwNCg0KICAgIGFzeW5jIGdldE1hdGVyaWFsTGlzdChwYXJhbXMpIHsNCiAgICAgIHJldHVybiBhd2FpdCBzaG93TWF0ZXJpYWxMaXN0KHBhcmFtcykNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0U3VwcGxpZXJMaXN0KHBhcmFtcykgew0KICAgICAgcmV0dXJuIGF3YWl0IHNob3dTdXBwTGlzdChwYXJhbXMpDQogICAgfSwNCg0KICAgIGFzeW5jIGdldFllYXJseUFtb3VudChwYXJhbXMpIHsNCiAgICAgIHJldHVybiBhd2FpdCBzaG93WWVhcmx5QW1vdW50KHBhcmFtcykNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0UmVhbFRpbWVBbW91bnQoKSB7DQogICAgICByZXR1cm4gYXdhaXQgc2hvd1JlYWxUaW1lQW1vdW50KCkNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0Q29raW5nQ29hbEFtb3VudCgpIHsNCiAgICAgIHJldHVybiBhd2FpdCBzaG93Q29raW5nQ29hbEFtb3VudCgpDQogICAgfSwNCg0KICAgIGFzeW5jIGdldEtleUluZGljYXRvcnMocGFyYW1zKSB7DQogICAgICByZXR1cm4gYXdhaXQgc2hvd0tleUluZGljYXRvcnMocGFyYW1zKQ0KICAgIH0sDQoNCiAgICBhc3luYyBnZXRIaWdoRnJlcXVlbmN5TWF0ZXJpYWxMaXN0KHBhcmFtcykgew0KICAgICAgcmV0dXJuIGF3YWl0IHNob3dIaWdoRnJlcXVlbmN5TWF0ZXJpYWxMaXN0KHBhcmFtcykNCiAgICB9LA0KDQogICAgYXN5bmMgZ2V0UHVyY2hhc2VTdXBwUmlzayhwYXJhbXMpIHsNCiAgICAgIHJldHVybiBhd2FpdCBzaG93UHVyY2hhc2VTdXBwUmlzayhwYXJhbXMpDQogICAgfSwNCg0KICAgIC8vIOagueaNrmRpbWVuc2lvblR5cGXojrflj5Z0aW1lRmxhZw0KICAgIGdldFRpbWVGbGFnQnlEaW1lbnNpb25UeXBlKGRpbWVuc2lvblR5cGUpIHsNCiAgICAgIHN3aXRjaChkaW1lbnNpb25UeXBlKSB7DQogICAgICAgIGNhc2UgMTogcmV0dXJuICcwMycgLy8g6L+R5LiJ5Liq5pyIDQogICAgICAgIGNhc2UgMjogcmV0dXJuICcwNicgLy8g6L+R5YWt5Liq5pyIDQogICAgICAgIGNhc2UgMzogcmV0dXJuICcxMicgLy8g6L+R5LiA5bm0DQogICAgICAgIGRlZmF1bHQ6IHJldHVybiAnMDMnDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOajgOafpUVDaGFydHPlj6/nlKjmgKcNCiAgICBjaGVja0VjaGFydHNBdmFpbGFiaWxpdHkoKSB7DQogICAgICBpZiAoIWVjaGFydHMpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcignRUNoYXJ0c+W6k+acquiDveWKoOi9ve+8jOS9v+eUqOWkh+eUqOaYvuekuuaWueW8jycpDQogICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5jaGFydCcpLmZvckVhY2goZWwgPT4gew0KICAgICAgICAgIGVsLmlubmVySFRNTCA9ICc8ZGl2IGNsYXNzPSJjaGFydC1wbGFjZWhvbGRlciI+5Zu+6KGo5Yqg6L295aSx6LSlPC9kaXY+Jw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4gZmFsc2UNCiAgICAgIH0NCiAgICAgIHJldHVybiB0cnVlDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluS7quihqOadv+aVsOaNrg0KICAgIGFzeW5jIGZldGNoRGFzaGJvYXJkRGF0YShkaW1lbnNpb25UeXBlUGFyYW0gPSAxKSB7DQogICAgICB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlID0gZGltZW5zaW9uVHlwZVBhcmFtDQoNCiAgICAgIC8vIOa4hemZpOaJgOacieWumuaXtuWZqA0KICAgICAgdGhpcy5jbGVhckFsbEludGVydmFscygpDQoNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOW5tuihjOiOt+WPluS7quihqOadv+aVsOaNruWSjOWFs+mUruaMh+agh+aVsOaNrg0KICAgICAgICBjb25zdCBbZGFzaGJvYXJkUmVzcG9uc2UsIGtleUluZGljYXRvcnNSZXNwb25zZV0gPSBhd2FpdCBQcm9taXNlLmFsbChbDQogICAgICAgICAgdGhpcy5nZXREYXNoYm9hcmREYXRhKGRpbWVuc2lvblR5cGVQYXJhbSksDQogICAgICAgICAgdGhpcy5nZXRLZXlJbmRpY2F0b3JzKHsgZGltZW5zaW9uVHlwZTogZGltZW5zaW9uVHlwZVBhcmFtIH0pDQogICAgICAgIF0pDQoNCiAgICAgICAgLy8g5aSE55CG5Luq6KGo5p2/5pWw5o2uDQogICAgICAgIGlmIChkYXNoYm9hcmRSZXNwb25zZSAmJiBkYXNoYm9hcmRSZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5kYXNoYm9hcmREYXRhID0gZGFzaGJvYXJkUmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCfojrflj5bku6rooajmnb/mlbDmja7miJDlip86JywgdGhpcy5kYXNoYm9hcmREYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FQSeaVsOaNruagvOW8j+S4jeato+ehruaIlue8uuWwkWRhdGHlrZfmrrUnLCBkYXNoYm9hcmRSZXNwb25zZSkNCiAgICAgICAgICB0aGlzLnNob3dFcnJvck1lc3NhZ2UoJ0FQSeaVsOaNruagvOW8j+S4jeato+ehruaIlue8uuWwkWRhdGHlrZfmrrUnKQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aSE55CG5YWz6ZSu5oyH5qCH5pWw5o2uDQogICAgICAgIGlmIChrZXlJbmRpY2F0b3JzUmVzcG9uc2UgJiYga2V5SW5kaWNhdG9yc1Jlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICB0aGlzLnB1cmNoYXNlU3RhdHMgPSBrZXlJbmRpY2F0b3JzUmVzcG9uc2UuZGF0YSB8fCB7fQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCfojrflj5blhbPplK7mjIfmoIfmlbDmja7miJDlip86JywgdGhpcy5wdXJjaGFzZVN0YXRzKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWFs+mUruaMh+agh+aVsOaNruWksei0pScsIGtleUluZGljYXRvcnNSZXNwb25zZSkNCiAgICAgICAgICB0aGlzLnB1cmNoYXNlU3RhdHMgPSB7fQ0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy5pbml0QWxsQ2hhcnRzKCkNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FQSeivt+axguaIluaVsOaNruWkhOeQhuWksei0pScsIGVycm9yKQ0KICAgICAgICB0aGlzLnNob3dFcnJvck1lc3NhZ2UoJ+aVsOaNruWKoOi9veWksei0pTogJyArIGVycm9yLm1lc3NhZ2UpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOaYvuekuumUmeivr+S/oeaBrw0KICAgIHNob3dFcnJvck1lc3NhZ2UobWVzc2FnZSkgew0KICAgICAgZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLmNoYXJ0JykuZm9yRWFjaChjaGFydCA9PiB7DQogICAgICAgIGNoYXJ0LmlubmVySFRNTCA9IGA8ZGl2IGNsYXNzPSJjaGFydC1wbGFjZWhvbGRlciI+JHttZXNzYWdlfTwvZGl2PmANCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOaXtumXtOi/h+a7pOWZqOWPmOWMluWkhOeQhg0KICAgIGhhbmRsZVRpbWVGaWx0ZXJDaGFuZ2UoZmlsdGVySWQsIGRpbWVuc2lvblR5cGUpIHsNCiAgICAgIHRoaXMuYWN0aXZlRmlsdGVyID0gZmlsdGVySWQNCiAgICAgIHRoaXMuY3VycmVudERpbWVuc2lvblR5cGUgPSBkaW1lbnNpb25UeXBlDQogICAgICBjb25zb2xlLmxvZygn6YCJ5oup55qE5pe26Ze06IyD5Zu0OicsIGZpbHRlcklkLCAn57u05bqmOicsIGRpbWVuc2lvblR5cGUpDQoNCiAgICAgIHRoaXMuY2xlYXJBbGxJbnRlcnZhbHMoKQ0KICAgICAgdGhpcy5mZXRjaERhc2hib2FyZERhdGEoZGltZW5zaW9uVHlwZSkNCiAgICAgIC8vIOWQjOaXtuabtOaWsOmrmOmikeeJqeaWmeaVsOaNrg0KICAgICAgdGhpcy5mZXRjaEhpZ2hGcmVxdWVuY3lNYXRlcmlhbERhdGEoKQ0KICAgICAgLy8g5ZCM5pe25pu05paw5L6b5bqU5ZWG6aOO6Zmp5pWw5o2uDQogICAgICB0aGlzLmZldGNoU3VwcGxpZXJSaXNrRGF0YSgpDQogICAgICAvLyDlkIzml7bmm7TmlrDnianmlpnlhaXlupPnu5/orqHmlbDmja4NCiAgICAgIHRoaXMuZmV0Y2hNYXRlcmlhbFN0YXRpc3RpY3NEYXRhKCkNCiAgICAgIC8vIOazqOaEj++8muS7t+agvOi2i+WKv+aVsOaNruWPquWcqOeUqOaIt+S4u+WKqOeCueWHu+aMiemSruaXtuiOt+WPlu+8jOS4jeWcqOaXtumXtOi/h+a7pOWZqOWPmOWMluaXtuiHquWKqOiOt+WPlg0KDQogICAgICAvLyDlkIzml7bmm7TmlrDmlrDnmoTnianmlpnliJfooajvvIjnlKjkuo7kuIvmi4nmoYbpgInpobnvvInvvIzkvYbkuI3kvJroh6rliqjop6blj5HmlbDmja7ojrflj5YNCiAgICAgIHRoaXMuZmV0Y2hQdXJjaGFzZUFtb3VudE1hdGVyaWFsTGlzdCgpDQogICAgICB0aGlzLmZldGNoTWFya2V0UHJpY2VNYXRlcmlhbExpc3QoKQ0KICAgIH0sDQoNCiAgICAvLyDmuIXpmaTmiYDmnInlrprml7blmagNCiAgICBjbGVhckFsbEludGVydmFscygpIHsNCiAgICAgIE9iamVjdC52YWx1ZXModGhpcy5jaGFydEluc3RhbmNlcykuZm9yRWFjaChpbnN0YW5jZSA9PiB7DQogICAgICAgIGlmIChpbnN0YW5jZSAmJiBpbnN0YW5jZS5pbnRlcnZhbElkKSB7DQogICAgICAgICAgY2xlYXJJbnRlcnZhbChpbnN0YW5jZS5pbnRlcnZhbElkKQ0KICAgICAgICAgIGluc3RhbmNlLmludGVydmFsSWQgPSBudWxsDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOmHjeaWsOiwg+aVtOaJgOacieWbvuihqOWkp+Wwjw0KICAgIHJlc2l6ZUFsbENoYXJ0cygpIHsNCiAgICAgIE9iamVjdC52YWx1ZXModGhpcy5jaGFydEluc3RhbmNlcykuZm9yRWFjaChpbnN0YW5jZSA9PiB7DQogICAgICAgIGlmIChpbnN0YW5jZSkgew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBpbnN0YW5jZS5yZXNpemUoKQ0KICAgICAgICAgIH0gY2F0Y2goZXJyKSB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCflm77ooajlpKflsI/osIPmlbTlpLHotKU6JywgZXJyKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5Yid5aeL5YyW5omA5pyJ5Zu+6KGoDQogICAgaW5pdEFsbENoYXJ0cygpIHsNCiAgICAgIGNvbnNvbGUubG9nKCdpbml0QWxsQ2hhcnRzIHN0YXJ0ZWQnKQ0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5rOo5oSP77ya5a6e5pe25bqT5a2Y5Zu+6KGo5ZKM55+/54Sm54Wk5bqT5a2Y5Zu+6KGo5Lya5Zyo5ZCE6Ieq5pWw5o2u6I635Y+W5a6M5oiQ5ZCO5Y2V54us5Yid5aeL5YyWDQogICAgICAgIC8vIOazqOaEj++8muaciOW6puW6k+WtmOmHkemineWbvuihqOS8muWcqGZldGNoWWVhcmx5SW52ZW50b3J5RGF0YeWujOaIkOWQjuWNleeLrOWIneWni+WMlg0KICAgICAgICAvLyDms6jmhI/vvJrnianmlpnlhaXlupPnu5/orqHlm77ooajkvJrlnKhmZXRjaE1hdGVyaWFsU3RhdGlzdGljc0RhdGHlrozmiJDlkI7ljZXni6zliJ3lp4vljJYNCiAgICAgICAgLy8g5rOo5oSP77ya5py65peB5bqT5a2Y5Zu+6KGo5Lya5ZyoZmV0Y2hGYWN0b3J5U3RvY2tEYXRh5a6M5oiQ5ZCO5Y2V54us5Yid5aeL5YyWDQoNCiAgICAgICAgLy8g5Yid5aeL5YyW54mp5paZ6K+N5LqR5Zu+DQogICAgICAgIHRoaXMuaW5pdE1hdGVyaWFsQ2xvdWQoKQ0KDQogICAgICAgIC8vIOWIneWni+WMllRPUOS+m+W6lOWVhuWbvg0KICAgICAgICB0aGlzLmluaXRUb3BTdXBwbGllcnNDaGFydCgpDQogICAgICAgIHRoaXMucG9wdWxhdGVJdGVtRHJvcGRvd24oJ3RvcFN1cHBsaWVyc0ZpbHRlcicsIDEsICd0b3BTdXBwbGllcnNPcHRpb25zJykNCg0KICAgICAgICAvLyDms6jmhI/vvJrkvpvlupTllYbpo47pmanlm77ooajkvJrlnKhmZXRjaFN1cHBsaWVyUmlza0RhdGHlrozmiJDlkI7ljZXni6zliJ3lp4vljJYNCg0KICAgICAgICAvLyDms6jmhI/vvJrph4fotK3ku7fmoLzotovlir/lm77kvJrlnKhmZXRjaFByaWNlQW5kU3RvcmVEYXRh5a6M5oiQ5ZCO5Y2V54us5Yid5aeL5YyWDQoNCiAgICAgICAgY29uc29sZS5sb2coJ+aJgOacieWbvuihqOWIneWni+WMluWujOaIkCcpDQogICAgICB9IGNhdGNoIChlcnIpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Zu+6KGo5Yid5aeL5YyW5Li75rWB56iL5aSx6LSlOicsIGVycikNCiAgICAgICAgdGhpcy5zaG93RXJyb3JNZXNzYWdlKCflm77ooajliJ3lp4vljJblpLHotKU6ICcgKyBlcnIubWVzc2FnZSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5aGr5YWF54mp5paZ57G75Z6L5LiL5ouJ5qGGDQogICAgYXN5bmMgcG9wdWxhdGVJdGVtRHJvcGRvd24oc2VsZWN0RWxlbWVudElkLCBpdGVtVHlwZSwgZGF0YVByb3BlcnR5TmFtZSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldEl0ZW1UeXBlTGlzdChpdGVtVHlwZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgdGhpc1tkYXRhUHJvcGVydHlOYW1lXSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKGBJbnZhbGlkIGRhdGEgZm9ybWF0IGZyb20gc2hvd0l0ZW1UeXBlTGlzdCBmb3IgaXRlbVR5cGUgJHtpdGVtVHlwZX06YCwgcmVzcG9uc2UpDQogICAgICAgICAgdGhpc1tkYXRhUHJvcGVydHlOYW1lXSA9IFtdDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGZldGNoaW5nIGl0ZW0gdHlwZXMgZm9yICR7c2VsZWN0RWxlbWVudElkfTpgLCBlcnJvcikNCiAgICAgICAgdGhpc1tkYXRhUHJvcGVydHlOYW1lXSA9IFtdDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOS4i+aLieahhuWPmOWMluWkhOeQhuaWueazlQ0KICAgIGFzeW5jIGhhbmRsZVRvcFN1cHBsaWVyc0ZpbHRlckNoYW5nZSgpIHsNCiAgICAgIGF3YWl0IHRoaXMucmVmcmVzaFRvcFN1cHBsaWVyc0NoYXJ0KCkNCiAgICB9LA0KDQogICAgYXN5bmMgaGFuZGxlT3JkZXJUeXBlQ2hhbmdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+aOkuW6j+exu+Wei+WPmOWMljonLCB0aGlzLnNlbGVjdGVkT3JkZXJUeXBlKQ0KICAgICAgYXdhaXQgdGhpcy5yZWZyZXNoVG9wU3VwcGxpZXJzQ2hhcnQoKQ0KICAgIH0sDQoNCiAgICBhc3luYyByZWZyZXNoVG9wU3VwcGxpZXJzQ2hhcnQoKSB7DQogICAgICBjb25zb2xlLmxvZyhgVG9wIHN1cHBsaWVyIGZpbHRlciBzZWxlY3RlZCBpdGVtIElEOiAke3RoaXMuc2VsZWN0ZWRUb3BTdXBwbGllcnNGaWx0ZXJ9LCBvcmRlclR5cGU6ICR7dGhpcy5zZWxlY3RlZE9yZGVyVHlwZX1gKQ0KICAgICAgY29uc3QgbXlDaGFydCA9IHRoaXMuY2hhcnRJbnN0YW5jZXMudG9wU3VwcGxpZXJzQ2hhcnQNCiAgICAgIGlmICghbXlDaGFydCkgew0KICAgICAgICBjb25zb2xlLmVycm9yKCJUT1AxMOS+m+W6lOWVhuWbvuihqOWunuS+i+acquaJvuWIsCIpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBpZiAobXlDaGFydC5pbnRlcnZhbElkKSB7DQogICAgICAgIGNsZWFySW50ZXJ2YWwobXlDaGFydC5pbnRlcnZhbElkKQ0KICAgICAgICBteUNoYXJ0LmludGVydmFsSWQgPSBudWxsDQogICAgICB9DQoNCiAgICAgIGlmICghdGhpcy5zZWxlY3RlZFRvcFN1cHBsaWVyc0ZpbHRlciB8fCB0aGlzLnNlbGVjdGVkVG9wU3VwcGxpZXJzRmlsdGVyID09PSAiIikgew0KICAgICAgICAvLyDkvb/nlKjljp/lp4vmlbDmja7vvIzkvYbpnIDopoHmoLnmja5vcmRlclR5cGXph43mlrDojrflj5YNCiAgICAgICAgbXlDaGFydC5zaG93TG9hZGluZygpDQogICAgICAgIHRyeSB7DQogICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldFN1cHBsaWVyTGlzdCh7DQogICAgICAgICAgICBkaW1lbnNpb25UeXBlOiB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlLA0KICAgICAgICAgICAgb3JkZXJUeXBlOiB0aGlzLnNlbGVjdGVkT3JkZXJUeXBlDQogICAgICAgICAgfSkNCg0KICAgICAgICAgIGxldCBuZXdTdXBwbGllckRhdGEgPSBbXQ0KICAgICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICAgIG5ld1N1cHBsaWVyRGF0YSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY29uc29sZS5lcnJvcign5LuOc2hvd1N1cHBMaXN0IEFQSeiOt+WPlueahOaVsOaNruaXoOaViDonLCByZXNwb25zZSkNCiAgICAgICAgICAgIG5ld1N1cHBsaWVyRGF0YSA9IHRoaXMub3JpZ2luYWxUb3BTdXBwbGllcnNEYXRhDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMucmVuZGVyQW5kUGFnaW5hdGVUb3BTdXBwbGllcnMobXlDaGFydCwgbmV3U3VwcGxpZXJEYXRhKQ0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOS4unRvcFN1cHBsaWVyc0NoYXJ06I635Y+W5L6b5bqU5ZWG5YiX6KGo5aSx6LSlOmAsIGVycm9yKQ0KICAgICAgICAgIHRoaXMucmVuZGVyQW5kUGFnaW5hdGVUb3BTdXBwbGllcnMobXlDaGFydCwgdGhpcy5vcmlnaW5hbFRvcFN1cHBsaWVyc0RhdGEpDQogICAgICAgIH0gZmluYWxseSB7DQogICAgICAgICAgbXlDaGFydC5oaWRlTG9hZGluZygpDQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIG15Q2hhcnQuc2hvd0xvYWRpbmcoKQ0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5nZXRTdXBwbGllckxpc3Qoew0KICAgICAgICAgICAgZGltZW5zaW9uVHlwZTogdGhpcy5jdXJyZW50RGltZW5zaW9uVHlwZSwNCiAgICAgICAgICAgIGl0ZW1JZDogdGhpcy5zZWxlY3RlZFRvcFN1cHBsaWVyc0ZpbHRlciwNCiAgICAgICAgICAgIG9yZGVyVHlwZTogdGhpcy5zZWxlY3RlZE9yZGVyVHlwZQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICBsZXQgbmV3U3VwcGxpZXJEYXRhID0gW10NCiAgICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgICBuZXdTdXBwbGllckRhdGEgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S7jnNob3dTdXBwTGlzdCBBUEnojrflj5bnmoTmlbDmja7ml6DmlYg6JywgcmVzcG9uc2UpDQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMucmVuZGVyQW5kUGFnaW5hdGVUb3BTdXBwbGllcnMobXlDaGFydCwgbmV3U3VwcGxpZXJEYXRhKQ0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYOS4unRvcFN1cHBsaWVyc0NoYXJ06I635Y+W5L6b5bqU5ZWG5YiX6KGo5aSx6LSlOmAsIGVycm9yKQ0KICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCd0b3BTdXBwbGllcnNDaGFydCcpLmlubmVySFRNTCA9ICc8ZGl2IGNsYXNzPSJjaGFydC1wbGFjZWhvbGRlciI+5L6b5bqU5ZWG5pWw5o2u5Yqg6L295aSx6LSlPC9kaXY+Jw0KICAgICAgICB9IGZpbmFsbHkgew0KICAgICAgICAgIG15Q2hhcnQuaGlkZUxvYWRpbmcoKQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOiuvue9ruWkp+Wwj+iwg+aVtOinguWvn+WZqA0KICAgIHNldHVwUmVzaXplT2JzZXJ2ZXIoKSB7DQogICAgICBjb25zdCByZXNpemVPYnNlcnZlciA9IG5ldyBSZXNpemVPYnNlcnZlcihlbnRyaWVzID0+IHsNCiAgICAgICAgZm9yIChsZXQgZW50cnkgb2YgZW50cmllcykgew0KICAgICAgICAgIGNvbnN0IGNoYXJ0cyA9IGVudHJ5LnRhcmdldC5xdWVyeVNlbGVjdG9yQWxsKCcuY2hhcnQnKQ0KICAgICAgICAgIGNoYXJ0cy5mb3JFYWNoKGNoYXJ0ID0+IHsNCiAgICAgICAgICAgIGlmIChjaGFydC5pZCkgew0KICAgICAgICAgICAgICBjb25zdCBpbnN0YW5jZSA9IGVjaGFydHMuZ2V0SW5zdGFuY2VCeURvbShkb2N1bWVudC5nZXRFbGVtZW50QnlJZChjaGFydC5pZCkpDQogICAgICAgICAgICAgIGlmIChpbnN0YW5jZSkgew0KICAgICAgICAgICAgICAgIGluc3RhbmNlLnJlc2l6ZSgpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KDQogICAgICBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcuY2FyZCcpLmZvckVhY2goY2FyZCA9PiB7DQogICAgICAgIHJlc2l6ZU9ic2VydmVyLm9ic2VydmUoY2FyZCkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHRvZ2dsZUZ1bGxzY3JlZW4oKSB7DQogICAgICBpZiAoc2NyZWVuZnVsbCAmJiBzY3JlZW5mdWxsLmlzRW5hYmxlZCkgew0KICAgICAgICBzY3JlZW5mdWxsLnRvZ2dsZSgpDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAn5oKo55qE5rWP6KeI5Zmo5LiN5pSv5oyB5YWo5bGP5Yqf6IO9JywNCiAgICAgICAgICB0eXBlOiAnd2FybmluZycNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KDQogICAgYXN5bmMgaGFuZGxlWWVhckNoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCflubTku73lj5jljJY6JywgdGhpcy5zZWxlY3RlZFllYXIpDQogICAgICBhd2FpdCB0aGlzLmZldGNoWWVhcmx5SW52ZW50b3J5RGF0YSgpDQogICAgfSwNCg0KICAgIGFzeW5jIGhhbmRsZU1hdGVyaWFsVHlwZUNoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfnianmlpnnsbvlnovlj5jljJY6JywgdGhpcy5zZWxlY3RlZE1hdGVyaWFsVHlwZSkNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hZZWFybHlJbnZlbnRvcnlEYXRhKCkNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5bm05bqm5bqT5a2Y5pWw5o2uDQogICAgYXN5bmMgZmV0Y2hZZWFybHlJbnZlbnRvcnlEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcGFyYW1zID0ge30NCg0KICAgICAgICAvLyDlj6rmnInlvZNtYXRlcmlhbFR5cGXkuI3kuLrnqbrml7bmiY3kvKDpgJLor6Xlj4LmlbANCiAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRNYXRlcmlhbFR5cGUgJiYgdGhpcy5zZWxlY3RlZE1hdGVyaWFsVHlwZSAhPT0gJycpIHsNCiAgICAgICAgICBwYXJhbXMubWF0ZXJpYWxUeXBlID0gdGhpcy5zZWxlY3RlZE1hdGVyaWFsVHlwZQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aaC5p6c6YCJ5oup5LqG5YW35L2T5bm05Lu977yM5Y+q5p+l6K+i6K+l5bm05Lu977yM5ZCm5YiZ5p+l6K+i5omA5pyJ5bm05Lu9DQogICAgICAgIGlmICh0aGlzLnNlbGVjdGVkWWVhcikgew0KICAgICAgICAgIHBhcmFtcy55ZWFyTGlzdCA9IFt0aGlzLnNlbGVjdGVkWWVhcl0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBwYXJhbXMueWVhckxpc3QgPSB0aGlzLmF2YWlsYWJsZVllYXJzDQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hZZWFybHlJbnZlbnRvcnlEYXRhIC0g6K+35rGC5Y+C5pWwOicsIHBhcmFtcykNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldFllYXJseUFtb3VudChwYXJhbXMpDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFllYXJseUludmVudG9yeURhdGEgLSDlrozmlbTlk43lupQ6JywgcmVzcG9uc2UpDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICB0aGlzLnllYXJseUludmVudG9yeURhdGEgPSByZXNwb25zZS5kYXRhIHx8IFtdDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoWWVhcmx5SW52ZW50b3J5RGF0YSAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLnllYXJseUludmVudG9yeURhdGEpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5L2/55So5qih5ouf5pWw5o2uDQogICAgICAgICAgdGhpcy55ZWFybHlJbnZlbnRvcnlEYXRhID0gdGhpcy5nZXRNb2NrWWVhcmx5RGF0YSgpDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoWWVhcmx5SW52ZW50b3J5RGF0YSAtIOS9v+eUqOaooeaLn+aVsOaNrjonLCB0aGlzLnllYXJseUludmVudG9yeURhdGEpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluW5tOW6puW6k+WtmOaVsOaNruWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNrjonLCBlcnJvcikNCiAgICAgICAgLy8g5L2/55So5qih5ouf5pWw5o2uDQogICAgICAgIHRoaXMueWVhcmx5SW52ZW50b3J5RGF0YSA9IHRoaXMuZ2V0TW9ja1llYXJseURhdGEoKQ0KICAgICAgfQ0KDQogICAgICAvLyDph43mlrDliJ3lp4vljJblm77ooagNCiAgICAgIHRoaXMuaW5pdE1vbnRobHlJbnZlbnRvcnlDaGFydCgpDQogICAgfSwNCg0KICAgIC8vIOeUn+aIkOaooeaLn+aVsOaNrg0KICAgIGdldE1vY2tZZWFybHlEYXRhKCkgew0KICAgICAgcmV0dXJuIFsNCiAgICAgICAgew0KICAgICAgICAgIHllYXI6ICcyMDIzJywNCiAgICAgICAgICBtb250aGx5UmVzdWx0Vm9MaXN0OiBbDQogICAgICAgICAgICB7IG1vbnRoSW5kZXg6IDEsIGFtb3VudDogMTIwMC41MCB9LA0KICAgICAgICAgICAgeyBtb250aEluZGV4OiAyLCBhbW91bnQ6IDEzNTAuNzUgfSwNCiAgICAgICAgICAgIHsgbW9udGhJbmRleDogMywgYW1vdW50OiAxMTgwLjIwIH0sDQogICAgICAgICAgICB7IG1vbnRoSW5kZXg6IDQsIGFtb3VudDogMTQyMC4zMCB9LA0KICAgICAgICAgICAgeyBtb250aEluZGV4OiA1LCBhbW91bnQ6IDEzODAuOTAgfSwNCiAgICAgICAgICAgIHsgbW9udGhJbmRleDogNiwgYW1vdW50OiAxNTIwLjQwIH0sDQogICAgICAgICAgICB7IG1vbnRoSW5kZXg6IDcsIGFtb3VudDogMTY1MC42MCB9LA0KICAgICAgICAgICAgeyBtb250aEluZGV4OiA4LCBhbW91bnQ6IDE0ODAuODUgfSwNCiAgICAgICAgICAgIHsgbW9udGhJbmRleDogOSwgYW1vdW50OiAxMzkwLjI1IH0sDQogICAgICAgICAgICB7IG1vbnRoSW5kZXg6IDEwLCBhbW91bnQ6IDE2MTAuNzAgfSwNCiAgICAgICAgICAgIHsgbW9udGhJbmRleDogMTEsIGFtb3VudDogMTU4MC4zNSB9LA0KICAgICAgICAgICAgeyBtb250aEluZGV4OiAxMiwgYW1vdW50OiAxNzIwLjk1IH0NCiAgICAgICAgICBdDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB5ZWFyOiAnMjAyNCcsDQogICAgICAgICAgbW9udGhseVJlc3VsdFZvTGlzdDogWw0KICAgICAgICAgICAgeyBtb250aEluZGV4OiAxLCBhbW91bnQ6IDEzMjAuODAgfSwNCiAgICAgICAgICAgIHsgbW9udGhJbmRleDogMiwgYW1vdW50OiAxNDUwLjYwIH0sDQogICAgICAgICAgICB7IG1vbnRoSW5kZXg6IDMsIGFtb3VudDogMTI4MC40MCB9LA0KICAgICAgICAgICAgeyBtb250aEluZGV4OiA0LCBhbW91bnQ6IDE1NDAuNzAgfSwNCiAgICAgICAgICAgIHsgbW9udGhJbmRleDogNSwgYW1vdW50OiAxNDgwLjIwIH0sDQogICAgICAgICAgICB7IG1vbnRoSW5kZXg6IDYsIGFtb3VudDogMTYyMC41MCB9LA0KICAgICAgICAgICAgeyBtb250aEluZGV4OiA3LCBhbW91bnQ6IDE3NTAuMzAgfSwNCiAgICAgICAgICAgIHsgbW9udGhJbmRleDogOCwgYW1vdW50OiAxNTgwLjkwIH0sDQogICAgICAgICAgICB7IG1vbnRoSW5kZXg6IDksIGFtb3VudDogMTQ5MC42MCB9LA0KICAgICAgICAgICAgeyBtb250aEluZGV4OiAxMCwgYW1vdW50OiAxNzEwLjQwIH0sDQogICAgICAgICAgICB7IG1vbnRoSW5kZXg6IDExLCBhbW91bnQ6IDE2ODAuODAgfSwNCiAgICAgICAgICAgIHsgbW9udGhJbmRleDogMTIsIGFtb3VudDogMTgyMC4yMCB9DQogICAgICAgICAgXQ0KICAgICAgICB9DQogICAgICBdDQogICAgfSwNCg0KICAgIGFzeW5jIGZldGNoUmVhbFRpbWVJbnZlbnRvcnlEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldFJlYWxUaW1lQW1vdW50KCkNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoUmVhbFRpbWVJbnZlbnRvcnlEYXRhIC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5yZWFsVGltZUludmVudG9yeURhdGEgPSByZXNwb25zZS5kYXRhIHx8IFtdDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoUmVhbFRpbWVJbnZlbnRvcnlEYXRhIC0g6K6+572u55qE5pWw5o2uOicsIHRoaXMucmVhbFRpbWVJbnZlbnRvcnlEYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWunuaXtuW6k+WtmOaVsOaNruWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNricsIHJlc3BvbnNlKQ0KICAgICAgICAgIC8vIOS9v+eUqOaooeaLn+aVsOaNrg0KICAgICAgICAgIHRoaXMucmVhbFRpbWVJbnZlbnRvcnlEYXRhID0gdGhpcy5nZXRNb2NrUmVhbFRpbWVEYXRhKCkNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5a6e5pe25bqT5a2Y5pWw5o2u5aSx6LSl77yM5L2/55So5qih5ouf5pWw5o2uOicsIGVycm9yKQ0KICAgICAgICAvLyDkvb/nlKjmqKHmi5/mlbDmja4NCiAgICAgICAgdGhpcy5yZWFsVGltZUludmVudG9yeURhdGEgPSB0aGlzLmdldE1vY2tSZWFsVGltZURhdGEoKQ0KICAgICAgfQ0KDQogICAgICAvLyDmlbDmja7ojrflj5blrozmiJDlkI7ph43mlrDliJ3lp4vljJblm77ooagNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5pbml0UmVhbFRpbWVJbnZlbnRvcnlDaGFydCgpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDnlJ/miJDmqKHmi5/lrp7ml7blupPlrZjmlbDmja4NCiAgICBnZXRNb2NrUmVhbFRpbWVEYXRhKCkgew0KICAgICAgcmV0dXJuIFsNCiAgICAgICAgew0KICAgICAgICAgIG1hdGVyaWFsVHlwZTogJ0EnLA0KICAgICAgICAgIG1hdGVyaWFsTmFtZTogJ+mAmueUqOWkh+S7ticsDQogICAgICAgICAgY2VudGVySW52ZW50b3J5QW1vdW50OiAxMjUwLjMwLA0KICAgICAgICAgIG1hY2hpbmVTaWRlSW52ZW50b3J5QW1vdW50OiAzODAuNTAsDQogICAgICAgICAgdG90YWxJbnZlbnRvcnlBbW91bnQ6IDE2MzAuODANCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG1hdGVyaWFsVHlwZTogJ0InLA0KICAgICAgICAgIG1hdGVyaWFsTmFtZTogJ+S4k+eUqOWkh+S7ticsDQogICAgICAgICAgY2VudGVySW52ZW50b3J5QW1vdW50OiA5ODAuNzUsDQogICAgICAgICAgbWFjaGluZVNpZGVJbnZlbnRvcnlBbW91bnQ6IDQyMC4yNSwNCiAgICAgICAgICB0b3RhbEludmVudG9yeUFtb3VudDogMTQwMS4wMA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbWF0ZXJpYWxUeXBlOiAnQycsDQogICAgICAgICAgbWF0ZXJpYWxOYW1lOiAn5p2Q5paZ57G7JywNCiAgICAgICAgICBjZW50ZXJJbnZlbnRvcnlBbW91bnQ6IDIxNTAuNjAsDQogICAgICAgICAgbWFjaGluZVNpZGVJbnZlbnRvcnlBbW91bnQ6IDY1MC40MCwNCiAgICAgICAgICB0b3RhbEludmVudG9yeUFtb3VudDogMjgwMS4wMA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbWF0ZXJpYWxUeXBlOiAnRCcsDQogICAgICAgICAgbWF0ZXJpYWxOYW1lOiAn5Y6f5p2Q5paZJywNCiAgICAgICAgICBjZW50ZXJJbnZlbnRvcnlBbW91bnQ6IDMyMDAuOTAsDQogICAgICAgICAgbWFjaGluZVNpZGVJbnZlbnRvcnlBbW91bnQ6IDg5MC4xMCwNCiAgICAgICAgICB0b3RhbEludmVudG9yeUFtb3VudDogNDA5MS4wMA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbWF0ZXJpYWxUeXBlOiAnRScsDQogICAgICAgICAgbWF0ZXJpYWxOYW1lOiAn6L6F6ICQ5p2QJywNCiAgICAgICAgICBjZW50ZXJJbnZlbnRvcnlBbW91bnQ6IDE1ODAuNDAsDQogICAgICAgICAgbWFjaGluZVNpZGVJbnZlbnRvcnlBbW91bnQ6IDMyMC42MCwNCiAgICAgICAgICB0b3RhbEludmVudG9yeUFtb3VudDogMTkwMS4wMA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbWF0ZXJpYWxUeXBlOiAnRycsDQogICAgICAgICAgbWF0ZXJpYWxOYW1lOiAn5Yqe5YWsJywNCiAgICAgICAgICBjZW50ZXJJbnZlbnRvcnlBbW91bnQ6IDE1MC4yMCwNCiAgICAgICAgICBtYWNoaW5lU2lkZUludmVudG9yeUFtb3VudDogNTAuODAsDQogICAgICAgICAgdG90YWxJbnZlbnRvcnlBbW91bnQ6IDIwMS4wMA0KICAgICAgICB9DQogICAgICBdDQogICAgfSwNCg0KICAgIGFzeW5jIGZldGNoQ29raW5nQ29hbEludmVudG9yeURhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuZ2V0Q29raW5nQ29hbEFtb3VudCgpDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaENva2luZ0NvYWxJbnZlbnRvcnlEYXRhIC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5jb2tpbmdDb2FsSW52ZW50b3J5RGF0YSA9IHJlc3BvbnNlLmRhdGEgfHwgW10NCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hDb2tpbmdDb2FsSW52ZW50b3J5RGF0YSAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLmNva2luZ0NvYWxJbnZlbnRvcnlEYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluefv+eEpueFpOW6k+WtmOaVsOaNruWksei0pScsIHJlc3BvbnNlKQ0KICAgICAgICAgIHRoaXMuY29raW5nQ29hbEludmVudG9yeURhdGEgPSBbXQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnn7/nhKbnhaTlupPlrZjmlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuY29raW5nQ29hbEludmVudG9yeURhdGEgPSBbXQ0KICAgICAgfQ0KDQogICAgICAvLyDmlbDmja7ojrflj5blrozmiJDlkI7ph43mlrDliJ3lp4vljJblm77ooagNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5pbml0Q29raW5nQ29hbEludmVudG9yeUNoYXJ0KCkNCiAgICAgIH0pDQogICAgfSwNCg0KDQoNCiAgICAvLyDnianmlpnlhaXlupPnu5/orqHnm7jlhbPmlrnms5UNCiAgICBhc3luYyBoYW5kbGVNYXRlcmlhbENhdGVnb3J5Q2hhbmdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+eJqeaWmeexu+WIq+WPmOWMljonLCB0aGlzLnNlbGVjdGVkTWF0ZXJpYWxDYXRlZ29yeSkNCiAgICAgIHRoaXMuc2VsZWN0ZWRNYXRlcmlhbEl0ZW0gPSAnJyAvLyDph43nva7nrKzkuozkuKrkuIvmi4nmoYYNCiAgICAgIGF3YWl0IHRoaXMudXBkYXRlTWF0ZXJpYWxJdGVtT3B0aW9ucygpDQogICAgICBhd2FpdCB0aGlzLmZldGNoTWF0ZXJpYWxTdGF0aXN0aWNzRGF0YSgpDQogICAgfSwNCg0KICAgIGFzeW5jIGhhbmRsZU1hdGVyaWFsSXRlbUNoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfnianmlpnpobnnm67lj5jljJY6JywgdGhpcy5zZWxlY3RlZE1hdGVyaWFsSXRlbSkNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hNYXRlcmlhbFN0YXRpc3RpY3NEYXRhKCkNCiAgICB9LA0KDQogICAgYXN5bmMgdXBkYXRlTWF0ZXJpYWxJdGVtT3B0aW9ucygpIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkTWF0ZXJpYWxDYXRlZ29yeSA9PT0gJzEnKSB7DQogICAgICAgIC8vIOWkp+exu++8muWPquacieWFqOmDqOmAiemhuQ0KICAgICAgICB0aGlzLm1hdGVyaWFsSXRlbU9wdGlvbnMgPSBbXQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5Lit57G744CB57uG57G744CB5Y+257G777ya6I635Y+W5a+55bqU55qE6YCJ6aG5DQogICAgICAgIGNvbnN0IGl0ZW1UeXBlID0gcGFyc2VJbnQodGhpcy5zZWxlY3RlZE1hdGVyaWFsQ2F0ZWdvcnkpIC0gMSAvLyAxLT4wLCAyLT4xLCAzLT4yLCA0LT4zDQogICAgICAgIHRyeSB7DQogICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldEl0ZW1UeXBlTGlzdChpdGVtVHlwZSkNCiAgICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgICB0aGlzLm1hdGVyaWFsSXRlbU9wdGlvbnMgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMubWF0ZXJpYWxJdGVtT3B0aW9ucyA9IFtdDQogICAgICAgICAgfQ0KICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueJqeaWmemhueebrumAiemhueWksei0pTonLCBlcnJvcikNCiAgICAgICAgICB0aGlzLm1hdGVyaWFsSXRlbU9wdGlvbnMgPSBbXQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIGFzeW5jIGZldGNoTWF0ZXJpYWxTdGF0aXN0aWNzRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICBpdGVtVHlwZTogcGFyc2VJbnQodGhpcy5zZWxlY3RlZE1hdGVyaWFsQ2F0ZWdvcnkpLA0KICAgICAgICAgIGRpbWVuc2lvblR5cGU6IHRoaXMuY3VycmVudERpbWVuc2lvblR5cGUNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWmguaenOmAieaLqeS6huWFt+S9k+eJqeaWmemhueebru+8jOa3u+WKoGl0ZW1JZOWPguaVsA0KICAgICAgICBpZiAodGhpcy5zZWxlY3RlZE1hdGVyaWFsSXRlbSAmJiB0aGlzLnNlbGVjdGVkTWF0ZXJpYWxJdGVtICE9PSAnJykgew0KICAgICAgICAgIHBhcmFtcy5pdGVtSWQgPSB0aGlzLnNlbGVjdGVkTWF0ZXJpYWxJdGVtDQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hNYXRlcmlhbFN0YXRpc3RpY3NEYXRhIC0g6K+35rGC5Y+C5pWwOicsIHBhcmFtcykNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldE1hdGVyaWFsTGlzdChwYXJhbXMpDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaE1hdGVyaWFsU3RhdGlzdGljc0RhdGEgLSDlrozmlbTlk43lupQ6JywgcmVzcG9uc2UpDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICB0aGlzLm1hdGVyaWFsU3RhdGlzdGljc0RhdGEgPSByZXNwb25zZS5kYXRhIHx8IFtdDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoTWF0ZXJpYWxTdGF0aXN0aWNzRGF0YSAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLm1hdGVyaWFsU3RhdGlzdGljc0RhdGEpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W54mp5paZ57uf6K6h5pWw5o2u5aSx6LSl77yM5L2/55So5qih5ouf5pWw5o2uJywgcmVzcG9uc2UpDQogICAgICAgICAgdGhpcy5tYXRlcmlhbFN0YXRpc3RpY3NEYXRhID0gdGhpcy5nZXRNb2NrTWF0ZXJpYWxTdGF0aXN0aWNzRGF0YSgpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueJqeaWmee7n+iuoeaVsOaNruWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNrjonLCBlcnJvcikNCiAgICAgICAgdGhpcy5tYXRlcmlhbFN0YXRpc3RpY3NEYXRhID0gdGhpcy5nZXRNb2NrTWF0ZXJpYWxTdGF0aXN0aWNzRGF0YSgpDQogICAgICB9DQoNCiAgICAgIC8vIOaVsOaNruiOt+WPluWujOaIkOWQjumHjeaWsOWIneWni+WMluWbvuihqA0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLmluaXRNYXRlcmlhbFN0YXRpc3RpY3NDaGFydCgpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDnlJ/miJDmqKHmi5/nianmlpnnu5/orqHmlbDmja4NCiAgICBnZXRNb2NrTWF0ZXJpYWxTdGF0aXN0aWNzRGF0YSgpIHsNCiAgICAgIHJldHVybiBbDQogICAgICAgIHsgaXRlbU5hbWU6ICfpgJrnlKjlpIfku7YnLCBpbkFtdDogMTI1MC4zMCwgYXJyaXZlUmF0ZTogODUuNSB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn5LiT55So5aSH5Lu2JywgaW5BbXQ6IDk4MC43NSwgYXJyaXZlUmF0ZTogNzguMiB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn5p2Q5paZ57G7JywgaW5BbXQ6IDIxNTAuNjAsIGFycml2ZVJhdGU6IDkyLjEgfSwNCiAgICAgICAgeyBpdGVtTmFtZTogJ+WOn+adkOaWmScsIGluQW10OiAzMjAwLjkwLCBhcnJpdmVSYXRlOiA4OC43IH0sDQogICAgICAgIHsgaXRlbU5hbWU6ICfovoXogJDmnZAnLCBpbkFtdDogMTU4MC40MCwgYXJyaXZlUmF0ZTogOTEuMyB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn5Yqe5YWsJywgaW5BbXQ6IDE1MC4yMCwgYXJyaXZlUmF0ZTogOTUuMCB9DQogICAgICBdDQogICAgfSwNCg0KICAgIGFzeW5jIGZldGNoSGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YSgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICBkaW1lbnNpb25UeXBlOiB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlLA0KICAgICAgICAgIGNvZGVUeXBlOiB0aGlzLnNlbGVjdGVkQ29kZVR5cGUsDQogICAgICAgICAgaXRlbVR5cGU6IHRoaXMuc2VsZWN0ZWRJdGVtVHlwZQ0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoSGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YSAtIOivt+axguWPguaVsDonLCBwYXJhbXMpDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5nZXRIaWdoRnJlcXVlbmN5TWF0ZXJpYWxMaXN0KHBhcmFtcykNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoSGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YSAtIOWujOaVtOWTjeW6lDonLCByZXNwb25zZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIHRoaXMuaGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YSA9IHJlc3BvbnNlLmRhdGEgfHwgW10NCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hIaWdoRnJlcXVlbmN5TWF0ZXJpYWxEYXRhIC0g6K6+572u55qE5pWw5o2uOicsIHRoaXMuaGlnaEZyZXF1ZW5jeU1hdGVyaWFsRGF0YSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bpq5jpopHnianmlpnmlbDmja7lpLHotKXvvIzkvb/nlKjmqKHmi5/mlbDmja4nLCByZXNwb25zZSkNCiAgICAgICAgICB0aGlzLmhpZ2hGcmVxdWVuY3lNYXRlcmlhbERhdGEgPSB0aGlzLmdldE1vY2tIaWdoRnJlcXVlbmN5RGF0YSgpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumrmOmikeeJqeaWmeaVsOaNruWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNrjonLCBlcnJvcikNCiAgICAgICAgdGhpcy5oaWdoRnJlcXVlbmN5TWF0ZXJpYWxEYXRhID0gdGhpcy5nZXRNb2NrSGlnaEZyZXF1ZW5jeURhdGEoKQ0KICAgICAgfQ0KDQogICAgICAvLyDmlbDmja7ojrflj5blrozmiJDlkI7ph43mlrDliJ3lp4vljJblm77ooagNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5pbml0TWF0ZXJpYWxDbG91ZCgpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDnlJ/miJDmqKHmi5/pq5jpopHnianmlpnmlbDmja4NCiAgICBnZXRNb2NrSGlnaEZyZXF1ZW5jeURhdGEoKSB7DQogICAgICByZXR1cm4gWw0KICAgICAgICB7IGl0ZW1OYW1lOiAn57KX57KJJywgaW5BbXQ6IDM5MjQ2Ny4yLCBpbk51bTogNTQyMTI5MyB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn57K+57KJJywgaW5BbXQ6IDI4MDM1MC41LCBpbk51bTogNDI1MDE4MCB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn55CD5ZuiJywgaW5BbXQ6IDE5NTIwMC44LCBpbk51bTogMzE4MDk3MCB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn54On57uTJywgaW5BbXQ6IDE1MDQyMC4zLCBpbk51bTogMjg5MDU0MCB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn54Sm54KtJywgaW5BbXQ6IDEyNTY4MC43LCBpbk51bTogMjM1MDIxMCB9LA0KICAgICAgICB7IGl0ZW1OYW1lOiAn54Wk54KtJywgaW5BbXQ6IDk4NzUwLjIsIGluTnVtOiAxOTgwNzYwIH0NCiAgICAgIF0NCiAgICB9LA0KDQogICAgYXN5bmMgaGFuZGxlQ29kZVR5cGVDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn5aSn57G757G75Z6L5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRDb2RlVHlwZSkNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hIaWdoRnJlcXVlbmN5TWF0ZXJpYWxEYXRhKCkNCiAgICB9LA0KDQogICAgYXN5bmMgaGFuZGxlSXRlbVR5cGVDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn57u05bqm5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRJdGVtVHlwZSkNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hIaWdoRnJlcXVlbmN5TWF0ZXJpYWxEYXRhKCkNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5L6b5bqU5ZWG6aOO6Zmp5pWw5o2uDQogICAgYXN5bmMgZmV0Y2hTdXBwbGllclJpc2tEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIHRpbWVGbGFnOiB0aGlzLmdldFRpbWVGbGFnQnlEaW1lbnNpb25UeXBlKHRoaXMuY3VycmVudERpbWVuc2lvblR5cGUpDQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hTdXBwbGllclJpc2tEYXRhIC0g6K+35rGC5Y+C5pWwOicsIHBhcmFtcykNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmdldFB1cmNoYXNlU3VwcFJpc2socGFyYW1zKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hTdXBwbGllclJpc2tEYXRhIC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5zdXBwbGllclJpc2tEYXRhID0gcmVzcG9uc2UuZGF0YSB8fCBbXQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFN1cHBsaWVyUmlza0RhdGEgLSDorr7nva7nmoTmlbDmja46JywgdGhpcy5zdXBwbGllclJpc2tEYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluS+m+W6lOWVhumjjumZqeaVsOaNruWksei0pScsIHJlc3BvbnNlKQ0KICAgICAgICAgIHRoaXMuc3VwcGxpZXJSaXNrRGF0YSA9IFtdDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluS+m+W6lOWVhumjjumZqeaVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5zdXBwbGllclJpc2tEYXRhID0gW10NCiAgICAgIH0NCg0KICAgICAgLy8g5pWw5o2u6I635Y+W5a6M5oiQ5ZCO6YeN5paw5Yid5aeL5YyW5Zu+6KGoDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuaW5pdFN1cHBsaWVyUmlza0NoYXJ0KCkNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWkmuS4queJqeaWmeeahEFJ5Lu35qC86aKE5rWLDQogICAgYXN5bmMgZmV0Y2hNdWx0aXBsZVByaWNlUHJlZGljdGlvbnMobWF0ZXJpYWxOYW1lcykgew0KICAgICAgdGhpcy5wcmVkaWN0aW9uTG9hZGluZyA9IHRydWUNCiAgICAgIHRoaXMucHJpY2VQcmVkaWN0aW9ucyA9IFtdIC8vIOa4heepuuS5i+WJjeeahOmihOa1i+e7k+aenA0KDQogICAgICB0cnkgew0KICAgICAgICAvLyDlubbooYzosIPnlKjmiYDmnInnianmlpnnmoTpooTmtYvmjqXlj6MNCiAgICAgICAgY29uc3QgcHJlZGljdGlvblByb21pc2VzID0gbWF0ZXJpYWxOYW1lcy5tYXAoYXN5bmMgKG1hdGVyaWFsTmFtZSkgPT4gew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgICAgIG1hdGVyaWFsTmFtZTogbWF0ZXJpYWxOYW1lLA0KICAgICAgICAgICAgICBtYXRlcmlhbFR5cGU6ICcxJyAvLyDpu5jorqTkvb/nlKjnn7/nn7PnsbvlnovvvIzlj6/ku6XmoLnmja7pnIDopoHosIPmlbQNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgY29uc29sZS5sb2coYGZldGNoUHJpY2VQcmVkaWN0aW9uIC0gJHttYXRlcmlhbE5hbWV9IOivt+axguWPguaVsDpgLCBwYXJhbXMpDQogICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldE1hdGVyaWFsRnV0dXJlUHJpY2UocGFyYW1zKQ0KICAgICAgICAgICAgY29uc29sZS5sb2coYGZldGNoUHJpY2VQcmVkaWN0aW9uIC0gJHttYXRlcmlhbE5hbWV9IOWujOaVtOWTjeW6lDpgLCByZXNwb25zZSkNCg0KICAgICAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmNvZGUgJiYgcmVzcG9uc2UuY29kZSA9PT0gMjAwICYmIHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgICAgICBtYXRlcmlhbE5hbWU6IG1hdGVyaWFsTmFtZSwNCiAgICAgICAgICAgICAgICBxdWVzdGlvbjogcmVzcG9uc2UuZGF0YS5xdWVzdGlvbiB8fCBg5YWz5LqOJHttYXRlcmlhbE5hbWV955qE5Lu35qC86aKE5rWLYCwNCiAgICAgICAgICAgICAgICBwcmVkaWN0aW9uOiByZXNwb25zZS5kYXRhLmFuc3dlciB8fCByZXNwb25zZS5kYXRhLnByZWRpY3Rpb24gfHwgcmVzcG9uc2UubXNnLA0KICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHJlc3BvbnNlLmRhdGEuc3VjY2VzcyAhPT0gZmFsc2UNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihg6I635Y+WJHttYXRlcmlhbE5hbWV95Lu35qC86aKE5rWL5pWw5o2u5aSx6LSlYCwgcmVzcG9uc2UpDQogICAgICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICAgICAgbWF0ZXJpYWxOYW1lOiBtYXRlcmlhbE5hbWUsDQogICAgICAgICAgICAgICAgcXVlc3Rpb246IGDlhbPkuo4ke21hdGVyaWFsTmFtZX3nmoTku7fmoLzpooTmtYtgLA0KICAgICAgICAgICAgICAgIHByZWRpY3Rpb246IGDojrflj5Yke21hdGVyaWFsTmFtZX3ku7fmoLzpooTmtYvlpLHotKVgLA0KICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IGZhbHNlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICAgICAgY29uc29sZS5lcnJvcihg6I635Y+WJHttYXRlcmlhbE5hbWV95Lu35qC86aKE5rWL5pWw5o2u5aSx6LSlOmAsIGVycm9yKQ0KICAgICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgICAgbWF0ZXJpYWxOYW1lOiBtYXRlcmlhbE5hbWUsDQogICAgICAgICAgICAgIHF1ZXN0aW9uOiBg5YWz5LqOJHttYXRlcmlhbE5hbWV955qE5Lu35qC86aKE5rWLYCwNCiAgICAgICAgICAgICAgcHJlZGljdGlvbjogYOiOt+WPliR7bWF0ZXJpYWxOYW1lfeS7t+agvOmihOa1i+Wksei0pe+8miR7ZXJyb3IubWVzc2FnZX1gLA0KICAgICAgICAgICAgICBzdWNjZXNzOiBmYWxzZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCg0KICAgICAgICAvLyDnrYnlvoXmiYDmnInpooTmtYvnu5PmnpwNCiAgICAgICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IFByb21pc2UuYWxsKHByZWRpY3Rpb25Qcm9taXNlcykNCiAgICAgICAgdGhpcy5wcmljZVByZWRpY3Rpb25zID0gcmVzdWx0cw0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hNdWx0aXBsZVByaWNlUHJlZGljdGlvbnMgLSDorr7nva7nmoTpooTmtYvmlbDmja46JywgdGhpcy5wcmljZVByZWRpY3Rpb25zKQ0KDQogICAgICAgIGNvbnN0IHN1Y2Nlc3NDb3VudCA9IHJlc3VsdHMuZmlsdGVyKHIgPT4gci5zdWNjZXNzKS5sZW5ndGgNCiAgICAgICAgY29uc3QgdG90YWxDb3VudCA9IHJlc3VsdHMubGVuZ3RoDQoNCiAgICAgICAgaWYgKHN1Y2Nlc3NDb3VudCA+IDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOaIkOWKn+iOt+WPliR7c3VjY2Vzc0NvdW50fS8ke3RvdGFsQ291bnR95Liq54mp5paZ55qE5Lu35qC86aKE5rWLYCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmiYDmnInnianmlpnnmoTku7fmoLzpooTmtYvojrflj5blpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmibnph4/ojrflj5bku7fmoLzpooTmtYvmlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aJuemHj+iOt+WPluS7t+agvOmihOa1i+Wksei0pe+8micgKyBlcnJvci5tZXNzYWdlKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5wcmVkaWN0aW9uTG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOiOt+WPlueJqeaWmeWQjeensOWIl+ihqA0KICAgIGFzeW5jIGZldGNoTWF0ZXJpYWxOYW1lTGlzdCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICBjYXRlZ29yeTogcGFyc2VJbnQodGhpcy5zZWxlY3RlZE1hdGVyaWFsQ2F0ZWdvcnkpDQogICAgICAgIH0NCg0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldE1hdGVyaWFsTmFtZUxpc3QocGFyYW1zKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hNYXRlcmlhbE5hbWVMaXN0IC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICB0aGlzLm1hdGVyaWFsTmFtZU9wdGlvbnMgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoTWF0ZXJpYWxOYW1lTGlzdCAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLm1hdGVyaWFsTmFtZU9wdGlvbnMpDQoNCiAgICAgICAgICAvLyDorr7nva7pu5jorqTpgInkuK1QQuWdl++8jOWmguaenOWtmOWcqOeahOivnQ0KICAgICAgICAgIGNvbnN0IHBiTWF0ZXJpYWwgPSB0aGlzLm1hdGVyaWFsTmFtZU9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0uaXRlbU5hbWUgPT09ICdQQuWdlycpDQogICAgICAgICAgaWYgKHBiTWF0ZXJpYWwpIHsNCiAgICAgICAgICAgIHRoaXMuc2VsZWN0ZWRNYXRlcmlhbCA9ICdQQuWdlycNCiAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMubWF0ZXJpYWxOYW1lT3B0aW9ucy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAvLyDlpoLmnpzmsqHmnIlQQuWdl++8jOmAieaLqeesrOS4gOS4qg0KICAgICAgICAgICAgdGhpcy5zZWxlY3RlZE1hdGVyaWFsID0gdGhpcy5tYXRlcmlhbE5hbWVPcHRpb25zWzBdLml0ZW1OYW1lDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g6I635Y+W5Lu35qC85pWw5o2uDQogICAgICAgICAgdGhpcy5mZXRjaFByaWNlQW5kU3RvcmVEYXRhKCkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnianmlpnlkI3np7DliJfooajlpLHotKUnLCByZXNwb25zZSkNCiAgICAgICAgICB0aGlzLm1hdGVyaWFsTmFtZU9wdGlvbnMgPSBbXQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnianmlpnlkI3np7DliJfooajlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMubWF0ZXJpYWxOYW1lT3B0aW9ucyA9IFtdDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOiOt+WPlueJqeaWmeS7t+agvOWSjOmHh+i0remHj+aVsOaNrg0KICAgIGFzeW5jIGZldGNoUHJpY2VBbmRTdG9yZURhdGEoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgZGltZW5zaW9uVHlwZTogdGhpcy5jdXJyZW50RGltZW5zaW9uVHlwZSwNCiAgICAgICAgICBpdGVtTmFtZTogdGhpcy5zZWxlY3RlZE1hdGVyaWFsDQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hQcmljZUFuZFN0b3JlRGF0YSAtIOivt+axguWPguaVsDonLCBwYXJhbXMpDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0UHVyY2hhc2VQcmljZUFuZFN0b3JlKHBhcmFtcykNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoUHJpY2VBbmRTdG9yZURhdGEgLSDlrozmlbTlk43lupQ6JywgcmVzcG9uc2UpDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSAmJiByZXNwb25zZS5kYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLnByaWNlQW5kU3RvcmVEYXRhID0gcmVzcG9uc2UuZGF0YVswXSAvLyDlj5bnrKzkuIDkuKrlhYPntKANCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hQcmljZUFuZFN0b3JlRGF0YSAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLnByaWNlQW5kU3RvcmVEYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluS7t+agvOWSjOmHh+i0remHj+aVsOaNruWksei0pScsIHJlc3BvbnNlKQ0KICAgICAgICAgIHRoaXMucHJpY2VBbmRTdG9yZURhdGEgPSBudWxsDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluS7t+agvOWSjOmHh+i0remHj+aVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5wcmljZUFuZFN0b3JlRGF0YSA9IG51bGwNCiAgICAgIH0NCg0KICAgICAgLy8g5pWw5o2u6I635Y+W5a6M5oiQ5ZCO6YeN5paw5Yid5aeL5YyW5Lu35qC86LaL5Yq/5Zu+DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuaW5pdFByaWNlVHJlbmRDaGFydCgpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbnianotYTnsbvlnovliIfmjaINCiAgICBhc3luYyBoYW5kbGVNYXRlcmlhbENhdGVnb3J5VHlwZUNoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfnianotYTnsbvlnovlj5jljJY6JywgdGhpcy5zZWxlY3RlZE1hdGVyaWFsQ2F0ZWdvcnkpDQogICAgICAvLyDph43mlrDojrflj5bnianmlpnlkI3np7DliJfooagNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hNYXRlcmlhbE5hbWVMaXN0KCkNCiAgICB9LA0KDQogICAgLy8g5aSE55CG54mp5paZ6YCJ5oup5Y+Y5YyWDQogICAgYXN5bmMgaGFuZGxlTWF0ZXJpYWxDaGFuZ2UoKSB7DQogICAgICBjb25zb2xlLmxvZygn54mp5paZ6YCJ5oup5Y+Y5YyWOicsIHRoaXMuc2VsZWN0ZWRNYXRlcmlhbCkNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hQcmljZUFuZFN0b3JlRGF0YSgpDQogICAgICAvLyDkuI3lho3oh6rliqjop6blj5FBSemihOa1i++8jOetieeUqOaIt+eCueWHu+aMiemSruWQjuWGjeinpuWPkQ0KICAgIH0sDQoNCiAgICBjYWxjdWxhdGVSZWFsVGltZUludmVudG9yeVRvdGFsKCkgew0KICAgICAgbGV0IHRvdGFsID0gMA0KICAgICAgaWYgKHRoaXMucmVhbFRpbWVJbnZlbnRvcnlEYXRhICYmIHRoaXMucmVhbFRpbWVJbnZlbnRvcnlEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5yZWFsVGltZUludmVudG9yeURhdGEuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICB0b3RhbCArPSBwYXJzZUZsb2F0KGl0ZW0udG90YWxJbnZlbnRvcnlBbW91bnQpIHx8IDANCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIHJldHVybiB0b3RhbC50b0ZpeGVkKDIpDQogICAgfSwNCg0KICAgIGNhbGN1bGF0ZUNva2luZ0NvYWxUb3RhbCgpIHsNCiAgICAgIGxldCB0b3RhbCA9IDANCiAgICAgIGlmICh0aGlzLmNva2luZ0NvYWxJbnZlbnRvcnlEYXRhICYmIHRoaXMuY29raW5nQ29hbEludmVudG9yeURhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAvLyDmib7liLDmiYDmnInmlbDmja7kuK3nmoTmnIDmlrDml6XmnJ8NCiAgICAgICAgbGV0IGxhdGVzdERhdGUgPSAnJw0KICAgICAgICB0aGlzLmNva2luZ0NvYWxJbnZlbnRvcnlEYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0ucHVyY2hhc2VDb2tpbmdEYWlseURldGFpbExpc3QgJiYgaXRlbS5wdXJjaGFzZUNva2luZ0RhaWx5RGV0YWlsTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBpdGVtLnB1cmNoYXNlQ29raW5nRGFpbHlEZXRhaWxMaXN0LmZvckVhY2goZGV0YWlsID0+IHsNCiAgICAgICAgICAgICAgaWYgKGRldGFpbC5pbnN0b2NrRGF0ZSA+IGxhdGVzdERhdGUpIHsNCiAgICAgICAgICAgICAgICBsYXRlc3REYXRlID0gZGV0YWlsLmluc3RvY2tEYXRlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KDQogICAgICAgIC8vIOiuoeeul+acgOaWsOaXpeacn+WQhOS4queJqeaWmeeahOW6k+WtmOmHj+WQiOiuoQ0KICAgICAgICB0aGlzLmNva2luZ0NvYWxJbnZlbnRvcnlEYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0ucHVyY2hhc2VDb2tpbmdEYWlseURldGFpbExpc3QgJiYgaXRlbS5wdXJjaGFzZUNva2luZ0RhaWx5RGV0YWlsTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBjb25zdCBsYXRlc3REZXRhaWwgPSBpdGVtLnB1cmNoYXNlQ29raW5nRGFpbHlEZXRhaWxMaXN0LmZpbmQoZGV0YWlsID0+IGRldGFpbC5pbnN0b2NrRGF0ZSA9PT0gbGF0ZXN0RGF0ZSkNCiAgICAgICAgICAgIGlmIChsYXRlc3REZXRhaWwpIHsNCiAgICAgICAgICAgICAgdG90YWwgKz0gcGFyc2VGbG9hdChsYXRlc3REZXRhaWwuaW52UXR5KSB8fCAwDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgcmV0dXJuICh0b3RhbCAvIDEwMDAwKS50b0ZpeGVkKDIpIC8vIOi9rOaNouS4uuS4h+WQqA0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbnn7/nhKbnhaTnsbvlnovkuIvmi4nmoYblj5jljJYNCiAgICBhc3luYyBoYW5kbGVDb2tpbmdDb2FsVHlwZUNoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfnn7/nhKbnhaTnsbvlnovlj5jljJY6JywgdGhpcy5zZWxlY3RlZENva2luZ0NvYWxUeXBlKQ0KICAgICAgLy8g6YeN5paw5Yid5aeL5YyW5Zu+6KGo5Lul5bqU55So6L+H5rukDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuaW5pdENva2luZ0NvYWxJbnZlbnRvcnlDaGFydCgpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDmnLrml4HlupPlrZjnm7jlhbPmlrnms5UNCiAgICAvLyDojrflj5bliIbljoLpgInpobnliJfooagNCiAgICBhc3luYyBmZXRjaEZhY3RvcnlEZXBPcHRpb25zKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXREZXBOYW1lTGlzdCgpDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaEZhY3RvcnlEZXBPcHRpb25zIC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICB0aGlzLmZhY3RvcnlEZXBPcHRpb25zID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaEZhY3RvcnlEZXBPcHRpb25zIC0g6K6+572u55qE5pWw5o2uOicsIHRoaXMuZmFjdG9yeURlcE9wdGlvbnMpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5YiG5Y6C6YCJ6aG55YiX6KGo5aSx6LSlJywgcmVzcG9uc2UpDQogICAgICAgICAgdGhpcy5mYWN0b3J5RGVwT3B0aW9ucyA9IFtdDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWIhuWOgumAiemhueWIl+ihqOWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5mYWN0b3J5RGVwT3B0aW9ucyA9IFtdDQogICAgICB9DQoNCiAgICAgIC8vIOiOt+WPlum7mOiupOaVsOaNru+8iOWFqOmDqO+8iQ0KICAgICAgdGhpcy5mZXRjaEZhY3RvcnlTdG9ja0RhdGEoKQ0KICAgIH0sDQoNCiAgICAvLyDlpITnkIbliIbljoLpgInmi6nlj5jljJYNCiAgICBhc3luYyBoYW5kbGVGYWN0b3J5RGVwQ2hhbmdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+WIhuWOgumAieaLqeWPmOWMljonLCB0aGlzLnNlbGVjdGVkRmFjdG9yeURlcCkNCiAgICAgIGF3YWl0IHRoaXMuZmV0Y2hGYWN0b3J5U3RvY2tEYXRhKCkNCiAgICB9LA0KDQogICAgLy8g5aSE55CG54mp5paZ57G75Z6L6YCJ5oup5Y+Y5YyWDQogICAgYXN5bmMgaGFuZGxlRmFjdG9yeU1hdGVyaWFsVHlwZUNoYW5nZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCfnianmlpnnsbvlnovpgInmi6nlj5jljJY6JywgdGhpcy5zZWxlY3RlZEZhY3RvcnlNYXRlcmlhbFR5cGUpDQogICAgICAvLyDph43mlrDliJ3lp4vljJblm77ooajku6XlupTnlKjnrZvpgIkNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5pbml0RmFjdG9yeVN0b2NrQ2hhcnQoKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5py65peB5bqT5a2Y5pWw5o2uDQogICAgYXN5bmMgZmV0Y2hGYWN0b3J5U3RvY2tEYXRhKCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgZGVwTmFtZSA9IHRoaXMuc2VsZWN0ZWRGYWN0b3J5RGVwIHx8ICcnIC8vIOepuuWtl+espuS4suihqOekuuWFqOmDqA0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hGYWN0b3J5U3RvY2tEYXRhIC0g6K+35rGC5Y+C5pWwOicsIGRlcE5hbWUpDQoNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRMaXN0TW9udGhseShkZXBOYW1lKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hGYWN0b3J5U3RvY2tEYXRhIC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICB0aGlzLmZhY3RvcnlTdG9ja0RhdGEgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoRmFjdG9yeVN0b2NrRGF0YSAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLmZhY3RvcnlTdG9ja0RhdGEpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5py65peB5bqT5a2Y5pWw5o2u5aSx6LSlJywgcmVzcG9uc2UpDQogICAgICAgICAgdGhpcy5mYWN0b3J5U3RvY2tEYXRhID0gW10NCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5py65peB5bqT5a2Y5pWw5o2u5aSx6LSlOicsIGVycm9yKQ0KICAgICAgICB0aGlzLmZhY3RvcnlTdG9ja0RhdGEgPSBbXQ0KICAgICAgfQ0KDQogICAgICAvLyDmlbDmja7ojrflj5blrozmiJDlkI7ph43mlrDliJ3lp4vljJblm77ooagNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5pbml0RmFjdG9yeVN0b2NrQ2hhcnQoKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5paw5aKe5pa55rOV77ya5aSE55CG6YeH6LSt6YeP5puy57q/54mp5paZ57G75Z6L5Y+Y5YyWDQogICAgYXN5bmMgaGFuZGxlUHVyY2hhc2VBbW91bnRDYXRlZ29yaWVzQ2hhbmdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+mHh+i0remHj+absue6v+eJqeaWmeexu+Wei+WPmOWMljonLCB0aGlzLnB1cmNoYXNlQW1vdW50Q2F0ZWdvcmllcykNCiAgICAgIHRoaXMuc2VsZWN0ZWRQdXJjaGFzZUFtb3VudE1hdGVyaWFscyA9IFtdIC8vIOmHjee9rumAieS4reeahOeJqeaWmQ0KICAgICAgYXdhaXQgdGhpcy5mZXRjaFB1cmNoYXNlQW1vdW50TWF0ZXJpYWxMaXN0KCkNCiAgICB9LA0KDQogICAgLy8g5paw5aKe5pa55rOV77ya5aSE55CG5biC5Zy65Lu35puy57q/54mp5paZ57G75Z6L5Y+Y5YyWDQogICAgYXN5bmMgaGFuZGxlTWFya2V0UHJpY2VDYXRlZ29yaWVzQ2hhbmdlKCkgew0KICAgICAgY29uc29sZS5sb2coJ+W4guWcuuS7t+absue6v+eJqeaWmeexu+Wei+WPmOWMljonLCB0aGlzLm1hcmtldFByaWNlQ2F0ZWdvcmllcykNCiAgICAgIHRoaXMuc2VsZWN0ZWRNYXJrZXRQcmljZU1hdGVyaWFscyA9IFtdIC8vIOmHjee9rumAieS4reeahOeJqeaWmQ0KICAgICAgYXdhaXQgdGhpcy5mZXRjaE1hcmtldFByaWNlTWF0ZXJpYWxMaXN0KCkNCiAgICB9LA0KDQogICAgLy8g5paw5aKe5pa55rOV77ya6I635Y+W6YeH6LSt6YeP5puy57q/54mp5paZ5YiX6KGoDQogICAgYXN5bmMgZmV0Y2hQdXJjaGFzZUFtb3VudE1hdGVyaWFsTGlzdCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICBjYXRlZ29yaWVzOiB0aGlzLnB1cmNoYXNlQW1vdW50Q2F0ZWdvcmllcywNCiAgICAgICAgICBjdXJ2ZVR5cGU6IDIsIC8vIOmHh+i0remHj+absue6vw0KICAgICAgICAgIGRpbWVuc2lvblR5cGU6IHRoaXMuY3VycmVudERpbWVuc2lvblR5cGUNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFB1cmNoYXNlQW1vdW50TWF0ZXJpYWxMaXN0IC0g6K+35rGC5Y+C5pWwOicsIHBhcmFtcykNCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRNYXRlcmlhbE5hbWVMaXN0RnJvbU5ld1RhYmxlcyhwYXJhbXMpDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFB1cmNoYXNlQW1vdW50TWF0ZXJpYWxMaXN0IC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICB0aGlzLnB1cmNoYXNlQW1vdW50TWF0ZXJpYWxPcHRpb25zID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFB1cmNoYXNlQW1vdW50TWF0ZXJpYWxMaXN0IC0g6K6+572u55qE5pWw5o2uOicsIHRoaXMucHVyY2hhc2VBbW91bnRNYXRlcmlhbE9wdGlvbnMpDQoNCiAgICAgICAgICAvLyDlj6rlnKjpobXpnaLliJ3lp4vljJbml7bvvIjnrKzkuIDmrKHliqDovb3kuJTml6DpgInkuK3nianmlpnml7bvvInorr7nva7pu5jorqTpgInkuK1QQuWdlw0KICAgICAgICAgIGlmICh0aGlzLnNlbGVjdGVkUHVyY2hhc2VBbW91bnRNYXRlcmlhbHMubGVuZ3RoID09PSAwICYmICF0aGlzLmhhc0luaXRpYWxpemVkUHJpY2VDaGFydCkgew0KICAgICAgICAgICAgY29uc3QgcGJNYXRlcmlhbCA9IHRoaXMucHVyY2hhc2VBbW91bnRNYXRlcmlhbE9wdGlvbnMuZmluZChpdGVtID0+IGl0ZW0uaXRlbU5hbWUgPT09ICdQQuWdlycpDQogICAgICAgICAgICBpZiAocGJNYXRlcmlhbCkgew0KICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkUHVyY2hhc2VBbW91bnRNYXRlcmlhbHMgPSBbJ1BC5Z2XJ10NCiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+m7mOiupOmAieS4rVBC5Z2XIC0g6YeH6LSt6YeP5puy57q/JykNCg0KICAgICAgICAgICAgICAvLyDmo4Dmn6XluILlnLrku7fmm7Lnur/mmK/lkKbkuZ/lt7Lnu4/orr7nva7lpb3pu5jorqTlgLzvvIzlpoLmnpzmmK/liJnop6blj5HmlbDmja7ojrflj5YNCiAgICAgICAgICAgICAgdGhpcy5jaGVja0FuZFRyaWdnZXJJbml0aWFsRGF0YUZldGNoKCkNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W6YeH6LSt6YeP5puy57q/54mp5paZ5YiX6KGo5aSx6LSlJywgcmVzcG9uc2UpDQogICAgICAgICAgdGhpcy5wdXJjaGFzZUFtb3VudE1hdGVyaWFsT3B0aW9ucyA9IFtdDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumHh+i0remHj+absue6v+eJqeaWmeWIl+ihqOWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy5wdXJjaGFzZUFtb3VudE1hdGVyaWFsT3B0aW9ucyA9IFtdDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOaWsOWinuaWueazle+8muiOt+WPluW4guWcuuS7t+absue6v+eJqeaWmeWIl+ihqA0KICAgIGFzeW5jIGZldGNoTWFya2V0UHJpY2VNYXRlcmlhbExpc3QoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgICAgY2F0ZWdvcmllczogdGhpcy5tYXJrZXRQcmljZUNhdGVnb3JpZXMsDQogICAgICAgICAgY3VydmVUeXBlOiAxLCAvLyDku7fmoLzmm7Lnur8NCiAgICAgICAgICBkaW1lbnNpb25UeXBlOiB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlDQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hNYXJrZXRQcmljZU1hdGVyaWFsTGlzdCAtIOivt+axguWPguaVsDonLCBwYXJhbXMpDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0TWF0ZXJpYWxOYW1lTGlzdEZyb21OZXdUYWJsZXMocGFyYW1zKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hNYXJrZXRQcmljZU1hdGVyaWFsTGlzdCAtIOWujOaVtOWTjeW6lDonLCByZXNwb25zZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgdGhpcy5tYXJrZXRQcmljZU1hdGVyaWFsT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hNYXJrZXRQcmljZU1hdGVyaWFsTGlzdCAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLm1hcmtldFByaWNlTWF0ZXJpYWxPcHRpb25zKQ0KDQogICAgICAgICAgLy8g5Y+q5Zyo6aG16Z2i5Yid5aeL5YyW5pe277yI56ys5LiA5qyh5Yqg6L295LiU5peg6YCJ5Lit54mp5paZ5pe277yJ6K6+572u6buY6K6k6YCJ5LitUELlnZcNCiAgICAgICAgICBpZiAodGhpcy5zZWxlY3RlZE1hcmtldFByaWNlTWF0ZXJpYWxzLmxlbmd0aCA9PT0gMCAmJiAhdGhpcy5oYXNJbml0aWFsaXplZFByaWNlQ2hhcnQpIHsNCiAgICAgICAgICAgIGNvbnN0IHBiTWF0ZXJpYWwgPSB0aGlzLm1hcmtldFByaWNlTWF0ZXJpYWxPcHRpb25zLmZpbmQoaXRlbSA9PiBpdGVtLml0ZW1OYW1lID09PSAnUELlnZcnKQ0KICAgICAgICAgICAgaWYgKHBiTWF0ZXJpYWwpIHsNCiAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZE1hcmtldFByaWNlTWF0ZXJpYWxzID0gWydQQuWdlyddDQogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfpu5jorqTpgInkuK1QQuWdlyAtIOW4guWcuuS7t+absue6vycpDQoNCiAgICAgICAgICAgICAgLy8g5qOA5p+l6YeH6LSt6YeP5puy57q/5piv5ZCm5Lmf5bey57uP6K6+572u5aW96buY6K6k5YC877yM5aaC5p6c5piv5YiZ6Kem5Y+R5pWw5o2u6I635Y+WDQogICAgICAgICAgICAgIHRoaXMuY2hlY2tBbmRUcmlnZ2VySW5pdGlhbERhdGFGZXRjaCgpDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluW4guWcuuS7t+absue6v+eJqeaWmeWIl+ihqOWksei0pScsIHJlc3BvbnNlKQ0KICAgICAgICAgIHRoaXMubWFya2V0UHJpY2VNYXRlcmlhbE9wdGlvbnMgPSBbXQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bluILlnLrku7fmm7Lnur/nianmlpnliJfooajlpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMubWFya2V0UHJpY2VNYXRlcmlhbE9wdGlvbnMgPSBbXQ0KICAgICAgfQ0KICAgIH0sDQoNCg0KDQogICAgLy8g5paw5aKe5pa55rOV77ya6I635Y+W54mp5paZ6YeH6LSt5Lu35qC85pWw5o2u77yI55So5LqO5paw55qE5Lu35qC86LaL5Yq/5Zu+77yJDQogICAgYXN5bmMgZmV0Y2hQcmljZUFuZFN0b3JlRGF0YUZvck5ld0NoYXJ0KCkgew0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRQdXJjaGFzZUFtb3VudE1hdGVyaWFscy5sZW5ndGggPT09IDAgJiYgdGhpcy5zZWxlY3RlZE1hcmtldFByaWNlTWF0ZXJpYWxzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+iHs+WwkemAieaLqeS4gOS4queJqeaWmScpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0aGlzLmZldGNoaW5nUHJpY2VEYXRhID0gdHJ1ZQ0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5p6E5bu6aXRlbUxpc3QNCiAgICAgICAgY29uc3QgaXRlbUxpc3QgPSBbXQ0KDQogICAgICAgIC8vIOa3u+WKoOmHh+i0remHj+absue6v+eahOeJqeaWmQ0KICAgICAgICB0aGlzLnNlbGVjdGVkUHVyY2hhc2VBbW91bnRNYXRlcmlhbHMuZm9yRWFjaChpdGVtTmFtZSA9PiB7DQogICAgICAgICAgaXRlbUxpc3QucHVzaCh7DQogICAgICAgICAgICBjdXJ2ZVR5cGU6IDIsIC8vIOmHh+i0remHj+absue6vw0KICAgICAgICAgICAgaXRlbU5hbWU6IGl0ZW1OYW1lDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCg0KICAgICAgICAvLyDmt7vliqDluILlnLrku7fmm7Lnur/nmoTnianmlpkNCiAgICAgICAgdGhpcy5zZWxlY3RlZE1hcmtldFByaWNlTWF0ZXJpYWxzLmZvckVhY2goaXRlbU5hbWUgPT4gew0KICAgICAgICAgIGl0ZW1MaXN0LnB1c2goew0KICAgICAgICAgICAgY3VydmVUeXBlOiAxLCAvLyDku7fmoLzmm7Lnur8NCiAgICAgICAgICAgIGl0ZW1OYW1lOiBpdGVtTmFtZQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQoNCiAgICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAgIGRpbWVuc2lvblR5cGU6IHRoaXMuY3VycmVudERpbWVuc2lvblR5cGUsDQogICAgICAgICAgaXRlbUxpc3Q6IGl0ZW1MaXN0DQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hQcmljZUFuZFN0b3JlRGF0YSAtIOivt+axguWPguaVsDonLCBwYXJhbXMpDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0UHVyY2hhc2VQcmljZUFuZFN0b3JlRnJvbU5ld1RhYmxlcyhwYXJhbXMpDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFByaWNlQW5kU3RvcmVEYXRhIC0g5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKQ0KDQogICAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5uZXdQcmljZUFuZFN0b3JlRGF0YSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hQcmljZUFuZFN0b3JlRGF0YSAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLm5ld1ByaWNlQW5kU3RvcmVEYXRhKQ0KDQogICAgICAgICAgLy8g6YeN5paw5riy5p+T5Zu+6KGoDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5pbml0TmV3UHJpY2VUcmVuZENoYXJ0KCkNCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgLy8g6I635Y+W5omA5pyJ6YCJ5Lit54mp5paZ55qE5Y676YeN5YiX6KGoDQogICAgICAgICAgY29uc3QgYWxsU2VsZWN0ZWRNYXRlcmlhbHMgPSBbLi4ubmV3IFNldChbDQogICAgICAgICAgICAuLi50aGlzLnNlbGVjdGVkUHVyY2hhc2VBbW91bnRNYXRlcmlhbHMsDQogICAgICAgICAgICAuLi50aGlzLnNlbGVjdGVkTWFya2V0UHJpY2VNYXRlcmlhbHMNCiAgICAgICAgICBdKV0NCg0KICAgICAgICAgIC8vIOS4uuavj+S4queJqeaWmeiwg+eUqEFJ6aKE5rWL5o6l5Y+jDQogICAgICAgICAgaWYgKGFsbFNlbGVjdGVkTWF0ZXJpYWxzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuZmV0Y2hNdWx0aXBsZVByaWNlUHJlZGljdGlvbnMoYWxsU2VsZWN0ZWRNYXRlcmlhbHMpDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5aaC5p6c5biC5Zy65Lu35puy57q/5pyJ6YCJ5Lit54mp5paZ77yM6I635Y+W55u45Ly854mp5paZ5L+h5oGvDQogICAgICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRNYXJrZXRQcmljZU1hdGVyaWFscy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLmZldGNoU2ltaWxhck1hdGVyaWFscyh0aGlzLnNlbGVjdGVkTWFya2V0UHJpY2VNYXRlcmlhbHMpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOa4heepuuebuOS8vOeJqeaWmeaVsOaNrg0KICAgICAgICAgICAgdGhpcy5zaW1pbGFyTWF0ZXJpYWxzRGF0YSA9IFtdDQogICAgICAgICAgfQ0KDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmlbDmja7ojrflj5bmiJDlip8nKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueJqeaWmemHh+i0reS7t+agvOaVsOaNruWksei0pScsIHJlc3BvbnNlKQ0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluaVsOaNruWksei0pScpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlueJqeaWmemHh+i0reS7t+agvOaVsOaNruWksei0pTonLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5pWw5o2u5aSx6LSl77yaJyArIGVycm9yLm1lc3NhZ2UpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmZldGNoaW5nUHJpY2VEYXRhID0gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6I635Y+W55u45Ly854mp5paZ5L+h5oGvDQogICAgYXN5bmMgZmV0Y2hTaW1pbGFyTWF0ZXJpYWxzKGl0ZW1OYW1lcykgew0KICAgICAgdGhpcy5zaW1pbGFyTWF0ZXJpYWxzTG9hZGluZyA9IHRydWUNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICBpdGVtTmFtZXM6IGl0ZW1OYW1lcw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoU2ltaWxhck1hdGVyaWFscyAtIOivt+axguWPguaVsDonLCBwYXJhbXMpDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbGlzdFNpbWlsYXJCeUl0ZW1OYW1lcyhwYXJhbXMpDQogICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaFNpbWlsYXJNYXRlcmlhbHMgLSDlrozmlbTlk43lupQ6JywgcmVzcG9uc2UpDQoNCiAgICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkgew0KICAgICAgICAgIHRoaXMuc2ltaWxhck1hdGVyaWFsc0RhdGEgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoU2ltaWxhck1hdGVyaWFscyAtIOiuvue9rueahOaVsOaNrjonLCB0aGlzLnNpbWlsYXJNYXRlcmlhbHNEYXRhKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluebuOS8vOeJqeaWmeaVsOaNruWksei0pScsIHJlc3BvbnNlKQ0KICAgICAgICAgIHRoaXMuc2ltaWxhck1hdGVyaWFsc0RhdGEgPSBbXQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnm7jkvLznianmlpnmlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuc2ltaWxhck1hdGVyaWFsc0RhdGEgPSBbXQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5zaW1pbGFyTWF0ZXJpYWxzTG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluaOkuWQjeagt+W8j+exuw0KICAgIGdldFJhbmtDbGFzcyhyYW5rKSB7DQogICAgICBpZiAocmFuayA9PT0gMSkgcmV0dXJuICdyYW5rLWZpcnN0Jw0KICAgICAgaWYgKHJhbmsgPT09IDIpIHJldHVybiAncmFuay1zZWNvbmQnDQogICAgICBpZiAocmFuayA9PT0gMykgcmV0dXJuICdyYW5rLXRoaXJkJw0KICAgICAgcmV0dXJuICdyYW5rLWRlZmF1bHQnDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWVhuWTgeWIhuexu+WQjeensA0KICAgIGdldENhdGVnb3J5TmFtZShjYXRlZ29yeSkgew0KICAgICAgY29uc3QgY2F0ZWdvcnlNYXAgPSB7DQogICAgICAgIDE6ICfnn7/nn7MnLA0KICAgICAgICAyOiAn54Wk54KtJywNCiAgICAgICAgMzogJ+WQiOmHkScsDQogICAgICAgIDQ6ICflup/pkqInDQogICAgICB9DQogICAgICByZXR1cm4gY2F0ZWdvcnlNYXBbY2F0ZWdvcnldIHx8ICfmnKrnn6UnDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluS7t+agvOexu+Wei+WQjeensA0KICAgIGdldFByaWNlVHlwZU5hbWUocHJpY2VUeXBlKSB7DQogICAgICBjb25zdCBwcmljZVR5cGVNYXAgPSB7DQogICAgICAgIDE6ICfnjrDotKfku7cnLA0KICAgICAgICAyOiAn5biC5Zy66YeH6LSt5Yiw5Y6C5Lu3JywNCiAgICAgICAgMzogJ+WFtOa+hOW6n+mSouaUtui0reS7tyjovabov5ApJywNCiAgICAgICAgNDogJ+WFtOa+hOW6n+mSouaUtui0reS7tyjoiLnov5ApJywNCiAgICAgICAgNTogJ+aymemSouW6n+mSouaUtui0reS7tyjovabov5ApJywNCiAgICAgICAgNjogJ+aymemSouW6n+mSouaUtui0reS7tyjoiLnov5ApJw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHByaWNlVHlwZU1hcFtwcmljZVR5cGVdIHx8ICfmnKrnn6UnDQogICAgfSwNCg0KICAgIC8vIOaJk+W8gOWvueavlOW8ueahhg0KICAgIG9wZW5Db21wYXJpc29uRGlhbG9nKGl0ZW0pIHsNCiAgICAgIGNvbnNvbGUubG9nKCdvcGVuQ29tcGFyaXNvbkRpYWxvZyAtIOS8oOWFpeeahGl0ZW3mlbDmja46JywgaXRlbSkNCiAgICAgIHRoaXMuY3VycmVudENvbXBhcmlzb24gPSB7IC4uLml0ZW0gfQ0KICAgICAgY29uc29sZS5sb2coJ29wZW5Db21wYXJpc29uRGlhbG9nIC0g6K6+572u55qEY3VycmVudENvbXBhcmlzb246JywgdGhpcy5jdXJyZW50Q29tcGFyaXNvbikNCiAgICAgIHRoaXMuY29tcGFyaXNvbkRpYWxvZ1Zpc2libGUgPSB0cnVlDQoNCiAgICAgIC8vIOW8ueahhuaJk+W8gOWQjuiOt+WPluWvueavlOaVsOaNrg0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICB0aGlzLmZldGNoQ29tcGFyaXNvbkRhdGEoKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLy8g5YWz6Zet5a+55q+U5by55qGGDQogICAgY2xvc2VDb21wYXJpc29uRGlhbG9nKCkgew0KICAgICAgdGhpcy5jb21wYXJpc29uRGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICB0aGlzLmN1cnJlbnRDb21wYXJpc29uID0ge30NCiAgICAgIHRoaXMuY29tcGFyaXNvblByaWNlRGF0YSA9IG51bGwNCg0KICAgICAgLy8g5riF55CG5Zu+6KGo5a6e5L6LDQogICAgICBpZiAodGhpcy5jb21wYXJpc29uQ2hhcnRJbnN0YW5jZSkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIHRoaXMuY29tcGFyaXNvbkNoYXJ0SW5zdGFuY2UuZGlzcG9zZSgpDQogICAgICAgICAgdGhpcy5jb21wYXJpc29uQ2hhcnRJbnN0YW5jZSA9IG51bGwNCiAgICAgICAgfSBjYXRjaCAoZXJyKSB7DQogICAgICAgICAgY29uc29sZS5lcnJvcign5riF55CG5a+55q+U5Zu+6KGo5a6e5L6L5aSx6LSlOicsIGVycikNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5blr7nmr5TmlbDmja7vvIjni6znq4vlrp7njrDvvIzkuI3ogKblkIjnjrDmnInotovlir/lm77vvIkNCiAgICBhc3luYyBmZXRjaENvbXBhcmlzb25EYXRhKCkgew0KICAgICAgdGhpcy5jb21wYXJpc29uQ2hhcnRMb2FkaW5nID0gdHJ1ZQ0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g5p6E5bu65Lik5Liq54mp5paZ55qE5a+55q+U6K+35rGC77yM5Y+q6I635Y+W5Lu35qC85puy57q/DQogICAgICAgIGNvbnN0IGl0ZW1MaXN0ID0gWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGN1cnZlVHlwZTogMSwgLy8g5Lu35qC85puy57q/DQogICAgICAgICAgICBpdGVtTmFtZTogdGhpcy5jdXJyZW50Q29tcGFyaXNvbi5pdGVtTmFtZQ0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgY3VydmVUeXBlOiAxLCAvLyDku7fmoLzmm7Lnur8NCiAgICAgICAgICAgIGl0ZW1OYW1lOiB0aGlzLmN1cnJlbnRDb21wYXJpc29uLmNvbXBhcmVJdGVtTmFtZQ0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KDQogICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICBkaW1lbnNpb25UeXBlOiB0aGlzLmN1cnJlbnREaW1lbnNpb25UeXBlLA0KICAgICAgICAgIGl0ZW1MaXN0OiBpdGVtTGlzdA0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ2ZldGNoQ29tcGFyaXNvbkRhdGEgLSDor7fmsYLlj4LmlbA6JywgcGFyYW1zKQ0KICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGdldFB1cmNoYXNlUHJpY2VBbmRTdG9yZUZyb21OZXdUYWJsZXMocGFyYW1zKQ0KICAgICAgICBjb25zb2xlLmxvZygnZmV0Y2hDb21wYXJpc29uRGF0YSAtIOWujOaVtOWTjeW6lDonLCByZXNwb25zZSkNCg0KICAgICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7DQogICAgICAgICAgLy8g5a+56L+U5Zue55qE5pWw5o2u6L+b6KGM562b6YCJ77yM56Gu5L+d5Z+65YeG54mp5paZ5ZKM55u45Ly854mp5paZ55qE5oyH5a6a5Lu35qC857G75Z6L6YO96IO96KKr5o+Q5Y+WDQogICAgICAgICAgY29uc3QgZmlsdGVyZWREYXRhID0gW10NCg0KICAgICAgICAgIC8vIOiOt+WPluWfuuWHhueJqeaWmeWSjOebuOS8vOeJqeaWmeeahOebruagh+S7t+agvOexu+Wei+WQjeensA0KICAgICAgICAgIGNvbnN0IGJhc2VQcmljZVR5cGVOYW1lID0gdGhpcy5nZXRQcmljZVR5cGVOYW1lKHRoaXMuY3VycmVudENvbXBhcmlzb24ucHJpY2VUeXBlKQ0KICAgICAgICAgIGNvbnN0IGNvbXBhcmVQcmljZVR5cGVOYW1lID0gdGhpcy5nZXRQcmljZVR5cGVOYW1lKHRoaXMuY3VycmVudENvbXBhcmlzb24uY29tcGFyZVByaWNlVHlwZSkNCg0KICAgICAgICAgIGNvbnNvbGUubG9nKCfnrZvpgInmnaHku7Y6Jywgew0KICAgICAgICAgICAgYmFzZUl0ZW1OYW1lOiB0aGlzLmN1cnJlbnRDb21wYXJpc29uLml0ZW1OYW1lLA0KICAgICAgICAgICAgYmFzZVByaWNlVHlwZU5hbWU6IGJhc2VQcmljZVR5cGVOYW1lLA0KICAgICAgICAgICAgY29tcGFyZUl0ZW1OYW1lOiB0aGlzLmN1cnJlbnRDb21wYXJpc29uLmNvbXBhcmVJdGVtTmFtZSwNCiAgICAgICAgICAgIGNvbXBhcmVQcmljZVR5cGVOYW1lOiBjb21wYXJlUHJpY2VUeXBlTmFtZQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICByZXNwb25zZS5kYXRhLmZvckVhY2gobWF0ZXJpYWxEYXRhID0+IHsNCiAgICAgICAgICAgIGNvbnN0IGZpbHRlcmVkTWF0ZXJpYWxEYXRhID0geyAuLi5tYXRlcmlhbERhdGEgfQ0KDQogICAgICAgICAgICBpZiAoZmlsdGVyZWRNYXRlcmlhbERhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdCkgew0KICAgICAgICAgICAgICAvLyDlj6rkv53nlZnljLnphY3nmoTku7fmoLznsbvlnosNCiAgICAgICAgICAgICAgZmlsdGVyZWRNYXRlcmlhbERhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdCA9IGZpbHRlcmVkTWF0ZXJpYWxEYXRhLnByb2N1cmVtZW50UHJpY2VWb0xpc3QuZmlsdGVyKHByaWNlR3JvdXAgPT4gew0KICAgICAgICAgICAgICAgIGxldCBpc01hdGNoID0gZmFsc2UNCiAgICAgICAgICAgICAgICAvLyDln7rlh4bnianmlpnvvJrljLnphY3nianmlpnlkI3np7Dlkozln7rlh4bku7fmoLznsbvlnosNCiAgICAgICAgICAgICAgICBpZiAobWF0ZXJpYWxEYXRhLml0ZW1OYW1lID09PSB0aGlzLmN1cnJlbnRDb21wYXJpc29uLml0ZW1OYW1lKSB7DQogICAgICAgICAgICAgICAgICBpc01hdGNoID0gcHJpY2VHcm91cC5wcmljZU5hbWUgPT09IGJhc2VQcmljZVR5cGVOYW1lDQogICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg5Z+65YeG54mp5paZWyR7bWF0ZXJpYWxEYXRhLml0ZW1OYW1lfV0g5Lu35qC857G75Z6LWyR7cHJpY2VHcm91cC5wcmljZU5hbWV9XSDnm67moIfnsbvlnotbJHtiYXNlUHJpY2VUeXBlTmFtZX1dIOWMuemFjToke2lzTWF0Y2h9YCkNCiAgICAgICAgICAgICAgICB9DQoNCiAgICAgICAgICAgICAgICBpZihpc01hdGNoKXsNCiAgICAgICAgICAgICAgICAgIHJldHVybiBpc01hdGNoDQogICAgICAgICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICAgICAgICBpZiAobWF0ZXJpYWxEYXRhLml0ZW1OYW1lID09PSB0aGlzLmN1cnJlbnRDb21wYXJpc29uLmNvbXBhcmVJdGVtTmFtZSkgew0KICAgICAgICAgICAgICAgICAgICBjb25zdCBpc01hdGNoID0gcHJpY2VHcm91cC5wcmljZU5hbWUgPT09IGNvbXBhcmVQcmljZVR5cGVOYW1lDQogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDnm7jkvLznianmlplbJHttYXRlcmlhbERhdGEuaXRlbU5hbWV9XSDku7fmoLznsbvlnotbJHtwcmljZUdyb3VwLnByaWNlTmFtZX1dIOebruagh+exu+Wei1ske2NvbXBhcmVQcmljZVR5cGVOYW1lfV0g5Yy56YWNOiR7aXNNYXRjaH1gKQ0KICAgICAgICAgICAgICAgICAgICByZXR1cm4gaXNNYXRjaA0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCg0KDQogICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgICAgICAgIH0pDQoNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coMTExMTExMTExKQ0KICAgICAgICAgICAgICBjb25zb2xlLmxvZyhmaWx0ZXJlZE1hdGVyaWFsRGF0YS5wcm9jdXJlbWVudFByaWNlVm9MaXN0KQ0KDQogICAgICAgICAgICAgIC8vIOWPquacieW9k+ivpeeJqeaWmeacieWMuemFjeeahOS7t+agvOexu+Wei+aXtuaJjeWKoOWFpee7k+aenA0KICAgICAgICAgICAgICBpZiAoZmlsdGVyZWRNYXRlcmlhbERhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgZmlsdGVyZWREYXRhLnB1c2goZmlsdGVyZWRNYXRlcmlhbERhdGEpDQogICAgICAgICAgICAgICAgY29uc29sZS5sb2coYOa3u+WKoOeJqeaWmVske21hdGVyaWFsRGF0YS5pdGVtTmFtZX1d77yM5YyF5ZCrJHtmaWx0ZXJlZE1hdGVyaWFsRGF0YS5wcm9jdXJlbWVudFByaWNlVm9MaXN0Lmxlbmd0aH3kuKrku7fmoLznu4RgKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCg0KICAgICAgICAgIHRoaXMuY29tcGFyaXNvblByaWNlRGF0YSA9IGZpbHRlcmVkRGF0YQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCdmZXRjaENvbXBhcmlzb25EYXRhIC0g562b6YCJ5ZCO55qE5pWw5o2uOicsIHRoaXMuY29tcGFyaXNvblByaWNlRGF0YSkNCiAgICAgICAgICBjb25zb2xlLmxvZygn562b6YCJ57uT5p6c57uf6K6hOicsIHsNCiAgICAgICAgICAgIHRvdGFsTWF0ZXJpYWxzOiBmaWx0ZXJlZERhdGEubGVuZ3RoLA0KICAgICAgICAgICAgbWF0ZXJpYWxzOiBmaWx0ZXJlZERhdGEubWFwKG0gPT4gKHsNCiAgICAgICAgICAgICAgbmFtZTogbS5pdGVtTmFtZSwNCiAgICAgICAgICAgICAgcHJpY2VHcm91cENvdW50OiBtLnByb2N1cmVtZW50UHJpY2VWb0xpc3Q/Lmxlbmd0aCB8fCAwLA0KICAgICAgICAgICAgICBwcmljZUdyb3VwczogbS5wcm9jdXJlbWVudFByaWNlVm9MaXN0Py5tYXAocCA9PiBwLnByaWNlTmFtZSkgfHwgW10NCiAgICAgICAgICAgIH0pKQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICAvLyDmuLLmn5Plr7nmr5Tlm77ooagNCiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgICB0aGlzLnJlbmRlckNvbXBhcmlzb25DaGFydCgpDQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5blr7nmr5TmlbDmja7lpLHotKUnLCByZXNwb25zZSkNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5blr7nmr5TmlbDmja7lpLHotKUnKQ0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5blr7nmr5TmlbDmja7lpLHotKU6JywgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluWvueavlOaVsOaNruWksei0pe+8micgKyBlcnJvci5tZXNzYWdlKQ0KICAgICAgfSBmaW5hbGx5IHsNCiAgICAgICAgdGhpcy5jb21wYXJpc29uQ2hhcnRMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5riy5p+T5a+55q+U5Zu+6KGo77yI54us56uL5a6e546w77yM5LiN6ICm5ZCI546w5pyJ6LaL5Yq/5Zu+77yJDQogICAgcmVuZGVyQ29tcGFyaXNvbkNoYXJ0KCkgew0KICAgICAgY29uc3QgY2hhcnREb20gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnY29tcGFyaXNvbkNoYXJ0JykNCiAgICAgIGlmICghY2hhcnREb20pIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5om+5LiN5Yiw5a+55q+U5Zu+6KGoRE9N5YWD57SgJykNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIC8vIOa4heeQhueOsOacieWunuS+iw0KICAgICAgaWYgKHRoaXMuY29tcGFyaXNvbkNoYXJ0SW5zdGFuY2UpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmNvbXBhcmlzb25DaGFydEluc3RhbmNlLmRpc3Bvc2UoKQ0KICAgICAgICB9IGNhdGNoIChlcnIpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCfmuIXnkIbnjrDmnInlr7nmr5Tlm77ooajlrp7kvovlpLHotKU6JywgZXJyKQ0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOWIm+W7uuaWsOeahOWbvuihqOWunuS+iw0KICAgICAgdHJ5IHsNCiAgICAgICAgdGhpcy5jb21wYXJpc29uQ2hhcnRJbnN0YW5jZSA9IGVjaGFydHMuaW5pdChjaGFydERvbSkNCiAgICAgIH0gY2F0Y2ggKGVycikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliJvlu7rlr7nmr5Tlm77ooajlrp7kvovlpLHotKU6JywgZXJyKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgaWYgKCF0aGlzLmNvbXBhcmlzb25QcmljZURhdGEgfHwgdGhpcy5jb21wYXJpc29uUHJpY2VEYXRhLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICBjaGFydERvbS5pbm5lckhUTUwgPSAnPGRpdiBjbGFzcz0iY2hhcnQtcGxhY2Vob2xkZXIiPuaaguaXoOWvueavlOaVsOaNrjwvZGl2PicNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIGNvbnN0IGZvcm1hdERhdGUgPSAoZGF0ZVN0cikgPT4gew0KICAgICAgICBjb25zdCB5ZWFyID0gZGF0ZVN0ci5zdWJzdHJpbmcoMCwgNCkNCiAgICAgICAgY29uc3QgbW9udGggPSBkYXRlU3RyLnN1YnN0cmluZyg0LCA2KQ0KICAgICAgICBjb25zdCBkYXkgPSBkYXRlU3RyLnN1YnN0cmluZyg2LCA4KQ0KICAgICAgICByZXR1cm4gYCR7eWVhcn3lubQke21vbnRofeaciCR7ZGF5feaXpWANCiAgICAgIH0NCg0KICAgICAgLy8g5pS26ZuG5omA5pyJ5pel5pyfDQogICAgICBsZXQgYWxsRGF0ZXMgPSBuZXcgU2V0KCkNCg0KICAgICAgdGhpcy5jb21wYXJpc29uUHJpY2VEYXRhLmZvckVhY2gobWF0ZXJpYWxEYXRhID0+IHsNCiAgICAgICAgaWYgKG1hdGVyaWFsRGF0YS5wcm9jdXJlbWVudFByaWNlVm9MaXN0KSB7DQogICAgICAgICAgbWF0ZXJpYWxEYXRhLnByb2N1cmVtZW50UHJpY2VWb0xpc3QuZm9yRWFjaChwcmljZUdyb3VwID0+IHsNCiAgICAgICAgICAgIGlmIChwcmljZUdyb3VwLnByaWNlTGlzdCkgew0KICAgICAgICAgICAgICBwcmljZUdyb3VwLnByaWNlTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAgIGFsbERhdGVzLmFkZChpdGVtLnJlY29yZERhdGUpDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgYWxsRGF0ZXMgPSBBcnJheS5mcm9tKGFsbERhdGVzKS5zb3J0KCkNCiAgICAgIGNvbnN0IHhBeGlzRGF0YSA9IGFsbERhdGVzLm1hcChmb3JtYXREYXRlKQ0KDQogICAgICBpZiAoYWxsRGF0ZXMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIGNoYXJ0RG9tLmlubmVySFRNTCA9ICc8ZGl2IGNsYXNzPSJjaGFydC1wbGFjZWhvbGRlciI+5pqC5peg5a+55q+U5pWw5o2uPC9kaXY+Jw0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g5p6E5bu657O75YiX5pWw5o2uDQogICAgICBjb25zdCBzZXJpZXMgPSBbXQ0KICAgICAgY29uc3QgbGVnZW5kRGF0YSA9IFtdDQogICAgICBjb25zdCBjb2xvcnMgPSBbJyM4ZmU5ZmYnLCAnI2ZmOWY3ZicsICcjNWZkOGI2JywgJyNmZmI5ODAnXQ0KICAgICAgbGV0IGNvbG9ySW5kZXggPSAwDQoNCiAgICAgIGNvbnNvbGUubG9nKCc9PT0g5byA5aeL5aSE55CG5a+55q+U5pWw5o2uID09PScpDQogICAgICBjb25zb2xlLmxvZygn5a+55q+U5pWw5o2u5oC76KeIOicsIHsNCiAgICAgICAgbWF0ZXJpYWxDb3VudDogdGhpcy5jb21wYXJpc29uUHJpY2VEYXRhLmxlbmd0aCwNCiAgICAgICAgYmFzZU1hdGVyaWFsOiB0aGlzLmN1cnJlbnRDb21wYXJpc29uLml0ZW1OYW1lLA0KICAgICAgICBjb21wYXJlTWF0ZXJpYWw6IHRoaXMuY3VycmVudENvbXBhcmlzb24uY29tcGFyZUl0ZW1OYW1lDQogICAgICB9KQ0KDQogICAgICB0aGlzLmNvbXBhcmlzb25QcmljZURhdGEuZm9yRWFjaChtYXRlcmlhbERhdGEgPT4gew0KICAgICAgICBjb25zdCBtYXRlcmlhbE5hbWUgPSBtYXRlcmlhbERhdGEuaXRlbU5hbWUNCiAgICAgICAgY29uc29sZS5sb2coYFxu5aSE55CG54mp5paZOiAke21hdGVyaWFsTmFtZX1gKQ0KDQogICAgICAgIGlmIChtYXRlcmlhbERhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdCkgew0KICAgICAgICAgIGNvbnNvbGUubG9nKGAgIOivpeeJqeaWmeaciSAke21hdGVyaWFsRGF0YS5wcm9jdXJlbWVudFByaWNlVm9MaXN0Lmxlbmd0aH0g5Liq5Lu35qC857uEYCkNCiAgICAgICAgICBtYXRlcmlhbERhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdC5mb3JFYWNoKChwcmljZUdyb3VwLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgY29uc29sZS5sb2coYCAg5Lu35qC857uEICR7aW5kZXggKyAxfTogJHtwcmljZUdyb3VwLnByaWNlTmFtZX3vvIzmlbDmja7ngrnmlbDph486ICR7cHJpY2VHcm91cC5wcmljZUxpc3Q/Lmxlbmd0aCB8fCAwfWApDQogICAgICAgICAgfSkNCg0KICAgICAgICAgIC8vIOaVsOaNruW3sue7j+WcqGZldGNoQ29tcGFyaXNvbkRhdGHkuK3pooTlhYjnrZvpgInov4fvvIzov5nph4znm7TmjqXlpITnkIbmiYDmnInljLnphY3nmoTku7fmoLznu4QNCiAgICAgICAgICBtYXRlcmlhbERhdGEucHJvY3VyZW1lbnRQcmljZVZvTGlzdC5mb3JFYWNoKChwcmljZUdyb3VwLCBncm91cEluZGV4KSA9PiB7DQogICAgICAgICAgICBjb25zdCBwcmljZURhdGEgPSBhbGxEYXRlcy5tYXAoZGF0ZSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IGZvdW5kID0gcHJpY2VHcm91cC5wcmljZUxpc3QuZmluZChpdGVtID0+IGl0ZW0ucmVjb3JkRGF0ZSA9PT0gZGF0ZSkNCiAgICAgICAgICAgICAgcmV0dXJuIGZvdW5kID8gcGFyc2VGbG9hdChmb3VuZC5wcmljZSkgOiBudWxsDQogICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICAvLyDnu5/orqHmnInmlYjmlbDmja7ngrkNCiAgICAgICAgICAgIGNvbnN0IHZhbGlkRGF0YUNvdW50ID0gcHJpY2VEYXRhLmZpbHRlcih2ID0+IHYgIT09IG51bGwgJiYgdiAhPT0gdW5kZWZpbmVkKS5sZW5ndGgNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKGAgICAg5aSE55CG5Lu35qC857uEWyR7cHJpY2VHcm91cC5wcmljZU5hbWV9Xe+8jOacieaViOaVsOaNrueCuTogJHt2YWxpZERhdGFDb3VudH0vJHtwcmljZURhdGEubGVuZ3RofWApDQoNCiAgICAgICAgICAgIC8vIOehruS/neavj+adoeabsue6v+mDveacieWUr+S4gOeahOWQjeensOWSjOminOiJsu+8jOWNs+S9v+aVsOaNruebuOWQjA0KICAgICAgICAgICAgY29uc3QgdW5pcXVlTmFtZSA9IGAke21hdGVyaWFsTmFtZX0tJHtwcmljZUdyb3VwLnByaWNlTmFtZX1gDQogICAgICAgICAgICBjb25zdCBsaW5lQ29sb3IgPSBjb2xvcnNbY29sb3JJbmRleCAlIGNvbG9ycy5sZW5ndGhdDQoNCiAgICAgICAgICAgIC8vIOajgOafpeaYr+WQpuW3sue7j+acieebuOWQjOeahOaVsOaNru+8jOWmguaenOacieWImea3u+WKoOi9u+W+ruWBj+enuw0KICAgICAgICAgICAgY29uc3QgZGF0YVN0ciA9IEpTT04uc3RyaW5naWZ5KHByaWNlRGF0YSkNCiAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nU2VyaWVzID0gc2VyaWVzLmZpbmQocyA9PiBKU09OLnN0cmluZ2lmeShzLmRhdGEpID09PSBkYXRhU3RyKQ0KICAgICAgICAgICAgbGV0IGFkanVzdGVkRGF0YSA9IHByaWNlRGF0YQ0KDQogICAgICAgICAgICBpZiAoZXhpc3RpbmdTZXJpZXMgJiYgcHJpY2VEYXRhLnNvbWUodiA9PiB2ICE9PSBudWxsKSkgew0KICAgICAgICAgICAgICAvLyDkuLrph43lpI3mlbDmja7mt7vliqDmnoHlsI/nmoTlgY/np7vph4/vvIgwLjAx77yJ77yM56Gu5L+d5Lik5p2h57q/6YO96IO95pi+56S6DQogICAgICAgICAgICAgIGFkanVzdGVkRGF0YSA9IHByaWNlRGF0YS5tYXAodmFsdWUgPT4gdmFsdWUgIT09IG51bGwgPyB2YWx1ZSArIDAuMDEgOiBudWxsKQ0KICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgICAgIOajgOa1i+WIsOmHjeWkjeaVsOaNru+8jOS4uiAke3VuaXF1ZU5hbWV9IOa3u+WKoOWBj+enu2ApDQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIHNlcmllcy5wdXNoKHsNCiAgICAgICAgICAgICAgbmFtZTogdW5pcXVlTmFtZSwNCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgICBkYXRhOiBhZGp1c3RlZERhdGEsDQogICAgICAgICAgICAgIHNtb290aDogdHJ1ZSwNCiAgICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgICAgd2lkdGg6IDMsDQogICAgICAgICAgICAgICAgY29sb3I6IGxpbmVDb2xvciwNCiAgICAgICAgICAgICAgICAvLyDlpoLmnpzmmK/lgY/np7vnmoTmlbDmja7vvIzkvb/nlKjomZrnur/moLflvI/ljLrliIYNCiAgICAgICAgICAgICAgICB0eXBlOiBhZGp1c3RlZERhdGEgIT09IHByaWNlRGF0YSA/ICdkYXNoZWQnIDogJ3NvbGlkJw0KICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsNCiAgICAgICAgICAgICAgICBjb2xvcjogbGluZUNvbG9yDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICAgIHN5bWJvbFNpemU6IDYsDQogICAgICAgICAgICAgIGNvbm5lY3ROdWxsczogdHJ1ZSwNCiAgICAgICAgICAgICAgLy8g5re75Yqgei1pbmRleOehruS/neS4pOadoee6v+mDveiDveaYvuekug0KICAgICAgICAgICAgICB6OiBjb2xvckluZGV4ICsgMQ0KICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgbGVnZW5kRGF0YS5wdXNoKHVuaXF1ZU5hbWUpDQogICAgICAgICAgICBjb2xvckluZGV4KysNCiAgICAgICAgICAgIGNvbnNvbGUubG9nKGAgICAg4pyTIOa3u+WKoOabsue6vzogJHt1bmlxdWVOYW1lfe+8jOminOiJsjogJHtsaW5lQ29sb3J977yM5pyJ5pWI5pWw5o2uOiAke3ZhbGlkRGF0YUNvdW50fWApDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgY29uc29sZS5sb2coYFxuPT09IOWbvuihqOaVsOaNruWkhOeQhuWujOaIkCA9PT1gKQ0KICAgICAgY29uc29sZS5sb2coYOaAu+iuoea3u+WKoCAke3Nlcmllcy5sZW5ndGh9IOadoeabsue6vzpgKQ0KICAgICAgc2VyaWVzLmZvckVhY2goKHMsIGkpID0+IHsNCiAgICAgICAgY29uc3QgdmFsaWRDb3VudCA9IHMuZGF0YS5maWx0ZXIodiA9PiB2ICE9PSBudWxsICYmIHYgIT09IHVuZGVmaW5lZCkubGVuZ3RoDQogICAgICAgIGNvbnNvbGUubG9nKGAgICR7aSArIDF9LiAke3MubmFtZX0gKOacieaViOaVsOaNrjogJHt2YWxpZENvdW50fSlgKQ0KICAgICAgfSkNCg0KICAgICAgLy8g6K6h566XWei9tOiMg+WbtA0KICAgICAgbGV0IHByaWNlTWluLCBwcmljZU1heA0KICAgICAgY29uc3QgcHJpY2VWYWx1ZXMgPSBzZXJpZXMuZmxhdE1hcChzID0+IHMuZGF0YS5maWx0ZXIodiA9PiB2ICE9PSBudWxsICYmIHYgIT09IHVuZGVmaW5lZCkpDQogICAgICBpZiAocHJpY2VWYWx1ZXMubGVuZ3RoID4gMCkgew0KICAgICAgICBwcmljZU1pbiA9IE1hdGgubWluKC4uLnByaWNlVmFsdWVzKQ0KICAgICAgICBwcmljZU1heCA9IE1hdGgubWF4KC4uLnByaWNlVmFsdWVzKQ0KICAgICAgfQ0KDQogICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JywNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogew0KICAgICAgICAgICAgdHlwZTogJ2Nyb3NzJw0KICAgICAgICAgIH0sDQogICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihwYXJhbXMpIHsNCiAgICAgICAgICAgIGxldCBzdHIgPSBwYXJhbXNbMF0uYXhpc1ZhbHVlTGFiZWwgKyAnPGJyLz4nDQogICAgICAgICAgICBwYXJhbXMuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgaWYgKGl0ZW0udmFsdWUgIT09IG51bGwgJiYgaXRlbS52YWx1ZSAhPT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgc3RyICs9IGAke2l0ZW0ubWFya2VyfSR7aXRlbS5zZXJpZXNOYW1lfTogJHtpdGVtLnZhbHVlfSDlhYMv5ZCoPGJyLz5gDQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgc3RyICs9IGAke2l0ZW0ubWFya2VyfSR7aXRlbS5zZXJpZXNOYW1lfTogLTxici8+YA0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KQ0KICAgICAgICAgICAgcmV0dXJuIHN0cg0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgZGF0YTogbGVnZW5kRGF0YSwNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnI2ZmZicNCiAgICAgICAgICB9LA0KICAgICAgICAgIHRvcDogJzUlJw0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7DQogICAgICAgICAgbGVmdDogJzMlJywNCiAgICAgICAgICByaWdodDogJzQlJywNCiAgICAgICAgICBib3R0b206ICcxMiUnLA0KICAgICAgICAgIHRvcDogJzIwJScsDQogICAgICAgICAgY29udGFpbkxhYmVsOiB0cnVlDQogICAgICAgIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiB4QXhpc0RhdGEsDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBjb2xvcjogJyNlZWUnLA0KICAgICAgICAgICAgaW50ZXJ2YWw6IGZ1bmN0aW9uKGluZGV4LCB2YWx1ZSkgew0KICAgICAgICAgICAgICBpZiAoaW5kZXggPj0gYWxsRGF0ZXMubGVuZ3RoIHx8ICFhbGxEYXRlcy5sZW5ndGgpIHJldHVybiBmYWxzZQ0KDQogICAgICAgICAgICAgIGNvbnN0IHVuaXF1ZU1vbnRocyA9IG5ldyBTZXQoKQ0KICAgICAgICAgICAgICBhbGxEYXRlcy5mb3JFYWNoKGRhdGVTdHIgPT4gew0KICAgICAgICAgICAgICAgIGNvbnN0IHllYXIgPSBkYXRlU3RyLnN1YnN0cmluZygwLCA0KQ0KICAgICAgICAgICAgICAgIGNvbnN0IG1vbnRoID0gZGF0ZVN0ci5zdWJzdHJpbmcoNCwgNikNCiAgICAgICAgICAgICAgICB1bmlxdWVNb250aHMuYWRkKGAke3llYXJ9JHttb250aH1gKQ0KICAgICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICAgIGNvbnN0IG1vbnRoc0NvdW50ID0gdW5pcXVlTW9udGhzLnNpemUNCiAgICAgICAgICAgICAgaWYgKG1vbnRoc0NvdW50IDw9IDEpIHJldHVybiB0cnVlDQoNCiAgICAgICAgICAgICAgY29uc3QgdG90YWxEYXRhUG9pbnRzID0gYWxsRGF0ZXMubGVuZ3RoDQogICAgICAgICAgICAgIGNvbnN0IGlkZWFsSW50ZXJ2YWwgPSBNYXRoLmZsb29yKHRvdGFsRGF0YVBvaW50cyAvIE1hdGgubWluKG1vbnRoc0NvdW50LCA4KSkNCg0KICAgICAgICAgICAgICByZXR1cm4gaW5kZXggJSBNYXRoLm1heChpZGVhbEludGVydmFsLCAxKSA9PT0gMA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24odmFsdWUsIGluZGV4KSB7DQogICAgICAgICAgICAgIGlmIChpbmRleCA+PSBhbGxEYXRlcy5sZW5ndGgpIHJldHVybiAnJw0KICAgICAgICAgICAgICBjb25zdCBvcmlnaW5hbERhdGVTdHIgPSBhbGxEYXRlc1tpbmRleF0NCiAgICAgICAgICAgICAgaWYgKCFvcmlnaW5hbERhdGVTdHIpIHJldHVybiAnJw0KDQogICAgICAgICAgICAgIGNvbnN0IHllYXIgPSBvcmlnaW5hbERhdGVTdHIuc3Vic3RyaW5nKDAsIDQpDQogICAgICAgICAgICAgIGNvbnN0IG1vbnRoID0gcGFyc2VJbnQob3JpZ2luYWxEYXRlU3RyLnN1YnN0cmluZyg0LCA2KSkNCiAgICAgICAgICAgICAgcmV0dXJuIGAke3llYXJ9LiR7bW9udGh9YA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsNCiAgICAgICAgICAgIGxpbmVTdHlsZTogew0KICAgICAgICAgICAgICBjb2xvcjogJyNlZWUnDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+S7t+agvO+8iOWFgy/lkKjvvIknLA0KICAgICAgICAgIG1pbjogcHJpY2VNaW4sDQogICAgICAgICAgbWF4OiBwcmljZU1heCwNCiAgICAgICAgICBheGlzTGluZTogew0KICAgICAgICAgICAgbGluZVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnI2VlZScNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgY29sb3I6ICcjZWVlJw0KICAgICAgICAgIH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuMSknDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IHNlcmllcw0KICAgICAgfQ0KDQogICAgICB0aGlzLmNvbXBhcmlzb25DaGFydEluc3RhbmNlLnNldE9wdGlvbihvcHRpb24sIHRydWUpDQogICAgfSwNCg0KICAgIC8vIOajgOafpeS4pOS4quabsue6v+aYr+WQpumDveW3suiuvue9rum7mOiupOWAvO+8jOWmguaenOaYr+WImeinpuWPkeWIneWni+aVsOaNruiOt+WPlg0KICAgIGNoZWNrQW5kVHJpZ2dlckluaXRpYWxEYXRhRmV0Y2goKSB7DQogICAgICAvLyDmo4Dmn6XkuKTkuKrmm7Lnur/mmK/lkKbpg73lt7Lnu4/orr7nva7kuobpu5jorqTnmoRQQuWdlw0KICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRQdXJjaGFzZUFtb3VudE1hdGVyaWFscy5pbmNsdWRlcygnUELlnZcnKSAmJg0KICAgICAgICB0aGlzLnNlbGVjdGVkTWFya2V0UHJpY2VNYXRlcmlhbHMuaW5jbHVkZXMoJ1BC5Z2XJykgJiYNCiAgICAgICAgIXRoaXMuaGFzSW5pdGlhbGl6ZWRQcmljZUNoYXJ0KSB7DQoNCiAgICAgICAgdGhpcy5oYXNJbml0aWFsaXplZFByaWNlQ2hhcnQgPSB0cnVlIC8vIOagh+iusOW3sue7j+WIneWni+WMlui/hw0KICAgICAgICBjb25zb2xlLmxvZygn5Lik5Liq5puy57q/6YO95bey6K6+572u6buY6K6k5YC877yM6Ieq5Yqo6Kem5Y+R5pWw5o2u6I635Y+WJykNCg0KICAgICAgICAvLyDoh6rliqjop6blj5HmlbDmja7ojrflj5YNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHRoaXMuZmV0Y2hQcmljZUFuZFN0b3JlRGF0YUZvck5ld0NoYXJ0KCkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoLA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/purchaseDashboardStock", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-container\">\r\n    <div class=\"dashboard-header\">\r\n      <h1>采购库存看板</h1>\r\n      <div class=\"header-controls\">\r\n        <div class=\"fullscreen-btn\" @click=\"toggleFullscreen\" :title=\"isFullscreen ? '退出全屏' : '进入全屏'\">\r\n          <i :class=\"isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'\"></i>\r\n        </div>\r\n        <div class=\"time-filter\">\r\n          <button\r\n            v-for=\"filter in timeFilters\"\r\n            :key=\"filter.id\"\r\n            :class=\"['time-filter-btn', { active: filter.id === activeFilter }]\"\r\n            @click=\"handleTimeFilterChange(filter.id, filter.value)\"\r\n          >\r\n            {{ filter.label }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"dashboard-grid\">\r\n      <!-- 第一行：中心仓库月度库存金额 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          中心仓库月度库存金额\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedYear\"\r\n              @change=\"handleYearChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部年份</option>\r\n              <option v-for=\"year in availableYears\" :key=\"year\" :value=\"year\">\r\n                {{ year }}年\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedMaterialType\"\r\n              @change=\"handleMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">总和</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"monthlyInventoryChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第一行：机旁库当前库存 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 1;\">\r\n        <h2 class=\"card-title\">\r\n          机旁库当前库存\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedFactoryDep\"\r\n              @change=\"handleFactoryDepChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"\">全部分厂</option>\r\n              <option v-for=\"depName in factoryDepOptions\" :key=\"depName\" :value=\"depName\">\r\n                {{ depName }}\r\n              </option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedFactoryMaterialType\"\r\n              @change=\"handleFactoryMaterialTypeChange\"\r\n            >\r\n              <option value=\"\">全部物料</option>\r\n              <option value=\"A\">通用备件</option>\r\n              <option value=\"B\">专用备件</option>\r\n              <option value=\"C\">材料类</option>\r\n              <option value=\"D\">原材料</option>\r\n              <option value=\"E\">辅耐材</option>\r\n              <option value=\"G\">办公</option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"factoryStockChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第二行：矿焦煤实时库存 -->\r\n      <div class=\"card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          <div style=\"display: flex; align-items: center; justify-content: space-between; width: 100%;\">\r\n            <div style=\"display: flex; align-items: center; gap: 15px;\">\r\n              <span>矿焦煤实时库存</span>\r\n              <span class=\"inventory-total\">\r\n                合计: {{ calculateCokingCoalTotal() }}万吨\r\n              </span>\r\n            </div>\r\n            <div class=\"chart-filter-dropdown-container\">\r\n              <select\r\n                v-model=\"selectedCokingCoalType\"\r\n                @change=\"handleCokingCoalTypeChange\"\r\n              >\r\n                <option value=\"\">全部</option>\r\n                <option value=\"矿料类\">矿料类</option>\r\n                <option value=\"焦炭\">焦炭</option>\r\n                <option value=\"煤焦类\">煤焦类</option>\r\n                <option value=\"合金类\">合金类</option>\r\n                <option value=\"辅助类/电极\">辅助类/电极</option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n        </h2>\r\n        <div class=\"chart\" style=\"display: flex; height: 100%;\">\r\n          <div id=\"cokingCoalPieChart\" style=\"width: 25%; height: 100%;\"></div>\r\n          <div id=\"cokingCoalLineChart\" style=\"width: 75%; height: 100%;\"></div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 物料入库统计 -->\r\n      <div class=\"card material-chart-card\" style=\"grid-column: span 6; grid-row: 2;\">\r\n        <h2 class=\"card-title\">\r\n          物料入库统计\r\n          <div class=\"chart-filter-dropdown-container\">\r\n            <select\r\n              v-model=\"selectedMaterialCategory\"\r\n              @change=\"handleMaterialCategoryChange\"\r\n              style=\"margin-right: 10px;\"\r\n            >\r\n              <option value=\"1\">大类</option>\r\n              <option value=\"2\">中类</option>\r\n              <option value=\"3\">细类</option>\r\n              <option value=\"4\">叶类</option>\r\n            </select>\r\n            <select\r\n              v-model=\"selectedMaterialItem\"\r\n              @change=\"handleMaterialItemChange\"\r\n            >\r\n              <option value=\"\">全部</option>\r\n              <option v-for=\"item in materialItemOptions\" :key=\"item.itemId\" :value=\"item.itemId\">\r\n                {{ item.itemName }}\r\n              </option>\r\n            </select>\r\n          </div>\r\n        </h2>\r\n        <div id=\"materialStatisticsChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- 物料价格对比弹框 -->\r\n    <el-dialog\r\n      title=\"物料价格趋势对比分析\"\r\n      :visible.sync=\"comparisonDialogVisible\"\r\n      width=\"90%\"\r\n      :before-close=\"closeComparisonDialog\"\r\n      custom-class=\"comparison-dialog\"\r\n    >\r\n      <div class=\"comparison-content\">\r\n        <div class=\"comparison-header\">\r\n          <div class=\"comparison-title\">\r\n            <span class=\"base-material\">{{ currentComparison.itemName }}</span>\r\n            <span class=\"vs-text\">VS</span>\r\n            <span class=\"compare-material\">{{ currentComparison.compareItemName }}</span>\r\n            <span class=\"similarity-info\">相似度：{{ currentComparison.score }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"comparison-chart-container\">\r\n          <div\r\n            id=\"comparisonChart\"\r\n            class=\"comparison-chart\"\r\n            v-loading=\"comparisonChartLoading\"\r\n            element-loading-text=\"正在加载对比数据...\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport chartMethods from './chartMethods'\r\nimport extendedChartMethods from './chartMethodsExtended'\r\nimport screenfull from 'screenfull'\r\nimport { showYearlyAmount, showRealTimeAmount, showCokingCoalAmount, showKeyIndicators, showItemTypeList, showMaterialList, showData, showSuppList, showHighFrequencyMaterialList, showPurchaseSuppRisk, getMaterialFuturePrice, getMaterialNameList, getPurchasePriceAndStore, getMaterialNameListFromNewTables, getPurchasePriceAndStoreFromNewTables } from '@/api/purchaseDashboard/purchaseDashboard'\r\nimport { listSimilarByItemNames } from '@/api/purchase/similar'\r\nimport { getDepNameList, getListMonthly } from '@/api/purchase/purdchaseFactoryStock'\r\n\r\nexport default {\r\n  name: 'PurchaseDashboard',\r\n  mixins: [chartMethods, extendedChartMethods],\r\n  data() {\r\n    return {\r\n      // 时间过滤器选项\r\n      timeFilters: [\r\n        { id: 'filter-3m', label: '近三个月', value: 1 },\r\n        { id: 'filter-6m', label: '近六个月', value: 2 },\r\n        { id: 'filter-1y', label: '近一年', value: 3 }\r\n      ],\r\n      activeFilter: 'filter-1y',\r\n      currentDimensionType: 3,\r\n\r\n      // 数据\r\n      dashboardData: {},\r\n      purchaseStats: {},\r\n\r\n      // 下拉选项\r\n      topSuppliersOptions: [],\r\n\r\n      // 选中的过滤器值\r\n      selectedTopSuppliersFilter: '',\r\n      selectedOrderType: 'TOP', // 排序类型，默认为TOP\r\n\r\n      // 图表实例\r\n      chartInstances: {},\r\n\r\n      // 原始数据备份\r\n      originalTopSuppliersData: [],\r\n\r\n      // 库存图表相关\r\n      selectedYear: '',\r\n      selectedMaterialType: '',\r\n      availableYears: (() => {\r\n        const currentYear = new Date().getFullYear()\r\n        const years = []\r\n        for (let year = 2020; year <= currentYear; year++) {\r\n          years.push(year.toString())\r\n        }\r\n        return years\r\n      })(),\r\n      yearlyInventoryData: [],\r\n      realTimeInventoryData: [],\r\n      cokingCoalInventoryData: [],\r\n\r\n      // 矿焦煤库存图表相关\r\n      selectedCokingCoalType: '', // 选中的矿焦煤类型，默认为空（全部）\r\n\r\n      // 物料入库统计相关\r\n      selectedMaterialCategory: '1',\r\n      selectedMaterialItem: '',\r\n      materialItemOptions: [],\r\n      materialStatisticsData: [],\r\n\r\n      // 高频采购物料相关\r\n      selectedCodeType: 'ALL',\r\n      selectedItemType: 'CLASS3',\r\n      highFrequencyMaterialData: [],\r\n\r\n      // 供应商风险数据\r\n      supplierRiskData: [],\r\n\r\n      // AI价格预测相关\r\n      pricePredictions: [], // 改为数组，支持多个物料的预测\r\n      predictionLoading: false,\r\n\r\n      // 物料价格趋势图相关\r\n      materialNameOptions: [],\r\n      selectedMaterial: 'PB块',\r\n      selectedMaterialCategory: '1', // 默认选择矿石\r\n      priceAndStoreData: null,\r\n\r\n      // 新的价格趋势图相关属性\r\n      // 采购量曲线\r\n      purchaseAmountCategories: [99], // 默认选择全部\r\n      selectedPurchaseAmountMaterials: [],\r\n      purchaseAmountMaterialOptions: [],\r\n\r\n      // 市场价曲线\r\n      marketPriceCategories: [99], // 默认选择全部\r\n      selectedMarketPriceMaterials: [],\r\n      marketPriceMaterialOptions: [],\r\n\r\n      // 获取数据状态\r\n      fetchingPriceData: false,\r\n      newPriceAndStoreData: null,\r\n\r\n      // 初始化标志\r\n      hasInitializedPriceChart: false,\r\n\r\n      // 相似物料数据\r\n      similarMaterialsData: [],\r\n      similarMaterialsLoading: false,\r\n\r\n      // 对比弹框相关\r\n      comparisonDialogVisible: false,\r\n      comparisonChartLoading: false,\r\n      currentComparison: {},\r\n      comparisonChartInstance: null,\r\n      comparisonPriceData: null,\r\n\r\n      // 机旁库当前库存相关\r\n      selectedFactoryDep: '', // 选中的分厂\r\n      selectedFactoryMaterialType: '', // 选中的物料类型\r\n      factoryDepOptions: [], // 分厂选项列表\r\n      factoryStockData: [] // 机旁库存数据\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isFullscreen() {\r\n      return this.$store.state.app.isFullscreenMode\r\n    },\r\n\r\n    // 按itemName、category、priceType联合索引分组相似物料数据\r\n    groupedSimilarMaterials() {\r\n      const grouped = {}\r\n      this.similarMaterialsData.forEach(item => {\r\n        // 创建联合索引key\r\n        const groupKey = `${item.itemName}_${item.category}_${item.priceType}`\r\n        const displayKey = `${item.itemName} (${this.getCategoryName(item.category)} - ${this.getPriceTypeName(item.priceType)})`\r\n\r\n        if (!grouped[displayKey]) {\r\n          grouped[displayKey] = {\r\n            groupKey: groupKey,\r\n            items: []\r\n          }\r\n        }\r\n        grouped[displayKey].items.push(item)\r\n      })\r\n\r\n      // 对每个组内的数据按排名排序\r\n      Object.keys(grouped).forEach(key => {\r\n        grouped[key].items.sort((a, b) => a.rank - b.rank)\r\n      })\r\n\r\n      return grouped\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.checkEchartsAvailability()\r\n    this.fetchDashboardData(3)\r\n    this.fetchYearlyInventoryData()\r\n    this.fetchRealTimeInventoryData()\r\n    this.fetchCokingCoalInventoryData()\r\n    // 初始化物料入库统计的下拉框选项和数据\r\n    this.updateMaterialItemOptions().then(() => {\r\n      this.fetchMaterialStatisticsData()\r\n    })\r\n    // 初始化高频采购物料数据\r\n    this.fetchHighFrequencyMaterialData()\r\n    // 初始化供应商风险数据\r\n    this.fetchSupplierRiskData()\r\n\r\n    // 初始化新的物料名称列表（会自动触发默认选中PB块和数据获取）\r\n    this.fetchPurchaseAmountMaterialList()\r\n    this.fetchMarketPriceMaterialList()\r\n\r\n    // 初始化机旁库存数据\r\n    this.fetchFactoryDepOptions()\r\n\r\n    this.setupResizeObserver()\r\n    this.initFullscreenListener()\r\n\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.resizeAllCharts)\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 清理定时器和事件监听器\r\n    this.clearAllIntervals()\r\n    this.removeFullscreenListener()\r\n    window.removeEventListener('resize', this.resizeAllCharts)\r\n\r\n    // 确保退出全屏模式\r\n    this.$store.dispatch('app/setFullscreenMode', false)\r\n  },\r\n\r\n  methods: {\r\n    // 初始化全屏监听器\r\n    initFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.on('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 移除全屏监听器\r\n    removeFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.off('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 处理全屏状态变化\r\n    handleFullscreenChange() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        const isFullscreen = screenfull.isFullscreen\r\n        this.$store.dispatch('app/setFullscreenMode', isFullscreen)\r\n\r\n        // 全屏状态变化后，重新调整图表大小\r\n        this.$nextTick(() => {\r\n          setTimeout(() => {\r\n            this.resizeAllCharts()\r\n          }, 300) // 给布局变化一些时间\r\n        })\r\n      }\r\n    },\r\n\r\n    // API调用方法\r\n    async getDashboardData(dimensionType) {\r\n      return await showData({ dimensionType: dimensionType })\r\n    },\r\n\r\n    async getItemTypeList(itemType) {\r\n      return await showItemTypeList({ itemType: itemType })\r\n    },\r\n\r\n    async getMaterialList(params) {\r\n      return await showMaterialList(params)\r\n    },\r\n\r\n    async getSupplierList(params) {\r\n      return await showSuppList(params)\r\n    },\r\n\r\n    async getYearlyAmount(params) {\r\n      return await showYearlyAmount(params)\r\n    },\r\n\r\n    async getRealTimeAmount() {\r\n      return await showRealTimeAmount()\r\n    },\r\n\r\n    async getCokingCoalAmount() {\r\n      return await showCokingCoalAmount()\r\n    },\r\n\r\n    async getKeyIndicators(params) {\r\n      return await showKeyIndicators(params)\r\n    },\r\n\r\n    async getHighFrequencyMaterialList(params) {\r\n      return await showHighFrequencyMaterialList(params)\r\n    },\r\n\r\n    async getPurchaseSuppRisk(params) {\r\n      return await showPurchaseSuppRisk(params)\r\n    },\r\n\r\n    // 根据dimensionType获取timeFlag\r\n    getTimeFlagByDimensionType(dimensionType) {\r\n      switch(dimensionType) {\r\n        case 1: return '03' // 近三个月\r\n        case 2: return '06' // 近六个月\r\n        case 3: return '12' // 近一年\r\n        default: return '03'\r\n      }\r\n    },\r\n\r\n    // 检查ECharts可用性\r\n    checkEchartsAvailability() {\r\n      if (!echarts) {\r\n        console.error('ECharts库未能加载，使用备用显示方式')\r\n        document.querySelectorAll('.chart').forEach(el => {\r\n          el.innerHTML = '<div class=\"chart-placeholder\">图表加载失败</div>'\r\n        })\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n\r\n    // 获取仪表板数据\r\n    async fetchDashboardData(dimensionTypeParam = 1) {\r\n      this.currentDimensionType = dimensionTypeParam\r\n\r\n      // 清除所有定时器\r\n      this.clearAllIntervals()\r\n\r\n      try {\r\n        // 并行获取仪表板数据和关键指标数据\r\n        const [dashboardResponse, keyIndicatorsResponse] = await Promise.all([\r\n          this.getDashboardData(dimensionTypeParam),\r\n          this.getKeyIndicators({ dimensionType: dimensionTypeParam })\r\n        ])\r\n\r\n        // 处理仪表板数据\r\n        if (dashboardResponse && dashboardResponse.data) {\r\n          this.dashboardData = dashboardResponse.data\r\n          console.log('获取仪表板数据成功:', this.dashboardData)\r\n        } else {\r\n          console.error('API数据格式不正确或缺少data字段', dashboardResponse)\r\n          this.showErrorMessage('API数据格式不正确或缺少data字段')\r\n        }\r\n\r\n        // 处理关键指标数据\r\n        if (keyIndicatorsResponse && keyIndicatorsResponse.data) {\r\n          this.purchaseStats = keyIndicatorsResponse.data || {}\r\n          console.log('获取关键指标数据成功:', this.purchaseStats)\r\n        } else {\r\n          console.error('获取关键指标数据失败', keyIndicatorsResponse)\r\n          this.purchaseStats = {}\r\n        }\r\n\r\n        this.initAllCharts()\r\n      } catch (error) {\r\n        console.error('API请求或数据处理失败', error)\r\n        this.showErrorMessage('数据加载失败: ' + error.message)\r\n      }\r\n    },\r\n\r\n    // 显示错误信息\r\n    showErrorMessage(message) {\r\n      document.querySelectorAll('.chart').forEach(chart => {\r\n        chart.innerHTML = `<div class=\"chart-placeholder\">${message}</div>`\r\n      })\r\n    },\r\n\r\n    // 时间过滤器变化处理\r\n    handleTimeFilterChange(filterId, dimensionType) {\r\n      this.activeFilter = filterId\r\n      this.currentDimensionType = dimensionType\r\n      console.log('选择的时间范围:', filterId, '维度:', dimensionType)\r\n\r\n      this.clearAllIntervals()\r\n      this.fetchDashboardData(dimensionType)\r\n      // 同时更新高频物料数据\r\n      this.fetchHighFrequencyMaterialData()\r\n      // 同时更新供应商风险数据\r\n      this.fetchSupplierRiskData()\r\n      // 同时更新物料入库统计数据\r\n      this.fetchMaterialStatisticsData()\r\n      // 注意：价格趋势数据只在用户主动点击按钮时获取，不在时间过滤器变化时自动获取\r\n\r\n      // 同时更新新的物料列表（用于下拉框选项），但不会自动触发数据获取\r\n      this.fetchPurchaseAmountMaterialList()\r\n      this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 清除所有定时器\r\n    clearAllIntervals() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance && instance.intervalId) {\r\n          clearInterval(instance.intervalId)\r\n          instance.intervalId = null\r\n        }\r\n      })\r\n    },\r\n\r\n    // 重新调整所有图表大小\r\n    resizeAllCharts() {\r\n      Object.values(this.chartInstances).forEach(instance => {\r\n        if (instance) {\r\n          try {\r\n            instance.resize()\r\n          } catch(err) {\r\n            console.error('图表大小调整失败:', err)\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 初始化所有图表\r\n    initAllCharts() {\r\n      console.log('initAllCharts started')\r\n      try {\r\n        // 注意：实时库存图表和矿焦煤库存图表会在各自数据获取完成后单独初始化\r\n        // 注意：月度库存金额图表会在fetchYearlyInventoryData完成后单独初始化\r\n        // 注意：物料入库统计图表会在fetchMaterialStatisticsData完成后单独初始化\r\n        // 注意：机旁库存图表会在fetchFactoryStockData完成后单独初始化\r\n\r\n        // 初始化物料词云图\r\n        this.initMaterialCloud()\r\n\r\n        // 初始化TOP供应商图\r\n        this.initTopSuppliersChart()\r\n        this.populateItemDropdown('topSuppliersFilter', 1, 'topSuppliersOptions')\r\n\r\n        // 注意：供应商风险图表会在fetchSupplierRiskData完成后单独初始化\r\n\r\n        // 注意：采购价格趋势图会在fetchPriceAndStoreData完成后单独初始化\r\n\r\n        console.log('所有图表初始化完成')\r\n      } catch (err) {\r\n        console.error('图表初始化主流程失败:', err)\r\n        this.showErrorMessage('图表初始化失败: ' + err.message)\r\n      }\r\n    },\r\n\r\n    // 填充物料类型下拉框\r\n    async populateItemDropdown(selectElementId, itemType, dataPropertyName) {\r\n      try {\r\n        const response = await this.getItemTypeList(itemType)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this[dataPropertyName] = response.data\r\n        } else {\r\n          console.error(`Invalid data format from showItemTypeList for itemType ${itemType}:`, response)\r\n          this[dataPropertyName] = []\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error fetching item types for ${selectElementId}:`, error)\r\n        this[dataPropertyName] = []\r\n      }\r\n    },\r\n\r\n    // 下拉框变化处理方法\r\n    async handleTopSuppliersFilterChange() {\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async handleOrderTypeChange() {\r\n      console.log('排序类型变化:', this.selectedOrderType)\r\n      await this.refreshTopSuppliersChart()\r\n    },\r\n\r\n    async refreshTopSuppliersChart() {\r\n      console.log(`Top supplier filter selected item ID: ${this.selectedTopSuppliersFilter}, orderType: ${this.selectedOrderType}`)\r\n      const myChart = this.chartInstances.topSuppliersChart\r\n      if (!myChart) {\r\n        console.error(\"TOP10供应商图表实例未找到\")\r\n        return\r\n      }\r\n\r\n      if (myChart.intervalId) {\r\n        clearInterval(myChart.intervalId)\r\n        myChart.intervalId = null\r\n      }\r\n\r\n      if (!this.selectedTopSuppliersFilter || this.selectedTopSuppliersFilter === \"\") {\r\n        // 使用原始数据，但需要根据orderType重新获取\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n            newSupplierData = this.originalTopSuppliersData\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          this.renderAndPaginateTopSuppliers(myChart, this.originalTopSuppliersData)\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      } else {\r\n        myChart.showLoading()\r\n        try {\r\n          const response = await this.getSupplierList({\r\n            dimensionType: this.currentDimensionType,\r\n            itemId: this.selectedTopSuppliersFilter,\r\n            orderType: this.selectedOrderType\r\n          })\r\n\r\n          let newSupplierData = []\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            newSupplierData = response.data\r\n          } else {\r\n            console.error('从showSuppList API获取的数据无效:', response)\r\n          }\r\n          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)\r\n        } catch (error) {\r\n          console.error(`为topSuppliersChart获取供应商列表失败:`, error)\r\n          document.getElementById('topSuppliersChart').innerHTML = '<div class=\"chart-placeholder\">供应商数据加载失败</div>'\r\n        } finally {\r\n          myChart.hideLoading()\r\n        }\r\n      }\r\n    },\r\n\r\n    // 设置大小调整观察器\r\n    setupResizeObserver() {\r\n      const resizeObserver = new ResizeObserver(entries => {\r\n        for (let entry of entries) {\r\n          const charts = entry.target.querySelectorAll('.chart')\r\n          charts.forEach(chart => {\r\n            if (chart.id) {\r\n              const instance = echarts.getInstanceByDom(document.getElementById(chart.id))\r\n              if (instance) {\r\n                instance.resize()\r\n              }\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      document.querySelectorAll('.card').forEach(card => {\r\n        resizeObserver.observe(card)\r\n      })\r\n    },\r\n\r\n    toggleFullscreen() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.toggle()\r\n      } else {\r\n        this.$message({\r\n          message: '您的浏览器不支持全屏功能',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n\r\n    async handleYearChange() {\r\n      console.log('年份变化:', this.selectedYear)\r\n      await this.fetchYearlyInventoryData()\r\n    },\r\n\r\n    async handleMaterialTypeChange() {\r\n      console.log('物料类型变化:', this.selectedMaterialType)\r\n      await this.fetchYearlyInventoryData()\r\n    },\r\n\r\n    // 获取年度库存数据\r\n    async fetchYearlyInventoryData() {\r\n      try {\r\n        const params = {}\r\n\r\n        // 只有当materialType不为空时才传递该参数\r\n        if (this.selectedMaterialType && this.selectedMaterialType !== '') {\r\n          params.materialType = this.selectedMaterialType\r\n        }\r\n\r\n        // 如果选择了具体年份，只查询该年份，否则查询所有年份\r\n        if (this.selectedYear) {\r\n          params.yearList = [this.selectedYear]\r\n        } else {\r\n          params.yearList = this.availableYears\r\n        }\r\n\r\n        console.log('fetchYearlyInventoryData - 请求参数:', params)\r\n        const response = await this.getYearlyAmount(params)\r\n        console.log('fetchYearlyInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.yearlyInventoryData = response.data || []\r\n          console.log('fetchYearlyInventoryData - 设置的数据:', this.yearlyInventoryData)\r\n        } else {\r\n          // 使用模拟数据\r\n          this.yearlyInventoryData = this.getMockYearlyData()\r\n          console.log('fetchYearlyInventoryData - 使用模拟数据:', this.yearlyInventoryData)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取年度库存数据失败，使用模拟数据:', error)\r\n        // 使用模拟数据\r\n        this.yearlyInventoryData = this.getMockYearlyData()\r\n      }\r\n\r\n      // 重新初始化图表\r\n      this.initMonthlyInventoryChart()\r\n    },\r\n\r\n    // 生成模拟数据\r\n    getMockYearlyData() {\r\n      return [\r\n        {\r\n          year: '2023',\r\n          monthlyResultVoList: [\r\n            { monthIndex: 1, amount: 1200.50 },\r\n            { monthIndex: 2, amount: 1350.75 },\r\n            { monthIndex: 3, amount: 1180.20 },\r\n            { monthIndex: 4, amount: 1420.30 },\r\n            { monthIndex: 5, amount: 1380.90 },\r\n            { monthIndex: 6, amount: 1520.40 },\r\n            { monthIndex: 7, amount: 1650.60 },\r\n            { monthIndex: 8, amount: 1480.85 },\r\n            { monthIndex: 9, amount: 1390.25 },\r\n            { monthIndex: 10, amount: 1610.70 },\r\n            { monthIndex: 11, amount: 1580.35 },\r\n            { monthIndex: 12, amount: 1720.95 }\r\n          ]\r\n        },\r\n        {\r\n          year: '2024',\r\n          monthlyResultVoList: [\r\n            { monthIndex: 1, amount: 1320.80 },\r\n            { monthIndex: 2, amount: 1450.60 },\r\n            { monthIndex: 3, amount: 1280.40 },\r\n            { monthIndex: 4, amount: 1540.70 },\r\n            { monthIndex: 5, amount: 1480.20 },\r\n            { monthIndex: 6, amount: 1620.50 },\r\n            { monthIndex: 7, amount: 1750.30 },\r\n            { monthIndex: 8, amount: 1580.90 },\r\n            { monthIndex: 9, amount: 1490.60 },\r\n            { monthIndex: 10, amount: 1710.40 },\r\n            { monthIndex: 11, amount: 1680.80 },\r\n            { monthIndex: 12, amount: 1820.20 }\r\n          ]\r\n        }\r\n      ]\r\n    },\r\n\r\n    async fetchRealTimeInventoryData() {\r\n      try {\r\n        const response = await this.getRealTimeAmount()\r\n        console.log('fetchRealTimeInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.realTimeInventoryData = response.data || []\r\n          console.log('fetchRealTimeInventoryData - 设置的数据:', this.realTimeInventoryData)\r\n        } else {\r\n          console.error('获取实时库存数据失败，使用模拟数据', response)\r\n          // 使用模拟数据\r\n          this.realTimeInventoryData = this.getMockRealTimeData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取实时库存数据失败，使用模拟数据:', error)\r\n        // 使用模拟数据\r\n        this.realTimeInventoryData = this.getMockRealTimeData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initRealTimeInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟实时库存数据\r\n    getMockRealTimeData() {\r\n      return [\r\n        {\r\n          materialType: 'A',\r\n          materialName: '通用备件',\r\n          centerInventoryAmount: 1250.30,\r\n          machineSideInventoryAmount: 380.50,\r\n          totalInventoryAmount: 1630.80\r\n        },\r\n        {\r\n          materialType: 'B',\r\n          materialName: '专用备件',\r\n          centerInventoryAmount: 980.75,\r\n          machineSideInventoryAmount: 420.25,\r\n          totalInventoryAmount: 1401.00\r\n        },\r\n        {\r\n          materialType: 'C',\r\n          materialName: '材料类',\r\n          centerInventoryAmount: 2150.60,\r\n          machineSideInventoryAmount: 650.40,\r\n          totalInventoryAmount: 2801.00\r\n        },\r\n        {\r\n          materialType: 'D',\r\n          materialName: '原材料',\r\n          centerInventoryAmount: 3200.90,\r\n          machineSideInventoryAmount: 890.10,\r\n          totalInventoryAmount: 4091.00\r\n        },\r\n        {\r\n          materialType: 'E',\r\n          materialName: '辅耐材',\r\n          centerInventoryAmount: 1580.40,\r\n          machineSideInventoryAmount: 320.60,\r\n          totalInventoryAmount: 1901.00\r\n        },\r\n        {\r\n          materialType: 'G',\r\n          materialName: '办公',\r\n          centerInventoryAmount: 150.20,\r\n          machineSideInventoryAmount: 50.80,\r\n          totalInventoryAmount: 201.00\r\n        }\r\n      ]\r\n    },\r\n\r\n    async fetchCokingCoalInventoryData() {\r\n      try {\r\n        const response = await this.getCokingCoalAmount()\r\n        console.log('fetchCokingCoalInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.cokingCoalInventoryData = response.data || []\r\n          console.log('fetchCokingCoalInventoryData - 设置的数据:', this.cokingCoalInventoryData)\r\n        } else {\r\n          console.error('获取矿焦煤库存数据失败', response)\r\n          this.cokingCoalInventoryData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取矿焦煤库存数据失败:', error)\r\n        this.cokingCoalInventoryData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 物料入库统计相关方法\r\n    async handleMaterialCategoryChange() {\r\n      console.log('物料类别变化:', this.selectedMaterialCategory)\r\n      this.selectedMaterialItem = '' // 重置第二个下拉框\r\n      await this.updateMaterialItemOptions()\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async handleMaterialItemChange() {\r\n      console.log('物料项目变化:', this.selectedMaterialItem)\r\n      await this.fetchMaterialStatisticsData()\r\n    },\r\n\r\n    async updateMaterialItemOptions() {\r\n      if (this.selectedMaterialCategory === '1') {\r\n        // 大类：只有全部选项\r\n        this.materialItemOptions = []\r\n      } else {\r\n        // 中类、细类、叶类：获取对应的选项\r\n        const itemType = parseInt(this.selectedMaterialCategory) - 1 // 1->0, 2->1, 3->2, 4->3\r\n        try {\r\n          const response = await this.getItemTypeList(itemType)\r\n          if (response && response.data && Array.isArray(response.data)) {\r\n            this.materialItemOptions = response.data\r\n          } else {\r\n            this.materialItemOptions = []\r\n          }\r\n        } catch (error) {\r\n          console.error('获取物料项目选项失败:', error)\r\n          this.materialItemOptions = []\r\n        }\r\n      }\r\n    },\r\n\r\n    async fetchMaterialStatisticsData() {\r\n      try {\r\n        const params = {\r\n          itemType: parseInt(this.selectedMaterialCategory),\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        // 如果选择了具体物料项目，添加itemId参数\r\n        if (this.selectedMaterialItem && this.selectedMaterialItem !== '') {\r\n          params.itemId = this.selectedMaterialItem\r\n        }\r\n\r\n        console.log('fetchMaterialStatisticsData - 请求参数:', params)\r\n        const response = await this.getMaterialList(params)\r\n        console.log('fetchMaterialStatisticsData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.materialStatisticsData = response.data || []\r\n          console.log('fetchMaterialStatisticsData - 设置的数据:', this.materialStatisticsData)\r\n        } else {\r\n          console.error('获取物料统计数据失败，使用模拟数据', response)\r\n          this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料统计数据失败，使用模拟数据:', error)\r\n        this.materialStatisticsData = this.getMockMaterialStatisticsData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialStatisticsChart()\r\n      })\r\n    },\r\n\r\n    // 生成模拟物料统计数据\r\n    getMockMaterialStatisticsData() {\r\n      return [\r\n        { itemName: '通用备件', inAmt: 1250.30, arriveRate: 85.5 },\r\n        { itemName: '专用备件', inAmt: 980.75, arriveRate: 78.2 },\r\n        { itemName: '材料类', inAmt: 2150.60, arriveRate: 92.1 },\r\n        { itemName: '原材料', inAmt: 3200.90, arriveRate: 88.7 },\r\n        { itemName: '辅耐材', inAmt: 1580.40, arriveRate: 91.3 },\r\n        { itemName: '办公', inAmt: 150.20, arriveRate: 95.0 }\r\n      ]\r\n    },\r\n\r\n    async fetchHighFrequencyMaterialData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          codeType: this.selectedCodeType,\r\n          itemType: this.selectedItemType\r\n        }\r\n\r\n        console.log('fetchHighFrequencyMaterialData - 请求参数:', params)\r\n        const response = await this.getHighFrequencyMaterialList(params)\r\n        console.log('fetchHighFrequencyMaterialData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.highFrequencyMaterialData = response.data || []\r\n          console.log('fetchHighFrequencyMaterialData - 设置的数据:', this.highFrequencyMaterialData)\r\n        } else {\r\n          console.error('获取高频物料数据失败，使用模拟数据', response)\r\n          this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取高频物料数据失败，使用模拟数据:', error)\r\n        this.highFrequencyMaterialData = this.getMockHighFrequencyData()\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initMaterialCloud()\r\n      })\r\n    },\r\n\r\n    // 生成模拟高频物料数据\r\n    getMockHighFrequencyData() {\r\n      return [\r\n        { itemName: '粗粉', inAmt: 392467.2, inNum: 5421293 },\r\n        { itemName: '精粉', inAmt: 280350.5, inNum: 4250180 },\r\n        { itemName: '球团', inAmt: 195200.8, inNum: 3180970 },\r\n        { itemName: '烧结', inAmt: 150420.3, inNum: 2890540 },\r\n        { itemName: '焦炭', inAmt: 125680.7, inNum: 2350210 },\r\n        { itemName: '煤炭', inAmt: 98750.2, inNum: 1980760 }\r\n      ]\r\n    },\r\n\r\n    async handleCodeTypeChange() {\r\n      console.log('大类类型变化:', this.selectedCodeType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    async handleItemTypeChange() {\r\n      console.log('维度变化:', this.selectedItemType)\r\n      await this.fetchHighFrequencyMaterialData()\r\n    },\r\n\r\n    // 获取供应商风险数据\r\n    async fetchSupplierRiskData() {\r\n      try {\r\n        const params = {\r\n          timeFlag: this.getTimeFlagByDimensionType(this.currentDimensionType)\r\n        }\r\n\r\n        console.log('fetchSupplierRiskData - 请求参数:', params)\r\n        const response = await this.getPurchaseSuppRisk(params)\r\n        console.log('fetchSupplierRiskData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.supplierRiskData = response.data || []\r\n          console.log('fetchSupplierRiskData - 设置的数据:', this.supplierRiskData)\r\n        } else {\r\n          console.error('获取供应商风险数据失败', response)\r\n          this.supplierRiskData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取供应商风险数据失败:', error)\r\n        this.supplierRiskData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initSupplierRiskChart()\r\n      })\r\n    },\r\n\r\n    // 获取多个物料的AI价格预测\r\n    async fetchMultiplePricePredictions(materialNames) {\r\n      this.predictionLoading = true\r\n      this.pricePredictions = [] // 清空之前的预测结果\r\n\r\n      try {\r\n        // 并行调用所有物料的预测接口\r\n        const predictionPromises = materialNames.map(async (materialName) => {\r\n          try {\r\n            const params = {\r\n              materialName: materialName,\r\n              materialType: '1' // 默认使用矿石类型，可以根据需要调整\r\n            }\r\n\r\n            console.log(`fetchPricePrediction - ${materialName} 请求参数:`, params)\r\n            const response = await getMaterialFuturePrice(params)\r\n            console.log(`fetchPricePrediction - ${materialName} 完整响应:`, response)\r\n\r\n            if (response && response.code && response.code === 200 && response.data) {\r\n              return {\r\n                materialName: materialName,\r\n                question: response.data.question || `关于${materialName}的价格预测`,\r\n                prediction: response.data.answer || response.data.prediction || response.msg,\r\n                success: response.data.success !== false\r\n              }\r\n            } else {\r\n              console.error(`获取${materialName}价格预测数据失败`, response)\r\n              return {\r\n                materialName: materialName,\r\n                question: `关于${materialName}的价格预测`,\r\n                prediction: `获取${materialName}价格预测失败`,\r\n                success: false\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error(`获取${materialName}价格预测数据失败:`, error)\r\n            return {\r\n              materialName: materialName,\r\n              question: `关于${materialName}的价格预测`,\r\n              prediction: `获取${materialName}价格预测失败：${error.message}`,\r\n              success: false\r\n            }\r\n          }\r\n        })\r\n\r\n        // 等待所有预测结果\r\n        const results = await Promise.all(predictionPromises)\r\n        this.pricePredictions = results\r\n        console.log('fetchMultiplePricePredictions - 设置的预测数据:', this.pricePredictions)\r\n\r\n        const successCount = results.filter(r => r.success).length\r\n        const totalCount = results.length\r\n\r\n        if (successCount > 0) {\r\n          this.$message.success(`成功获取${successCount}/${totalCount}个物料的价格预测`)\r\n        } else {\r\n          this.$message.error('所有物料的价格预测获取失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('批量获取价格预测数据失败:', error)\r\n        this.$message.error('批量获取价格预测失败：' + error.message)\r\n      } finally {\r\n        this.predictionLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取物料名称列表\r\n    async fetchMaterialNameList() {\r\n      try {\r\n        const params = {\r\n          category: parseInt(this.selectedMaterialCategory)\r\n        }\r\n\r\n        const response = await getMaterialNameList(params)\r\n        console.log('fetchMaterialNameList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.materialNameOptions = response.data\r\n          console.log('fetchMaterialNameList - 设置的数据:', this.materialNameOptions)\r\n\r\n          // 设置默认选中PB块，如果存在的话\r\n          const pbMaterial = this.materialNameOptions.find(item => item.itemName === 'PB块')\r\n          if (pbMaterial) {\r\n            this.selectedMaterial = 'PB块'\r\n          } else if (this.materialNameOptions.length > 0) {\r\n            // 如果没有PB块，选择第一个\r\n            this.selectedMaterial = this.materialNameOptions[0].itemName\r\n          }\r\n\r\n          // 获取价格数据\r\n          this.fetchPriceAndStoreData()\r\n        } else {\r\n          console.error('获取物料名称列表失败', response)\r\n          this.materialNameOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料名称列表失败:', error)\r\n        this.materialNameOptions = []\r\n      }\r\n    },\r\n\r\n    // 获取物料价格和采购量数据\r\n    async fetchPriceAndStoreData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemName: this.selectedMaterial\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStore(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n          this.priceAndStoreData = response.data[0] // 取第一个元素\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.priceAndStoreData)\r\n        } else {\r\n          console.error('获取价格和采购量数据失败', response)\r\n          this.priceAndStoreData = null\r\n        }\r\n      } catch (error) {\r\n        console.error('获取价格和采购量数据失败:', error)\r\n        this.priceAndStoreData = null\r\n      }\r\n\r\n      // 数据获取完成后重新初始化价格趋势图\r\n      this.$nextTick(() => {\r\n        this.initPriceTrendChart()\r\n      })\r\n    },\r\n\r\n    // 处理物资类型切换\r\n    async handleMaterialCategoryTypeChange() {\r\n      console.log('物资类型变化:', this.selectedMaterialCategory)\r\n      // 重新获取物料名称列表\r\n      await this.fetchMaterialNameList()\r\n    },\r\n\r\n    // 处理物料选择变化\r\n    async handleMaterialChange() {\r\n      console.log('物料选择变化:', this.selectedMaterial)\r\n      await this.fetchPriceAndStoreData()\r\n      // 不再自动触发AI预测，等用户点击按钮后再触发\r\n    },\r\n\r\n    calculateRealTimeInventoryTotal() {\r\n      let total = 0\r\n      if (this.realTimeInventoryData && this.realTimeInventoryData.length > 0) {\r\n        this.realTimeInventoryData.forEach(item => {\r\n          total += parseFloat(item.totalInventoryAmount) || 0\r\n        })\r\n      }\r\n      return total.toFixed(2)\r\n    },\r\n\r\n    calculateCokingCoalTotal() {\r\n      let total = 0\r\n      if (this.cokingCoalInventoryData && this.cokingCoalInventoryData.length > 0) {\r\n        // 找到所有数据中的最新日期\r\n        let latestDate = ''\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            item.purchaseCokingDailyDetailList.forEach(detail => {\r\n              if (detail.instockDate > latestDate) {\r\n                latestDate = detail.instockDate\r\n              }\r\n            })\r\n          }\r\n        })\r\n\r\n        // 计算最新日期各个物料的库存量合计\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)\r\n            if (latestDetail) {\r\n              total += parseFloat(latestDetail.invQty) || 0\r\n            }\r\n          }\r\n        })\r\n      }\r\n      return (total / 10000).toFixed(2) // 转换为万吨\r\n    },\r\n\r\n    // 处理矿焦煤类型下拉框变化\r\n    async handleCokingCoalTypeChange() {\r\n      console.log('矿焦煤类型变化:', this.selectedCokingCoalType)\r\n      // 重新初始化图表以应用过滤\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalInventoryChart()\r\n      })\r\n    },\r\n\r\n    // 机旁库存相关方法\r\n    // 获取分厂选项列表\r\n    async fetchFactoryDepOptions() {\r\n      try {\r\n        const response = await getDepNameList()\r\n        console.log('fetchFactoryDepOptions - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryDepOptions = response.data\r\n          console.log('fetchFactoryDepOptions - 设置的数据:', this.factoryDepOptions)\r\n        } else {\r\n          console.error('获取分厂选项列表失败', response)\r\n          this.factoryDepOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取分厂选项列表失败:', error)\r\n        this.factoryDepOptions = []\r\n      }\r\n\r\n      // 获取默认数据（全部）\r\n      this.fetchFactoryStockData()\r\n    },\r\n\r\n    // 处理分厂选择变化\r\n    async handleFactoryDepChange() {\r\n      console.log('分厂选择变化:', this.selectedFactoryDep)\r\n      await this.fetchFactoryStockData()\r\n    },\r\n\r\n    // 处理物料类型选择变化\r\n    async handleFactoryMaterialTypeChange() {\r\n      console.log('物料类型选择变化:', this.selectedFactoryMaterialType)\r\n      // 重新初始化图表以应用筛选\r\n      this.$nextTick(() => {\r\n        this.initFactoryStockChart()\r\n      })\r\n    },\r\n\r\n    // 获取机旁库存数据\r\n    async fetchFactoryStockData() {\r\n      try {\r\n        const depName = this.selectedFactoryDep || '' // 空字符串表示全部\r\n        console.log('fetchFactoryStockData - 请求参数:', depName)\r\n\r\n        const response = await getListMonthly(depName)\r\n        console.log('fetchFactoryStockData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.factoryStockData = response.data\r\n          console.log('fetchFactoryStockData - 设置的数据:', this.factoryStockData)\r\n        } else {\r\n          console.error('获取机旁库存数据失败', response)\r\n          this.factoryStockData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取机旁库存数据失败:', error)\r\n        this.factoryStockData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initFactoryStockChart()\r\n      })\r\n    },\r\n\r\n    // 新增方法：处理采购量曲线物料类型变化\r\n    async handlePurchaseAmountCategoriesChange() {\r\n      console.log('采购量曲线物料类型变化:', this.purchaseAmountCategories)\r\n      this.selectedPurchaseAmountMaterials = [] // 重置选中的物料\r\n      await this.fetchPurchaseAmountMaterialList()\r\n    },\r\n\r\n    // 新增方法：处理市场价曲线物料类型变化\r\n    async handleMarketPriceCategoriesChange() {\r\n      console.log('市场价曲线物料类型变化:', this.marketPriceCategories)\r\n      this.selectedMarketPriceMaterials = [] // 重置选中的物料\r\n      await this.fetchMarketPriceMaterialList()\r\n    },\r\n\r\n    // 新增方法：获取采购量曲线物料列表\r\n    async fetchPurchaseAmountMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.purchaseAmountCategories,\r\n          curveType: 2, // 采购量曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchPurchaseAmountMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchPurchaseAmountMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.purchaseAmountMaterialOptions = response.data\r\n          console.log('fetchPurchaseAmountMaterialList - 设置的数据:', this.purchaseAmountMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedPurchaseAmountMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.purchaseAmountMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedPurchaseAmountMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 采购量曲线')\r\n\r\n              // 检查市场价曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取采购量曲线物料列表失败', response)\r\n          this.purchaseAmountMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取采购量曲线物料列表失败:', error)\r\n        this.purchaseAmountMaterialOptions = []\r\n      }\r\n    },\r\n\r\n    // 新增方法：获取市场价曲线物料列表\r\n    async fetchMarketPriceMaterialList() {\r\n      try {\r\n        const params = {\r\n          categories: this.marketPriceCategories,\r\n          curveType: 1, // 价格曲线\r\n          dimensionType: this.currentDimensionType\r\n        }\r\n\r\n        console.log('fetchMarketPriceMaterialList - 请求参数:', params)\r\n        const response = await getMaterialNameListFromNewTables(params)\r\n        console.log('fetchMarketPriceMaterialList - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.marketPriceMaterialOptions = response.data\r\n          console.log('fetchMarketPriceMaterialList - 设置的数据:', this.marketPriceMaterialOptions)\r\n\r\n          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块\r\n          if (this.selectedMarketPriceMaterials.length === 0 && !this.hasInitializedPriceChart) {\r\n            const pbMaterial = this.marketPriceMaterialOptions.find(item => item.itemName === 'PB块')\r\n            if (pbMaterial) {\r\n              this.selectedMarketPriceMaterials = ['PB块']\r\n              console.log('默认选中PB块 - 市场价曲线')\r\n\r\n              // 检查采购量曲线是否也已经设置好默认值，如果是则触发数据获取\r\n              this.checkAndTriggerInitialDataFetch()\r\n            }\r\n          }\r\n        } else {\r\n          console.error('获取市场价曲线物料列表失败', response)\r\n          this.marketPriceMaterialOptions = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取市场价曲线物料列表失败:', error)\r\n        this.marketPriceMaterialOptions = []\r\n      }\r\n    },\r\n\r\n\r\n\r\n    // 新增方法：获取物料采购价格数据（用于新的价格趋势图）\r\n    async fetchPriceAndStoreDataForNewChart() {\r\n      if (this.selectedPurchaseAmountMaterials.length === 0 && this.selectedMarketPriceMaterials.length === 0) {\r\n        this.$message.warning('请至少选择一个物料')\r\n        return\r\n      }\r\n\r\n      this.fetchingPriceData = true\r\n      try {\r\n        // 构建itemList\r\n        const itemList = []\r\n\r\n        // 添加采购量曲线的物料\r\n        this.selectedPurchaseAmountMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 2, // 采购量曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        // 添加市场价曲线的物料\r\n        this.selectedMarketPriceMaterials.forEach(itemName => {\r\n          itemList.push({\r\n            curveType: 1, // 价格曲线\r\n            itemName: itemName\r\n          })\r\n        })\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchPriceAndStoreData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchPriceAndStoreData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.newPriceAndStoreData = response.data\r\n          console.log('fetchPriceAndStoreData - 设置的数据:', this.newPriceAndStoreData)\r\n\r\n          // 重新渲染图表\r\n          this.$nextTick(() => {\r\n            this.initNewPriceTrendChart()\r\n          })\r\n\r\n          // 获取所有选中物料的去重列表\r\n          const allSelectedMaterials = [...new Set([\r\n            ...this.selectedPurchaseAmountMaterials,\r\n            ...this.selectedMarketPriceMaterials\r\n          ])]\r\n\r\n          // 为每个物料调用AI预测接口\r\n          if (allSelectedMaterials.length > 0) {\r\n            this.fetchMultiplePricePredictions(allSelectedMaterials)\r\n          }\r\n\r\n          // 如果市场价曲线有选中物料，获取相似物料信息\r\n          if (this.selectedMarketPriceMaterials.length > 0) {\r\n            this.fetchSimilarMaterials(this.selectedMarketPriceMaterials)\r\n          } else {\r\n            // 清空相似物料数据\r\n            this.similarMaterialsData = []\r\n          }\r\n\r\n          this.$message.success('数据获取成功')\r\n        } else {\r\n          console.error('获取物料采购价格数据失败', response)\r\n          this.$message.error('获取数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取物料采购价格数据失败:', error)\r\n        this.$message.error('获取数据失败：' + error.message)\r\n      } finally {\r\n        this.fetchingPriceData = false\r\n      }\r\n    },\r\n\r\n    // 获取相似物料信息\r\n    async fetchSimilarMaterials(itemNames) {\r\n      this.similarMaterialsLoading = true\r\n      try {\r\n        const params = {\r\n          itemNames: itemNames\r\n        }\r\n\r\n        console.log('fetchSimilarMaterials - 请求参数:', params)\r\n        const response = await listSimilarByItemNames(params)\r\n        console.log('fetchSimilarMaterials - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          this.similarMaterialsData = response.data\r\n          console.log('fetchSimilarMaterials - 设置的数据:', this.similarMaterialsData)\r\n        } else {\r\n          console.error('获取相似物料数据失败', response)\r\n          this.similarMaterialsData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取相似物料数据失败:', error)\r\n        this.similarMaterialsData = []\r\n      } finally {\r\n        this.similarMaterialsLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取排名样式类\r\n    getRankClass(rank) {\r\n      if (rank === 1) return 'rank-first'\r\n      if (rank === 2) return 'rank-second'\r\n      if (rank === 3) return 'rank-third'\r\n      return 'rank-default'\r\n    },\r\n\r\n    // 获取商品分类名称\r\n    getCategoryName(category) {\r\n      const categoryMap = {\r\n        1: '矿石',\r\n        2: '煤炭',\r\n        3: '合金',\r\n        4: '废钢'\r\n      }\r\n      return categoryMap[category] || '未知'\r\n    },\r\n\r\n    // 获取价格类型名称\r\n    getPriceTypeName(priceType) {\r\n      const priceTypeMap = {\r\n        1: '现货价',\r\n        2: '市场采购到厂价',\r\n        3: '兴澄废钢收购价(车运)',\r\n        4: '兴澄废钢收购价(船运)',\r\n        5: '沙钢废钢收购价(车运)',\r\n        6: '沙钢废钢收购价(船运)'\r\n      }\r\n      return priceTypeMap[priceType] || '未知'\r\n    },\r\n\r\n    // 打开对比弹框\r\n    openComparisonDialog(item) {\r\n      console.log('openComparisonDialog - 传入的item数据:', item)\r\n      this.currentComparison = { ...item }\r\n      console.log('openComparisonDialog - 设置的currentComparison:', this.currentComparison)\r\n      this.comparisonDialogVisible = true\r\n\r\n      // 弹框打开后获取对比数据\r\n      this.$nextTick(() => {\r\n        this.fetchComparisonData()\r\n      })\r\n    },\r\n\r\n    // 关闭对比弹框\r\n    closeComparisonDialog() {\r\n      this.comparisonDialogVisible = false\r\n      this.currentComparison = {}\r\n      this.comparisonPriceData = null\r\n\r\n      // 清理图表实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n          this.comparisonChartInstance = null\r\n        } catch (err) {\r\n          console.error('清理对比图表实例失败:', err)\r\n        }\r\n      }\r\n    },\r\n\r\n    // 获取对比数据（独立实现，不耦合现有趋势图）\r\n    async fetchComparisonData() {\r\n      this.comparisonChartLoading = true\r\n      try {\r\n        // 构建两个物料的对比请求，只获取价格曲线\r\n        const itemList = [\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.itemName\r\n          },\r\n          {\r\n            curveType: 1, // 价格曲线\r\n            itemName: this.currentComparison.compareItemName\r\n          }\r\n        ]\r\n\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemList: itemList\r\n        }\r\n\r\n        console.log('fetchComparisonData - 请求参数:', params)\r\n        const response = await getPurchasePriceAndStoreFromNewTables(params)\r\n        console.log('fetchComparisonData - 完整响应:', response)\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          // 对返回的数据进行筛选，确保基准物料和相似物料的指定价格类型都能被提取\r\n          const filteredData = []\r\n\r\n          // 获取基准物料和相似物料的目标价格类型名称\r\n          const basePriceTypeName = this.getPriceTypeName(this.currentComparison.priceType)\r\n          const comparePriceTypeName = this.getPriceTypeName(this.currentComparison.comparePriceType)\r\n\r\n          console.log('筛选条件:', {\r\n            baseItemName: this.currentComparison.itemName,\r\n            basePriceTypeName: basePriceTypeName,\r\n            compareItemName: this.currentComparison.compareItemName,\r\n            comparePriceTypeName: comparePriceTypeName\r\n          })\r\n\r\n          response.data.forEach(materialData => {\r\n            const filteredMaterialData = { ...materialData }\r\n\r\n            if (filteredMaterialData.procurementPriceVoList) {\r\n              // 只保留匹配的价格类型\r\n              filteredMaterialData.procurementPriceVoList = filteredMaterialData.procurementPriceVoList.filter(priceGroup => {\r\n                let isMatch = false\r\n                // 基准物料：匹配物料名称和基准价格类型\r\n                if (materialData.itemName === this.currentComparison.itemName) {\r\n                  isMatch = priceGroup.priceName === basePriceTypeName\r\n                  console.log(`基准物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${basePriceTypeName}] 匹配:${isMatch}`)\r\n                }\r\n\r\n                if(isMatch){\r\n                  return isMatch\r\n                }else{\r\n                  if (materialData.itemName === this.currentComparison.compareItemName) {\r\n                    const isMatch = priceGroup.priceName === comparePriceTypeName\r\n                    console.log(`相似物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${comparePriceTypeName}] 匹配:${isMatch}`)\r\n                    return isMatch\r\n                  }\r\n                }\r\n\r\n\r\n                return false\r\n              })\r\n\r\n              console.log(111111111)\r\n              console.log(filteredMaterialData.procurementPriceVoList)\r\n\r\n              // 只有当该物料有匹配的价格类型时才加入结果\r\n              if (filteredMaterialData.procurementPriceVoList.length > 0) {\r\n                filteredData.push(filteredMaterialData)\r\n                console.log(`添加物料[${materialData.itemName}]，包含${filteredMaterialData.procurementPriceVoList.length}个价格组`)\r\n              }\r\n            }\r\n          })\r\n\r\n          this.comparisonPriceData = filteredData\r\n          console.log('fetchComparisonData - 筛选后的数据:', this.comparisonPriceData)\r\n          console.log('筛选结果统计:', {\r\n            totalMaterials: filteredData.length,\r\n            materials: filteredData.map(m => ({\r\n              name: m.itemName,\r\n              priceGroupCount: m.procurementPriceVoList?.length || 0,\r\n              priceGroups: m.procurementPriceVoList?.map(p => p.priceName) || []\r\n            }))\r\n          })\r\n\r\n          // 渲染对比图表\r\n          this.$nextTick(() => {\r\n            this.renderComparisonChart()\r\n          })\r\n        } else {\r\n          console.error('获取对比数据失败', response)\r\n          this.$message.error('获取对比数据失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取对比数据失败:', error)\r\n        this.$message.error('获取对比数据失败：' + error.message)\r\n      } finally {\r\n        this.comparisonChartLoading = false\r\n      }\r\n    },\r\n\r\n    // 渲染对比图表（独立实现，不耦合现有趋势图）\r\n    renderComparisonChart() {\r\n      const chartDom = document.getElementById('comparisonChart')\r\n      if (!chartDom) {\r\n        console.error('找不到对比图表DOM元素')\r\n        return\r\n      }\r\n\r\n      // 清理现有实例\r\n      if (this.comparisonChartInstance) {\r\n        try {\r\n          this.comparisonChartInstance.dispose()\r\n        } catch (err) {\r\n          console.error('清理现有对比图表实例失败:', err)\r\n        }\r\n      }\r\n\r\n      // 创建新的图表实例\r\n      try {\r\n        this.comparisonChartInstance = echarts.init(chartDom)\r\n      } catch (err) {\r\n        console.error('创建对比图表实例失败:', err)\r\n        return\r\n      }\r\n\r\n      if (!this.comparisonPriceData || this.comparisonPriceData.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      const formatDate = (dateStr) => {\r\n        const year = dateStr.substring(0, 4)\r\n        const month = dateStr.substring(4, 6)\r\n        const day = dateStr.substring(6, 8)\r\n        return `${year}年${month}月${day}日`\r\n      }\r\n\r\n      // 收集所有日期\r\n      let allDates = new Set()\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        if (materialData.procurementPriceVoList) {\r\n          materialData.procurementPriceVoList.forEach(priceGroup => {\r\n            if (priceGroup.priceList) {\r\n              priceGroup.priceList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      allDates = Array.from(allDates).sort()\r\n      const xAxisData = allDates.map(formatDate)\r\n\r\n      if (allDates.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无对比数据</div>'\r\n        return\r\n      }\r\n\r\n      // 构建系列数据\r\n      const series = []\r\n      const legendData = []\r\n      const colors = ['#8fe9ff', '#ff9f7f', '#5fd8b6', '#ffb980']\r\n      let colorIndex = 0\r\n\r\n      console.log('=== 开始处理对比数据 ===')\r\n      console.log('对比数据总览:', {\r\n        materialCount: this.comparisonPriceData.length,\r\n        baseMaterial: this.currentComparison.itemName,\r\n        compareMaterial: this.currentComparison.compareItemName\r\n      })\r\n\r\n      this.comparisonPriceData.forEach(materialData => {\r\n        const materialName = materialData.itemName\r\n        console.log(`\\n处理物料: ${materialName}`)\r\n\r\n        if (materialData.procurementPriceVoList) {\r\n          console.log(`  该物料有 ${materialData.procurementPriceVoList.length} 个价格组`)\r\n          materialData.procurementPriceVoList.forEach((priceGroup, index) => {\r\n            console.log(`  价格组 ${index + 1}: ${priceGroup.priceName}，数据点数量: ${priceGroup.priceList?.length || 0}`)\r\n          })\r\n\r\n          // 数据已经在fetchComparisonData中预先筛选过，这里直接处理所有匹配的价格组\r\n          materialData.procurementPriceVoList.forEach((priceGroup, groupIndex) => {\r\n            const priceData = allDates.map(date => {\r\n              const found = priceGroup.priceList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.price) : null\r\n            })\r\n\r\n            // 统计有效数据点\r\n            const validDataCount = priceData.filter(v => v !== null && v !== undefined).length\r\n            console.log(`    处理价格组[${priceGroup.priceName}]，有效数据点: ${validDataCount}/${priceData.length}`)\r\n\r\n            // 确保每条曲线都有唯一的名称和颜色，即使数据相同\r\n            const uniqueName = `${materialName}-${priceGroup.priceName}`\r\n            const lineColor = colors[colorIndex % colors.length]\r\n\r\n            // 检查是否已经有相同的数据，如果有则添加轻微偏移\r\n            const dataStr = JSON.stringify(priceData)\r\n            const existingSeries = series.find(s => JSON.stringify(s.data) === dataStr)\r\n            let adjustedData = priceData\r\n\r\n            if (existingSeries && priceData.some(v => v !== null)) {\r\n              // 为重复数据添加极小的偏移量（0.01），确保两条线都能显示\r\n              adjustedData = priceData.map(value => value !== null ? value + 0.01 : null)\r\n              console.log(`    检测到重复数据，为 ${uniqueName} 添加偏移`)\r\n            }\r\n\r\n            series.push({\r\n              name: uniqueName,\r\n              type: 'line',\r\n              data: adjustedData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 3,\r\n                color: lineColor,\r\n                // 如果是偏移的数据，使用虚线样式区分\r\n                type: adjustedData !== priceData ? 'dashed' : 'solid'\r\n              },\r\n              itemStyle: {\r\n                color: lineColor\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 6,\r\n              connectNulls: true,\r\n              // 添加z-index确保两条线都能显示\r\n              z: colorIndex + 1\r\n            })\r\n\r\n            legendData.push(uniqueName)\r\n            colorIndex++\r\n            console.log(`    ✓ 添加曲线: ${uniqueName}，颜色: ${lineColor}，有效数据: ${validDataCount}`)\r\n          })\r\n        }\r\n      })\r\n\r\n      console.log(`\\n=== 图表数据处理完成 ===`)\r\n      console.log(`总计添加 ${series.length} 条曲线:`)\r\n      series.forEach((s, i) => {\r\n        const validCount = s.data.filter(v => v !== null && v !== undefined).length\r\n        console.log(`  ${i + 1}. ${s.name} (有效数据: ${validCount})`)\r\n      })\r\n\r\n      // 计算Y轴范围\r\n      let priceMin, priceMax\r\n      const priceValues = series.flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n      if (priceValues.length > 0) {\r\n        priceMin = Math.min(...priceValues)\r\n        priceMax = Math.max(...priceValues)\r\n      }\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          formatter: function(params) {\r\n            let str = params[0].axisValueLabel + '<br/>'\r\n            params.forEach(item => {\r\n              if (item.value !== null && item.value !== undefined) {\r\n                str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`\r\n              } else {\r\n                str += `${item.marker}${item.seriesName}: -<br/>`\r\n              }\r\n            })\r\n            return str\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '12%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: xAxisData,\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: function(index, value) {\r\n              if (index >= allDates.length || !allDates.length) return false\r\n\r\n              const uniqueMonths = new Set()\r\n              allDates.forEach(dateStr => {\r\n                const year = dateStr.substring(0, 4)\r\n                const month = dateStr.substring(4, 6)\r\n                uniqueMonths.add(`${year}${month}`)\r\n              })\r\n\r\n              const monthsCount = uniqueMonths.size\r\n              if (monthsCount <= 1) return true\r\n\r\n              const totalDataPoints = allDates.length\r\n              const idealInterval = Math.floor(totalDataPoints / Math.min(monthsCount, 8))\r\n\r\n              return index % Math.max(idealInterval, 1) === 0\r\n            },\r\n            formatter: function(value, index) {\r\n              if (index >= allDates.length) return ''\r\n              const originalDateStr = allDates[index]\r\n              if (!originalDateStr) return ''\r\n\r\n              const year = originalDateStr.substring(0, 4)\r\n              const month = parseInt(originalDateStr.substring(4, 6))\r\n              return `${year}.${month}`\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '价格（元/吨）',\r\n          min: priceMin,\r\n          max: priceMax,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee'\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: series\r\n      }\r\n\r\n      this.comparisonChartInstance.setOption(option, true)\r\n    },\r\n\r\n    // 检查两个曲线是否都已设置默认值，如果是则触发初始数据获取\r\n    checkAndTriggerInitialDataFetch() {\r\n      // 检查两个曲线是否都已经设置了默认的PB块\r\n      if (this.selectedPurchaseAmountMaterials.includes('PB块') &&\r\n        this.selectedMarketPriceMaterials.includes('PB块') &&\r\n        !this.hasInitializedPriceChart) {\r\n\r\n        this.hasInitializedPriceChart = true // 标记已经初始化过\r\n        console.log('两个曲线都已设置默认值，自动触发数据获取')\r\n\r\n        // 自动触发数据获取\r\n        this.$nextTick(() => {\r\n          this.fetchPriceAndStoreDataForNewChart()\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n  font-family: \"Microsoft YaHei\", sans-serif;\r\n}\r\n\r\n.dashboard-container {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  color: #fff;\r\n  overflow-x: hidden;\r\n  padding: 10px;\r\n}\r\n\r\n.dashboard-header {\r\n  text-align: center;\r\n  margin-bottom: 10px;\r\n  position: relative;\r\n  padding: 5px 0;\r\n}\r\n\r\n.dashboard-header h1 {\r\n  font-size: 24px;\r\n  position: relative;\r\n  display: inline-block;\r\n  padding: 5px 40px;\r\n}\r\n\r\n.dashboard-header::before,\r\n.dashboard-header::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  top: 50%;\r\n  width: 30%;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.dashboard-header::before {\r\n  left: 0;\r\n}\r\n\r\n.dashboard-header::after {\r\n  right: 0;\r\n}\r\n\r\n.dashboard-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(12, 1fr);\r\n  grid-template-rows: auto auto auto auto auto auto;\r\n  gap: 10px;\r\n  min-height: calc(100vh - 80px);\r\n}\r\n\r\n.card {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 5px;\r\n  padding: 10px;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 2px;\r\n  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n}\r\n\r\n.card-title {\r\n  font-size: 14px;\r\n  margin-bottom: 5px;\r\n  font-weight: normal;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.chart-filter-dropdown-container {\r\n  z-index: 10;\r\n}\r\n\r\n.chart-filter-dropdown-container select {\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n  color: #fff;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  font-size: 12px;\r\n}\r\n\r\n.chart {\r\n  width: 100%;\r\n  height: calc(100% - 20px);\r\n  min-height: 200px;\r\n  flex: 1;\r\n}\r\n\r\n.stat-cards {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  height: 100%;\r\n  align-items: center;\r\n}\r\n\r\n.stat-card {\r\n  text-align: center;\r\n  flex-grow: 1;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 34px;\r\n  font-weight: bold;\r\n  color: #00ffff;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 18px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.chart-placeholder {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  color: rgba(255,255,255,0.5);\r\n  font-size: 14px;\r\n}\r\n\r\n.material-chart-card {\r\n  height: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 300px;\r\n}\r\n\r\n.material-chart-card .chart {\r\n  flex-grow: 1;\r\n  height: 250px;\r\n  min-height: 250px;\r\n}\r\n\r\n.time-filter {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.time-filter-btn {\r\n  padding: 6px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n}\r\n\r\n.time-filter-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.time-filter-btn.active {\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n  font-weight: 500;\r\n}\r\n\r\n.header-controls {\r\n  position: absolute;\r\n  right: 20px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  z-index: 1000;\r\n}\r\n\r\n.fullscreen-btn {\r\n  padding: 8px 12px;\r\n  border: none;\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  color: #eee;\r\n  border-radius: 20px;\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 40px;\r\n  height: 32px;\r\n}\r\n\r\n.fullscreen-btn:hover {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-color: rgba(0, 212, 255, 0.7);\r\n  color: #00ffff;\r\n}\r\n\r\n/* AI价格预测区域样式 */\r\n.price-prediction-section {\r\n  margin-top: 15px;\r\n  margin-bottom: 15px;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-size: 13px;\r\n}\r\n\r\n.prediction-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.model-info {\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 12px;\r\n}\r\n\r\n.prediction-content {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 15px;\r\n  border-left: 3px solid #00ffff;\r\n  position: relative;\r\n}\r\n\r\n.prediction-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n\r\n\r\n/* 多个预测结果的样式 */\r\n.predictions-container {\r\n  max-height: 500px;\r\n  overflow-y: auto;\r\n  padding-right: 5px;\r\n}\r\n\r\n.prediction-item {\r\n  margin-bottom: 15px;\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n  overflow: hidden;\r\n}\r\n\r\n.prediction-item.prediction-error {\r\n  border-left-color: #ff6b6b;\r\n}\r\n\r\n.prediction-material-title {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  padding: 8px 12px;\r\n  font-size: 13px;\r\n  font-weight: 600;\r\n  color: #00ffff;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.prediction-item.prediction-error .prediction-material-title {\r\n  background-color: rgba(255, 107, 107, 0.1);\r\n  color: #ff6b6b;\r\n  border-bottom-color: rgba(255, 107, 107, 0.2);\r\n}\r\n\r\n.prediction-material-title i {\r\n  margin-right: 6px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n/* 预测容器滚动条样式 */\r\n.predictions-container::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.predictions-container::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 一问一答样式 */\r\n.qa-section {\r\n  padding: 0;\r\n}\r\n\r\n.question-section, .answer-section {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.answer-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.qa-label {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n  font-weight: 600;\r\n  font-size: 13px;\r\n}\r\n\r\n.question-label {\r\n  color: #ffb980;\r\n}\r\n\r\n.answer-label {\r\n  color: #00ffff;\r\n}\r\n\r\n.qa-label i {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n.question-text, .answer-text {\r\n  background-color: rgba(0, 0, 0, 0.3);\r\n  border-radius: 8px;\r\n  padding: 12px 15px;\r\n  line-height: 1.6;\r\n  font-size: 13px;\r\n  color: rgba(255, 255, 255, 0.9);\r\n  white-space: pre-wrap;\r\n  word-wrap: break-word;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.question-text {\r\n  border-left: 3px solid #ffb980;\r\n}\r\n\r\n.answer-text {\r\n  border-left: 3px solid #00ffff;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding-right: 18px;\r\n}\r\n\r\n/* 问题文本样式 */\r\n.question-text {\r\n  font-style: italic;\r\n  color: rgba(255, 200, 150, 0.9);\r\n}\r\n\r\n/* 答案文本滚动条样式 */\r\n.answer-text::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 255, 0.5);\r\n  border-radius: 2px;\r\n}\r\n\r\n.answer-text::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n/* 价格趋势卡片特殊样式 */\r\n.price-trend-card {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: auto;\r\n  min-height: 400px;\r\n}\r\n\r\n.price-trend-card .chart {\r\n  flex-shrink: 0;\r\n  height: 300px !important;\r\n  min-height: 300px;\r\n}\r\n\r\n.price-trend-card .price-prediction-section {\r\n  flex-shrink: 0;\r\n  margin-top: 15px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.inventory-total {\r\n  font-size: 12px;\r\n  color: #00ffff;\r\n  font-weight: normal;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 新增：价格趋势图控件样式 */\r\n.price-trend-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 15px;\r\n  padding: 10px;\r\n  background-color: rgba(16, 7, 33, 0.4);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.left-controls, .right-controls {\r\n  flex: 1;\r\n  max-width: 45%;\r\n}\r\n\r\n.curve-label {\r\n  font-size: 13px;\r\n  color: #00ffff;\r\n  margin-bottom: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n.left-controls .curve-label {\r\n  text-align: left;\r\n}\r\n\r\n.right-controls .curve-label {\r\n  text-align: right;\r\n}\r\n\r\n.dropdown-row {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.right-controls .dropdown-row {\r\n  justify-content: flex-end;\r\n}\r\n\r\n.fetch-data-btn-container {\r\n  text-align: right;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.modern-fetch-btn {\r\n  background: rgba(138, 43, 226, 0.7);\r\n  border: none;\r\n  border-radius: 12px;\r\n  padding: 10px 20px;\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 3px 10px rgba(138, 43, 226, 0.3);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-fetch-btn:hover:not(:disabled) {\r\n  background: rgba(138, 43, 226, 0.9);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.5);\r\n}\r\n\r\n.modern-fetch-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.modern-fetch-btn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.modern-fetch-btn.loading {\r\n  background: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.modern-fetch-btn i {\r\n  margin-right: 8px;\r\n  animation: rotate 1s linear infinite;\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n/* Element UI 下拉框样式覆盖 */\r\n.price-trend-controls .el-select {\r\n  background-color: transparent !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner {\r\n  background-color: #4a1c5a !important;\r\n  border: 1px solid rgba(116, 75, 162, 0.5) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 8px !important;\r\n  font-size: 13px !important;\r\n  transition: all 0.3s ease !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:hover {\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  box-shadow: 0 0 8px rgba(116, 75, 162, 0.3) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner:focus {\r\n  border-color: #764ba2 !important;\r\n  box-shadow: 0 0 12px rgba(116, 75, 162, 0.5) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__inner::placeholder {\r\n  color: rgba(255, 255, 255, 0.7) !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-select .el-input__suffix i {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag {\r\n  background-color: rgba(116, 75, 162, 0.6) !important;\r\n  border-color: rgba(116, 75, 162, 0.8) !important;\r\n  color: #ffffff !important;\r\n  border-radius: 6px !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.price-trend-controls .el-tag .el-tag__close:hover {\r\n  background-color: rgba(255, 255, 255, 0.2) !important;\r\n}\r\n\r\n/* 相似物料区域样式 */\r\n.similar-materials-section {\r\n  margin: 20px 0;\r\n  padding: 15px;\r\n  background-color: rgba(16, 7, 33, 0.6);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.similar-materials-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  font-size: 14px;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.similar-materials-header i {\r\n  color: #00ffff;\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.section-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n}\r\n\r\n.loading-info {\r\n  color: #00ffff;\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  font-style: italic;\r\n}\r\n\r\n.similar-materials-container {\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  border-radius: 6px;\r\n  padding: 10px;\r\n  overflow-x: auto;\r\n}\r\n\r\n.materials-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  font-size: 13px;\r\n}\r\n\r\n.materials-table th {\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  color: #00ffff;\r\n  padding: 8px 12px;\r\n  text-align: left;\r\n  border-bottom: 2px solid rgba(0, 212, 255, 0.3);\r\n  font-weight: 600;\r\n}\r\n\r\n.materials-table td {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.material-row {\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.material-row:hover {\r\n  background-color: rgba(0, 212, 255, 0.05);\r\n}\r\n\r\n.rank-cell {\r\n  text-align: center;\r\n  width: 60px;\r\n}\r\n\r\n.rank-badge {\r\n  display: inline-block;\r\n  padding: 4px 8px;\r\n  border-radius: 50%;\r\n  color: #fff;\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n  min-width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.rank-first {\r\n  background: linear-gradient(135deg, #ffd700, #ffb347);\r\n  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.rank-second {\r\n  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);\r\n  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);\r\n}\r\n\r\n.rank-third {\r\n  background: linear-gradient(135deg, #cd7f32, #b8860b);\r\n  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);\r\n}\r\n\r\n.rank-default {\r\n  background-color: rgba(138, 43, 226, 0.7);\r\n}\r\n\r\n.material-name, .compare-material-name {\r\n  font-weight: 500;\r\n  color: #fff;\r\n}\r\n\r\n.compare-material-name {\r\n  color: #00ffff;\r\n}\r\n\r\n.score-cell {\r\n  text-align: center;\r\n  width: 120px;\r\n  min-width: 120px;\r\n}\r\n\r\n.score-value {\r\n  display: inline-block;\r\n  padding: 2px 6px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 4px;\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n}\r\n\r\n.score-desc {\r\n  color: #ffb980;\r\n  font-style: italic;\r\n}\r\n\r\n.category-cell {\r\n  color: #5fd8b6;\r\n  font-weight: 500;\r\n}\r\n\r\n.similar-materials-placeholder {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  text-align: center;\r\n  padding: 20px;\r\n  font-size: 12px;\r\n}\r\n\r\n.similar-materials-group {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.similar-materials-group:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.group-title {\r\n  color: #00ffff;\r\n  font-weight: bold;\r\n  font-size: 14px;\r\n  margin-bottom: 10px;\r\n  padding: 8px 12px;\r\n  background-color: rgba(0, 212, 255, 0.1);\r\n  border-radius: 6px;\r\n  border-left: 3px solid #00ffff;\r\n}\r\n\r\n.price-type-cell {\r\n  color: #e879ed;\r\n  font-size: 11px;\r\n  max-width: 120px;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.algorithm-desc {\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 11px;\r\n  font-style: italic;\r\n  margin-left: 8px;\r\n}\r\n\r\n.action-cell {\r\n  text-align: center;\r\n  width: 100px;\r\n}\r\n\r\n.view-comparison-btn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 6px;\r\n  padding: 8px 16px;\r\n  color: #fff;\r\n  font-size: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  white-space: nowrap;\r\n  min-width: 70px;\r\n}\r\n\r\n.view-comparison-btn:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);\r\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\r\n}\r\n\r\n.view-comparison-btn:active {\r\n  transform: translateY(0);\r\n}\r\n\r\n.view-comparison-btn i {\r\n  font-size: 13px;\r\n}\r\n\r\n/* 对比弹框样式 */\r\n.comparison-dialog .el-dialog {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__header {\r\n  background: linear-gradient(135deg, rgba(33, 10, 56, 0.9), rgba(0, 212, 255, 0.2));\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid rgba(0, 212, 255, 0.3);\r\n}\r\n\r\n.comparison-dialog .el-dialog__title {\r\n  color: #00ffff;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close {\r\n  color: #00ffff;\r\n  font-size: 20px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.comparison-dialog .el-dialog__headerbtn .el-dialog__close:hover {\r\n  color: #fff;\r\n  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);\r\n}\r\n\r\n.comparison-dialog .el-dialog__body {\r\n  padding: 0;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-content {\r\n  padding: 20px;\r\n  background: transparent;\r\n}\r\n\r\n.comparison-header {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.comparison-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 15px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.base-material {\r\n  color: #00ffff;\r\n  padding: 8px 16px;\r\n  background-color: rgba(0, 212, 255, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.vs-text {\r\n  color: #fff;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.compare-material {\r\n  color: #ea7ccc;\r\n  padding: 8px 16px;\r\n  background-color: rgba(234, 124, 204, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(234, 124, 204, 0.5);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.similarity-info {\r\n  color: #ffb980;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  padding: 4px 12px;\r\n  background-color: rgba(255, 185, 128, 0.2);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 185, 128, 0.4);\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.comparison-chart-container {\r\n  background-color: rgba(33, 10, 56, 0.7);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(0, 212, 255, 0.3);\r\n  overflow: hidden;\r\n  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);\r\n}\r\n\r\n.comparison-chart {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n</style>\r\n"]}]}