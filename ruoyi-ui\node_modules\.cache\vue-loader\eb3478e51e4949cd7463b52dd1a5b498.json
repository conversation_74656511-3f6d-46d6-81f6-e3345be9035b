{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\scrapDetail\\index.vue?vue&type=template&id=c2ea2d10&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\scrapDetail\\index.vue", "mtime": 1756456493917}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}