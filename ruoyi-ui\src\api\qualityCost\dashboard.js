import request from '@/utils/request'

// 查询质量成本四大类别占比数据
export function getPieChartData(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getPieChartData',
    method: 'get',
    params: query
  })
}


export function getMultiLineChartData(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getMultiLineChartData',
    method: 'get',
    params: query
  })
}

export function getQualityCostDetail(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getQualityCostDetail',
    method: 'get',
    params: query
  })
}

export function getExternalCostDetail(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getExternalCostDetail',
    method: 'get',
    params: query
  })
}

export function getInternalCostDetail(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getInternalCostDetail',
    method: 'get',
    params: query
  })
}


export function getComboChartDetail(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getComboChartDetail',
    method: 'get',
    params: query
  })
}

export function getWaterfallChartDetail(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getWaterfallChartDetail',
    method: 'get',
    params: query
  })
}

export function getFactoryRejectionChartDetail(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getFactoryRejectionChartDetail',
    method: 'get',
    params: query
  })
}

export function getFactoryScrapChartDetail(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getFactoryScrapChartDetail',
    method: 'get',
    params: query
  })
}

export function getFactoryContractChartDetail(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getFactoryContractChartDetail',
    method: 'get',
    params: query
  })
}

export function getFactoryReturnChartDetail(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getFactoryReturnChartDetail',
    method: 'get',
    params: query
  })
}

export function getScrapLossChartDetailsDetail(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getScrapLossChartDetailsDetail',
    method: 'get',
    params: query
  })
}

export function getQualityObjectionLossDetail(query) {
  return request({
    url: '/qualityCost/qualityCostDashboard/getQualityObjectionLossDetail',
    method: 'get',
    params: query
  })
}

