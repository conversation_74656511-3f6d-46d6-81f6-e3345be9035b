import request from '@/utils/request'

// 查询供应商专用人员列表
export function listSupplierInfo(query) {
  return request({
    url: '/web/supply/supplier/list',
    method: 'get',
    params: query
  })
}

// 查询供应商信息详细
export function getSupplierInfo(supplyCode) {
  return request({
    url: '/web/supply/supplier/info/' + supplyCode,
    method: 'get'
  })
}

// 修改供应商信息
export function updateSupplierInfo(data) {
  return request({
    url: '/web/supply/supplier/info',
    method: 'put',
    data: data
  })
}

// 查询供应商人员详细
export function getSupplierUserInfo(id) {
  return request({
    url: '/web/supply/supplier/user/' + id,
    method: 'get'
  })
}

// 新增供应商人员
export function addSupplierUserInfo(data) {
  return request({
    url: '/web/supply/supplier/user',
    method: 'post',
    data: data
  })
}

// 修改供应商人员
export function updateSupplierUserInfo(data) {
  return request({
    url: '/web/supply/supplier/user',
    method: 'put',
    data: data
  })
}

// 删除供应商人员
export function delSupplierUserInfo(id) {
  return request({
    url: '/web/supply/supplier/user/' + id,
    method: 'delete'
  })
}

// 导出供应商人员
export function exportSupplierUserInfo(query) {
  return request({
    url: '/web/supply/supplier/export',
    method: 'post',
    data: query
  })
}

// 导入供应商人员
export function importSupplierUserInfo(data) {
  return request({
    url: '/web/supply/supplier/import',
    method: 'post',
    data: data
  })
}

// 下载供应商人员导入模板
export function downloadSupplierTemplate() {
  return request({
    url: '/web/supply/supplier/importTemplate',
    method: 'post'
  })
}

// 下载供应商人员文件
export function downloadSupplierFile(id) {
  return request({
    url: '/web/supply/supplier/download/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

// 获取供应商岗位识别卡信息
export function getSupplierFac(userId) {
  return request({
    url: '/web/supply/supplier/fac/' + userId,
    method: 'get'
  })
}

// 新增供应商岗位识别卡
export function addSupplierFac(data) {
  return request({
    url: '/web/supply/supplier/fac',
    method: 'post',
    data: data
  })
}

// 修改供应商岗位识别卡
export function updateSupplierFac(data) {
  return request({
    url: '/web/supply/supplier/fac',
    method: 'put',
    data: data
  })
}

// 获取供应商健康信息
export function getSupplierHealth(userId) {
  return request({
    url: '/web/supply/supplier/health/' + userId,
    method: 'get'
  })
}

// 新增供应商健康信息
export function addSupplierHealth(data) {
  return request({
    url: '/web/supply/supplier/health',
    method: 'post',
    data: data
  })
}

// 修改供应商健康信息
export function updateSupplierHealth(data) {
  return request({
    url: '/web/supply/supplier/health',
    method: 'put',
    data: data
  })
}

// 查询供应商文件列表
export function listSupplierFile(userId) {
  return request({
    url: '/web/supply/supplier/file/list/' + userId,
    method: 'get'
  })
}

// 删除供应商文件
export function delSupplierFile(id) {
  return request({
    url: '/web/supply/supplier/file/' + id,
    method: 'delete'
  })
}
