package com.ruoyi.web.controller.xctg;

import com.ruoyi.app.qs.domain.imp.QsRoleImp;
import com.ruoyi.app.v1.domain.*;
import com.ruoyi.app.v1.enums.dataReportFrequency;
import com.ruoyi.app.v1.service.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.multipart.MultipartFile;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * TYjyAnswerController
 * 
 * <AUTHOR>
 * @date 2024-10-21
 */
@RestController
@RequestMapping("/web/TYjy/answer")
public class WebTYjyAnswerController extends BaseController
{
    @Autowired
    private ITYjyAnswerService tYjyAnswerService;

    @Autowired
    private ITYjyFormService tYjyFormService;

//    @Autowired
//    private IStaticAnswerService staticAnswerService;

    @Autowired
    private ITYjyAnswerHisService tYjyAnswerHisService;

    @Autowired
    private ITYjyPermissionService tYjyPermissionService;
    @Autowired
    private ITYjyDeptService tYjyDeptService;

    @Autowired
    private ITYjyDimensionalityService tYjyDimensionalityService;
    /**
     * 查询TYjyAnswer列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TYjyAnswer tYjyAnswer)
    {
        startPage();
        List<TYjyAnswer> list = tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
        return getDataTable(list);
    }

    /**
     *  根据工号查询用户相关可以审核的问题
     */
    @GetMapping("/checklist")
    public TableDataInfo checklist(TYjyAnswer tYjyAnswer)
    {
        tYjyAnswer.setCheckWorkNo(SecurityUtils.getUsername());
        startPage();
        List<TYjyAnswer> list = tYjyAnswerService.selectCheckList(tYjyAnswer);
        return getDataTable(list);
    }



    /**
     *  根据工号查询用户相关可以审核的问题
     */
    @GetMapping("/checklistplus")
    public AjaxResult checklistplus(TYjyAnswer tYjyAnswer)
    {
        if(tYjyAnswer.getFrequency().equals("9"))
        {
            tYjyAnswer.setFrequency(null);
        }
        tYjyAnswer.setCheckWorkNo(SecurityUtils.getUsername());
        startPage();
        List<TYjyAnswer> list = tYjyAnswerService.selectCheckList(tYjyAnswer);
        Long total =getDataTable(list).getTotal();
        List<JSONObject> result= new ArrayList<>();
        Map<String,Map<String,String>> dimensionalityMap=new HashMap<>();
        for(TYjyAnswer item:list)
        {
            TYjyForm form=tYjyFormService.selectTYjyFormById(item.getFormId());
            TYjyAnswer search =new TYjyAnswer();
            search.setCreatorDept(item.getCreatorDept());
            search.setFormId(item.getFormId());
            search.setFcDate(item.getFcDate());
            JSONObject jsonResult=new JSONObject();
            jsonResult.put("id",item.getId());
            jsonResult.put("formId",item.getFormId());
            jsonResult.put("formQuestion",item.getFormQuestion());
            jsonResult.put("formType",item.getFormType());
            jsonResult.put("creatorDept",item.getCreatorDept());
            jsonResult.put("creatorName",item.getCreatorName());
            jsonResult.put("fcDate",tYjyFormService.timetranslate(item.getFcDate(),item.getFrequency()));
            jsonResult.put("formValue",item.getFormValue());
            jsonResult.put("dimensionalityPath",form.getDimensionalityPath());
            jsonResult.put("formQuestion",form.getFormQuestion());
            jsonResult.put("frequency",form.getFrequency());
            jsonResult.put("formType",form.getFormType());
//            jsonResult.put("dimensionalityName",tYjyFormService.searchNames(form.getDimensionalityId()));
            jsonResult.put("dimensionalityName",tYjyFormService.searchNames(form.getDimensionalityPath(),dimensionalityMap));
            jsonResult.put("formNote",form.getFormNote());
            jsonResult.put("formUnit",form.getUnit());
            result.add(jsonResult);
        }
        JSONObject re=new JSONObject();
        re.put("data",result);
        re.put("total",total);
        return AjaxResult.success(re);
//        if(tYjyAnswer.getFrequency().equals("9"))
//        {
//            tYjyAnswer.setFrequency(null);
//        }
//        tYjyAnswer.setCheckWorkNo(SecurityUtils.getUsername());
//        int total = tYjyAnswerService.selectCheckListCount(tYjyAnswer);
//        startPage();
//        List<TYjyAnswer> list = tYjyAnswerService.selectCheckList(tYjyAnswer);
//        List<JSONObject> result= new ArrayList<>();
//        JSONObject json = new JSONObject();
//        List<JSONObject> array=new ArrayList<>();
//        for(TYjyAnswer item:list)
//        {
//            TYjyAnswer search =new TYjyAnswer();
//            search.setCreatorDept(item.getCreatorDept());
//            search.setFormId(item.getFormId());
//            search.setFcDate(item.getFcDate());
//            List<TYjyAnswer> searchlist=tYjyAnswerService.selectAnswerLast3forcheck(search);
//            JSONObject jsonResult=new JSONObject();
//            JSONObject hisData=new JSONObject();
//            JSONObject hisDatain=new JSONObject();
//            jsonResult.put("id",item.getId());
//            jsonResult.put("formId",item.getFormId());
//            jsonResult.put("formQuestion",item.getFormQuestion());
//            jsonResult.put("formType",item.getFormType());
//            jsonResult.put("creatorDept",item.getCreatorDept());
//            jsonResult.put("fcDate",tYjyFormService.timetranslate(item.getFcDate(),item.getFrequency()));
//            int count=searchlist.size();
//            if(item.getFormValue()==null)
//            {
//
//                jsonResult.put("formValue",null);
//            }
//            else
//            {
//                if(item.getFormType().equals("0"))
//                {
//                    jsonResult.put("formValue",Integer.valueOf(item.getFormValue()));
//                }
//                if(item.getFormType().equals("1"))
//                {
//                    jsonResult.put("formValue",Double.valueOf(item.getFormValue()));
//
//                }
//                if(item.getFormType().equals("2")||item.getFormType().equals("3"))
//                {
//                    jsonResult.put("formValue",item.getFormValue());
//                }
//            }
//            if(item.getFormFile()==null)
//            {
//                jsonResult.put("formFile", new ArrayList());
//            }
//            else
//            {
//                jsonResult.put("formFile",JSONObject.parse(item.getFormFile()));
//            }
//            jsonResult.put("status",item.getStatus());
//            jsonResult.put("checkHistory",item.getCheckHistory());
//            int num=0;
//            while(num<count)
//            {
//                hisDatain=new JSONObject();
//                hisDatain.put("title",searchlist.get(num).getFcDate()+"("+searchlist.get(num).getCreatorName()+")");
//                if(searchlist.get(num).getFormValue()==null)
//                {
//                    hisDatain.put("formValue",null);
//                }
//                else
//                {
//                    if(searchlist.get(num).getFormType().equals("0"))
//                    {
//                        hisDatain.put("value",Integer.valueOf(searchlist.get(num).getFormValue()));
//                    }
//                    if(searchlist.get(num).getFormType().equals("1"))
//                    {
//                        hisDatain.put("value",Double.valueOf(searchlist.get(num).getFormValue()));
//                    }
//                    if(searchlist.get(num).getFormType().equals("2")||searchlist.get(num).getFormType().equals("3"))
//                    {
//                        hisDatain.put("value",searchlist.get(num).getFormValue());
//                    }
//                }
//                hisDatain.put("status",searchlist.get(num).getStatus());
//                hisDatain.put("checkHistory",searchlist.get(num).getCheckHistory());
//                hisData.put("data"+(num+1),hisDatain);
//                num=num+1;
//            }
//            while(num<3)
//            {
//                hisDatain=new JSONObject();
//                hisDatain.put("title",null);
//                hisDatain.put("formValue",null);
//                hisDatain.put("status","4");
//                hisDatain.put("checkHistory","");
//                hisData.put("data"+(num+1),hisDatain);
//                num=num+1;
//            }
//            jsonResult.put("hisData",hisData);
//            array.add(jsonResult);
//        }
//        json.put("data",array);
//        result.add(json);
//        JSONObject re=new JSONObject();
//        re.put("data",result);
//        re.put("total",total);
//        return AjaxResult.success(re);
    }


    @GetMapping("/everydaylist")
    public TableDataInfo everydaylist(TYjyAnswer tYjyAnswer)
    {
        tYjyAnswer.setWorkNo(SecurityUtils.getUsername());
        startPage();
        List<TYjyAnswer> list = tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
        return getDataTable(list);
    }

//    /**
//     * 查询TYjyAnswer列表
//     */
//    @GetMapping("/hislist")
//    public TableDataInfo hislist(TYjyAnswer tYjyAnswer)
//    {
//        Date currentDate = new Date(); // 创建Calendar实例，并设置为当前时间
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(currentDate); //
//        Calendar changetime ;
//        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
//        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
//        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
//
//        String frequency="0";
//        if(frequency=="0")//每日填报
//        {
//            if(currentDay>=3)
//            {
//                tYjyAnswer.setFcDate(currentYear+"-"+(currentMonth+1)+"-"+(currentDay-3));
//            }
//            else
//            {
//                changetime = Calendar.getInstance();
//                changetime.set(currentYear,currentMonth,currentDay);
//                changetime.add(Calendar.DAY_OF_YEAR,-3);
//                tYjyAnswer.setFcDate(changetime.get(Calendar.YEAR)+"-"+(changetime.get(Calendar.MONTH)+1)+"-"+changetime.get(Calendar.DAY_OF_MONTH));
//            }
//
//        }
//        else if(frequency=="1")//每月填报,可能需要考虑会计月，或者再统计结果的时候会要进行对会计月的转化
//        {
//            if(currentMonth>=2)
//            {
//                tYjyAnswer.setFcDate(currentYear+"-"+(currentMonth-2));
//            }
//            else
//            {
//                tYjyAnswer.setFcDate((currentYear-1)+"-"+"12");
//            }
//        }else if(frequency=="2")//每季填报
//        {
//            if(currentMonth>=9)
//            {
//                tYjyAnswer.setFcDate(currentYear+"-"+(currentMonth-8));
//            }
//            else
//            {
//                tYjyAnswer.setFcDate((currentYear-1)+"-"+(currentMonth+3));
//            }
//        }else if(frequency=="3")//每半年填报
//        {
//            if(currentMonth>=6)
//            {
//                tYjyAnswer.setFcDate((currentYear-1)+"-"+(currentMonth-6));
//            }
//            else
//            {
//                tYjyAnswer.setFcDate((currentYear-2)+"-"+(currentMonth+6));
//            }
//        }else if(frequency=="4")//每年填报
//        {
//            tYjyAnswer.setFcDate((currentYear-3)+"");
//        }
//
//        startPage();
//        List<TYjyAnswer> list = tYjyAnswerService.selectAnswerWithPermission(tYjyAnswer);
//        Map<Long,TYjyAnswer> map= new HashMap<>();
//        for(TYjyAnswer item:list)
//        {
//            map.put(item.getFormId(),item);
//        }
//        return getDataTable(list);
//    }

    /**
     * 查询TYjyAnswer列表
     */
    @GetMapping("/formlist")
    public TableDataInfo formlist(TYjyAnswer tYjyAnswer)
    {
        startPage();
        List<TYjyAnswer> list = tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
        return getDataTable(list);
    }


    /**
     * 导出TYjyAnswer列表
     */
    @Log(title = "TYjyAnswer", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TYjyAnswer tYjyAnswer)
    {
        List<TYjyAnswer> list = tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
        ExcelUtil<TYjyAnswer> util = new ExcelUtil<TYjyAnswer>(TYjyAnswer.class);
        return util.exportExcel(list, "answer");
    }

    /**
     * 获取TYjyAnswer详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tYjyAnswerService.selectTYjyAnswerById(id));
    }


    /**
     * 获取TYjyAnswer历史信息
     */
    @GetMapping(value = "last/{id}")
    public AjaxResult getLastAnswer(@PathVariable("id") Long id)
    {
        TYjyAnswer tYjyAnswer=new TYjyAnswer();
        SysUser user= SecurityUtils.getLoginUser().getUser();
        tYjyAnswer.setFormId(id);
        tYjyAnswer.setCreatorDept(user.getRsDeptCode());
        Calendar changetime ;
        Date currentDate = new Date(); // 创建Calendar实例，并设置为当前时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate); //
        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
        TYjyForm re=tYjyFormService.selectTYjyFormById(id);
        String frequency=re.getFrequency();
        String formType= re.getFormType();
        String dimensionalityNames=tYjyFormService.searchNames(re.getDimensionalityId());
        if(frequency.equals("0"))//每日填报
        {
            if(currentDay>=3)
            {
                tYjyAnswer.setFcDate(currentYear+"-"+(currentMonth+1)+"-"+(currentDay-3));
            }
            else
            {
                changetime = Calendar.getInstance();
                changetime.set(currentYear,currentMonth,currentDay);
                changetime.add(Calendar.DAY_OF_YEAR,-3);
                tYjyAnswer.setFcDate(changetime.get(Calendar.YEAR)+"-"+(changetime.get(Calendar.MONTH)+1)+"-"+changetime.get(Calendar.DAY_OF_MONTH));
            }
        }
        else if(frequency.equals("1"))//每月填报,可能需要考虑会计月，或者再统计结果的时候会要进行对会计月的转化
        {
            if(currentMonth>=2)
            {
                tYjyAnswer.setFcDate(currentYear+"-"+(currentMonth-2));
            }
            else
            {
                tYjyAnswer.setFcDate((currentYear-1)+"-"+"12");
            }
        }else if(frequency.equals("2"))//每季填报
        {
            if(currentMonth>=9)
            {
                tYjyAnswer.setFcDate(currentYear+"-"+(currentMonth-8));
            }
            else
            {
                tYjyAnswer.setFcDate((currentYear-1)+"-"+(currentMonth+3));
            }
        }else if(frequency.equals("3"))//每半年填报
        {
            if(currentMonth>=6)
            {
                tYjyAnswer.setFcDate((currentYear-1)+"-"+(currentMonth-6));
            }
            else
            {
                tYjyAnswer.setFcDate((currentYear-2)+"-"+(currentMonth+6));
            }
        }else if(frequency.equals("4"))//每年填报
        {
            tYjyAnswer.setFcDate((currentYear-3)+"");
        }
        JSONObject result = new JSONObject();
        result.put("formType",formType);
        result.put("dimensionalityNames",dimensionalityNames);
        result.put("data",tYjyAnswerService.selectAnswerHisByFormId(tYjyAnswer));
        return AjaxResult.success(result);
    }

//    /**
//     * 新增TYjyAnswer
//     */
//    @Log(title = "TYjyAnswer", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody TYjyAnswer tYjyAnswer)
//    {
//        return toAjax(tYjyAnswerService.insertTYjyAnswer(tYjyAnswer));
//    }


//    /**
//     * 新增TYjyAnswer
//     */
//    @Log(title = "TYjyAnswer", businessType = BusinessType.INSERT)
//    @PostMapping("/add")
//    public AjaxResult add(@RequestBody List<String> tYjyAnswers)
//    {
//        //需要确定是怎么拆分的
//        TYjyAnswer tYjyAnswer;
//        SysUser user= SecurityUtils.getLoginUser().getUser();
//        for(String item:tYjyAnswers)
//        {
//            //需要确认到底是如何实现拆封的
//            tYjyAnswer=new TYjyAnswer();
//            tYjyAnswer.setFormId(Long.valueOf("111"));
//            tYjyAnswer.setFcDate("1111");
//            tYjyAnswer.setDelFlag("0");
//
//            List<TYjyAnswer> list = tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
//            if(list.size()>0)
//            {
//                tYjyAnswerHisService.insertTYjyAnswerHisByFormIdAndFcDate(tYjyAnswer);
//                tYjyAnswerService.deleteTYjyAnswerById(list.get(0).getId());
//                tYjyAnswer.setVersion(Long.valueOf(list.get(0).getVersion()+1));
//            }
//            else
//            {
//                tYjyAnswer.setVersion(Long.valueOf(1));
//            }
//
//            tYjyAnswer.setCreatorName(user.getNickName());
//            tYjyAnswer.setCreatorNo(user.getUserName());
//            tYjyAnswer.setCreatorDept("1111");
//            tYjyAnswer.setFormValue("101010");
//
//            tYjyAnswerService.insertTYjyAnswer(tYjyAnswer);
//        }
//        return AjaxResult.success("1");
//    }

//    /**
//     * 新增TYjyAnswer
//     */
//    @Log(title = "TYjyAnswer", businessType = BusinessType.INSERT)
//    @PostMapping("/add")
//    public AjaxResult add(@RequestBody JSONObject json)
//    {
//        Set<String> jsonlist=json.keySet();
//        TYjyAnswer tYjyAnswer;
//        SysUser user= SecurityUtils.getLoginUser().getUser();
//        Date currentDate = new Date(); // 创建Calendar实例，并设置为当前时间
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(currentDate); //
//        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
//        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
//        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
//        String fcDate=currentYear+"-"+(currentMonth+1)+"-"+currentDay;
//        for (String item : jsonlist) {
//            //需要确认到底是如何实现拆封的
//            tYjyAnswer=new TYjyAnswer();
//            tYjyAnswer.setFormId(Long.valueOf(item.split("-")[1]));
//            tYjyAnswer.setFcDate(fcDate);
//            tYjyAnswer.setDelFlag("0");
//            List<TYjyAnswer> list = tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
//            if(list.size()>0)
//            {
//                tYjyAnswerHisService.insertTYjyAnswerHisByFormIdAndFcDate(tYjyAnswer);
//                tYjyAnswerService.deleteTYjyAnswerById(list.get(0).getId());
//                tYjyAnswer.setVersion(Long.valueOf(list.get(0).getVersion()+1));
//            }
//            else
//            {
//                tYjyAnswer.setVersion(Long.valueOf(1));
//            }
//            tYjyAnswer.setCreatorName(user.getNickName());
//            tYjyAnswer.setCreatorNo(user.getUserName());
//            tYjyAnswer.setCreatorDept(user.getRsDeptCode());
//            if(json.get(item)==null)
//            {
//                tYjyAnswer.setFormValue(null);
//            }
//            else
//            {
//                tYjyAnswer.setFormValue(json.get(item).toString());
//            }
//            tYjyAnswerService.insertTYjyAnswer(tYjyAnswer);
//        }
//        return AjaxResult.success("1");
//    }
//
//
//    /**
//     * 新增TYjyAnswer
//     */
//    @Log(title = "TYjyAnswer", businessType = BusinessType.INSERT)
//    @PostMapping("/addbystring")
//    public AjaxResult addbystring(@RequestBody String jsonString)
//    {
//        JSONObject json=JSONObject.parseObject(jsonString);
//        Set<String> jsonlist=json.keySet();
//        TYjyAnswer tYjyAnswer;
//        SysUser user= SecurityUtils.getLoginUser().getUser();
//        Date currentDate = new Date(); // 创建Calendar实例，并设置为当前时间
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(currentDate); //
//        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
//        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
//        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
//        String fcDate=currentYear+"-"+(currentMonth+1)+"-"+currentDay;
//        for (String item : jsonlist) {
//            //需要确认到底是如何实现拆封的
//            tYjyAnswer=new TYjyAnswer();
//            tYjyAnswer.setFormId(Long.valueOf(item.split("-")[1]));
//            tYjyAnswer.setFcDate(fcDate);
//            tYjyAnswer.setDelFlag("0");
//            List<TYjyAnswer> list = tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
//            if(list.size()>0)
//            {
//                tYjyAnswerHisService.insertTYjyAnswerHisByFormIdAndFcDate(tYjyAnswer);
//                tYjyAnswerService.deleteTYjyAnswerById(list.get(0).getId());
//                tYjyAnswer.setVersion(Long.valueOf(list.get(0).getVersion()+1));
//            }
//            else
//            {
//                tYjyAnswer.setVersion(Long.valueOf(1));
//            }
//            tYjyAnswer.setCreatorName(user.getNickName());
//            tYjyAnswer.setCreatorNo(user.getUserName());
//            tYjyAnswer.setCreatorDept(user.getRsDeptCode());
//            if(json.get(item)==null)
//            {
//                tYjyAnswer.setFormValue(null);
//            }
//            else
//            {
//                tYjyAnswer.setFormValue(json.get(item).toString());
//            }
//            tYjyAnswerService.insertTYjyAnswer(tYjyAnswer);
//        }
//        return AjaxResult.success("1");
//    }


    /**
     * 新增TYjyAnswer
     */
    @Log(title = "TYjyAnswer", businessType = BusinessType.INSERT)
    @PostMapping("/newadd")
    public AjaxResult newadd(@RequestBody List<JSONObject> json)
    {
        return AjaxResult.success(tYjyAnswerService.dealnewadd(json));

//        TYjyAnswer tYjyAnswer;
//        SysUser user= SecurityUtils.getLoginUser().getUser();
//        Date currentDate = new Date();
//        SimpleDateFormat addtime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String formattedDate = addtime.format(currentDate);
//        // 创建Calendar实例，并设置为当前时间
//        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//        Calendar calendar = Calendar.getInstance();
//        if(json.size()>0)
//        {
//                if(json.get(0).get("fcDate")!=null)
//                {
//                    try {
//                        currentDate = formatter.parse(json.get(0).get("fcDate").toString());
//                        calendar.setTime(currentDate);
//                    } catch (ParseException e) {
//                        // 异常处理代码
//                        e.printStackTrace();
//                        // 或者其他错误处理逻辑
//                    }
//                }
//        }
//
//        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
//        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
//        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
//        String fcDate=currentYear+"";
//        if(currentMonth<9)
//        {
//            fcDate=fcDate+"-0"+(currentMonth+1);
//        }
//        else
//        {
//            fcDate=fcDate+"-"+(currentMonth+1);
//        }
//        if(currentDay<10)
//        {
//            fcDate=fcDate+"-0"+currentDay;
//        }
//        else
//        {
//            fcDate=fcDate+"-"+currentDay;
//        }
//
//        for (JSONObject item : json) {
//            //需要确认到底是如何实现拆封的
//            tYjyAnswer=new TYjyAnswer();
//            tYjyAnswer.setFormId(Long.valueOf(item.get("formId").toString()));
//            if(item.get("reason")!=null)
//            {
//                tYjyAnswer.setReason(item.get("reason").toString());
//            }
//            if(item.get("measure")!=null)
//            {
//                tYjyAnswer.setMeasure(item.get("measure").toString());
//            }
//            TYjyForm search=tYjyFormService.selectTYjyFormById(Long.valueOf(item.get("formId").toString()));
//            if(search.getFrequency().equals("0"))//时间上的判断还需要进一步的考虑，还有进一步确定要获取的范围
//            {
//                tYjyAnswer.setFcDate(fcDate);
//            }else if(search.getFrequency().equals("1"))
//            {
//                if(currentMonth<9)
//                {
//                    tYjyAnswer.setFcDate(currentYear+"-0"+(currentMonth+1)+"-01");
//                }
//                else
//                {
//                    tYjyAnswer.setFcDate(currentYear+"-"+(currentMonth+1)+"-01");
//                }
//            }else if(search.getFrequency().equals("2"))
//            {
//                if(currentMonth<=2)
//                {
//                    tYjyAnswer.setFcDate(currentYear+"-01-01");
//                }else if(currentMonth<=5)
//                {
//                    tYjyAnswer.setFcDate(currentYear+"-04-01");
//                }else if(currentMonth<=8)
//                {
//                    tYjyAnswer.setFcDate(currentYear+"-07-01");
//                }else if(currentMonth<=11)
//                {
//                    tYjyAnswer.setFcDate(currentYear+"-10-01");
//                }
//            }else if(search.getFrequency().equals("3"))
//            {
//                if(currentMonth<=5)
//                {
//                    tYjyAnswer.setFcDate(currentYear+"-01-01");
//                }
//                else
//                {
//                    tYjyAnswer.setFcDate(currentYear+"-07-01");
//                }
//            }else if(search.getFrequency().equals("4"))
//            {
//                tYjyAnswer.setFcDate(currentYear+"-01-01");
//            }else if(search.getFrequency().equals("5"))
//            {
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                calendar.add(Calendar.DAY_OF_YEAR,-1);
//                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
//                tYjyAnswer.setFcDate(dateFormat.format(calendar.getTime()));
//            }
//            tYjyAnswer.setDelFlag("0");
//            //*** 需要修改
//            tYjyAnswer.setWorkNo(user.getUserName());
//            tYjyAnswer.setCreatorDept(item.get("creatorDept").toString());
//            List<TYjyAnswer> list = tYjyAnswerService.selectTYjyAnswerListCount(tYjyAnswer);
//            if(list.size()>0)
//            {
//                tYjyAnswerHisService.insertTYjyAnswerHisByFormIdAndFcDate(tYjyAnswer);
//                tYjyAnswerService.deleteTYjyAnswerById(list.get(0).getId());
//                tYjyAnswer.setVersion(Long.valueOf(list.get(0).getVersion()+1));
//                tYjyAnswer.setCheckHistory(list.get(0).getCheckHistory());
//            }
//            else
//            {
//                tYjyAnswer.setVersion(Long.valueOf(1));
//                tYjyAnswer.setCheckHistory("");
//            }
//            tYjyAnswer.setCreatorName(user.getNickName());
//            tYjyAnswer.setCreatorNo(user.getUserName());
//
//            if(item.get("formValue")==null)
//            {
//                tYjyAnswer.setFormValue(null);
//            }
//            else
//            {
//                tYjyAnswer.setFormValue(item.get("formValue").toString());
//            }
//
//            //新增添加审核人的流程
//
//            tYjyAnswer.setCheckerList(search.getCheckerList());
//            tYjyAnswer.setCheckHistory(tYjyAnswer.getCheckHistory()+formattedDate+" "+user.getNickName()+" 提交审批;\n");
//            JSONArray checklist=tYjyFormService.getchecklist(search.getCheckerList(),item.get("creatorDept").toString());
//            //下面新增对于常态表的插入
//
//            TYjyDept searchdept=new TYjyDept();
//            List<TYjyDept> deptList=tYjyDeptService.selectTYjyDeptList(searchdept);
//            Map<String,String> deptListmap=new HashMap<>();
//            for(TYjyDept deptitem:deptList)
//            {
//                deptListmap.put(deptitem.getPath(),deptitem.getDeptName());
//            }
//
//            if(checklist==null  || checklist.size()==0)
//            {
//                tYjyAnswer.setStatus("2");
//                tYjyAnswerService.staticAnswer(tYjyAnswer,deptListmap,search);
//            }
//            else
//            {
//                tYjyAnswer.setStatus("0");
//                JSONObject userinfo =tYjyFormService.userinfo(checklist.get(0).toString());
//                tYjyAnswer.setCheckUserName(userinfo.get("userName").toString());
//                tYjyAnswer.setCheckWorkNo(userinfo.get("workNo").toString());
//            }
//            tYjyAnswerService.insertTYjyAnswer(tYjyAnswer);
//
//        }
//        return AjaxResult.success("1");
    }


    /**
     * 新增TYjyAnswer,单个新增且传的对象为 TYjyAnswer
     */
    @Log(title = "TYjyAnswer", businessType = BusinessType.INSERT)
    @PostMapping("/addalone")
    public AjaxResult addalone(@RequestBody TYjyAnswer tYjyAnswer)
    {
        return AjaxResult.success(tYjyAnswerService.dealaddalone(tYjyAnswer));
    }



    //修改审核状态
    @Log(title = "TYjyAnswer", businessType = BusinessType.INSERT)
    @PutMapping("/check")
    public AjaxResult check(@RequestBody TYjyAnswer tYjyAnswer)
    {
        TYjyAnswer check=new  TYjyAnswer();
        TYjyAnswer search=tYjyAnswerService.selectTYjyAnswerById(tYjyAnswer.getId());
        JSONArray checklist=tYjyFormService.getchecklist(search.getCheckerList(),search.getCreatorDept());

//        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date currentDate = new Date();
        String formattedDate = formatter.format(currentDate);

        if(tYjyAnswer.getStatus().equals("3"))
        {
            check.setId(tYjyAnswer.getId());
            check.setStatus("3");
            check.setCheckWorkNo("");
            check.setCheckUserName("");
            check.setAssessment(tYjyAnswer.getAssessment());
            check.setCheckHistory(search.getCheckHistory()+formattedDate+" "+search.getCheckUserName()+" 驳回了审批，驳回意见为："+tYjyAnswer.getAssessment()+";\n");//不确定要不要再拼上一句话
        }
        else
        {
            check.setId(tYjyAnswer.getId());
            for(int i=0;i<checklist.size();i++)
            {
                JSONObject userinfo =tYjyFormService.userinfo(checklist.get(i).toString());
                if(search.getCheckWorkNo().equals(userinfo.get("workNo").toString()))
                {
                    if(search.getCheckHistory()==null)
                    {
                        search.setCheckHistory("");
                    }
                    if(i==checklist.size()-1)
                    {
                        check.setStatus("2");
                        check.setCheckWorkNo("");
                        check.setCheckUserName("");
                        check.setCheckHistory(search.getCheckHistory()+formattedDate+" "+search.getCheckUserName()+"  通过了审批，审批意见为："+tYjyAnswer.getAssessment()+";\n");
                        //此处进行记录新增
                        TYjyDept searchdept=new TYjyDept();
                        List<TYjyDept> deptList=tYjyDeptService.selectTYjyDeptList(searchdept);
                        Map<String,String> deptListmap=new HashMap<>();
                        for(TYjyDept deptitem:deptList)
                        {
                            deptListmap.put(deptitem.getPath(),deptitem.getDeptName());
                        }
                        tYjyAnswerService.staticAnswer(search,deptListmap);

                        break;
                    }
                    else
                    {
                        check.setStatus("1");
                        userinfo =tYjyFormService.userinfo(checklist.get(i+1).toString());
                        check.setCheckWorkNo(userinfo.get("workNo").toString());
                        check.setCheckUserName(userinfo.get("userName").toString());
                        check.setCheckHistory(search.getCheckHistory()+formattedDate+" "+search.getCheckUserName()+"  通过了审批，审批意见为："+tYjyAnswer.getAssessment()+";\n");
                        break;
                    }
                }
            }
        }
        return toAjax(tYjyAnswerService.updateTYjyAnswer(check));
    }


    @Log(title = "TYjyAnswer", businessType = BusinessType.INSERT)
    @PostMapping("/checkanswers")
    public AjaxResult checkanswers(@RequestBody List<Long> ids)
    {
        for(Long id:ids)
        {
            try{
            TYjyAnswer check=new  TYjyAnswer();
            TYjyAnswer search=tYjyAnswerService.selectTYjyAnswerById(id);
            JSONArray checklist=tYjyFormService.getchecklist(search.getCheckerList(),search.getCreatorDept());
            check.setId(id);
            for(int i=0;i<checklist.size();i++)
            {
                JSONObject userinfo =tYjyFormService.userinfo(checklist.get(i).toString());

                if(search.getCheckHistory()==null)
                {
                    search.setCheckHistory("");
                }

                if(search.getCheckWorkNo().equals(userinfo.get("workNo").toString()))
                {
                    if(i==checklist.size()-1)
                    {
                        check.setStatus("2");
                        check.setCheckWorkNo("");
                        check.setCheckUserName("");
                        check.setCheckHistory(search.getCheckHistory()+" "+search.getCheckUserName()+" "+userinfo.get("workNo").toString()+" 审核通过");
                        TYjyDept searchdept=new TYjyDept();
                        List<TYjyDept> deptList=tYjyDeptService.selectTYjyDeptList(searchdept);
                        Map<String,String> deptListmap=new HashMap<>();
                        for(TYjyDept deptitem:deptList)
                        {
                            deptListmap.put(deptitem.getPath(),deptitem.getDeptName());
                        }
                        tYjyAnswerService.staticAnswer(search,deptListmap);
                        break;
                    }
                    else
                    {
                        check.setStatus("1");
                        userinfo =tYjyFormService.userinfo(checklist.get(i+1).toString());
                        check.setCheckWorkNo(userinfo.get("workNo").toString());
                        check.setCheckUserName(userinfo.get("userName").toString());
                        check.setCheckHistory(search.getCheckHistory()+" "+search.getCheckUserName()+" "+userinfo.get("workNo").toString()+" 审核通过");
                        break;
                    }
                }
            }
            tYjyAnswerService.updateTYjyAnswer(check);
            }catch (Exception e){
                return success("批量审核失败");
            }
        }
        return success("批量审核成功");

    }
    /**
     * 修改TYjyAnswer,感觉没有啥必要
     */
    @Log(title = "TYjyAnswer", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody TYjyAnswer tYjyAnswer)
    {
        //需要确定修改的格式问题
        tYjyAnswerHisService.insertTYjyAnswerHisByAnswerId(tYjyAnswer.getId());
        toAjax(tYjyAnswerService.deleteTYjyAnswerById(tYjyAnswer.getId()));
        tYjyAnswer.setVersion(Long.valueOf(tYjyAnswer.getVersion()+1));
        return toAjax(tYjyAnswerService.insertTYjyAnswer(tYjyAnswer));
    }

    /**
     * 删除TYjyAnswer,感觉没有啥必要
     */
    @Log(title = "TYjyAnswer", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete/id")
    public AjaxResult delete(@PathVariable Long id)
    {
        tYjyAnswerHisService.insertTYjyAnswerHisByAnswerId(id);
        return toAjax(tYjyAnswerService.deleteTYjyAnswerById(id));
    }

    /**
     * 删除TYjyAnswer
     */
    @Log(title = "TYjyAnswer", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteAll/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tYjyAnswerService.deleteTYjyAnswerByIds(ids));
    }



//    @GetMapping("/answerlistplus")
//    public AjaxResult answerlistplus(TYjyAnswer tYjyAnswer) {
//        tYjyAnswer.setWorkNo(SecurityUtils.getUsername());
////        String time=tYjyAnswer.getFcDate();
////        tYjyAnswer.setFcDate(time.substring(0,4)+"-"+time.substring(4,6)+"-"+time.substring(6,8));//不一定用的上,时间强行进行转化，来实现对不同类型的查询
//
//        startPage();
//        List<TYjyAnswer> list = tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
//        List<JSONObject> result= new ArrayList<>();
//        Map<String,List<TYjyForm>> answermap= new HashMap<>();
//        return AjaxResult.success(result);
//    }


    public TYjyAnswerHis changeType(TYjyAnswer tYjyAnswer)
    {
        TYjyAnswerHis re=new TYjyAnswerHis();
        re.setCreatorDept(tYjyAnswer.getCreatorDept());
        re.setCreatorName(tYjyAnswer.getCreatorName());
        re.setCreatorNo(tYjyAnswer.getCreatorNo());
        re.setFormId(tYjyAnswer.getFormId());
        re.setFormValue(tYjyAnswer.getFormValue());
        re.setVersion(tYjyAnswer.getVersion());
        re.setCreateTime(tYjyAnswer.getCreateTime());
        re.setFcDate(tYjyAnswer.getFcDate());
        return re;
    }



    @GetMapping("/answerstatuslist")
    public AjaxResult answerstatuslist(Long dimensionalityId,String fcDate,String formQuestion) {

//        TYjyForm tYjyForm =new TYjyForm();
//        tYjyForm.setDimensionalityId(dimensionalityId);
//        tYjyForm.setFormQuestion(formQuestion);
//        tYjyForm.setWorkNo(SecurityUtils.getUsername());
//        Calendar calendar = Calendar.getInstance();
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        Date currentDate = new Date();
//        if(fcDate!=null) {
//            try {
//                currentDate = dateFormat.parse(fcDate);
//                calendar.setTime(currentDate);
//            } catch (ParseException e) {
//                // 异常处理代码
//                e.printStackTrace();
//                // 或者其他错误处理逻辑
//            }
//        }
//        else
//        {
//            calendar.setTime(currentDate);
//        }
//        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
//        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
//        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
//
//
//        List<TYjyForm> list = tYjyFormService.selectTYjyFormListForAnswer(tYjyForm);
//
//        List<JSONObject> result= new ArrayList<>();
//        List<TYjyAnswer> deallist;
//        String nowtime=currentYear+"-"+(currentMonth+1)+"-"+(currentDay);
//        JSONObject json = new JSONObject();
//        List<JSONObject> array=new ArrayList<>();
//        for(TYjyForm form:list)
//        {
//            if(form.getFrequency().equals("0"))//
//            {
//                nowtime=currentYear+"";
//                if(currentMonth<9)
//                {
//                    nowtime=nowtime+"-0"+(currentMonth+1);
//                }
//                else
//                {
//                    nowtime=nowtime+"-"+(currentMonth+1);
//                }
//                if(currentDay<10)
//                {
//                    nowtime=nowtime+"-0"+currentDay;
//                }
//                else
//                {
//                    nowtime=nowtime+"-"+currentDay;
//                }
//            }else if(form.getFrequency().equals("1"))
//            {
//                if(currentMonth<9)
//                {
//                    nowtime=currentYear+"-0"+(currentMonth+1)+"-01";
//                }
//                else
//                {
//                    nowtime=currentYear+"-"+(currentMonth+1)+"-01";
//                }
//            }else if(form.getFrequency().equals("2"))
//            {
//                if(currentMonth<=2)
//                {
//                    nowtime=currentYear+"-01-01";
//                }else if(currentMonth<=5)
//                {
//                    nowtime=currentYear+"-04-01";
//                }else if(currentMonth<=8)
//                {
//                    nowtime=currentYear+"-07-01";
//                }else if(currentMonth<=11)
//                {
//                    nowtime=currentYear+"-10-01";
//                }
//
//            }else if(form.getFrequency().equals("3"))
//            {
//                if(currentMonth<=5)
//                {
//                    nowtime=currentYear+"-01-01";
//                }
//                else
//                {
//                    nowtime=currentYear+"-07-01";
//                }
//            }else if(form.getFrequency().equals("4"))
//            {
//                nowtime=currentYear+"-01-01";
//            }else if(form.getFrequency().equals("5"))
//            {
//
////                Calendar weekcalendar = Calendar.getInstance();
////                int test=calendar.get(Calendar.DAY_OF_WEEK);
////                if(calendar.get(Calendar.DAY_OF_WEEK)<=1)
////                {
////                    weekcalendar.setWeekDate(calendar.getWeekYear(), calendar.get(Calendar.WEEK_OF_MONTH)-1, 2);
////                }
////                else
////                {
////                    weekcalendar.setWeekDate(calendar.getWeekYear(), calendar.get(Calendar.WEEK_OF_MONTH), 2);
////                }
////                nowtime=dateFormat.format(weekcalendar.getTime());
//
//
//                calendar.add(Calendar.DAY_OF_YEAR,-1);
//                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
//                nowtime=dateFormat.format(calendar.getTime());
//            }
//
//            JSONObject jsonResult=new JSONObject();
//            jsonResult.put("formId",form.getId());
//            jsonResult.put("dimensionalityPath",form.getDimensionalityPath());
//            jsonResult.put("formQuestion",form.getFormQuestion());
//            jsonResult.put("frequency",form.getFrequency());
//            jsonResult.put("formType",form.getFormType());
//            jsonResult.put("minimum",form.getMinimum());
//            jsonResult.put("maximum",form.getMaximum());
//            jsonResult.put("creatorDept",form.getDeptCode());
//            jsonResult.put("dimensionalityName",tYjyFormService.searchNames(form.getDimensionalityId()));
//            jsonResult.put("formNote",form.getFormNote());
//            jsonResult.put("formNote1",form.getFormNote1());
//            jsonResult.put("formUnit",form.getUnit());
//
//            int count=0;
//            int num=0;
//            TYjyAnswer tYjyAnswer=new TYjyAnswer();
//            tYjyAnswer.setFcDate(nowtime);
//            //填报部门的石山
////            tYjyAnswer.setCreatorDept(form.getDeptCode());
//            tYjyAnswer.setFormId(form.getId());
//            deallist =tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
//            if(deallist.size()>0)
//            {
//                jsonResult.put("formValue",deallist.get(0).getFormValue());
//                jsonResult.put("creatorNo",deallist.get(0).getCreatorNo());
//                jsonResult.put("creatorName",deallist.get(0).getCreatorName());
//                jsonResult.put("reason",deallist.get(0).getReason());
//                jsonResult.put("measure",deallist.get(0).getMeasure());
//                jsonResult.put("status",deallist.get(0).getStatus());
//                jsonResult.put("assessment",deallist.get(0).getAssessment());
//                jsonResult.put("checkWorkNo",deallist.get(0).getCheckWorkNo());
//                jsonResult.put("checkUserName",deallist.get(0).getCheckUserName());
//            }
//            else
//            {
//                jsonResult.put("formValue",null);
//                jsonResult.put("creatorNo",null);
//                jsonResult.put("creatorName",null);
//                jsonResult.put("reason",null);
//                jsonResult.put("measure",null);
//                jsonResult.put("status","4");
//            }
//
//            array.add(jsonResult);
//        }
//        return AjaxResult.success(array);

        TYjyDimensionality tYjyDimensionality=tYjyDimensionalityService.selectTYjyDimensionalityById(dimensionalityId);
        List<TYjyForm> list= tYjyDimensionalityService.getformstatusSubmit(tYjyDimensionality.getPath(),fcDate, formQuestion,SecurityUtils.getUsername());
        List<JSONObject> array=new ArrayList<>();
        Map<String,Map<String,String>> dimensionalityMap=new HashMap<>();
        for(TYjyForm form:list)
        {
            if(form.getHiddenFlag().equals("1"))
            {
                continue;
            }
            JSONObject jsonResult=new JSONObject();
            jsonResult.put("formId",form.getId());
            jsonResult.put("dimensionalityPath",form.getDimensionalityPath());
            jsonResult.put("formQuestion",form.getFormQuestion());
            jsonResult.put("frequency",form.getFrequency());
            jsonResult.put("formType",form.getFormType());
            jsonResult.put("minimum",form.getMinimum());
            jsonResult.put("maximum",form.getMaximum());
            jsonResult.put("creatorDept",form.getDeptCode());
//            jsonResult.put("dimensionalityName",tYjyFormService.searchNames(form.getDimensionalityId()));
            jsonResult.put("dimensionalityName",tYjyFormService.searchNamesNoneHand(form.getDimensionalityPath(),dimensionalityMap));
            jsonResult.put("formNote",form.getFormNote());
            jsonResult.put("formNote1",form.getFormNote1());
            jsonResult.put("formUnit",form.getUnit());
            jsonResult.put("formValue",form.getFormValue());
            jsonResult.put("creatorNo",form.getCreatorNo());
            jsonResult.put("creatorName",form.getCreatorName());
            jsonResult.put("reason",form.getReason());
            jsonResult.put("measure",form.getMeasure());
            jsonResult.put("status",form.getStatus());
            jsonResult.put("assessment",form.getAssessment());
            jsonResult.put("checkWorkNo",form.getCheckWorkNo());
            jsonResult.put("checkUserName",form.getCheckUserName());
            jsonResult.put("ruleType",form.getRuleType());
            jsonResult.put("hiddenFlag",form.getHiddenFlag());
            array.add(jsonResult);
        }
        return AjaxResult.success(array);

    }

    //扎口部门查看填报情况
    @GetMapping("/answerstatuslistAdmin")
    public AjaxResult answerstatuslistAdmin(Long dimensionalityId,String fcDate,String formQuestion) {

        TYjyDimensionality tYjyDimensionality=tYjyDimensionalityService.selectTYjyDimensionalityById(dimensionalityId);
        List<TYjyForm> list= tYjyDimensionalityService.getformstatus(tYjyDimensionality.getPath(),fcDate, formQuestion);
        List<JSONObject> array=new ArrayList<>();
        Map<String,Map<String,String>> dimensionalityMap=new HashMap<>();
        for(TYjyForm form:list)
        {
            JSONObject jsonResult=new JSONObject();
            jsonResult.put("formId",form.getId());
            jsonResult.put("dimensionalityPath",form.getDimensionalityPath());
            jsonResult.put("formQuestion",form.getFormQuestion());
            jsonResult.put("frequency",form.getFrequency());
            jsonResult.put("formType",form.getFormType());
            jsonResult.put("minimum",form.getMinimum());
            jsonResult.put("maximum",form.getMaximum());
            jsonResult.put("creatorDept",form.getDeptCode());
//            jsonResult.put("dimensionalityName",tYjyFormService.searchNames(form.getDimensionalityId()));
            jsonResult.put("dimensionalityName",tYjyFormService.searchNames(form.getDimensionalityPath(),dimensionalityMap));
            jsonResult.put("formNote",form.getFormNote());
            jsonResult.put("formNote1",form.getFormNote1());
            jsonResult.put("formUnit",form.getUnit());
            jsonResult.put("formValue",form.getFormValue());
            jsonResult.put("creatorNo",form.getCreatorNo());
            jsonResult.put("creatorName",form.getCreatorName());
            jsonResult.put("reason",form.getReason());
            jsonResult.put("measure",form.getMeasure());
            jsonResult.put("status",form.getStatus());
            jsonResult.put("assessment",form.getAssessment());
            array.add(jsonResult);
        }
        return AjaxResult.success(array);
//        TYjyDimensionality tYjyDimensionality=tYjyDimensionalityService.selectTYjyDimensionalityById(dimensionalityId);
//        TYjyForm tYjyForm =new TYjyForm();
//        tYjyForm.setDimensionalityPath(tYjyDimensionality.getPath());
//        tYjyForm.setFormQuestion(formQuestion);
//        tYjyForm.setWorkNo(SecurityUtils.getUsername());
//        Calendar calendar = Calendar.getInstance();
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        Date currentDate = new Date();
//        if(fcDate!=null) {
//            try {
//                currentDate = dateFormat.parse(fcDate);
//                calendar.setTime(currentDate);
//            } catch (ParseException e) {
//                // 异常处理代码
//                e.printStackTrace();
//                // 或者其他错误处理逻辑
//            }
//        }
//        else
//        {
//            calendar.setTime(currentDate);
//        }
//        int currentYear = calendar.get(Calendar.YEAR); // 获取当前年份
//        int currentMonth = calendar.get(Calendar.MONTH); // 获取当前月份
//        int currentDay = calendar.get(Calendar.DAY_OF_MONTH); // 获取当前日期
//        List<TYjyForm> list = tYjyFormService.selectTYjyFormList(tYjyForm);
//        List<JSONObject> result= new ArrayList<>();
//        List<TYjyAnswer> deallist;
//        String nowtime=currentYear+"-"+(currentMonth+1)+"-"+(currentDay);
//        JSONObject json = new JSONObject();
//        List<JSONObject> array=new ArrayList<>();
//        for(TYjyForm form:list)
//        {
//            if(form.getFrequency().equals("0"))//
//            {
//                nowtime=currentYear+"";
//                if(currentMonth<9)
//                {
//                    nowtime=nowtime+"-0"+(currentMonth+1);
//                }
//                else
//                {
//                    nowtime=nowtime+"-"+(currentMonth+1);
//                }
//                if(currentDay<10)
//                {
//                    nowtime=nowtime+"-0"+currentDay;
//                }
//                else
//                {
//                    nowtime=nowtime+"-"+currentDay;
//                }
//            }else if(form.getFrequency().equals("1"))
//            {
//                if(currentMonth<9)
//                {
//                    nowtime=currentYear+"-0"+(currentMonth+1)+"-01";
//                }
//                else
//                {
//                    nowtime=currentYear+"-"+(currentMonth+1)+"-01";
//                }
//            }else if(form.getFrequency().equals("2"))
//            {
//                if(currentMonth<=2)
//                {
//                    nowtime=currentYear+"-01-01";
//                }else if(currentMonth<=5)
//                {
//                    nowtime=currentYear+"-04-01";
//                }else if(currentMonth<=8)
//                {
//                    nowtime=currentYear+"-07-01";
//                }else if(currentMonth<=11)
//                {
//                    nowtime=currentYear+"-10-01";
//                }
//
//            }else if(form.getFrequency().equals("3"))
//            {
//                if(currentMonth<=5)
//                {
//                    nowtime=currentYear+"-01-01";
//                }
//                else
//                {
//                    nowtime=currentYear+"-07-01";
//                }
//            }else if(form.getFrequency().equals("4"))
//            {
//                nowtime=currentYear+"-01-01";
//            }else if(form.getFrequency().equals("5"))
//            {
//
////                Calendar weekcalendar = Calendar.getInstance();
////                int test=calendar.get(Calendar.DAY_OF_WEEK);
////                if(calendar.get(Calendar.DAY_OF_WEEK)<=1)
////                {
////                    weekcalendar.setWeekDate(calendar.getWeekYear(), calendar.get(Calendar.WEEK_OF_MONTH)-1, 2);
////                }
////                else
////                {
////                    weekcalendar.setWeekDate(calendar.getWeekYear(), calendar.get(Calendar.WEEK_OF_MONTH), 2);
////                }
////                nowtime=dateFormat.format(weekcalendar.getTime());
//
//
//                calendar.add(Calendar.DAY_OF_YEAR,-1);
//                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
//                nowtime=dateFormat.format(calendar.getTime());
//            }
//
//            JSONObject jsonResult=new JSONObject();
//            jsonResult.put("formId",form.getId());
//            jsonResult.put("dimensionalityPath",form.getDimensionalityPath());
//            jsonResult.put("formQuestion",form.getFormQuestion());
//            jsonResult.put("frequency",form.getFrequency());
//            jsonResult.put("formType",form.getFormType());
//            jsonResult.put("minimum",form.getMinimum());
//            jsonResult.put("maximum",form.getMaximum());
//            jsonResult.put("creatorDept",form.getDeptCode());
//            jsonResult.put("dimensionalityName",tYjyFormService.searchNames(form.getDimensionalityId()));
//            jsonResult.put("formNote",form.getFormNote());
//            jsonResult.put("formNote1",form.getFormNote1());
//            jsonResult.put("formUnit",form.getUnit());
//            int count=0;
//            int num=0;
//            TYjyAnswer tYjyAnswer=new TYjyAnswer();
//            tYjyAnswer.setFcDate(nowtime);
//            //填报部门的石山
////            tYjyAnswer.setCreatorDept(form.getDeptCode());
//            tYjyAnswer.setFormId(form.getId());
//            deallist =tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
//            if(deallist.size()>0)
//            {
//                jsonResult.put("formValue",deallist.get(0).getFormValue());
//                jsonResult.put("creatorNo",deallist.get(0).getCreatorNo());
//                jsonResult.put("creatorName",deallist.get(0).getCreatorName());
//                jsonResult.put("reason",deallist.get(0).getReason());
//                jsonResult.put("measure",deallist.get(0).getMeasure());
//                jsonResult.put("status",deallist.get(0).getStatus());
//            }
//            else
//            {
//                jsonResult.put("formValue",null);
//                jsonResult.put("creatorNo",null);
//                jsonResult.put("creatorName",null);
//                jsonResult.put("reason",null);
//                jsonResult.put("measure",null);
//                jsonResult.put("status","4");
//            }
//
//            array.add(jsonResult);
//        }
////        json.put("data",array);
////        result.add(json);
////        JSONObject re=new JSONObject();
////        re.put("data",array);
//        return AjaxResult.success(array);
    }

    @GetMapping("/answerliststatic")
    public AjaxResult answerliststatic(TYjyAnswer tYjyAnswer) {
        String workNo=SecurityUtils.getUsername();
        tYjyAnswer.setWorkNo(workNo);
        startPage();
        List<TYjyAnswer> list = tYjyAnswerService.selectTYjyAnswerList(tYjyAnswer);
        List<JSONObject> result= new ArrayList<>();
        for(TYjyAnswer item:list)
        {
            JSONObject jsonResult=new JSONObject();
            JSONObject hisData=new JSONObject();
            JSONObject hisDatain=new JSONObject();
            jsonResult.put("formId",item.getFormId());
            jsonResult.put("creatorDept",item.getCreatorDept());
            TYjyForm form=tYjyFormService.selectTYjyFormById(item.getFormId());
            jsonResult.put("formQuestion",form.getFormQuestion());
            jsonResult.put("formType",form.getFormType());
            jsonResult.put("minimum",form.getMinimum());
            jsonResult.put("maximum",form.getMaximum());
//            jsonResult.put("formValue",item.getFormValue());
            if(item.getFormValue()==null)
            {
                hisDatain.put("formValue",null);
//                if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                {
//                    hisDatain.put("formValue",new ArrayList());
//                }
//                else
//                {
//                    hisDatain.put("formValue",null);
//                }
            }
            else
            {
                if(form.getFormType().equals("0"))
                {
                    hisDatain.put("formValue",Integer.valueOf(item.getFormValue()));
                }
                if(form.getFormType().equals("1"))
                {
                    hisDatain.put("formValue",Double.valueOf(item.getFormValue()));
                }
                if(form.getFormType().equals("2")||form.getFormType().equals("3"))
                {
                    hisDatain.put("formValue",item.getFormValue());
                }
                if(form.getFormType().equals("4") || form.getFormType().equals("5"))
                {
                    hisDatain.put("formValue",JSONObject.parse(item.getFormValue()));
                }
            }
            item.setWorkNo(workNo);
            List<TYjyAnswer> answerlist =tYjyAnswerService.selectAnswerLast3(item);
            int count=0;
            while(count<answerlist.size())
            {
                hisDatain=new JSONObject();
                hisDatain.put("title",answerlist.get(count).getFcDate()+"("+answerlist.get(count).getCreatorName()+")");
                if(answerlist.get(count).getFormValue()==null)
                {
                    if(form.getFormType().equals("4") || form.getFormType().equals("5"))
                    {
                        hisDatain.put("value",new ArrayList<>());
                    }
                    else
                    {
                        hisDatain.put("value",null);
                    }
                }
                else
                {
                    if(form.getFormType().equals("0"))
                    {
                        hisDatain.put("value",Integer.valueOf(answerlist.get(count).getFormValue()));
                    }
                    if(form.getFormType().equals("1"))
                    {
                        hisDatain.put("value",Double.valueOf(answerlist.get(count).getFormValue()));
                    }
                    if(form.getFormType().equals("2")||form.getFormType().equals("3"))
                    {
                        hisDatain.put("value",answerlist.get(count).getFormValue());
                    }
//                    if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                    {
//                        hisDatain.put("value",JSONObject.parse(answerlist.get(count).getFormValue()));
//                    }
                }

                hisData.put("data"+(count+1),hisDatain);
                jsonResult.put("hisData",hisData);
                count=count+1;
            }
            while(count<3)
            {
                hisDatain=new JSONObject();
                hisDatain.put("title",null);
                hisDatain.put("formValue",null);
//                if(form.getFormType().equals("4") || form.getFormType().equals("5"))
//                {
//                    hisDatain.put("formValue",new ArrayList());
//                }
//                else
//                {
//                    hisDatain.put("formValue",null);
//                }
                hisData.put("data"+(count+1),hisDatain);
                count=count+1;
                jsonResult.put("hisData",hisData);
            }
            result.add(jsonResult);
        }
        return AjaxResult.success(result);
    }


    @PostMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response, String startDate, String endDate, Long rootId) throws IOException {
        tYjyDimensionalityService.exportTemplate(response, startDate,endDate,rootId);
    }




    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception
    {
        if (file.isEmpty()) {
            return error("文件不能为空");
        }
        try {
            Map<String, List<JSONObject>> data = tYjyDimensionalityService.readExcelBySheetNames(file, dataReportFrequency.getSheets());
            for (String key:data.keySet()){
                if(Objects.nonNull(data.get(key))&&data.get(key).size()>0)tYjyAnswerService.dealnewadd(data.get(key));
            }
            return success("导入成功");
        } catch (Exception e) {
            return error("解析失败：" + e.getMessage());
        }
    }


    @PostMapping("/exportWithTemplate")
    public void exportWithTemplate(HttpServletResponse response, String fcDate, Long rootId,String type,String isUpdate) throws IOException {
        tYjyDimensionalityService.exportTemplate( response,  fcDate,  rootId,type, isUpdate);
    }

    @PostMapping("/exportWithTemplate1")
    public void exportWithTemplate1(HttpServletResponse response, String startDate, String endDate, Long rootId,String type,String isUpdate) throws IOException {
        tYjyDimensionalityService.exportTemplate1( response,  startDate, endDate, rootId,type,isUpdate);
    }


    @PostMapping("/exportTemplateSpecial")
    public void exportTemplateSpecial(HttpServletResponse response, String fcDate, Long rootId,String type) throws IOException {
        tYjyDimensionalityService.exportOnce( response,  fcDate,  rootId,type);
    }

    @PostMapping("/importDataSpecial")
    public AjaxResult importDataSpecial(MultipartFile file) throws Exception
    {
        if (file.isEmpty()) {
            return error("文件不能为空");
        }
        try {

            tYjyDimensionalityService.readExcelBySheetSpecial(file);
//            for (String key:data.keySet()){
//                if(Objects.nonNull(data.get(key))&&data.get(key).size()>0)tYjyAnswerService.dealnewadd(data.get(key));
//            }
            return success("导入成功");
        } catch (Exception e) {
            return error("解析失败：" + e.getMessage());
        }
    }

    //给公司累计数据的
    @PostMapping("/exportTemplateNomral")
    public void exportTemplateNomral(HttpServletResponse response, String startDate, String endDate, Long rootId,String type) throws IOException {
        tYjyDimensionalityService.exportTwice( response, startDate,endDate, rootId,type);
    }
    //给单月所有的都在一行的
    //实现所有单元的填报
    @PostMapping("/exportEverymouth")
    public void exportEverymouth(HttpServletResponse response, String startDate, String endDate, Long rootId,String type) throws IOException {
        tYjyDimensionalityService.exportThird( response, startDate,endDate, rootId,type);
    }
    //还没有开发完的
    @PostMapping("/importDataNomral")//有空的时候再说
    public AjaxResult importDataNomral(MultipartFile file) throws Exception
    {
        if (file.isEmpty()) {
            return error("文件不能为空");
        }
        try {

            tYjyDimensionalityService.readExcelBySheetSpecial(file);
//            for (String key:data.keySet()){
//                if(Objects.nonNull(data.get(key))&&data.get(key).size()>0)tYjyAnswerService.dealnewadd(data.get(key));
//            }
            return success("导入成功");
        } catch (Exception e) {
            return error("解析失败：" + e.getMessage());
        }
    }


//    测试接口，用于测试各个方法
    @GetMapping("/test")
    public AjaxResult test(String beginTime,String endTime,String frequency)
    {
        return AjaxResult.success(tYjyAnswerService.calculateDifference(beginTime,endTime,frequency));
    }

    @GetMapping("/gustest")
    public int gustest(String fcDate,String orign)
    {
//        tYjyAnswerService.updateGas();
//        tYjyAnswerService.newupdateGas();
//        tYjyAnswerService.qtjsdatedeal();

        tYjyAnswerService.localgasupdatePlus(fcDate,orign);
//        tYjyAnswerService.scrapyUpadte();
        return 1;
    }


    @GetMapping("/importDateUpdate")
    public AjaxResult importDateget(String fcDate)
    {

        return AjaxResult.success(tYjyAnswerService.importDateUpdate());
    }
}
