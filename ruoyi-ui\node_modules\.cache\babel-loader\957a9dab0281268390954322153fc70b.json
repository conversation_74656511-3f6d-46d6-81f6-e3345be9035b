{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\purchaseDashboardMain\\index.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\purchaseDashboardMain\\index.js", "mtime": 1756456493789}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getDashboardData", "params", "request", "url", "method", "getPersonalConsumption", "getPurchaseAnalysis", "getProductAnalysis", "getMapData", "getRealTimeStats", "getSalesFunnel", "getPurchaseTrend", "getSupplierAnalysis", "getInventoryAnalysis", "getAmtManage"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/api/purchaseDashboardMain/index.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 获取采购销售看板数据\r\nexport function getDashboardData(params) {\r\n  return request({\r\n    url: '/purchase/dashboard/main/data',\r\n    method: 'get',\r\n    params: params\r\n  })\r\n}\r\n\r\n// 获取个人消费情况数据\r\nexport function getPersonalConsumption(params) {\r\n  return request({\r\n    url: '/purchase/dashboard/main/personalConsumption',\r\n    method: 'get',\r\n    params: params\r\n  })\r\n}\r\n\r\n// 获取采购分析数据\r\nexport function getPurchaseAnalysis(params) {\r\n  return request({\r\n    url: '/purchase/dashboard/main/purchaseAnalysis',\r\n    method: 'get',\r\n    params: params\r\n  })\r\n}\r\n\r\n// 获取产品分析数据\r\nexport function getProductAnalysis(params) {\r\n  return request({\r\n    url: '/purchase/dashboard/main/productAnalysis',\r\n    method: 'get',\r\n    params: params\r\n  })\r\n}\r\n\r\n// 获取地图数据\r\nexport function getMapData(params) {\r\n  return request({\r\n    url: '/purchase/dashboard/main/mapData',\r\n    method: 'get',\r\n    params: params\r\n  })\r\n}\r\n\r\n// 获取实时数据统计\r\nexport function getRealTimeStats(params) {\r\n  return request({\r\n    url: '/purchase/dashboard/main/realTimeStats',\r\n    method: 'get',\r\n    params: params\r\n  })\r\n}\r\n\r\n// 获取销售漏斗数据\r\nexport function getSalesFunnel(params) {\r\n  return request({\r\n    url: '/purchase/dashboard/main/salesFunnel',\r\n    method: 'get',\r\n    params: params\r\n  })\r\n}\r\n\r\n// 获取采购趋势数据\r\nexport function getPurchaseTrend(params) {\r\n  return request({\r\n    url: '/purchase/dashboard/main/purchaseTrend',\r\n    method: 'get',\r\n    params: params\r\n  })\r\n}\r\n\r\n// 获取供应商分析数据\r\nexport function getSupplierAnalysis(params) {\r\n  return request({\r\n    url: '/purchase/dashboard/main/supplierAnalysis',\r\n    method: 'get',\r\n    params: params\r\n  })\r\n}\r\n\r\n// 获取库存分析数据\r\nexport function getInventoryAnalysis(params) {\r\n  return request({\r\n    url: '/purchase/dashboard/main/inventoryAnalysis',\r\n    method: 'get',\r\n    params: params\r\n  })\r\n}\r\n\r\n// 获取资金管理数据\r\nexport function getAmtManage() {\r\n  return request({\r\n    url: '/procurement/dashboard/showAmtManage',\r\n    method: 'get'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,sBAAsBA,CAACJ,MAAM,EAAE;EAC7C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,mBAAmBA,CAACL,MAAM,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,kBAAkBA,CAACN,MAAM,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0CAA0C;IAC/CC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,UAAUA,CAACP,MAAM,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,gBAAgBA,CAACR,MAAM,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,cAAcA,CAACT,MAAM,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,gBAAgBA,CAACV,MAAM,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,mBAAmBA,CAACX,MAAM,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,oBAAoBA,CAACZ,MAAM,EAAE;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}