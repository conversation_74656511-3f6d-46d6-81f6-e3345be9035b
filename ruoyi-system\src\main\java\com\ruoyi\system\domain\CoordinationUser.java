package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
public class CoordinationUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */

    private Long userId;

    /**
     * 部门ID
     */

    private Long deptId;

    /**
     * 用户账号
     */
    @Excel(name = "登录名称")
    private String userName;

    /**
     * 用户昵称
     */
    @Excel(name = "用户名称")
    private String nickName;

    /**
     * 用户邮箱
     */

    private String email;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String phonenumber;

    /**
     * 用户性别
     */

    private String sex;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 身份证号码
     */
    @Excel(name = "身份证号码")
    private String idCard;

    @Excel(name = "所属单位")
    private String coordinationName;

    /**
     * 密码
     */

    private String password;

    /**
     * 盐加密
     */
    private String salt;

    /**
     * 帐号状态（0正常 1停用）
     */

    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 最后登录IP
     */

    private String loginIp;

    /**
     * 最后登录时间
     */

    private Date loginDate;

    /**
     * 部门对象
     */

    private SysDept dept;

    /**
     * 角色对象
     */
    private List<SysRole> roles;

    /**
     * 角色组
     */
    private Long[] roleIds;

    /**
     * 岗位组
     */
    private Long[] postIds;

    /**
     * 扩展部门组
     */
    private Long[] deptExtends;

    /**
     * 角色ID
     */
    private Long roleId;

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }


    /**
     * 扩展部门组
     */
    private String[] dailyDeptNames;

    public CoordinationUser() {

    }

    public CoordinationUser(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public boolean isAdmin() {
        return this.isSuperAdmin(this.userId) || this.isAdmin(this.roles);
    }

    private static boolean isAdmin(List<SysRole> roles) {
        if (roles == null) {
            return false;
        }
        else {
            for (SysRole role : roles) {
                if (role.isAdmin()) {
                    return true;
                }
            }
            return false;
        }
    }

    public boolean isSuperAdmin() {
        return isSuperAdmin(this.userId);
    }

    public static boolean isSuperAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public String getCoordinationName() {
        return coordinationName;
    }

    public void setCoordinationName(String coordinationName) {
        this.coordinationName = coordinationName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String[] getDailyDeptNames() {
        return dailyDeptNames;
    }

    public void setDailyDeptNames(String[] dailyDeptNames) {
        this.dailyDeptNames = dailyDeptNames;
    }

    @JsonIgnore
    @JsonProperty
    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public Date getLoginDate() {
        return loginDate;
    }

    public void setLoginDate(Date loginDate) {
        this.loginDate = loginDate;
    }

    public SysDept getDept() {
        return dept;
    }

    public void setDept(SysDept dept) {
        this.dept = dept;
    }

    public List<SysRole> getRoles() {
        return roles;
    }

    public void setRoles(List<SysRole> roles) {
        this.roles = roles;
    }

    public Long[] getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(Long[] roleIds) {
        this.roleIds = roleIds;
    }

    public Long[] getPostIds() {
        return postIds;
    }

    public void setPostIds(Long[] postIds) {
        this.postIds = postIds;
    }

    public Long[] getDeptExtends() {
        return deptExtends;
    }

    public void setDeptExtends(Long[] deptExtends) {
        this.deptExtends = deptExtends;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("CoordinationUser{");
        sb.append("userId=").append(userId);
        sb.append(", deptId=").append(deptId);
        sb.append(", userName='").append(userName).append('\'');
        sb.append(", nickName='").append(nickName).append('\'');
        sb.append(", email='").append(email).append('\'');
        sb.append(", phonenumber='").append(phonenumber).append('\'');
        sb.append(", sex='").append(sex).append('\'');
        sb.append(", avatar='").append(avatar).append('\'');
        sb.append(", idCard='").append(idCard).append('\'');
        sb.append(", coordinationName='").append(coordinationName).append('\'');
        sb.append(", password='").append(password).append('\'');
        sb.append(", salt='").append(salt).append('\'');
        sb.append(", status='").append(status).append('\'');
        sb.append(", delFlag='").append(delFlag).append('\'');
        sb.append(", loginIp='").append(loginIp).append('\'');
        sb.append(", loginDate=").append(loginDate);
        sb.append(", dept=").append(dept);
        sb.append(", roles=").append(roles);
        sb.append(", roleIds=").append(roleIds == null ? "null" : Arrays.asList(roleIds).toString());
        sb.append(", postIds=").append(postIds == null ? "null" : Arrays.asList(postIds).toString());
        sb.append(", deptExtends=").append(deptExtends == null ? "null" : Arrays.asList(deptExtends).toString());
        sb.append(", roleId=").append(roleId);
        sb.append(", dailyDeptNames=").append(dailyDeptNames == null ? "null" : Arrays.asList(dailyDeptNames).toString());
        sb.append('}');
        return sb.toString();
    }
}
