package com.ruoyi.xctgDevice.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 角色信息对象 check_role
 * 
 * <AUTHOR>
 * @date 2021-06-07
 */
public class CheckRole extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 角色ID */
    private Long roleId;

    /** 角色名称 */
    @Excel(name = "角色名称")
    private String roleName;

    /** 角色权限字符串 */
    @Excel(name = "角色权限字符串")
    private String roleKey;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Integer roleSort;

    /** 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限） */
    @Excel(name = "数据范围", readConverterExp = "1=：全部数据权限,2=：自定数据权限,3=：本部门数据权限,4=：本部门及以下数据权限")
    private String dataScope;

    /** 菜单树选择项是否关联显示 */
    @Excel(name = "菜单树选择项是否关联显示")
    private Integer menuCheckStrictly;

    /** 部门树选择项是否关联显示 */
    @Excel(name = "部门树选择项是否关联显示")
    private Integer deptCheckStrictly;

    private String cron;

    /** 角色状态（0正常 1停用） */
    @Excel(name = "角色状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    private Long devices[];

    public Long[] getDevices() {
        return devices;
    }

    public void setDevices(Long[] devices) {
        this.devices = devices;
    }

    public void setRoleId(Long roleId)
    {
        this.roleId = roleId;
    }

    public Long getRoleId() 
    {
        return roleId;
    }
    public void setRoleName(String roleName) 
    {
        this.roleName = roleName;
    }

    public String getRoleName() 
    {
        return roleName;
    }
    public void setRoleKey(String roleKey) 
    {
        this.roleKey = roleKey;
    }

    public String getRoleKey() 
    {
        return roleKey;
    }
    public void setRoleSort(Integer roleSort) 
    {
        this.roleSort = roleSort;
    }

    public Integer getRoleSort() 
    {
        return roleSort;
    }
    public void setDataScope(String dataScope) 
    {
        this.dataScope = dataScope;
    }

    public String getDataScope() 
    {
        return dataScope;
    }
    public void setMenuCheckStrictly(Integer menuCheckStrictly) 
    {
        this.menuCheckStrictly = menuCheckStrictly;
    }

    public Integer getMenuCheckStrictly() 
    {
        return menuCheckStrictly;
    }
    public void setDeptCheckStrictly(Integer deptCheckStrictly) 
    {
        this.deptCheckStrictly = deptCheckStrictly;
    }

    public Integer getDeptCheckStrictly() 
    {
        return deptCheckStrictly;
    }


    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public boolean isAdmin()
    {
        return isSuperAdmin(this.roleId) || isAdmin(this.roleKey);
    }

    private static boolean isAdmin(String roleKey) {
        return roleKey != null && roleKey.equals("sysAdmin");
    }

    public boolean isSuperAdmin() {
        return isSuperAdmin(this.roleId);
    }

    public static boolean isSuperAdmin(Long roleId)
    {
        return roleId != null && 1L == roleId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("roleId", getRoleId())
            .append("roleName", getRoleName())
            .append("roleKey", getRoleKey())
            .append("roleSort", getRoleSort())
            .append("dataScope", getDataScope())
            .append("menuCheckStrictly", getMenuCheckStrictly())
            .append("deptCheckStrictly", getDeptCheckStrictly())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
