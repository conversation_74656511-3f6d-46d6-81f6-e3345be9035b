package com.ruoyi.app.purchase.controller;

import com.ruoyi.app.purchase.domain.PurchaseProviderTotalAmount;
import com.ruoyi.app.purchase.domain.PurchaseSuppRisk;
import com.ruoyi.app.purchase.domain.PurchaseTotalAmount;
import com.ruoyi.app.purchase.dto.*;
import com.ruoyi.app.purchase.service.IProcurementDashboardService;
import com.ruoyi.app.purchase.vo.MaterialNameVo;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 采购全景看板
 * @CreateTime: 2025-05-22 10:56
 * @Author: liekkas
 */
@RestController
@RequestMapping("/procurement/dashboard")
public class ProcurementDashboardController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(ProcurementDashboardController.class);

    @Autowired
    private IProcurementDashboardService procurementDashboardService;

    /**
     * 看板整体数据
     */
    @GetMapping("/showData")
    public AjaxResult showData(ProcurementDto query) {
        return AjaxResult.success(procurementDashboardService.showData(query));
    }

    /**
     * 物资类型列表
     */
    @GetMapping("/showItemTypeList")
    public AjaxResult showItemTypeList(ProcurementOptionDto query) {
        return AjaxResult.success(procurementDashboardService.showItemTypeList(query));
    }

    /**
     * 物资列表
     */
    @GetMapping("/showMaterialList")
    public AjaxResult showMaterialList(ProcurementDetailDto query) {
        return AjaxResult.success(procurementDashboardService.showMaterialList(query));
    }

    /**
     * 高频物资
     */
    @PostMapping("/showHighFrequencyMaterialList")
    public AjaxResult showHighFrequencyMaterialList(@RequestBody ProcurementFrequencyDto query) {
        return AjaxResult.success(procurementDashboardService.showHighFrequencyMaterialList(query));
    }



    /**
     * 供应商风险
     * @param query
     * @return
     */
    @PostMapping("/showPurchaseSuppRisk")
    public AjaxResult showPurchaseSuppRisk(@RequestBody PurchaseSuppRisk query) {
        return AjaxResult.success(procurementDashboardService.showPurchaseSuppRisk(query));
    }

    @GetMapping("/showSuppList")
    public AjaxResult showSuppList(ProcurementSuppDto query) {
        return AjaxResult.success(procurementDashboardService.showSuppList(query));
    }

    /**
     * 采购关键指标
     */
    @PostMapping("/showKeyIndicators")
    public AjaxResult showKeyIndicators(@RequestBody ProcurementDto query) {
        return AjaxResult.success(procurementDashboardService.showKeyIndicators(query));
    }

    /**
     * 驾驶舱：资金管理
     */
    @GetMapping("/showAmtManage")
    public AjaxResult showAmtManage() {
        return AjaxResult.success(procurementDashboardService.showAmtManage());
    }

    /**
     * 采购计划看板
     */
    @GetMapping("/plan/showPurchasePlanList")
    public AjaxResult showPurchasePlanList() {
        return AjaxResult.success(procurementDashboardService.showPurchasePlanList());
    }
    /**
     * 中心库金额曲线
     */
    @PostMapping("/showYearlyAmount")
    public AjaxResult showYearlyAmount(@RequestBody ProcurementYearlyQueryDto query) {
        return AjaxResult.success(procurementDashboardService.showYearlyAmount(query));
    }

    /**
     * 实时库存
     */
    @PostMapping("/showRealTimeAmount")
    public AjaxResult showRealTimeAmount() {
        return AjaxResult.success(procurementDashboardService.showRealTimeAmount());
    }

    /**
     * 矿焦煤七天前的实物库存量--中类
     *
     * @return 矿焦煤库存明细列表
     */
    @PostMapping("/showCokingCoalAmount")
    public AjaxResult showCokingCoalAmount() {
        return AjaxResult.success(procurementDashboardService.showCokingCoalAmount());
    }

    /**
     * 获取物资价格和入库数量
     * 支持查询多个物资的价格数据或采购量数据：
     * - 每个物资可以指定独立的曲线类型（价格曲线或采购量曲线）
     * - 物资名称为必填项
     * - 曲线类型：1-价格曲线 2-采购量曲线
     * 
     * @param query 查询参数，包含多个物资的查询条件列表
     * @return 物资价格和入库数量结果
     */
    @PostMapping("/getPurchasePriceAndStore")
    public AjaxResult getPurchasePriceAndStore(@RequestBody ProcurementPriceAndStoreQueryDto query) {
        return AjaxResult.success(procurementDashboardService.getPurchasePriceAndStore(query));
    }

    /**
     * 获取物资名称列表
     * 支持多选物资类型筛选：
     * - categories：物资类型列表，支持多选：1-矿石 2-煤炭 3-合金 4-废钢 99-全部
     * - curveType：曲线类型：1-价格曲线 2-采购量曲线
     * - dimensionType：时间维度：1-近三个月 2-近半年 3-近一年
     *
     * 根据曲线类型筛选：
     * - 价格曲线：只返回有价格数据的物资
     * - 采购量曲线：只返回有采购量数据的物资
     * - 未指定曲线类型：返回所有物资
     *
     * @param query 查询参数
     * @return 带序号的物资名称列表
     */
    @PostMapping("/getMaterialNameList")
    public AjaxResult getMaterialNameList(@RequestBody MaterialNameQueryDto query) {
        return AjaxResult.success(procurementDashboardService.getMaterialNameList(query));
    }

    /**
     * 调用大模型获取物料未来的价格走势
     * 返回包含问题和答案的完整信息
     */
    @PostMapping("/getMaterialFuturePrice")
    public AjaxResult getMaterialFuturePrice(@RequestBody ProcurementFuturePriceDto query) {
        return AjaxResult.success(procurementDashboardService.getMaterialFuturePrice(query));
    }

    /**
     * 获取物资价格和入库数量（使用新数据表）
     * 价格数据从procurement_material_price表获取
     * 采购量数据从procurement_material_amount表获取
     * 
     * @param query 查询参数，包含多个物资的查询条件列表
     * @return 物资价格和入库数量结果
     */
    @PostMapping("/getPurchasePriceAndStoreFromNewTables")
    public AjaxResult getPurchasePriceAndStoreFromNewTables(@RequestBody ProcurementPriceAndStoreQueryDto query) {
        return AjaxResult.success(procurementDashboardService.getPurchasePriceAndStoreFromNewTables(query));
    }

    /**
     * 获取物资名称列表（使用新数据表）
     * 支持多选物资类型筛选：
     * - categories：物资类型列表，支持多选：1-矿石 2-煤炭 3-合金 4-废钢 99-全部
     * - curveType：曲线类型：1-价格曲线 2-采购量曲线
     * - dimensionType：时间维度：1-近三个月 2-近半年 3-近一年
     *
     * 根据曲线类型筛选：
     * - 价格曲线：只返回有价格数据的物资（从procurement_material_price表）
     * - 采购量曲线：只返回有采购量数据的物资（从procurement_material_amount表）
     * - 未指定曲线类型：返回所有物资
     *
     * @param query 查询参数
     * @return 带序号的物资名称列表
     */
    @PostMapping("/getMaterialNameListFromNewTables")
    public AjaxResult getMaterialNameListFromNewTables(@RequestBody MaterialNameQueryDto query) {
        return AjaxResult.success(procurementDashboardService.getMaterialNameListFromNewTables(query));
    }

}
