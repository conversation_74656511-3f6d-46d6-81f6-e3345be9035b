package com.ruoyi.web.controller.xctg;

import com.ruoyi.app.v1.domain.*;
import com.ruoyi.app.v1.service.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * TYjyDeptUserController
 * 
 * <AUTHOR>
 * @date 2024-11-19
 */
@RestController
@RequestMapping("/web/TYjy/deptUser")
public class WebTYjyDeptUserController extends BaseController
{
    @Autowired
    private ITYjyDeptUserService tYjyDeptUserService;

    @Autowired
    private ITYjyDeptService tYjyDeptService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ITYjyPermissionService tYjyPermissionService;

    @Autowired
    private ITYjyFormService tYjyFormService;

    @Autowired
    private ITYjyDimensionalityPermissionService tYjyDimensionalityPermissionService;
    /**
     * 查询TYjyDeptUser列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TYjyDeptUser tYjyDeptUser)
    {

        startPage();
        List<TYjyDeptUser> list = tYjyDeptUserService.selectTYjyDeptUserList(tYjyDeptUser);
        return getDataTable(list);
    }

    //查询自己的部门信息
    @GetMapping("/selflist")
    public TableDataInfo selflist(TYjyDeptUser tYjyDeptUser)
    {
        tYjyDeptUser.setWorkNo(SecurityUtils.getUsername());
        startPage();
        List<TYjyDeptUser> list = tYjyDeptUserService.selectTYjyDeptUserList(tYjyDeptUser);
        return getDataTable(list);
    }

    //获取获取用户管理的所有部门的信息，用于管理和筛选功能
    @GetMapping("/managelist")
    public TableDataInfo managelist(TYjyDeptUser tYjyDeptUser)
    {
//        startPage();
        tYjyDeptUser.setWorkNo(SecurityUtils.getUsername());
        tYjyDeptUser.setRuleType("99");
        List<TYjyDeptUser> list = tYjyDeptUserService.selectTYjyDeptUserList(tYjyDeptUser);
        return getDataTable(list);
    }

    //获取部门下所有的普通员工的信息
    @GetMapping("/memberlist")
    public TableDataInfo memberlist(TYjyDeptUser tYjyDeptUser)
    {
//        startPage();
        tYjyDeptUser.setRuleType("0");
        List<TYjyDeptUser> list = tYjyDeptUserService.selectTYjyDeptUserList(tYjyDeptUser);
        return getDataTable(list);
    }


    //获取部门及子部门的员工情况
    @GetMapping("/allmemberlist")
    public TableDataInfo allmemberlist(TYjyDeptUser tYjyDeptUser)
    {
//        startPage();
        List<TYjyDeptUser> list = tYjyDeptUserService.selectAllMemberList(tYjyDeptUser);
        for(TYjyDeptUser item : list)
        {
            if(item.getRuleType().equals("99"))
            {
                item.setUserName(item.getDeptName()+"-"+item.getUserName()+"-分配人");
            }
            else
            {
                item.setUserName(item.getDeptName()+"-"+item.getUserName()+"-填报人");
            }
        }
        return getDataTable(list);
    }



    //获取部门及子部门的可分配员工情况
    @GetMapping("/todistributelist")
    public TableDataInfo todistributelist(TYjyDeptUser tYjyDeptUser)
    {
//        startPage();
        TYjyDeptUser searchinfo=new TYjyDeptUser();
        searchinfo.setPath(tYjyDeptUser.getPath());
        searchinfo.setWorkNo(SecurityUtils.getUsername());
        List<TYjyDeptUser> highnum=tYjyDeptUserService.selectAdministratorstoroot(searchinfo);
        if(highnum.size()==0)
        {
            return getDataTable(new ArrayList<>());
        }
        else
        {
            List<TYjyDeptUser> list = null;
            TYjyDeptUser rule =highnum.get(0);
            if(rule.getPath().length()>=tYjyDeptUser.getPath().length())
            {
                tYjyDeptUser.setPath(rule.getPath());
                list=tYjyDeptUserService.selectMemberToDistribute1(tYjyDeptUser);
            }
            else
            {
                list=tYjyDeptUserService.selectMemberToDistribute2(tYjyDeptUser);
            }
            for(TYjyDeptUser item : list)
            {
                if(item.getRuleType().equals("99"))
                {
                    item.setUserName(item.getDeptName()+"-"+item.getUserName()+"-分配人");
                }
                else
                {
                    item.setUserName(item.getDeptName()+"-"+item.getUserName()+"-填报人");
                }
            }
            return getDataTable(list);
        }
    }

    //获取部门及子部门的可分配员工情况
    @GetMapping("/distributedlist")
    public TableDataInfo distributedlist(TYjyDeptUser tYjyDeptUser)
    {
//        startPage();
//        TYjyDeptUser searchinfo=new TYjyDeptUser();
//        searchinfo.setPath(tYjyDeptUser.getPath());
//        searchinfo.setWorkNo(SecurityUtils.getUsername());
//        List<TYjyDeptUser> highnum=tYjyDeptUserService.selectAdministratorstoroot(searchinfo);
//        if(highnum.size()==0)
//        {
//            return getDataTable(new ArrayList<>());
//        }
//        else
//        {
//            List<TYjyDeptUser> list = new ArrayList<>();
//            TYjyDeptUser rule =highnum.get(0);
//            if(rule.getPath().length()>=tYjyDeptUser.getPath().length())
//            {
//                tYjyDeptUser.setPath(rule.getPath());
//                list=tYjyDeptUserService.selectMemberDistributed1(tYjyDeptUser);
//            }
//            else
//            {
//                list=tYjyDeptUserService.selectMemberDistributed2(tYjyDeptUser);
//            }
//            for(TYjyDeptUser item : list)
//            {
//                if(item.getRuleType().equals("99"))
//                {
//                    item.setUserName(item.getDeptName()+"-"+item.getUserName()+"-分配人");
//                }
//                else
//                {
//                    item.setUserName(item.getDeptName()+"-"+item.getUserName()+"-填报人");
//                }
//            }
//            return getDataTable(list);
//        }
//        TYjyDeptUser searchinfo=new TYjyDeptUser();
//        searchinfo.setPath(tYjyDeptUser.getPath());
//        searchinfo.setWorkNo(SecurityUtils.getUsername());

        List<TYjyDeptUser> list = new ArrayList<>();
        list=tYjyDeptUserService.selectMemberDistributed2(tYjyDeptUser);
        for(TYjyDeptUser item : list)
        {
            if(item.getRuleType().equals("99"))
            {
                item.setUserName(item.getDeptName()+"-"+item.getUserName()+"-分配人");
            }
            else
            {
                item.setUserName(item.getDeptName()+"-"+item.getUserName()+"-填报人");
            }
        }
        return getDataTable(list);

    }


    //获取部门及子部门的审核人权限
//    @GetMapping("/checklist")
//    public TableDataInfo checklist(TYjyDeptUser tYjyDeptUser)
//    {
//        TYjyDeptUser searchinfo=new TYjyDeptUser();
//        searchinfo.setPath(tYjyDeptUser.getPath());
//        searchinfo.setWorkNo(SecurityUtils.getUsername());
//        List<TYjyDeptUser> highnum=tYjyDeptUserService.selectAdministratorstoroot(searchinfo);
//        if(highnum.size()==0)
//        {
//            return getDataTable(null);
//        }
//        else
//        {
//            List<TYjyDeptUser> list = null;
//            TYjyDeptUser rule =highnum.get(0);
//            if(rule.getPath().length()>=tYjyDeptUser.getPath().length())
//            {
//                tYjyDeptUser.setPath(rule.getPath());
//                list=tYjyDeptUserService.selectMemberDistributed1(tYjyDeptUser);
//            }
//            else
//            {
//                list=tYjyDeptUserService.selectMemberDistributed2(tYjyDeptUser);
//            }
//            for(TYjyDeptUser item : list)
//            {
//                if(item.getRuleType().equals("99"))
//                {
//                    item.setUserName(item.getDeptName()+"-"+item.getUserName()+"-分配人");
//                }
//                else
//                {
//                    item.setUserName(item.getDeptName()+"-"+item.getUserName()+"-填报人");
//                }
//            }
//            return getDataTable(list);
//        }
//    }


    /**
     * 导出TYjyDeptUser列表
     */
    @Log(title = "TYjyDeptUser", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TYjyDeptUser tYjyDeptUser)
    {
        List<TYjyDeptUser> list = tYjyDeptUserService.selectTYjyDeptUserList(tYjyDeptUser);
        ExcelUtil<TYjyDeptUser> util = new ExcelUtil<TYjyDeptUser>(TYjyDeptUser.class);
        return util.exportExcel(list, "deptUser");
    }

    /**
     * 获取TYjyDeptUser详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tYjyDeptUserService.selectTYjyDeptUserById(id));
    }

    /**
     * 新增TYjyDeptUser
     */
    @Log(title = "TYjyDeptUser", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TYjyDeptUser tYjyDeptUser)
    {
        SysUser userinfo =userService.selectUserByUserName(tYjyDeptUser.getWorkNo());
        if(userinfo==null)
        {
            return error("该用户的工号错误");
        }
        if(!userinfo.getNickName().equals(tYjyDeptUser.getUserName()))
        {
            return error("该用户的姓名和工号不匹配");
        }
        if(tYjyDeptUser.getRuleType().equals("99"))
        {
            TYjyDept change=new TYjyDept();
            change.setId(tYjyDeptUser.getDeptId());
            tYjyDeptService.addCount(change);
        }
        return toAjax(tYjyDeptUserService.insertTYjyDeptUser(tYjyDeptUser));
    }

    @Log(title = "TYjyDeptUser", businessType = BusinessType.INSERT)
    @PostMapping("/distributemember")
    public AjaxResult distributemember(@RequestBody TYjyDeptUser tYjyDeptUser)
    {

        SysUser userinfo =userService.selectUserByUserName(tYjyDeptUser.getWorkNo());
        if(userinfo==null)
        {
            return error("该用户的工号错误");
        }
        if(!userinfo.getNickName().equals(tYjyDeptUser.getUserName()))
        {
            return error("该用户的姓名和工号不匹配");
        }
        if(tYjyDeptUser.getRuleType().equals("99"))
        {
            TYjyDept change=new TYjyDept();
            change.setId(tYjyDeptUser.getDeptId());
            tYjyDeptService.addCount(change);
        }
        return toAjax(tYjyDeptUserService.insertTYjyDeptUser(tYjyDeptUser));
    }

    /**
     * 修改TYjyDeptUser
     */
    @Log(title = "TYjyDeptUser", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody TYjyDeptUser tYjyDeptUser)
    {
        TYjyDeptUser compare=tYjyDeptUserService.selectTYjyDeptUserById(tYjyDeptUser.getId());
        TYjyDeptUser user= new TYjyDeptUser();
        user.setDeptId(compare.getDeptId());
        user.setWorkNo(compare.getWorkNo());
        user.setRuleType(tYjyDeptUser.getRuleType());
        List<TYjyDeptUser> list=tYjyDeptUserService.selectTYjyDeptUserList(user);
        if(list.size()>0)
        {
            return error(tYjyDeptUser.getUserName()+"在"+list.get(0).getDeptName()+"已经存在相同的角色了，本次变更无效");
        }
        if(tYjyDeptUser.getRuleType().equals("99")&&(!compare.getRuleType().equals("99")))
        {
            TYjyDept change=new TYjyDept();
            change.setId(tYjyDeptUser.getDeptId());
            tYjyDeptService.addCount(change);
        }
        if(!tYjyDeptUser.getRuleType().equals("99")&&(compare.getRuleType().equals("99")))
        {
            TYjyDept change=new TYjyDept();
            change.setId(tYjyDeptUser.getDeptId());
            tYjyDeptService.reduceCount(change);
        }

//        SysUser userinfo =userService.selectUserByUserName(tYjyDeptUser.getWorkNo());
//        if(userinfo==null)
//        {
//            return error("该用户的工号错误");
//        }
//        if(!userinfo.getNickName().equals(tYjyDeptUser.getUserName()))
//        {
//            return error("该用户的姓名和工号不匹配");
//        }
        if(tYjyDeptUser.getRuleType().equals("99"))
        {
            user= new TYjyDeptUser();
            user.setPath(tYjyDeptUser.getPath());
            user.setWorkNo(tYjyDeptUser.getWorkNo());
            user.setRuleType("99");
            list=tYjyDeptUserService.selectuserdeptinfo(user);
            if(list.size()>0)
            {
                return error(tYjyDeptUser.getUserName()+"在"+list.get(0).getDeptName()+"已经存在分配人权限，与两个部门间存在父子关系，要新增配置请删除旧配置");
            }
        }
        if(!tYjyDeptUser.getRuleType().equals("99")&&compare.getRuleType().equals("99"))
        {
            tYjyPermissionService.deleteTYjyPermissionByRoleId(tYjyDeptUser.getId());
        }
        if(tYjyDeptUser.getRuleType().equals("99")&&!compare.getRuleType().equals("99"))
        {
            TYjyForm tYjyForm=new TYjyForm();
            tYjyForm.setDistributeDept(compare.getPath());
            List<TYjyForm> tYjyFormlist=tYjyFormService.selectWithDistribute(tYjyForm);
            TYjyPermission tYjyPermission=new TYjyPermission();
            tYjyPermission.setWorkNo(tYjyDeptUser.getWorkNo());
            tYjyPermission.setRoleId(tYjyDeptUser.getId());
            tYjyPermission.setDeptCode(tYjyDeptUser.getPath());
            tYjyPermission.setRuleType("99");
            tYjyPermission.setDeptName(tYjyDeptUser.getDeptName());
            for(TYjyForm item:tYjyFormlist)
            {
                tYjyPermission.setFormId(item.getId());
                tYjyPermissionService.insertTYjyPermission(tYjyPermission);
            }
        }


        if(tYjyDeptUser.getRuleType().equals("1"))
        {
            user= new TYjyDeptUser();
            user.setPath(tYjyDeptUser.getPath());
            user.setWorkNo(tYjyDeptUser.getWorkNo());
            user.setRuleType("1");
            list=tYjyDeptUserService.selectuserdeptinfo(user);
            if(list.size()>0)
            {
                return error(tYjyDeptUser.getUserName()+"在"+list.get(0).getDeptName()+"已经存在扎口部门管理人权限，与两个部门间存在父子关系，要新增配置请删除旧配置");
            }
        }

        if(!tYjyDeptUser.getRuleType().equals("1")&&compare.getRuleType().equals("1"))
        {
            TYjyDimensionalityPermission tYjyDimensionalityPermission = new TYjyDimensionalityPermission();
            tYjyDimensionalityPermission.setWorkNo(compare.getWorkNo());
            tYjyDimensionalityPermission.setPath(compare.getPath());
            tYjyDimensionalityPermissionService.deleteTYjyDimensionalityPermissionForAdministrators(tYjyDimensionalityPermission);
        }
        if(tYjyDeptUser.getRuleType().equals("1")&&!compare.getRuleType().equals("1"))
        {
            TYjyDimensionalityPermission tYjyDimensionalityPermission = new TYjyDimensionalityPermission();
            tYjyDimensionalityPermission.setWorkNo(compare.getWorkNo());
            tYjyDimensionalityPermission.setPath(compare.getPath());
            tYjyDimensionalityPermissionService.deleteTYjyDimensionalityPermissionForAdministrators(tYjyDimensionalityPermission);
            tYjyDimensionalityPermission.setUserName(compare.getUserName());
            tYjyDimensionalityPermissionService.insertTYjyDimensionalityPermissionForAdministrators(tYjyDimensionalityPermission);
        }
        return toAjax(tYjyDeptUserService.updateTYjyDeptUser(tYjyDeptUser));
    }

    /**
     * 删除TYjyDeptUser
     */
    @Log(title = "TYjyDeptUser", businessType = BusinessType.DELETE)
    @DeleteMapping("/authUser/cancel")
    public AjaxResult authUserCancel(Long id)
    {
        TYjyDeptUser tYjyDeptUser=tYjyDeptUserService.selectTYjyDeptUserById(id);

        if(tYjyDeptUser.getRuleType().equals("99"))
        {
            TYjyDept change=new TYjyDept();
            change.setId(tYjyDeptUser.getDeptId());
            tYjyDeptService.reduceCount(change);
        }
        if(tYjyDeptUser.getRuleType().equals("1"))
        {
            TYjyDimensionalityPermission tYjyDimensionalityPermission = new TYjyDimensionalityPermission();
            tYjyDimensionalityPermission.setWorkNo(tYjyDeptUser.getWorkNo());
            tYjyDimensionalityPermission.setPath(tYjyDeptUser.getPath());
            tYjyDimensionalityPermissionService.deleteTYjyDimensionalityPermissionForAdministrators(tYjyDimensionalityPermission);
        }

        tYjyPermissionService.deleteTYjyPermissionByRoleId(id);
        return toAjax(tYjyDeptUserService.deleteTYjyDeptUserById(id));
    }
    @Log(title = "TYjyDeptUserDeleteAll", businessType = BusinessType.DELETE)
    @DeleteMapping("/authUser/cancelAll")
    public AjaxResult authUserCancelAll(Long[] ids)
    {
        for(Long id:ids)
        {
            TYjyDeptUser tYjyDeptUser=tYjyDeptUserService.selectTYjyDeptUserById(id);
            if(tYjyDeptUser.getRuleType().equals("99"))
            {
                TYjyDept change=new TYjyDept();
                change.setId(tYjyDeptUser.getDeptId());
                tYjyDeptService.reduceCount(change);
            }
            if(tYjyDeptUser.getRuleType().equals("1"))
            {
                TYjyDimensionalityPermission tYjyDimensionalityPermission = new TYjyDimensionalityPermission();
                tYjyDimensionalityPermission.setWorkNo(tYjyDeptUser.getWorkNo());
                tYjyDimensionalityPermission.setPath(tYjyDeptUser.getPath());
                tYjyDimensionalityPermissionService.deleteTYjyDimensionalityPermissionForAdministrators(tYjyDimensionalityPermission);
            }
            tYjyPermissionService.deleteTYjyPermissionByRoleId(id);
        }
        return toAjax(tYjyDeptUserService.deleteTYjyDeptUserByIds(ids));
    }

    @GetMapping("/authUser/unallocatedList")
    public TableDataInfo unallocatedList(TYjyDeptUser user)
    {
        startPage();
        List<TYjyDeptUser> list = tYjyDeptUserService.selectUnallocatedList(user);
        return getDataTable(list);
    }

    @GetMapping("/authUser/selectAllUser")
    public TableDataInfo selectAllUser(TYjyDeptUser user)
    {
        startPage();
        List<TYjyDeptUser> list = tYjyDeptUserService.selectAllUser(user);
        return getDataTable(list);
    }

    @Log(title = "TYjyDeptUserSelectAll", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/selectAll")
    public AjaxResult selectAuthUserAll(Long deptId, String[] workNos, String[] userNames)
    {
        for(int i=0;i< workNos.length;i++)
        {
            TYjyDeptUser user= new TYjyDeptUser();
            user.setDeptId(deptId);
            user.setWorkNo(workNos[i]);
            user.setRuleType("0");
            List<TYjyDeptUser> list=tYjyDeptUserService.selectTYjyDeptUserList(user);
            if(list.size()>0)
            {
                return error(userNames[i]+"在"+list.get(0).getDeptName()+"已经存在相同的角色了，本次新增无效");
            }
        }
        return toAjax(tYjyDeptUserService.batchTYjyDeptUsers(deptId, workNos,userNames));
    }


}
