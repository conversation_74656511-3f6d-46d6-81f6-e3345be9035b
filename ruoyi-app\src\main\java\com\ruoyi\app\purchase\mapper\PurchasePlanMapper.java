package com.ruoyi.app.purchase.mapper;

import com.ruoyi.app.purchase.domain.PurchasePlan;
import com.ruoyi.app.purchase.domain.PurchaseProviderTotalAmount;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 采购计划看板Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface PurchasePlanMapper
{

    @DataSource(value = DataSourceType.XCC1)
    List<PurchasePlan> selectPurchasePlanList();

}
