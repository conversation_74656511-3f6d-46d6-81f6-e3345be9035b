<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.purchase.mapper.PurchasePlanMapper">

    <resultMap type="com.ruoyi.app.purchase.domain.PurchasePlan" id="PurchasePlanResult">
        <result property="recCreator"    column="REC_CREATOR"    />
        <result property="recCreateTime"    column="REC_CREATE_TIME"    />
        <result property="recRevisor"    column="REC_REVISOR"    />
        <result property="recReviseTime"    column="REC_REVISE_TIME"    />
        <result property="midDays"    column="MID_DAYS"    />
        <result property="avgDays"    column="AVG_DAYS"    />
        <result property="countType"    column="COUNT_TYPE"    />
        <result property="itemType"    column="ITEM_TYPE"    />
        <result property="period"    column="PERIOD"    />
        <result property="lyNum"    column="LY_NUM"    />
        <result property="rkNum"    column="RK_NUM"    />
    </resultMap>

    <select id="selectPurchasePlanList"  resultMap="PurchasePlanResult">
        SELECT t.PERIOD,t.COUNT_TYPE,t.MID_DAYS,t.AVG_DAYS FROM TPOPI99_PLAN t
    </select>


</mapper>
