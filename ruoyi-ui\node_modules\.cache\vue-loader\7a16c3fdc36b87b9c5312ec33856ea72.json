{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardMain\\index.vue?vue&type=style&index=0&id=5eb4b045&lang=scss&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardMain\\index.vue", "mtime": 1756456493834}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluIHsNCiAgd2lkdGg6IDEwMCU7DQogIG1pbi1oZWlnaHQ6IDEwMHZoOw0KDQogIC5kYXNoYm9hcmQtY29udGFpbmVyIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBtaW4taGVpZ2h0OiAxMDB2aDsNCiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMTkxOTcwLCAjNEIwMDgyLCAjODAwMDgwKTsNCiAgICBjb2xvcjogI2ZmZjsNCiAgICBvdmVyZmxvdy14OiBoaWRkZW47DQogICAgcGFkZGluZzogMTBweDsNCiAgfQ0KDQogIC5kYXNoYm9hcmQtaGVhZGVyIHsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgcGFkZGluZzogNXB4IDA7DQoNCiAgICBoMSB7DQogICAgICBmb250LXNpemU6IDI0cHg7DQogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICBwYWRkaW5nOiA1cHggNDBweDsNCiAgICAgIG1hcmdpbjogMDsNCiAgICAgIGNvbG9yOiAjZmZmOw0KICAgIH0NCg0KICAgICY6OmJlZm9yZSwNCiAgICAmOjphZnRlciB7DQogICAgICBjb250ZW50OiAiIjsNCiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgIHRvcDogNTAlOw0KICAgICAgd2lkdGg6IDMwJTsNCiAgICAgIGhlaWdodDogMnB4Ow0KICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCByZ2JhKDAsMjEyLDI1NSwwKSAwJSwgcmdiYSgwLDIxMiwyNTUsMSkgNTAlLCByZ2JhKDAsMjEyLDI1NSwwKSAxMDAlKTsNCiAgICB9DQoNCiAgICAmOjpiZWZvcmUgew0KICAgICAgbGVmdDogMDsNCiAgICB9DQoNCiAgICAmOjphZnRlciB7DQogICAgICByaWdodDogMDsNCiAgICB9DQogIH0NCg0KICAuaGVhZGVyLWNvbnRyb2xzIHsNCiAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgcmlnaHQ6IDIwcHg7DQogICAgdG9wOiA1MCU7DQogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBnYXA6IDE1cHg7DQogICAgei1pbmRleDogMTAwMDsNCiAgfQ0KDQogIC5mdWxsc2NyZWVuLWJ0biB7DQogICAgcGFkZGluZzogOHB4IDEycHg7DQogICAgYm9yZGVyOiBub25lOw0KICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMzMsIDEwLCA1NiwgMC43KTsNCiAgICBjb2xvcjogI2VlZTsNCiAgICBib3JkZXItcmFkaXVzOiAyMHB4Ow0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjIpOw0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgd2lkdGg6IDQwcHg7DQogICAgaGVpZ2h0OiAzMnB4Ow0KDQogICAgJjpob3ZlciB7DQogICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpOw0KICAgICAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSgwLCAyMTIsIDI1NSwgMC4zKTsNCiAgICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMjEyLCAyNTUsIDAuMik7DQogICAgICBib3JkZXItY29sb3I6IHJnYmEoMCwgMjEyLCAyNTUsIDAuNyk7DQogICAgICBjb2xvcjogIzAwZmZmZjsNCiAgICB9DQogIH0NCg0KICAudGltZS1maWx0ZXIgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZ2FwOiAxMHB4Ow0KICB9DQoNCiAgLnRpbWUtZmlsdGVyLWJ0biB7DQogICAgcGFkZGluZzogNnB4IDEycHg7DQogICAgYm9yZGVyOiBub25lOw0KICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMzMsIDEwLCA1NiwgMC43KTsNCiAgICBjb2xvcjogI2VlZTsNCiAgICBib3JkZXItcmFkaXVzOiAyMHB4Ow0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjIpOw0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAgICY6aG92ZXIgew0KICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTsNCiAgICAgIGJveC1zaGFkb3c6IDAgMCAxNXB4IHJnYmEoMCwgMjEyLCAyNTUsIDAuMyk7DQogICAgfQ0KDQogICAgJi5hY3RpdmUgew0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAyMTIsIDI1NSwgMC4yKTsNCiAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgwLCAyMTIsIDI1NSwgMC43KTsNCiAgICAgIGNvbG9yOiAjMDBmZmZmOw0KICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICB9DQogIH0NCg0KICAuZGFzaGJvYXJkLWNvbnRlbnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gODBweCk7DQogICAgZ2FwOiAxMHB4Ow0KICB9DQoNCiAgLmxlZnQtcGFuZWwsDQogIC5yaWdodC1wYW5lbCB7DQogICAgZmxleDogMCAwIDMyMHB4Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDEwcHg7DQogIH0NCg0KICAuY2VudGVyLXBhbmVsIHsNCiAgICBmbGV4OiAxOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBnYXA6IDVweDsgLyog57yp55+t6KGM6Ze06Led5LuOMTBweOWIsDVweCAqLw0KICB9DQoNCiAgLmNlbnRlci1yb3cgew0KICAgIGZsZXg6IDE7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBnYXA6IDVweDsgLyog57yp55+t5Y2h54mH6Ze06Led5LuOMTBweOWIsDVweCAqLw0KICB9DQoNCiAgLmNlbnRlci1yb3cgLmNhcmQgew0KICAgIGZsZXg6IDE7DQogIH0NCg0KICAvKiDnrKzkuIDooYznibnlrprmoLflvI8gLSDnvKnnn63pq5jluqYgKi8NCiAgLmNlbnRlci1yb3ctZmlyc3Qgew0KICAgIGZsZXg6IDE7IC8qIOWHj+Wwj+esrOS4gOihjOeahOmrmOW6puavlOS+iyAqLw0KICAgIG1heC1oZWlnaHQ6IDI1MHB4OyAvKiDov5vkuIDmraXpmZDliLbnrKzkuIDooYznmoTmnIDlpKfpq5jluqYgKi8NCiAgfQ0KDQogIC8qIOesrOS6jOihjOeJueWumuagt+W8jyAtIOe8qeefremrmOW6piAqLw0KICAuY2VudGVyLXJvdy1zZWNvbmQgew0KICAgIGZsZXg6IDAuNzsgLyog6L+b5LiA5q2l5YeP5bCP56ys5LqM6KGM55qE6auY5bqm5q+U5L6LICovDQogICAgbWF4LWhlaWdodDogMzMwcHg7IC8qIOi/m+S4gOatpemZkOWItuesrOS6jOihjOeahOacgOWkp+mrmOW6piAqLw0KICB9DQoNCiAgLyog5YWo5bGP5qih5byP5LiL55qE5qC35byP6LCD5pW0IC0g5L2/55So5pu06auY5LyY5YWI57qn55qE6YCJ5oup5ZmoICovDQogIC5wdXJjaGFzZS1kYXNoYm9hcmQtbWFpbi5mdWxsc2NyZWVuLW1vZGUgew0KICAgIC8qIOiwg+ivleagt+W8jyAtIOWFqOWxj+aXtuaUueWPmOiDjOaZr+iJsiAqLw0KICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyYTJhOTAsICM1QjEwODIsICM5MDAwOTApICFpbXBvcnRhbnQ7DQogIH0NCg0KICAucHVyY2hhc2UtZGFzaGJvYXJkLW1haW4uZnVsbHNjcmVlbi1tb2RlIC5kYXNoYm9hcmQtY29udGFpbmVyIC5jZW50ZXItcGFuZWwgLmNlbnRlci1yb3ctZmlyc3Qgew0KICAgIG1heC1oZWlnaHQ6IG5vbmUgIWltcG9ydGFudDsgLyog5YWo5bGP5pe256e76Zmk56ys5LiA6KGM6auY5bqm6ZmQ5Yi2ICovDQogICAgZmxleDogMSAhaW1wb3J0YW50OyAvKiDnoa7kv51mbGV45q+U5L6L5q2j56GuICovDQogIH0NCg0KICAucHVyY2hhc2UtZGFzaGJvYXJkLW1haW4uZnVsbHNjcmVlbi1tb2RlIC5kYXNoYm9hcmQtY29udGFpbmVyIC5jZW50ZXItcGFuZWwgLmNlbnRlci1yb3ctc2Vjb25kIHsNCiAgICBtYXgtaGVpZ2h0OiBub25lICFpbXBvcnRhbnQ7IC8qIOWFqOWxj+aXtuenu+mZpOesrOS6jOihjOmrmOW6pumZkOWItiAqLw0KICAgIGZsZXg6IDEgIWltcG9ydGFudDsgLyog56Gu5L+dZmxleOavlOS+i+ato+ehriAqLw0KICB9DQoNCiAgLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluLmZ1bGxzY3JlZW4tbW9kZSAuZGFzaGJvYXJkLWNvbnRhaW5lciAuY2VudGVyLXBhbmVsIC5jZW50ZXItcm93LWZ1bGwgLmNhcmQgew0KICAgIG1heC1oZWlnaHQ6IG5vbmUgIWltcG9ydGFudDsgLyog5YWo5bGP5pe256e76Zmk5YWo5a696KGM6auY5bqm6ZmQ5Yi2ICovDQogICAgbWluLWhlaWdodDogNjBweCAhaW1wb3J0YW50OyAvKiDkv53mjIHmnIDlsI/pq5jluqYgKi8NCiAgfQ0KDQogIC8qIOWFqOWxj+aooeW8j+S4i+iwg+aVtOaVtOS9k+WuueWZqOmrmOW6puWSjOW4g+WxgCAqLw0KICAucHVyY2hhc2UtZGFzaGJvYXJkLW1haW4uZnVsbHNjcmVlbi1tb2RlIC5kYXNoYm9hcmQtY29udGVudCB7DQogICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gNjBweCkgIWltcG9ydGFudDsgLyog5YWo5bGP5pe25YeP5Y675qCH6aKY6auY5bqmICovDQogICAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDYwcHgpICFpbXBvcnRhbnQ7DQogICAgZGlzcGxheTogZmxleCAhaW1wb3J0YW50Ow0KICAgIGdhcDogMTBweCAhaW1wb3J0YW50Ow0KICAgIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXIgIWltcG9ydGFudDsgLyog5bGF5Lit5a+56b2QICovDQogICAgYWxpZ24taXRlbXM6IHN0cmV0Y2ggIWltcG9ydGFudDsNCiAgICBvdmVyZmxvdy14OiBhdXRvICFpbXBvcnRhbnQ7IC8qIOWFgeiuuOawtOW5s+a7muWKqOS7pemYsuWGheWuuei/h+WuvSAqLw0KICAgIHBhZGRpbmc6IDAgMTBweCAhaW1wb3J0YW50OyAvKiDmt7vliqDkuIDkupvlhoXovrnot50gKi8NCiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAvKiDmlrDlop7vvJrlhajlrr3ooYzmoLflvI8gKi8NCiAgLmNlbnRlci1yb3ctZnVsbCB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgbWFyZ2luOiAycHggMDsgLyog57yp55+t5LiK5LiL6L656Led5LuONXB45YiwMnB4ICovDQogICAgZmxleC1zaHJpbms6IDA7IC8qIOmYsuatouiiq+WOi+e8qSAqLw0KICB9DQoNCiAgLmNlbnRlci1yb3ctZnVsbCAuY2FyZCB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgbWluLWhlaWdodDogNTBweDsgLyog6K6+572u5pyA5bCP6auY5bqmICovDQogICAgbWF4LWhlaWdodDogODBweDsgLyog6K6+572u5pyA5aSn6auY5bqm77yM56Gu5L+d5LiN5Y2g55So5aSq5aSa56m66Ze0ICovDQogIH0NCg0KICAubGVmdC1wYW5lbCAuY2FyZCwNCiAgLnJpZ2h0LXBhbmVsIC5jYXJkIHsNCiAgICBmbGV4OiAxOw0KICB9DQoNCiAgLyog5YWo5bGP5qih5byP5LiL5Y+z5L6n6Z2i5p2/55qE54m55q6K5qC35byPIC0g6Kej5Yaz6auY5bqm6KKr6L+H5bqm5ouJ5Ly455qE6Zeu6aKYICovDQogIC5wdXJjaGFzZS1kYXNoYm9hcmQtbWFpbi5mdWxsc2NyZWVuLW1vZGUgLnJpZ2h0LXBhbmVsIHsNCiAgICAvKiDmlLnlj5jlj7PkvqfpnaLmnb/nmoTluIPlsYDmlrnlvI/vvIzlubPlnYfliIbphY3nqbrpl7TogIzkuI3mmK/mi4nkvLggKi8NCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW4gIWltcG9ydGFudDsNCiAgICBhbGlnbi1pdGVtczogc3RyZXRjaCAhaW1wb3J0YW50Ow0KICAgIHdpZHRoOiAzMjBweCAhaW1wb3J0YW50OyAvKiDlm7rlrprlj7PkvqfpnaLmnb/lrr3luqYgKi8NCiAgICBtaW4td2lkdGg6IDMyMHB4ICFpbXBvcnRhbnQ7DQogICAgbWF4LXdpZHRoOiAzMjBweCAhaW1wb3J0YW50Ow0KICAgIGZsZXg6IG5vbmUgIWltcG9ydGFudDsNCiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAucHVyY2hhc2UtZGFzaGJvYXJkLW1haW4uZnVsbHNjcmVlbi1tb2RlIC5yaWdodC1wYW5lbCAuY2FyZCB7DQogICAgZmxleDogMCAwIGNhbGMoMzMuMzMlIC0gOHB4KSAhaW1wb3J0YW50OyAvKiDkuInnrYnliIbvvIzlh4/ljrvpl7Tot50gKi8NCiAgICBoZWlnaHQ6IGF1dG8gIWltcG9ydGFudDsgLyog6K6p5YaF5a655Yaz5a6a6auY5bqmICovDQogICAgbWluLWhlaWdodDogMTIwcHggIWltcG9ydGFudDsgLyog6K6+572u5pu05bCP55qE5pyA5bCP6auY5bqmICovDQogICAgbWF4LWhlaWdodDogMjAwcHggIWltcG9ydGFudDsgLyog6ZmQ5Yi25pyA5aSn6auY5bqm77yM5pu05Yqg57Sn5YeRICovDQogICAgb3ZlcmZsb3cteTogYXV0byAhaW1wb3J0YW50OyAvKiDlhoXlrrnov4flpJrml7bmu5rliqggKi8NCiAgfQ0KDQogIC5wdXJjaGFzZS1kYXNoYm9hcmQtbWFpbi5mdWxsc2NyZWVuLW1vZGUgLmxlZnQtcGFuZWwgew0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbiAhaW1wb3J0YW50Ow0KICAgIHdpZHRoOiAzMjBweCAhaW1wb3J0YW50OyAvKiDlm7rlrprlt6bkvqfpnaLmnb/lrr3luqYgKi8NCiAgICBtaW4td2lkdGg6IDMyMHB4ICFpbXBvcnRhbnQ7DQogICAgbWF4LXdpZHRoOiAzMjBweCAhaW1wb3J0YW50Ow0KICAgIGZsZXg6IG5vbmUgIWltcG9ydGFudDsNCiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAucHVyY2hhc2UtZGFzaGJvYXJkLW1haW4uZnVsbHNjcmVlbi1tb2RlIC5jZW50ZXItcGFuZWwgew0KICAgIGZsZXg6IDEgIWltcG9ydGFudDsgLyog5Lit6Ze06Z2i5p2/5Y2g55So5Ymp5L2Z56m66Ze0ICovDQogICAgbWluLXdpZHRoOiA0MDBweCAhaW1wb3J0YW50OyAvKiDmnIDlsI/lrr3luqbkv53or4HlhoXlrrnmmL7npLogKi8NCiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94ICFpbXBvcnRhbnQ7DQogIH0NCg0KDQoNCiAgLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluLmZ1bGxzY3JlZW4tbW9kZSAubGVmdC1wYW5lbCAuY2FyZCB7DQogICAgZmxleDogMCAwIGNhbGMoNTAlIC0gNnB4KSAhaW1wb3J0YW50OyAvKiDkuoznrYnliIbvvIzlh4/ljrvpl7Tot50gKi8NCiAgICBoZWlnaHQ6IGF1dG8gIWltcG9ydGFudDsNCiAgICBtaW4taGVpZ2h0OiAxNDBweCAhaW1wb3J0YW50Ow0KICAgIG1heC1oZWlnaHQ6IDI2MHB4ICFpbXBvcnRhbnQ7DQogICAgb3ZlcmZsb3cteTogYXV0byAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLyog5YWo5bGP5qih5byP5LiL5LyY5YyW5YW35L2T5YaF5a6555qE5pi+56S6IC0g57yp5bCP5YaF5a65ICovDQogIC5wdXJjaGFzZS1kYXNoYm9hcmQtbWFpbi5mdWxsc2NyZWVuLW1vZGUgLmNhcmQtdGl0bGUgew0KICAgIGZvbnQtc2l6ZTogMTRweCAhaW1wb3J0YW50Ow0KICAgIG1hcmdpbi1ib3R0b206IDhweCAhaW1wb3J0YW50Ow0KICAgIHBhZGRpbmc6IDhweCAxMnB4ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAucHVyY2hhc2UtZGFzaGJvYXJkLW1haW4uZnVsbHNjcmVlbi1tb2RlIC53YXJuaW5nLWFuYWx5c2lzIHsNCiAgICBwYWRkaW5nOiA2cHggMTJweCAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluLmZ1bGxzY3JlZW4tbW9kZSAud2FybmluZy1pdGVtIHsNCiAgICBtYXJnaW4tYm90dG9tOiA2cHggIWltcG9ydGFudDsNCiAgICBwYWRkaW5nOiA0cHggMCAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluLmZ1bGxzY3JlZW4tbW9kZSAud2FybmluZy1uYW1lIHsNCiAgICBmb250LXNpemU6IDEycHggIWltcG9ydGFudDsNCiAgfQ0KDQogIC5wdXJjaGFzZS1kYXNoYm9hcmQtbWFpbi5mdWxsc2NyZWVuLW1vZGUgLndhcm5pbmctdmFsdWUgew0KICAgIGZvbnQtc2l6ZTogMTFweCAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluLmZ1bGxzY3JlZW4tbW9kZSAuc2ltcGxlLWRpc3BsYXkgew0KICAgIHBhZGRpbmc6IDEwcHggMTJweCAhaW1wb3J0YW50Ow0KICAgIHRleHQtYWxpZ246IGNlbnRlciAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluLmZ1bGxzY3JlZW4tbW9kZSAuZGlzcGxheS1udW1iZXIgew0KICAgIGZvbnQtc2l6ZTogMjRweCAhaW1wb3J0YW50Ow0KICAgIG1hcmdpbi1ib3R0b206IDRweCAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluLmZ1bGxzY3JlZW4tbW9kZSAuZGlzcGxheS1sYWJlbCB7DQogICAgZm9udC1zaXplOiAxMXB4ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAucHVyY2hhc2UtZGFzaGJvYXJkLW1haW4uZnVsbHNjcmVlbi1tb2RlIC5mdW5uZWwtZGF0YSB7DQogICAgcGFkZGluZzogNnB4IDEycHggIWltcG9ydGFudDsNCiAgfQ0KDQogIC5wdXJjaGFzZS1kYXNoYm9hcmQtbWFpbi5mdWxsc2NyZWVuLW1vZGUgLmZ1bm5lbC1pdGVtIHsNCiAgICBtYXJnaW4tYm90dG9tOiA0cHggIWltcG9ydGFudDsNCiAgICBwYWRkaW5nOiAzcHggMCAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluLmZ1bGxzY3JlZW4tbW9kZSAuZnVubmVsLWxhYmVsIHsNCiAgICBmb250LXNpemU6IDExcHggIWltcG9ydGFudDsNCiAgfQ0KDQogIC5wdXJjaGFzZS1kYXNoYm9hcmQtbWFpbi5mdWxsc2NyZWVuLW1vZGUgLmZ1bm5lbC12YWx1ZSB7DQogICAgZm9udC1zaXplOiAxMnB4ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAvKiDlhajlsY/mqKHlvI/kuIvnvKnlsI/orablkYrmnaHnmoTpq5jluqYgKi8NCiAgLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluLmZ1bGxzY3JlZW4tbW9kZSAud2FybmluZy1iYXIgew0KICAgIGhlaWdodDogMTZweCAhaW1wb3J0YW50Ow0KICAgIG1hcmdpbi1sZWZ0OiA4cHggIWltcG9ydGFudDsNCiAgfQ0KDQogIC5wdXJjaGFzZS1kYXNoYm9hcmQtbWFpbi5mdWxsc2NyZWVuLW1vZGUgLmJhci1iZyB7DQogICAgaGVpZ2h0OiAxNnB4ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAucHVyY2hhc2UtZGFzaGJvYXJkLW1haW4uZnVsbHNjcmVlbi1tb2RlIC5iYXItZmlsbCB7DQogICAgaGVpZ2h0OiAxNnB4ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAvKiDlhajlsY/mqKHlvI/kuIvosIPmlbTljaHniYflhoXovrnot50gKi8NCiAgLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluLmZ1bGxzY3JlZW4tbW9kZSAucmlnaHQtcGFuZWwgLmNhcmQgew0KICAgIHBhZGRpbmc6IDhweCAhaW1wb3J0YW50Ow0KICB9DQoNCiAgLnB1cmNoYXNlLWRhc2hib2FyZC1tYWluLmZ1bGxzY3JlZW4tbW9kZSAubGVmdC1wYW5lbCAuY2FyZCB7DQogICAgcGFkZGluZzogOHB4ICFpbXBvcnRhbnQ7DQogIH0NCg0KICAuY2FyZCB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgzMywgMTAsIDU2LCAwLjcpOw0KICAgIGJvcmRlci1yYWRpdXM6IDVweDsNCiAgICBwYWRkaW5nOiAxMHB4Ow0KICAgIGJveC1zaGFkb3c6IDAgMCAxMHB4IHJnYmEoMCwgMCwgMCwgMC4zKTsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQoNCiAgICAvLyDorqHliJLnrqHnkIbnroDljJbmoLflvI8NCiAgICAmLnBsYW4tbWFuYWdlbWVudC1jYXJkIHsNCiAgICAgIC5wbGFuLWdyaWQgew0KICAgICAgICBkaXNwbGF5OiBncmlkOw0KICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnI7DQogICAgICAgIGdhcDogNHB4OyAvKiDnvKnnn63nvZHmoLzpl7Tot53ku444cHjliLA0cHggKi8NCiAgICAgICAgcGFkZGluZzogNXB4IDA7IC8qIOe8qeefreS4iuS4i+WGhei+uei3neS7jjEwcHjliLA1cHggKi8NCg0KICAgICAgICAucGxhbi1pdGVtIHsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgcGFkZGluZzogNnB4IDM1cHg7IC8qIOe8qeefreWGhei+uei3neS7jjhweOWIsDRweCA2cHggKi8NCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7DQogICAgICAgICAgdHJhbnNpdGlvbjogYmFja2dyb3VuZCAwLjJzIGVhc2U7DQoNCiAgICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMTg2LCAyNTUsIDAuMSk7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLnBsYW4taWNvbiB7DQogICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICBjb2xvcjogIzAwQkFGRjsNCiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNnB4OyAvKiDnvKnnn63lj7Povrnot53ku444cHjliLA2cHggKi8NCiAgICAgICAgICAgIHdpZHRoOiAxOHB4OyAvKiDnvKnnn63lrr3luqbku44yMHB45YiwMThweCAqLw0KICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5wbGFuLXRleHQgew0KICAgICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICAgIG1pbi13aWR0aDogMDsNCg0KICAgICAgICAgICAgLnBsYW4tdmFsdWUgew0KICAgICAgICAgICAgICBjb2xvcjogI2ZmZjsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMTsgLyog57yp55+t6KGM6auY5LuOMS4y5YiwMS4xICovDQogICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDFweDsgLyog57yp55+t5LiL6L656Led5LuOMnB45YiwMXB4ICovDQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC5wbGFuLWxhYmVsIHsNCiAgICAgICAgICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC44KTsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxMXB4Ow0KICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMTsgLyog57yp55+t6KGM6auY5LuOMS4y5YiwMS4xICovDQogICAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogICAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCg0KICAgIC8vIOiuoeWIkuaJp+ihjOeKtuaAgeagt+W8jw0KICAgICYucGxhbi1leGVjdXRpb24tY2FyZCB7DQogICAgICAucGxhbi1leGVjdXRpb24tZ3JpZCB7DQogICAgICAgIGRpc3BsYXk6IGdyaWQ7DQogICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDQsIDFmcik7DQogICAgICAgIGdhcDogMTJweDsNCiAgICAgICAgcGFkZGluZzogMnB4IDA7DQoNCiAgICAgICAgLmV4ZWN1dGlvbi1pdGVtIHsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgcGFkZGluZzogMTBweDsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAxODYsIDI1NSwgMC4xKTsNCiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDE4NiwgMjU1LCAwLjMpOw0KICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7DQoNCiAgICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMTg2LCAyNTUsIDAuMik7DQogICAgICAgICAgICBib3JkZXItY29sb3I6IHJnYmEoMCwgMTg2LCAyNTUsIDAuNSk7DQogICAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7DQogICAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMTg2LCAyNTUsIDAuMyk7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLmV4ZWN1dGlvbi1pY29uIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgICAgIGNvbG9yOiAjMDBCQUZGOw0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICAgICAgICAgICAgd2lkdGg6IDIycHg7DQogICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgICAgICBmbGV4LXNocmluazogMDsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAuZXhlY3V0aW9uLXRleHQgew0KICAgICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICAgIG1pbi13aWR0aDogMDsNCg0KICAgICAgICAgICAgLmV4ZWN1dGlvbi12YWx1ZSB7DQogICAgICAgICAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMS4yOw0KICAgICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAzcHg7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC5leGVjdXRpb24tbGFiZWwgew0KICAgICAgICAgICAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjgpOw0KICAgICAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjI7DQogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQoNCiAgICBvdmVyZmxvdzogaGlkZGVuOyAvLyDmgaLlpI1oaWRkZW7vvIzpmLLmraLph43lj6ANCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQoNCiAgICAmOjpiZWZvcmUgew0KICAgICAgY29udGVudDogJyc7DQogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICB0b3A6IDA7DQogICAgICBsZWZ0OiAwOw0KICAgICAgcmlnaHQ6IDA7DQogICAgICBoZWlnaHQ6IDJweDsNCiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgcmdiYSgwLDIxMiwyNTUsMCkgMCUsIHJnYmEoMCwyMTIsMjU1LDEpIDUwJSwgcmdiYSgwLDIxMiwyNTUsMCkgMTAwJSk7DQogICAgfQ0KICB9DQoNCiAgLmNsaWNrYWJsZS1jYXJkIHsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCg0KICAgICY6aG92ZXIgew0KICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOw0KICAgICAgYm94LXNoYWRvdzogMCA1cHggMjBweCByZ2JhKDAsIDIxMiwgMjU1LCAwLjMpOw0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgzMywgMTAsIDU2LCAwLjkpOw0KICAgIH0NCg0KICAgICY6YWN0aXZlIHsNCiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsNCiAgICB9DQogIH0NCg0KICAuY2FyZC10aXRsZSB7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDVweDsNCiAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGNvbG9yOiAjZmZmOw0KICB9DQoNCiAgLmludmVudG9yeS10b3RhbCB7DQogICAgZm9udC1zaXplOiAxMnB4Ow0KICAgIGNvbG9yOiAjMDBkNGZmOw0KICAgIGZvbnQtd2VpZ2h0OiBub3JtYWw7DQogICAgYmFja2dyb3VuZDogcmdiYSgwLCAyMTIsIDI1NSwgMC4xKTsNCiAgICBwYWRkaW5nOiAycHggOHB4Ow0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjMpOw0KICB9DQoNCiAgLmNoYXJ0LWZpbHRlci1kcm9wZG93bi1jb250YWluZXIgew0KICAgIHotaW5kZXg6IDEwOw0KICB9DQoNCiAgLmNoYXJ0LWZpbHRlci1kcm9wZG93bi1jb250YWluZXIgc2VsZWN0IHsNCiAgICBwYWRkaW5nOiA0cHggOHB4Ow0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDEzOCwgNDMsIDIyNiwgMC43KTsNCiAgICBjb2xvcjogI2ZmZjsNCiAgICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDAsIDIxMiwgMjU1LCAwLjMpOw0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogIH0NCg0KICAuY2hhcnQtZmlsdGVyLWRyb3Bkb3duLWNvbnRhaW5lciBzZWxlY3Q6aG92ZXIgew0KICAgIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMTM4LCA0MywgMjI2LCAwLjkpOw0KICAgIGJvcmRlci1jb2xvcjogcmdiYSgwLCAyMTIsIDI1NSwgMC42KTsNCiAgfQ0KDQogIC5jaGFydCB7DQogICAgZmxleDogMTsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBtaW4taGVpZ2h0OiAxNTBweDsNCiAgfQ0KDQogIC5iaWctbnVtYmVyLWNvbnRhaW5lciB7DQogICAgZmxleDogMTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCg0KICAgIC5iaWctbnVtYmVyIHsNCiAgICAgIGNvbG9yOiAjMDBCQUZGOw0KICAgICAgZm9udC1zaXplOiAzNnB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgIH0NCg0KICAgIC51bml0LXRleHQgew0KICAgICAgY29sb3I6ICNmZmY7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgfQ0KICB9DQoNCiAgLnByb2dyZXNzLWNvbnRhaW5lciB7DQogICAgZmxleDogMTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIH0NCg0KICAvLyDmvI/mlpfmlbDmja7moLflvI8NCiAgLmZ1bm5lbC1kYXRhIHsNCiAgICBmbGV4OiAxOw0KICAgIHBhZGRpbmc6IDEwcHggMDsNCg0KICAgIC5mdW5uZWwtaXRlbSB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIHBhZGRpbmc6IDhweCAwOw0KICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHJnYmEoMCwgMTg2LCAyNTUsIDAuMik7DQoNCiAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7DQogICAgICB9DQoNCiAgICAgIC5mdW5uZWwtbGFiZWwgew0KICAgICAgICBjb2xvcjogI2ZmZjsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgfQ0KDQogICAgICAuZnVubmVsLXZhbHVlIHsNCiAgICAgICAgY29sb3I6ICMwMEJBRkY7DQogICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g6aKE6K2m5L+h5oGv5qC35byP77yI5a6M5YWo54Wn5oqE6K6h5YiS566h55CG5qC35byP77yM5Y+q5pS56aKc6Imy5Li657qi6Imy77yJDQogIC53YXJuaW5nLWFuYWx5c2lzIHsNCiAgICBmbGV4OiAxOw0KICAgIHBhZGRpbmc6IDEwcHggMDsNCg0KICAgIC53YXJuaW5nLWl0ZW0gew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KDQogICAgICAud2FybmluZy1uYW1lIHsNCiAgICAgICAgY29sb3I6ICNmZmY7DQogICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgd2lkdGg6IDgwcHg7DQogICAgICAgIGZsZXgtc2hyaW5rOiAwOw0KICAgICAgfQ0KDQogICAgICAud2FybmluZy1iYXIgew0KICAgICAgICBmbGV4OiAxOw0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBtYXJnaW4tbGVmdDogMTBweDsNCg0KICAgICAgICAuYmFyLWJnIHsNCiAgICAgICAgICBmbGV4OiAxOw0KICAgICAgICAgIGhlaWdodDogOHB4Ow0KICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCA4NywgODcsIDAuMik7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KDQogICAgICAgICAgLmJhci1maWxsIHsNCiAgICAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgaHNsKDAsIDg1JSwgNjklKSwgI2YzMTgwNCk7DQogICAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgICB0cmFuc2l0aW9uOiB3aWR0aCAwLjNzIGVhc2U7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLndhcm5pbmctdmFsdWUgew0KICAgICAgICAgIGNvbG9yOiAjRkY1NzU3Ow0KICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICB3aWR0aDogNjBweDsNCiAgICAgICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC8vIOi2i+WKv+e7n+iuoeagt+W8jw0KICAudHJlbmQtc3RhdHMgew0KICAgIG1hcmdpbi1ib3R0b206IDVweDsNCiAgICBmbGV4LXNocmluazogMDsNCg0KICAgIC50cmVuZC1pdGVtIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgcGFkZGluZzogM3B4IDA7DQoNCiAgICAgIC50cmVuZC1sYWJlbCB7DQogICAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgICBmb250LXNpemU6IDExcHg7DQogICAgICB9DQoNCiAgICAgIC50cmVuZC12YWx1ZSB7DQogICAgICAgIGNvbG9yOiAjM0RFN0M5Ow0KICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC8vIOiuoeWIkueuoeeQhuagt+W8jw0KICAucHJvZHVjdC1hbmFseXNpcyB7DQogICAgZmxleDogMTsNCiAgICBwYWRkaW5nOiAxMHB4IDA7DQoNCiAgICAucHJvZHVjdC1pdGVtIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgbWFyZ2luLWJvdHRvbTogMTVweDsNCg0KICAgICAgLnByb2R1Y3QtbmFtZSB7DQogICAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgIHdpZHRoOiA4MHB4Ow0KICAgICAgICBmbGV4LXNocmluazogMDsNCiAgICAgIH0NCg0KICAgICAgLnByb2R1Y3QtYmFyIHsNCiAgICAgICAgZmxleDogMTsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7DQoNCiAgICAgICAgLmJhci1iZyB7DQogICAgICAgICAgZmxleDogMTsNCiAgICAgICAgICBoZWlnaHQ6IDhweDsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDE4NiwgMjU1LCAwLjIpOw0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICAgIG1hcmdpbi1yaWdodDogMTBweDsNCg0KICAgICAgICAgIC5iYXItZmlsbCB7DQogICAgICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICMwMEJBRkYsICMzREU3QzkpOw0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICAgICAgdHJhbnNpdGlvbjogd2lkdGggMC4zcyBlYXNlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC5wcm9kdWN0LXZhbHVlIHsNCiAgICAgICAgICBjb2xvcjogIzAwQkFGRjsNCiAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgICAgd2lkdGg6IDYwcHg7DQogICAgICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAvLyDkvpvlupTllYblnIblvaLmmL7npLrmoLflvI8NCiAgLnN1cHBsaWVyLWNpcmNsZXMgew0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICBoZWlnaHQ6IDEwMCU7DQogICAgcGFkZGluZzogMjBweDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQoNCiAgICAuY2lyY2xlLWl0ZW0gew0KICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgICAuY2lyY2xlIHsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlOw0KICAgICAgICBib3JkZXI6IDJweCBzb2xpZDsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDhweDsNCiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCg0KICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpOw0KICAgICAgICAgIGZpbHRlcjogYnJpZ2h0bmVzcygxLjIpOw0KICAgICAgICB9DQoNCiAgICAgICAgJi5jbGlja2FibGUgew0KICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCg0KICAgICAgICAgICY6aG92ZXIgew0KICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjEpOw0KICAgICAgICAgICAgYm94LXNoYWRvdzogMCAwIDIwcHggcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC5jaXJjbGUtbnVtYmVyIHsNCiAgICAgICAgICBjb2xvcjogI2ZmZjsNCiAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMjsNCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAuY2lyY2xlLWxhYmVsIHsNCiAgICAgICAgY29sb3I6ICNmZmY7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDEuMjsNCiAgICAgICAgd29yZC13cmFwOiBicmVhay13b3JkOw0KICAgICAgfQ0KDQogICAgICAvLyDmma7pgJrlnIblvaLmoLflvI/vvIjpmo/mnLrkvY3nva7vvIkNCiAgICAgICYucmFuZG9tLXBvc2l0aW9uIHsNCiAgICAgICAgLmNpcmNsZSB7DQogICAgICAgICAgd2lkdGg6IDYwcHg7DQogICAgICAgICAgaGVpZ2h0OiA2MHB4Ow0KDQogICAgICAgICAgLmNpcmNsZS1udW1iZXIgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC5jaXJjbGUtbGFiZWwgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTBweDsNCiAgICAgICAgICBtYXgtd2lkdGg6IDYwcHg7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g5Lit5b+D5ZyG5b2i5qC35byP77yI6ICD5qC45oOF5Ya177yJDQogICAgICAmLmNlbnRlci1jaXJjbGUgew0KICAgICAgICAuY2lyY2xlIHsNCiAgICAgICAgICB3aWR0aDogMTIwcHg7DQogICAgICAgICAgaGVpZ2h0OiAxMjBweDsNCg0KICAgICAgICAgIC5jaXJjbGUtbnVtYmVyIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAuY2lyY2xlLWxhYmVsIHsNCiAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgbWF4LXdpZHRoOiAxMjBweDsNCiAgICAgICAgICBtYXJnaW4tdG9wOiA1cHg7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g5Lit5b+D5L2N572uDQogICAgICAmLmNlbnRlciB7DQogICAgICAgIHRvcDogNTAlOw0KICAgICAgICBsZWZ0OiA1MCU7DQogICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIC01MCUpOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC8vIOeugOWNleaYvuekuuagt+W8jw0KICAuc2ltcGxlLWRpc3BsYXkgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIGhlaWdodDogMTAwJTsNCiAgICBwYWRkaW5nOiAyMHB4IDA7DQoNCiAgICAuZGlzcGxheS1udW1iZXIgew0KICAgICAgY29sb3I6ICNGRjhDMDA7DQogICAgICBmb250LXNpemU6IDM2cHg7DQogICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgIGxpbmUtaGVpZ2h0OiAxOw0KICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICB9DQoNCiAgICAuZGlzcGxheS1sYWJlbCB7DQogICAgICBjb2xvcjogI2ZmZjsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgfQ0KICB9DQoNCiAgLy8g5L6b5pa5566h55CG57uf6K6h5qC35byPDQogIC5zdXBwbGllci1zdGF0cyB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIHBhZGRpbmc6IDNweCA1cHg7DQogICAgZ2FwOiA1cHg7DQoNCiAgICAuc3VwcGxpZXItc3RhdC1pdGVtIHsNCiAgICAgIGZsZXg6IDE7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgIHBhZGRpbmc6IDJweDsNCg0KICAgICAgLnN0YXQtbnVtYmVyIHsNCiAgICAgICAgY29sb3I6ICNGRjhDMDA7DQogICAgICAgIGZvbnQtc2l6ZTogMjhweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAxOw0KICAgICAgICBtYXJnaW4tYm90dG9tOiAzcHg7DQogICAgICB9DQoNCiAgICAgIC5zdGF0LWxhYmVsIHsNCiAgICAgICAgY29sb3I6ICNmZmY7DQogICAgICAgIGZvbnQtc2l6ZTogMTVweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC8vIOS+m+aWueeuoeeQhuWNoeeJh+agt+W8jw0KICAuc3VwcGxpZXItbWFuYWdlbWVudC1jYXJkIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgaGVpZ2h0OiAxMDAlOw0KDQogICAgLmNhcmQtdGl0bGUgew0KICAgICAgZmxleC1zaHJpbms6IDA7DQogICAgICBtYXJnaW4tYm90dG9tOiAtMjVweDsgLyog6L+b5LiA5q2l5YeP5bCP5LiL6L656LedICovDQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgfQ0KDQogICAgLmNoYXJ0IHsNCiAgICAgIGZsZXgtc2hyaW5rOiAwOw0KICAgICAgbWFyZ2luLWJvdHRvbTogMHB4Ow0KICAgIH0NCg0KICAgIC5zdXBwbGllci1zdGF0cyB7DQogICAgICBmbGV4OiAxOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICB9DQogIH0NCg0KICAvLyDpq5jpopHnianotYTmqKHlnZfmoLflvI8NCiAgLmhpZ2gtZnJlcXVlbmN5LWNvbnRlbnQgew0KICAgIGhlaWdodDogMTAwJTsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIH0NCg0KICAuaGlnaC1mcmVxdWVuY3ktbWF0ZXJpYWxzIHsNCiAgICBmbGV4OiAyICFpbXBvcnRhbnQ7DQogICAgbWFyZ2luLWJvdHRvbTogMHB4Ow0KICB9DQoNCiAgLnByaWNlLXRyZW5kLXNlY3Rpb24gew0KICAgIGZsZXg6IDggIWltcG9ydGFudDsNCiAgICBtaW4taGVpZ2h0OiAxMjBweDsgLyog5YeP5bCP5pyA5bCP6auY5bqmICovDQogICAgbWFyZ2luLWJvdHRvbTogMHB4Ow0KICAgIG1hcmdpbi10b3A6IDVweDsgLyog5YeP5bCP5LiK6L656LedICovDQogIH0NCg0KICAuc2VjdGlvbi10aXRsZSB7DQogICAgZm9udC1zaXplOiAxMnB4Ow0KICAgIGNvbG9yOiAjMDBCQUZGOw0KICAgIG1hcmdpbjogMCAwIDRweCAwOyAvKiDlh4/lsI/kuIvovrnot53ku444cHjliLA0cHggKi8NCiAgICBmb250LXdlaWdodDogNTAwOw0KICB9DQoNCiAgLm1hdGVyaWFsLWNsb3VkIHsNCiAgICBoZWlnaHQ6IDE0MHB4Ow0KICAgIHdpZHRoOiAxMDAlOw0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICBvdmVyZmxvdzogaGlkZGVuOw0KICB9DQoNCiAgLmNoYXJ0LXBsYWNlaG9sZGVyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgaGVpZ2h0OiAxMDAlOw0KICAgIGNvbG9yOiAjNjY2Ow0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgfQ0KDQogIC5tYXRlcmlhbC10b29sdGlwIHsNCiAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgYmFja2dyb3VuZDogcmdiYSgwLDAsMCwwLjgpOw0KICAgIGNvbG9yOiAjZmZmOw0KICAgIHBhZGRpbmc6IDJweCA2cHg7DQogICAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICAgIGZvbnQtc2l6ZTogMTBweDsNCiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgIHotaW5kZXg6IDEwMDA7DQogICAgcG9pbnRlci1ldmVudHM6IG5vbmU7DQogIH0NCg0KICAubWluaS1jaGFydCB7DQogICAgaGVpZ2h0OiAxMDBweDsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KDQogIC8vIOa7muWKqOadoeagt+W8jw0KICAubWF0ZXJpYWwtbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICAgIHdpZHRoOiA0cHg7DQogIH0NCg0KICAubWF0ZXJpYWwtbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsNCiAgICBib3JkZXItcmFkaXVzOiAycHg7DQogIH0NCg0KICAubWF0ZXJpYWwtbGlzdDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgew0KICAgIGJhY2tncm91bmQ6IHJnYmEoMCwgMTg2LCAyNTUsIDAuNSk7DQogICAgYm9yZGVyLXJhZGl1czogMnB4Ow0KICB9DQoNCiAgLm1hdGVyaWFsLWxpc3Q6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOmhvdmVyIHsNCiAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDE4NiwgMjU1LCAwLjgpOw0KICB9DQoNCiAgLyog5ZON5bqU5byP5qC35byPIC0g6K6h5YiS5omn6KGM54q25oCB5qih5Z2XICovDQogIEBtZWRpYSAobWF4LXdpZHRoOiAxNDAwcHgpIHsNCiAgICAucGxhbi1leGVjdXRpb24tY2FyZCAucGxhbi1leGVjdXRpb24tZ3JpZCB7DQogICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgzLCAxZnIpOw0KICAgIH0NCiAgfQ0KDQogIEBtZWRpYSAobWF4LXdpZHRoOiAxMDAwcHgpIHsNCiAgICAucGxhbi1leGVjdXRpb24tY2FyZCAucGxhbi1leGVjdXRpb24tZ3JpZCB7DQogICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgyLCAxZnIpOw0KICAgIH0NCiAgfQ0KDQogIEBtZWRpYSAobWF4LXdpZHRoOiA2MDBweCkgew0KICAgIC5wbGFuLWV4ZWN1dGlvbi1jYXJkIC5wbGFuLWV4ZWN1dGlvbi1ncmlkIHsNCiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyOw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuiEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/purchaseDashboardMain", "sourcesContent": ["<template>\r\n  <div class=\"purchase-dashboard-main\" :class=\"{ 'fullscreen-mode': isFullscreen }\">\r\n    <div class=\"dashboard-container\">\r\n      <!-- 头部标题 -->\r\n      <div class=\"dashboard-header\">\r\n        <h1>采购管理全景视图</h1>\r\n        <div class=\"header-controls\">\r\n          <div class=\"fullscreen-btn\" @click=\"toggleFullscreen\" :title=\"isFullscreen ? '退出全屏' : '进入全屏'\">\r\n            <i :class=\"isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'\"></i>\r\n          </div>\r\n          <div class=\"time-filter\">\r\n            <button\r\n              v-for=\"filter in timeFilters\"\r\n              :key=\"filter.id\"\r\n              :class=\"['time-filter-btn', { active: filter.id === activeFilter }]\"\r\n              @click=\"handleTimeFilterChange(filter.id, filter.value)\"\r\n            >\r\n              {{ filter.label }}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主要内容区域 -->\r\n      <div class=\"dashboard-content\">\r\n        <!-- 左侧面板 -->\r\n        <div class=\"left-panel\" :style=\"isFullscreen ? { width: '320px', minWidth: '320px', maxWidth: '320px', gap: '12px' } : {}\">\r\n          <!-- 左侧第一个：资金管理 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">资金管理</h2>\r\n            <div class=\"chart\" id=\"fundManagementChart\"></div>\r\n          </div>\r\n\r\n          <!-- 左侧第二个：供应商信息全景 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">供应商信息全景</h2>\r\n            <div class=\"supplier-circles\">\r\n              <!-- 中心圆形：考核情况 -->\r\n              <div class=\"circle-item center-circle center\" @click=\"goToSupplierPenalty\">\r\n                <div class=\"circle clickable\" :style=\"{ borderColor: centerData.color, backgroundColor: centerData.color }\">\r\n                  <div class=\"circle-number\">\r\n                    <div>金额: {{ centerData.amount }}</div>\r\n                    <div>次数: {{ centerData.count }}</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"circle-label\">{{ centerData.label }}</div>\r\n              </div>\r\n\r\n              <!-- 周围四个圆形 - 随机位置分布 -->\r\n              <div\r\n                class=\"circle-item random-position\"\r\n                v-for=\"item in supplierData\"\r\n                :key=\"item.id\"\r\n                :style=\"item.position\"\r\n              >\r\n                <div class=\"circle\" :style=\"{ borderColor: item.color, backgroundColor: item.color }\">\r\n                  <span class=\"circle-number\">{{ item.value }}</span>\r\n                </div>\r\n                <div class=\"circle-label\">{{ item.label }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 左侧第三个：合同管理（暂时隐藏） -->\r\n          <!-- <div class=\"card\">\r\n            <h2 class=\"card-title\">合同管理</h2>\r\n            <div id=\"contractChart\" class=\"chart\"></div>\r\n          </div> -->\r\n\r\n          <!-- 左侧第三个：单一来源 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">单一来源</h2>\r\n            <div id=\"supplierChart\" class=\"chart\"></div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 中间面板 -->\r\n        <div class=\"center-panel\" :style=\"isFullscreen ? { gap: '10px', flex: '1', minWidth: '400px' } : {}\">\r\n          <!-- 中间第一行 -->\r\n          <div class=\"center-row center-row-first\" :style=\"isFullscreen ? { maxHeight: 'none', flex: '1' } : {}\">\r\n            <!-- 中间第一个：计划管理 -->\r\n            <div class=\"card clickable-card plan-management-card\" @click=\"goToPlanDashboard\">\r\n              <h2 class=\"card-title\">计划管理</h2>\r\n              <div class=\"plan-grid\" :style=\"isFullscreen ? { gap: '8px', padding: '10px 0' } : {}\">\r\n                <div class=\"plan-item\" v-for=\"(item, index) in productAnalysisData\" :key=\"item.name\" :style=\"isFullscreen ? { padding: '10px 40px' } : {}\">\r\n                  <i :class=\"getPlanIcon(index)\" class=\"plan-icon\" :style=\"isFullscreen ? { fontSize: '18px', marginRight: '8px', width: '20px' } : {}\"></i>\r\n                  <div class=\"plan-text\">\r\n                    <div class=\"plan-value\" :style=\"isFullscreen ? { marginBottom: '4px', fontSize: '15px' } : {}\">{{ item.value }}</div>\r\n                    <div class=\"plan-label\" :style=\"isFullscreen ? { fontSize: '12px', lineHeight: '1.3' } : {}\">{{ item.name }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 中间第二个：库存管理 -->\r\n            <div class=\"card clickable-card\" @click=\"goToStockDashboard\">\r\n              <h2 class=\"card-title\">\r\n                <div style=\"display: flex; align-items: center; justify-content: space-between; width: 100%;\">\r\n                  <div style=\"display: flex; align-items: center; gap: 15px;\">\r\n                    <span>库存管理</span>\r\n                    <span class=\"inventory-total\">\r\n                      合计: {{ calculateCokingCoalTotal() }}万吨\r\n                    </span>\r\n                  </div>\r\n                  <div class=\"chart-filter-dropdown-container\">\r\n                    <select\r\n                      v-model=\"selectedCokingCoalType\"\r\n                      @change=\"handleCokingCoalTypeChange\"\r\n                      @click.stop\r\n                    >\r\n                      <option value=\"\">全部</option>\r\n                      <option value=\"矿料类\">矿料类</option>\r\n                      <option value=\"焦炭\">焦炭</option>\r\n                      <option value=\"煤焦类\">煤焦类</option>\r\n                      <option value=\"合金类\">合金类</option>\r\n                      <option value=\"辅助类/电极\">辅助类/电极</option>\r\n                    </select>\r\n                  </div>\r\n                </div>\r\n              </h2>\r\n              <div id=\"cokingCoalLineChart\" class=\"chart\"></div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 新增：中间插入行 - 计划执行状态 -->\r\n          <div class=\"center-row-full\">\r\n            <div class=\"card plan-execution-card\" :style=\"isFullscreen ? { maxHeight: 'none', minHeight: '80px' } : {}\">\r\n              <!-- <h2 class=\"card-title\">计划执行状态</h2> -->\r\n              <div class=\"plan-execution-grid\">\r\n                <div class=\"execution-item\" v-for=\"(item, index) in planExecutionData\" :key=\"item.name\">\r\n                  <i :class=\"getExecutionIcon(index)\" class=\"execution-icon\"></i>\r\n                  <div class=\"execution-text\">\r\n                    <div class=\"execution-value\">{{ item.value }}</div>\r\n                    <div class=\"execution-label\">{{ item.name }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 中间第二行 -->\r\n          <div class=\"center-row center-row-second\" :style=\"isFullscreen ? { maxHeight: 'none', flex: '1' } : {}\">\r\n            <!-- 中间第三个：供方管理 -->\r\n            <div class=\"card supplier-management-card\">\r\n              <h2 class=\"card-title\">供方管理</h2>\r\n              <!-- 上半部分：饼图 -->\r\n              <div id=\"supplierManagementChart\" class=\"chart\" style=\"height: 40px;\"></div>\r\n              <!-- 下半部分：三个数字显示区域 -->\r\n              <div class=\"supplier-stats\">\r\n                <div class=\"supplier-stat-item\">\r\n                  <div class=\"stat-number\">{{ supplierStats.admission }}</div>\r\n                  <div class=\"stat-label\">准入</div>\r\n                </div>\r\n                <div class=\"supplier-stat-item\">\r\n                  <div class=\"stat-number\">{{ supplierStats.elimination }}</div>\r\n                  <div class=\"stat-label\">淘汰</div>\r\n                </div>\r\n                <div class=\"supplier-stat-item\">\r\n                  <div class=\"stat-number\">{{ supplierStats.suspension }}</div>\r\n                  <div class=\"stat-label\">暂缓</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 中间第四个：高频物资 -->\r\n            <div class=\"card clickable-card\" @click=\"goToHighFrequencyDashboard\">\r\n              <h2 class=\"card-title\">高频物资</h2>\r\n              <div class=\"high-frequency-content\">\r\n                <!-- 上半部分：高频采购物料词云 -->\r\n                <div class=\"high-frequency-materials\">\r\n                  <h3 class=\"section-title\">高频采购物料 TOP10</h3>\r\n                  <div id=\"highFrequencyMaterialCloud\" class=\"material-cloud\"></div>\r\n                </div>\r\n\r\n                <!-- 下半部分：物料采购价格和采购量趋势图 -->\r\n                <div class=\"price-trend-section\">\r\n                  <h3 class=\"section-title\">PB块价格及采购量趋势</h3>\r\n                  <div id=\"highFrequencyPriceTrendChart\" class=\"mini-chart\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n           \r\n          </div>\r\n        </div>\r\n\r\n        <!-- 右侧面板 -->\r\n        <div class=\"right-panel\" :style=\"isFullscreen ? { width: '320px', minWidth: '320px', maxWidth: '320px', gap: '12px' } : {}\">\r\n          <!-- 右侧第一个：预警信息 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">预警信息</h2>\r\n            <div class=\"warning-analysis\">\r\n              <div class=\"warning-item\">\r\n                <span class=\"warning-name\">证书过期</span>\r\n                <div class=\"warning-bar\">\r\n                  <div class=\"bar-bg\">\r\n                    <div class=\"bar-fill\" :style=\"{ width: getWarningPercentage(warningInfo.certificateExpiry) + '%' }\"></div>\r\n                  </div>\r\n                  <span class=\"warning-value\">{{ warningInfo.certificateExpiry }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"warning-item\">\r\n                <span class=\"warning-name\">合同过期</span>\r\n                <div class=\"warning-bar\">\r\n                  <div class=\"bar-bg\">\r\n                    <div class=\"bar-fill\" :style=\"{ width: getWarningPercentage(warningInfo.contractExpiry) + '%' }\"></div>\r\n                  </div>\r\n                  <span class=\"warning-value\">{{ warningInfo.contractExpiry }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 右侧第二个：质量异议管理 -->\r\n          <div class=\"card\">\r\n            <h2 class=\"card-title\">质量异议管理</h2>\r\n            <div class=\"simple-display\">\r\n              <div class=\"display-number\">{{ qualityIssueCount }}</div>\r\n              <div class=\"display-label\">质量异议总数</div>\r\n            </div>\r\n          </div>\r\n\r\n           <!-- 右侧第三个：异常管理 -->\r\n            <div class=\"card\">\r\n              <h2 class=\"card-title\">异常管理</h2>\r\n              <div class=\"funnel-data\">                \r\n              <div class=\"funnel-item\">\r\n                <span class=\"funnel-label\">到货完成度</span>\r\n                <span class=\"funnel-value\">{{ (purchaseStats.arriveRate || 0) + '%' }}</span>\r\n              </div>\r\n              <div class=\"funnel-item\">\r\n                <span class=\"funnel-label\">采购职责不符合数量</span>\r\n                <span class=\"funnel-value\">9000</span>\r\n              </div>\r\n              <div class=\"funnel-item\">\r\n                <span class=\"funnel-label\">计划被驳回数量</span>\r\n                <span class=\"funnel-value\">9000</span>\r\n              </div>\r\n            </div>\r\n            </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport screenfull from 'screenfull'\r\nimport {\r\n  getDashboardData,\r\n  getPersonalConsumption,\r\n  getPurchaseAnalysis,\r\n  getProductAnalysis,\r\n  getMapData,\r\n  getRealTimeStats,\r\n  getSalesFunnel,\r\n  getPurchaseTrend,\r\n  getSupplierAnalysis,\r\n  getAmtManage\r\n} from '@/api/purchaseDashboardMain'\r\nimport {\r\n  showKeyIndicators,\r\n  showHighFrequencyMaterialList,\r\n  getPurchasePriceAndStore,\r\n  showCokingCoalAmount\r\n} from '@/api/purchaseDashboard/purchaseDashboard'\r\n\r\nexport default {\r\n  name: 'PurchaseDashboardMain',\r\n  data() {\r\n    return {\r\n      // 时间过滤器选项\r\n      timeFilters: [\r\n        { id: 'filter-3m', label: '近三个月', value: 1 },\r\n        { id: 'filter-6m', label: '近六个月', value: 2 },\r\n        { id: 'filter-1y', label: '近一年', value: 3 }\r\n      ],\r\n      activeFilter: 'filter-1y',\r\n      currentDimensionType: 3,\r\n\r\n      // 图表实例\r\n      charts: {},\r\n      // 窗口大小变化定时器\r\n      resizeTimer: null,\r\n\r\n      // 高频物资相关数据\r\n      highFrequencyMaterialList: [],\r\n      priceAndStoreData: null,\r\n      selectedCodeType: 'ALL',\r\n      selectedItemType: 'CLASS3',\r\n      selectedMaterial: 'PB块',\r\n\r\n      // 矿焦煤库存相关数据\r\n      cokingCoalInventoryData: [],\r\n      selectedCokingCoalType: '', // 选中的矿焦煤类型，默认为空（全部）\r\n\r\n      // 实时数据\r\n      realTimeStats: {\r\n        sales: '343567',\r\n        orders: '734245'\r\n      },\r\n      // 计划管理数据\r\n      productAnalysisData: [\r\n        { name: '计划总条数', value: '0', percentage: 0 },\r\n        { name: '审核驳回', value: '0%', percentage: 0 },\r\n        { name: '商务部驳回', value: '0%', percentage: 0 },\r\n        { name: '订单至入库平均天数', value: '30000', percentage: 30 },\r\n        { name: '入库至领用平均天数', value: '30000', percentage: 30 },\r\n        { name: '接收至挂单平均天数', value: '40000', percentage: 40 },\r\n        { name: '超期未入库数', value: '40000', percentage: 40 },\r\n        { name: '超期未领用数', value: '40000', percentage: 40 }\r\n\r\n      ],\r\n      // 计划执行状态数据\r\n      planExecutionData: [\r\n        { name: '计划完成率', value: '85%' },\r\n        { name: '在途订单数', value: '1,234' },\r\n        { name: '待审核计划', value: '56' },\r\n        { name: '紧急采购', value: '12' },\r\n        // { name: '供应商响应率', value: '92%' },\r\n        // { name: '库存周转率', value: '3.2' },\r\n        // { name: '采购成本节约', value: '8.5%' },\r\n        // { name: '质量合格率', value: '98.7%' }\r\n      ],\r\n      // 底部统计数据\r\n      bottomStats: [\r\n        { label: '供应商信息全景', value: '34554' },\r\n        { label: '订单量', value: '34554' },\r\n        { label: '客户数', value: '34554' }\r\n      ],\r\n      // 水位图配置\r\n      waterLevelConfig: {\r\n        data: [50],\r\n        shape: 'circle',\r\n        waveNum: 3,\r\n        waveHeight: 40,\r\n        waveOpacity: 0.4,\r\n        colors: ['#00BAFF', '#3DE7C9']\r\n      },\r\n      // 供应商数据（周围四个圆）\r\n      supplierData: [\r\n        { id: 1, value: '8,092', label: '参标次数', color: '#FF6B6B', position: { top: '10%', left: '10%' } },\r\n        { id: 2, value: '1,245', label: '中标次数', color: '#4ECDC4', position: { top: '10%', right: '10%' } },\r\n        { id: 3, value: '89', label: '质量异议次数', color: '#45B7D1', position: { bottom: '10%', left: '10%' } },\r\n        { id: 4, value: '156', label: '合作年限', color: '#96CEB4', position: { bottom: '10%', right: '10%' } }\r\n      ],\r\n      // 中心圆数据（考核情况）\r\n      centerData: {\r\n        label: '考核情况',\r\n        amount: '567万',\r\n        count: '23次',\r\n        color: '#00BAFF'\r\n      },\r\n      // 质量异议总数\r\n      qualityIssueCount: '156',\r\n      // 供方管理统计数据\r\n      supplierStats: {\r\n        admission: '1,245',    // 准入\r\n        elimination: '89',     // 淘汰\r\n        suspension: '156'      // 暂缓\r\n      },\r\n      // 预警信息数据\r\n      warningInfo: {\r\n        certificateExpiry: 0, // 证书过期数量（供应商风险提醒）\r\n        contractExpiry: 0     // 合同过期数量（合同到期提醒）\r\n      },\r\n      // 采购关键指标数据\r\n      purchaseStats: {\r\n        arriveRate: 0  // 到货完成度\r\n      },\r\n      // 资金管理数据\r\n      fundManagement: {\r\n        nextMonth: '0',    // 下月拟入库总金额\r\n        twoMonthsLater: '0', // 2月后拟入库总金额\r\n        threeMonthsLater: '0' // 3月后拟入库总金额\r\n      },\r\n      // 合同管理数据\r\n      contractData: [\r\n        { name: '原材料', count: 12095282 },\r\n        { name: '辅耐材', count: 8340154 },\r\n        { name: '材料类', count: 33344517 },\r\n        { name: '通用备件', count: 76374451 },\r\n        { name: '专用备件', count: 4353921 },\r\n        { name: '办公', count: 23515 }\r\n      ]\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    isFullscreen() {\r\n      return this.$store.state.app.isFullscreenMode\r\n    }\r\n  },\r\n\r\n  mounted() {\r\n    this.initFullscreenListener()\r\n    this.initDashboard()\r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.handleResize)\r\n  },\r\n  beforeDestroy() {\r\n    // 移除事件监听\r\n    window.removeEventListener('resize', this.handleResize)\r\n    // 销毁所有图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      if (chart && chart.dispose) {\r\n        chart.dispose()\r\n      }\r\n    })\r\n    // 移除全屏监听器\r\n    this.removeFullscreenListener()\r\n    // 确保退出全屏模式\r\n    this.$store.dispatch('app/setFullscreenMode', false)\r\n  },\r\n  methods: {\r\n    async initDashboard() {\r\n      try {\r\n        await this.loadData()\r\n        this.$nextTick(() => {\r\n          this.initCharts()\r\n        })\r\n      } catch (error) {\r\n        console.error('初始化驾驶舱失败:', error)\r\n      }\r\n    },\r\n    \r\n    async loadData() {\r\n      try {\r\n        // 并行获取预警信息数据和资金管理数据\r\n        await Promise.all([\r\n          this.fetchWarningData(),\r\n          this.fetchFundManagementData()\r\n        ])\r\n\r\n        // 这里可以并行加载其他数据\r\n        // const [dashboardData, personalData, purchaseData] = await Promise.all([\r\n        //   getDashboardData(),\r\n        //   getPersonalConsumption(),\r\n        //   getPurchaseAnalysis()\r\n        // ])\r\n\r\n        console.log('数据加载完成')\r\n      } catch (error) {\r\n        console.error('加载数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 获取资金管理数据\r\n    async fetchFundManagementData() {\r\n      try {\r\n        const response = await getAmtManage()\r\n\r\n        if (response && response.data && Array.isArray(response.data)) {\r\n          const data = response.data\r\n\r\n          // 根据reserve1字段找到对应的数据\r\n          const nextMonthData = data.find(item => item.reserve1 === '01')\r\n          const twoMonthsData = data.find(item => item.reserve1 === '02')\r\n          const threeMonthsData = data.find(item => item.reserve1 === '03')\r\n\r\n          // 更新资金管理数据\r\n          this.fundManagement.nextMonth = nextMonthData ? (nextMonthData.reserve4 || '0') : '0'\r\n          this.fundManagement.twoMonthsLater = twoMonthsData ? (twoMonthsData.reserve4 || '0') : '0'\r\n          this.fundManagement.threeMonthsLater = threeMonthsData ? (threeMonthsData.reserve4 || '0') : '0'\r\n\r\n          // 更新图表\r\n          this.updateFundManagementChart()\r\n\r\n          console.log('资金管理数据获取成功:', this.fundManagement)\r\n        } else {\r\n          console.error('资金管理数据格式不正确:', response)\r\n          this.resetFundManagementData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取资金管理数据失败:', error)\r\n        this.resetFundManagementData()\r\n      }\r\n    },\r\n\r\n    // 重置资金管理数据为默认值\r\n    resetFundManagementData() {\r\n      this.fundManagement.nextMonth = '0'\r\n      this.fundManagement.twoMonthsLater = '0'\r\n      this.fundManagement.threeMonthsLater = '0'\r\n      // 更新图表\r\n      this.updateFundManagementChart()\r\n    },\r\n\r\n    // 获取预警信息和计划管理数据\r\n    async fetchWarningData() {\r\n      try {\r\n        // 调用采购全景看板的关键指标接口\r\n        const response = await showKeyIndicators({ dimensionType: this.currentDimensionType })\r\n\r\n        if (response && response.data) {\r\n          const data = response.data\r\n\r\n          // 更新预警信息数据\r\n          // 证书过期数据来自供应商风险提醒\r\n          this.warningInfo.certificateExpiry = data.suppRiskNum || 0\r\n          // 合同过期数据来自合同到期提醒\r\n          this.warningInfo.contractExpiry = data.bpoExpireNum || 0\r\n\r\n          // 更新计划管理数据\r\n          this.updatePlanManagementData(data)\r\n\r\n          // 更新计划执行状态数据\r\n          this.updatePlanExecutionData(data)\r\n\r\n          // 更新采购关键指标数据\r\n          this.purchaseStats.arriveRate = data.arriveRate || 0\r\n\r\n          console.log('预警信息和计划管理数据获取成功:', {\r\n            warningInfo: this.warningInfo,\r\n            planData: {\r\n              planTotalNum: data.planTotalNum,\r\n              rejectNum1: data.rejectNum1,\r\n              rejectNum2: data.rejectNum2\r\n            },\r\n            purchaseStats: this.purchaseStats\r\n          })\r\n        } else {\r\n          console.error('数据获取失败:', response)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取数据失败:', error)\r\n        // 使用默认值\r\n        this.warningInfo.certificateExpiry = 0\r\n        this.warningInfo.contractExpiry = 0\r\n        this.purchaseStats.arriveRate = 0\r\n        this.resetPlanManagementData()\r\n      }\r\n    },\r\n\r\n    // 更新计划管理数据\r\n    updatePlanManagementData(data) {\r\n      // 计划总条数\r\n      if (data.planTotalNum !== undefined) {\r\n        this.productAnalysisData[0].value = data.planTotalNum.toString()\r\n        this.productAnalysisData[0].percentage = Math.min(100, Math.max(0, (data.planTotalNum / 100000) * 100))\r\n      }\r\n\r\n      // 审核驳回（百分比）\r\n      if (data.rejectNum1 !== undefined) {\r\n        this.productAnalysisData[1].value = data.rejectNum1 + '%'\r\n        this.productAnalysisData[1].percentage = Math.min(100, Math.max(0, data.rejectNum1))\r\n      }\r\n\r\n      // 商务部驳回（百分比）\r\n      if (data.rejectNum2 !== undefined) {\r\n        this.productAnalysisData[2].value = data.rejectNum2 + '%'\r\n        this.productAnalysisData[2].percentage = Math.min(100, Math.max(0, data.rejectNum2))\r\n      }\r\n\r\n      // 订单至入库平均天数（绑定到reserve4字段）\r\n      if (data.reserve4 !== undefined) {\r\n        this.productAnalysisData[3].value = data.reserve4.toString() \r\n        this.productAnalysisData[3].percentage = Math.min(100, Math.max(0, (data.reserve4 / 30) * 100)) // 假设30天为100%\r\n      }\r\n    },\r\n\r\n    // 重置计划管理数据为默认值\r\n    resetPlanManagementData() {\r\n      this.productAnalysisData[0].value = '0'\r\n      this.productAnalysisData[0].percentage = 0\r\n      this.productAnalysisData[1].value = '0%'\r\n      this.productAnalysisData[1].percentage = 0\r\n      this.productAnalysisData[2].value = '0%'\r\n      this.productAnalysisData[2].percentage = 0\r\n      this.productAnalysisData[3].value = '0天'\r\n      this.productAnalysisData[3].percentage = 0\r\n    },\r\n\r\n    // 更新计划执行状态数据\r\n    updatePlanExecutionData(data) {\r\n      if (data) {\r\n        // 计划完成率\r\n        if (data.planCompletionRate !== undefined) {\r\n          this.planExecutionData[0].value = data.planCompletionRate + '%'\r\n        }\r\n        // 在途订单数\r\n        if (data.inTransitOrderNum !== undefined) {\r\n          this.planExecutionData[1].value = this.formatNumber(data.inTransitOrderNum)\r\n        }\r\n        // 待审核计划\r\n        if (data.pendingAuditNum !== undefined) {\r\n          this.planExecutionData[2].value = data.pendingAuditNum.toString()\r\n        }\r\n        // 紧急采购\r\n        if (data.urgentPurchaseNum !== undefined) {\r\n          this.planExecutionData[3].value = data.urgentPurchaseNum.toString()\r\n        }\r\n        // 供应商响应率\r\n        if (data.supplierResponseRate !== undefined) {\r\n          this.planExecutionData[4].value = data.supplierResponseRate + '%'\r\n        }\r\n        // 库存周转率\r\n        if (data.inventoryTurnoverRate !== undefined) {\r\n          this.planExecutionData[5].value = data.inventoryTurnoverRate.toString()\r\n        }\r\n        // 采购成本节约\r\n        if (data.costSavingRate !== undefined) {\r\n          this.planExecutionData[6].value = data.costSavingRate + '%'\r\n        }\r\n        // 质量合格率\r\n        if (data.qualityPassRate !== undefined) {\r\n          this.planExecutionData[7].value = data.qualityPassRate + '%'\r\n        }\r\n        console.log('计划执行状态数据更新完成:', this.planExecutionData)\r\n      }\r\n    },\r\n\r\n    // 格式化数字显示（添加千分位分隔符）\r\n    formatNumber(num) {\r\n      if (num === undefined || num === null) return '0'\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')\r\n    },\r\n\r\n    // 获取计划管理图标\r\n    getPlanIcon(index) {\r\n      const icons = [\r\n        'el-icon-document', // 计划总条数\r\n        'el-icon-close', // 审核驳回\r\n        'el-icon-error', // 商务部驳回\r\n        'el-icon-time', // 订单至入库平均天数\r\n        'el-icon-truck', // 入库至领用平均天数\r\n        'el-icon-message', // 接收至挂单平均天数\r\n        'el-icon-warning-outline', // 超期未入库数\r\n        'el-icon-warning-outline' // 超期未领用数\r\n      ]\r\n      return icons[index] || 'el-icon-info'\r\n    },\r\n\r\n    // 获取计划执行状态图标\r\n    getExecutionIcon(index) {\r\n      const icons = [\r\n        'el-icon-success', // 计划完成率\r\n        'el-icon-goods', // 在途订单数\r\n        'el-icon-edit-outline', // 待审核计划\r\n        'el-icon-warning', // 紧急采购\r\n        'el-icon-phone', // 供应商响应率\r\n        'el-icon-refresh', // 库存周转率\r\n        'el-icon-coin', // 采购成本节约\r\n        'el-icon-circle-check' // 质量合格率\r\n      ]\r\n      return icons[index] || 'el-icon-info'\r\n    },\r\n\r\n    initCharts() {\r\n      this.initFundManagementChart()\r\n      this.initSupplierManagementChart()\r\n      this.initPersonalConsumptionChart()\r\n      this.initPurchaseAnalysisChart()\r\n      this.initContractChart()\r\n      this.initTrendChart()\r\n      this.initSupplierChart()\r\n      this.fetchHighFrequencyData()\r\n      this.fetchCokingCoalInventoryData()\r\n    },\r\n\r\n    // 资金管理柱状图\r\n    initFundManagementChart() {\r\n      const chartDom = document.getElementById('fundManagementChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.fundManagement = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        grid: {\r\n          left: '15%',\r\n          right: '10%',\r\n          top: '25%',\r\n          bottom: '25%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['下月', '2月后', '3月后'],\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00BAFF'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff',\r\n            fontSize: 12\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '拟入库金额',\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            align: 'right',\r\n          },\r\n          min: 0,\r\n          max: 8000,\r\n          interval: 2000,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00BAFF'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            formatter: function(value) {\r\n              return value\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(0, 186, 255, 0.2)'\r\n            }\r\n          }\r\n        },\r\n        series: [{\r\n          name: '拟入库金额',\r\n          type: 'bar',\r\n          data: [\r\n            parseFloat(this.fundManagement.nextMonth) || 0,\r\n            parseFloat(this.fundManagement.twoMonthsLater) || 0,\r\n            parseFloat(this.fundManagement.threeMonthsLater) || 0\r\n          ],\r\n          barWidth: '50%',\r\n          itemStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0,\r\n              y: 0,\r\n              x2: 0,\r\n              y2: 1,\r\n              colorStops: [{\r\n                offset: 0, color: '#00BAFF' // 顶部颜色\r\n              }, {\r\n                offset: 1, color: '#0080CC' // 底部颜色\r\n              }],\r\n              global: false\r\n            },\r\n            borderRadius: [4, 4, 0, 0] // 顶部圆角\r\n          },\r\n          label: {\r\n            show: true,\r\n            position: 'top',\r\n            color: '#fff',\r\n            fontSize: 12,\r\n            formatter: function(params) {\r\n              return params.value\r\n            }\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              color: {\r\n                type: 'linear',\r\n                x: 0,\r\n                y: 0,\r\n                x2: 0,\r\n                y2: 1,\r\n                colorStops: [{\r\n                  offset: 0, color: '#33C7FF' // 高亮顶部颜色\r\n                }, {\r\n                  offset: 1, color: '#0099DD' // 高亮底部颜色\r\n                }],\r\n                global: false\r\n              }\r\n            }\r\n          }\r\n        }],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          formatter: function(params) {\r\n            const param = params[0]\r\n            return `${param.name}: ${param.value}`\r\n          }\r\n        }\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 供方管理饼图\r\n    initSupplierManagementChart() {\r\n      this.$nextTick(() => {\r\n        const chartDom = document.getElementById('supplierManagementChart')\r\n        if (!chartDom) {\r\n          console.error('找不到供方管理图表DOM元素')\r\n          return\r\n        }\r\n\r\n        const chart = echarts.init(chartDom)\r\n        this.charts.supplierManagement = chart\r\n\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          tooltip: {\r\n            trigger: 'item',\r\n            backgroundColor: 'rgba(0,0,0,0.8)',\r\n            borderColor: '#00BAFF',\r\n            textStyle: { color: '#fff' }\r\n          },\r\n          series: [{\r\n            type: 'pie',\r\n            radius: '60%',\r\n            center: ['50%', '60%'],\r\n            data: [\r\n              { value: 45, name: '工程' },\r\n              { value: 35, name: '货物' },\r\n              { value: 20, name: '服务' }\r\n            ],\r\n            itemStyle: {\r\n              color: function(params) {\r\n                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']\r\n                return colors[params.dataIndex % colors.length]\r\n              }\r\n            },\r\n            label: {\r\n              color: '#fff',\r\n              fontSize: 15\r\n            }\r\n          }]\r\n        }\r\n\r\n        chart.setOption(option)\r\n      })\r\n    },\r\n\r\n    // 更新资金管理图表数据\r\n    updateFundManagementChart() {\r\n      if (!this.charts.fundManagement) return\r\n\r\n      const newData = [\r\n        parseFloat(this.fundManagement.nextMonth) || 0,\r\n        parseFloat(this.fundManagement.twoMonthsLater) || 0,\r\n        parseFloat(this.fundManagement.threeMonthsLater) || 0\r\n      ]\r\n\r\n      this.charts.fundManagement.setOption({\r\n        series: [{\r\n          data: newData\r\n        }]\r\n      })\r\n    },\r\n\r\n    // 库存管理图表\r\n    initPersonalConsumptionChart() {\r\n      const chartDom = document.getElementById('personalConsumptionChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.personalConsumption = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'item',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        series: [{\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          center: ['50%', '50%'],\r\n          data: [\r\n            { value: 335, name: '矿料' },\r\n            { value: 310, name: '合金' },\r\n            { value: 234, name: '焦炭' },\r\n            { value: 135, name: '辅料/电极' }\r\n          ],\r\n          itemStyle: {\r\n            color: function(params) {\r\n              const colors = ['#00BAFF', '#3DE7C9', '#FFC107', '#FF6B6B']\r\n              return colors[params.dataIndex % colors.length]\r\n            }\r\n          },\r\n          label: {\r\n            color: '#fff',\r\n            fontSize: 12\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 供方管理图表\r\n    initPurchaseAnalysisChart() {\r\n      const chartDom = document.getElementById('purchaseAnalysisChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.purchaseAnalysis = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff' }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff' },\r\n          splitLine: { lineStyle: { color: 'rgba(0,186,255,0.2)' } }\r\n        },\r\n        series: [{\r\n          data: [120, 200, 150, 80, 70, 110],\r\n          type: 'line',\r\n          smooth: true,\r\n          lineStyle: { color: '#00BAFF', width: 2 },\r\n          itemStyle: { color: '#00BAFF' },\r\n          areaStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0, y: 0, x2: 0, y2: 1,\r\n              colorStops: [\r\n                { offset: 0, color: 'rgba(0,186,255,0.3)' },\r\n                { offset: 1, color: 'rgba(0,186,255,0.1)' }\r\n              ]\r\n            }\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 合同管理柱状图\r\n    initContractChart() {\r\n      const chartDom = document.getElementById('contractChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.contract = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' },\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            const data = params[0]\r\n            let value = data.value\r\n            let formattedValue = ''\r\n            if (value >= 10000000) {\r\n              formattedValue = (value / 10000000).toFixed(1) + '千万'\r\n            } else if (value >= 10000) {\r\n              formattedValue = (value / 10000).toFixed(1) + '万'\r\n            } else {\r\n              formattedValue = value.toString()\r\n            }\r\n            return `${data.name}<br/>合同数量: ${formattedValue}`\r\n          }\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '4%',\r\n          bottom: '20%',\r\n          top: '25%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: this.contractData.map(item => item.name),\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 0,\r\n            rotate: 30,\r\n            fontSize: 10\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '合同数量',\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            align: 'right'\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: function(value) {\r\n              if (value >= 10000000) {\r\n                return (value / 10000000).toFixed(1) + '千万'\r\n              } else if (value >= 10000) {\r\n                return (value / 10000).toFixed(1) + '万'\r\n              } else {\r\n                return value\r\n              }\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: [{\r\n          name: '合同数量',\r\n          type: 'bar',\r\n          data: this.contractData.map(item => item.count),\r\n          itemStyle: {\r\n            color: '#83bff6',\r\n            borderRadius: [4, 4, 0, 0]            \r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              color: '#83bff6',\r\n              borderRadius: [4, 4, 0, 0],\r\n              shadowBlur: 10,\r\n              shadowColor: 'rgba(255, 255, 255, 0.5)',\r\n              borderWidth: 2,\r\n              borderColor: '#fff'\r\n            }\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 趋势分析图表\r\n    initTrendChart() {\r\n      const chartDom = document.getElementById('trendChart')\r\n      if (!chartDom) return\r\n\r\n      const chart = echarts.init(chartDom)\r\n      this.charts.trend = chart\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00BAFF',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff', fontSize: 10 }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          axisLine: { lineStyle: { color: '#00BAFF' } },\r\n          axisLabel: { color: '#fff', fontSize: 10 },\r\n          splitLine: { lineStyle: { color: 'rgba(0,186,255,0.2)' } }\r\n        },\r\n        series: [{\r\n          data: [820, 932, 901, 934, 1290, 1330, 1320],\r\n          type: 'line',\r\n          smooth: true,\r\n          lineStyle: { color: '#3DE7C9', width: 2 },\r\n          itemStyle: { color: '#3DE7C9' },\r\n          areaStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0, y: 0, x2: 0, y2: 1,\r\n              colorStops: [\r\n                { offset: 0, color: 'rgba(61,231,201,0.3)' },\r\n                { offset: 1, color: 'rgba(61,231,201,0.1)' }\r\n              ]\r\n            }\r\n          }\r\n        }]\r\n      }\r\n\r\n      chart.setOption(option)\r\n    },\r\n\r\n    // 单一来源图表\r\n    initSupplierChart() {\r\n      this.$nextTick(() => {\r\n        const chartDom = document.getElementById('supplierChart')\r\n        console.log('单一来源图表DOM元素:', chartDom)\r\n\r\n        if (!chartDom) {\r\n          console.error('找不到单一来源图表DOM元素')\r\n          return\r\n        }\r\n\r\n        const chart = echarts.init(chartDom)\r\n        this.charts.supplier = chart\r\n\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          tooltip: {\r\n            trigger: 'item',\r\n            backgroundColor: 'rgba(0,0,0,0.8)',\r\n            borderColor: '#00BAFF',\r\n            textStyle: { color: '#fff' }\r\n          },\r\n          series: [{\r\n            type: 'pie',\r\n            radius: '50%',\r\n            center: ['50%', '30%'],\r\n            data: [\r\n              { value: 40, name: '工程' },\r\n              { value: 30, name: '货物' },\r\n              { value: 30, name: '服务' }\r\n            ],\r\n            itemStyle: {\r\n              color: function(params) {\r\n                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']\r\n                return colors[params.dataIndex % colors.length]\r\n              }\r\n            },\r\n            label: {\r\n              color: '#fff',\r\n              fontSize: 10\r\n            }\r\n          }]\r\n        }\r\n\r\n        chart.setOption(option)\r\n      })\r\n    },\r\n\r\n    // 获取高频物资数据\r\n    async fetchHighFrequencyData() {\r\n      try {\r\n        // 获取高频采购物料数据\r\n        await this.fetchHighFrequencyMaterialData()\r\n        // 获取价格趋势数据\r\n        await this.fetchPriceAndStoreData()\r\n        // 初始化词云和价格趋势图\r\n        this.$nextTick(() => {\r\n          this.initHighFrequencyMaterialCloud()\r\n          this.initHighFrequencyPriceTrendChart()\r\n        })\r\n      } catch (error) {\r\n        console.error('获取高频物资数据失败:', error)\r\n      }\r\n    },\r\n\r\n    // 获取高频采购物料数据\r\n    async fetchHighFrequencyMaterialData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          codeType: this.selectedCodeType,\r\n          itemType: this.selectedItemType\r\n        }\r\n\r\n        const response = await showHighFrequencyMaterialList(params)\r\n        if (response && response.data) {\r\n          // 取前10条数据\r\n          this.highFrequencyMaterialList = (response.data || []).slice(0, 10)\r\n        } else {\r\n          // 使用模拟数据\r\n          this.highFrequencyMaterialList = this.getMockHighFrequencyData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取高频物料数据失败:', error)\r\n        this.highFrequencyMaterialList = this.getMockHighFrequencyData()\r\n      }\r\n    },\r\n\r\n    // 初始化高频物料词云\r\n    initHighFrequencyMaterialCloud() {\r\n      const chartDom = document.getElementById('highFrequencyMaterialCloud')\r\n      if (!chartDom) {\r\n        console.error('找不到高频物料词云DOM元素')\r\n        return\r\n      }\r\n\r\n      chartDom.innerHTML = ''\r\n\r\n      const rawMaterialList = this.highFrequencyMaterialList\r\n      if (!rawMaterialList || rawMaterialList.length === 0) {\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">无高频采购物料数据</div>'\r\n        return\r\n      }\r\n\r\n      // 按入库金额排序，取前15条\r\n      const highFrequencyMaterials = rawMaterialList\r\n        .sort((a, b) => (b.inAmt || 0) - (a.inAmt || 0))\r\n        .slice(0, 15)\r\n\r\n      const container = document.createElement('div')\r\n      container.style.width = '100%'\r\n      container.style.height = '100%'\r\n      container.style.position = 'relative'\r\n      container.style.overflow = 'hidden'\r\n\r\n      const colors = [\r\n        '#4fc3f7', '#a5d6a7', '#ffcc80', '#ef9a9a', '#ce93d8',\r\n        '#90caf9', '#80deea', '#c5e1a5', '#fff59d', '#ffab91'\r\n      ]\r\n\r\n      const maxFontSize = 24\r\n      const minFontSize = 12\r\n\r\n      highFrequencyMaterials.forEach((item, index) => {\r\n        let fontSize = maxFontSize - (index * 1.2)\r\n        if (fontSize < minFontSize) {\r\n          fontSize = minFontSize\r\n        }\r\n\r\n        const div = document.createElement('div')\r\n        div.textContent = item.itemName\r\n        div.style.position = 'absolute'\r\n        div.style.fontSize = `${fontSize}px`\r\n        div.style.fontWeight = 'bold'\r\n        div.style.color = colors[index % colors.length]\r\n        div.style.transform = `rotate(${Math.random() * 30 - 15}deg)`\r\n        div.style.left = `${10 + Math.random() * 60}%`\r\n        div.style.top = `${10 + Math.random() * 60}%`\r\n        div.style.whiteSpace = 'nowrap'\r\n        div.style.textShadow = '1px 1px 2px rgba(0,0,0,0.3)'\r\n        div.style.transition = 'all 0.3s ease'\r\n        div.style.cursor = 'pointer'\r\n        div.style.zIndex = highFrequencyMaterials.length - index\r\n\r\n        // 添加悬停效果\r\n        const self = this\r\n        div.addEventListener('mouseenter', function() {\r\n          this.style.transform = `rotate(${Math.random() * 30 - 15}deg) scale(1.1)`\r\n          this.style.zIndex = '999'\r\n          this.style.textShadow = '2px 2px 4px rgba(0,0,0,0.5)'\r\n\r\n          // 显示金额提示\r\n          const tooltip = document.createElement('div')\r\n          tooltip.className = 'material-tooltip'\r\n          tooltip.textContent = `金额: ${self.formatAmount(item.inAmt)}`\r\n          tooltip.style.position = 'absolute'\r\n          tooltip.style.bottom = '-25px'\r\n          tooltip.style.left = '50%'\r\n          tooltip.style.transform = 'translateX(-50%)'\r\n          tooltip.style.background = 'rgba(0,0,0,0.8)'\r\n          tooltip.style.color = '#fff'\r\n          tooltip.style.padding = '2px 6px'\r\n          tooltip.style.borderRadius = '3px'\r\n          tooltip.style.fontSize = '10px'\r\n          tooltip.style.whiteSpace = 'nowrap'\r\n          tooltip.style.zIndex = '1000'\r\n          this.appendChild(tooltip)\r\n        })\r\n\r\n        div.addEventListener('mouseleave', function() {\r\n          this.style.transform = `rotate(${Math.random() * 30 - 15}deg) scale(1)`\r\n          this.style.zIndex = (highFrequencyMaterials.length - index).toString()\r\n          this.style.textShadow = '1px 1px 2px rgba(0,0,0,0.3)'\r\n\r\n          const tooltip = this.querySelector('.material-tooltip')\r\n          if (tooltip) {\r\n            this.removeChild(tooltip)\r\n          }\r\n        })\r\n\r\n        container.appendChild(div)\r\n      })\r\n\r\n      chartDom.appendChild(container)\r\n    },\r\n\r\n    // 获取模拟高频物料数据\r\n    getMockHighFrequencyData() {\r\n      return [\r\n        { itemName: '粗粉', inAmt: 392467.2, inNum: 5421293 },\r\n        { itemName: '精粉', inAmt: 280350.5, inNum: 4250180 },\r\n        { itemName: '球团', inAmt: 195200.8, inNum: 3180970 },\r\n        { itemName: '烧结', inAmt: 150420.3, inNum: 2890540 },\r\n        { itemName: '焦炭', inAmt: 125680.7, inNum: 2350210 },\r\n        { itemName: '煤炭', inAmt: 98750.2, inNum: 1980760 },\r\n        { itemName: 'PB块', inAmt: 85420.1, inNum: 1650430 },\r\n        { itemName: '铁矿石', inAmt: 72350.8, inNum: 1420890 },\r\n        { itemName: '废钢', inAmt: 65280.4, inNum: 1280560 },\r\n        { itemName: '石灰石', inAmt: 58190.6, inNum: 1150320 },\r\n        { itemName: '合金', inAmt: 52180.3, inNum: 980450 },\r\n        { itemName: '电极', inAmt: 48750.9, inNum: 850320 },\r\n        { itemName: '耐火材料', inAmt: 42350.7, inNum: 720180 },\r\n        { itemName: '化工原料', inAmt: 38920.5, inNum: 650290 },\r\n        { itemName: '辅料', inAmt: 35680.2, inNum: 580160 }\r\n      ]\r\n    },\r\n\r\n    // 获取物料价格和采购量数据\r\n    async fetchPriceAndStoreData() {\r\n      try {\r\n        const params = {\r\n          dimensionType: this.currentDimensionType,\r\n          itemName: 'PB块'  // 固定获取PB块数据\r\n        }\r\n\r\n        const response = await getPurchasePriceAndStore(params)\r\n        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {\r\n          this.priceAndStoreData = response.data[0]\r\n          console.log('获取到PB块价格和采购量数据:', this.priceAndStoreData)\r\n        } else {\r\n          console.log('未获取到真实数据，使用模拟数据')\r\n          this.priceAndStoreData = this.getMockPriceAndStoreData()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取价格和采购量数据失败:', error)\r\n        this.priceAndStoreData = this.getMockPriceAndStoreData()\r\n      }\r\n    },\r\n\r\n    // 获取模拟的价格和采购量数据（参考采购看板数据结构）\r\n    getMockPriceAndStoreData() {\r\n      return {\r\n        procurementPriceVoList: [\r\n          {\r\n            priceName: 'PB块价格',\r\n            priceList: [\r\n              { recordDate: '20240801', price: 850.0 },\r\n              { recordDate: '20240815', price: 870.5 },\r\n              { recordDate: '20240901', price: 890.2 },\r\n              { recordDate: '20240915', price: 875.8 },\r\n              { recordDate: '20241001', price: 920.3 },\r\n              { recordDate: '20241015', price: 905.7 },\r\n              { recordDate: '20241101', price: 880.4 },\r\n              { recordDate: '20241115', price: 895.6 },\r\n              { recordDate: '20241123', price: 910.2 },  // 2024年11月23日价格\r\n              { recordDate: '20241201', price: 925.8 },\r\n              { recordDate: '20241215', price: 940.1 },\r\n              { recordDate: '20250101', price: 930.5 },\r\n              { recordDate: '20250115', price: 915.3 },\r\n              { recordDate: '20250201', price: 900.7 },\r\n              { recordDate: '20250215', price: 885.9 },\r\n              { recordDate: '20250301', price: 870.2 },\r\n              { recordDate: '20250315', price: 855.8 },\r\n              { recordDate: '20250401', price: 840.6 },\r\n              { recordDate: '20250415', price: 825.4 },\r\n              { recordDate: '20250501', price: 810.9 },\r\n              { recordDate: '20250515', price: 795.7 },\r\n              { recordDate: '20250601', price: 780.3 }\r\n            ]\r\n          }\r\n        ],\r\n        procurementPurchaseAmountVoList: [\r\n          {\r\n            amountName: 'PB块采购量',\r\n            amountList: [\r\n              { recordDate: '20240801', amount: 125000 },   // 12.5万吨\r\n              { recordDate: '20240815', amount: 118000 },   // 11.8万吨\r\n              { recordDate: '20240901', amount: 132000 },   // 13.2万吨\r\n              { recordDate: '20240915', amount: 145000 },   // 14.5万吨\r\n              { recordDate: '20241001', amount: 138000 },   // 13.8万吨\r\n              { recordDate: '20241015', amount: 152000 },   // 15.2万吨\r\n              { recordDate: '20241101', amount: 168000 },   // 16.8万吨\r\n              { recordDate: '20241115', amount: 175000 },   // 17.5万吨\r\n              { recordDate: '20241123', amount: 100000 },   // 10万吨\r\n              { recordDate: '20241201', amount: 185000 },   // 18.5万吨\r\n              { recordDate: '20241215', amount: 192000 },   // 19.2万吨\r\n              { recordDate: '20250101', amount: 178000 },   // 17.8万吨\r\n              { recordDate: '20250115', amount: 165000 },   // 16.5万吨\r\n              { recordDate: '20250201', amount: 158000 },   // 15.8万吨\r\n              { recordDate: '20250215', amount: 142000 },   // 14.2万吨\r\n              { recordDate: '20250301', amount: 135000 },   // 13.5万吨\r\n              { recordDate: '20250315', amount: 128000 },   // 12.8万吨\r\n              { recordDate: '20250401', amount: 121000 },   // 12.1万吨\r\n              { recordDate: '20250415', amount: 115000 },   // 11.5万吨\r\n              { recordDate: '20250501', amount: 108000 },   // 10.8万吨\r\n              { recordDate: '20250515', amount: 102000 },   // 10.2万吨\r\n              { recordDate: '20250601', amount: 95000 }     // 9.5万吨\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n    },\r\n\r\n    // 初始化高频物资价格趋势图（完全按照图片效果重写）\r\n    initHighFrequencyPriceTrendChart() {\r\n      this.$nextTick(() => {\r\n        const chartDom = document.getElementById('highFrequencyPriceTrendChart')\r\n        if (!chartDom) {\r\n          console.error('找不到高频物资价格趋势图DOM元素')\r\n          return\r\n        }\r\n\r\n        // 清理现有实例\r\n        if (this.charts.highFrequencyPriceTrend) {\r\n          this.charts.highFrequencyPriceTrend.dispose()\r\n        }\r\n\r\n        const chart = echarts.init(chartDom)\r\n        this.charts.highFrequencyPriceTrend = chart\r\n\r\n        // 使用真实数据结构\r\n        const priceAndStoreData = this.priceAndStoreData\r\n        if (!priceAndStoreData) {\r\n          chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无价格趋势数据</div>'\r\n          return\r\n        }\r\n\r\n        // 收集所有日期\r\n        let allDates = new Set()\r\n\r\n        // 从价格数据中收集日期\r\n        if (priceAndStoreData.procurementPriceVoList) {\r\n          priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {\r\n            if (priceGroup.priceList) {\r\n              priceGroup.priceList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n\r\n        // 从采购量数据中收集日期\r\n        if (priceAndStoreData.procurementPurchaseAmountVoList) {\r\n          priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {\r\n            if (amountGroup.amountList) {\r\n              amountGroup.amountList.forEach(item => {\r\n                allDates.add(item.recordDate)\r\n              })\r\n            }\r\n          })\r\n        }\r\n\r\n        // 转换为排序的数组\r\n        allDates = Array.from(allDates).sort()\r\n\r\n        if (allDates.length === 0) {\r\n          chartDom.innerHTML = '<div class=\"chart-placeholder\">暂无价格趋势数据</div>'\r\n          return\r\n        }\r\n\r\n        // 构建系列数据\r\n        const series = []\r\n        const legendData = []\r\n\r\n        // 构建价格系列（蓝色线）\r\n        if (priceAndStoreData.procurementPriceVoList) {\r\n          priceAndStoreData.procurementPriceVoList.forEach(priceGroup => {\r\n            const priceData = allDates.map(date => {\r\n              const found = priceGroup.priceList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.price) : null\r\n            })\r\n\r\n            series.push({\r\n              name: priceGroup.priceName,\r\n              type: 'line',\r\n              yAxisIndex: 0,\r\n              data: priceData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 2,\r\n                color: '#00d4ff'  // 蓝色\r\n              },\r\n              itemStyle: {\r\n                color: '#00d4ff'\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 4,\r\n              connectNulls: true\r\n            })\r\n\r\n            legendData.push(priceGroup.priceName)\r\n          })\r\n        }\r\n\r\n        // 构建采购量系列（橙色线）\r\n        if (priceAndStoreData.procurementPurchaseAmountVoList) {\r\n          priceAndStoreData.procurementPurchaseAmountVoList.forEach(amountGroup => {\r\n            const amountData = allDates.map(date => {\r\n              const found = amountGroup.amountList.find(item => item.recordDate === date)\r\n              return found ? parseFloat(found.amount) : null\r\n            })\r\n\r\n            series.push({\r\n              name: amountGroup.amountName,\r\n              type: 'line',\r\n              yAxisIndex: 1,\r\n              data: amountData,\r\n              smooth: true,\r\n              lineStyle: {\r\n                width: 2,\r\n                color: '#ff9f7f'  // 橙色\r\n              },\r\n              itemStyle: {\r\n                color: '#ff9f7f'\r\n              },\r\n              symbol: 'circle',\r\n              symbolSize: 4,\r\n              connectNulls: true\r\n            })\r\n\r\n            legendData.push(amountGroup.amountName)\r\n          })\r\n        }\r\n\r\n        // 计算Y轴范围\r\n        let priceMin, priceMax\r\n\r\n        // 计算价格轴范围（左轴）\r\n        const priceValues = series.filter(s => s.yAxisIndex === 0)\r\n          .flatMap(s => s.data.filter(v => v !== null && v !== undefined))\r\n        if (priceValues.length > 0) {\r\n          priceMin = Math.min(...priceValues)\r\n          priceMax = Math.max(...priceValues)\r\n        }\r\n\r\n        // 采购量轴范围固定为5万-20万\r\n\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: {\r\n              type: 'cross',\r\n              crossStyle: {\r\n                color: '#999'\r\n              }\r\n            },\r\n            backgroundColor: 'rgba(0,0,0,0.8)',\r\n            borderColor: '#00d4ff',\r\n            borderWidth: 1,\r\n            textStyle: {\r\n              color: '#fff'\r\n            },\r\n            formatter: function(params) {\r\n              let str = params[0].axisValueLabel + '<br/>'\r\n              params.forEach(item => {\r\n                if (item.value !== null && item.value !== undefined) {\r\n                  if (item.seriesName.includes('价格') || item.seriesName.includes('价')) {\r\n                    str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`\r\n                  } else {\r\n                    // 采购量显示为万吨\r\n                    const valueInWan = (parseFloat(item.value) / 10000).toFixed(1)\r\n                    str += `${item.marker}${item.seriesName}: ${valueInWan} 万吨<br/>`\r\n                  }\r\n                } else {\r\n                  str += `${item.marker}${item.seriesName}: -<br/>`\r\n                }\r\n              })\r\n              return str\r\n            }\r\n          },\r\n          legend: {\r\n            data: legendData,\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 12\r\n            },\r\n            top: '0%',\r\n            right: '10%',\r\n            itemGap: 5,\r\n            orient: 'horizontal'\r\n          },\r\n          grid: {\r\n            left: '12%',\r\n            right: '12%',\r\n            bottom: '8%',\r\n            top: '32%',\r\n            containLabel: false\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: allDates.map(date => {\r\n              const year = date.substring(0, 4)\r\n              const month = parseInt(date.substring(4, 6))\r\n              return `${year}.${month}`\r\n            }),\r\n            axisLabel: {\r\n              color: '#fff',\r\n              fontSize: 11,\r\n              interval: 'auto'\r\n            },\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#666'\r\n              }\r\n            },\r\n            axisTick: {\r\n              show: false\r\n            }\r\n          },\r\n          yAxis: [\r\n            {\r\n              type: 'value',\r\n              name: '价格(元/吨)',\r\n              nameLocation: 'middle',\r\n              nameGap: 35,\r\n              nameRotate: 90,\r\n              nameTextStyle: {\r\n                color: '#fff',\r\n                fontSize: 11,\r\n                fontWeight: 'normal'\r\n              },\r\n              position: 'left',\r\n              min: priceMin,\r\n              max: priceMax,\r\n              axisLine: {\r\n                show: true,\r\n                lineStyle: {\r\n                  color: '#666'\r\n                }\r\n              },\r\n              axisLabel: {\r\n                color: '#fff',\r\n                fontSize: 10\r\n              },\r\n              splitLine: {\r\n                show: true,\r\n                lineStyle: {\r\n                  color: 'rgba(255,255,255,0.1)',\r\n                  type: 'dashed'\r\n                }\r\n              },\r\n              axisTick: {\r\n                show: false\r\n              }\r\n            },\r\n            {\r\n              type: 'value',\r\n              name: '采购量(万吨)',\r\n              nameLocation: 'middle',\r\n              nameGap: 35,\r\n              nameRotate: -90,\r\n              nameTextStyle: {\r\n                color: '#fff',\r\n                fontSize: 11,\r\n                fontWeight: 'normal'\r\n              },\r\n              position: 'right',\r\n              min: 50000,  // 5万\r\n              max: 200000, // 20万\r\n              axisLine: {\r\n                show: true,\r\n                lineStyle: {\r\n                  color: '#666'\r\n                }\r\n              },\r\n              axisLabel: {\r\n                color: '#fff',\r\n                fontSize: 10,\r\n                formatter: function(value) {\r\n                  return (value / 10000).toFixed(0)\r\n                }\r\n              },\r\n              splitLine: {\r\n                show: false\r\n              },\r\n              axisTick: {\r\n                show: false\r\n              }\r\n            }\r\n          ],\r\n          series: series\r\n        }\r\n\r\n        chart.setOption(option, true)\r\n      })\r\n    },\r\n\r\n    // 格式化金额显示\r\n    formatAmount(amount) {\r\n      if (amount >= 10000) {\r\n        return (amount / 10000).toFixed(1) + '万'\r\n      }\r\n      return amount.toFixed(1)\r\n    },\r\n\r\n    // 处理窗口大小变化\r\n    handleResize() {\r\n      // 延迟执行，避免频繁触发\r\n      clearTimeout(this.resizeTimer)\r\n      this.resizeTimer = setTimeout(() => {\r\n        Object.values(this.charts).forEach(chart => {\r\n          if (chart && chart.resize) {\r\n            chart.resize()\r\n          }\r\n        })\r\n      }, 300)\r\n    },\r\n\r\n    // 初始化全屏监听器\r\n    initFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.on('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 移除全屏监听器\r\n    removeFullscreenListener() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.off('change', this.handleFullscreenChange)\r\n      }\r\n    },\r\n\r\n    // 处理全屏状态变化\r\n    handleFullscreenChange() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        const isFullscreen = screenfull.isFullscreen\r\n        this.$store.dispatch('app/setFullscreenMode', isFullscreen)\r\n\r\n        console.log('全屏状态变化:', isFullscreen) // 调试信息\r\n        console.log('Store状态:', this.$store.state.app.isFullscreenMode) // 调试Store状态\r\n\r\n        // 全屏状态变化后，重新调整图表大小\r\n        this.$nextTick(() => {\r\n          setTimeout(() => {\r\n            this.handleResize()\r\n          }, 300) // 给布局变化一些时间\r\n        })\r\n      }\r\n    },\r\n\r\n    // 切换全屏\r\n    toggleFullscreen() {\r\n      if (screenfull && screenfull.isEnabled) {\r\n        screenfull.toggle()\r\n      } else {\r\n        this.$message({\r\n          message: '您的浏览器不支持全屏功能',\r\n          type: 'warning'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 时间过滤器变化处理\r\n    handleTimeFilterChange(filterId, dimensionType) {\r\n      this.activeFilter = filterId\r\n      this.currentDimensionType = dimensionType\r\n      console.log('选择的时间范围:', filterId, '维度:', dimensionType)\r\n\r\n      // 根据时间范围重新加载数据\r\n      this.fetchWarningData()\r\n      this.fetchFundManagementData()\r\n    },\r\n\r\n    // 跳转到供应商处罚页面\r\n    goToSupplierPenalty() {\r\n      // 在新窗口中打开供应商处罚页面\r\n      const routeUrl = this.$router.resolve('/purchase/suppPunishment')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 跳转到采购库存看板\r\n    goToStockDashboard() {\r\n      console.log('跳转到采购库存看板')\r\n      // 在新窗口中打开采购库存看板页面\r\n      const routeUrl = this.$router.resolve('/purchaseDashboardStock')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 跳转到高频物料看板\r\n    goToHighFrequencyDashboard() {\r\n      console.log('跳转到高频物料看板')\r\n      // 在新窗口中打开高频物料看板页面\r\n      const routeUrl = this.$router.resolve('/purchaseDashboardPrice')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 跳转到采购计划看板\r\n    goToPlanDashboard() {\r\n      console.log('跳转到采购计划看板')\r\n      // 在新窗口中打开采购计划看板页面\r\n      const routeUrl = this.$router.resolve('/purchaseDashboardPlan')\r\n      window.open(routeUrl.href, '_blank')\r\n    },\r\n\r\n    // 获取矿焦煤库存数据\r\n    async fetchCokingCoalInventoryData() {\r\n      try {\r\n        const response = await showCokingCoalAmount()\r\n        console.log('fetchCokingCoalInventoryData - 完整响应:', response)\r\n\r\n        if (response && response.data) {\r\n          this.cokingCoalInventoryData = response.data || []\r\n          console.log('fetchCokingCoalInventoryData - 设置的数据:', this.cokingCoalInventoryData)\r\n        } else {\r\n          console.error('获取矿焦煤库存数据失败', response)\r\n          this.cokingCoalInventoryData = []\r\n        }\r\n      } catch (error) {\r\n        console.error('获取矿焦煤库存数据失败:', error)\r\n        this.cokingCoalInventoryData = []\r\n      }\r\n\r\n      // 数据获取完成后重新初始化图表\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalLineChart()\r\n      })\r\n    },\r\n\r\n    // 计算矿焦煤库存总量\r\n    calculateCokingCoalTotal() {\r\n      let total = 0\r\n      if (this.cokingCoalInventoryData && this.cokingCoalInventoryData.length > 0) {\r\n        // 找到最新日期\r\n        let latestDate = ''\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            item.purchaseCokingDailyDetailList.forEach(detail => {\r\n              if (detail.instockDate > latestDate) {\r\n                latestDate = detail.instockDate\r\n              }\r\n            })\r\n          }\r\n        })\r\n\r\n        // 计算最新日期各个物料的库存量合计\r\n        this.cokingCoalInventoryData.forEach(item => {\r\n          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {\r\n            const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)\r\n            if (latestDetail) {\r\n              total += parseFloat(latestDetail.invQty) || 0\r\n            }\r\n          }\r\n        })\r\n      }\r\n      return (total / 10000).toFixed(2) // 转换为万吨\r\n    },\r\n\r\n    // 处理矿焦煤类型下拉框变化\r\n    async handleCokingCoalTypeChange() {\r\n      console.log('矿焦煤类型变化:', this.selectedCokingCoalType)\r\n      // 重新初始化图表以应用过滤\r\n      this.$nextTick(() => {\r\n        this.initCokingCoalLineChart()\r\n      })\r\n    },\r\n\r\n    // 获取矿焦煤物料类型的颜色映射\r\n    getCokingCoalMaterialColorMap() {\r\n      // 使用与库存看板一致的颜色方案\r\n      const baseColors = ['#0066ff', '#00ff00', '#ff0000', '#8b00ff', '#ffff00', '#ffffff']\r\n\r\n      // 基于所有原始数据为每个物料类型分配固定颜色，确保过滤时颜色保持一致\r\n      const allMaterialTypes = []\r\n      const inventoryData = this.cokingCoalInventoryData || []\r\n\r\n      // 收集所有物料类型\r\n      inventoryData.forEach(item => {\r\n        const materialName = item.class2Name || '未知物料'\r\n        if (!allMaterialTypes.includes(materialName)) {\r\n          allMaterialTypes.push(materialName)\r\n        }\r\n      })\r\n\r\n      // 按字母顺序排序，确保颜色分配的一致性\r\n      allMaterialTypes.sort()\r\n\r\n      // 为每个物料类型分配固定颜色\r\n      const colorMap = {}\r\n      allMaterialTypes.forEach((materialName, index) => {\r\n        colorMap[materialName] = baseColors[index % baseColors.length]\r\n      })\r\n\r\n      return colorMap\r\n    },\r\n\r\n    // 初始化矿焦煤库存折线图\r\n    initCokingCoalLineChart() {\r\n      const chartDom = document.getElementById('cokingCoalLineChart')\r\n      if (!chartDom) {\r\n        console.error('找不到矿焦煤折线图DOM: cokingCoalLineChart')\r\n        return\r\n      }\r\n\r\n      // 清理现有实例\r\n      if (this.charts.cokingCoalLineChart) {\r\n        this.charts.cokingCoalLineChart.dispose()\r\n      }\r\n\r\n      const myChart = echarts.init(chartDom)\r\n      this.charts.cokingCoalLineChart = myChart\r\n\r\n      const inventoryData = this.cokingCoalInventoryData || []\r\n\r\n      if (!inventoryData || inventoryData.length === 0) {\r\n        const option = {\r\n          backgroundColor: 'transparent',\r\n          title: {\r\n            text: '暂无数据',\r\n            left: 'center',\r\n            top: 'middle',\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 16\r\n            }\r\n          }\r\n        }\r\n        myChart.setOption(option)\r\n        return\r\n      }\r\n\r\n      // 根据选中的类型过滤数据（使用与库存看板一致的过滤逻辑）\r\n      let filteredData = inventoryData\r\n      if (this.selectedCokingCoalType && this.selectedCokingCoalType !== '') {\r\n        filteredData = inventoryData.filter(item => {\r\n          return item.class2Name === this.selectedCokingCoalType ||\r\n                 item.class2Name.includes(this.selectedCokingCoalType) ||\r\n                 this.selectedCokingCoalType.includes(item.class2Name)\r\n        })\r\n      }\r\n\r\n      // 提取所有日期并排序\r\n      const allDates = new Set()\r\n      filteredData.forEach(item => {\r\n        if (item.purchaseCokingDailyDetailList) {\r\n          item.purchaseCokingDailyDetailList.forEach(detail => {\r\n            allDates.add(detail.instockDate)\r\n          })\r\n        }\r\n      })\r\n\r\n      const sortedDates = Array.from(allDates).sort()\r\n\r\n      // 格式化日期显示（从yyyyMMdd转换为MM-dd）\r\n      const formattedDates = sortedDates.map(dateStr => {\r\n        if (dateStr && dateStr.length === 8) {\r\n          const month = dateStr.substring(4, 6)\r\n          const day = dateStr.substring(6, 8)\r\n          return `${month}-${day}`\r\n        }\r\n        return dateStr\r\n      })\r\n\r\n      // 构建每个类型的曲线数据\r\n      const seriesData = []\r\n      const legendData = []\r\n\r\n      // 获取统一的颜色映射\r\n      const colorMap = this.getCokingCoalMaterialColorMap()\r\n\r\n      filteredData.forEach((item, index) => {\r\n        const typeName = item.class2Name || '未知物料'\r\n\r\n        // 为每个日期构建数据点\r\n        const lineData = sortedDates.map(date => {\r\n          const detail = item.purchaseCokingDailyDetailList?.find(d => d.instockDate === date)\r\n          return detail ? parseFloat(detail.invQty) || 0 : null\r\n        })\r\n\r\n        // 使用统一的颜色映射\r\n        const materialColor = colorMap[typeName] || '#83bff6'\r\n\r\n        const seriesItem = {\r\n          name: typeName,\r\n          type: 'line',\r\n          data: lineData,\r\n          smooth: true,\r\n          symbol: 'circle',\r\n          symbolSize: 6,\r\n          lineStyle: {\r\n            width: 3,\r\n            color: materialColor\r\n          },\r\n          itemStyle: {\r\n            color: materialColor\r\n          },\r\n          connectNulls: false\r\n        }\r\n\r\n        seriesData.push(seriesItem)\r\n        legendData.push(typeName)\r\n      })\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          },\r\n          backgroundColor: 'rgba(0,0,0,0.8)',\r\n          borderColor: '#00d4ff',\r\n          borderWidth: 1,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              if (param.value !== null && param.value !== undefined) {\r\n                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 吨<br/>`\r\n              }\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          bottom: '5%',\r\n          left: 'center'\r\n        },\r\n        grid: {\r\n          left: '8%',\r\n          right: '5%',\r\n          bottom: '25%',\r\n          top: '15%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: formattedDates,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00d4ff'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff'\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '库存量(吨)',\r\n          nameTextStyle: {\r\n            color: '#fff',\r\n            align:'right',\r\n            fontSize: 9,\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#00d4ff'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#fff'\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(0, 212, 255, 0.2)'\r\n            }\r\n          }\r\n        },\r\n        series: seriesData\r\n      }\r\n\r\n      myChart.setOption(option)\r\n    },\r\n\r\n    // 计算预警信息的百分比\r\n    getWarningPercentage(value) {\r\n      const numValue = parseInt(value) || 0\r\n\r\n      // 如果值为0，返回0%\r\n      if (numValue === 0) {\r\n        return 0\r\n      }\r\n\r\n      // 获取两个预警值中的最大值作为基准\r\n      const certificateValue = parseInt(this.warningInfo.certificateExpiry) || 0\r\n      const contractValue = parseInt(this.warningInfo.contractExpiry) || 0\r\n      const maxValue = Math.max(certificateValue, contractValue, 1) // 至少为1，避免除0\r\n\r\n      // 计算相对百分比，确保最大值显示为100%\r\n      const percentage = (numValue / maxValue) * 100\r\n      return Math.min(100, Math.max(0, percentage))\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.purchase-dashboard-main {\r\n  width: 100%;\r\n  min-height: 100vh;\r\n\r\n  .dashboard-container {\r\n    width: 100%;\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #191970, #4B0082, #800080);\r\n    color: #fff;\r\n    overflow-x: hidden;\r\n    padding: 10px;\r\n  }\r\n\r\n  .dashboard-header {\r\n    text-align: center;\r\n    margin-bottom: 10px;\r\n    position: relative;\r\n    padding: 5px 0;\r\n\r\n    h1 {\r\n      font-size: 24px;\r\n      position: relative;\r\n      display: inline-block;\r\n      padding: 5px 40px;\r\n      margin: 0;\r\n      color: #fff;\r\n    }\r\n\r\n    &::before,\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 50%;\r\n      width: 30%;\r\n      height: 2px;\r\n      background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n    }\r\n\r\n    &::before {\r\n      left: 0;\r\n    }\r\n\r\n    &::after {\r\n      right: 0;\r\n    }\r\n  }\r\n\r\n  .header-controls {\r\n    position: absolute;\r\n    right: 20px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 15px;\r\n    z-index: 1000;\r\n  }\r\n\r\n  .fullscreen-btn {\r\n    padding: 8px 12px;\r\n    border: none;\r\n    background-color: rgba(33, 10, 56, 0.7);\r\n    color: #eee;\r\n    border-radius: 20px;\r\n    font-size: 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    border: 1px solid rgba(0, 212, 255, 0.2);\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 40px;\r\n    height: 32px;\r\n\r\n    &:hover {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n      background-color: rgba(0, 212, 255, 0.2);\r\n      border-color: rgba(0, 212, 255, 0.7);\r\n      color: #00ffff;\r\n    }\r\n  }\r\n\r\n  .time-filter {\r\n    display: flex;\r\n    gap: 10px;\r\n  }\r\n\r\n  .time-filter-btn {\r\n    padding: 6px 12px;\r\n    border: none;\r\n    background-color: rgba(33, 10, 56, 0.7);\r\n    color: #eee;\r\n    border-radius: 20px;\r\n    font-size: 12px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    border: 1px solid rgba(0, 212, 255, 0.2);\r\n    position: relative;\r\n\r\n    &:hover {\r\n      transform: scale(1.05);\r\n      box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);\r\n    }\r\n\r\n    &.active {\r\n      background-color: rgba(0, 212, 255, 0.2);\r\n      border-color: rgba(0, 212, 255, 0.7);\r\n      color: #00ffff;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  .dashboard-content {\r\n    display: flex;\r\n    height: calc(100vh - 80px);\r\n    gap: 10px;\r\n  }\r\n\r\n  .left-panel,\r\n  .right-panel {\r\n    flex: 0 0 320px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .center-panel {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 5px; /* 缩短行间距从10px到5px */\r\n  }\r\n\r\n  .center-row {\r\n    flex: 1;\r\n    display: flex;\r\n    gap: 5px; /* 缩短卡片间距从10px到5px */\r\n  }\r\n\r\n  .center-row .card {\r\n    flex: 1;\r\n  }\r\n\r\n  /* 第一行特定样式 - 缩短高度 */\r\n  .center-row-first {\r\n    flex: 1; /* 减小第一行的高度比例 */\r\n    max-height: 250px; /* 进一步限制第一行的最大高度 */\r\n  }\r\n\r\n  /* 第二行特定样式 - 缩短高度 */\r\n  .center-row-second {\r\n    flex: 0.7; /* 进一步减小第二行的高度比例 */\r\n    max-height: 330px; /* 进一步限制第二行的最大高度 */\r\n  }\r\n\r\n  /* 全屏模式下的样式调整 - 使用更高优先级的选择器 */\r\n  .purchase-dashboard-main.fullscreen-mode {\r\n    /* 调试样式 - 全屏时改变背景色 */\r\n    background: linear-gradient(135deg, #2a2a90, #5B1082, #900090) !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-first {\r\n    max-height: none !important; /* 全屏时移除第一行高度限制 */\r\n    flex: 1 !important; /* 确保flex比例正确 */\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-second {\r\n    max-height: none !important; /* 全屏时移除第二行高度限制 */\r\n    flex: 1 !important; /* 确保flex比例正确 */\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-container .center-panel .center-row-full .card {\r\n    max-height: none !important; /* 全屏时移除全宽行高度限制 */\r\n    min-height: 60px !important; /* 保持最小高度 */\r\n  }\r\n\r\n  /* 全屏模式下调整整体容器高度和布局 */\r\n  .purchase-dashboard-main.fullscreen-mode .dashboard-content {\r\n    height: calc(100vh - 60px) !important; /* 全屏时减去标题高度 */\r\n    min-height: calc(100vh - 60px) !important;\r\n    display: flex !important;\r\n    gap: 10px !important;\r\n    width: 100% !important;\r\n    justify-content: center !important; /* 居中对齐 */\r\n    align-items: stretch !important;\r\n    overflow-x: auto !important; /* 允许水平滚动以防内容过宽 */\r\n    padding: 0 10px !important; /* 添加一些内边距 */\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n  /* 新增：全宽行样式 */\r\n  .center-row-full {\r\n    width: 100%;\r\n    margin: 2px 0; /* 缩短上下边距从5px到2px */\r\n    flex-shrink: 0; /* 防止被压缩 */\r\n  }\r\n\r\n  .center-row-full .card {\r\n    width: 100%;\r\n    min-height: 50px; /* 设置最小高度 */\r\n    max-height: 80px; /* 设置最大高度，确保不占用太多空间 */\r\n  }\r\n\r\n  .left-panel .card,\r\n  .right-panel .card {\r\n    flex: 1;\r\n  }\r\n\r\n  /* 全屏模式下右侧面板的特殊样式 - 解决高度被过度拉伸的问题 */\r\n  .purchase-dashboard-main.fullscreen-mode .right-panel {\r\n    /* 改变右侧面板的布局方式，平均分配空间而不是拉伸 */\r\n    justify-content: space-between !important;\r\n    align-items: stretch !important;\r\n    width: 320px !important; /* 固定右侧面板宽度 */\r\n    min-width: 320px !important;\r\n    max-width: 320px !important;\r\n    flex: none !important;\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .right-panel .card {\r\n    flex: 0 0 calc(33.33% - 8px) !important; /* 三等分，减去间距 */\r\n    height: auto !important; /* 让内容决定高度 */\r\n    min-height: 120px !important; /* 设置更小的最小高度 */\r\n    max-height: 200px !important; /* 限制最大高度，更加紧凑 */\r\n    overflow-y: auto !important; /* 内容过多时滚动 */\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .left-panel {\r\n    justify-content: space-between !important;\r\n    width: 320px !important; /* 固定左侧面板宽度 */\r\n    min-width: 320px !important;\r\n    max-width: 320px !important;\r\n    flex: none !important;\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .center-panel {\r\n    flex: 1 !important; /* 中间面板占用剩余空间 */\r\n    min-width: 400px !important; /* 最小宽度保证内容显示 */\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .left-panel .card {\r\n    flex: 0 0 calc(50% - 6px) !important; /* 二等分，减去间距 */\r\n    height: auto !important;\r\n    min-height: 140px !important;\r\n    max-height: 260px !important;\r\n    overflow-y: auto !important;\r\n  }\r\n\r\n  /* 全屏模式下优化具体内容的显示 - 缩小内容 */\r\n  .purchase-dashboard-main.fullscreen-mode .card-title {\r\n    font-size: 14px !important;\r\n    margin-bottom: 8px !important;\r\n    padding: 8px 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-analysis {\r\n    padding: 6px 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-item {\r\n    margin-bottom: 6px !important;\r\n    padding: 4px 0 !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-name {\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .warning-value {\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .simple-display {\r\n    padding: 10px 12px !important;\r\n    text-align: center !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .display-number {\r\n    font-size: 24px !important;\r\n    margin-bottom: 4px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .display-label {\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-data {\r\n    padding: 6px 12px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-item {\r\n    margin-bottom: 4px !important;\r\n    padding: 3px 0 !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-label {\r\n    font-size: 11px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .funnel-value {\r\n    font-size: 12px !important;\r\n  }\r\n\r\n  /* 全屏模式下缩小警告条的高度 */\r\n  .purchase-dashboard-main.fullscreen-mode .warning-bar {\r\n    height: 16px !important;\r\n    margin-left: 8px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .bar-bg {\r\n    height: 16px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .bar-fill {\r\n    height: 16px !important;\r\n  }\r\n\r\n  /* 全屏模式下调整卡片内边距 */\r\n  .purchase-dashboard-main.fullscreen-mode .right-panel .card {\r\n    padding: 8px !important;\r\n  }\r\n\r\n  .purchase-dashboard-main.fullscreen-mode .left-panel .card {\r\n    padding: 8px !important;\r\n  }\r\n\r\n  .card {\r\n    background-color: rgba(33, 10, 56, 0.7);\r\n    border-radius: 5px;\r\n    padding: 10px;\r\n    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);\r\n    position: relative;\r\n\r\n    // 计划管理简化样式\r\n    &.plan-management-card {\r\n      .plan-grid {\r\n        display: grid;\r\n        grid-template-columns: 1fr 1fr;\r\n        gap: 4px; /* 缩短网格间距从8px到4px */\r\n        padding: 5px 0; /* 缩短上下内边距从10px到5px */\r\n\r\n        .plan-item {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 6px 35px; /* 缩短内边距从8px到4px 6px */\r\n          border-radius: 6px;\r\n          transition: background 0.2s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 186, 255, 0.1);\r\n          }\r\n\r\n          .plan-icon {\r\n            font-size: 16px;\r\n            color: #00BAFF;\r\n            margin-right: 6px; /* 缩短右边距从8px到6px */\r\n            width: 18px; /* 缩短宽度从20px到18px */\r\n            text-align: center;\r\n          }\r\n\r\n          .plan-text {\r\n            flex: 1;\r\n            min-width: 0;\r\n\r\n            .plan-value {\r\n              color: #fff;\r\n              font-size: 14px;\r\n              font-weight: bold;\r\n              line-height: 1.1; /* 缩短行高从1.2到1.1 */\r\n              margin-bottom: 1px; /* 缩短下边距从2px到1px */\r\n            }\r\n\r\n            .plan-label {\r\n              color: rgba(255, 255, 255, 0.8);\r\n              font-size: 11px;\r\n              font-weight: bold;\r\n              line-height: 1.1; /* 缩短行高从1.2到1.1 */\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 计划执行状态样式\r\n    &.plan-execution-card {\r\n      .plan-execution-grid {\r\n        display: grid;\r\n        grid-template-columns: repeat(4, 1fr);\r\n        gap: 12px;\r\n        padding: 2px 0;\r\n\r\n        .execution-item {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 10px;\r\n          border-radius: 8px;\r\n          background: rgba(0, 186, 255, 0.1);\r\n          border: 1px solid rgba(0, 186, 255, 0.3);\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            background: rgba(0, 186, 255, 0.2);\r\n            border-color: rgba(0, 186, 255, 0.5);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 186, 255, 0.3);\r\n          }\r\n\r\n          .execution-icon {\r\n            font-size: 18px;\r\n            color: #00BAFF;\r\n            margin-right: 10px;\r\n            width: 22px;\r\n            text-align: center;\r\n            flex-shrink: 0;\r\n          }\r\n\r\n          .execution-text {\r\n            flex: 1;\r\n            min-width: 0;\r\n\r\n            .execution-value {\r\n              color: #fff;\r\n              font-size: 16px;\r\n              font-weight: bold;\r\n              line-height: 1.2;\r\n              margin-bottom: 3px;\r\n            }\r\n\r\n            .execution-label {\r\n              color: rgba(255, 255, 255, 0.8);\r\n              font-size: 12px;\r\n              line-height: 1.2;\r\n              font-weight: bold;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              text-overflow: ellipsis;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    overflow: hidden; // 恢复hidden，防止重叠\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    &::before {\r\n      content: '';\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      height: 2px;\r\n      background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);\r\n    }\r\n  }\r\n\r\n  .clickable-card {\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 5px 20px rgba(0, 212, 255, 0.3);\r\n      background-color: rgba(33, 10, 56, 0.9);\r\n    }\r\n\r\n    &:active {\r\n      transform: translateY(0);\r\n    }\r\n  }\r\n\r\n  .card-title {\r\n    font-size: 14px;\r\n    margin-bottom: 5px;\r\n    font-weight: bold;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    color: #fff;\r\n  }\r\n\r\n  .inventory-total {\r\n    font-size: 12px;\r\n    color: #00d4ff;\r\n    font-weight: normal;\r\n    background: rgba(0, 212, 255, 0.1);\r\n    padding: 2px 8px;\r\n    border-radius: 4px;\r\n    border: 1px solid rgba(0, 212, 255, 0.3);\r\n  }\r\n\r\n  .chart-filter-dropdown-container {\r\n    z-index: 10;\r\n  }\r\n\r\n  .chart-filter-dropdown-container select {\r\n    padding: 4px 8px;\r\n    border-radius: 4px;\r\n    background-color: rgba(138, 43, 226, 0.7);\r\n    color: #fff;\r\n    border: 1px solid rgba(0, 212, 255, 0.3);\r\n    font-size: 12px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .chart-filter-dropdown-container select:hover {\r\n    background-color: rgba(138, 43, 226, 0.9);\r\n    border-color: rgba(0, 212, 255, 0.6);\r\n  }\r\n\r\n  .chart {\r\n    flex: 1;\r\n    width: 100%;\r\n    min-height: 150px;\r\n  }\r\n\r\n  .big-number-container {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .big-number {\r\n      color: #00BAFF;\r\n      font-size: 36px;\r\n      font-weight: bold;\r\n      text-align: center;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .unit-text {\r\n      color: #fff;\r\n      font-size: 14px;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  .progress-container {\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  // 漏斗数据样式\r\n  .funnel-data {\r\n    flex: 1;\r\n    padding: 10px 0;\r\n\r\n    .funnel-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 8px 0;\r\n      border-bottom: 1px solid rgba(0, 186, 255, 0.2);\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .funnel-label {\r\n        color: #fff;\r\n        font-size: 14px;\r\n      }\r\n\r\n      .funnel-value {\r\n        color: #00BAFF;\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 预警信息样式（完全照抄计划管理样式，只改颜色为红色）\r\n  .warning-analysis {\r\n    flex: 1;\r\n    padding: 10px 0;\r\n\r\n    .warning-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 15px;\r\n\r\n      .warning-name {\r\n        color: #fff;\r\n        font-size: 12px;\r\n        width: 80px;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .warning-bar {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-left: 10px;\r\n\r\n        .bar-bg {\r\n          flex: 1;\r\n          height: 8px;\r\n          background: rgba(255, 87, 87, 0.2);\r\n          border-radius: 4px;\r\n          overflow: hidden;\r\n          margin-right: 10px;\r\n\r\n          .bar-fill {\r\n            height: 100%;\r\n            background: linear-gradient(90deg, hsl(0, 85%, 69%), #f31804);\r\n            border-radius: 4px;\r\n            transition: width 0.3s ease;\r\n          }\r\n        }\r\n\r\n        .warning-value {\r\n          color: #FF5757;\r\n          font-size: 12px;\r\n          font-weight: bold;\r\n          width: 60px;\r\n          text-align: right;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 趋势统计样式\r\n  .trend-stats {\r\n    margin-bottom: 5px;\r\n    flex-shrink: 0;\r\n\r\n    .trend-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 3px 0;\r\n\r\n      .trend-label {\r\n        color: #fff;\r\n        font-size: 11px;\r\n      }\r\n\r\n      .trend-value {\r\n        color: #3DE7C9;\r\n        font-size: 12px;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 计划管理样式\r\n  .product-analysis {\r\n    flex: 1;\r\n    padding: 10px 0;\r\n\r\n    .product-item {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 15px;\r\n\r\n      .product-name {\r\n        color: #fff;\r\n        font-size: 12px;\r\n        width: 80px;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .product-bar {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-left: 10px;\r\n\r\n        .bar-bg {\r\n          flex: 1;\r\n          height: 8px;\r\n          background: rgba(0, 186, 255, 0.2);\r\n          border-radius: 4px;\r\n          overflow: hidden;\r\n          margin-right: 10px;\r\n\r\n          .bar-fill {\r\n            height: 100%;\r\n            background: linear-gradient(90deg, #00BAFF, #3DE7C9);\r\n            border-radius: 4px;\r\n            transition: width 0.3s ease;\r\n          }\r\n        }\r\n\r\n        .product-value {\r\n          color: #00BAFF;\r\n          font-size: 12px;\r\n          font-weight: bold;\r\n          width: 60px;\r\n          text-align: right;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 供应商圆形显示样式\r\n  .supplier-circles {\r\n    position: relative;\r\n    height: 100%;\r\n    padding: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .circle-item {\r\n      position: absolute;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n\r\n      .circle {\r\n        border-radius: 50%;\r\n        border: 2px solid;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-bottom: 8px;\r\n        transition: all 0.3s ease;\r\n\r\n        &:hover {\r\n          transform: scale(1.05);\r\n          filter: brightness(1.2);\r\n        }\r\n\r\n        &.clickable {\r\n          cursor: pointer;\r\n\r\n          &:hover {\r\n            transform: scale(1.1);\r\n            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);\r\n          }\r\n        }\r\n\r\n        .circle-number {\r\n          color: #fff;\r\n          font-weight: bold;\r\n          text-align: center;\r\n          line-height: 1.2;\r\n        }\r\n      }\r\n\r\n      .circle-label {\r\n        color: #fff;\r\n        text-align: center;\r\n        line-height: 1.2;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      // 普通圆形样式（随机位置）\r\n      &.random-position {\r\n        .circle {\r\n          width: 60px;\r\n          height: 60px;\r\n\r\n          .circle-number {\r\n            font-size: 12px;\r\n          }\r\n        }\r\n\r\n        .circle-label {\r\n          font-size: 10px;\r\n          max-width: 60px;\r\n        }\r\n      }\r\n\r\n      // 中心圆形样式（考核情况）\r\n      &.center-circle {\r\n        .circle {\r\n          width: 120px;\r\n          height: 120px;\r\n\r\n          .circle-number {\r\n            font-size: 14px;\r\n          }\r\n        }\r\n\r\n        .circle-label {\r\n          font-size: 12px;\r\n          max-width: 120px;\r\n          margin-top: 5px;\r\n        }\r\n      }\r\n\r\n      // 中心位置\r\n      &.center {\r\n        top: 50%;\r\n        left: 50%;\r\n        transform: translate(-50%, -50%);\r\n      }\r\n    }\r\n  }\r\n\r\n  // 简单显示样式\r\n  .simple-display {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    padding: 20px 0;\r\n\r\n    .display-number {\r\n      color: #FF8C00;\r\n      font-size: 36px;\r\n      font-weight: bold;\r\n      line-height: 1;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .display-label {\r\n      color: #fff;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n      text-align: center;\r\n    }\r\n  }\r\n\r\n  // 供方管理统计样式\r\n  .supplier-stats {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    align-items: center;\r\n    padding: 3px 5px;\r\n    gap: 5px;\r\n\r\n    .supplier-stat-item {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 2px;\r\n\r\n      .stat-number {\r\n        color: #FF8C00;\r\n        font-size: 28px;\r\n        font-weight: bold;\r\n        line-height: 1;\r\n        margin-bottom: 3px;\r\n      }\r\n\r\n      .stat-label {\r\n        color: #fff;\r\n        font-size: 15px;\r\n        font-weight: 500;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 供方管理卡片样式\r\n  .supplier-management-card {\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 100%;\r\n\r\n    .card-title {\r\n      flex-shrink: 0;\r\n      margin-bottom: -25px; /* 进一步减小下边距 */\r\n      font-size: 14px;\r\n    }\r\n\r\n    .chart {\r\n      flex-shrink: 0;\r\n      margin-bottom: 0px;\r\n    }\r\n\r\n    .supplier-stats {\r\n      flex: 1;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n  }\r\n\r\n  // 高频物资模块样式\r\n  .high-frequency-content {\r\n    height: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n  }\r\n\r\n  .high-frequency-materials {\r\n    flex: 2 !important;\r\n    margin-bottom: 0px;\r\n  }\r\n\r\n  .price-trend-section {\r\n    flex: 8 !important;\r\n    min-height: 120px; /* 减小最小高度 */\r\n    margin-bottom: 0px;\r\n    margin-top: 5px; /* 减小上边距 */\r\n  }\r\n\r\n  .section-title {\r\n    font-size: 12px;\r\n    color: #00BAFF;\r\n    margin: 0 0 4px 0; /* 减小下边距从8px到4px */\r\n    font-weight: 500;\r\n  }\r\n\r\n  .material-cloud {\r\n    height: 140px;\r\n    width: 100%;\r\n    position: relative;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .chart-placeholder {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    color: #666;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .material-tooltip {\r\n    position: absolute;\r\n    background: rgba(0,0,0,0.8);\r\n    color: #fff;\r\n    padding: 2px 6px;\r\n    border-radius: 3px;\r\n    font-size: 10px;\r\n    white-space: nowrap;\r\n    z-index: 1000;\r\n    pointer-events: none;\r\n  }\r\n\r\n  .mini-chart {\r\n    height: 100px;\r\n    width: 100%;\r\n  }\r\n\r\n  // 滚动条样式\r\n  .material-list::-webkit-scrollbar {\r\n    width: 4px;\r\n  }\r\n\r\n  .material-list::-webkit-scrollbar-track {\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .material-list::-webkit-scrollbar-thumb {\r\n    background: rgba(0, 186, 255, 0.5);\r\n    border-radius: 2px;\r\n  }\r\n\r\n  .material-list::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(0, 186, 255, 0.8);\r\n  }\r\n\r\n  /* 响应式样式 - 计划执行状态模块 */\r\n  @media (max-width: 1400px) {\r\n    .plan-execution-card .plan-execution-grid {\r\n      grid-template-columns: repeat(3, 1fr);\r\n    }\r\n  }\r\n\r\n  @media (max-width: 1000px) {\r\n    .plan-execution-card .plan-execution-grid {\r\n      grid-template-columns: repeat(2, 1fr);\r\n    }\r\n  }\r\n\r\n  @media (max-width: 600px) {\r\n    .plan-execution-card .plan-execution-grid {\r\n      grid-template-columns: 1fr;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n\r\n"]}]}