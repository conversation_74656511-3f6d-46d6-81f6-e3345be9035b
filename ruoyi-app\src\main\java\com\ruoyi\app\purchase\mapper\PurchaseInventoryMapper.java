package com.ruoyi.app.purchase.mapper;

import com.ruoyi.app.purchase.domain.PurchaseStatistic;
import com.ruoyi.app.purchase.domain.PurchaseTotalAmount;
import com.ruoyi.app.purchase.vo.ProcurementMonthlyResultVo;
import com.ruoyi.app.purchase.dto.ProcurementYearlyQueryDto;
import com.ruoyi.app.purchase.dto.PurchaseInventoryQueryDto;
import com.ruoyi.app.purchase.vo.PurchaseCokingCoalInventoryDetailVo;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购库存收发存全景视图Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface PurchaseInventoryMapper {

    /**
     * 查询库存金额年度统计
     *
     * @param query 查询条件
     * @return 库存金额统计列表
     */
    @DataSource(value = DataSourceType.XCC1)
    List<ProcurementMonthlyResultVo> selectYearlyInventoryAmount(ProcurementYearlyQueryDto query);

    /**
     * 查询矿焦煤每日实物库存量--中类
     *
     * @param instockDate 库存日期(yyyyMMdd格式)
     * @return 矿焦煤库存明细列表
     */
    @DataSource(value = DataSourceType.XCC1)
    List<PurchaseCokingCoalInventoryDetailVo> selectCokingCoalInventoryByDate(@Param("instockDate") String instockDate);

    /**
     * 查询矿焦煤库存表中最新的有数据的日期
     *
     * @return 最新的库存日期(yyyyMMdd格式)
     */
    @DataSource(value = DataSourceType.XCC1)
    String selectLatestCokingCoalInventoryDate();

    /**
     * 查询中心仓库金额
     *
     * @return 中心仓库金额列表
     */
    @DataSource(value = DataSourceType.XCC1)
    List<PurchaseInventoryQueryDto> selectCenterInventoryAmount();

    /**
     * 查询机旁库金额
     *
     * @return 机旁库金额列表
     */
    @DataSource(value = DataSourceType.XCC1)
    List<PurchaseInventoryQueryDto> selectMachineSideInventoryAmount();

    /**
     * 查询拟入库金额
     * @return
     */
    @DataSource(value = DataSourceType.XCC1)
    List<PurchaseTotalAmount> selectPlanInventoryAmount();
}
