{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue?vue&type=style&index=0&id=5e61e69e&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\suppPunishment\\punishmentBasis-module.vue", "mtime": 1756456493919}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLyog5by556qX5YaF5a655a655ZmoICovDQouYmFzaXMtZGlhbG9nLWNvbnRlbnQgew0KICBwYWRkaW5nOiAxMHB4IDA7DQp9DQoNCi8qIOeroOiKguagh+mimOagt+W8jyAqLw0KLnNlY3Rpb24tdGl0bGUgew0KICBmb250LXNpemU6IDE2cHg7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBtYXJnaW46IDAgMCAxNXB4IDA7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQp9DQoNCi8qIOmhtue6p+agh+mimOagt+W8j++8iOmAieaLqeS+neaNruexu+Wei++8iSAqLw0KLmJhc2lzLW9wdGlvbnMgLnNlY3Rpb24tdGl0bGUgew0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KfQ0KDQovKiDlpITnvZrkvp3mja7pgInpobnmoLflvI8gKi8NCi5iYXNpcy1vcHRpb25zIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLmJhc2lzLWl0ZW0gew0KICBtYXJnaW4tYm90dG9tOiAxNXB4Ow0KICBwYWRkaW5nOiAwOw0KfQ0KDQovKiDmlrDnmoTooYzluIPlsYAgKi8NCi5iYXNpcy1yb3cgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICB3aWR0aDogMTAwJTsNCiAgbWluLWhlaWdodDogMzZweDsNCn0NCg0KLmNoZWNrYm94LXdyYXBwZXIgew0KICB3aWR0aDogMTIwcHg7DQogIGZsZXgtc2hyaW5rOiAwOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBoZWlnaHQ6IDM2cHg7DQp9DQoNCi5pbnB1dC13cmFwcGVyIHsNCiAgd2lkdGg6IGNhbGMoMTAwJSAtIDEzNXB4KTsNCiAgbWFyZ2luLWxlZnQ6IDE0cHg7DQp9DQoNCi5hbGlnbmVkLWlucHV0IHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi5hbGlnbmVkLWlucHV0IDo6di1kZWVwIC5lbC1pbnB1dF9faW5uZXIgew0KICBoZWlnaHQ6IDM2cHg7DQogIGxpbmUtaGVpZ2h0OiAzNnB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLyog5aSa6YCJ5qGG5a+56b2Q5qC35byPICovDQouY2hlY2tib3gtd3JhcHBlciA6OnYtZGVlcCAuZWwtY2hlY2tib3ggew0KICBoZWlnaHQ6IDM2cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbjogMDsNCn0NCg0KLmNoZWNrYm94LXdyYXBwZXIgOjp2LWRlZXAgLmVsLWNoZWNrYm94X19pbnB1dCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1yaWdodDogOHB4Ow0KfQ0KDQouY2hlY2tib3gtd3JhcHBlciA6OnYtZGVlcCAuZWwtY2hlY2tib3hfX2lubmVyIHsNCiAgd2lkdGg6IDE2cHg7DQogIGhlaWdodDogMTZweDsNCn0NCg0KLmNoZWNrYm94LXdyYXBwZXIgOjp2LWRlZXAgLmVsLWNoZWNrYm94X19sYWJlbCB7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbGluZS1oZWlnaHQ6IDM2cHg7DQogIHBhZGRpbmctbGVmdDogOHB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCn0NCg0KLyog5L6d5o2u5YaF5a655Yy65Z+f5qC35byPICovDQouYmFzaXMtY29udGVudCB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5iYXNpcy1jb250ZW50IC5zZWN0aW9uLXRpdGxlIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLmNvbnRlbnQtd3JhcHBlciB7DQogIHBhZGRpbmc6IDA7DQp9DQoNCi5jb250ZW50LXRleHRhcmVhIHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi5jb250ZW50LXRleHRhcmVhIDo6di1kZWVwIC5lbC10ZXh0YXJlYV9faW5uZXIgew0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGZvbnQtZmFtaWx5OiBpbmhlcml0Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjU7DQogIG1pbi1oZWlnaHQ6IDEyMHB4Ow0KfQ0KDQovKiDpooTop4jljLrln5/moLflvI8gKi8NCi5wcmV2aWV3LWFyZWEgew0KICBtYXJnaW4tYm90dG9tOiAwOw0KfQ0KDQoucHJldmlldy1hcmVhIC5zZWN0aW9uLXRpdGxlIHsNCiAgbWFyZ2luLWJvdHRvbTogMTVweDsNCn0NCg0KLnByZXZpZXctd3JhcHBlciB7DQogIHBhZGRpbmc6IDA7DQp9DQoNCi5wcmV2aWV3LXRleHRhcmVhIHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi5wcmV2aWV3LXRleHRhcmVhIDo6di1kZWVwIC5lbC10ZXh0YXJlYV9faW5uZXIgew0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7DQogIGZvbnQtZmFtaWx5OiBpbmhlcml0Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGxpbmUtaGVpZ2h0OiAxLjU7DQogIG1pbi1oZWlnaHQ6IDEyMHB4Ow0KfQ0KDQovKiDlvLnnqpfmoIfpopjlsYXkuK0gKi8NCjo6di1kZWVwIC5lbC1kaWFsb2dfX2hlYWRlciB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCjo6di1kZWVwIC5lbC1kaWFsb2dfX3RpdGxlIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICB3aWR0aDogMTAwJTsNCiAgZGlzcGxheTogYmxvY2s7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQovKiDkuJPpl6jnmoTlvLnnqpfmoLflvI8gKi8NCjo6di1kZWVwIC5iYXNpcy1kaWFsb2cgew0KICBtYXJnaW4tdG9wOiA1dmggIWltcG9ydGFudDsNCn0NCg0KOjp2LWRlZXAgLmJhc2lzLWRpYWxvZyAuZWwtZGlhbG9nX19ib2R5IHsNCiAgcGFkZGluZzogMjVweDsNCiAgbWF4LWhlaWdodDogNzV2aDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCn0NCg0KOjp2LWRlZXAgLmJhc2lzLWRpYWxvZyAuZWwtZGlhbG9nX19oZWFkZXIgew0KICBwYWRkaW5nOiAyMHB4IDI1cHggMTVweDsNCn0NCg0KOjp2LWRlZXAgLmJhc2lzLWRpYWxvZyAuZWwtZGlhbG9nX19mb290ZXIgew0KICBwYWRkaW5nOiAxNXB4IDI1cHggMjBweDsNCiAgdGV4dC1hbGlnbjogcmlnaHQ7DQp9DQoNCi8qIOW6lemDqOaMiemSruagt+W8jyAqLw0KLmZvb3Rlci1idXR0b25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCiAgZ2FwOiAxMHB4Ow0KfQ0KDQovKiDlpJrpgInmoYbnu4TmoLflvI8gKi8NCjo6di1kZWVwIC5lbC1jaGVja2JveC1ncm91cCB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQo6OnYtZGVlcCAuZWwtY2hlY2tib3ggew0KICBtYXJnaW4tcmlnaHQ6IDA7DQogIG1hcmdpbi1ib3R0b206IDA7DQp9DQoNCjo6di1kZWVwIC5lbC1jaGVja2JveF9fbGFiZWwgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICB3aWR0aDogMTAwJTsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCn0NCg0KOjp2LWRlZXAgLmVsLWNoZWNrYm94X19pbnB1dC5pcy1jaGVja2VkICsgLmVsLWNoZWNrYm94X19sYWJlbCB7DQogIGNvbG9yOiAjNDA5RUZGOw0KfQ0KDQovKiDlv4XloavmoIfor4bnrKbmoLflvI8gKi8NCi5yZXF1aXJlZC1tYXJrIHsNCiAgY29sb3I6ICNGNTZDNkM7DQogIG1hcmdpbi1yaWdodDogNHB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg=="}, {"version": 3, "sources": ["punishmentBasis-module.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgXA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "punishmentBasis-module.vue", "sourceRoot": "src/views/suppPunishment", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    title=\"处罚依据选择\"\r\n    :visible.sync=\"visible\"\r\n    width=\"500px\"\r\n    top=\"5vh\"\r\n    append-to-body\r\n    @close=\"handleClose\"\r\n    :close-on-click-modal=\"false\"\r\n    custom-class=\"basis-dialog\"\r\n  >\r\n    <div class=\"basis-dialog-content\">\r\n      <!-- 处罚依据选项 -->\r\n      <div class=\"basis-options\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>选择依据类型：</h4>\r\n        <el-checkbox-group v-model=\"selectedBasisTypes\" @change=\"handleBasisTypeChange\">\r\n          <!-- 质量异议单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"checkbox-wrapper\">\r\n                <el-checkbox label=\"quality\" @change=\"handleBasisTypeChange\">质量异议单号</el-checkbox>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"qualityNumber\"\r\n                  placeholder=\"请输入质量异议单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  :disabled=\"!selectedBasisTypes.includes('quality')\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 文件报批单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"checkbox-wrapper\">\r\n                <el-checkbox label=\"report\" @change=\"handleBasisTypeChange\">文件报批单号</el-checkbox>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"reportName\"\r\n                  placeholder=\"请输入文件报批单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  :disabled=\"!selectedBasisTypes.includes('report')\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 巡检处罚单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"checkbox-wrapper\">\r\n                <el-checkbox label=\"inspection\" @change=\"handleBasisTypeChange\">巡检处罚单号</el-checkbox>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"inspectionNumber\"\r\n                  placeholder=\"请输入巡检处罚单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  :disabled=\"!selectedBasisTypes.includes('inspection')\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 安管处罚单号 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"checkbox-wrapper\">\r\n                <el-checkbox label=\"safety\" @change=\"handleBasisTypeChange\">安管处罚单号</el-checkbox>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"safetyNumber\"\r\n                  placeholder=\"请输入安管处罚单号\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  :disabled=\"!selectedBasisTypes.includes('safety')\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <!-- 制度名称 -->\r\n          <div class=\"basis-item\">\r\n            <div class=\"basis-row\">\r\n              <div class=\"checkbox-wrapper\">\r\n                <el-checkbox label=\"system\" @change=\"handleBasisTypeChange\">制度名称</el-checkbox>\r\n              </div>\r\n              <div class=\"input-wrapper\">\r\n                <el-input\r\n                  v-model=\"systemName\"\r\n                  placeholder=\"请输入制度名称\"\r\n                  class=\"aligned-input\"\r\n                  @input=\"updateBasisText\"\r\n                  :disabled=\"!selectedBasisTypes.includes('system')\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-checkbox-group>\r\n      </div>\r\n\r\n      <!-- 依据内容 -->\r\n      <div class=\"basis-content\">\r\n        <h4 class=\"section-title\"><span class=\"required-mark\">*</span>依据内容：</h4>\r\n        <div class=\"content-wrapper\">\r\n          <el-input\r\n            v-model=\"basisContent\"\r\n            type=\"textarea\"\r\n            :rows=\"4\"\r\n            placeholder=\"请输入依据内容\"\r\n            @input=\"updateBasisText\"\r\n            class=\"content-textarea\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n\r\n    <!-- 底部按钮 -->\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <div class=\"footer-buttons\">\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">确 定</el-button>\r\n        <el-button @click=\"handleClose\">取 消</el-button>\r\n      </div>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"PunishmentBasisDialog\",\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      visible: false,\r\n      // 选中的依据类型（多选）\r\n      selectedBasisTypes: [],\r\n      // 质量异议单号\r\n      qualityNumber: '',\r\n      // 制度名称\r\n      systemName: '',\r\n      // 报告名称\r\n      reportName: '',\r\n      // 巡检处罚单号\r\n      inspectionNumber: '',\r\n      // 安管处罚单号\r\n      safetyNumber: '',\r\n      // 依据内容\r\n      basisContent: '',\r\n      // 预览文本\r\n      previewText: ''\r\n    };\r\n  },\r\n  methods: {\r\n    /** 显示弹窗 */\r\n    show(currentValue = '') {\r\n      this.visible = true;\r\n      this.parseCurrentValue(currentValue);\r\n      this.updatePreview();\r\n      // 确保弹窗完全打开后再进行其他操作\r\n      this.$nextTick(() => {\r\n        console.log('弹窗已显示，当前数据：', {\r\n          selectedBasisTypes: this.selectedBasisTypes,\r\n          qualityNumber: this.qualityNumber,\r\n          systemName: this.systemName,\r\n          reportName: this.reportName,\r\n          inspectionNumber: this.inspectionNumber,\r\n          safetyNumber: this.safetyNumber,\r\n          basisContent: this.basisContent\r\n        });\r\n      });\r\n    },\r\n    \r\n    /** 隐藏弹窗 */\r\n    hide() {\r\n      this.visible = false;\r\n    },\r\n    \r\n    /** 关闭弹窗 */\r\n    handleClose() {\r\n      this.visible = false;\r\n      this.reset();\r\n    },\r\n    \r\n    /** 重置数据 */\r\n    reset() {\r\n      this.selectedBasisTypes = [];\r\n      this.qualityNumber = '';\r\n      this.systemName = '';\r\n      this.reportName = '';\r\n      this.inspectionNumber = '';\r\n      this.safetyNumber = '';\r\n      this.basisContent = '';\r\n      this.previewText = '';\r\n    },\r\n    \r\n    /** 解析当前值 */\r\n    parseCurrentValue(value) {\r\n      if (!value) {\r\n        this.reset();\r\n        return;\r\n      }\r\n\r\n      // 重置选中的类型数组\r\n      this.selectedBasisTypes = [];\r\n\r\n      // 尝试解析现有的依据内容（支持多个类型）\r\n      if (value.includes('质量异议单号：')) {\r\n        this.selectedBasisTypes.push('quality');\r\n        const match = value.match(/质量异议单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.qualityNumber = match[1].trim();\r\n        }\r\n      }\r\n\r\n      if (value.includes('制度名称：')) {\r\n        this.selectedBasisTypes.push('system');\r\n        const match = value.match(/制度名称：([^；\\n]*)/);\r\n        if (match) {\r\n          this.systemName = match[1].trim();\r\n        }\r\n      }\r\n\r\n      if (value.includes('报告：')) {\r\n        this.selectedBasisTypes.push('report');\r\n        const match = value.match(/报告：([^；\\n]*)/);\r\n        if (match) {\r\n          this.reportName = match[1].trim();\r\n        }\r\n      }\r\n\r\n      if (value.includes('巡检处罚单号：')) {\r\n        this.selectedBasisTypes.push('inspection');\r\n        const match = value.match(/巡检处罚单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.inspectionNumber = match[1].trim();\r\n        }\r\n      }\r\n\r\n      if (value.includes('安管处罚单号：')) {\r\n        this.selectedBasisTypes.push('safety');\r\n        const match = value.match(/安管处罚单号：([^；\\n]*)/);\r\n        if (match) {\r\n          this.safetyNumber = match[1].trim();\r\n        }\r\n      }\r\n\r\n      // 解析依据内容\r\n      const contentMatch = value.match(/依据内容：([^]*)/);\r\n      if (contentMatch) {\r\n        this.basisContent = contentMatch[1].trim();\r\n      } else {\r\n        // 如果没有找到依据内容标识，将整个内容作为依据内容\r\n        this.basisContent = value;\r\n      }\r\n    },\r\n    \r\n    /** 依据类型变化 */\r\n    handleBasisTypeChange() {\r\n      this.updatePreview();\r\n    },\r\n    \r\n    /** 更新依据文本 */\r\n    updateBasisText() {\r\n      this.updatePreview();\r\n    },\r\n\r\n\r\n    \r\n    /** 更新预览 */\r\n    updatePreview() {\r\n      const parts = [];\r\n\r\n      // 添加选中的依据类型信息（支持多选）\r\n      if (this.selectedBasisTypes.includes('quality') && this.qualityNumber) {\r\n        parts.push(`质量异议单号：${this.qualityNumber}`);\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('system') && this.systemName) {\r\n        parts.push(`制度名称：${this.systemName}`);\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('report') && this.reportName) {\r\n        parts.push(`报告：${this.reportName}`);\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('inspection') && this.inspectionNumber) {\r\n        parts.push(`巡检处罚单号：${this.inspectionNumber}`);\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('safety') && this.safetyNumber) {\r\n        parts.push(`安管处罚单号：${this.safetyNumber}`);\r\n      }\r\n\r\n      // 添加依据内容\r\n      if (this.basisContent) {\r\n        parts.push(`依据内容：${this.basisContent}`);\r\n      }\r\n\r\n      this.previewText = parts.join('；');\r\n\r\n      console.log('预览更新：', {\r\n        selectedBasisTypes: this.selectedBasisTypes,\r\n        qualityNumber: this.qualityNumber,\r\n        systemName: this.systemName,\r\n        reportName: this.reportName,\r\n        inspectionNumber: this.inspectionNumber,\r\n        safetyNumber: this.safetyNumber,\r\n        basisContent: this.basisContent,\r\n        previewText: this.previewText\r\n      });\r\n    },\r\n    \r\n    /** 确认选择 */\r\n    handleConfirm() {\r\n      this.updatePreview();\r\n\r\n      // 验证是否填写了必要信息\r\n      if (this.selectedBasisTypes.length === 0) {\r\n        this.$message.warning('请至少选择一个依据类型');\r\n        return;\r\n      }\r\n\r\n      // 验证选中的每个类型是否都填写了对应的内容\r\n      if (this.selectedBasisTypes.includes('quality') && !this.qualityNumber) {\r\n        this.$message.warning('请输入质量异议单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('system') && !this.systemName) {\r\n        this.$message.warning('请输入制度名称');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('report') && !this.reportName) {\r\n        this.$message.warning('请输入文件报批单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('inspection') && !this.inspectionNumber) {\r\n        this.$message.warning('请输入巡检处罚单号');\r\n        return;\r\n      }\r\n\r\n      if (this.selectedBasisTypes.includes('safety') && !this.safetyNumber) {\r\n        this.$message.warning('请输入安管处罚单号');\r\n        return;\r\n      }\r\n\r\n      if (!this.basisContent) {\r\n        this.$message.warning('请输入依据内容');\r\n        return;\r\n      }\r\n\r\n      this.$emit('select', this.previewText);\r\n      this.handleClose();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 弹窗内容容器 */\r\n.basis-dialog-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 章节标题样式 */\r\n.section-title {\r\n  font-size: 16px;\r\n  color: #303133;\r\n  margin: 0 0 15px 0;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 顶级标题样式（选择依据类型） */\r\n.basis-options .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 处罚依据选项样式 */\r\n.basis-options {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-item {\r\n  margin-bottom: 15px;\r\n  padding: 0;\r\n}\r\n\r\n/* 新的行布局 */\r\n.basis-row {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  min-height: 36px;\r\n}\r\n\r\n.checkbox-wrapper {\r\n  width: 120px;\r\n  flex-shrink: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  height: 36px;\r\n}\r\n\r\n.input-wrapper {\r\n  width: calc(100% - 135px);\r\n  margin-left: 14px;\r\n}\r\n\r\n.aligned-input {\r\n  width: 100%;\r\n}\r\n\r\n.aligned-input ::v-deep .el-input__inner {\r\n  height: 36px;\r\n  line-height: 36px;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 多选框对齐样式 */\r\n.checkbox-wrapper ::v-deep .el-checkbox {\r\n  height: 36px;\r\n  display: flex;\r\n  align-items: center;\r\n  margin: 0;\r\n}\r\n\r\n.checkbox-wrapper ::v-deep .el-checkbox__input {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 8px;\r\n}\r\n\r\n.checkbox-wrapper ::v-deep .el-checkbox__inner {\r\n  width: 16px;\r\n  height: 16px;\r\n}\r\n\r\n.checkbox-wrapper ::v-deep .el-checkbox__label {\r\n  font-size: 14px;\r\n  line-height: 36px;\r\n  padding-left: 8px;\r\n  color: #606266;\r\n}\r\n\r\n/* 依据内容区域样式 */\r\n.basis-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.basis-content .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.content-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.content-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.content-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 预览区域样式 */\r\n.preview-area {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.preview-area .section-title {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.preview-wrapper {\r\n  padding: 0;\r\n}\r\n\r\n.preview-textarea {\r\n  width: 100%;\r\n}\r\n\r\n.preview-textarea ::v-deep .el-textarea__inner {\r\n  border-radius: 4px;\r\n  background-color: #ffffff;\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  min-height: 120px;\r\n}\r\n\r\n/* 弹窗标题居中 */\r\n::v-deep .el-dialog__header {\r\n  text-align: center;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n::v-deep .el-dialog__title {\r\n  text-align: center;\r\n  width: 100%;\r\n  display: block;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n/* 专门的弹窗样式 */\r\n::v-deep .basis-dialog {\r\n  margin-top: 5vh !important;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__body {\r\n  padding: 25px;\r\n  max-height: 75vh;\r\n  overflow-y: auto;\r\n  background-color: #ffffff;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__header {\r\n  padding: 20px 25px 15px;\r\n}\r\n\r\n::v-deep .basis-dialog .el-dialog__footer {\r\n  padding: 15px 25px 20px;\r\n  text-align: right;\r\n}\r\n\r\n/* 底部按钮样式 */\r\n.footer-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n}\r\n\r\n/* 多选框组样式 */\r\n::v-deep .el-checkbox-group {\r\n  width: 100%;\r\n}\r\n\r\n::v-deep .el-checkbox {\r\n  margin-right: 0;\r\n  margin-bottom: 0;\r\n}\r\n\r\n::v-deep .el-checkbox__label {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {\r\n  color: #409EFF;\r\n}\r\n\r\n/* 必填标识符样式 */\r\n.required-mark {\r\n  color: #F56C6C;\r\n  margin-right: 4px;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"]}]}