package com.ruoyi.web.controller.dgcb;

import com.google.common.collect.Lists;
import com.ruoyi.app.dgcb.domain.DgcbDriver;
import com.ruoyi.app.dgcb.service.IDgcbDriverService;
import com.ruoyi.app.domain.XctgDriverCar;
import com.ruoyi.app.domain.XctgDriverUser;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 吨钢承包司机信息Controller
 * 
 * <AUTHOR>
 * @date 2024-04-15
 */
@RestController
@RequestMapping("/web/driver")
public class WebDgcbDriverController extends BaseController
{
    public static final String DGCB_GYS_ROEL_KEY = "dgcb-gys";
    public static final String FG_GYS_ROEL_KEY = "fg-gys";
    public static final String DGCB_GLY_ROEL_KEY = "gly";
    public static final String DGCB_ADMIN_ROEL_KEY = "admin";
    public static final String BUSINESS_APPROVER_ROLE_KEY = "truck.scrapSteel.business";
    //货车业务-治安保卫处
    public static final String TRUCK_POLICE_VIEW = "truck.police.view";

    @Autowired
    private IDgcbDriverService dgcbDriverService;

    @Autowired
    private ISysRoleService sysRoleService;

    /**
     * 查询吨钢承包司机信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DgcbDriver dgcbDriver)
    {
        String workNo = SecurityUtils.getUsername();
        boolean isDgcbGys = sysRoleService.selectRoleExistByUserName(workNo, DGCB_GYS_ROEL_KEY);
        boolean isFgGys = sysRoleService.selectRoleExistByUserName(workNo, FG_GYS_ROEL_KEY);
        boolean isAdmin = sysRoleService.selectRoleExistByUserName(workNo, Lists.newArrayList(DGCB_GLY_ROEL_KEY, DGCB_ADMIN_ROEL_KEY,BUSINESS_APPROVER_ROLE_KEY));
        boolean isPolice = sysRoleService.selectRoleExistByUserName(workNo, TRUCK_POLICE_VIEW);

        //既不是供应商也不是管理员
        if(!isDgcbGys && !isFgGys && !isAdmin && !isPolice){
            return getDataTable(Lists.newArrayList());
        }

        //管理员
        if(isAdmin || isPolice){
            startPage();
            List<DgcbDriver> list = dgcbDriverService.selectDgcbDriverList(dgcbDriver);
            return getDataTable(list);
        }

        //供应商
        startPage();
        dgcbDriver.setCreateBy(workNo);
        List<DgcbDriver> list = dgcbDriverService.selectDgcbDriverList(dgcbDriver);
        return getDataTable(list);
    }

    @GetMapping("/getAllList")
    public AjaxResult getAllList(DgcbDriver dgcbDriver)
    {
        String workNo = SecurityUtils.getUsername();
        boolean isDgcbGys = sysRoleService.selectRoleExistByUserName(workNo, DGCB_GYS_ROEL_KEY);
        boolean isFgGys = sysRoleService.selectRoleExistByUserName(workNo, FG_GYS_ROEL_KEY);
        boolean isAdmin = sysRoleService.selectRoleExistByUserName(workNo, Lists.newArrayList(DGCB_GLY_ROEL_KEY, DGCB_ADMIN_ROEL_KEY));

        //既不是供应商也不是管理员
        if(!isDgcbGys && !isFgGys && !isAdmin){
            return AjaxResult.success(Lists.newArrayList());
        }

        //管理员
        if(isAdmin){
            List<DgcbDriver> list = dgcbDriverService.selectDgcbDriverList(dgcbDriver);
            return AjaxResult.success(list);
        }

        //供应商
        dgcbDriver.setCreateBy(workNo);
        List<DgcbDriver> list = dgcbDriverService.selectDgcbDriverList(dgcbDriver);
        return AjaxResult.success(list);
    }

    @GetMapping("/getXctgDriverUserList")
    public AjaxResult getXctgDriverUserList(XctgDriverUser xctgDriverUser)
    {
        String workNo = SecurityUtils.getUsername();
        boolean isDgcbGys = sysRoleService.selectRoleExistByUserName(workNo, DGCB_GYS_ROEL_KEY);
        boolean isFgGys = sysRoleService.selectRoleExistByUserName(workNo, FG_GYS_ROEL_KEY);
        boolean isAdmin = sysRoleService.selectRoleExistByUserName(workNo, Lists.newArrayList(DGCB_GLY_ROEL_KEY, DGCB_ADMIN_ROEL_KEY));

        //既不是供应商也不是管理员
        if(!isDgcbGys && !isFgGys && !isAdmin){
            return AjaxResult.success(Lists.newArrayList());
        }

        //管理员
        if(isAdmin){
            List<XctgDriverUser> list = dgcbDriverService.selectXctgDriverUserList(xctgDriverUser);
            return AjaxResult.success(list);
        }

        //供应商
        xctgDriverUser.setOpenId(workNo);
        List<XctgDriverUser> list = dgcbDriverService.selectXctgDriverUserList(xctgDriverUser);
        return AjaxResult.success(list);
    }

    @GetMapping("/getXctgDriverCarList")
    public AjaxResult getXctgDriverCarList(XctgDriverCar xctgDriverCar)
    {
        String workNo = SecurityUtils.getUsername();
        boolean isDgcbGys = sysRoleService.selectRoleExistByUserName(workNo, DGCB_GYS_ROEL_KEY);
        boolean isFgGys = sysRoleService.selectRoleExistByUserName(workNo, FG_GYS_ROEL_KEY);
        boolean isAdmin = sysRoleService.selectRoleExistByUserName(workNo, Lists.newArrayList(DGCB_GLY_ROEL_KEY, DGCB_ADMIN_ROEL_KEY));

        //既不是供应商也不是管理员
        if(!isDgcbGys && !isFgGys && !isAdmin){
            return AjaxResult.success(Lists.newArrayList());
        }

        //管理员
        if(isAdmin){
            List<XctgDriverCar> list = dgcbDriverService.selectXctgDriverCarList(xctgDriverCar);
            return AjaxResult.success(list);
        }

        //供应商
        xctgDriverCar.setOpenId(workNo);
        List<XctgDriverCar> list = dgcbDriverService.selectXctgDriverCarList(xctgDriverCar);
        return AjaxResult.success(list);
    }

    @GetMapping("/getAllListByCompanyId")
    public AjaxResult getAllListByCompanyId(DgcbDriver dgcbDriver)
    {
        dgcbDriver.setCreateBy(dgcbDriver.getQueryCompanyId());
        List<DgcbDriver> list = dgcbDriverService.selectDgcbDriverList(dgcbDriver);
        return AjaxResult.success(list);
    }

    /**
     * 导出吨钢承包司机信息列表
     */
    @Log(title = "吨钢承包司机信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(DgcbDriver dgcbDriver)
    {
        List<DgcbDriver> list = dgcbDriverService.selectDgcbDriverList(dgcbDriver);
        ExcelUtil<DgcbDriver> util = new ExcelUtil<DgcbDriver>(DgcbDriver.class);
        return util.exportExcel(list, "driver");
    }

    /**
     * 获取吨钢承包司机信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(dgcbDriverService.selectDgcbDriverById(id));
    }

    /**
     * 新增吨钢承包司机信息
     */
    @Log(title = "吨钢承包司机信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DgcbDriver dgcbDriver)
    {
        return toAjax(dgcbDriverService.insertDgcbDriver(dgcbDriver));
    }

    /**
     * 修改吨钢承包司机信息
     */
    @Log(title = "吨钢承包司机信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DgcbDriver dgcbDriver)
    {
        return toAjax(dgcbDriverService.updateDgcbDriver(dgcbDriver));
    }

    /**
     * 删除吨钢承包司机信息
     */
    @Log(title = "吨钢承包司机信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dgcbDriverService.deleteDgcbDriverByIds(ids));
    }

    @PostMapping("/syncTruckCar")
    public AjaxResult syncTruckCar() throws Exception {
        dgcbDriverService.syncTruckCar();
        return AjaxResult.success();
    }


//    @PostMapping("/driverLicenseOCR")
//    public AjaxResult driverLicenseOCR(@RequestBody String imageUrl) {
//
//
//        String result = this.dgcbDriverService.driverLicenseOCR(imageUrl);
//
//        return AjaxResult.success(result);
//    }
//
//    @PostMapping("/vehicleLicenseOCR")
//    public AjaxResult vehicleLicenseOCR(@RequestBody String imageUrl) {
//        String result = this.dgcbDriverService.vehicleLicenseOCR(imageUrl);
//
//        return AjaxResult.success(result);
//    }








}
