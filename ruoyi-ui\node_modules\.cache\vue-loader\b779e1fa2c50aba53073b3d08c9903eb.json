{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\check\\list.vue", "mtime": 1756456282469}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RUb0NoZWNrLCBsaXN0Q2hlY2tlZCwgZ2V0SW5mbywgY2hlY2ssIGJhdGNoUXVpY2tTY29yZSB9IGZyb20gIkAvYXBpL2Fzc2Vzcy9zZWxmL2luZm8iCmltcG9ydCB7IGdldENoZWNrRGVwdExpc3QgfSBmcm9tICJAL2FwaS9hc3Nlc3Mvc2VsZi91c2VyIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiU2VsZkFzc2Vzc0NoZWNrIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmmL7npLrliJfooajkuovkuJrpg6jor4TliIYKICAgICAgc2hvd0J1c2luZXNzOmZhbHNlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIGNoZWNrZWRUb3RhbDogMCwKICAgICAgLy8g57up5pWI6ICD5qC4LeW5sumDqOiHquivhOS6uuWRmOmFjee9ruihqOagvOaVsOaNrgogICAgICBsaXN0VG9DaGVjazogW10sCiAgICAgIGxpc3RDaGVja2VkOiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICB3b3JrTm86IG51bGwsCiAgICAgICAgbmFtZTpudWxsLAogICAgICAgIGRlcHRJZDpudWxsLAogICAgICAgIGFzc2Vzc0RhdGU6bnVsbAogICAgICB9LAogICAgICAvLyDor4TliIborrDlvZXmn6Xor6Llj4LmlbAKICAgICAgY2hlY2tlZFF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgd29ya05vOiBudWxsLAogICAgICAgIG5hbWU6bnVsbCwKICAgICAgICBkZXB0SWQ6bnVsbCwKICAgICAgICBhc3Nlc3NEYXRlOm51bGwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHsKICAgICAgICBpZDpudWxsLAogICAgICAgIC8vIOmDqOmXqOmihuWvvOWKoOWHj+WIhgogICAgICAgIGRlcHRBZGRTY29yZTpudWxsLAogICAgICAgIC8vIOS6i+S4mumDqOWKoOWHj+WIhgogICAgICAgIGJ1c2luZXNzQWRkU2NvcmU6bnVsbCwKICAgICAgICAvLyDpg6jpl6jpooblr7zor4TliIbnkIbnlLEKICAgICAgICBkZXB0U2NvcmVSZWFzb246bnVsbCwKICAgICAgICAvLyDkuovkuJrpg6jor4TliIbnkIbnlLEKICAgICAgICBidXNpbmVzc1Njb3JlUmVhc29uOm51bGwsCiAgICAgIH0sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICB9LAogICAgICBkZXB0T3B0aW9uczpbXSwKICAgICAgb3BlbkNoZWNrOmZhbHNlLAogICAgICBjaGVja0luZm86e30sCiAgICAgIC8vIOWQiOW5tuWNleWFg+agvAogICAgICBzcGFuTGlzdDpbXSwKICAgICAgLy8g5b6F6K+E5YiG5qCH562+CiAgICAgIHRvQ2hlY2tMYWJlbDoi5b6F6K+E5YiGKDApIiwKICAgICAgLy8g5b+r6YCf6K+E5YiG5o+Q5Lqk54q25oCBCiAgICAgIHN1Ym1pdHRpbmc6IGZhbHNlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgbXVsdGlwbGVTZWxlY3Rpb246IFtdLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaJuemHj+W/q+mAn+ivhOWIhuWvueivneahhuaYvuekuueKtuaAgQogICAgICBxdWlja1Njb3JlRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIC8vIOaJuemHj+W/q+mAn+ivhOWIhuihqOWNleWPguaVsAogICAgICBiYXRjaFF1aWNrU2NvcmVGb3JtOiB7CiAgICAgICAgc2NvcmU6IHVuZGVmaW5lZCwKICAgICAgICBpZHM6IFtdCiAgICAgIH0sCiAgICAgIC8vIOaJuemHj+W/q+mAn+ivhOWIhuihqOWNlemqjOivgeinhOWImQogICAgICBiYXRjaFF1aWNrU2NvcmVSdWxlczogewogICAgICAgIHNjb3JlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+E5YiG5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sCiAgICAgICAgICB7IHR5cGU6ICdudW1iZXInLCBtZXNzYWdlOiAi6K+E5YiG5b+F6aG75Li65pWw5a2XIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8vIOaJuemHj+W/q+mAn+ivhOWIhuWvueivneahhgogICAgICBiYXRjaFF1aWNrU2NvcmVPcGVuOiBmYWxzZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmAieS4reeahOihjOaVsOaNrgogICAgICBzZWxlY3RlZFJvd3M6IFtdLAogICAgfTsKICB9LAogICAgY29tcHV0ZWQ6IHsKICAvLyDmmK/lkKblj6/ku6Xmj5DkuqTmibnph4/or4TliIbvvIjln7rnoYDmo4Dmn6XvvIkKICBjYW5TdWJtaXRCYXRjaFNjb3JlKCkgewogICAgaWYgKHRoaXMuc2VsZWN0ZWRSb3dzLmxlbmd0aCA9PT0gMCkgcmV0dXJuIGZhbHNlOwogICAgCiAgICAvLyDln7rnoYDmo4Dmn6XvvJrmmK/lkKbmiYDmnInooYzpg73loavlhpnkuobliqDlh4/liIYKICAgIGZvciAobGV0IHJvdyBvZiB0aGlzLnNlbGVjdGVkUm93cykgewogICAgICBpZiAocm93LnF1aWNrQWRkU2NvcmUgPT09IG51bGwgfHwgcm93LnF1aWNrQWRkU2NvcmUgPT09IHVuZGVmaW5lZCkgewogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgfQogICAgCiAgICByZXR1cm4gdHJ1ZTsKICB9Cn0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMucXVlcnlQYXJhbXMuYXNzZXNzRGF0ZSA9IHRoaXMuZ2V0RGVmYXVsdEFzc2Vzc0RhdGUoKQogICAgdGhpcy5jaGVja2VkUXVlcnlQYXJhbXMuYXNzZXNzRGF0ZSA9IHRoaXMuZ2V0RGVmYXVsdEFzc2Vzc0RhdGUoKQogICAgLy8gdGhpcy5nZXRTZWxmQXNzZXNzVXNlcigpOwogICAgdGhpcy5nZXRDaGVja0RlcHRMaXN0KCk7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIHRoaXMuZ2V0Q2hlY2tlZExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKCiAgICAvLyDojrflj5bpu5jorqTogIPmoLjml6XmnJ8KICAgIGdldERlZmF1bHRBc3Nlc3NEYXRlKCkgewogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICBjb25zdCBjdXJyZW50RGF5ID0gbm93LmdldERhdGUoKTsKCiAgICAgIGxldCB0YXJnZXREYXRlOwogICAgICBpZiAoY3VycmVudERheSA8IDEwKSB7CiAgICAgICAgLy8g5b2T5YmN5pel5pyf5bCP5LqOMTDml6XvvIzpu5jorqTkuLrkuIrkuKrmnIgKICAgICAgICB0YXJnZXREYXRlID0gbmV3IERhdGUobm93LmdldEZ1bGxZZWFyKCksIG5vdy5nZXRNb250aCgpIC0gMSwgMSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5b2T5YmN5pel5pyf5aSn5LqO562J5LqOMTDml6XvvIzpu5jorqTkuLrlvZPmnIgKICAgICAgICB0YXJnZXREYXRlID0gbmV3IERhdGUobm93LmdldEZ1bGxZZWFyKCksIG5vdy5nZXRNb250aCgpLCAxKTsKICAgICAgfQoKICAgICAgLy8g5qC85byP5YyW5Li6IFlZWVktTSDmoLzlvI8KICAgICAgY29uc3QgeWVhciA9IHRhcmdldERhdGUuZ2V0RnVsbFllYXIoKTsKICAgICAgY29uc3QgbW9udGggPSB0YXJnZXREYXRlLmdldE1vbnRoKCkgKyAxOwogICAgICByZXR1cm4gYCR7eWVhcn0tJHttb250aH1gOwogICAgfSwKCiAgICAvLyDojrflj5bpg6jpl6jkv6Hmga8KICAgIGdldENoZWNrRGVwdExpc3QoKXsKICAgICAgZ2V0Q2hlY2tEZXB0TGlzdCgpLnRoZW4ocmVzID0+IHsKICAgICAgICBjb25zb2xlLmxvZyhyZXMpCiAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsKICAgICAgICAgIGxldCBkZXB0T3B0aW9ucyA9IFtdOwogICAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgZGVwdE9wdGlvbnMucHVzaCh7CiAgICAgICAgICAgICAgZGVwdE5hbWU6aXRlbS5kZXB0TmFtZSwKICAgICAgICAgICAgICBkZXB0SWQ6aXRlbS5kZXB0SWQKICAgICAgICAgICAgfSkKICAgICAgICAgIH0pCiAgICAgICAgICB0aGlzLmRlcHRPcHRpb25zID0gZGVwdE9wdGlvbnM7CiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8qKiDmn6Xor6Lnu6nmlYjogIPmoLgt5bmy6YOo6Ieq6K+E5b6F5a6h5qC45YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0VG9DaGVjayh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLmxpc3RUb0NoZWNrID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy50b0NoZWNrTGFiZWwgPSBg5b6F6K+E5YiGKCR7cmVzcG9uc2UudG90YWx9KWAKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB0aGlzLnNob3VsZEJ1c2luZXNzRGlzcGxheSgpOwogICAgICB9KTsKICAgIH0sCgogICAgc2hvdWxkQnVzaW5lc3NEaXNwbGF5KCl7CiAgICAgIHRoaXMuc2hvd0J1c2luZXNzID0gdGhpcy5saXN0VG9DaGVjay5zb21lKHJvdyA9PiByb3dbInN0YXR1cyJdID09ICcyJykKICAgIH0sCiAgICAKICAgIC8qKiDojrflj5blt7LlrqHmoLjliJfooaggKi8KICAgIGdldENoZWNrZWRMaXN0KCl7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RDaGVja2VkKHRoaXMuY2hlY2tlZFF1ZXJ5UGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5saXN0Q2hlY2tlZCA9IHJlcy5yb3dzOwogICAgICAgIHRoaXMuY2hlY2tlZFRvdGFsID0gcmVzLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KQogICAgfSwKCiAgICAvLyDlj5bmtojmjInpkq4KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgaWQ6IG51bGwsCiAgICAgICAgZGVwdEFkZFNjb3JlOiBudWxsLAogICAgICAgIGJ1c2luZXNzQWRkU2NvcmU6IG51bGwsCiAgICAgICAgZGVwdFNjb3JlUmVhc29uOiBudWxsLAogICAgICAgIGJ1c2luZXNzU2NvcmVSZWFzb246IG51bGwsCiAgICAgIH07CiAgICAgIC8vIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuY2hlY2tlZFF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICAvLyDlkIzmraXmkJzntKLmnaHku7YKICAgICAgdGhpcy5jaGVja2VkUXVlcnlQYXJhbXMubmFtZSA9IHRoaXMucXVlcnlQYXJhbXMubmFtZTsKICAgICAgdGhpcy5jaGVja2VkUXVlcnlQYXJhbXMuZGVwdElkID0gdGhpcy5xdWVyeVBhcmFtcy5kZXB0SWQ7CiAgICAgIHRoaXMuY2hlY2tlZFF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGUgPSB0aGlzLnF1ZXJ5UGFyYW1zLmFzc2Vzc0RhdGU7CiAgICAgIHRoaXMuZ2V0Q2hlY2tlZExpc3QoKTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAoKICAgIC8vIOWuoeaJueivpuaDhQogICAgaGFuZGxlQ2hlY2tEZXRhaWwocm93KXsKICAgICAgZ2V0SW5mbyh7aWQ6cm93LmlkfSkudGhlbihyZXMgPT4gewogICAgICAgIGNvbnNvbGUubG9nKHJlcyk7CiAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsKICAgICAgICAgIHRoaXMuY2hlY2tJbmZvID0gcmVzLmRhdGE7CiAgICAgICAgICBsZXQgbGlzdCA9IEpTT04ucGFyc2UocmVzLmRhdGEuY29udGVudCk7CiAgICAgICAgICB0aGlzLmhhbmRsZVNwYW5MaXN0KGxpc3QpOwogICAgICAgICAgdGhpcy5jaGVja0luZm8ubGlzdCA9IGxpc3Q7CiAgICAgICAgfQogICAgICAgIHRoaXMub3BlbiA9IHRydWUKICAgICAgfSkKICAgIH0sCgogICAgLy8g5a6h5om55o+Q5LqkCiAgICBjaGVja1N1Ym1pdCgpewogICAgICBpZih0aGlzLnZlcmlmeSgpKXsKICAgICAgICBsZXQgcG9pbnQgPSB0aGlzLmdldERlcHRTY29yZUZyb21Gb3JtKCk7CiAgICAgICAgaWYodGhpcy5jaGVja0luZm8uc3RhdHVzID09ICcyJykgcG9pbnQgPSB0aGlzLmdldEJ1c2luZXNzU2NvcmVGcm9tRm9ybSgpOwogICAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupCcgKyB0aGlzLmNoZWNrSW5mby5uYW1lICsgJ+ivhOWIhuS4uu+8micgKyBwb2ludCArICfliIYnLCAn5o+Q56S6JywgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMub25DaGVjaygpOwogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKCiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCgogICAgb25DaGVjaygpewogICAgICB0aGlzLmZvcm0uaWQgPSB0aGlzLmNoZWNrSW5mby5pZDsKICAgICAgdGhpcy5mb3JtLnN0YXR1cyA9IHRoaXMuY2hlY2tJbmZvLnN0YXR1czsKICAgICAgCiAgICAgIC8vIOiuoeeul+acgOe7iOivhOWIhgogICAgICBpZih0aGlzLmNoZWNrSW5mby5zdGF0dXMgPT0gJzEnKSB7CiAgICAgICAgdGhpcy5mb3JtLmRlcHRTY29yZSA9IHRoaXMuZ2V0RGVwdFNjb3JlRnJvbUZvcm0oKTsKICAgICAgfQogICAgICBpZih0aGlzLmNoZWNrSW5mby5zdGF0dXMgPT0gJzInKSB7CiAgICAgICAgdGhpcy5mb3JtLmJ1c2luZXNzU2NvcmUgPSB0aGlzLmdldEJ1c2luZXNzU2NvcmVGcm9tRm9ybSgpOwogICAgICB9CiAgICAgIAogICAgICBjaGVjayh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICBjb25zb2xlLmxvZyhyZXMpCiAgICAgICAgaWYocmVzLmNvZGUgPT0gMjAwKXsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICfmj5DkuqTmiJDlip8hJwogICAgICAgICAgfSk7CiAgICAgICAgICB0aGlzLnJlc2V0KCk7CiAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgdGhpcy5nZXRDaGVja2VkTGlzdCgpOwogICAgICAgIH1lbHNlewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgICAgbWVzc2FnZTogJ+aTjeS9nOWksei0pe+8jOaXoOadg+mZkOaIluW9k+WJjeWuoeaJueeKtuaAgeS4jeWMuemFjScKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSkKICAgIH0sCgogICAgLy8g5pWw5o2u6aqM6K+BCiAgICB2ZXJpZnkoKXsKICAgICAgaWYodGhpcy5jaGVja0luZm8uc3RhdHVzID09ICcxJyAmJiB0aGlzLmZvcm0uZGVwdEFkZFNjb3JlID09PSBudWxsKXsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgIG1lc3NhZ2U6ICfor7floavlhpnliqDlh4/liIYnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIGlmKHRoaXMuY2hlY2tJbmZvLnN0YXR1cyA9PSAnMicgJiYgdGhpcy5mb3JtLmJ1c2luZXNzQWRkU2NvcmUgPT09IG51bGwpewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+Whq+WGmeWKoOWHj+WIhicKICAgICAgICB9KTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgaWYodGhpcy5jaGVja0luZm8uc3RhdHVzID09ICcxJyAmJiB0aGlzLmZvcm0uZGVwdEFkZFNjb3JlICE9PSAwICYmICF0aGlzLmZvcm0uZGVwdFNjb3JlUmVhc29uKXsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJywKICAgICAgICAgIG1lc3NhZ2U6ICfmnInliqDlh4/liIbml7bor7floavlhpnliqDlh4/liIbnkIbnlLEnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9IAogICAgICBpZih0aGlzLmNoZWNrSW5mby5zdGF0dXMgPT0gJzInICYmIHRoaXMuZm9ybS5idXNpbmVzc0FkZFNjb3JlICE9PSAwICYmICF0aGlzLmZvcm0uYnVzaW5lc3NTY29yZVJlYXNvbil7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnd2FybmluZycsCiAgICAgICAgICBtZXNzYWdlOiAn5pyJ5Yqg5YeP5YiG5pe26K+35aGr5YaZ5Yqg5YeP5YiG55CG55SxJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICByZXR1cm4gdHJ1ZTsKICAgIH0sCgogICAgaGFuZGxlTGlzdENoYW5nZSh0eXBlKXsKICAgICAgY29uc29sZS5sb2codHlwZSkKICAgIH0sCiAgICAvLyDlpITnkIbliJfooagKICAgIGhhbmRsZVNwYW5MaXN0KGRhdGEpewogICAgICBsZXQgc3Bhbkxpc3QgPSBbXTsKICAgICAgbGV0IGZsYWcgPSAwOwogICAgICBmb3IobGV0IGkgPSAwOyBpIDwgZGF0YS5sZW5ndGg7IGkrKyl7CiAgICAgICAgLy8g55u45ZCM6ICD5qC46aG55ZCI5bm2CiAgICAgICAgaWYoaSA9PSAwKXsKICAgICAgICAgIHNwYW5MaXN0LnB1c2goewogICAgICAgICAgICByb3dzcGFuOiAxLAogICAgICAgICAgICBjb2xzcGFuOiAxCiAgICAgICAgICB9KQogICAgICAgIH1lbHNlewogICAgICAgICAgaWYoZGF0YVtpIC0gMV0uaXRlbSA9PSBkYXRhW2ldLml0ZW0pewogICAgICAgICAgICBzcGFuTGlzdC5wdXNoKHsKICAgICAgICAgICAgICByb3dzcGFuOiAwLAogICAgICAgICAgICAgIGNvbHNwYW46IDAKICAgICAgICAgICAgfSkKICAgICAgICAgICAgc3Bhbkxpc3RbZmxhZ10ucm93c3BhbiArPSAxOwogICAgICAgICAgfWVsc2V7CiAgICAgICAgICAgIHNwYW5MaXN0LnB1c2goewogICAgICAgICAgICAgIHJvd3NwYW46IDEsCiAgICAgICAgICAgICAgY29sc3BhbjogMQogICAgICAgICAgICB9KQogICAgICAgICAgICBmbGFnID0gaTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgICAgdGhpcy5zcGFuTGlzdCA9IHNwYW5MaXN0OwogICAgfSwKCiAgICAvLyDlkIjlubbljZXlhYPmoLzmlrnms5UKICAgIG9iamVjdFNwYW5NZXRob2QoeyByb3csIGNvbHVtbiwgcm93SW5kZXgsIGNvbHVtbkluZGV4IH0pIHsKICAgICAgLy8g56ys5LiA5YiX55u45ZCM6aG55ZCI5bm2CiAgICAgIGlmIChjb2x1bW5JbmRleCA9PT0gMCkgewogICAgICAgIHJldHVybiB0aGlzLnNwYW5MaXN0W3Jvd0luZGV4XTsKICAgICAgfQogICAgICAvLyDnsbvliKvml6DlhoXlrrkg5ZCI5bm2CiAgICAgIGlmKGNvbHVtbkluZGV4ID09PSAxKXsKICAgICAgICBpZighcm93LmNhdGVnb3J5KXsKICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIHJvd3NwYW46IDAsCiAgICAgICAgICAgIGNvbHNwYW46IDAKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgICAgaWYoY29sdW1uSW5kZXggPT09IDIpewogICAgICAgIGlmKCFyb3cuY2F0ZWdvcnkpewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgcm93c3BhbjogMSwKICAgICAgICAgICAgY29sc3BhbjogMgogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfSwKCiAgICAvKiog5b+r6YCf6K+E5YiG5o+Q5LqkICovCiAgICBoYW5kbGVRdWlja1N1Ym1pdChyb3cpIHsKICAgICAgaWYgKCFyb3cucXVpY2tTY29yZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36L6T5YWl6K+E5YiGJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOaPkOS6pOivpeivhOWIhuWQl++8nycsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRzZXQocm93LCAnc3VibWl0dGluZycsIHRydWUpOwogICAgICAgIGNvbnN0IGRhdGEgPSB7CiAgICAgICAgICBpZDogcm93LmlkLAogICAgICAgICAgc2NvcmU6IHJvdy5xdWlja1Njb3JlLAogICAgICAgICAgdHlwZTogcm93LnR5cGUKICAgICAgICB9OwogICAgICAgIGNoZWNrKGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfor4TliIbmj5DkuqTmiJDlip8nKTsKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgdGhpcy5nZXRDaGVja2VkTGlzdCgpOwogICAgICAgIH0pLmZpbmFsbHkoKCkgPT4gewogICAgICAgICAgdGhpcy4kc2V0KHJvdywgJ3N1Ym1pdHRpbmcnLCBmYWxzZSk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKCiAgICAvKiog6YCJ5oup5p2h5pWw5pS55Y+YICovCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpCiAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzID0gc2VsZWN0aW9uCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgIH0sCgogICAgLyoqIOaJuemHj+W/q+mAn+ivhOWIhuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQmF0Y2hRdWlja1Njb3JlKCkgewogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+mAieaLqemcgOimgeivhOWIhueahOaVsOaNriIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICAKICAgICAgLy8g6aqM6K+B6K+E5YiG5LiA6Ie05oCn5ZKM55CG55Sx5b+F5aGrCiAgICAgIGNvbnN0IHZhbGlkYXRpb25SZXN1bHQgPSB0aGlzLnZhbGlkYXRlQmF0Y2hRdWlja1Njb3JlKCk7CiAgICAgIGlmICghdmFsaWRhdGlvblJlc3VsdC5pc1ZhbGlkKSB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IodmFsaWRhdGlvblJlc3VsdC5tZXNzYWdlKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRoaXMuYmF0Y2hRdWlja1Njb3JlT3BlbiA9IHRydWU7CiAgICB9LAoKICAgIC8qKiDlj5bmtojmibnph4/lv6vpgJ/or4TliIbmk43kvZwgKi8KICAgIGNhbmNlbEJhdGNoUXVpY2tTY29yZSgpIHsKICAgICAgdGhpcy5iYXRjaFF1aWNrU2NvcmVPcGVuID0gZmFsc2U7CiAgICB9LAoKICAgIC8qKiDmj5DkuqTmibnph4/lv6vpgJ/or4TliIYgKi8KICAgIHN1Ym1pdEJhdGNoUXVpY2tTY29yZSgpIHsKICAgICAgLy8g5YeG5aSH5o+Q5Lqk5pWw5o2uCiAgICAgIGNvbnN0IHN1Ym1pdERhdGEgPSB0aGlzLnNlbGVjdGVkUm93cy5tYXAocm93ID0+IHsKICAgICAgICBsZXQgZmluYWxTY29yZTsKICAgICAgICBpZiAocm93LnN0YXR1cyA9PSAnMScpIHsKICAgICAgICAgIC8vIOmDqOmXqOmihuWvvOivhOWIhiA9IOiHquivhOWIhiArIOWKoOWHj+WIhgogICAgICAgICAgZmluYWxTY29yZSA9IHRoaXMuZ2V0RGVwdFNjb3JlKHJvdyk7CiAgICAgICAgfSBlbHNlIGlmIChyb3cuc3RhdHVzID09ICcyJykgewogICAgICAgICAgLy8g5LqL5Lia6YOo6K+E5YiGID0g6YOo6Zeo6aKG5a+86K+E5YiGICsg5Yqg5YeP5YiGCiAgICAgICAgICBmaW5hbFNjb3JlID0gdGhpcy5nZXRCdXNpbmVzc1Njb3JlKHJvdyk7CiAgICAgICAgfQogICAgICAgIAogICAgICAgIHJldHVybiB7CiAgICAgICAgICBpZDogcm93LmlkLAogICAgICAgICAgcXVpY2tTY29yZTogcGFyc2VGbG9hdChmaW5hbFNjb3JlKSwKICAgICAgICAgIHF1aWNrQWRkU2NvcmU6IHJvdy5xdWlja0FkZFNjb3JlLAogICAgICAgICAgcXVpY2tSZWFzb246IHJvdy5xdWlja1JlYXNvbgogICAgICAgIH07CiAgICAgIH0pOwoKICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5o+Q5Lqk6YCJ5Lit5Lq65ZGY55qE5b+r6YCf6K+E5YiG77yfJykudGhlbigoKSA9PiB7CiAgICAgICAgcmV0dXJuIGJhdGNoUXVpY2tTY29yZShzdWJtaXREYXRhKTsKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5om56YeP6K+E5YiG5oiQ5YqfIik7CiAgICAgICAgdGhpcy5iYXRjaFF1aWNrU2NvcmVPcGVuID0gZmFsc2U7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgICAgdGhpcy5nZXRDaGVja2VkTGlzdCgpOwogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAoKICAgIC8qKiDpqozor4Hmibnph4/lv6vpgJ/or4TliIYgKi8KICAgIHZhbGlkYXRlQmF0Y2hRdWlja1Njb3JlKCkgewogICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuc2VsZWN0ZWRSb3dzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgY29uc3Qgcm93ID0gdGhpcy5zZWxlY3RlZFJvd3NbaV07CiAgICAgICAgCiAgICAgICAgLy8g5qOA5p+l5piv5ZCm5aGr5YaZ5LqG5Yqg5YeP5YiG77yI5YWB6K645Li6MO+8iQogICAgICAgIGlmIChyb3cucXVpY2tBZGRTY29yZSA9PT0gbnVsbCB8fCByb3cucXVpY2tBZGRTY29yZSA9PT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICBpc1ZhbGlkOiBmYWxzZSwKICAgICAgICAgICAgbWVzc2FnZTogYOesrCR7aSArIDF96KGMICR7cm93Lm5hbWV9IOivt+Whq+WGmeWKoOWHj+WIhmAKICAgICAgICAgIH07CiAgICAgICAgfQoKICAgICAgICAvLyDmo4Dmn6XliqDlh4/liIbkuI3kuLow5pe25piv5ZCm5aGr5YaZ5LqG55CG55SxCiAgICAgICAgaWYgKHBhcnNlRmxvYXQocm93LnF1aWNrQWRkU2NvcmUpICE9PSAwICYmICFyb3cucXVpY2tSZWFzb24pIHsKICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgIGlzVmFsaWQ6IGZhbHNlLAogICAgICAgICAgICBtZXNzYWdlOiBg56ysJHtpICsgMX3ooYwgJHtyb3cubmFtZX0g5pyJ5Yqg5YeP5YiG5pe26K+35aGr5YaZ5Yqg5YeP5YiG55CG55SxYAogICAgICAgICAgfTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIHJldHVybiB7IGlzVmFsaWQ6IHRydWUgfTsKICAgIH0sCgogICAgLyoqIOafpeeci+ivhOWIhuiusOW9leivpuaDhSAqLwogICAgaGFuZGxlQ2hlY2tlZERldGFpbChyb3cpIHsKICAgICAgZ2V0SW5mbyh7aWQ6IHJvdy5pbmZvSWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgY29uc29sZS5sb2cocmVzKTsKICAgICAgICBpZihyZXMuY29kZSA9PSAyMDApewogICAgICAgICAgdGhpcy5jaGVja0luZm8gPSByZXMuZGF0YTsKICAgICAgICAgIGxldCBsaXN0ID0gSlNPTi5wYXJzZShyZXMuZGF0YS5jb250ZW50KTsKICAgICAgICAgIHRoaXMuaGFuZGxlU3Bhbkxpc3QobGlzdCk7CiAgICAgICAgICB0aGlzLmNoZWNrSW5mby5saXN0ID0gbGlzdDsKICAgICAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W6K+m5oOF5aSx6LSlJyk7CiAgICAgIH0pOwogICAgfSwKCiAgICAvLyDorqHnrpfpg6jpl6jpooblr7zor4TliIbvvIjlv6vpgJ/or4TliIbooajmoLznlKjvvIkKICAgIGdldERlcHRTY29yZShyb3cpIHsKICAgICAgY29uc3Qgc2VsZlNjb3JlID0gcGFyc2VGbG9hdChyb3cuc2VsZlNjb3JlKSB8fCAwOwogICAgICBjb25zdCBhZGRTY29yZSA9IHBhcnNlRmxvYXQocm93LnF1aWNrQWRkU2NvcmUpIHx8IDA7CiAgICAgIGNvbnN0IHJlc3VsdCA9IHNlbGZTY29yZSArIGFkZFNjb3JlOwogICAgICAvLyDnoa7kv53or4TliIblnKgwLTEwMOiMg+WbtOWGhQogICAgICByZXR1cm4gTWF0aC5tYXgoMCwgTWF0aC5taW4oMTAwLCByZXN1bHQpKS50b0ZpeGVkKDEpOwogICAgfSwKCiAgICAvLyDorqHnrpfkuovkuJrpg6jor4TliIbvvIjlv6vpgJ/or4TliIbooajmoLznlKjvvIkKICAgIGdldEJ1c2luZXNzU2NvcmUocm93KSB7CiAgICAgIGNvbnN0IGRlcHRTY29yZSA9IHBhcnNlRmxvYXQocm93LmRlcHRTY29yZSkgfHwgMDsKICAgICAgY29uc3QgYWRkU2NvcmUgPSBwYXJzZUZsb2F0KHJvdy5xdWlja0FkZFNjb3JlKSB8fCAwOwogICAgICBjb25zdCByZXN1bHQgPSBkZXB0U2NvcmUgKyBhZGRTY29yZTsKICAgICAgLy8g56Gu5L+d6K+E5YiG5ZyoMC0xMDDojIPlm7TlhoUKICAgICAgcmV0dXJuIE1hdGgubWF4KDAsIE1hdGgubWluKDEwMCwgcmVzdWx0KSkudG9GaXhlZCgxKTsKICAgIH0sCgogICAgLy8g6K6h566X6YOo6Zeo6aKG5a+86K+E5YiG77yI6K+m57uG6K+E5YiG6KGo5Y2V55So77yJCiAgICBnZXREZXB0U2NvcmVGcm9tRm9ybSgpIHsKICAgICAgY29uc3Qgc2VsZlNjb3JlID0gcGFyc2VGbG9hdCh0aGlzLmNoZWNrSW5mby5zZWxmU2NvcmUpIHx8IDA7CiAgICAgIGNvbnN0IGFkZFNjb3JlID0gcGFyc2VGbG9hdCh0aGlzLmZvcm0uZGVwdEFkZFNjb3JlKSB8fCAwOwogICAgICBjb25zdCByZXN1bHQgPSBzZWxmU2NvcmUgKyBhZGRTY29yZTsKICAgICAgLy8g56Gu5L+d6K+E5YiG5ZyoMC0xMDDojIPlm7TlhoUKICAgICAgcmV0dXJuIE1hdGgubWF4KDAsIE1hdGgubWluKDEwMCwgcmVzdWx0KSkudG9GaXhlZCgxKTsKICAgIH0sCgogICAgLy8g6K6h566X5LqL5Lia6YOo6K+E5YiG77yI6K+m57uG6K+E5YiG6KGo5Y2V55So77yJCiAgICBnZXRCdXNpbmVzc1Njb3JlRnJvbUZvcm0oKSB7CiAgICAgIGNvbnN0IGRlcHRTY29yZSA9IHBhcnNlRmxvYXQodGhpcy5jaGVja0luZm8uZGVwdFNjb3JlKSB8fCAwOwogICAgICBjb25zdCBhZGRTY29yZSA9IHBhcnNlRmxvYXQodGhpcy5mb3JtLmJ1c2luZXNzQWRkU2NvcmUpIHx8IDA7CiAgICAgIGNvbnN0IHJlc3VsdCA9IGRlcHRTY29yZSArIGFkZFNjb3JlOwogICAgICAvLyDnoa7kv53or4TliIblnKgwLTEwMOiMg+WbtOWGhQogICAgICByZXR1cm4gTWF0aC5tYXgoMCwgTWF0aC5taW4oMTAwLCByZXN1bHQpKS50b0ZpeGVkKDEpOwogICAgfSwKICB9Cn07Cg=="}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2ZA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/assess/self/check", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.assessDate\"\r\n              type=\"month\"\r\n              value-format=\"yyyy-M\"\r\n              format=\"yyyy 年 M 月\"\r\n              placeholder=\"选择考核年月\"\r\n              :clearable=\"false\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"姓名\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入姓名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <el-select v-model=\"queryParams.deptId\" placeholder=\"请选择部门\">\r\n              <el-option\r\n                v-for=\"item in deptOptions\"\r\n                :key=\"item.deptId\"\r\n                :label=\"item.deptName\"\r\n                :value=\"item.deptId\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n\r\n      \r\n      <!-- 待评分列表 -->\r\n      <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n            <i class=\"el-icon-s-order\"></i>\r\n            {{ toCheckLabel }}\r\n          </span>\r\n        </div>\r\n        \r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              @click=\"handleBatchQuickScore\"\r\n            >批量快速评分</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n          <el-table v-loading=\"loading\" :data=\"listToCheck\" @selection-change=\"handleSelectionChange\">\r\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n            <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n            <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n            <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" ></el-table-column>\r\n            <el-table-column label=\"自评分\" align=\"center\" prop=\"selfScore\"></el-table-column>\r\n            <el-table-column label=\"部门领导评分\" align=\"center\" prop=\"deptScore\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.status == '1'\" style=\"font-weight: bold; color: #409EFF;\">{{ getDeptScore(scope.row) }}</span>\r\n                <span v-else-if=\"scope.row.deptScore\">{{ scope.row.deptScore }}</span>\r\n                <span v-else></span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column v-if=\"showBusiness\" label=\"事业部评分\" align=\"center\" prop=\"businessScore\">\r\n              <template slot-scope=\"scope\">\r\n                <span v-if=\"scope.row.status == '2'\" style=\"font-weight: bold; color: #409EFF;\">{{ getBusinessScore(scope.row) }}</span>\r\n                <span v-else-if=\"scope.row.businessScore\">{{ scope.row.businessScore }}</span>\r\n                <span v-else></span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"加减分\" align=\"center\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number \r\n                  v-if=\"scope.row.status == '1' || scope.row.status == '2'\"\r\n                  v-model=\"scope.row.quickAddScore\" \r\n                  :min=\"-100\" \r\n                  :max=\"100\" \r\n                  size=\"mini\"\r\n                  :precision=\"1\"\r\n                  style=\"width: 100px\"\r\n                  placeholder=\"加减分\">\r\n                </el-input-number>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"加减分原因\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input\r\n                  v-model=\"scope.row.quickReason\"\r\n                  type=\"textarea\"\r\n                  :autosize=\"{ minRows: 1, maxRows: 4}\"\r\n                  size=\"mini\"\r\n                  style=\"width: 150px\"\r\n                  placeholder=\"请输入加减分原因\">\r\n                </el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <!-- <el-table-column label=\"运改组织部评分\" align=\"center\" prop=\"organizationScore\">\r\n                <el-input-number \r\n                  v-if=\"scope.row.status == '3'\"\r\n                  v-model=\"scope.row.quickScore\" \r\n                  :min=\"0\" \r\n                  :max=\"100\" \r\n                  size=\"mini\"\r\n                  style=\"width: 120px\"\r\n                  placeholder=\"请输入分数\">\r\n                </el-input-number>\r\n                <span v-else-if=\"scope.row.businessScore\">{{ businessScore }}</span>\r\n                <span v-else></span>\r\n            </el-table-column> -->\r\n            <!-- <el-table-column label=\"快速评分\" align=\"center\" width=\"280\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input-number \r\n                  v-model=\"scope.row.quickScore\" \r\n                  :min=\"0\" \r\n                  :max=\"100\" \r\n                  size=\"mini\"\r\n                  style=\"width: 120px\"\r\n                  placeholder=\"请输入分数\">\r\n                </el-input-number>\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"primary\"\r\n                  @click=\"handleQuickSubmit(scope.row)\"\r\n                  :loading=\"scope.row.submitting\"\r\n                  style=\"margin-left: 10px\">\r\n                  提交评分\r\n                </el-button>\r\n              </template>\r\n            </el-table-column> -->\r\n            <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  icon=\"el-icon-edit\"\r\n                  @click=\"handleCheckDetail(scope.row)\"\r\n                >详细评分</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-card>\r\n\r\n      <!-- 评分记录 -->\r\n      <el-card class=\"box-card\">\r\n        <div slot=\"header\" class=\"clearfix\">\r\n          <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n            <i class=\"el-icon-document\"></i>\r\n            评分记录({{ checkedTotal }})\r\n          </span>\r\n        </div>\r\n        \r\n        <el-table v-loading=\"loading\" :data=\"listChecked\">\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" width=\"120\"/>\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" width=\"120\"/>\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n          <el-table-column label=\"职务\" align=\"center\" prop=\"job\" width=\"150\"/>\r\n          <el-table-column label=\"评分类型\" align=\"center\" prop=\"type\" >\r\n            <template slot-scope=\"scope\">\r\n              <el-tag v-if=\"scope.row.type == '1'\" type=\"primary\" size=\"small\">部门领导评分</el-tag>\r\n              <el-tag v-if=\"scope.row.type == '2'\" type=\"warning\" size=\"small\">事业部领导评分</el-tag>\r\n              <el-tag v-if=\"scope.row.type == '3'\" type=\"success\" size=\"small\">运改组织部审核</el-tag>\r\n              <el-tag v-if=\"scope.row.type == '4'\" type=\"info\" size=\"small\">条线领导评分</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"评分时间\" align=\"center\" prop=\"createTime\" width=\"160\"/>\r\n          <el-table-column label=\"评分\" align=\"center\" prop=\"score\" width=\"100\"/>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handleCheckedDetail(scope.row)\"\r\n              >查看详情</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        \r\n        <pagination\r\n          v-show=\"checkedTotal>0\"\r\n          :total=\"checkedTotal\"\r\n          :page.sync=\"checkedQueryParams.pageNum\"\r\n          :limit.sync=\"checkedQueryParams.pageSize\"\r\n          @pagination=\"getCheckedList\"\r\n          style=\"margin-top: 20px;\"\r\n        />\r\n      </el-card>\r\n\r\n          <el-dialog\r\n      :visible.sync=\"open\"\r\n      fullscreen\r\n      class=\"assessment-detail-dialog\">\r\n      <div class=\"detail-container\">\r\n        <div class=\"detail-header\">\r\n          <h2 style=\"text-align: center; color: #303133; margin-bottom: 20px;\">\r\n            <i class=\"el-icon-document\"></i>\r\n            月度业绩考核表\r\n          </h2>\r\n          <el-card shadow=\"never\" style=\"margin-bottom: 20px;\">\r\n            <el-descriptions class=\"margin-top\" :column=\"3\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-user\"></i> 姓名\r\n                </template>\r\n                {{ checkInfo.name }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-office-building\"></i> 部门\r\n                </template>\r\n                {{ checkInfo.deptName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\">\r\n                  <i class=\"el-icon-date\"></i> 考核年月\r\n                </template>\r\n                {{ checkInfo.assessDate }}\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </el-card>\r\n        </div>\r\n        \r\n        <el-card shadow=\"never\" class=\"assessment-table-card\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #409EFF;\">\r\n              <i class=\"el-icon-s-data\"></i>\r\n              考核详情\r\n            </span>\r\n          </div>\r\n          <el-table v-loading=\"loading\" :data=\"checkInfo.list\"\r\n            :span-method=\"objectSpanMethod\" border stripe>\r\n            <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n            <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"150\"/>\r\n            <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"180\"/>\r\n            <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n            <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" />\r\n            <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"150\" />\r\n            <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\" width=\"180\" />\r\n          </el-table>\r\n        </el-card>\r\n        \r\n        <el-card shadow=\"never\" class=\"signature-card\" style=\"margin-top: 20px;\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 16px; font-weight: bold; color: #67C23A;\">\r\n              <i class=\"el-icon-edit-outline\"></i>\r\n              评分记录\r\n            </span>\r\n          </div>\r\n          <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" label-position=\"left\">\r\n            <!-- 自评分 -->\r\n            <el-form-item>\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  自评分数 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.selfScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.name }}</span>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 部门领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.deptScore && checkInfo.deptUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  部门领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.deptScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.deptUserName }}</span>\r\n                <div v-if=\"checkInfo.deptScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.deptScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 事业部领导评分 -->\r\n            <el-form-item v-if=\"checkInfo.businessUserName && checkInfo.businessScore\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  事业部领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.businessScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.businessUserName }}</span>\r\n                <div v-if=\"checkInfo.businessScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.businessScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 运改组织部评分 -->\r\n            <el-form-item v-if=\"checkInfo.organizationScore && checkInfo.organizationUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  运改组织部评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ checkInfo.organizationScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ checkInfo.organizationUserName }}</span>\r\n                <div v-if=\"checkInfo.organizationScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ checkInfo.organizationScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 当前状态评分输入 -->\r\n            <el-form-item v-if=\"checkInfo.status == '1'\" label=\"加减分：\">\r\n              <el-input-number v-model=\"form.deptAddScore\" :min=\"-100\" :max=\"100\" :precision=\"1\" placeholder=\"请输入加减分\" style=\"width: 150px;\" />\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '1'\" label=\"部门领导评分：\">\r\n              <span style=\"font-weight: bold; color: #409EFF; font-size: 16px;\">{{ getDeptScoreFromForm() }}分</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '1'\" label=\"加减分理由：\">\r\n              <el-input type=\"textarea\" :autosize=\"{ minRows: 2, maxRows: 4}\" v-model=\"form.deptScoreReason\" placeholder=\"请输入加减分理由\" style=\"width: 400px;\" />\r\n            </el-form-item>\r\n            \r\n            <el-form-item v-if=\"checkInfo.status == '2'\" label=\"加减分：\">\r\n              <el-input-number v-model=\"form.businessAddScore\" :min=\"-100\" :max=\"100\" :precision=\"1\" placeholder=\"请输入加减分\" style=\"width: 150px;\" />\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '2'\" label=\"事业部领导评分：\">\r\n              <span style=\"font-weight: bold; color: #409EFF; font-size: 16px;\">{{ getBusinessScoreFromForm() }}分</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"checkInfo.status == '2'\" label=\"加减分理由：\">\r\n              <el-input type=\"textarea\" :autosize=\"{ minRows: 2, maxRows: 4}\" v-model=\"form.businessScoreReason\" placeholder=\"请输入加减分理由\" style=\"width: 400px;\" />\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n        \r\n        <div class=\"dialog-footer\" style=\"text-align: center; margin-top: 30px; padding: 20px;\">\r\n          <el-button type=\"primary\" size=\"medium\" @click=\"checkSubmit\">\r\n            <i class=\"el-icon-check\"></i> 提 交\r\n          </el-button>\r\n          <el-button plain type=\"info\" size=\"medium\" @click=\"cancel\">\r\n            <i class=\"el-icon-close\"></i> 返 回\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n      <!-- 批量快速评分对话框 -->\r\n      <el-dialog :title=\"'批量快速评分确认'\" :visible.sync=\"batchQuickScoreOpen\" width=\"800px\" append-to-body>\r\n        <el-alert\r\n          title=\"请确认以下人员的评分信息\"\r\n          type=\"warning\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          class=\"mb20\"\r\n        />\r\n        <el-table :data=\"selectedRows\" size=\"small\" border>\r\n          <el-table-column label=\"工号\" align=\"center\" prop=\"workNo\" />\r\n          <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" />\r\n          <el-table-column label=\"部门\" align=\"center\" prop=\"deptName\" />\r\n          <el-table-column label=\"岗位\" align=\"center\" prop=\"job\" />\r\n          <el-table-column label=\"加减分\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.quickAddScore || 0 }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"最终评分\" align=\"center\" prop=\"quickScore\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.status == '1'\" style=\"font-weight: bold; color: #409EFF;\">{{ getDeptScore(scope.row) }}</span>\r\n              <span v-else-if=\"scope.row.status == '2'\" style=\"font-weight: bold; color: #409EFF;\">{{ getBusinessScore(scope.row) }}</span>\r\n              <span v-else :class=\"{'text-red': !scope.row.quickScore}\">{{ scope.row.quickScore || '未填写' }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"加减分理由\" align=\"center\" prop=\"quickReason\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.quickReason || '' }}</span>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitBatchQuickScore\" :disabled=\"!canSubmitBatchScore\">确 定</el-button>\r\n          <el-button @click=\"cancelBatchQuickScore\">取 消</el-button>\r\n          </div>\r\n      </el-dialog>\r\n\r\n    </div>\r\n  </template>\r\n\r\n  <script>\r\n  import { listToCheck, listChecked, getInfo, check, batchQuickScore } from \"@/api/assess/self/info\"\r\n  import { getCheckDeptList } from \"@/api/assess/self/user\";\r\n\r\n  export default {\r\n    name: \"SelfAssessCheck\",\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        // 显示列表事业部评分\r\n        showBusiness:false,\r\n        // 总条数\r\n        total: 0,\r\n        checkedTotal: 0,\r\n        // 绩效考核-干部自评人员配置表格数据\r\n        listToCheck: [],\r\n        listChecked: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          workNo: null,\r\n          name:null,\r\n          deptId:null,\r\n          assessDate:null\r\n        },\r\n        // 评分记录查询参数\r\n        checkedQueryParams: {\r\n          pageNum: 1,\r\n          pageSize: 10,\r\n          workNo: null,\r\n          name:null,\r\n          deptId:null,\r\n          assessDate:null\r\n        },\r\n        // 表单参数\r\n        form: {\r\n          id:null,\r\n          // 部门领导加减分\r\n          deptAddScore:null,\r\n          // 事业部加减分\r\n          businessAddScore:null,\r\n          // 部门领导评分理由\r\n          deptScoreReason:null,\r\n          // 事业部评分理由\r\n          businessScoreReason:null,\r\n        },\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        deptOptions:[],\r\n        openCheck:false,\r\n        checkInfo:{},\r\n        // 合并单元格\r\n        spanList:[],\r\n        // 待评分标签\r\n        toCheckLabel:\"待评分(0)\",\r\n        // 快速评分提交状态\r\n        submitting: false,\r\n        // 选中数组\r\n        multipleSelection: [],\r\n        // 非单个禁用\r\n        single: true,\r\n        // 非多个禁用\r\n        multiple: true,\r\n        // 批量快速评分对话框显示状态\r\n        quickScoreDialogVisible: false,\r\n        // 批量快速评分表单参数\r\n        batchQuickScoreForm: {\r\n          score: undefined,\r\n          ids: []\r\n        },\r\n        // 批量快速评分表单验证规则\r\n        batchQuickScoreRules: {\r\n          score: [\r\n            { required: true, message: \"评分不能为空\", trigger: \"blur\" },\r\n            { type: 'number', message: \"评分必须为数字\", trigger: \"blur\" }\r\n          ]\r\n        },\r\n        // 批量快速评分对话框\r\n        batchQuickScoreOpen: false,\r\n        // 选中数组\r\n        ids: [],\r\n        // 选中的行数据\r\n        selectedRows: [],\r\n      };\r\n    },\r\n      computed: {\r\n    // 是否可以提交批量评分（基础检查）\r\n    canSubmitBatchScore() {\r\n      if (this.selectedRows.length === 0) return false;\r\n      \r\n      // 基础检查：是否所有行都填写了加减分\r\n      for (let row of this.selectedRows) {\r\n        if (row.quickAddScore === null || row.quickAddScore === undefined) {\r\n          return false;\r\n        }\r\n      }\r\n      \r\n      return true;\r\n    }\r\n  },\r\n    created() {\r\n      this.queryParams.assessDate = this.getDefaultAssessDate()\r\n      this.checkedQueryParams.assessDate = this.getDefaultAssessDate()\r\n      // this.getSelfAssessUser();\r\n      this.getCheckDeptList();\r\n      this.getList();\r\n      this.getCheckedList();\r\n    },\r\n    methods: {\r\n\r\n      // 获取默认考核日期\r\n      getDefaultAssessDate() {\r\n        const now = new Date();\r\n        const currentDay = now.getDate();\r\n\r\n        let targetDate;\r\n        if (currentDay < 10) {\r\n          // 当前日期小于10日，默认为上个月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n        } else {\r\n          // 当前日期大于等于10日，默认为当月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n        }\r\n\r\n        // 格式化为 YYYY-M 格式\r\n        const year = targetDate.getFullYear();\r\n        const month = targetDate.getMonth() + 1;\r\n        return `${year}-${month}`;\r\n      },\r\n\r\n      // 获取部门信息\r\n      getCheckDeptList(){\r\n        getCheckDeptList().then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            let deptOptions = [];\r\n            res.data.forEach(item => {\r\n              deptOptions.push({\r\n                deptName:item.deptName,\r\n                deptId:item.deptId\r\n              })\r\n            })\r\n            this.deptOptions = deptOptions;\r\n          }\r\n        })\r\n      },\r\n      /** 查询绩效考核-干部自评待审核列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        listToCheck(this.queryParams).then(response => {\r\n          this.listToCheck = response.rows;\r\n          this.total = response.total;\r\n          this.toCheckLabel = `待评分(${response.total})`\r\n          this.loading = false;\r\n          this.shouldBusinessDisplay();\r\n        });\r\n      },\r\n\r\n      shouldBusinessDisplay(){\r\n        this.showBusiness = this.listToCheck.some(row => row[\"status\"] == '2')\r\n      },\r\n      \r\n      /** 获取已审核列表 */\r\n      getCheckedList(){\r\n        this.loading = true;\r\n        listChecked(this.checkedQueryParams).then(res => {\r\n          this.listChecked = res.rows;\r\n          this.checkedTotal = res.total;\r\n          this.loading = false;\r\n        })\r\n      },\r\n\r\n      // 取消按钮\r\n      cancel() {\r\n        this.open = false;\r\n        this.reset();\r\n      },\r\n      // 表单重置\r\n      reset() {\r\n        this.form = {\r\n          id: null,\r\n          deptAddScore: null,\r\n          businessAddScore: null,\r\n          deptScoreReason: null,\r\n          businessScoreReason: null,\r\n        };\r\n        // this.resetForm(\"form\");\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.queryParams.pageNum = 1;\r\n        this.checkedQueryParams.pageNum = 1;\r\n        // 同步搜索条件\r\n        this.checkedQueryParams.name = this.queryParams.name;\r\n        this.checkedQueryParams.deptId = this.queryParams.deptId;\r\n        this.checkedQueryParams.assessDate = this.queryParams.assessDate;\r\n        this.getCheckedList();\r\n        this.getList();\r\n      },\r\n      /** 重置按钮操作 */\r\n      resetQuery() {\r\n        this.resetForm(\"queryForm\");\r\n        this.handleQuery();\r\n      },\r\n\r\n      // 审批详情\r\n      handleCheckDetail(row){\r\n        getInfo({id:row.id}).then(res => {\r\n          console.log(res);\r\n          if(res.code == 200){\r\n            this.checkInfo = res.data;\r\n            let list = JSON.parse(res.data.content);\r\n            this.handleSpanList(list);\r\n            this.checkInfo.list = list;\r\n          }\r\n          this.open = true\r\n        })\r\n      },\r\n\r\n      // 审批提交\r\n      checkSubmit(){\r\n        if(this.verify()){\r\n          let point = this.getDeptScoreFromForm();\r\n          if(this.checkInfo.status == '2') point = this.getBusinessScoreFromForm();\r\n          this.$confirm('是否确认' + this.checkInfo.name + '评分为：' + point + '分', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.onCheck();\r\n          }).catch(() => {\r\n\r\n          });\r\n        }\r\n      },\r\n\r\n      onCheck(){\r\n        this.form.id = this.checkInfo.id;\r\n        this.form.status = this.checkInfo.status;\r\n        \r\n        // 计算最终评分\r\n        if(this.checkInfo.status == '1') {\r\n          this.form.deptScore = this.getDeptScoreFromForm();\r\n        }\r\n        if(this.checkInfo.status == '2') {\r\n          this.form.businessScore = this.getBusinessScoreFromForm();\r\n        }\r\n        \r\n        check(this.form).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            });\r\n            this.reset();\r\n            this.open = false;\r\n            this.getList();\r\n            this.getCheckedList();\r\n          }else{\r\n            this.$message({\r\n              type: 'warning',\r\n              message: '操作失败，无权限或当前审批状态不匹配'\r\n            });\r\n          }\r\n        })\r\n      },\r\n\r\n      // 数据验证\r\n      verify(){\r\n        if(this.checkInfo.status == '1' && this.form.deptAddScore === null){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请填写加减分'\r\n          });\r\n          return false;\r\n        }\r\n        if(this.checkInfo.status == '2' && this.form.businessAddScore === null){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请填写加减分'\r\n          });\r\n          return false;\r\n        }\r\n        if(this.checkInfo.status == '1' && this.form.deptAddScore !== 0 && !this.form.deptScoreReason){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '有加减分时请填写加减分理由'\r\n          });\r\n          return false;\r\n        } \r\n        if(this.checkInfo.status == '2' && this.form.businessAddScore !== 0 && !this.form.businessScoreReason){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '有加减分时请填写加减分理由'\r\n          });\r\n          return false;\r\n        }\r\n        return true;\r\n      },\r\n\r\n      handleListChange(type){\r\n        console.log(type)\r\n      },\r\n      // 处理列表\r\n      handleSpanList(data){\r\n        let spanList = [];\r\n        let flag = 0;\r\n        for(let i = 0; i < data.length; i++){\r\n          // 相同考核项合并\r\n          if(i == 0){\r\n            spanList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n          }else{\r\n            if(data[i - 1].item == data[i].item){\r\n              spanList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              spanList[flag].rowspan += 1;\r\n            }else{\r\n              spanList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              flag = i;\r\n            }\r\n          }\r\n        }\r\n        this.spanList = spanList;\r\n      },\r\n\r\n      // 合并单元格方法\r\n      objectSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n        // 第一列相同项合并\r\n        if (columnIndex === 0) {\r\n          return this.spanList[rowIndex];\r\n        }\r\n        // 类别无内容 合并\r\n        if(columnIndex === 1){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 0,\r\n              colspan: 0\r\n            }\r\n          }\r\n        }\r\n        if(columnIndex === 2){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 1,\r\n              colspan: 2\r\n            }\r\n          }\r\n        }\r\n      },\r\n\r\n      /** 快速评分提交 */\r\n      handleQuickSubmit(row) {\r\n        if (!row.quickScore) {\r\n          this.$message.warning('请输入评分');\r\n          return;\r\n        }\r\n        this.$confirm('确认提交该评分吗？', \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          this.$set(row, 'submitting', true);\r\n          const data = {\r\n            id: row.id,\r\n            score: row.quickScore,\r\n            type: row.type\r\n          };\r\n          check(data).then(response => {\r\n            this.$message.success('评分提交成功');\r\n            this.getList();\r\n            this.getCheckedList();\r\n          }).finally(() => {\r\n            this.$set(row, 'submitting', false);\r\n          });\r\n        });\r\n      },\r\n\r\n      /** 选择条数改变 */\r\n      handleSelectionChange(selection) {\r\n        this.ids = selection.map(item => item.id)\r\n        this.selectedRows = selection\r\n        this.single = selection.length !== 1\r\n        this.multiple = !selection.length\r\n      },\r\n\r\n      /** 批量快速评分按钮操作 */\r\n      handleBatchQuickScore() {\r\n        if (this.ids.length === 0) {\r\n          this.$modal.msgError(\"请选择需要评分的数据\");\r\n          return;\r\n        }\r\n        \r\n        // 验证评分一致性和理由必填\r\n        const validationResult = this.validateBatchQuickScore();\r\n        if (!validationResult.isValid) {\r\n          this.$modal.msgError(validationResult.message);\r\n          return;\r\n        }\r\n\r\n        this.batchQuickScoreOpen = true;\r\n      },\r\n\r\n      /** 取消批量快速评分操作 */\r\n      cancelBatchQuickScore() {\r\n        this.batchQuickScoreOpen = false;\r\n      },\r\n\r\n      /** 提交批量快速评分 */\r\n      submitBatchQuickScore() {\r\n        // 准备提交数据\r\n        const submitData = this.selectedRows.map(row => {\r\n          let finalScore;\r\n          if (row.status == '1') {\r\n            // 部门领导评分 = 自评分 + 加减分\r\n            finalScore = this.getDeptScore(row);\r\n          } else if (row.status == '2') {\r\n            // 事业部评分 = 部门领导评分 + 加减分\r\n            finalScore = this.getBusinessScore(row);\r\n          }\r\n          \r\n          return {\r\n            id: row.id,\r\n            quickScore: parseFloat(finalScore),\r\n            quickAddScore: row.quickAddScore,\r\n            quickReason: row.quickReason\r\n          };\r\n        });\r\n\r\n        this.$modal.confirm('是否确认提交选中人员的快速评分？').then(() => {\r\n          return batchQuickScore(submitData);\r\n        }).then(() => {\r\n          this.$modal.msgSuccess(\"批量评分成功\");\r\n          this.batchQuickScoreOpen = false;\r\n          this.getList();\r\n          this.getCheckedList();\r\n        }).catch(() => {});\r\n      },\r\n\r\n      /** 验证批量快速评分 */\r\n      validateBatchQuickScore() {\r\n        for (let i = 0; i < this.selectedRows.length; i++) {\r\n          const row = this.selectedRows[i];\r\n          \r\n          // 检查是否填写了加减分（允许为0）\r\n          if (row.quickAddScore === null || row.quickAddScore === undefined) {\r\n            return {\r\n              isValid: false,\r\n              message: `第${i + 1}行 ${row.name} 请填写加减分`\r\n            };\r\n          }\r\n\r\n          // 检查加减分不为0时是否填写了理由\r\n          if (parseFloat(row.quickAddScore) !== 0 && !row.quickReason) {\r\n            return {\r\n              isValid: false,\r\n              message: `第${i + 1}行 ${row.name} 有加减分时请填写加减分理由`\r\n            };\r\n          }\r\n        }\r\n\r\n        return { isValid: true };\r\n      },\r\n\r\n      /** 查看评分记录详情 */\r\n      handleCheckedDetail(row) {\r\n        getInfo({id: row.infoId}).then(res => {\r\n          console.log(res);\r\n          if(res.code == 200){\r\n            this.checkInfo = res.data;\r\n            let list = JSON.parse(res.data.content);\r\n            this.handleSpanList(list);\r\n            this.checkInfo.list = list;\r\n            this.open = true;\r\n          }\r\n        }).catch(error => {\r\n          this.$message.error('获取详情失败');\r\n        });\r\n      },\r\n\r\n      // 计算部门领导评分（快速评分表格用）\r\n      getDeptScore(row) {\r\n        const selfScore = parseFloat(row.selfScore) || 0;\r\n        const addScore = parseFloat(row.quickAddScore) || 0;\r\n        const result = selfScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n\r\n      // 计算事业部评分（快速评分表格用）\r\n      getBusinessScore(row) {\r\n        const deptScore = parseFloat(row.deptScore) || 0;\r\n        const addScore = parseFloat(row.quickAddScore) || 0;\r\n        const result = deptScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n\r\n      // 计算部门领导评分（详细评分表单用）\r\n      getDeptScoreFromForm() {\r\n        const selfScore = parseFloat(this.checkInfo.selfScore) || 0;\r\n        const addScore = parseFloat(this.form.deptAddScore) || 0;\r\n        const result = selfScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n\r\n      // 计算事业部评分（详细评分表单用）\r\n      getBusinessScoreFromForm() {\r\n        const deptScore = parseFloat(this.checkInfo.deptScore) || 0;\r\n        const addScore = parseFloat(this.form.businessAddScore) || 0;\r\n        const result = deptScore + addScore;\r\n        // 确保评分在0-100范围内\r\n        return Math.max(0, Math.min(100, result)).toFixed(1);\r\n      },\r\n    }\r\n  };\r\n  </script>\r\n\r\n  <style scoped>\r\n  .assessment-detail-dialog .detail-container {\r\n    padding: 20px;\r\n    background-color: #f5f7fa;\r\n    min-height: 100vh;\r\n  }\r\n\r\n  .assessment-detail-dialog .detail-header h2 {\r\n    background: linear-gradient(135deg, #409EFF, #67C23A);\r\n    background-clip: text;\r\n    -webkit-background-clip: text;\r\n    color: transparent;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .assessment-detail-dialog .assessment-table-card {\r\n    margin-bottom: 20px;\r\n  }\r\n\r\n  .assessment-detail-dialog .signature-card {\r\n    background: #ffffff;\r\n  }\r\n\r\n  .signature-content {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .score-text {\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .separator {\r\n    color: #909399;\r\n    margin: 0 4px;\r\n  }\r\n\r\n  .signature-name {\r\n    color: #303133;\r\n  }\r\n\r\n  .reason-text {\r\n    width: 100%;\r\n    margin-top: 8px;\r\n    padding: 8px 12px;\r\n    background-color: #f8f9fa;\r\n    border-left: 3px solid #409EFF;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .reason-label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n    margin-right: 8px;\r\n  }\r\n\r\n  .reason-content {\r\n    color: #303133;\r\n    line-height: 1.6;\r\n  }\r\n\r\n  .dialog-footer {\r\n    border-top: 1px solid #e4e7ed;\r\n    background-color: #ffffff;\r\n    border-radius: 0 0 6px 6px;\r\n  }\r\n\r\n  .assessment-detail-dialog .el-card {\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .assessment-detail-dialog .el-descriptions {\r\n    background-color: #ffffff;\r\n  }\r\n\r\n  .assessment-detail-dialog .el-table {\r\n    border-radius: 6px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .text-red {\r\n    color: #F56C6C;\r\n  }\r\n  </style>\r\n"]}]}