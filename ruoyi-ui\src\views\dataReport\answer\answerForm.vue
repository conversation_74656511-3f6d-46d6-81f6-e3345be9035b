<template>
  <div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
    >
      <el-row :gutter="20" style="margin: 20px">
        <el-form-item label="报表名称" prop="dimensionalityId">
          <el-select
            v-model="queryParams.dimensionalityId"
            placeholder="请选择"
            filterable
            @change="handleQuery"
          >
            <el-option
              v-for="item in rootList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="填报时间" v-if="count == '1'"  >
          <el-date-picker
            v-model="queryParams.fcDate"
            value-format="yyyy-MM-dd"
            type="date"
            :default-value="new Date()"
            :picker-options="pickerOptions"
            placeholder="选择时间"
            @change="handleDateChange"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="填报时间" v-if="count == '0'">
          <el-date-picker
            v-model="queryParams.fcDate"
            value-format="yyyy-MM-01"
            type="month"
            :default-value="new Date()"
            :picker-options="pickerOptions"
            placeholder="选择时间"
            @change="handleDateChange"
          >
          </el-date-picker>
        </el-form-item>


        <el-form-item label="填报问题" prop="formQuestion">
          <el-input
            v-model="queryParams.formQuestion"
            placeholder="请输入填报问题"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="cyan"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
        </el-form-item>
        </el-row>
        <el-row :gutter="10" class="mb8">
        <el-form-item>
          <el-button  v-if=" !containsSubstring('安全责任工资考核表',dimensionalityName) "
            type="warning"
            icon="el-icon-download"
            size="mini"
            @click="importOpen = true"
            >数据导入导出</el-button
          >
          
          <el-button v-if=" aloneList(dimensionalityName) || containsSubstring('安全责任工资考核表',dimensionalityName)"
            type="danger"
            icon="el-icon-download"
            size="mini"
            @click="SpecialImportOpen = true"
            >单周期报表导入导出</el-button>

          <el-button v-if="aloneList(dimensionalityName)  || containsSubstring('安全责任工资考核表',dimensionalityName) "
            type="warning"
            icon="el-icon-search"
            size="mini"
            @click="handlePreview"
            >数据预览</el-button
          >
        </el-form-item>
      </el-row>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">报表名称：{{dimensionalityName}}</el-col>
        
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">管理部门：{{deptName}}</el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
    </el-form>
    <vxe-form
      ref="formRef"
      :data="formData"
      @submit="handleSubmit"
      border
      title-background
      vertical-align="center"
      title-width="300"
      title-bold
    >
      <vxe-form-group
        v-for="(group, index) in answerList"
        :key="index"
        span="24"
        :title="group.title"
        title-bold
        vertical
      >
        <vxe-form-item
          v-for="(question, qIndex) in group.list"
          :key="qIndex"
          :title="question.formQuestion"
          :field="answerList[index].list[qIndex].formValue"
          :span="question.formType == '3' ? 24 : 12"
          :item-render="{}"
        >
          <template #default="params">
            <vxe-tag
              v-if="question.status == '0'"
              status="primary"
              content="主要颜色"
              >待审核 （审核人姓名：{{question.checkWorkNo}}  审核人工号：{{question.checkUserName}}）</vxe-tag
            >
            <vxe-tag
              v-if="question.status == '1'"
              status="warning"
              content="信息颜色"
              >审核中</vxe-tag
            >
            <vxe-tag
              v-if="question.status == '2'"
              status="success"
              content="信息颜色"
              >审核完成</vxe-tag
            >
            <vxe-tag
              v-if="question.status == '3'"
              status="danger"
              content="警告颜色"
              >驳回理由: {{ question.assessment }}</vxe-tag
            >

            <vxe-input
              v-if="question.formType == '0'"
              v-model="answerList[index].list[qIndex].formValue"
              @change="changeEvent(params)"
              type="integer"
            ></vxe-input>
            <vxe-input
              v-if="question.formType == '1'"
              v-model="answerList[index].list[qIndex].formValue"
              @change="changeEvent(params)"
              type="'float'"
            ></vxe-input>
            <vxe-input
              v-if="question.formType == '2'"
              v-model="answerList[index].list[qIndex].formValue"
              @change="changeEvent(params)"
              type="text"
            ></vxe-input>
            <vxe-textarea
              v-if="question.formType == '3'"
              v-model="answerList[index].list[qIndex].formValue"
              :placeholder="question.formQuestion"
            ></vxe-textarea>
            <vxe-text v-if="question.formNote != null" status="warning"
              >指标:{{ question.formNote }}<br
            /></vxe-text>
            <vxe-text v-if="question.formNote1 != null" status="warning"
              >备注:{{ question.formNote1 }}<br
            /></vxe-text>
            <vxe-text v-if="question.maximum != null" status="primary"
              >最大值:{{ question.maximum }}<br
            /></vxe-text>
            <vxe-text v-if="question.minimum != null" status="primary"
              >最小值:{{ question.minimum }}<br
            /></vxe-text>
            <vxe-text v-if="question.formUnit != null" status="primary"
              >单位:{{ question.formUnit }}<br
            /></vxe-text>
            <vxe-tag
              v-if="
                question.formValue != null &&
                question.formValue != '' &&
                (question.formType == '0' || question.formType == '1') &&
                ((question.minimum != null &&
                  question.formValue < question.minimum) ||
                  (question.maximum != null &&
                    question.formValue > question.maximum))
              "
              status="danger"
              content="警告颜色"
              >输入值超出预计范围，请输入原因和改进措施</vxe-tag
            >
            <vxe-textarea
              v-if="
                question.formValue != null &&
                question.formValue != '' &&
                (question.formType == '0' || question.formType == '1') &&
                ((question.minimum != null &&
                  question.formValue < question.minimum) ||
                  (question.maximum != null &&
                    question.formValue > question.maximum))
              "
              v-model="answerList[index].list[qIndex].reason"
              @change="changeEvent(params)"
              placeholder="请填写原因"
            ></vxe-textarea>
            <vxe-textarea
              v-if="
                question.formValue != null &&
                question.formValue != '' &&
                (question.formType == '0' || question.formType == '1') &&
                ((question.minimum != null &&
                  question.formValue < question.minimum) ||
                  (question.maximum != null &&
                    question.formValue > question.maximum))
              "
              v-model="answerList[index].list[qIndex].measure"
              @change="changeEvent(params)"
              placeholder="请输入改进措施"
            ></vxe-textarea>
          </template>
        </vxe-form-item>
      </vxe-form-group>

      <vxe-form-item align="center" span="24" :item-render="{}">
        <template #default>
          <vxe-button
            type="submit"
            status="primary"
            content="提交"
          ></vxe-button>
        </template>
      </vxe-form-item>
    </vxe-form>

    <el-dialog
      title="选择导出范围"
      :visible.sync="importOpen"
      width="400px"
      append-to-body
      destroy-on-close
    >
      <span>数据日期范围：</span>
      <el-date-picker
        v-model="dateValue"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        @change="onDateChange"
      >
      </el-date-picker>
      <el-row :gutter="10" style="margin-top: 10px;">
       
        <el-col :span="1.5">
          <el-button
            size="small"
            type="info"
            plain
            icon="el-icon-link"
            @click="downloadTemplate"
            >数据下载</el-button
          >
        </el-col>
         <el-col :span="1.5" >
          <el-upload
            accept=".xlsx, .xls"
            :headers="upload.headers"
            :disabled="upload.isUploading"
            :action="upload.url"
            :show-file-list="false"
            :multiple="false"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
          >
            <el-button size="small" type="warning" plain icon="el-icon-download"
              >表格导入</el-button
            >
          </el-upload>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importOpen = false">取 消</el-button>
      </div>
    </el-dialog>



    <el-dialog
      title="单位时间报表导出"
      :visible.sync="SpecialImportOpen"
      width="400px"
      append-to-body
      destroy-on-close
    >
      <span>选择导出时间：</span>
      <el-date-picker
            v-model="specialFcDate"
            value-format="yyyy-MM-dd"
            type="date"
            :default-value="new Date()"
            placeholder="选择时间"
          >
      </el-date-picker>
      <el-row :gutter="10" style="margin-top: 10px;">
       
        <el-col :span="1.5">
          <el-button
            size="small"
            type="info"
            plain
            icon="el-icon-link"
            @click="downloadTemplateSpecial"
            >数据下载</el-button
          >
        </el-col>
         <el-col :span="1.5" >
          <el-upload
            accept=".xlsx, .xls"
            :headers="uploadSpecial.headers"
            :disabled="uploadSpecial.isUploading"
            :action="uploadSpecial.url"
            :show-file-list="false"
            :multiple="false"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
          >
            <el-button size="small" type="warning" plain icon="el-icon-download"
              >表格导入</el-button
            >
          </el-upload>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="SpecialImportOpen = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog  title="文件预览"  :visible.sync="searchopen" width="1800px" >
        <div class="test">
          <vue-office-excel
              :src="customBlobContent"
              style="height: 100vh;"
          />
        </div>
    </el-dialog>
  </div>
</template>
  
  <script>
import { answerListPlusAll,formFrequency } from "@/api/tYjy/form";
import answerInput from "./input";
import { newAdd, addAlone } from "@/api/tYjy/answer";
import { getAllRootListForAnswer } from "@/api/tYjy/dimensionality";
import { getToken } from "@/utils/auth";
import axios from "axios";
import * as xlsx from 'xlsx';

//引入相关样式

export default {
  name: "Answer",
  components: { answerInput },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      frequencyOptions: [],

      queryParams: {
        formQuestion: undefined,
        fcDate: undefined,
        dimensionalityId: undefined,
        formQuestion: undefined,
      },

      formType: null,
      dimensionalityNames: null,
      drawerShow: false,
      stickyTop: 0,
      loading: false,
      showSearch: true,
      answerList: [],
      formData: {},
      row: {},
      rootList: [],
      userInfo: {},
      datesave:{},
      pathsave:{},
      count:1,
      deptName:null,
      dimensionalityName:null,
      dateValue: null,
      specialFcDate:null,
      queryImport: {
        startDate: null,
        endDate: null,
        rootId: null,
      },
      importOpen:false,
      SpecialImportOpen:false,
      // 导入参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/web/TYjy/answer/importData",
      },


      uploadSpecial: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          "/web/TYjy/answer/importDataSpecial",

      },
      excelHtml:"",
      searchopen:false,
      excelData: [], // 存储 Excel 数据
      exceltitle: [],
      customBlobContent:""
    };
  },
  mounted() {
    this.userInfo = JSON.parse(JSON.stringify(this.$store.state.user));
  },

  created() {
    const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;
    const fcDate = this.$route.query && this.$route.query.fcDate;
    this.queryParams.dimensionalityId=dimensionalityId
    this.queryParams.fcDate=fcDate
    this.initData();


    // if(this.$route.query)
    // {
    //   const dimensionalityId = this.$route.query && this.$route.query.dimensionalityId;
    //   // const dimensionalityName = this.$route.query && this.$route.query.dimensionalityName;
    //   const fcDate = this.$route.query && this.$route.query.fcDate;
    //   this.queryParams.dimensionalityId=dimensionalityId
    //   // this.queryParams.dimensionalityName=dimensionalityName
    //   this.queryParams.fcDate=fcDate
    //   this.initData1();
    // }
    // else
    // {
    //   this.initData();
    // }
  },
  methods: {
    onDateChange() {
      console.log(this.dateValue);
      if (this.dateValue != null && this.dateValue != "") {
        this.queryImport.startDate = this.dateValue[0];
        this.queryImport.endDate = this.dateValue[1];
      } else {
        this.queryImport.startDate = "";
        this.queryImport.endDate = "";
      }
    },
    clickNode($event, node) {
      $event.target.parentElement.parentElement.firstElementChild.click();
    },
    changeEvent(params) {
      const $form = this.$refs.formRef;
      if ($form) {
        $form.updateStatus(params);
      }
    },
    disabledDate(time) {
      return time.getTime() < Date.now() - 8.64e7; // 8.64e7 毫秒数代表一天
    },
    inputChange(val, row) {
      row.formValue = val;
    },
    handleScroll() {
      this.isSticky = window.scrollY >= this.stickyTop;
    },
    initData() {
      getAllRootListForAnswer().then((res) => {
        this.rootList = res.data;
        if(this.queryParams.dimensionalityId==null)
        {
          this.queryParams.dimensionalityId = this.rootList[0].value;
          this.deptName= this.rootList[0].deptName;
          this.deptCode= this.rootList[0].deptCode;
          this.dimensionalityName=this.rootList[0].label
        }
        else
        {
          // this.queryParams.dimensionalityId = this.queryParams.dimensionalityId;
          for(let i=0;i<this.rootList.length;i++)
          {
            if(this.queryParams.dimensionalityId == this.rootList[i].value)
            {
              this.queryParams.dimensionalityId = this.rootList[i].value;
              this.deptName= this.rootList[i].deptName;
              this.deptCode= this.rootList[i].deptCode;
              this.dimensionalityName=this.rootList[i].label
            }
          }
        }
        this.getList();
      });
    },
    initData1() {
      getAllRootListForAnswer().then((res) => {
        this.rootList = res.data;
        for(let i=0;i<this.rootList.length;i++)
        {
          if(this.queryParams.dimensionalityId == this.rootList[i].value)
          {
            this.deptName= this.rootList[0].deptName;
          }
        }
        this.getList();
      });
    },
    containsSubstring(substring, string) {
      return string.includes(substring);
    },
    aloneList(string) {
      if(string== '气体结算月报')
      {
        return true;
      }
      if(string== '高炉、转炉煤气月报表')
      {
        return true;
      }
      if(string== '天然气消耗月报表')
      {
        return true;
      }
      if(string== '蒸汽消耗月报表')
      {
        return true;
      }
      if(string== '电量月报表')
      {
        return true;
      }
      if(string== '2025年经济责任制考核表（特板事业部）')
      {
        return true;
      }
      if(string== '水处理水量报表')
      {
        return true;
      }
      
      // if(string== '研究院目标指标一览')
      // {
      //   return true;
      // }
      return false;
    },

    /** 查询TYjyAnswer列表 */
    getList() {
      formFrequency({dimensionalityId: this.queryParams.dimensionalityId}).then((res) => {
           if(this.count!=res.data)
           {
            this.queryParams.fcDate=undefined
           }
           this.count=res.data
      });

      this.answerList = [];
      answerListPlusAll({
        fcDate: this.queryParams.fcDate,
        dimensionalityId: this.queryParams.dimensionalityId,
        formQuestion: this.queryParams.formQuestion,
      }).then((res) => {
        let answerList = [];
        let list = res.data;
        for(let i=0;i<this.rootList.length;i++)
        {
          if(this.queryParams.dimensionalityId == this.rootList[i].value)
          {
            this.queryParams.dimensionalityId = this.rootList[i].value;
            this.deptName= this.rootList[i].deptName;
            this.deptCode= this.rootList[i].deptCode;
            this.dimensionalityName=this.rootList[i].label
          }
        }
        if(this.containsSubstring('安全责任工资考核表',this.dimensionalityName))
        {
          console.log("test1")
          let num=0
          for(let i=0;i<list.length;i++)
          {
            if(list[i].formQuestion!='自评总分' && list[i].formQuestion!='厂长评分')
            {
              num=1;
              break;
            }
          }
          if(num==0)
          {
            for(let i=0;i<list.length;i++)
            {
              list[i].dimensionalityName=list[i].dimensionalityName.replace('/七、考核评分', '')
            }
            console.log("test3",list)
          }
        }
        
        for(let i=0;i<list.length;i++)
        {
            this.datesave[list[i].formId]=list[i].formValue
            this.pathsave[list[i].dimensionalityName]=list[i].dimensionalityPath
        }
        // 使用 map 提取 dimensionalityName 属性到一个数组
        let dimensionalityNames = list.map((x) => x.dimensionalityName);

        // 提取 / 后的前三位字符，并与原字符串配对
        dimensionalityNames = dimensionalityNames.map((name) => {
          // let key = name.includes("/") ? name.split("/")[1].slice(0, 3) : "";
          let key = this.pathsave[name];
          return { originalName: name, sortKey: key };
        });

        // 按照提取出的前三字符排序
        dimensionalityNames.sort((a, b) => a.sortKey.localeCompare(b.sortKey));
        // console.log("test0",dimensionalityNames)
        // 如果需要，可以提取排序后的原始名字
        dimensionalityNames = dimensionalityNames.map(
          (item) => item.originalName
        );

        // 使用 Set 去重
        let uniqueDimensionalityNames = [...new Set(dimensionalityNames)];

        uniqueDimensionalityNames.forEach((title) => {
          let group = {
            title: "",
            list: [],
          };
          group.title = title;
          group.list = list.filter((item) => item.dimensionalityName === title);
          // 假设你有一个数组来存储所有的组
          answerList.push(group); // 将生成的组添加到groups数组中
        });
        this.answerList = answerList;
        // console.log("test111",answerList)
        this.$forceUpdate();
      });
    },
    handleQuery() {
      for(let i=0;i<this.rootList.length;i++)
      {
        if(this.queryParams.dimensionalityId == this.rootList[i].value)
        {
          this.deptName= this.rootList[i].deptName;
          this.deptCode= this.rootList[i].deptCode;
          this.dimensionalityName=this.rootList[i].label
        }
      }
      this.getList();
    },
    handlePreview() {
      let queryImport={}
      queryImport.rootId = this.queryParams.dimensionalityId
      queryImport.fcDate = this.queryParams.fcDate
      queryImport.type="0"
      if(this.dimensionalityName=='研究院目标指标一览')
      {
        this.downloadXlsx(
        "/web/TYjy/answer/exportWithTemplate",
        {
          ...queryImport,
        },
        this.dimensionalityName+"(" +this.specialFcDate+
          ")" +
          `数据.xlsx`
      ).then((blob) => {
        let reader = new FileReader();
        reader.readAsArrayBuffer(blob);
        reader.onload = (evt) => {
          this.customBlobContent=reader.result;
          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array
          ints = ints.slice(0, blob.size);
          let workBook = xlsx.read(ints, { type: "array" });
          let sheetNames = workBook.SheetNames;
          let sheetName = sheetNames[0];
          let workSheet = workBook.Sheets[sheetName];
          //获取Excle内容，并将空内容用空值保存
          let excelTable = xlsx.utils.sheet_to_json(workSheet);
          // 获取Excel头部
          let tableThead = Array.from(Object.keys(excelTable[0])).map(
            (item) => {
              return item
            }
          );
          this.excelData = excelTable;
          this.exceltitle=tableThead
          this.excelHtml= excelTable
          this.searchopen = true;
        }
      });
      }
      else
      {
        this.downloadXlsx(
        "/web/TYjy/answer/exportTemplateSpecial",
        {
          ...queryImport,
        },
        this.dimensionalityName+"(" +this.specialFcDate+
          ")" +
          `数据.xlsx`
      ).then((blob) => {
        let reader = new FileReader();
        reader.readAsArrayBuffer(blob);
        reader.onload = (evt) => {
          this.customBlobContent=reader.result;
          let ints = new Uint8Array(evt.target.result); //要使用读取的内容，所以将读取内容转化成Uint8Array
          ints = ints.slice(0, blob.size);
          let workBook = xlsx.read(ints, { type: "array" });
          let sheetNames = workBook.SheetNames;
          let sheetName = sheetNames[0];
          let workSheet = workBook.Sheets[sheetName];
          //获取Excle内容，并将空内容用空值保存
          let excelTable = xlsx.utils.sheet_to_json(workSheet);
          // 获取Excel头部
          let tableThead = Array.from(Object.keys(excelTable[0])).map(
            (item) => {
              return item
            }
          );
          this.excelData = excelTable;
          this.exceltitle=tableThead
          this.excelHtml= excelTable
          this.searchopen = true;
        }
      });
      }
    },
    
    handleDateChange() {
      this.getList();
    },
    resetQuery() {
      this.queryParams.fcDate = undefined;
      this.queryParams.formQuestion = undefined;
      this.queryParams.dimensionalityId = undefined;
      this.getList();
    },
    handleUpload({ file }) {
      const formData = new FormData();
      formData.append("file", file);
      return axios
        .post(
          process.env.VUE_APP_BASE_API + "/common/uploadMinioDataReport",
          formData
        )
        .then((res) => {
          return {
            ...res.data,
          };
        });
    },
    /** 提交按钮 */
    handleSubmit() {
      console.log(this.answerList);
      console.log("test1",this.datesave);
      // 首先对 answerList 进行处理：合并、过滤和转换
      let processedLists = this.answerList
        .reduce((acc, current) => {
          return acc.concat(current.list);
        }, [])
        .filter((x) => {
          // 过滤条件
          console.log("test1",x.status);
          console.log(this.datesave[x.formId]);
          console.log(x.formValue);
          return (
            x.formValue != null &&
            x.formValue != "" &&
            ((!["1"].includes(x.status))&&
            (
              (["0","2","3"].includes(x.status) && this.datesave[x.formId]!=x.formValue))
              ||(["4"].includes(x.status))
            )
          );
        });

      // 对符合条件的元素进行 formValue 的转换
      processedLists.forEach((x) => {
        if (["0", "1"].includes(x.formType)) {
          x.formValue = parseFloat(x.formValue);
        }
        x.fcDate = this.queryParams.fcDate;
      });

      // 最后进行深拷贝
      let allLists = JSON.parse(JSON.stringify(processedLists));

      // console.log("allLists:", allLists);
      const now = new Date();
      
      let datestr="请确定是否要提交数据"
      if(this.queryParams.fcDate==null)
      {
        // 获取年月日
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要+1
        const day = String(now.getDate()).padStart(2, '0');

        // 格式化为 yyyy年MM月dd日
        const format1 = `${year}年${month}月${day}日`;

        // 格式化为 yyyy年MM月
        const format2 = `${year}年${month}月`;
        
        if(this.count=='1')
        {
          datestr='您未选择时间,请确定是否要提交'+format1+'的数据?'
        }
        else
        {
          datestr='您未选择时间,请确定是否要提交'+format2+'的数据?'
        }
      }
      this.$confirm(
        datestr,
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",

        }
      ).then(()=>{
        newAdd(allLists).then((res) => {
        this.getList();
        this.msgSuccess("保存成功");
      });
      }).catch(()=>{});

      // newAdd(allLists).then((res) => {
      //   this.getList();
      //   this.msgSuccess("保存成功");
      // });
    },
    
    handleFileUploadProgress() {
      this.upload.isUploading = true;
    },
    handleFileSuccess(response) {
      console.log(response)
      if (response.code == 200) {
        this.$modal.msgSuccess("上传成功");
        this.getList();
        this.importOpen = false;
        this.SpecialImportOpen = false;
      }
      else {
        this.$modal.msgError("上传失败")
      }
      this.upload.isUploading = false;
    },
    // 模板下载
    downloadTemplate(){
    
      if (
        this.queryImport.startDate == null ||
        this.queryImport.startDate == ""||
        this.queryImport.endDate == null||
        this.queryImport.endDate == ""
      ) {
        this.$notify.error({
          title: "错误",
          message: "导出前请先输入开始结束时间",
        });
        return;
      }
      this.queryImport.rootId = this.queryParams.dimensionalityId
      this.downloadFile(
        "/web/TYjy/answer/exportTemplate",
        {
          ...this.queryImport,
        },
        "(" +
          this.queryImport.startDate +
          "-" +
          this.queryImport.endDate +
          ")" +
          `数据.xlsx`
      );
    
    },

        // 模板下载
    downloadTemplateSpecial(){
      if (this.specialFcDate == null ) {
        this.specialFcDate= this.queryParams.fcDate
      }

      // if (
      //   this.specialFcDate == null 
      // ) {
      //   this.$notify.error({
      //     title: "错误",
      //     message: "导出前请先输入开始结束时间",
      //   });
      //   return;
      // }
      let queryImport={}
      queryImport.rootId = this.queryParams.dimensionalityId
      queryImport.fcDate = this.specialFcDate
      queryImport.type="0"
      this.downloadFile(
        "/web/TYjy/answer/exportTemplateSpecial",
        {
          ...queryImport,
        },
        this.dimensionalityName+"(" +this.specialFcDate+
          ")" +
          `数据.xlsx`
      );
  
    },

  },
};
</script>
<style scoped lang="scss">

.test{
  height: 100vh;
}

.excel-preview {
  margin-top: 20px;
  overflow: auto;
  max-height: 500px;
  border: 1px solid #ddd;
  padding: 10px;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 10px;
  /* 竖向滚动条宽度 */
  height: 15px;
  /* 横向滚动条宽度 */
  background-color: #ffffff;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  /* 其实直接在  ::-webkit-scrollbar 中设置也能达到同样的视觉效果*/
  /* -webkit-box-shadow: inset 0 0 6px rgba(177, 223, 117, 0.7); */
  background-color: #e4e4e4;
  border-radius: 10px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(158, 156, 156, 0.616);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 138, 138, 0.616);
  -webkit-box-shadow: unset;
}

::-webkit-scrollbar-thumb:window-inactive {
  /* 容器不被激活时的样式 */
  background: #bdbdbd66;
}

::-webkit-scrollbar-corner {
  /* 两个滚动条交汇处边角的样式 */
  background-color: #cacaca66;
}

.sticky {
  padding: 20px;
  position: -webkit-sticky;
  position: sticky;
  top: 0; /* 粘性定位的起始位置 */
  z-index: 100; /* 确保按钮在卡片之上 */
}
.el-tabs--card {
  height: calc(100vh - 110px);
}
.el-tab-pane {
  height: calc(100vh - 110px);
  overflow-y: auto;
}
</style>
  