<template>
  <div class="app-container">
    <!-- 质量成本表格 -->
    <div :style="containerStyle">
      <!-- 标题和产量信息 -->
      <div style="position: relative; width: 100%; display: flex; justify-content: center; margin: 20px 0;">
        <h3 style="text-align: center; margin: 0;">各分厂退货汇总表</h3>
        <div style="position: absolute; right: 0; bottom: -5px; font-size: 16px; font-weight: bold; color: #333;">
          <!-- 类型选择框 -->
          <!-- <el-select
            v-model="selectedType"
            placeholder="选择类型"
            style="width: 100px; margin-right: 10px;"
            @change="handleTypeChange">
            <el-option
              v-for="type in typeOptions"
              :key="type"
              :label="type"
              :value="type">
            </el-option>
          </el-select> -->
          <!-- 年份选择框 -->
          <el-select
            v-model="selectedYear"
            placeholder="选择年份"
            style="width: 100px; margin-right: 20px;"
            @change="handleYearChange">
            <el-option
              v-for="year in yearOptions"
              :key="year"
              :label="year + '年'"
              :value="year">
            </el-option>
          </el-select>

        </div>
      </div>

      <div :style="tableContainerStyle">
        <vxe-table
          :loading="loading"
          :data="qualityCostList"
          border
          v-bind="tableHeight ? { height: tableHeight } : {}"
          :class="{ 'no-scroll': !needScrollbar }"
          style="table-layout: fixed; width: 100%; margin: 0 auto; padding: 0;"
          :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#303133' }"
          :merge-cells="mergeCells"
          stripe
          :auto-resize="!tableHeight"
          :scroll-y="{enabled: needScrollbar}"
          :scroll-x="{enabled: true}">
          <vxe-column title="分厂" align="center" field="company" width="8%" fixed="left">
            <template #default="{ row }">
              <span :style="{ fontWeight: 'bold' }">
                {{ row.company }}
              </span>
            </template>
          </vxe-column>

          <!-- 一月分组 -->
          <vxe-colgroup title="一月" align="center">
            <vxe-column title="金额（元）" align="center" field="januaryAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.januaryAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="januaryPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.januaryPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 二月分组 -->
          <vxe-colgroup title="二月" align="center">
            <vxe-column title="金额（元）" align="center" field="februaryAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.februaryAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="februaryPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.februaryPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 三月分组 -->
          <vxe-colgroup title="三月" align="center">
            <vxe-column title="金额（元）" align="center" field="marchAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.marchAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="marchPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.marchPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 四月分组 -->
          <vxe-colgroup title="四月" align="center">
            <vxe-column title="金额（元）" align="center" field="aprilAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.aprilAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="aprilPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.aprilPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 五月分组 -->
          <vxe-colgroup title="五月" align="center">
            <vxe-column title="金额（元）" align="center" field="mayAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.mayAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="mayPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.mayPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 六月分组 -->
          <vxe-colgroup title="六月" align="center">
            <vxe-column title="金额（元）" align="center" field="juneAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.juneAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="junePerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.junePerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 七月分组 -->
          <vxe-colgroup title="七月" align="center">
            <vxe-column title="金额（元）" align="center" field="julyAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.julyAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="julyPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.julyPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 八月分组 -->
          <vxe-colgroup title="八月" align="center">
            <vxe-column title="金额（元）" align="center" field="augustAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.augustAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="augustPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.augustPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 九月分组 -->
          <vxe-colgroup title="九月" align="center">
            <vxe-column title="金额（元）" align="center" field="septemberAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.septemberAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="septemberPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.septemberPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 十月分组 -->
          <vxe-colgroup title="十月" align="center">
            <vxe-column title="金额（元）" align="center" field="octoberAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.octoberAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="octoberPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.octoberPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 十一月分组 -->
          <vxe-colgroup title="十一月" align="center">
            <vxe-column title="金额（元）" align="center" field="novemberAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.novemberAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="novemberPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.novemberPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 十二月分组 -->
          <vxe-colgroup title="十二月" align="center">
            <vxe-column title="金额（元）" align="center" field="decemberAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatCurrency(row.decemberAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="decemberPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'normal' }">
                  {{ formatTon(row.decemberPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 年度累计分组 -->
          <vxe-colgroup title="年度累计" align="center">
            <vxe-column title="金额（元）" align="center" field="yearlyTotalAmount" width="6%">
              <template #header>
                <span style="font-size: 12px;">金额（元）</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'bold' }">
                  {{ formatCurrency(row.yearlyTotalAmount) }}
                </span>
              </template>
            </vxe-column>
            <vxe-column title="吨位" align="center" field="yearlyTotalPerTon" width="6%">
              <template #header>
                <span style="font-size: 12px;">吨位</span>
              </template>
              <template #default="{ row }">
                <span :style="{ fontWeight: 'bold' }">
                  {{ formatTon(row.yearlyTotalPerTon) }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>
        </vxe-table>
      </div>
    </div>
  </div>
</template>

<script>
import { listCompanySummaryData } from "@/api/qualityCost/companySummary1";
import { testFactoryData, testFactoryDataWithRealValues, testDataMapping } from "./testData";


export default {
  name: "CompanySummary1",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 质量成本数据列表
      qualityCostList: [],
      // 合并单元格配置
      mergeCells: [],
      // 表格高度
      tableHeight: null,
      // 是否需要滚动条
      needScrollbar: true,
      // 年份选择
      selectedYear: new Date().getFullYear(),
      // 年份选项
      yearOptions: [],
      // 类型选择
      selectedType: '退货',
      // 类型选项
      typeOptions: ['改判', '报废', '脱合同', '退货']
    };
  },
  computed: {
    /** 容器样式 */
    containerStyle() {
      return {
        height: this.tableHeight ? `${this.tableHeight}px` : 'auto'
      };
    },
    /** 表格容器样式 */
    tableContainerStyle() {
      return {
        height: this.tableHeight ? `${this.tableHeight - 80}px` : 'auto'
      };
    }
  },
  mounted() {
    this.calculateTableHeight();
    window.addEventListener('resize', this.calculateTableHeight);
    this.generateYearOptions();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.calculateTableHeight);
  },
  methods: {
    getCompanySummaryData() {
      this.loading = true;

      const params = {
        yearMonth: this.selectedYear,
        companySummaryType: this.selectedType
      };

      listCompanySummaryData(params).then(response => {
        console.log('listCompanySummaryData:', response);
        if (response.data) {
          // 处理分厂数据格式
          this.processFactoryData(response.data);
        } else {
          this.qualityCostList = [];
        }
        this.loading = false;
      }).catch(error => {
        console.error('获取数据失败:', error);
        this.$message.error('获取质量成本数据失败');
        this.loading = false;
      });
    },

    /** 处理分厂数据格式 */
    processFactoryData(rawData) {
      console.log('开始处理分厂数据:', rawData);

      // 检查是否为分厂数据格式
      if (this.isFactoryDataFormat(rawData)) {
        console.log('检测到分厂数据格式，开始处理...');

        // 转换为表格格式
        const tableData = this.convertFactoryDataToTable(rawData);

        // 更新表格数据
        this.updateTableWithFactoryData(tableData);
      } else {
        console.log('不是分厂数据格式，使用默认处理');
        this.qualityCostList = [];
      }
    },

    /** 检查是否为分厂数据格式 */
    isFactoryDataFormat(data) {
      if (!data || typeof data !== 'object') return false;

      const factoryKeys = Object.keys(data);
      if (factoryKeys.length === 0) return false;

      const firstFactory = data[factoryKeys[0]];
      if (!firstFactory || typeof firstFactory !== 'object') return false;

      const monthKeys = Object.keys(firstFactory);
      if (monthKeys.length === 0) return false;

      const firstMonth = firstFactory[monthKeys[0]];
      return firstMonth &&
             typeof firstMonth === 'object' &&
             'costEx' in firstMonth &&
             'costTon' in firstMonth;
    },

    /** 清理模拟数据 */
    cleanMockData(data) {
      const cleanedData = {};

      Object.keys(data).forEach(factoryName => {
        const factoryData = data[factoryName];
        const validMonths = {};

        Object.keys(factoryData).forEach(monthKey => {
          const monthData = factoryData[monthKey];

          // 检查是否为模拟数据
          if (!this.isMockData(monthData)) {
            validMonths[monthKey] = monthData;
          }
        });

        // 只有当分厂有有效数据时才保留
        if (Object.keys(validMonths).length > 0) {
          cleanedData[factoryName] = validMonths;
        }
      });

      console.log('清理后的数据:', cleanedData);
      return cleanedData;
    },

    /** 判断是否为模拟数据 */
    isMockData(monthData) {
      return (
        monthData.costCenterName === null &&
        monthData.costEx === 0 &&
        monthData.costTon === 0 &&
        monthData.yearMonth === null
      );
    },

    /** 将分厂数据转换为表格格式 */
    convertFactoryDataToTable(factoryData) {
      const tableData = [];

      Object.keys(factoryData).forEach(factoryName => {
        const factoryMonths = factoryData[factoryName];

        Object.keys(factoryMonths).forEach(monthKey => {
          const monthData = factoryMonths[monthKey];

          // 创建表格行数据
          const rowData = {
            factoryName: factoryName,
            month: monthKey,
            costEx: monthData.costEx || 0,
            costTon: monthData.costTon || 0,
            yearMonth: monthData.yearMonth,
            costCenterName: monthData.costCenterName
          };

          tableData.push(rowData);
        });
      });

      console.log('转换后的表格数据:', tableData);
      return tableData;
    },

    /** 更新表格显示分厂数据 */
    updateTableWithFactoryData(tableData) {
      if (tableData.length === 0) {
        this.qualityCostList = [];
        return;
      }

      // 获取所有分厂名称
      const factoryNames = [...new Set(tableData.map(row => row.factoryName))];

      // 创建表格行数据
      this.qualityCostList = factoryNames.map(factoryName => {
        const rowData = {
          company: factoryName
        };

        // 初始化所有月份的数据为0
        const months = ['january', 'february', 'march', 'april', 'may', 'june',
                       'july', 'august', 'september', 'october', 'november', 'december'];

        months.forEach((month, index) => {
          const monthNumber = index + 1;
          const monthData = tableData.find(row =>
            row.factoryName === factoryName && row.month === monthNumber.toString()
          );

          if (monthData) {
            rowData[`${month}Amount`] = monthData.costEx;
            rowData[`${month}PerTon`] = monthData.costTon;
          } else {
            rowData[`${month}Amount`] = 0;
            rowData[`${month}PerTon`] = 0;
          }
        });

        // 计算年度累计
        rowData.yearlyTotalAmount = months.reduce((sum, month) => sum + (rowData[`${month}Amount`] || 0), 0);
        rowData.yearlyTotalPerTon = months.reduce((sum, month) => sum + (rowData[`${month}PerTon`] || 0), 0);

        return rowData;
      });

      console.log('更新后的表格数据:', this.qualityCostList);
    },


    /** 格式化货币 */
    formatCurrency(value) {
      if (value === null || value === undefined || value === '' || Number(value) === 0) {
        return '-';
      }
      const num = Number(value);
      // 金额显示为元，不转换为万
      return num.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    /** 格式化吨位 */
    formatTon(value) {
      if (value === null || value === undefined || value === '' || Number(value) === 0) {
        return '-';
      }
      const num = Number(value);
      // 吨位显示为吨，保留2位小数
      return num.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    /** 计算表格高度 */
    calculateTableHeight() {
      this.$nextTick(() => {
        const windowHeight = window.innerHeight;
        // 减去页面头部、标题等高度，大约100px
        const availableHeight = windowHeight - 100;
        // 设置表格最大高度，最小400px，最大不超过可用高度的90%
        this.tableHeight = Math.max(400, Math.min(800, availableHeight * 0.9));
        this.needScrollbar = this.qualityCostList.length > 8;
      });
    },

    /** 生成年份选项 */
    generateYearOptions() {
      const currentYear = new Date().getFullYear();
      for (let i = 2000; i <= currentYear; i++) {
        this.yearOptions.push(i);
      }
      this.getCompanySummaryData()
    },

         /** 年份选择变化 */
     handleYearChange(year) {
       this.selectedYear = year;
       // 重新获取数据
       this.getCompanySummaryData();
     },

     /** 类型选择变化 */
     handleTypeChange(type) {
       this.selectedType = type;
       // 重新获取数据
       this.getCompanySummaryData();
     },

     /** 测试分厂数据处理 */
     testFactoryDataProcessing() {
       console.log('开始测试分厂数据处理...');

       // 测试1: 纯模拟数据
       console.log('=== 测试1: 纯模拟数据 ===');
       this.processFactoryData(testFactoryData);

       // 等待2秒后测试真实数据
       setTimeout(() => {
         console.log('=== 测试2: 包含真实数据 ===');
         this.processFactoryData(testFactoryDataWithRealValues);
       }, 2000);

       // 等待4秒后测试数据映射
       setTimeout(() => {
         console.log('=== 测试3: 数据映射验证 ===');
         this.processFactoryData(testDataMapping);
         this.verifyDataMapping();
       }, 4000);
     },

     /** 验证数据映射 */
     verifyDataMapping() {
       console.log('验证数据映射...');

       if (this.qualityCostList.length > 0) {
         this.qualityCostList.forEach((row, index) => {
           console.log(`第${index + 1}行 - 分厂: ${row.company}`);
           console.log(`  一月金额: ${row.januaryAmount}, 一月吨位: ${row.januaryPerTon}`);
           console.log(`  二月金额: ${row.februaryAmount}, 二月吨位: ${row.februaryPerTon}`);
           console.log(`  三月金额: ${row.marchAmount}, 三月吨位: ${row.marchPerTon}`);
         });
       }
     }
  }
};
</script>

<style scoped>
/* 页面容器样式 */
.app-container {
  position: relative;
  padding: 20px;
}

/* 表格容器样式 */
.table-container {
  margin-top: 20px;
}

/* 表格滚动容器 */
.table-scroll-container {
  width: 100%;
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

/* 表格单元格内容 */
.table-cell-content {
  padding: 10px 0;
}
</style>

<style>
/* 非scoped样式，确保能够覆盖vxe-table的默认样式 */

/* 强制覆盖所有滚动条样式 - 15px粗细，淡蓝色主题 */
.vxe-table ::-webkit-scrollbar {
  width: 15px !important;
  height: 15px !important;
}

.vxe-table ::-webkit-scrollbar-track {
  background: #e3f2fd !important;
  border-radius: 7px !important;
}

.vxe-table ::-webkit-scrollbar-thumb {
  background: #64b5f6 !important;
  border-radius: 7px !important;
  border: none !important;
}

.vxe-table ::-webkit-scrollbar-thumb:hover {
  background: #42a5f5 !important;
}

.vxe-table ::-webkit-scrollbar-thumb:active {
  background: #2196f3 !important;
}

.vxe-table ::-webkit-scrollbar-corner {
  background: #e3f2fd !important;
}

/* vxe-table 表格边框和样式 */
.vxe-table ::v-deep .vxe-table {
  border: 1px solid #ebeef5;
}

.vxe-table ::v-deep .vxe-table--border-line {
  border-color: #ebeef5;
}

/* 表头样式 */
.vxe-table ::v-deep .vxe-table--header-wrapper .vxe-table--header {
  background-color: #f5f7fa;
}

.vxe-table ::v-deep .vxe-table--header .vxe-header--column {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 500;
}

/* 表头分组样式 */
.vxe-table ::v-deep .vxe-table--header .vxe-header--group {
  background-color: #e8f4fd !important;
  color: #1890ff !important;
  font-weight: 600 !important;
  border: 1px solid #d1e7ff !important;
}

/* 确保分组表头显示 */
.vxe-table ::v-deep .vxe-table--header .vxe-header--group .vxe-header--group-title {
  background-color: #e8f4fd !important;
  color: #1890ff !important;
  font-weight: 600 !important;
  padding: 8px 4px !important;
  text-align: center !important;
}

/* 子列表头样式 */
.vxe-table ::v-deep .vxe-table--header .vxe-header--column {
  background-color: #f5f7fa !important;
  color: #303133 !important;
  font-weight: 500 !important;
  border: 1px solid #ebeef5 !important;
}

/* 确保表头分组正确显示 */
.vxe-table ::v-deep .vxe-table--header-wrapper {
  border: 1px solid #ebeef5 !important;
}

.vxe-table ::v-deep .vxe-table--header {
  border-bottom: 1px solid #ebeef5 !important;
}

/* 强制显示分组表头 */
.vxe-table ::v-deep .vxe-table--header .vxe-header--group {
  display: table-cell !important;
  vertical-align: middle !important;
}

/* 分组表头文字样式 */
.vxe-table ::v-deep .vxe-header--group-title {
  display: block !important;
  width: 100% !important;
  text-align: center !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

/* 确保表头分组边框显示 */
.vxe-table ::v-deep .vxe-table--header .vxe-header--group {
  border-right: 1px solid #d1e7ff !important;
  border-bottom: 1px solid #d1e7ff !important;
}

/* 最后一个分组表头右边框 */
.vxe-table ::v-deep .vxe-table--header .vxe-header--group:last-child {
  border-right: 1px solid #ebeef5 !important;
}

/* 确保表头文字居中 */
.vxe-table ::v-deep .vxe-table--header .vxe-header--column .vxe-cell--title {
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
}
</style>
