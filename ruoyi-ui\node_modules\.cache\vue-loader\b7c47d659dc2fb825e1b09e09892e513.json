{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\supplier\\index.vue?vue&type=style&index=0&id=054d36c5&scoped=true&lang=css", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\supply\\supplier\\index.vue", "mtime": 1756456282822}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouc3VwcGxpZXItaW5mby1iYXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjVmN2ZhIDAlLCAjYzNjZmUyIDEwMCUpOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIHBhZGRpbmc6IDE2cHggMjBweDsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7DQp9DQoNCi5zdXBwbGllci1pbmZvLWNvbnRlbnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICBnYXA6IDhweDsNCn0NCg0KLnN1cHBsaWVyLWJhc2ljIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxMnB4Ow0KfQ0KDQouc3VwcGxpZXItY29kZSB7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGJhY2tncm91bmQ6IHJnYmEoNjQsIDE1OCwgMjU1LCAwLjEpOw0KICBwYWRkaW5nOiA0cHggMTJweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDY0LCAxNTgsIDI1NSwgMC4yKTsNCn0NCg0KLnN1cHBsaWVyLW5hbWUgew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQouc3VwcGxpZXItY29udGFjdCB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGdhcDogMjBweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCn0NCg0KLnN1cHBsaWVyLWNvbnRhY3Qgc3BhbiB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5vcGVyYXRpb24tYnV0dG9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLm9wZXJhdGlvbi1idXR0b25zIC5lbC1idXR0b24gew0KICBtYXJnaW46IDA7DQp9DQoNCi8qIOmZhOS7tueuoeeQhuagt+W8jyAqLw0KLnVwbG9hZC1zZWN0aW9uIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLnVwbG9hZC1kcmFnZ2VyIHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi51cGxvYWQtZHJhZ2dlciAuZWwtdXBsb2FkLWRyYWdnZXIgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMjBweDsNCiAgYm9yZGVyOiAxcHggZGFzaGVkICNkOWQ5ZDk7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZhZmFmYTsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgdHJhbnNpdGlvbjogYm9yZGVyLWNvbG9yIDAuM3M7DQp9DQoNCi51cGxvYWQtZHJhZ2dlciAuZWwtdXBsb2FkLWRyYWdnZXI6aG92ZXIgew0KICBib3JkZXItY29sb3I6ICM0MDlFRkY7DQp9DQoNCi51cGxvYWQtZHJhZ2dlciAuZWwtaWNvbi11cGxvYWQgew0KICBmb250LXNpemU6IDI4cHg7DQogIGNvbG9yOiAjYzBjNGNjOw0KICBtYXJnaW46IDIwcHggMCAxNnB4Ow0KICBsaW5lLWhlaWdodDogNTBweDsNCn0NCg0KLnVwbG9hZC1kcmFnZ2VyIC5lbC11cGxvYWRfX3RleHQgew0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi51cGxvYWQtZHJhZ2dlciAuZWwtdXBsb2FkX190ZXh0IGVtIHsNCiAgY29sb3I6ICM0MDlFRkY7DQogIGZvbnQtc3R5bGU6IG5vcm1hbDsNCn0NCg0KLnVwbG9hZC1kcmFnZ2VyIC5lbC11cGxvYWRfX3RpcCB7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBmb250LXNpemU6IDEycHg7DQogIGxpbmUtaGVpZ2h0OiAxLjU7DQogIG1hcmdpbi10b3A6IDhweDsNCn0NCg0KLmZpbGUtbGlzdC1zZWN0aW9uIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsNCiAgcGFkZGluZzogMjBweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KfQ0KDQouZmlsZS1saXN0LWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KfQ0KDQouZmlsZS1saXN0LXRpdGxlIHsNCiAgbWFyZ2luLWxlZnQ6IDhweDsNCiAgZm9udC1zaXplOiAxNnB4Ow0KfQ0KDQouZmlsZS1jb3VudCB7DQogIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KfQ0KDQouZmlsZS1saXN0LWNvbnRlbnQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIHBhZGRpbmc6IDEwcHg7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlYmVlZjU7DQp9DQoNCi5maWxlLWluZm8gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiA1cHg7DQp9DQoNCi5maWxlLW5hbWUgew0KICBtYXJnaW4tbGVmdDogOHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgbWF4LXdpZHRoOiAxNTBweDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6wBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/supply/supplier", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 供应商信息展示 -->\r\n    <div class=\"supplier-info-bar\">\r\n      <div class=\"supplier-info-content\">\r\n        <div class=\"supplier-basic\">\r\n          <span class=\"supplier-code\">{{ supplierCode }}</span>\r\n          <span class=\"supplier-name\">{{ supplierInfo.supplyName || '未设置供应商名称' }}</span>\r\n          <el-tag v-if=\"supplierInfo.status\" :type=\"supplierInfo.status === '1' ? 'success' : 'danger'\" size=\"mini\">\r\n            {{ supplierInfo.status === '1' ? '正常' : '停用' }}\r\n          </el-tag>\r\n        </div>\r\n        <div class=\"supplier-contact\" v-if=\"supplierInfo.contactPerson || supplierInfo.contactPhone\">\r\n          <span v-if=\"supplierInfo.contactPerson\">联系人：{{ supplierInfo.contactPerson }}</span>\r\n          <span v-if=\"supplierInfo.contactPhone\">电话：{{ supplierInfo.contactPhone }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 查询表单 -->\r\n    <el-form :inline=\"true\" :model=\"queryParams\" class=\"demo-form-inline\" @submit.native.prevent>\r\n      <el-form-item label=\"用户姓名\">\r\n        <el-input v-model=\"queryParams.userName\" placeholder=\"请输入用户姓名\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"身份证\">\r\n        <el-input v-model=\"queryParams.idcard\" placeholder=\"请输入身份证\" clearable />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">查询</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 工具栏 -->\r\n    <div style=\"margin-bottom: 10px;\">\r\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增人员</el-button>\r\n      <el-button type=\"success\" icon=\"el-icon-upload\" @click=\"handleImport\">导入</el-button>\r\n      <el-button type=\"warning\" icon=\"el-icon-download\" @click=\"handleExport\">导出</el-button>\r\n    </div>\r\n\r\n    <!-- 人员列表 -->\r\n    <el-table :data=\"userList\" border stripe style=\"width: 100%\">\r\n      <el-table-column prop=\"userName\" label=\"用户姓名\" min-width=\"100\" />\r\n      <el-table-column prop=\"idcard\" label=\"身份证\" min-width=\"160\" />\r\n      <el-table-column label=\"岗位识别卡\" min-width=\"110\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFacDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"健康信息\" min-width=\"100\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openHealthDialog(scope.row)\">补充/编辑</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"附件\" min-width=\"80\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"openFileDialog(scope.row)\">管理</el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" min-width=\"160\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"operation-buttons\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"downloadFile(scope.row)\"\r\n              title=\"下载\">\r\n              下载\r\n            </el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              title=\"编辑\">\r\n              编辑\r\n            </el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              title=\"删除\">\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 分页 -->\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 新增/编辑人员对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"用户姓名\" prop=\"userName\">\r\n          <el-input v-model=\"form.userName\" placeholder=\"请输入用户姓名\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证\" prop=\"idcard\">\r\n          <el-input v-model=\"form.idcard\" placeholder=\"请输入身份证\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n\r\n    <!-- 岗位识别卡弹窗 -->\r\n    <vxe-modal v-model=\"facDialogVisible\" title=\"岗位识别卡\" width=\"700\" show-footer>\r\n      <vxe-form\r\n        :data=\"facForm\"\r\n        :items=\"facFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"facDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitFac\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 健康信息弹窗 -->\r\n    <vxe-modal v-model=\"healthDialogVisible\" title=\"健康信息\" width=\"800\" show-footer>\r\n      <vxe-form\r\n        :data=\"healthForm\"\r\n        :items=\"healthFormItems\"\r\n        title-align=\"left\"\r\n        title-width=\"90\"\r\n        title-colon\r\n        border\r\n        size=\"small\"\r\n      />\r\n      <template #footer>\r\n        <vxe-button @click=\"healthDialogVisible = false\">取消</vxe-button>\r\n        <vxe-button status=\"primary\" @click=\"submitHealth\">保存</vxe-button>\r\n      </template>\r\n    </vxe-modal>\r\n\r\n    <!-- 附件管理弹窗 -->\r\n    <el-dialog\r\n      :visible.sync=\"fileDialogVisible\"\r\n      title=\"附件管理\"\r\n      width=\"800px\"\r\n      :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n    >\r\n      <!-- 上传区域 -->\r\n      <div class=\"upload-section\">\r\n        <el-upload\r\n          ref=\"fileUpload\"\r\n          :action=\"uploadUrl\"\r\n          :headers=\"upload.headers\"\r\n          :data=\"uploadData\"\r\n          :on-success=\"handleFileUploadSuccess\"\r\n          :on-error=\"handleFileUploadError\"\r\n          :before-upload=\"beforeFileUpload\"\r\n          :show-file-list=\"false\"\r\n          accept=\".pdf\"\r\n          drag\r\n          class=\"upload-dragger\"\r\n        >\r\n          <i class=\"el-icon-upload\"></i>\r\n          <div class=\"el-upload__text\">将PDF文件拖到此处，或<em>点击上传</em></div>\r\n          <div class=\"el-upload__tip\">仅支持PDF格式，单个文件不超过50MB</div>\r\n        </el-upload>\r\n      </div>\r\n\r\n      <!-- 文件列表 -->\r\n      <div class=\"file-list-section\">\r\n        <div class=\"file-list-header\">\r\n          <i class=\"el-icon-document\"></i>\r\n          <span class=\"file-list-title\">已上传文件</span>\r\n          <span class=\"file-count\">(共 {{fileList.length}} 个文件)</span>\r\n        </div>\r\n        <div class=\"file-list-content\">\r\n          <el-table\r\n            :data=\"fileList\"\r\n            style=\"width: 100%\"\r\n            :header-cell-style=\"{background:'#f5f7fa',color:'#606266'}\"\r\n          >\r\n            <el-table-column prop=\"filename\" label=\"文件名\" min-width=\"200\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"file-info\">\r\n                  <i class=\"el-icon-document\"></i>\r\n                  <span class=\"file-name\">{{scope.row.filename}}</span>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"format\" label=\"格式\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" type=\"info\">{{scope.row.format}}</el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"state\" label=\"状态\" width=\"80\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag\r\n                  size=\"mini\"\r\n                  :type=\"scope.row.state === 1 ? 'success' : 'danger'\"\r\n                >\r\n                  {{scope.row.state === 1 ? '正常' : '异常'}}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"200\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"operation-buttons\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-download\"\r\n                    @click=\"downloadFileItem(scope.row)\"\r\n                  >\r\n                    下载\r\n                  </el-button>\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"danger\"\r\n                    icon=\"el-icon-delete\"\r\n                    @click=\"deleteFile(scope.row)\"\r\n                  >\r\n                    删除\r\n                  </el-button>\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 人员导入对话框 -->\r\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :limit=\"1\"\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :action=\"upload.url\"\r\n        :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\"\r\n        :auto-upload=\"false\"\r\n        drag\r\n      >\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\r\n          <div class=\"el-upload__tip\" slot=\"tip\">\r\n            <el-checkbox v-model=\"upload.updateSupport\" /> 是否更新已经存在的用户数据\r\n          </div>\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n          <el-link type=\"primary\" :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" @click=\"importTemplate\">下载模板</el-link>\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" :loading=\"upload.isUploading\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request'\r\nimport { getToken } from '@/utils/auth'\r\n\r\nexport default {\r\n  name: 'SupplierInfo',\r\n  data() {\r\n    return {\r\n      // 供应商信息\r\n      supplierInfo: {},\r\n      supplierCode: '',\r\n      // 人员列表\r\n      userList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        supplyCode: null,\r\n        userName: null,\r\n        idcard: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        userName: [\r\n          { required: true, message: \"用户姓名不能为空\", trigger: \"blur\" }\r\n        ],\r\n        idcard: [\r\n          { required: true, message: \"身份证不能为空\", trigger: \"blur\" }\r\n        ]\r\n      },\r\n      // 总条数\r\n      total: 0,\r\n      // 岗位识别卡\r\n      facDialogVisible: false,\r\n      facForm: {},\r\n      facFormItems: [\r\n        { field: 'userPost', title: '岗位名称', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入岗位名称', rows: 2 } } },\r\n        { field: 'userFacClass', title: '岗位班组', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位班组' } } },\r\n        { field: 'userDeptName', title: '所属部门', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入所属部门' } } },\r\n        { field: 'userFacWork', title: '岗位描述', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入岗位描述' } } },\r\n        { field: 'userTimeBegin', title: '入厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'userTimeEnd', title: '离厂时间', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 24,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '起草', value: 0 },\r\n              { label: '分厂审核人', value: 1 },\r\n              { label: '人力资源部', value: 2 },\r\n              { label: '退回', value: -1 },\r\n              { label: '禁用', value: 101 },\r\n              { label: '审核通过', value: 99 },\r\n              { label: '删除', value: 102 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 健康信息\r\n      healthDialogVisible: false,\r\n      healthForm: {},\r\n      healthFormItems: [\r\n        { field: 'healdate', title: '体检日期', span: 12, itemRender: { name: 'VxeInput', props: { type: 'date', placeholder: '选择日期' } } },\r\n        { field: 'hos', title: '医院', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入医院' } } },\r\n        { field: 'healtz', title: '体重', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重' } } },\r\n        { field: 'healtzzs', title: '体重指数', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入体重指数' } } },\r\n        { field: 'healptt', title: '血糖', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入血糖' } } },\r\n        { field: 'healssy', title: '收缩压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入收缩压' } } },\r\n        { field: 'healszy', title: '舒张压', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入舒张压' } } },\r\n        { field: 'healzdgc', title: '总胆固醇', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入总胆固醇' } } },\r\n        { field: 'healgysz', title: '甘油三酯', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入甘油三酯' } } },\r\n        { field: 'healga', title: '谷氨酰转肽酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷氨酰转肽酶' } } },\r\n        { field: 'healgb', title: '谷丙转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷丙转氨酶' } } },\r\n        { field: 'healgc', title: '谷草转氨酶', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入谷草转氨酶' } } },\r\n        { field: 'healnsd', title: '尿素氮', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入尿素氮' } } },\r\n        { field: 'healjg', title: '肌酐', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入肌酐' } } },\r\n        { field: 'healxd', title: '心电图', span: 12, itemRender: { name: 'VxeInput', props: { placeholder: '请输入心电图' } } },\r\n        { field: 'healxj', title: '小结', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入小结', rows: 2 } } },\r\n        { field: 'healjy', title: '建议', span: 24, itemRender: { name: 'VxeTextarea', props: { placeholder: '请输入建议', rows: 2 } } },\r\n        {\r\n          field: 'state',\r\n          title: '状态',\r\n          span: 12,\r\n          itemRender: {\r\n            name: 'VxeSelect',\r\n            options: [\r\n              { label: '正常', value: 1 },\r\n              { label: '删除', value: 101 }\r\n            ],\r\n            props: { placeholder: '请选择' }\r\n          }\r\n        }\r\n      ],\r\n      // 附件管理\r\n      fileDialogVisible: false,\r\n      fileList: [],\r\n      uploadUrl: process.env.VUE_APP_BASE_API + '/web/supply/userfile/upload',\r\n      currentUserId: null,\r\n      currentUserInfo: {},\r\n      // 导入参数\r\n      upload: {\r\n        // 是否显示弹出层（导入）\r\n        open: false,\r\n        // 弹出层标题（导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 是否更新已经存在的用户数据\r\n        updateSupport: 0,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/web/supply/supplier/import\"\r\n      }\r\n    };\r\n  },\r\n  computed: {\r\n    uploadData() {\r\n      return {\r\n        userid: this.currentUserId\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.initSupplierCode();\r\n  },\r\n  methods: {\r\n    /** 初始化供应商代码 */\r\n    initSupplierCode() {\r\n      // 获取当前登录用户信息\r\n      this.$store.dispatch('GetInfo').then(res => {\r\n        const username = res.user.userName;\r\n        // 当前用户编号的前7位为供应商编号\r\n        this.supplierCode = username.substring(0, 7);\r\n        this.queryParams.supplyCode = this.supplierCode;\r\n\r\n        // 在获取到供应商代码后，再调用其他初始化方法\r\n        this.getSupplierInfo();\r\n        this.getList();\r\n      }).catch(() => {\r\n        this.$message.error('获取用户信息失败');\r\n      });\r\n    },\r\n    /** 获取供应商信息 */\r\n    getSupplierInfo() {\r\n      if (this.supplierCode) {\r\n        // 调用SupplyInfoController中的方法查询供应商信息\r\n        request.get(`/web/supply/info/getByCode/${this.supplierCode}`).then(response => {\r\n          if (response.code === 200 && response.data) {\r\n            this.supplierInfo = response.data;\r\n          } else {\r\n            // 如果供应商信息不存在，显示默认信息\r\n            this.supplierInfo = {\r\n              supplyCode: this.supplierCode,\r\n              supplyName: '',\r\n              contactPerson: '',\r\n              contactPhone: '',\r\n              address: '',\r\n              status: '1'\r\n            };\r\n          }\r\n        }).catch(() => {\r\n          // 如果供应商信息不存在，显示默认信息\r\n          this.supplierInfo = {\r\n            supplyCode: this.supplierCode,\r\n            supplyName: '',\r\n            contactPerson: '',\r\n            contactPhone: '',\r\n            address: '',\r\n            status: '1'\r\n          };\r\n        });\r\n      }\r\n    },\r\n    /** 查询人员列表 */\r\n    getList() {\r\n      // 调用供应商专用接口查询人员列表\r\n      request.get('/web/supply/supplier/list', {\r\n        params: this.queryParams\r\n      }).then(response => {\r\n        if (response.code === 200) {\r\n          this.userList = response.rows || [];\r\n          this.total = response.total || 0;\r\n        } else {\r\n          this.$message.error(response.msg || '查询失败');\r\n          this.userList = [];\r\n          this.total = 0;\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('查询失败: ' + error.message);\r\n        this.userList = [];\r\n        this.total = 0;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        supplyCode: this.supplierCode,\r\n        userName: null,\r\n        idcard: null\r\n      };\r\n      this.getList();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加人员\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      // 调用供应商专用接口获取人员信息\r\n      request.get(`/web/supply/supplier/user/${id}`).then(response => {\r\n        if (response.code === 200) {\r\n          this.form = response.data;\r\n          this.open = true;\r\n          this.title = \"修改人员\";\r\n        } else {\r\n          this.$message.error(response.msg || '获取人员信息失败');\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取人员信息失败: ' + error.message);\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            // 调用供应商专用修改接口\r\n            request.put('/web/supply/supplier/user', this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.$modal.msgSuccess(\"修改成功\");\r\n                this.open = false;\r\n                this.getList();\r\n              } else {\r\n                this.$message.error(response.msg || '修改失败');\r\n              }\r\n            }).catch(error => {\r\n              this.$message.error('修改失败: ' + error.message);\r\n            });\r\n          } else {\r\n            // 调用供应商专用新增接口\r\n            request.post('/web/supply/supplier/user', this.form).then(response => {\r\n              if (response.code === 200) {\r\n                this.$modal.msgSuccess(\"新增成功\");\r\n                this.open = false;\r\n                this.getList();\r\n              } else {\r\n                this.$message.error(response.msg || '新增失败');\r\n              }\r\n            }).catch(error => {\r\n              this.$message.error('新增失败: ' + error.message);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('是否确认删除人员编号为\"' + ids + '\"的数据项？').then(() => {\r\n        // 调用供应商专用删除接口\r\n        return request.delete(`/web/supply/supplier/user/${ids}`);\r\n      }).then(response => {\r\n        if (response.code === 200) {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        } else {\r\n          this.$message.error(response.msg || '删除失败');\r\n        }\r\n      }).catch(error => {\r\n        if (error !== 'cancel') {\r\n          this.$message.error('删除失败: ' + error.message);\r\n        }\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('web/supply/supplier/export', {\r\n        ...this.queryParams\r\n      }, `supplier_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 导入按钮操作 */\r\n    handleImport() {\r\n      this.upload.title = \"人员导入\";\r\n      this.upload.open = true;\r\n    },\r\n    /** 下载模板操作 */\r\n    importTemplate() {\r\n      this.download('web/supply/supplier/importTemplate', {}, `supplier_template_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 文件上传中处理 */\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n    /** 文件上传成功处理 */\r\n    handleFileSuccess(response, file, fileList) {\r\n      this.upload.open = false;\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.$alert(\"<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>\" + response.msg + \"</div>\", \"导入结果\", { dangerouslyUseHTMLString: true });\r\n      this.getList();\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        supplyCode: this.supplierCode,\r\n        userName: null,\r\n        idcard: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n\r\n    /** 打开岗位识别卡对话框 */\r\n    openFacDialog(row) {\r\n      // 调用供应商专用接口获取岗位识别卡信息\r\n      request.get(`/web/supply/supplier/fac/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          this.facForm = response.data || { userId: row.id };\r\n        } else {\r\n          this.facForm = { userId: row.id };\r\n        }\r\n        this.facDialogVisible = true;\r\n      }).catch(() => {\r\n        this.facForm = { userId: row.id };\r\n        this.facDialogVisible = true;\r\n      });\r\n    },\r\n    /** 提交岗位识别卡 */\r\n    submitFac() {\r\n      const url = this.facForm.id ? '/web/supply/supplier/fac' : '/web/supply/supplier/fac';\r\n      const method = this.facForm.id ? 'put' : 'post';\r\n\r\n      request[method](url, this.facForm).then(response => {\r\n        if (response.code === 200) {\r\n          this.$message.success('保存成功');\r\n          this.facDialogVisible = false;\r\n          this.getList();\r\n        } else {\r\n          this.$message.error(response.msg || '保存失败');\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('保存失败: ' + error.message);\r\n      });\r\n    },\r\n    /** 打开健康信息对话框 */\r\n    openHealthDialog(row) {\r\n      // 调用供应商专用接口获取健康信息\r\n      request.get(`/web/supply/supplier/health/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          this.healthForm = response.data || { userid: row.id };\r\n        } else {\r\n          this.healthForm = { userid: row.id };\r\n        }\r\n        this.healthDialogVisible = true;\r\n      }).catch(() => {\r\n        this.healthForm = { userid: row.id };\r\n        this.healthDialogVisible = true;\r\n      });\r\n    },\r\n    /** 提交健康信息 */\r\n    submitHealth() {\r\n      const url = this.healthForm.id ? '/web/supply/supplier/health' : '/web/supply/supplier/health';\r\n      const method = this.healthForm.id ? 'put' : 'post';\r\n\r\n      request[method](url, this.healthForm).then(response => {\r\n        if (response.code === 200) {\r\n          this.$message.success('保存成功');\r\n          this.healthDialogVisible = false;\r\n          this.getList();\r\n        } else {\r\n          this.$message.error(response.msg || '保存失败');\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('保存失败: ' + error.message);\r\n      });\r\n    },\r\n    /** 打开文件管理对话框 */\r\n    openFileDialog(row) {\r\n      this.currentUserId = row.id;\r\n      this.currentUserInfo = row;\r\n      this.getFileList(row.id);\r\n      this.fileDialogVisible = true;\r\n    },\r\n    /** 获取文件列表 */\r\n    getFileList(userid) {\r\n      // 调用供应商专用接口获取文件列表\r\n      request.get(`/web/supply/supplier/file/list/${userid}`).then(response => {\r\n        if (response.code === 200) {\r\n          this.fileList = response.rows || [];\r\n        } else {\r\n          this.fileList = [];\r\n        }\r\n      }).catch(() => {\r\n        this.fileList = [];\r\n      });\r\n    },\r\n    /** 文件上传成功处理 */\r\n    handleFileUploadSuccess(response) {\r\n      if (response.code === 200) {\r\n        this.$message.success('文件上传成功');\r\n        this.getFileList(this.currentUserId);\r\n      } else {\r\n        this.$message.error(response.msg || '文件上传失败');\r\n      }\r\n    },\r\n    /** 文件上传错误处理 */\r\n    handleFileUploadError(err) {\r\n      this.$message.error('文件上传失败: ' + (err.message || '未知错误'));\r\n    },\r\n    /** 文件上传前检查 */\r\n    beforeFileUpload(file) {\r\n      const isPDF = file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');\r\n      if (!isPDF) {\r\n        this.$message.error('只能上传PDF格式文件！');\r\n        return false;\r\n      }\r\n\r\n      const isLt50M = file.size / 1024 / 1024 < 50;\r\n      if (!isLt50M) {\r\n        this.$message.error('上传文件大小不能超过 50MB!');\r\n        return false;\r\n      }\r\n\r\n      return true;\r\n    },\r\n    /** 删除文件 */\r\n    deleteFile(row) {\r\n      this.$confirm('确定删除该附件吗？', '提示', { type: 'warning' }).then(() => {\r\n        // 调用供应商专用删除文件接口\r\n        request.delete(`/web/supply/supplier/file/${row.id}`).then(response => {\r\n          if (response.code === 200) {\r\n            this.$message.success('删除成功');\r\n            this.getFileList(this.currentUserId);\r\n          } else {\r\n            this.$message.error(response.msg || '删除失败');\r\n          }\r\n        }).catch(error => {\r\n          this.$message.error('删除失败: ' + error.message);\r\n        });\r\n      }).catch(() => {});\r\n    },\r\n    /** 下载单个文件 */\r\n    downloadFileItem(row) {\r\n      // 调用下载接口获取文件URL\r\n      request.get(`/web/supply/userfile/download/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          const fileUrl = response.data;\r\n          window.open(fileUrl, '_blank');\r\n        } else {\r\n          this.$message.error(response.msg || '下载失败');\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('下载失败: ' + error.message);\r\n      });\r\n    },\r\n    /** 下载文件 */\r\n    downloadFile(row) {\r\n      // 调用下载接口获取文件URL\r\n      request.get(`/web/supply/userfile/download/${row.id}`).then(response => {\r\n        if (response.code === 200) {\r\n          // 获取到文件URL后，在新窗口中打开下载\r\n          const fileUrl = response.data\r\n          window.open(fileUrl, '_blank')\r\n        } else {\r\n          this.$message.error(response.msg || '下载失败')\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('下载失败: ' + error.message)\r\n      })\r\n    },\r\n    /** 提交上传文件 */\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.supplier-info-bar {\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  padding: 16px 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.supplier-info-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.supplier-basic {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.supplier-code {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #409EFF;\r\n  background: rgba(64, 158, 255, 0.1);\r\n  padding: 4px 12px;\r\n  border-radius: 4px;\r\n  border: 1px solid rgba(64, 158, 255, 0.2);\r\n}\r\n\r\n.supplier-name {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.supplier-contact {\r\n  display: flex;\r\n  gap: 20px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.supplier-contact span {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.operation-buttons {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.operation-buttons .el-button {\r\n  margin: 0;\r\n}\r\n\r\n/* 附件管理样式 */\r\n.upload-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.upload-dragger {\r\n  width: 100%;\r\n}\r\n\r\n.upload-dragger .el-upload-dragger {\r\n  width: 100%;\r\n  height: 120px;\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  background-color: #fafafa;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-dragger .el-upload-dragger:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.upload-dragger .el-icon-upload {\r\n  font-size: 28px;\r\n  color: #c0c4cc;\r\n  margin: 20px 0 16px;\r\n  line-height: 50px;\r\n}\r\n\r\n.upload-dragger .el-upload__text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  text-align: center;\r\n}\r\n\r\n.upload-dragger .el-upload__text em {\r\n  color: #409EFF;\r\n  font-style: normal;\r\n}\r\n\r\n.upload-dragger .el-upload__tip {\r\n  color: #909399;\r\n  font-size: 12px;\r\n  line-height: 1.5;\r\n  margin-top: 8px;\r\n}\r\n\r\n.file-list-section {\r\n  background-color: #f5f7fa;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.file-list-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  color: #606266;\r\n}\r\n\r\n.file-list-title {\r\n  margin-left: 8px;\r\n  font-size: 16px;\r\n}\r\n\r\n.file-count {\r\n  margin-left: 10px;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.file-list-content {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  padding: 10px;\r\n  border: 1px solid #ebeef5;\r\n}\r\n\r\n.file-info {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.file-name {\r\n  margin-left: 8px;\r\n  font-size: 14px;\r\n  color: #303133;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  max-width: 150px;\r\n}\r\n</style>\r\n"]}]}