{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerForm.vue?vue&type=template&id=2d147b48&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\dataReport\\answer\\answerForm.vue", "mtime": 1756456493810}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}