package com.ruoyi.common.utils.EasyExcel;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFPatriarch;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.util.ResourceUtils;


import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.ruoyi.common.utils.file.FileUtils.getNetUrlHttps;

/**
 * @Description 根据模版导出Excel程序
 * <AUTHOR>
 * @Date 2024/2/19
 */
public class ExcelTemplateProc {

    /**
     * @param templateFileName
     * @param exportFilePathAndName
     * @param staticDataMap
     * @param dynamicDataMappingList
     * @return void
     * @description: 根据模版导出Excel入口
     * <AUTHOR>
     * @date 2024/2/20
     */
    public static void doExportExcelByTemplateProc(String templateFileName, String exportFilePathAndName,
                                                   Map<String, Object> staticDataMap,
                                                   List<DynamicDataMapping> dynamicDataMappingList) throws IOException {
        /**
         * 1. 从resources下加载模板并替换
         *    使用 ResourceUtils 加载文件
         */
        File file = getNetUrlHttps(templateFileName);
        InputStream inputStream = new FileInputStream(file);

        Workbook workbook = dealFirstSheetByTemplate(inputStream, staticDataMap, dynamicDataMappingList);
        // 2. 保存到本地
        saveExportFile(workbook, exportFilePathAndName);
    }



    public static void doExportExcelByTemplateProcWithSign(String templateFileName, String exportFilePathAndName,
                                                   Map<String, Object> staticDataMap,
                                                   List<DynamicDataMapping> dynamicDataMappingList,
                                                   boolean isSign, String imageUrl, int rowNum, int colNum) throws IOException {
        /**
         * 1. 从resources下加载模板并替换
         *    使用 ResourceUtils 加载文件
         */
        File file = getNetUrlHttps(templateFileName);
        InputStream inputStream = new FileInputStream(file);

        Workbook workbook = dealFirstSheetByTemplate(inputStream, staticDataMap, dynamicDataMappingList,isSign,imageUrl,rowNum,colNum);
        // 2. 保存到本地
        saveExportFile(workbook, exportFilePathAndName);
    }

    public static void doExportExcelByTemplateProcWithSign(String templateFileName, String exportFilePathAndName,
                                                           List<Map<String, Object>> staticDataMapList,
                                                           List<List<DynamicDataMapping>> dynamicDataMappingList,
                                                           List<Boolean> isSigns, List<String> imageUrls, List<Integer> rowNums, List<Integer> colNums) throws IOException {
        /**
         * 1. 从resources下加载模板并替换
         *    使用 ResourceUtils 加载文件
         */
        File file = getNetUrlHttps(templateFileName);
        InputStream inputStream = new FileInputStream(file);

        Workbook workbook = dealAllSheetByTemplate(inputStream, staticDataMapList, dynamicDataMappingList,isSigns,imageUrls,rowNums,colNums);
        // 2. 保存到本地
        saveExportFile(workbook, exportFilePathAndName);
    }

    /**
     * @param workbook
     * @param excelFilePath
     * @return void
     * @description: 保存导出的Excel文件到服务器
     * <AUTHOR>
     * @date 2024/2/20
     */
    public static void saveExportFile(Workbook workbook, String excelFilePath) throws IOException {
        FileOutputStream outputStream = new FileOutputStream(excelFilePath);
        executeWorkBookWrite(workbook, outputStream);
    }

    /**
     * @param workbook
     * @param outputStream
     * @return void
     * @description: 数据输出
     * <AUTHOR>
     * @date 2024/2/20
     */
    public static void executeWorkBookWrite(Workbook workbook, OutputStream outputStream) throws IOException {
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        workbook.close();
    }

    /**
     * @param inputStream
     * @param staticDataMap
     * @param dynamicDataMappingList
     * @return org.apache.poi.ss.usermodel.Workbook
     * @description: 处理只有一个sheet页的模版
     * <AUTHOR>
     * @date 2024/2/20
     */
    public static Workbook dealFirstSheetByTemplate(InputStream inputStream,
                                                    Map<String, Object> staticDataMap,
                                                    List<DynamicDataMapping> dynamicDataMappingList
    ) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        XSSFSheet sheet = workbook.getSheetAt(0);
        // 按模板处理sheet页
        dealSheetDataByTemplate(sheet, staticDataMap, dynamicDataMappingList);
        return workbook;
    }
    public static Workbook dealFirstSheetByTemplate(InputStream inputStream,
                                                    Map<String, Object> staticDataMap,
                                                    List<DynamicDataMapping> dynamicDataMappingList,
                                                    boolean isSign, String imageUrl, int rowNum, int colNum
                                                    ) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        XSSFSheet sheet = workbook.getSheetAt(0);
        // 按模板处理sheet页
        dealSheetDataByTemplate(sheet, staticDataMap, dynamicDataMappingList);
        if(isSign) insertImageToSheet(workbook, sheet,imageUrl, rowNum, colNum);
        return workbook;
    }

    public static Workbook dealAllSheetByTemplate(InputStream inputStream,
                                                    List<Map<String, Object>> staticDataMapList,
                                                    List<List<DynamicDataMapping>> dynamicDataMappingList,
                                                    List<Boolean> isSigns, List<String> imageUrls, List<Integer> rowNums, List<Integer> colNums
    ) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        // 遍历所有的sheet
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            XSSFSheet sheet = workbook.getSheetAt(i);
            dealSheetDataByTemplate(sheet, staticDataMapList.get(i), dynamicDataMappingList.get(i));
            // 对每个sheet应用数据处理逻辑
            if(isSigns.get(i)) insertImageToSheet(workbook, sheet,imageUrls.get(i), rowNums.get(i), colNums.get(i));
        }
        return workbook;
    }
    /**
     * 将图片插入单元格
     */
    private static void insertImageToSheet(Workbook workbook,Sheet sheet, String imageUrl, int rowNum, int colNum) throws IOException {
        File imageFile = getNetUrlHttps(imageUrl);
        InputStream inputStream = Files.newInputStream(imageFile.toPath());
        byte[] bytes = IOUtils.toByteArray(inputStream);

//        // 将图片插入到指定位置
//        int pictureIdx = workbook.addPicture(bytes, Workbook.PICTURE_TYPE_PNG);
//        CreationHelper helper = workbook.getCreationHelper();
//        Drawing<?> drawingPatriarch = sheet.createDrawingPatriarch();
//        ClientAnchor anchor = helper.createClientAnchor();
//
//        // 设置图片在Excel中的位置
//        anchor.setCol1(colNum); // 设置图片在Excel中的列索引
//        anchor.setRow1(rowNum); // 设置图片在Excel中的行索引
//        anchor.setRow2(rowNum+1);
//        anchor.setCol2(colNum+1);
//        anchor.setDx1(0);
//        anchor.setDx2(0);
//        anchor.setDy1(0);
//        anchor.setDy2(0);
//        // 可以设置col2, row2来调整图片大小
//        Picture picture = drawingPatriarch.createPicture(anchor, pictureIdx);
//        picture.resize();
// 将字节数组转换成输入流，以便在Excel中使用
        ByteArrayInputStream bis = new ByteArrayInputStream(bytes);

        // 创建画图对象，用于在sheet上绘制图片
        XSSFDrawing drawing = (XSSFDrawing) sheet.createDrawingPatriarch();
        // 创建客户端锚点，用于定位图片在sheet上的位置和大小
        ClientAnchor anchor = workbook.getCreationHelper().createClientAnchor();
        anchor.setCol1(colNum); // 列开始位置（0开始）
        anchor.setRow1(rowNum); // 行开始位置（0开始）
        anchor.setCol2(colNum+1); // 列结束位置（不包含此列）
        anchor.setRow2(rowNum+1); // 行结束位置（不包含此行）

        // 将图片插入到工作表中，并设置锚点
        drawing.createPicture(anchor, workbook.addPicture(bytes, Workbook.PICTURE_TYPE_PNG));


    }


    /**
     * @param sheet
     * @param staticDataMap
     * @param dynamicDataMappingList
     * @return void
     * @description: 按模板处理sheet页里的数据
     * <AUTHOR>
     * @date 2024/2/19
     */
    private static void dealSheetDataByTemplate(XSSFSheet sheet, Map<String, Object> staticDataMap, List<DynamicDataMapping> dynamicDataMappingList) {
        // 循环sheet里每一行
        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            XSSFRow row = sheet.getRow(i);
            DynamicDataMapping dynamicDataMapping = getDynamicRowDataByMatch(row, dynamicDataMappingList);
            if (dynamicDataMapping != null) {
                i = getTemplateLastRowIndexAfterDealTemplate(sheet, i, dynamicDataMapping);
            } else {
                dealTemplateDataRow(row, null, staticDataMap);
            }
        }
    }

    /**
     * @param row
     * @param dataMap
     * @param dataPrefix
     * @return void
     * @description: 循环处理模版中每行的数据
     * <AUTHOR>
     * @date 2024/2/20
     */
    private static void dealTemplateDataRow(XSSFRow row, String dataPrefix, Map<String, Object> dataMap) {
        if (dataMap == null) {
            return;
        }
        for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            XSSFCell cell = row.getCell(i);
            fillInTemplateCellDataValue(cell, dataPrefix, dataMap);
        }
    }

    /**
     * @param cell
     * @param dataPrefix
     * @param dataMap
     * @return void
     * @description: 填充模版里单元格的值
     * <AUTHOR>
     * @date 2024/2/20
     */
    private static void fillInTemplateCellDataValue(XSSFCell cell, String dataPrefix, Map<String, Object> dataMap) {
        if (cell == null) {
            return;
        }
        String cellValue = cell.getStringCellValue();//获取模版里设置的数据
        if (StringUtils.isEmpty(cellValue)) {
            return;
        }
        boolean flag = false;
        dataPrefix = StringUtils.isEmpty(dataPrefix) ? "" : (dataPrefix + ".");
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            // 循环所有，因为可能一行有多个占位符
            String cellTemplateStr = "{{" + dataPrefix + entry.getKey() + "}}";
            if (cellValue.contains(cellTemplateStr)) {
                // 替换模版中单元格的数据
                cellValue = cellValue.replace(cellTemplateStr, entry.getValue() == null ? "" : entry.getValue().toString());
                flag = true;
            }
        }
        if (flag) {
            cell.setCellValue(cellValue);
        }
    }

    /**
     * @param row
     * @param dynamicDataMappingList
     * @return com.liu.susu.excel.template.poi.common.DynamicDataMapping
     * @description: 通过模版sheet中的行数据 与 动态数据匹配,获取此行需要填充的动态数据
     * <AUTHOR>
     * @date 2024/2/21
     */
    private static DynamicDataMapping getDynamicRowDataByMatch(XSSFRow row, List<DynamicDataMapping> dynamicDataMappingList) {
        if (dynamicDataMappingList == null || dynamicDataMappingList.size() < 1) {
            return null;
        }
        for (int j = row.getFirstCellNum(); j < row.getLastCellNum(); j++) {
            XSSFCell cell = row.getCell(j);
            String value = cell.getStringCellValue();
            if (value != null) {
                for (DynamicDataMapping dynamicData : dynamicDataMappingList) {
                    if (value.startsWith("{{" + dynamicData.getDataId() + ".")) {
                        return dynamicData;
                    }
                }
            }
        }
        return null;
    }

    /**
     * @param sheet
     * @param rowIndex
     * @param dynamicDataMapping
     * @return int
     * @description: 根据动态数据的条数动态复制模版行，每处理一个类型的list返回最后的行数，进而处理下一个类型的list
     * <AUTHOR>
     * @date 2024/2/20
     */
    private static int getTemplateLastRowIndexAfterDealTemplate(XSSFSheet sheet, int rowIndex, DynamicDataMapping dynamicDataMapping) {
        if (dynamicDataMapping == null) {
            return rowIndex;
        }
        int dataRows = dynamicDataMapping.getDataList().size();
        // 需要拷贝的行数（因为模板行本身占1行，所以-1）
        int copyRows = dataRows - 1;
        if (copyRows > 0) {
            /**
             * shiftRows: 从动态数据模版行(rowIndex)到最后一行，这些全部行都向下移copyRows行
             *            相当于模版行上面插入n行空行（n=copyRows）
             */
            sheet.shiftRows(rowIndex, sheet.getLastRowNum(), copyRows, true, false);
            // 拷贝策略
            CellCopyPolicy cellCopyPolicy = makeCellCopyPolicy();
            // 因为从模版行开始向下平移了copyRows行，所以这里 模板行=rowIndex + copyRows,
            int templateDataRow = rowIndex + copyRows;
            // 因为模版行上新增了空行，所以要把模板所在行的模版 拷贝到上面新增的空行
            for (int i = 0; i < copyRows; i++) {
                //templateDataRow-模版行数据   rowIndex + i循环的当前空行
                sheet.copyRows(templateDataRow, templateDataRow, rowIndex + i, cellCopyPolicy);
            }
        }
        // 循环模版行：动态替换模版行（将模版行里的模版替换成动态数据）
        for (int j = rowIndex; j < rowIndex + dataRows; j++) {
            Map<String, Object> dataMap = dynamicDataMapping.getDataList().get(j - rowIndex);
            dealTemplateDataRow(sheet.getRow(j), dynamicDataMapping.getDataId(), dataMap);
        }
        return rowIndex + copyRows;
    }

    /**
     * @param
     * @return org.apache.poi.ss.usermodel.CellCopyPolicy
     * @description: 拷贝策略
     * <AUTHOR>
     * @date 2024/2/20
     */
    public static CellCopyPolicy makeCellCopyPolicy() {
        CellCopyPolicy cellCopyPolicy = new CellCopyPolicy();
        cellCopyPolicy.setCopyCellValue(true);
        cellCopyPolicy.setCopyCellStyle(true);
        return cellCopyPolicy;
    }

    /**
     * @param
     * @return org.apache.poi.ss.usermodel.CellCopyPolicy
     * @description: 单独针对运营信息交互系统进行接口改造工作
     * <AUTHOR>
     * @date 2025/8/5
     */


    public static XSSFWorkbook doExportExcelByTemplateProcByQcy(String templateFileName,
                                                            Map<String, Object> staticDataMap,
                                                            Map<String, JSONObject> jsonMap,
                                                            List<DynamicDataMapping> dynamicDataMappingList) throws IOException {
        /**
         * 1. 从resources下加载模板并替换
         *    使用 ResourceUtils 加载文件
         */
        File file = getNetUrlHttps(templateFileName);
        InputStream inputStream = new FileInputStream(file);
        XSSFWorkbook workbook = dealFirstSheetByTemplateByqcy(inputStream, staticDataMap,jsonMap, dynamicDataMappingList);
        return workbook;
    }
    public static File doenloadFileByQcy(String templateFileName) throws IOException {
        /**
         * 1. 从resources下加载模板并替换
         *    使用 ResourceUtils 加载文件
         */
        return getNetUrlHttps(templateFileName);
    }


    public static XSSFWorkbook dealFirstSheetByTemplateByqcy(InputStream inputStream,
                                                    Map<String, Object> staticDataMap,
                                                         Map<String, JSONObject> jsonMap,
                                                    List<DynamicDataMapping> dynamicDataMappingList
    ) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        XSSFSheet sheet = workbook.getSheetAt(0);
        // 按模板处理sheet页
        dealSheetDataByTemplateQcy(sheet, staticDataMap,jsonMap, dynamicDataMappingList);
        return workbook;
    }

    private static void dealSheetDataByTemplateQcy(XSSFSheet sheet, Map<String, Object> staticDataMap,Map<String, JSONObject> jsonMap, List<DynamicDataMapping> dynamicDataMappingList) {
        // 循环sheet里每一行
        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            XSSFRow row = sheet.getRow(i);
            dealTemplateDataRowQcy(row, null, staticDataMap,jsonMap);
//            DynamicDataMapping dynamicDataMapping = getDynamicRowDataByMatch(row, dynamicDataMappingList);
//            if (dynamicDataMapping != null) {
//                i = getTemplateLastRowIndexAfterDealTemplate(sheet, i, dynamicDataMapping);
//            } else {
//                dealTemplateDataRowQcy(row, null, staticDataMap,jsonMap);
//            }
        }
    }

    private static void dealTemplateDataRowQcy(XSSFRow row, String dataPrefix, Map<String, Object> dataMap,Map<String, JSONObject> jsonMap) {
        if (dataMap == null) {
            return;
        }
        for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
            XSSFCell cell = row.getCell(i);
            fillInTemplateCellDataValueQcy(cell, dataPrefix, dataMap,jsonMap);
        }
    }


    //此处也需要进行改造，保证汇报没有数据的情况给覆盖掉
    private static void fillInTemplateCellDataValueQcy(XSSFCell cell, String dataPrefix, Map<String, Object> dataMap,Map<String, JSONObject> jsonMap) {
        if (cell == null) {
            return;
        }
        String cellValue = new DataFormatter().formatCellValue(cell);;//获取模版里设置的数据
        if (StringUtils.isEmpty(cellValue)) {
            return;
        }
        boolean flag = false;
        String tyep="2";

        List<String> replaceList =extractBracedSubstrings(cellValue);
        for(String item:replaceList)
        {
            String stringvalue="";
            if(dataMap.get(item)==null)
            {
                stringvalue="";
            }
            else
            {
                stringvalue=dataMap.get(item).toString();
            }
//            String stringvalue=dataMap.get(item).toString();
            String cellTemplateStr = "{{" +item + "}}";
            if(stringvalue==null)
            {
                cellValue=cellValue.replace(cellTemplateStr,"");
            }
            else
            {
                cellValue=cellValue.replace(cellTemplateStr,stringvalue);
            }
            if(jsonMap.get(item)!=null)
            {
                tyep=jsonMap.get(item).get("formType").toString();
            }
//            String[] anrrylist=item.split(".");
//            if(anrrylist.length==0)
//            {
//                if(jsonMap.get(item)!=null)
//                {
//                    tyep=jsonMap.get(item).get("formType").toString();
//                }
//            }
//            else
//            {
//                if(jsonMap.get(anrrylist[0])!=null)
//                {
//                    tyep=jsonMap.get(item.split(".")[0]).get("formType").toString();
//                }
//            }

            flag = true;
        }
        if (flag)
        {
            if(tyep.equals("0"))
            {
                cell.setCellValue(Long.valueOf(cellValue));
            }
            else if(tyep.equals("1"))
            {
                double numericValue = Double.parseDouble(cellValue); // 转换为数字
                cell.setCellValue(numericValue); // 设置新的数值
                cell.setCellType(CellType.NUMERIC); // 确保类型为数值
            }
            else
            {
                cell.setCellValue(cellValue);
            }
        }

//        dataPrefix = StringUtils.isEmpty(dataPrefix) ? "" : (dataPrefix + ".");
//        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
//            // 循环所有，因为可能一行有多个占位符
//            String cellTemplateStr = "{{" + dataPrefix + entry.getKey() + "}}";
//            if (cellValue.contains(cellTemplateStr))
//            {
//                // 替换模版中单元格的数据
//                cellValue = cellValue.replace(cellTemplateStr, entry.getValue() == null ? "" : entry.getValue().toString());
//                if(jsonMap.get(entry.getKey().split(".")[0])!=null)
//                {
//                    tyep=jsonMap.get(entry.getKey()).get("formType").toString();
//                }
//                flag = true;
//            }
//        }
//        if (flag)
//        {
//            if(cellValue.equals(""))
//            {
//                cell.setCellValue("");
//            }
//            else
//            {
//                if(tyep.equals("0"))
//                {
//                    cell.setCellValue(Long.valueOf(cellValue));
//                }
//                else if(tyep.equals("1"))
//                {
//                    double numericValue = Double.parseDouble(cellValue); // 转换为数字
//                    cell.setCellValue(numericValue); // 设置新的数值
//                    cell.setCellType(CellType.NUMERIC); // 确保类型为数值
//                }
//                else
//                {
//                    cell.setCellValue(cellValue);
//                }
//            }
//        }

    }
    public  static List<String> extractBracedSubstrings(String input) {
        List<String> result = new ArrayList<>();

        // 使用正则表达式匹配{{}}包裹的内容
        Pattern pattern = Pattern.compile("\\{\\{(.*?)\\}\\}");
        Matcher matcher = pattern.matcher(input);

        // 查找所有匹配项
        while (matcher.find()) {
            // 添加匹配的内容（去掉{{和}}）
            result.add(matcher.group(1).trim());
        }

        return result;
    }

}
