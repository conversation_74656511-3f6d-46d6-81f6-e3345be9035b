<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.supp.mapper.SuppPunishmentMapper">
    
    <resultMap type="SuppPunishment" id="SuppPunishmentResult">
        <result property="recCreator"    column="REC_CREATOR"    />
        <result property="recCreateTime"    column="REC_CREATE_TIME"    />
        <result property="recRevisor"    column="REC_REVISOR"    />
        <result property="recReviseTime"    column="REC_REVISE_TIME"    />
        <result property="userNo"    column="USER_NO"    />
        <result property="userName"    column="USER_NAME"    />
        <result property="confirmNo"    column="CONFIRM_NO"    />
        <result property="confirmName"    column="CONFIRM_NAME"    />
        <result property="serialNo"    column="SERIAL_NO"    />
        <result property="companyCode"    column="COMPANY_CODE"    />
        <result property="deptNo"    column="DEPT_NO"    />
        <result property="suppId"    column="SUPP_ID"    />
        <result property="suppName"    column="SUPP_NAME"    />
        <result property="itemNo"    column="ITEM_NO"    />
        <result property="itemName"    column="ITEM_NAME"    />
        <result property="punishmentType"    column="PUNISHMENT_TYPE"    />
        <result property="suppType"    column="SUPP_TYPE"    />
        <result property="happenedTime"    column="HAPPENED_TIME"    />
        <result property="punishmentTime"    column="PUNISHMENT_TIME"    />
        <result property="confirmTime"    column="CONFIRM_TIME"    />
        <result property="punishmentReason"    column="PUNISHMENT_REASON"    />
        <result property="punishmentBasis"    column="PUNISHMENT_BASIS"    />
        <result property="punishmentMeasure"    column="PUNISHMENT_MEASURE"    />
        <result property="id"    column="id"    />
        <result property="stateId"    column="STATE_ID"    />
    </resultMap>
    <resultMap type="SuppInfo" id="SuppInfoResult">
        <result property="suppId"    column="SUPP_ID"    />
        <result property="suppName"    column="SUPP_NAME"    />
        <result property="suppShortName"    column="SUPP_SHORT_NAME"    />
        <result property="suppAddress"    column="SUPP_ADDRESS"    />
    </resultMap>
    <resultMap type="ServiceProject" id="ServiceProjectResult">
        <result property="serviceNo"    column="SERVICE_NO"    />
        <result property="serviceName"    column="SERVICE_NAME"    />
        <result property="projectNo"    column="PROJECT_NO"    />
        <result property="projectName"    column="PROJECT_NAME"    />
    </resultMap>
    <resultMap type="MaterialInfo" id="MaterialInfoResult">
        <result property="itemName"    column="ITEM_NAME"    />
        <result property="itemId"    column="ITEM_ID"    />
    </resultMap>
    <resultMap type="SuppUser" id="SuppUserResult">
        <result property="userGroup"    column="USER_GROUP"    />
    </resultMap>

    <sql id="selectSuppPunishment">
        select t.*,sys_user.nick_name as nick_name from supp_punishment t
        left join sys_user on t.user_no = sys_user.user_name
    </sql>
    <sql id="selectSuppInfoVo">
        select SUPP_ID, SUPP_NAME, SUPP_SHORT_NAME, SUPP_ADDRESS from TPOPG00
    </sql>

    <select id="selectSuppPunishmentList" parameterType="SuppPunishment" resultMap="SuppPunishmentResult">
        <include refid="selectSuppPunishment"/>
        <where>  
            <if test="recCreator != null  and recCreator != ''"> and REC_CREATOR = #{recCreator}</if>
            <if test="recCreateTime != null  and recCreateTime != ''"> and REC_CREATE_TIME = #{recCreateTime}</if>
            <if test="recRevisor != null  and recRevisor != ''"> and REC_REVISOR = #{recRevisor}</if>
            <if test="recReviseTime != null  and recReviseTime != ''"> and REC_REVISE_TIME = #{recReviseTime}</if>
            <if test="userNo != null  and userNo != ''"> and USER_NO = #{userNo}</if>
            <if test="serialNo != null  and serialNo != ''"> and SERIAL_NO = #{serialNo}</if>
            <if test="companyCode != null  and companyCode != ''"> and COMPANY_CODE = #{companyCode}</if>
            <if test="deptNo != null  and deptNo != ''"> and DEPT_NO = #{deptNo}</if>
            <if test="suppId != null  and suppId != ''"> and SUPP_ID like concat('%', #{suppId}, '%')</if>
            <if test="stateId != null and stateId !=0"> and STATE_ID = #{stateId}</if>
            <if test="suppName != null  and suppName != ''"> and SUPP_NAME like concat('%', #{suppName}, '%')</if>
            <if test="itemNo != null  and itemNo != ''"> and ITEM_NO like concat('%', #{itemNo}, '%')</if>
            <if test="itemName != null  and itemName != ''"> and ITEM_NAME like concat('%', #{itemName}, '%')</if>
            <if test="punishmentType != null  and punishmentType != ''"> and PUNISHMENT_TYPE = #{punishmentType}</if>
            <if test="suppType != null  and suppType != ''"> and SUPP_TYPE = #{suppType}</if>
            <if test="params.happenedTimeBeginTime != null and params.happenedTimeBeginTime != ''"><!-- 开始时间检索 -->
                AND date_format(HAPPENED_TIME,'%y%m%d') &gt;= date_format(#{params.happenedTimeBeginTime},'%y%m%d')
            </if>
            <if test="params.happenedTimeEndTime != null and params.happenedTimeEndTime != ''"><!-- 结束时间检索 -->
                AND date_format(HAPPENED_TIME,'%y%m%d') &lt;= date_format(#{params.happenedTimeEndTime},'%y%m%d')
            </if>
            <if test="params.punishmentTimeBeginTime != null and params.punishmentTimeBeginTime != ''"><!-- 开始时间检索 -->
                AND date_format(PUNISHMENT_TIME,'%y%m%d') &gt;= date_format(#{params.punishmentTimeBeginTime},'%y%m%d')
            </if>
            <if test="params.punishmentTimeEndTime != null and params.punishmentTimeEndTime != ''"><!-- 结束时间检索 -->
                AND date_format(PUNISHMENT_TIME,'%y%m%d') &lt;= date_format(#{params.punishmentTimeEndTime},'%y%m%d')
            </if>
            <if test="punishmentReason != null  and punishmentReason != ''"> and PUNISHMENT_REASON = #{punishmentReason}</if>
            <if test="punishmentBasis != null  and punishmentBasis != ''"> and PUNISHMENT_BASIS = #{punishmentBasis}</if>
            <if test="punishmentMeasure != null  and punishmentMeasure != ''"> and PUNISHMENT_MEASURE = #{punishmentMeasure}</if>
        </where>
        order by REC_CREATE_TIME desc
    </select>
    <select id="selectSuppInfoList" parameterType="SuppInfo" resultMap="SuppInfoResult">
        <include refid="selectSuppInfoVo"/>
        <where>
            <if test="suppId != null  and suppId != ''"> and SUPP_ID like '%' || #{suppId} ||'%'</if>
            <if test="suppName != null  and suppName != ''"> and SUPP_NAME like '%' || #{suppName} ||'%'</if>
        </where>
    </select>

    <select id="selectServiceList" parameterType="ServiceProject" resultMap="ServiceProjectResult">
        select service_no,service_name from service_project
        <where>
            service_no !=''
            <if test="serviceNo != null  and serviceNo != ''"> and service_no like concat('%', #{serviceNo}, '%')</if>
            <if test="serviceName != null  and serviceName != ''"> and service_name like concat('%', #{serviceName}, '%')</if>
        </where>
    </select>
    <select id="selectProjectList" parameterType="ServiceProject" resultMap="ServiceProjectResult">
        select project_no,project_name from service_project  
        <where>
            project_no !=''
            <if test="projectNo != null  and projectNo != ''"> and project_no like concat('%', #{projectNo}, '%')</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
        </where>
    </select>

    <select id="selectMaterialInfoList" parameterType="MaterialInfo" resultMap="MaterialInfoResult">
        select tpopc00.leaf_code as item_id, tpopc03.child_label as item_name from tpopc00
        left join tpopc03 on tpopc00.leaf_code = tpopc03.child_name
        <where>
            <if test="itemId != null  and itemId != ''"> and tpopc00.leaf_code like '%' || #{itemId} ||'%'</if>
            <if test="itemName != null  and itemName != ''"> and tpopc03.child_label like '%' || #{itemName} ||'%'</if>
        </where>
        group by tpopc00.leaf_code ,tpopc03.child_label

    </select>

    <select id="selectSuppPunishmentById" parameterType="Long" resultMap="SuppPunishmentResult">
        <include refid="selectSuppPunishment"/>
        where id = #{id}
    </select>
    <select id="selectSuppPunishmentSerialNo" resultMap="SuppPunishmentResult">
        select t.SERIAL_NO   from supp_punishment t
        WHERE t.STATE_ID=2 and substr(t.CONFIRM_TIME,1,6)=DATE_FORMAT(NOW(), '%Y%m')
        ORDER BY t.CONFIRM_TIME desc
        limit 1
    </select>
    <select id="selectUserGroupByUserName" parameterType="SuppUser" resultMap="SuppUserResult">
        select t.user_group   from supp_punishment_user t
        <where>
            <if test="userNo != null  and userNo != ''"> and USER_NO = #{userNo}</if>
        </where>

    </select>

    <insert id="insertSuppPunishment" parameterType="SuppPunishment" useGeneratedKeys="true" keyProperty="id">
        insert into supp_punishment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recCreator != null">REC_CREATOR,</if>
            <if test="recCreateTime != null">REC_CREATE_TIME,</if>
            <if test="recRevisor != null">REC_REVISOR,</if>
            <if test="recReviseTime != null">REC_REVISE_TIME,</if>
            <if test="userNo != null">USER_NO,</if>
            <if test="userName != null">USER_NAME,</if>
            <if test="confirmNo != null">CONFIRM_NO,</if>
            <if test="confirmName != null">CONFIRM_NAME,</if>
            <if test="confirmTime != null">CONFIRM_TIME,</if>
            <if test="serialNo != null">SERIAL_NO,</if>
            <if test="companyCode != null">COMPANY_CODE,</if>
            <if test="deptNo != null">DEPT_NO,</if>
            <if test="suppId != null">SUPP_ID,</if>
            <if test="stateId != null">STATE_ID,</if>
            <if test="suppName != null">SUPP_NAME,</if>
            <if test="itemNo != null">ITEM_NO,</if>
            <if test="itemName != null">ITEM_NAME,</if>
            <if test="punishmentType != null">PUNISHMENT_TYPE,</if>
            <if test="suppType != null">SUPP_TYPE,</if>
            <if test="happenedTime != null">HAPPENED_TIME,</if>
            <if test="punishmentTime != null">PUNISHMENT_TIME,</if>
            <if test="punishmentReason != null">PUNISHMENT_REASON,</if>
            <if test="punishmentBasis != null">PUNISHMENT_BASIS,</if>
            <if test="punishmentMeasure != null">PUNISHMENT_MEASURE,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recCreator != null">#{recCreator},</if>
            <if test="recCreateTime != null">#{recCreateTime},</if>
            <if test="recRevisor != null">#{recRevisor},</if>
            <if test="recReviseTime != null">#{recReviseTime},</if>
            <if test="userNo != null">#{userNo},</if>
            <if test="userName != null">#{userName},</if>
            <if test="confirmNo != null">#{confirmNo},</if>
            <if test="confirmName != null">#{confirmName},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
            <if test="serialNo != null">#{serialNo},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="deptNo != null">#{deptNo},</if>
            <if test="suppId != null">#{suppId},</if>
            <if test="stateId != null">#{stateId},</if>
            <if test="suppName != null">#{suppName},</if>
            <if test="itemNo != null">#{itemNo},</if>
            <if test="itemName != null">#{itemName},</if>
            <if test="punishmentType != null">#{punishmentType},</if>
            <if test="suppType != null">#{suppType},</if>
            <if test="happenedTime != null">#{happenedTime},</if>
            <if test="punishmentTime != null">#{punishmentTime},</if>
            <if test="punishmentReason != null">#{punishmentReason},</if>
            <if test="punishmentBasis != null">#{punishmentBasis},</if>
            <if test="punishmentMeasure != null">#{punishmentMeasure},</if>
         </trim>
    </insert>

    <update id="updateSuppPunishment" parameterType="SuppPunishment">
        update supp_punishment
        <trim prefix="SET" suffixOverrides=",">
            <if test="recCreator != null">REC_CREATOR = #{recCreator},</if>
            <if test="recCreateTime != null">REC_CREATE_TIME = #{recCreateTime},</if>
            <if test="recRevisor != null">REC_REVISOR = #{recRevisor},</if>
            <if test="recReviseTime != null">REC_REVISE_TIME = #{recReviseTime},</if>
            <if test="userNo != null">USER_NO = #{userNo},</if>
            <if test="serialNo != null">SERIAL_NO = #{serialNo},</if>
            <if test="companyCode != null">COMPANY_CODE = #{companyCode},</if>
            <if test="deptNo != null">DEPT_NO = #{deptNo},</if>
            <if test="suppId != null">SUPP_ID = #{suppId},</if>
            <if test="suppName != null">SUPP_NAME = #{suppName},</if>
            <if test="itemNo != null">ITEM_NO = #{itemNo},</if>
            <if test="itemName != null">ITEM_NAME = #{itemName},</if>
            <if test="punishmentType != null">PUNISHMENT_TYPE = #{punishmentType},</if>
            <if test="suppType != null">SUPP_TYPE = #{suppType},</if>
            <if test="happenedTime != null">HAPPENED_TIME = #{happenedTime},</if>
            <if test="punishmentTime != null">PUNISHMENT_TIME = #{punishmentTime},</if>
            <if test="punishmentReason != null">PUNISHMENT_REASON = #{punishmentReason},</if>
            <if test="punishmentBasis != null">PUNISHMENT_BASIS = #{punishmentBasis},</if>
            <if test="punishmentMeasure != null">PUNISHMENT_MEASURE = #{punishmentMeasure},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSuppPunishmentById" parameterType="Long">
        delete from supp_punishment where id = #{id}
    </delete>

    <delete id="deleteSuppPunishmentByIds" parameterType="String">
        delete from supp_punishment where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <update id="confirmSuppPunishmentById" parameterType="SuppPunishment">
        update supp_punishment
        <trim prefix="SET" suffixOverrides=",">
            <if test="recReviseTime != null">REC_REVISE_TIME = #{recReviseTime},</if>
            <if test="stateId != null">STATE_ID = #{stateId},</if>
            <if test="serialNo != null">SERIAL_NO = #{serialNo},</if>
            <if test="deptNo != null">DEPT_NO = #{deptNo},</if>
            <if test="confirmNo != null">CONFIRM_NO = #{confirmNo},</if>
            <if test="confirmName != null">CONFIRM_NAME = #{confirmName},</if>
            <if test="confirmTime != null">CONFIRM_TIME = #{confirmTime},</if>
            <if test="companyCode != null">COMPANY_CODE = #{companyCode},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>