{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\qualityCost\\dashboard.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\api\\qualityCost\\dashboard.js", "mtime": 1756456493790}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getPieChartData", "query", "request", "url", "method", "params", "getMultiLineChartData", "getQualityCostDetail", "getExternalCostDetail", "getInternalCostDetail", "getComboChartDetail", "getWaterfallChartDetail", "getFactoryRejectionChartDetail", "getFactoryScrapChartDetail", "getFactoryContractChartDetail", "getFactoryReturnChartDetail", "getScrapLossChartDetailsDetail", "getQualityObjectionLossDetail"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/api/qualityCost/dashboard.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询质量成本四大类别占比数据\r\nexport function getPieChartData(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getPieChartData',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n\r\nexport function getMultiLineChartData(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getMultiLineChartData',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getQualityCostDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getQualityCostDetail',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getExternalCostDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getExternalCostDetail',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getInternalCostDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getInternalCostDetail',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n\r\nexport function getComboChartDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getComboChartDetail',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getWaterfallChartDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getWaterfallChartDetail',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getFactoryRejectionChartDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getFactoryRejectionChartDetail',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getFactoryScrapChartDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getFactoryScrapChartDetail',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getFactoryContractChartDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getFactoryContractChartDetail',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getFactoryReturnChartDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getFactoryReturnChartDetail',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getScrapLossChartDetailsDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getScrapLossChartDetailsDetail',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getQualityObjectionLossDetail(query) {\r\n  return request({\r\n    url: '/qualityCost/qualityCostDashboard/getQualityObjectionLossDetail',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mDAAmD;IACxDC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAGO,SAASK,qBAAqBA,CAACL,KAAK,EAAE;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yDAAyD;IAC9DC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASM,oBAAoBA,CAACN,KAAK,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wDAAwD;IAC7DC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASO,qBAAqBA,CAACP,KAAK,EAAE;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yDAAyD;IAC9DC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASQ,qBAAqBA,CAACR,KAAK,EAAE;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yDAAyD;IAC9DC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAGO,SAASS,mBAAmBA,CAACT,KAAK,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uDAAuD;IAC5DC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASU,uBAAuBA,CAACV,KAAK,EAAE;EAC7C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,2DAA2D;IAChEC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASW,8BAA8BA,CAACX,KAAK,EAAE;EACpD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kEAAkE;IACvEC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASY,0BAA0BA,CAACZ,KAAK,EAAE;EAChD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8DAA8D;IACnEC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASa,6BAA6BA,CAACb,KAAK,EAAE;EACnD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iEAAiE;IACtEC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASc,2BAA2BA,CAACd,KAAK,EAAE;EACjD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,+DAA+D;IACpEC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASe,8BAA8BA,CAACf,KAAK,EAAE;EACpD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kEAAkE;IACvEC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASgB,6BAA6BA,CAAChB,KAAK,EAAE;EACnD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iEAAiE;IACtEC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}