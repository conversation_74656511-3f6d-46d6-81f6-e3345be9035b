{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\scrapDetail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\scrapDetail\\index.vue", "mtime": 1756456493917}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBjb3N0Q2VudGVybGlzdCB9IGZyb20gIkAvYXBpL3F1YWxpdHlDb3N0L3F1YWxpdHlDb3N0RGV0YWlsIjsNCmltcG9ydCB7IGxpc3RBbGxTY3JhcERldGFpbCwgZ2V0U3VtLGdldEFsbFN1bSB9IGZyb20gIkAvYXBpL3F1YWxpdHlDb3N0L3NjcmFwRGV0YWlsIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiU2NyYXBEZXRhaWwiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICB9LA0KICAgICAgLy8g5oiQ5pys5Lit5b+D5ZKM5Lya6K6h5pyfDQogICAgICBjb3N0Q2VudGVyOiAnJywNCiAgICAgIGFjY291bnRpbmdQZXJpb2Q6IHRoaXMuZ2V0RGVmYXVsdFllYXJNb250aCgpLA0KICAgICAgLy8g5paw5aKe77ya5piv5ZCm6K6h5YiS5YaF562b6YCJDQogICAgICBwbGFuRmxhZzogJzEnLCAvLyDpu5jorqTigJzmmK/igJ0NCiAgICAgIHBsYW5GbGFnT3B0aW9uczogWw0KICAgICAgICB7IGxhYmVsOiAn5YWo6YOoJywgdmFsdWU6ICcnIH0sDQogICAgICAgIHsgbGFiZWw6ICfmmK8nLCB2YWx1ZTogJzEnIH0sDQogICAgICAgIHsgbGFiZWw6ICflkKYnLCB2YWx1ZTogJzAnIH0NCiAgICAgIF0sDQogICAgICAvLyDooajmoLzmlbDmja4NCiAgICAgIHRhYmxlRGF0YTogW10sDQogICAgICAvLyDooajmoLzliqDovb3nirbmgIENCiAgICAgIHRhYmxlTG9hZGluZzogZmFsc2UsDQoNCiAgICAgIC8vIOaIkOacrOS4reW/g+mAiemhuQ0KICAgICAgY29zdENlbnRlck9wdGlvbnM6IFtdLA0KICAgICAgY29zdENlbnRlckxvYWRpbmc6IGZhbHNlLA0KICAgICAgdG90YWw6IDAsDQogICAgICBzdW1EYXRhOiB7fSwNCiAgICAgIGFsbFN1bURhdGE6IHt9LA0KICAgICAgLy8g5paw5aKe77ya5pCc57Si5Y+C5pWwDQogICAgICBzZWFyY2hQYXJhbXM6IHsNCiAgICAgICAgc2dTaWduOiAnJywNCiAgICAgICAgc2dTdGQ6ICcnLA0KICAgICAgICByZWFzb246ICcnLA0KICAgICAgICBzZWFyY2hNb2RlOiAn5qih57OK5pCc57SiJw0KICAgICAgfSwNCiAgICAgIC8vIOaQnOe0ouaooeW8j+mAiemhuQ0KICAgICAgc2VhcmNoTW9kZU9wdGlvbnM6IFsNCiAgICAgICAgeyBsYWJlbDogJ+aooeeziuaQnOe0oicsIHZhbHVlOiAn5qih57OK5pCc57SiJyB9LA0KICAgICAgICB7IGxhYmVsOiAn57K+56Gu5pCc57SiJywgdmFsdWU6ICfnsr7noa7mkJzntKInIH0NCiAgICAgIF0NCiAgICB9Ow0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8vIOiuoeeul+Wwj+iuoeaVsOaNrg0KICAgIHN1YnRvdGFsRGF0YSgpIHsNCiAgICAgIGlmICghdGhpcy50YWJsZURhdGEgfHwgdGhpcy50YWJsZURhdGEubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHJldHVybiBbew0KICAgICAgICAgIGxhYmVsOiAi5Lqn5ZOB5oql5bqf5o2f5aSx5bCP6K6hIiwNCiAgICAgICAgICB0b3RhbFRvbm5hZ2U6IDAsDQogICAgICAgICAgdG90YWxVbml0UHJpY2U6IDAsDQogICAgICAgICAgdG90YWxBbW91bnQ6IDANCiAgICAgICAgfV07DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHRvdGFsVG9ubmFnZSA9IHRoaXMuc3VtRGF0YS5jb3N0VG9uOw0KDQogICAgICBjb25zdCB0b3RhbFVuaXRQcmljZSA9IHRoaXMuc3VtRGF0YS5jb3N0UGVyVG9uOw0KDQogICAgICBjb25zdCB0b3RhbEFtb3VudCA9IHRoaXMuc3VtRGF0YS5jb3N0RXg7DQoNCiAgICAgIHJldHVybiBbew0KICAgICAgICBsYWJlbDogIuS6p+WTgeaKpeW6n+aNn+WkseWwj+iuoSIsDQogICAgICAgIHRvdGFsVG9ubmFnZTogdG90YWxUb25uYWdlLA0KICAgICAgICB0b3RhbFVuaXRQcmljZTogdG90YWxVbml0UHJpY2UsDQogICAgICAgIHRvdGFsQW1vdW50OiB0b3RhbEFtb3VudA0KICAgICAgfV07DQogICAgfSwNCiAgICAvLyDorqHnrpfmgLvorqHmlbDmja4NCiAgICB0b3RhbERhdGEoKSB7DQogICAgICAgaWYgKCF0aGlzLnRhYmxlRGF0YSB8fCB0aGlzLnRhYmxlRGF0YS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgcmV0dXJuIFt7DQogICAgICAgICAgbGFiZWw6ICLkuqflk4HmiqXlup/mjZ/lpLHmgLvorqEiLA0KICAgICAgICAgIHRvdGFsVG9ubmFnZTogMCwNCiAgICAgICAgICB0b3RhbFVuaXRQcmljZTogMCwNCiAgICAgICAgICB0b3RhbEFtb3VudDogMA0KICAgICAgICB9XTsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgdG90YWxUb25uYWdlID0gdGhpcy5hbGxTdW1EYXRhLmNvc3RUb247DQoNCiAgICAgIGNvbnN0IHRvdGFsVW5pdFByaWNlID0gdGhpcy5hbGxTdW1EYXRhLmNvc3RQZXJUb247DQoNCiAgICAgIGNvbnN0IHRvdGFsQW1vdW50ID0gdGhpcy5hbGxTdW1EYXRhLmNvc3RFeDsNCg0KICAgICAgcmV0dXJuIFt7DQogICAgICAgIGxhYmVsOiAi5Lqn5ZOB5oql5bqf5o2f5aSx5oC76K6hIiwNCiAgICAgICAgdG90YWxUb25uYWdlOiB0b3RhbFRvbm5hZ2UsDQogICAgICAgIHRvdGFsVW5pdFByaWNlOiB0b3RhbFVuaXRQcmljZSwNCiAgICAgICAgdG90YWxBbW91bnQ6IHRvdGFsQW1vdW50DQogICAgICB9XTsNCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgLy8g55uR5ZCs5oiQ5pys5Lit5b+D5Y+Y5YyWDQogICAgY29zdENlbnRlcjogew0KICAgICAgaGFuZGxlcigpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgdGhpcy5mZXRjaFRhYmxlRGF0YSgpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g55uR5ZCs5Lya6K6h5pyf5Y+Y5YyWDQogICAgYWNjb3VudGluZ1BlcmlvZDogew0KICAgICAgaGFuZGxlcigpIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgICAgdGhpcy5mZXRjaFRhYmxlRGF0YSgpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5paw5aKe77ya55uR5ZCs5piv5ZCm6K6h5YiS5YaF5Y+Y5YyWDQogICAgcGxhbkZsYWc6IHsNCiAgICAgIGhhbmRsZXIoKSB7DQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICAgIHRoaXMuZmV0Y2hUYWJsZURhdGEoKTsNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5nZXRDb3N0Q2VudGVyTGlzdCgpOw0KICAgIHRoaXMucXVlcnlQYXJhbXMucGxhbkZsYWcgPSB0aGlzLnBsYW5GbGFnOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOiOt+WPlum7mOiupOS8muiuoeacnyAqLw0KICAgIGdldERlZmF1bHRZZWFyTW9udGgoKSB7DQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOw0KICAgICAgY29uc3QgeWVhciA9IG5vdy5nZXRGdWxsWWVhcigpOw0KICAgICAgY29uc3QgbW9udGggPSBub3cuZ2V0TW9udGgoKSArIDE7IC8vIDEtMTINCiAgICAgIGNvbnN0IGRheSA9IG5vdy5nZXREYXRlKCk7DQogICAgICBjb25zdCBob3VyID0gbm93LmdldEhvdXJzKCk7DQoNCiAgICAgIC8vIOWmguaenOS7iuWkqeaYr+acrOaciDI15Y+3OOeCueWJje+8iOWQqzI15Y+3Nzo1Oe+8ie+8jOWImeeUqOS4iuS4quaciA0KICAgICAgaWYgKGRheSA8IDI4IHx8IChkYXkgPT09IDI4ICYmIGhvdXIgPCAxKSkgew0KICAgICAgICAvLyDlpITnkIYx5pyI5pe255qE6Leo5bm0DQogICAgICAgIGNvbnN0IHByZXZNb250aCA9IG1vbnRoID09PSAxID8gMTIgOiBtb250aCAtIDE7DQogICAgICAgIGNvbnN0IHByZXZZZWFyID0gbW9udGggPT09IDEgPyB5ZWFyIC0gMSA6IHllYXI7DQogICAgICAgIHJldHVybiBgJHtwcmV2WWVhcn0tJHtTdHJpbmcocHJldk1vbnRoKS5wYWRTdGFydCgyLCAnMCcpfWA7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gYCR7eWVhcn0tJHtTdHJpbmcobW9udGgpLnBhZFN0YXJ0KDIsICcwJyl9YDsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6I635Y+W5oiQ5pys5Lit5b+D5YiX6KGoDQogICAgZ2V0Q29zdENlbnRlckxpc3QoKSB7DQogICAgICB0aGlzLmNvc3RDZW50ZXJMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGNvc3RDZW50ZXJsaXN0KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY29zdENlbnRlck9wdGlvbnMgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgICAvLyDlpoLmnpzmnInmlbDmja7vvIzorr7nva7pu5jorqTpgInkuK3nrKzkuIDkuKoNCiAgICAgICAgaWYgKHRoaXMuY29zdENlbnRlck9wdGlvbnMubGVuZ3RoID4gMCkgew0KICAgICAgICAgIGNvbnNvbGUubG9nKCfojrflj5bmiJDmnKzkuK3lv4PliJfooag6JywgdGhpcy5jb3N0Q2VudGVyT3B0aW9ucyk7DQogICAgICAgICAgdGhpcy5jb3N0Q2VudGVyID0gdGhpcy5jb3N0Q2VudGVyT3B0aW9uc1swXS5rZXk7DQogICAgICAgICAgLy8g6K6+572u6buY6K6k5YC85ZCO77yM5Li75Yqo6Kem5Y+R5LiA5qyh5pWw5o2u6I635Y+WDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5mZXRjaFRhYmxlRGF0YSgpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluaIkOacrOS4reW/g+WIl+ihqOWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluaIkOacrOS4reW/g+WIl+ihqOWksei0pScpOw0KICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgIHRoaXMuY29zdENlbnRlckxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g6I635Y+W6KGo5qC85pWw5o2uDQogICAgZmV0Y2hUYWJsZURhdGEoKSB7DQogICAgICAvLyDlj6rmnInlvZPmiJDmnKzkuK3lv4PlkozkvJrorqHmnJ/pg73mnInlgLzml7bmiY3or7fmsYINCiAgICAgIGlmICghdGhpcy5jb3N0Q2VudGVyIHx8ICF0aGlzLmFjY291bnRpbmdQZXJpb2QpIHsNCiAgICAgICAgdGhpcy50YWJsZURhdGEgPSBbXTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICB0aGlzLnRhYmxlTG9hZGluZyA9IHRydWU7DQoNCiAgICAgIC8vIOW9k+mAieaLqSLmsZ/pmLTlhbTmvoTnibnnp43pkqLpk4Ei5pe277yM5p+l6K+i5omA5pyJ5pWw5o2u77yI5LiN5LygY29zdENlbnRlcuWPguaVsO+8iQ0KICAgICAgbGV0IGNvc3RDZW50ZXJQYXJhbSA9IHRoaXMuY29zdENlbnRlcjsNCiAgICAgIGNvbnN0IHNlbGVjdGVkT3B0aW9uID0gdGhpcy5jb3N0Q2VudGVyT3B0aW9ucy5maW5kKGl0ZW0gPT4gaXRlbS5rZXkgPT09IHRoaXMuY29zdENlbnRlcik7DQogICAgICBpZiAoc2VsZWN0ZWRPcHRpb24gJiYgc2VsZWN0ZWRPcHRpb24ubGFiZWwgPT09ICflhazlj7gnKSB7DQogICAgICAgIGNvc3RDZW50ZXJQYXJhbSA9ICcnOyAvLyDorr7nva7kuLrnqbrlrZfnrKbkuLLvvIzmn6Xor6LmiYDmnInmlbDmja4NCiAgICAgIH0NCg0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb3N0Q2VudGVyID0gY29zdENlbnRlclBhcmFtOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy55ZWFyTW9udGggPSB0aGlzLmFjY291bnRpbmdQZXJpb2QucmVwbGFjZSgnLScsICcnKTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGxhbkZsYWcgPSB0aGlzLnBsYW5GbGFnOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zZ1NpZ24gPSB0aGlzLnNlYXJjaFBhcmFtcy5zZ1NpZ247DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNnU3RkID0gdGhpcy5zZWFyY2hQYXJhbXMuc2dTdGQ7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnJlYXNvbiA9IHRoaXMuc2VhcmNoUGFyYW1zLnJlYXNvbjsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc2VhcmNoTW9kZSA9IHRoaXMuc2VhcmNoUGFyYW1zLnNlYXJjaE1vZGU7DQoNCiAgICAgIGxpc3RBbGxTY3JhcERldGFpbCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgLy90aGlzLnRhYmxlRGF0YSA9IChyZXNwb25zZS5yb3dzIHx8IFtdKS5maWx0ZXIoaXRlbSA9PiBpdGVtLmNvc3RFeCAhPT0gbnVsbCAmJiBpdGVtLmNvc3RFeCAhPT0gdW5kZWZpbmVkICYmIGl0ZW0uY29zdEV4ICE9PSAwKTsNCiAgICAgICAgdGhpcy50YWJsZURhdGEgPSByZXNwb25zZS5yb3dzIHx8IFtdOw0KICAgICAgICBjb25zb2xlLmxvZygn6I635Y+W5oql5bqf5o2f5aSx5pWw5o2uOicsIHRoaXMudGFibGVEYXRhKTsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsIHx8IDA7DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluaKpeW6n+aNn+WkseaVsOaNruWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluaKpeW6n+aNn+WkseaVsOaNruWksei0pScpOw0KICAgICAgICB0aGlzLnRhYmxlRGF0YSA9IFtdOw0KICAgICAgICB0aGlzLnRvdGFsID0gMDsNCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLnRhYmxlTG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQoNCiAgICAgIGdldFN1bSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5zdW1EYXRhID0gcmVzcG9uc2UuZGF0YSB8fCBbXTsNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5pWw5o2u5aSx6LSlJyk7DQogICAgICAgIHRoaXMuc3VtRGF0YSA9IFtdOw0KICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgIHRoaXMudGFibGVMb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCg0KICAgICAgZ2V0QWxsU3VtKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmFsbFN1bURhdGEgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bmlbDmja7lpLHotKUnKTsNCiAgICAgICAgdGhpcy5hbGxTdW1EYXRhID0gW107DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy50YWJsZUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5aSE55CG6K6h5YiS5YaF5qCH5b+X5YC8DQogICAgZ2V0UGxhbkZsYWdWYWx1ZShwbGFuRmxhZykgew0KICAgICAgaWYgKHBsYW5GbGFnID09PSAnMCcgfHwgcGxhbkZsYWcgPT09IDApIHsNCiAgICAgICAgcmV0dXJuICflkKYnOw0KICAgICAgfSBlbHNlIGlmIChwbGFuRmxhZyA9PT0gJzEnIHx8IHBsYW5GbGFnID09PSAxKSB7DQogICAgICAgIHJldHVybiAn5pivJzsNCiAgICAgIH0NCiAgICAgIHJldHVybiAn5pyq55+lJzsgLy8g5pei5LiN5pivMOS5n+S4jeaYrzHml7bmmL7npLrmnKrnn6UNCiAgICB9LA0KICAgIC8vIOiOt+WPluiuoeWIkuWGheagh+W/l+agh+etvuexu+Weiw0KICAgIGdldFBsYW5GbGFnVGFnVHlwZShwbGFuRmxhZykgew0KICAgICAgaWYgKHBsYW5GbGFnID09PSAnMCcgfHwgcGxhbkZsYWcgPT09IDApIHsNCiAgICAgICAgcmV0dXJuICdkYW5nZXInOyAvLyDnu7/oibINCiAgICAgIH0gZWxzZSBpZiAocGxhbkZsYWcgPT09ICcxJyB8fCBwbGFuRmxhZyA9PT0gMSkgew0KICAgICAgICByZXR1cm4gJ3N1Y2Nlc3MnOyAvLyDnuqLoibINCiAgICAgIH0NCiAgICAgIHJldHVybiAnd2FybmluZyc7IC8vIOm7hOiJsu+8iOacquefpeeKtuaAge+8iQ0KICAgIH0sDQogICAgLy8g6I635Y+W5b2T5YmN5bm05pyIDQogICAgZ2V0Q3VycmVudE1vbnRoKCkgew0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsNCiAgICAgIGNvbnN0IHllYXIgPSBub3cuZ2V0RnVsbFllYXIoKTsNCiAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKG5vdy5nZXRNb250aCgpICsgMSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIHJldHVybiBgJHt5ZWFyfS0ke21vbnRofWA7DQogICAgfSwNCiAgICAvLyDmoLzlvI/ljJbmlbDlrZfmmL7npLoNCiAgICBmb3JtYXROdW1iZXIodmFsdWUsIGRlY2ltYWxzID0gMikgew0KICAgICAgaWYgKHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSB1bmRlZmluZWQpIHJldHVybiAnJzsNCiAgICAgIHJldHVybiBOdW1iZXIodmFsdWUpLnRvTG9jYWxlU3RyaW5nKCd6aC1DTicsIHsNCiAgICAgICAgbWluaW11bUZyYWN0aW9uRGlnaXRzOiBkZWNpbWFscywNCiAgICAgICAgbWF4aW11bUZyYWN0aW9uRGlnaXRzOiBkZWNpbWFscw0KICAgICAgfSk7DQogICAgfSwNCiAgICBzdWJ0b3RhbFNwYW5NZXRob2QoeyByb3csIGNvbHVtbiwgcm93SW5kZXgsIGNvbHVtbkluZGV4IH0pIHsNCiAgICAgIC8vIOWQiOW5tuWJjTbliJfkuLrlsI/orqHmoIfnrb4NCiAgICAgIGlmIChjb2x1bW5JbmRleCA+PSAwICYmIGNvbHVtbkluZGV4IDw9IDYpIHsNCiAgICAgICAgaWYgKGNvbHVtbkluZGV4ID09PSAwKSB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIHJvd3NwYW46IDEsDQogICAgICAgICAgICBjb2xzcGFuOiA3DQogICAgICAgICAgfTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgcm93c3BhbjogMCwNCiAgICAgICAgICAgIGNvbHNwYW46IDANCiAgICAgICAgICB9Ow0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAoY29sdW1uSW5kZXggPj0gOSAmJiBjb2x1bW5JbmRleCA8PSAxMCkgew0KICAgICAgICBpZiAoY29sdW1uSW5kZXggPT09IDApIHsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgcm93c3BhbjogMSwNCiAgICAgICAgICAgIGNvbHNwYW46IDINCiAgICAgICAgICB9Ow0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICByb3dzcGFuOiAwLA0KICAgICAgICAgICAgY29sc3BhbjogMA0KICAgICAgICAgIH07DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC8vIOWFtuS7luWIl+S/neaMgeS4jeWPmA0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgcm93c3BhbjogMSwNCiAgICAgICAgY29sc3BhbjogMQ0KICAgICAgfTsNCiAgICB9LA0KICAgIC8vIOaAu+iuoeihjOWQiOW5tuaWueazlQ0KICAgIHRvdGFsU3Bhbk1ldGhvZCh7IHJvdywgY29sdW1uLCByb3dJbmRleCwgY29sdW1uSW5kZXggfSkgew0KICAgICAgLy8g5ZCI5bm25YmNNuWIl+S4uuaAu+iuoeagh+etvg0KICAgICAgaWYgKGNvbHVtbkluZGV4ID49IDAgJiYgY29sdW1uSW5kZXggPD0gNikgew0KICAgICAgICBpZiAoY29sdW1uSW5kZXggPT09IDApIHsNCiAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgcm93c3BhbjogMSwNCiAgICAgICAgICAgIGNvbHNwYW46IDcNCiAgICAgICAgICB9Ow0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICByb3dzcGFuOiAwLA0KICAgICAgICAgICAgY29sc3BhbjogMA0KICAgICAgICAgIH07DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGlmIChjb2x1bW5JbmRleCA+PSA5ICYmIGNvbHVtbkluZGV4IDw9IDEwKSB7DQogICAgICAgIGlmIChjb2x1bW5JbmRleCA9PT0gOSkgew0KICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICByb3dzcGFuOiAxLA0KICAgICAgICAgICAgY29sc3BhbjogMg0KICAgICAgICAgIH07DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIHJvd3NwYW46IDAsDQogICAgICAgICAgICBjb2xzcGFuOiAwDQogICAgICAgICAgfTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLy8g5YW25LuW5YiX5L+d5oyB5LiN5Y+YDQogICAgICByZXR1cm4gew0KICAgICAgICByb3dzcGFuOiAxLA0KICAgICAgICBjb2xzcGFuOiAxDQogICAgICB9Ow0KICAgIH0sDQogICAgLy8g5pCc57Si5oyJ6ZKu54K55Ye7DQogICAgaGFuZGxlU2VhcmNoKCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZmV0Y2hUYWJsZURhdGEoKTsNCiAgICB9LA0KICAgIC8vIOmHjee9ruaMiemSrueCueWHuw0KICAgIGhhbmRsZVJlc2V0KCkgew0KICAgICAgdGhpcy5zZWFyY2hQYXJhbXMgPSB7DQogICAgICAgIHNnU2lnbjogJycsDQogICAgICAgIHNnU3RkOiAnJywNCiAgICAgICAgcmVhc29uOiAnJywNCiAgICAgICAgc2VhcmNoTW9kZTogJ+aooeeziuaQnOe0oicNCiAgICAgIH07DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5mZXRjaFRhYmxlRGF0YSgpOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6LA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/scrapDetail", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"scrap-detail-container\">\r\n      <!-- 表格标题 -->\r\n      <div class=\"table-title\">\r\n        <h2>兴澄特钢质量成本表-产品报废损失</h2>\r\n      </div>\r\n\r\n      <!-- 表格头部信息 -->\r\n      <div class=\"table-header-info\">\r\n        <!-- <div class=\"header-item\">\r\n          <span class=\"label\">产品报废损失</span>\r\n        </div> -->\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">成本中心名称：</span>\r\n          <el-select v-model=\"costCenter\" placeholder=\"请选择成本中心\" style=\"width: 160px;\" :loading=\"costCenterLoading\">\r\n            <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">会计期：</span>\r\n          <el-date-picker v-model=\"accountingPeriod\" type=\"month\" placeholder=\"2025-06\" format=\"yyyy-MM\"\r\n            value-format=\"yyyy-MM\" style=\"width: 150px;\">\r\n          </el-date-picker>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">是否计划内：</span>\r\n          <el-select v-model=\"planFlag\" placeholder=\"全部\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in planFlagOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 搜索行 -->\r\n      <div class=\"search-bar-row\">\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">钢种：</span>\r\n          <el-input v-model=\"searchParams.sgSign\" placeholder=\"请输入钢种\" style=\"width: 150px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">标准：</span>\r\n          <el-input v-model=\"searchParams.sgStd\" placeholder=\"请输入标准\" style=\"width: 150px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">报废原因：</span>\r\n          <el-input v-model=\"searchParams.reason\" placeholder=\"请输入报废原因\" style=\"width: 150px;\" clearable />\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <span class=\"label\">搜索模式：</span>\r\n          <el-select v-model=\"searchParams.searchMode\" placeholder=\"请选择搜索模式\" style=\"width: 120px;\">\r\n            <el-option v-for=\"item in searchModeOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n        <div class=\"header-item\">\r\n          <el-button type=\"primary\" @click=\"handleSearch\" size=\"small\">搜索</el-button>\r\n          <el-button @click=\"handleReset\" size=\"small\">重置</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主表格 -->\r\n      <div class=\"main-table\">\r\n        <el-table :data=\"tableData\" border style=\"width: auto;\" class=\"scrap-detail-table\" v-loading=\"tableLoading\"\r\n          element-loading-text=\"加载中...\">\r\n          <el-table-column prop=\"sgSign\" label=\"钢种\" align=\"center\" />\r\n          <el-table-column prop=\"sgStd\" label=\"标准\" align=\"center\" />\r\n          <el-table-column prop=\"crShp\" label=\"截面\" align=\"center\" />\r\n          <el-table-column prop=\"thick\" label=\"厚度\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.thick !== null && scope.row.thick !== undefined\">\r\n                {{ scope.row.thick }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"width\" label=\"宽度\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.width !== null && scope.row.width !== undefined\">\r\n                {{ scope.row.width }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"len\" label=\"长度\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.len !== null && scope.row.len !== undefined\">\r\n                {{ scope.row.len }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column prop=\"costPerTon\" label=\"损失单价（元/吨）\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costPerTon !== null && scope.row.costPerTon !== undefined\">\r\n                {{ formatNumber(scope.row.costPerTon) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costTon\" label=\"吨位\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costTon !== null && scope.row.costTon !== undefined\">\r\n                {{ formatNumber(scope.row.costTon, 2) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"costEx\" label=\"损失金额（元）\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              <span v-if=\"scope.row.costEx !== null && scope.row.costEx !== undefined\">\r\n                {{ formatNumber(scope.row.costEx) }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"reason\" label=\"报废原因\" align=\"center\" />\r\n          <el-table-column prop=\"planFlag\" label=\"是否计划内\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"getPlanFlagTagType(scope.row.planFlag)\">\r\n                {{ getPlanFlagValue(scope.row.planFlag) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 小计行 -->\r\n      <div class=\"subtotal-section\">\r\n        <el-table :data=\"subtotalData\" border style=\"width: auto;\" class=\"subtotal-table\" :show-header=\"false\"\r\n          :span-method=\"subtotalSpanMethod\">\r\n          <el-table-column prop=\"label\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty1\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty2\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty3\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty4\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"totalTonnage\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalTonnage, 2) }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"totalUnitPrice\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalUnitPrice) }}\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"totalAmount\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"empty6\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty7\" label=\"\" align=\"center\" />\r\n        </el-table>\r\n      </div>\r\n\r\n      <!-- 总计行 -->\r\n      <div class=\"total-section\">\r\n        <el-table :data=\"totalData\" border style=\"width: auto;\" class=\"total-table\" :show-header=\"false\"\r\n          :span-method=\"totalSpanMethod\">\r\n          <el-table-column prop=\"label\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty1\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty2\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty3\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty4\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty5\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"totalTonnage\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalTonnage, 2) }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column prop=\"totalUnitPrice\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalUnitPrice) }}\r\n            </template>\r\n          </el-table-column> -->\r\n          <el-table-column prop=\"totalAmount\" label=\"\" align=\"right\">\r\n            <template #default=\"scope\">\r\n              {{ formatNumber(scope.row.totalAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"empty6\" label=\"\" align=\"center\" />\r\n          <el-table-column prop=\"empty7\" label=\"\" align=\"center\" />\r\n        </el-table>\r\n      </div>\r\n      <pagination :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"fetchTableData\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { listAllScrapDetail, getSum,getAllSum } from \"@/api/qualityCost/scrapDetail\";\r\n\r\nexport default {\r\n  name: \"ScrapDetail\",\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      },\r\n      // 成本中心和会计期\r\n      costCenter: '',\r\n      accountingPeriod: this.getDefaultYearMonth(),\r\n      // 新增：是否计划内筛选\r\n      planFlag: '1', // 默认“是”\r\n      planFlagOptions: [\r\n        { label: '全部', value: '' },\r\n        { label: '是', value: '1' },\r\n        { label: '否', value: '0' }\r\n      ],\r\n      // 表格数据\r\n      tableData: [],\r\n      // 表格加载状态\r\n      tableLoading: false,\r\n\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      costCenterLoading: false,\r\n      total: 0,\r\n      sumData: {},\r\n      allSumData: {},\r\n      // 新增：搜索参数\r\n      searchParams: {\r\n        sgSign: '',\r\n        sgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      },\r\n      // 搜索模式选项\r\n      searchModeOptions: [\r\n        { label: '模糊搜索', value: '模糊搜索' },\r\n        { label: '精确搜索', value: '精确搜索' }\r\n      ]\r\n    };\r\n  },\r\n  computed: {\r\n    // 计算小计数据\r\n    subtotalData() {\r\n      if (!this.tableData || this.tableData.length === 0) {\r\n        return [{\r\n          label: \"产品报废损失小计\",\r\n          totalTonnage: 0,\r\n          totalUnitPrice: 0,\r\n          totalAmount: 0\r\n        }];\r\n      }\r\n\r\n      const totalTonnage = this.sumData.costTon;\r\n\r\n      const totalUnitPrice = this.sumData.costPerTon;\r\n\r\n      const totalAmount = this.sumData.costEx;\r\n\r\n      return [{\r\n        label: \"产品报废损失小计\",\r\n        totalTonnage: totalTonnage,\r\n        totalUnitPrice: totalUnitPrice,\r\n        totalAmount: totalAmount\r\n      }];\r\n    },\r\n    // 计算总计数据\r\n    totalData() {\r\n       if (!this.tableData || this.tableData.length === 0) {\r\n        return [{\r\n          label: \"产品报废损失总计\",\r\n          totalTonnage: 0,\r\n          totalUnitPrice: 0,\r\n          totalAmount: 0\r\n        }];\r\n      }\r\n\r\n      const totalTonnage = this.allSumData.costTon;\r\n\r\n      const totalUnitPrice = this.allSumData.costPerTon;\r\n\r\n      const totalAmount = this.allSumData.costEx;\r\n\r\n      return [{\r\n        label: \"产品报废损失总计\",\r\n        totalTonnage: totalTonnage,\r\n        totalUnitPrice: totalUnitPrice,\r\n        totalAmount: totalAmount\r\n      }];\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听成本中心变化\r\n    costCenter: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 监听会计期变化\r\n    accountingPeriod: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    },\r\n    // 新增：监听是否计划内变化\r\n    planFlag: {\r\n      handler() {\r\n        this.queryParams.pageNum = 1;\r\n        this.fetchTableData();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCostCenterList();\r\n    this.queryParams.planFlag = this.planFlag;\r\n  },\r\n  methods: {\r\n    /** 获取默认会计期 */\r\n    getDefaultYearMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    },\r\n\r\n    // 获取成本中心列表\r\n    getCostCenterList() {\r\n      this.costCenterLoading = true;\r\n      costCenterlist().then(response => {\r\n        this.costCenterOptions = response.data || [];\r\n        // 如果有数据，设置默认选中第一个\r\n        if (this.costCenterOptions.length > 0) {\r\n          console.log('获取成本中心列表:', this.costCenterOptions);\r\n          this.costCenter = this.costCenterOptions[0].key;\r\n          // 设置默认值后，主动触发一次数据获取\r\n          this.$nextTick(() => {\r\n            this.fetchTableData();\r\n          });\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取成本中心列表失败:', error);\r\n        this.$message.error('获取成本中心列表失败');\r\n      }).finally(() => {\r\n        this.costCenterLoading = false;\r\n      });\r\n    },\r\n    // 获取表格数据\r\n    fetchTableData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod) {\r\n        this.tableData = [];\r\n        return;\r\n      }\r\n\r\n      this.tableLoading = true;\r\n\r\n      // 当选择\"江阴兴澄特种钢铁\"时，查询所有数据（不传costCenter参数）\r\n      let costCenterParam = this.costCenter;\r\n      const selectedOption = this.costCenterOptions.find(item => item.key === this.costCenter);\r\n      if (selectedOption && selectedOption.label === '公司') {\r\n        costCenterParam = ''; // 设置为空字符串，查询所有数据\r\n      }\r\n\r\n      this.queryParams.costCenter = costCenterParam;\r\n      this.queryParams.yearMonth = this.accountingPeriod.replace('-', '');\r\n      this.queryParams.planFlag = this.planFlag;\r\n      this.queryParams.sgSign = this.searchParams.sgSign;\r\n      this.queryParams.sgStd = this.searchParams.sgStd;\r\n      this.queryParams.reason = this.searchParams.reason;\r\n      this.queryParams.searchMode = this.searchParams.searchMode;\r\n\r\n      listAllScrapDetail(this.queryParams).then(response => {\r\n        //this.tableData = (response.rows || []).filter(item => item.costEx !== null && item.costEx !== undefined && item.costEx !== 0);\r\n        this.tableData = response.rows || [];\r\n        console.log('获取报废损失数据:', this.tableData);\r\n        this.total = response.total || 0;\r\n      }).catch(error => {\r\n        console.error('获取报废损失数据失败:', error);\r\n        this.$message.error('获取报废损失数据失败');\r\n        this.tableData = [];\r\n        this.total = 0;\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n\r\n      getSum(this.queryParams).then(response => {\r\n        this.sumData = response.data || [];\r\n      }).catch(error => {\r\n        this.$message.error('获取数据失败');\r\n        this.sumData = [];\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n\r\n      getAllSum(this.queryParams).then(response => {\r\n        this.allSumData = response.data || [];\r\n      }).catch(error => {\r\n        this.$message.error('获取数据失败');\r\n        this.allSumData = [];\r\n      }).finally(() => {\r\n        this.tableLoading = false;\r\n      });\r\n    },\r\n    // 处理计划内标志值\r\n    getPlanFlagValue(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return '否';\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return '是';\r\n      }\r\n      return '未知'; // 既不是0也不是1时显示未知\r\n    },\r\n    // 获取计划内标志标签类型\r\n    getPlanFlagTagType(planFlag) {\r\n      if (planFlag === '0' || planFlag === 0) {\r\n        return 'danger'; // 绿色\r\n      } else if (planFlag === '1' || planFlag === 1) {\r\n        return 'success'; // 红色\r\n      }\r\n      return 'warning'; // 黄色（未知状态）\r\n    },\r\n    // 获取当前年月\r\n    getCurrentMonth() {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = String(now.getMonth() + 1).padStart(2, '0');\r\n      return `${year}-${month}`;\r\n    },\r\n    // 格式化数字显示\r\n    formatNumber(value, decimals = 2) {\r\n      if (value === null || value === undefined) return '';\r\n      return Number(value).toLocaleString('zh-CN', {\r\n        minimumFractionDigits: decimals,\r\n        maximumFractionDigits: decimals\r\n      });\r\n    },\r\n    subtotalSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 合并前6列为小计标签\r\n      if (columnIndex >= 0 && columnIndex <= 6) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 7\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      if (columnIndex >= 9 && columnIndex <= 10) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      // 其他列保持不变\r\n      return {\r\n        rowspan: 1,\r\n        colspan: 1\r\n      };\r\n    },\r\n    // 总计行合并方法\r\n    totalSpanMethod({ row, column, rowIndex, columnIndex }) {\r\n      // 合并前6列为总计标签\r\n      if (columnIndex >= 0 && columnIndex <= 6) {\r\n        if (columnIndex === 0) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 7\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      if (columnIndex >= 9 && columnIndex <= 10) {\r\n        if (columnIndex === 9) {\r\n          return {\r\n            rowspan: 1,\r\n            colspan: 2\r\n          };\r\n        } else {\r\n          return {\r\n            rowspan: 0,\r\n            colspan: 0\r\n          };\r\n        }\r\n      }\r\n      // 其他列保持不变\r\n      return {\r\n        rowspan: 1,\r\n        colspan: 1\r\n      };\r\n    },\r\n    // 搜索按钮点击\r\n    handleSearch() {\r\n      this.queryParams.pageNum = 1;\r\n      this.fetchTableData();\r\n    },\r\n    // 重置按钮点击\r\n    handleReset() {\r\n      this.searchParams = {\r\n        sgSign: '',\r\n        sgStd: '',\r\n        reason: '',\r\n        searchMode: '模糊搜索'\r\n      };\r\n      this.queryParams.pageNum = 1;\r\n      this.fetchTableData();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.scrap-detail-container {\r\n  background: #fff;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-title {\r\n  text-align: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-title h2 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.table-header-info {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 24px;\r\n}\r\n\r\n.header-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.header-item .label {\r\n  font-weight: bold;\r\n  color: #606266;\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .value {\r\n  color: #303133;\r\n}\r\n\r\n.header-item:first-child .label {\r\n  color: #303133;\r\n  font-size: 16px;\r\n}\r\n\r\n.search-bar-row {\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 10px 0;\r\n  gap: 24px;\r\n}\r\n\r\n.main-table {\r\n  margin-bottom: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.subtotal-section {\r\n  margin-top: -1px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n/* 表格样式定制 */\r\n.scrap-detail-table {\r\n  font-size: 14px;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__header-wrapper) {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__header th) {\r\n  background-color: #f5f7fa;\r\n  color: #303133;\r\n  font-weight: bold;\r\n  padding: 12px 0;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__body tr:nth-child(odd)) {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.scrap-detail-table :deep(.el-table__body tr:hover) {\r\n  background-color: #f0f9ff;\r\n}\r\n\r\n/* 小计表格样式 */\r\n.subtotal-table {\r\n  font-size: 14px;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__header-wrapper) {\r\n  display: none !important;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body tr) {\r\n  background-color: #f0f9ff;\r\n  font-weight: bold;\r\n}\r\n\r\n.subtotal-table :deep(.el-table__body td) {\r\n  background-color: #f0f9ff !important;\r\n  padding: 12px 0;\r\n}\r\n\r\n/* 总计表格样式 */\r\n.total-section {\r\n  margin-top: -1px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.total-table {\r\n  font-size: 14px;\r\n}\r\n\r\n.total-table :deep(.el-table__header) {\r\n  display: none !important;\r\n}\r\n\r\n.total-table :deep(.el-table__header-wrapper) {\r\n  display: none !important;\r\n}\r\n\r\n.total-table :deep(.el-table__body tr) {\r\n  background-color: #ff4d4f;\r\n  font-weight: bold;\r\n  color: white;\r\n}\r\n\r\n.total-table :deep(.el-table__body td) {\r\n  background-color: #ff4d4f !important;\r\n  padding: 12px 0;\r\n  color: white;\r\n}\r\n\r\n/* 标签样式 */\r\n.el-tag {\r\n  font-size: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .table-header-info {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .search-bar-row {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n\r\n  .scrap-detail-table {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .scrap-detail-table :deep(.el-table__body td) {\r\n    padding: 8px 0;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .scrap-detail-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .main-table {\r\n    overflow-x: auto;\r\n  }\r\n}\r\n\r\n/* 搜索区域样式 */\r\n.header-item .el-input {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button {\r\n  margin-right: 8px;\r\n}\r\n\r\n.header-item .el-button:last-child {\r\n  margin-right: 0;\r\n}\r\n</style>\r\n"]}]}