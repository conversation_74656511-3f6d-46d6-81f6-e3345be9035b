{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\assess\\self\\index.vue", "mtime": 1756456493795}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_info", "require", "_user", "_vueSignaturePad", "_default", "exports", "default", "components", "VueSignaturePad", "name", "data", "_this", "loading", "showSearch", "openSign", "list", "title", "open", "queryParams", "userId", "workNo", "deptId", "assessDate", "assessDateText", "deptName", "form", "rules", "userInfo", "spanList", "itemList", "standardList", "resetShow", "readOnly", "deptOptions", "id", "selfScore", "status", "info", "beAssessedList", "rejectReason", "selfSign", "signOptions", "onBegin", "$refs", "signaturePad", "resizeCanvas", "backgroundColor", "sign", "file", "fileList", "upload", "url", "process", "env", "VUE_APP_BASE_API", "isUploading", "created", "getDefaultAssessDate", "replace", "getReportDeptList", "methods", "now", "Date", "currentDay", "getDate", "targetDate", "getFullYear", "getMonth", "year", "month", "concat", "_this2", "then", "res", "console", "log", "code", "handleDeptList", "getByWorkNoDeptId", "_this3", "getList", "getBeAssessedList", "_this4", "listBeAssessed", "length", "for<PERSON>ach", "item", "_toConsumableArray2", "hrLateralAssessInfoList", "_this5", "getInfoByDate", "response", "Array", "isArray", "handleSpanList", "map", "performance", "noSpaceStr", "includes", "dePoints", "calculateSelfScore", "JSON", "parse", "content", "itemFlag", "standardFlag", "i", "push", "rowspan", "colspan", "standard", "deptList", "handleQuery", "_this6", "save", "_this7", "$message", "type", "message", "handleData", "stringify", "job", "postType", "saveInfo", "submit", "_this8", "$confirm", "confirmButtonText", "cancelButtonText", "submitData", "catch", "onSubmit", "verifyInsert", "clearSign", "clearSignature", "clearValidationErrors", "trim", "$set", "focusAndScrollToField", "undefined", "pointsReason", "_this9", "averageLinkFlag", "benefitLinkFlag", "submitInfo", "resetInfo", "_this0", "delInfo", "error", "result", "category", "target", "handleConfig", "_this1", "$router", "path", "query", "objectSpanMethod", "_ref", "row", "rowIndex", "columnIndex", "handleBeAssessedClick", "optionRow", "index", "Number", "deductionOfPoint", "reasonContent", "assessContent", "showPopper", "scoreInput", "arguments", "value", "numValue", "points", "uploadSignature", "_this10", "_this$$refs$signature", "saveSignature", "isEmpty", "blob<PERSON>in", "atob", "split", "array", "charCodeAt", "fileBlob", "Blob", "Uint8Array", "formData", "FormData", "append", "fetch", "method", "body", "json", "fileName", "validateField", "fieldType", "_this11", "refName", "rowNumber", "fieldName", "_this12", "$nextTick", "setTimeout", "field", "targetElement", "inputElement", "$el", "scrollIntoView", "behavior", "block", "inline", "focus", "focusError", "warn", "duration", "scrollToTableRow", "scrollToTable", "tableRows", "querySelectorAll", "table", "querySelector"], "sources": ["src/views/assess/self/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n      <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-form-item label=\"考核年月\" prop=\"assessDate\">\r\n          <el-date-picker\r\n            v-model=\"queryParams.assessDate\"\r\n            type=\"month\"\r\n            value-format=\"yyyy-M\"\r\n            format=\"yyyy 年 M 月\"\r\n            placeholder=\"选择考核年月\"\r\n            @change=\"handleQuery\"\r\n            :clearable=\"false\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"部门\" prop=\"deptId\">\r\n          <el-select v-model=\"queryParams.deptId\" placeholder=\"请选择部门\" \r\n            @change=\"handleQuery\">\r\n            <el-option\r\n              v-for=\"item in deptOptions\"\r\n              :key=\"item.deptId\"\r\n              :label=\"item.deptName\"\r\n              :value=\"item.deptId\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n          <el-button type=\"warning\" icon=\"el-icon-s-tools\" size=\"mini\" @click=\"handleConfig\">指标配置</el-button>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row>\r\n        <el-form-item label=\"当前状态\">\r\n          <el-tag v-if=\"status == '0' && rejectReason\" type=\"danger\">退 回</el-tag>\r\n          <el-tag v-if=\"status == '0' && !rejectReason\" type=\"info\">未提交</el-tag>\r\n          <el-tag v-if=\"status == '1'\" type=\"warning\">部门领导评分</el-tag>\r\n          <el-tag v-if=\"status == '2'\" type=\"warning\">事业部评分</el-tag>\r\n          <el-tag v-if=\"status == '3'\" type=\"warning\">运改部/组织部审核</el-tag>\r\n          <el-tag v-if=\"status == '4'\" type=\"warning\">总经理部评分</el-tag>\r\n          <el-tag v-if=\"status == '5'\" type=\"success\">已完成</el-tag>\r\n        </el-form-item>\r\n      </el-row>\r\n      <el-row v-if=\"rejectReason\">\r\n        <el-form-item label=\"退回原因\" prop=\"rejectReason\">\r\n            <span class=\"el-icon-warning\" style=\"color: #f56c6c;\"></span>\r\n            {{ rejectReason }}\r\n        </el-form-item>\r\n      </el-row>\r\n    </el-form>\r\n      <h3 style=\"text-align: center;\">月度业绩考核表</h3>\r\n      <el-descriptions class=\"margin-top\" :column=\"3\">\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            姓名\r\n          </template>\r\n          {{ userInfo.name }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            部门\r\n          </template>\r\n          {{ deptName }}\r\n        </el-descriptions-item>\r\n        <el-descriptions-item>\r\n          <template slot=\"label\">\r\n            考核年月\r\n          </template>\r\n          {{ assessDateText }}\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n      <el-table v-loading=\"loading\" :data=\"list\"\r\n        :span-method=\"objectSpanMethod\" border>\r\n        <el-table-column label=\"类型\" align=\"center\" prop=\"item\" width=\"120\"/>\r\n        <el-table-column label=\"指标\" align=\"center\" prop=\"category\" width=\"140\"/>\r\n        <el-table-column label=\"目标\" align=\"center\" prop=\"target\" width=\"150\" />\r\n        <el-table-column label=\"评分标准\" align=\"center\" prop=\"standard\" />\r\n        <el-table-column label=\"完成实绩（若扣分，写明原因）\" align=\"center\" prop=\"performance\" width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.performance }}</span>\r\n                <div v-else style=\"display: flex\">\r\n                  <!-- <el-button icon=\"el-icon-search\" size=\"small\"></el-button> -->\r\n                  <el-popover\r\n                    placement=\"left\"\r\n                    width=\"636\"\r\n                    trigger=\"click\"\r\n                    :ref=\"'popover' + scope.$index\">\r\n                    <el-table :data=\"beAssessedList\">\r\n                      <el-table-column width=\"150\" property=\"assessDeptName\" label=\"提出考核单位\"></el-table-column>\r\n                      <el-table-column width=\"300\" property=\"assessContent\" label=\"事项\"></el-table-column>\r\n                      <el-table-column width=\"80\" property=\"deductionOfPoint\" label=\"加减分\"></el-table-column>\r\n                      <el-table-column width=\"80\" label=\"操作\">\r\n                        <template slot-scope=\"beAssessed\">\r\n                          <el-button\r\n                            size=\"mini\"\r\n                            type=\"text\"\r\n                            icon=\"el-icon-edit\"\r\n                            @click=\"handleBeAssessedClick(scope.row,beAssessed.row,scope.$index)\"\r\n                          >填入</el-button>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                    <el-button slot=\"reference\" icon=\"el-icon-search\" size=\"small\"></el-button>\r\n                  </el-popover>\r\n                  <el-input\r\n                    :ref=\"`performance_${scope.$index}`\"\r\n                    class=\"table-input\"\r\n                    type=\"textarea\"\r\n                    autosize\r\n                    v-model=\"scope.row.performance\"\r\n                    placeholder=\"请输入完成实绩\"\r\n                    @blur=\"validateField(scope.row, 'performance')\"\r\n                    :class=\"{'is-error': scope.row.performanceError}\" />\r\n                  <div v-if=\"scope.row.performanceError\" class=\"el-form-item__error\">{{ scope.row.performanceError }}</div>\r\n                </div>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分\" align=\"center\" prop=\"dePoints\" width=\"108\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.dePoints }}</span>\r\n                <div v-else>\r\n                  <el-input\r\n                    :ref=\"`dePoints_${scope.$index}`\"\r\n                    class=\"table-input\"\r\n                    type=\"number\"\r\n                    autosize\r\n                    v-model=\"scope.row.dePoints\"\r\n                    placeholder=\"请输入加减分\"\r\n                    @input=\"scoreInput(scope.row)\"\r\n                    @blur=\"validateField(scope.row, 'dePoints')\"\r\n                    :class=\"{'is-error': scope.row.dePointsError}\" />\r\n                  <div v-if=\"scope.row.dePointsError\" class=\"el-form-item__error\">{{ scope.row.dePointsError }}</div>\r\n                </div>\r\n            </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"加减分原因\" align=\"center\" prop=\"pointsReason\"  width=\"440\">\r\n            <template slot-scope=\"scope\">\r\n                <span v-if=\"readOnly\">{{ scope.row.pointsReason }}</span>\r\n                <div v-else>\r\n                  <el-input\r\n                    :ref=\"`pointsReason_${scope.$index}`\"\r\n                    class=\"table-input\"\r\n                    type=\"textarea\"\r\n                    autosize\r\n                    v-model=\"scope.row.pointsReason\"\r\n                    placeholder=\"请输入加减分原因\"\r\n                    @blur=\"validateField(scope.row, 'pointsReason')\"\r\n                    :class=\"{'is-error': scope.row.pointsReasonError}\" />\r\n                  <div v-if=\"scope.row.pointsReasonError\" class=\"el-form-item__error\">{{ scope.row.pointsReasonError }}</div>\r\n                </div>\r\n            </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <el-form size=\"small\" :inline=\"false\" label-width=\"200px\" style=\"margin-top: 10px;\" label-position=\"left\">\r\n            <el-form-item v-if=\"readOnly\" label=\"自评分数 / 签名：\" prop=\"deptId\">\r\n              <div style=\"display: flex;\">\r\n                <span >{{ selfScore + \" 分 / \" }}</span>\r\n                <span v-if=\"!selfSign\">{{info.name}}</span>\r\n                <el-image v-else\r\n                  style=\"width: 100px; height: 46px\"\r\n                  :src=\"selfSign.url\"></el-image>\r\n              </div>\r\n              \r\n            </el-form-item>\r\n            <el-form-item v-else label=\"自评分数：\" prop=\"selfScore\">\r\n              <div style=\"display: flex;width: 180px;\">\r\n                <el-input type=\"number\" autosize v-model=\"selfScore\" placeholder=\"请输入分数\" :readonly=\"readOnly\" />\r\n                <span style=\"margin-left: 8px;\">分</span>\r\n              </div>\r\n            </el-form-item>\r\n            <!-- <el-form-item v-if=\"status > '1' && info.deptScore && info.deptUserName\" label=\"部门领导评分 / 签名：\">\r\n              <span >{{ info.deptScore + \" 分 / \" + info.deptUserName }}</span>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"status > '2' && info.businessUserName && info.businessScore\" label=\"事业部领导评分 / 签名：\">\r\n              <span>{{ info.businessScore + \" 分 / \" + info.businessUserName }}</span>\r\n            </el-form-item> -->\r\n            \r\n            <!-- 部门领导评分 -->\r\n            <el-form-item v-if=\"info && info.deptScore && info.deptUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  部门领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ info.deptScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ info.deptUserName }}</span>\r\n                <div v-if=\"info.deptScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ info.deptScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 事业部领导评分 -->\r\n            <el-form-item v-if=\"info && info.businessUserName && info.businessScore\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  事业部领导评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ info.businessScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ info.businessUserName }}</span>\r\n                <div v-if=\"info.businessScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ info.businessScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n            \r\n            <!-- 运改组织部评分 -->\r\n            <el-form-item v-if=\"info && info.organizationScore && info.organizationUserName\">\r\n              <template slot=\"label\">\r\n                <span style=\"color: #606266;\">\r\n                  运改组织部评分 / 签名：\r\n                </span>\r\n              </template>\r\n              <div class=\"signature-content\">\r\n                <span class=\"score-text\">{{ info.organizationScore }} 分</span>\r\n                <span class=\"separator\">/</span>\r\n                <span class=\"signature-name\">{{ info.organizationUserName }}</span>\r\n                <div v-if=\"info.organizationScoreReason\" class=\"reason-text\">\r\n                  <span class=\"reason-label\">加减分理由：</span>\r\n                  <span class=\"reason-content\">{{ info.organizationScoreReason }}</span>\r\n                </div>\r\n              </div>\r\n            </el-form-item>\r\n\r\n\r\n            <!-- <el-form-item v-if=\"status > '4' && info.leaderScore && info.leaderName\" label=\"总经理部领导评分 / 签名：\" prop=\"deptId\">\r\n              <span >{{ info.leaderScore + \" 分 / \" + info.leaderName }}</span>\r\n            </el-form-item> -->\r\n          </el-form>\r\n      <div v-if=\"!readOnly\" style=\"text-align: center;\">\r\n        <el-button v-if=\"resetShow\" plain type=\"info\" @click=\"resetInfo\">重 置 </el-button>\r\n        <el-button type=\"success\" @click=\"save\">保 存</el-button>\r\n        <el-button type=\"primary\" @click=\"onSubmit\">提 交</el-button>\r\n      </div>\r\n\r\n      <!-- 签名板 -->\r\n      <el-dialog title=\"签字确认\" :visible.sync=\"openSign\" width=\"760px\" append-to-body>\r\n        <div style=\"border: 1px #ccc solid;width: 702px;\">\r\n            <vue-signature-pad\r\n            width=\"700px\"\r\n            height=\"300px\"\r\n            ref=\"signaturePad\"\r\n            :options=\"signOptions\"\r\n          />\r\n        </div>\r\n        <div style=\"text-align: center;padding: 10px 10px;\">\r\n          <el-button style=\"margin-right: 20px;\" type=\"success\" @click=\"clearSign\">清除</el-button>\r\n          <el-button type=\"primary\" @click=\"uploadSignature\">确认</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </template>\r\n\r\n  <script>\r\n  import { getInfoByDate, saveInfo, submitInfo, delInfo, listBeAssessed } from \"@/api/assess/self/info\";\r\n  // import { batchTarget, listSelfTargetAll } from \"@/api/assess/self/target\";\r\n  import { getReportDeptList, getByWorkNoDeptId } from \"@/api/assess/self/user\";\r\n  import { VueSignaturePad } from 'vue-signature-pad';\r\n\r\n  export default {\r\n    components: {\r\n      VueSignaturePad\r\n    },\r\n    name: \"SelfAssessReport\",\r\n    data() {\r\n      return {\r\n        // 遮罩层\r\n        loading: true,\r\n        // 显示搜索条件\r\n        showSearch: true,\r\n        openSign:false,\r\n        // 绩效考核-自评指标配置表格数据\r\n        list: [],\r\n        // 弹出层标题\r\n        title: \"\",\r\n        // 是否显示弹出层\r\n        open: false,\r\n        // 查询参数\r\n        queryParams: {\r\n          userId:null,\r\n          workNo: null,\r\n          deptId:null,\r\n          assessDate: null,\r\n        },\r\n        // 考核年月文本显示\r\n        assessDateText:null,\r\n        // 部门显示\r\n        deptName:null,\r\n        // 表单参数\r\n        form: {},\r\n        // 表单校验\r\n        rules: {\r\n        },\r\n        // 用户信息\r\n        userInfo:{},\r\n        // 合并单元格信息\r\n        spanList:{\r\n          itemList:[],\r\n          standardList:[]\r\n        },\r\n        // 是否显示重置按钮\r\n        resetShow:false,\r\n        // 是否只读\r\n        readOnly:false,\r\n        // 部门选项\r\n        deptOptions:[],\r\n        // 自评信息Id\r\n        id:null,\r\n        // 自评分数\r\n        selfScore:100,\r\n        // 状态\r\n        status:\"0\",\r\n        // 自评信息\r\n        info:{},\r\n        // 横向被考评信息\r\n        beAssessedList:[],\r\n        // 退回理由\r\n        rejectReason:\"\",\r\n        // 自评签名\r\n        selfSign:\"\",\r\n        // 签名板配置\r\n        signOptions: {\r\n          onBegin: () => this.$refs.signaturePad.resizeCanvas(),\r\n          backgroundColor: 'rgba(255, 255, 255, 1)'\r\n        },\r\n        sign:\"\",\r\n        file:null,\r\n        fileList:[],\r\n        upload: {\r\n          // 上传的地址\r\n          url: process.env.VUE_APP_BASE_API + \"/app/common/uploadMinio\",\r\n          isUploading: false,\r\n        },\r\n      };\r\n    },\r\n    created() {\r\n      this.queryParams.assessDate = this.getDefaultAssessDate()\r\n      this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n      // this.getSelfAssessUser();\r\n      this.getReportDeptList();\r\n    },\r\n    methods: {\r\n\r\n      // 获取默认考核日期\r\n      getDefaultAssessDate() {\r\n        const now = new Date();\r\n        const currentDay = now.getDate();\r\n\r\n        let targetDate;\r\n        if (currentDay < 10) {\r\n          // 当前日期小于10日，默认为上个月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);\r\n        } else {\r\n          // 当前日期大于等于10日，默认为当月\r\n          targetDate = new Date(now.getFullYear(), now.getMonth(), 1);\r\n        }\r\n\r\n        // 格式化为 YYYY-M 格式\r\n        const year = targetDate.getFullYear();\r\n        const month = targetDate.getMonth() + 1;\r\n        return `${year}-${month}`;\r\n      },\r\n\r\n      // 获取部门信息\r\n      getReportDeptList(){\r\n        getReportDeptList().then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.handleDeptList(res.data);\r\n            // 根据部门获取用户信息\r\n            this.getByWorkNoDeptId();\r\n          }\r\n        })\r\n      },\r\n      // 获取用户信息\r\n      getByWorkNoDeptId(){\r\n        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            this.queryParams.userId = res.data.id;\r\n            this.queryParams.workNo = res.data.workNo;\r\n            this.userInfo = res.data;\r\n            this.getList();\r\n            // 获取被考核信息\r\n            this.getBeAssessedList();\r\n          }\r\n        })\r\n      },\r\n      \r\n      // 获取被考核信息\r\n      getBeAssessedList(){\r\n        listBeAssessed({deptId:this.queryParams.deptId,assessDate:this.queryParams.assessDate}).then(res =>{\r\n          let beAssessedList = [];\r\n          if(res.code == 200){\r\n            if(res.data.length > 0){\r\n              res.data.forEach(item => {\r\n                beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]\r\n              })\r\n              this.beAssessedList = beAssessedList;\r\n            }\r\n          }\r\n          console.log(beAssessedList)\r\n        })\r\n      },\r\n      /** 查询绩效考核-自评指标配置列表 */\r\n      getList() {\r\n        this.loading = true;\r\n        getInfoByDate(this.queryParams).then(response => {\r\n          console.log(response.data);\r\n          // console.log(typeof response.data);\r\n          if (Array.isArray(response.data)) {\r\n            // 指标配置数据\r\n            this.handleSpanList(response.data);\r\n            this.list = response.data.map(item => {\r\n              item.performance = \"\";\r\n              // 根据项目类型设置默认分数\r\n              let noSpaceStr = item.item.replace(/\\s+/g, '');\r\n              if(noSpaceStr.includes(\"月度重点工作\")){\r\n                item.dePoints = 5; // 月度重点工作默认5分\r\n              } else {\r\n                item.dePoints = 0; // 其他项目默认0分\r\n              }\r\n              return item;\r\n            });\r\n            this.status = \"0\";\r\n            this.readOnly = false;\r\n            this.resetShow = false;\r\n            this.rejectReason = null;\r\n            this.calculateSelfScore();\r\n          } else {\r\n            // 自评信息\r\n            let info = response.data;\r\n            let list = JSON.parse(info.content);\r\n            this.handleSpanList(list);\r\n            this.list = list;\r\n            this.id = info.id;\r\n            this.selfScore = info.selfScore;\r\n            this.rejectReason = info.rejectReason;\r\n            this.status = info.status;\r\n            if(info.sign){\r\n              this.selfSign = JSON.parse(info.sign);\r\n            }\r\n            this.info = info;\r\n            if(info.status == \"0\"){\r\n              this.readOnly = false;\r\n              this.resetShow = true;\r\n            }else{\r\n              this.readOnly = true;\r\n              this.resetShow = false;\r\n            }\r\n          }\r\n          this.loading = false;\r\n        });\r\n      },\r\n\r\n      // 处理列表\r\n      handleSpanList(data){\r\n        let itemList = [];\r\n        let standardList = [];\r\n        let itemFlag = 0;\r\n        let standardFlag = 0;\r\n        for(let i = 0; i < data.length; i++){\r\n          // 相同考核项、评分标准合并\r\n          if(i == 0){\r\n            itemList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n            standardList.push({\r\n              rowspan: 1,\r\n              colspan: 1\r\n            })\r\n          }else{\r\n            // 考核项\r\n            if(data[i - 1].item == data[i].item){\r\n              itemList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              itemList[itemFlag].rowspan += 1;\r\n            }else{\r\n              itemList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              itemFlag = i;\r\n            }\r\n            // 评分标准\r\n            if(data[i - 1].standard == data[i].standard){\r\n              standardList.push({\r\n                rowspan: 0,\r\n                colspan: 0\r\n              })\r\n              standardList[standardFlag].rowspan += 1;\r\n            }else{\r\n              standardList.push({\r\n                rowspan: 1,\r\n                colspan: 1\r\n              })\r\n              standardFlag = i;\r\n            }\r\n          }\r\n        }\r\n        this.spanList.itemList = itemList;\r\n        this.spanList.standardList = standardList;\r\n      },\r\n\r\n\r\n      // 处理部门下拉选项\r\n      handleDeptList(data){\r\n        // let syb = [\"炼铁事业部\",\"炼钢事业部\",\"轧钢事业部\",\"特板事业部\",\"动力事业部\",\"物流事业部\",\"研究院\"];\r\n        let deptList = [];\r\n        data.forEach(item => {\r\n          //if(syb.indexOf(item.deptName) == -1){\r\n            deptList.push({\r\n              deptName:item.deptName,\r\n              deptId:item.deptId\r\n            })\r\n          //}\r\n        })\r\n        this.deptOptions = deptList;\r\n        if(deptList.length > 0){\r\n          this.queryParams.deptId = deptList[0].deptId;\r\n          this.deptName = deptList[0].deptName;\r\n        }\r\n      },\r\n      /** 搜索按钮操作 */\r\n      handleQuery() {\r\n        this.assessDateText = this.queryParams.assessDate.replace(\"-\",\" 年 \") + \" 月\";\r\n        this.deptOptions.forEach(item => {\r\n          if(item.deptId == this.queryParams.deptId){\r\n            this.deptName = item.deptName;\r\n          }\r\n        })\r\n        this.id = null;\r\n        this.info = null;\r\n        this.selfScore = \"\";\r\n        this.list = [];\r\n        this.beAssessedList = [];\r\n        this.rejectReason = \"\";\r\n        this.readOnly = false;\r\n        this.resetShow = false;\r\n        this.getByWorkNoDeptId();\r\n      },\r\n\r\n      // 保存\r\n      save(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"0\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType\r\n        }\r\n        saveInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.$message({\r\n              type: 'success',\r\n              message: '保存成功!'\r\n            });\r\n          }\r\n        })\r\n      },\r\n\r\n      // 确认提交点击事件\r\n      submit(){\r\n          this.$confirm('确认后将流转至下一节点, 是否继续?', '提示', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.submitData();\r\n          }).catch(() => {\r\n\r\n          });\r\n      },\r\n\r\n      onSubmit(){\r\n        if(this.verifyInsert()){\r\n          this.openSign = true;\r\n        }\r\n      },\r\n\r\n      clearSign(){\r\n        this.$refs.signaturePad.clearSignature();\r\n      },\r\n\r\n      // 提交数据验证\r\n      verifyInsert(){\r\n        if(this.list.length == 0){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '未配置相关信息，请先配置指标'\r\n            });\r\n            return false;\r\n        }\r\n\r\n        // 清除之前的错误状态\r\n        this.clearValidationErrors();\r\n\r\n        for(let i = 0; i < this.list.length; i++){\r\n          const item = this.list[i];\r\n\r\n          // 验证完成实绩\r\n          if(!item.performance || item.performance.trim() === ''){\r\n            this.$set(item, 'performanceError', '请填写完成实绩');\r\n            this.focusAndScrollToField(`performance_${i}`, i + 1, '完成实绩');\r\n            return false;\r\n          }\r\n\r\n          // 验证加减分\r\n          if(item.dePoints === null || item.dePoints === undefined || item.dePoints === ''){\r\n            this.$set(item, 'dePointsError', '请填写加减分');\r\n            this.focusAndScrollToField(`dePoints_${i}`, i + 1, '加减分');\r\n            return false;\r\n          }\r\n\r\n          // 验证加减分原因\r\n          if(item.dePoints != 0 && (!item.pointsReason || item.pointsReason.trim() === '')){\r\n            // 检查是否为月度重点工作\r\n            let noSpaceStr = item.item.replace(/\\s+/g, '');\r\n            if(!noSpaceStr.includes(\"月度重点工作\")){\r\n              // 非月度重点工作需要填写加减分原因\r\n              this.$set(item, 'pointsReasonError', '有加减分的请填写原因');\r\n              this.focusAndScrollToField(`pointsReason_${i}`, i + 1, '加减分原因');\r\n              return false;\r\n            }\r\n          }\r\n        }\r\n\r\n        if(!this.selfScore){\r\n          this.$message({\r\n              type: 'warning',\r\n              message: '请填写自评分数'\r\n            });\r\n            return false;\r\n        }\r\n        return true;\r\n      },\r\n\r\n      // 新增数据\r\n      submitData(){\r\n        let data = this.handleData(this.list);\r\n        let form = {\r\n          id:this.id,\r\n          workNo:this.userInfo.workNo,\r\n          assessDate:this.queryParams.assessDate,\r\n          deptId:this.queryParams.deptId,\r\n          content:JSON.stringify(data),\r\n          status:\"1\",\r\n          userId:this.queryParams.userId,\r\n          deptName:this.deptName,\r\n          name:this.userInfo.name,\r\n          selfScore:this.selfScore,\r\n          job:this.userInfo.job,\r\n          postType:this.userInfo.postType,\r\n          averageLinkFlag:this.userInfo.averageLinkFlag,\r\n          benefitLinkFlag:this.userInfo.benefitLinkFlag,\r\n          sign:JSON.stringify(this.sign)\r\n        }\r\n        submitInfo(form).then(res => {\r\n          if(res.code == 200){\r\n            this.getList();\r\n            this.sign = \"\";\r\n            this.$refs.signaturePad.clearSignature();\r\n            this.openSign = false;\r\n            this.$message({\r\n              type: 'success',\r\n              message: '提交成功!'\r\n            });\r\n          }else{\r\n\r\n          }\r\n        })\r\n      },\r\n\r\n      // 保存重置\r\n      resetInfo(){\r\n        this.$confirm('重置后将清空所有已填写的内容，是否确认重置?', '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          // 删除保存信息\r\n          delInfo({id:this.id}).then(res => {\r\n            if(res.code == 200){\r\n              this.id = null;\r\n              this.selfScore = null;\r\n              // 获取配置信息\r\n              this.getList();\r\n              this.$message({\r\n                type: 'success',\r\n                message: '重置成功!'\r\n              });\r\n            }\r\n          }).catch(error => {\r\n            console.error('重置失败:', error);\r\n            this.$message({\r\n              type: 'error',\r\n              message: '重置失败，请重试'\r\n            });\r\n          });\r\n        }).catch(() => {\r\n          // 用户取消重置\r\n        });\r\n      },\r\n\r\n      // 处理提交数据\r\n      handleData(data){\r\n        let result = []\r\n        data.forEach(item => {\r\n          let form = {\r\n            item: item.item,\r\n            category: item.category,\r\n            target: item.target,\r\n            standard: item.standard,\r\n            performance: item.performance,\r\n            dePoints: item.dePoints,\r\n            pointsReason:item.pointsReason\r\n          };\r\n          result.push(form);\r\n        })\r\n        return result\r\n      },\r\n\r\n      /** 标准配置跳转 */\r\n      handleConfig(){\r\n        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {\r\n          console.log(res)\r\n          if(res.code == 200){\r\n            if(res.data.id){\r\n            this.$router.push({\r\n              path:\"/assess/self/user/detail\",\r\n              query:{\r\n                userId:res.data.id\r\n              }\r\n            })\r\n          }\r\n          }\r\n        })\r\n        \r\n      },\r\n\r\n\r\n      // 合并单元格方法\r\n      objectSpanMethod({ row, rowIndex, columnIndex }) {\r\n        // 第一列相同项合并\r\n        if (columnIndex === 0) {\r\n          return this.spanList.itemList[rowIndex];\r\n        }\r\n        // 评分标准相同合并\r\n        if(columnIndex === 3){\r\n          return this.spanList.standardList[rowIndex];\r\n        }\r\n        // 类别无内容 合并\r\n        if(columnIndex === 1){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 0,\r\n              colspan: 0\r\n            }\r\n          }\r\n        }\r\n        if(columnIndex === 2){\r\n          if(!row.category){\r\n            return {\r\n              rowspan: 1,\r\n              colspan: 2\r\n            }\r\n          }\r\n        }\r\n      },\r\n\r\n      // 被考核事项点击事件\r\n      handleBeAssessedClick(row,optionRow,index){\r\n        console.log(row)\r\n        // 将事项填入完成实绩列（弃用）\r\n        // if(row.performance){\r\n        //   this.$set(row, 'performance', row.performance + \"；\" + optionRow.assessContent);\r\n        // }else{\r\n        //   this.$set(row, 'performance', optionRow.assessContent);\r\n        // }\r\n        \r\n        // 将分数填入加减分列\r\n        if(row.dePoints){\r\n          this.$set(row, 'dePoints', Number(row.dePoints) + Number(optionRow.deductionOfPoint));\r\n        }else{\r\n          this.$set(row, 'dePoints', Number(optionRow.deductionOfPoint));\r\n        }\r\n        \r\n        // 将事项+分数填入加减分理由列\r\n        let reasonContent = optionRow.assessContent + \"(\" + optionRow.deductionOfPoint + \"分)\";\r\n        if(row.pointsReason){\r\n          this.$set(row, 'pointsReason', row.pointsReason + \"；\" + reasonContent);\r\n        }else{\r\n          this.$set(row, 'pointsReason', reasonContent);\r\n        }\r\n        \r\n        this.$refs[`popover${index}`].showPopper = false;\r\n        // 重新计算自评分数\r\n        this.scoreInput();\r\n      },\r\n\r\n      // 加减分输入\r\n      scoreInput(row = null){\r\n        // 验证加减分规则（仅当传入row参数时进行验证）\r\n        if (row && row.item) {\r\n          let noSpaceStr = row.item.replace(/\\s+/g, '');\r\n          let value = row.dePoints;\r\n\r\n          if (value !== null && value !== undefined && value !== '') {\r\n            let numValue = Number(value);\r\n\r\n            // 月度重点工作：只能为1、3或5分\r\n            if (noSpaceStr.includes(\"月度重点工作\")) {\r\n              if (![1, 3, 5].includes(numValue)) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '月度重点工作为得分制，只能为1分、3分或5分'\r\n                });\r\n                this.$set(row, 'dePoints', null);\r\n                return;\r\n              }\r\n            }\r\n            // 加分项：除月度重点工作外，只能填0\r\n            else if (noSpaceStr.includes(\"加分项\")) {\r\n              if (numValue !== 0) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '加分项只能填0分'\r\n                });\r\n                this.$set(row, 'dePoints', 0);\r\n                return;\r\n              }\r\n            }\r\n            // 其他类型项：只能填0或负数\r\n            else {\r\n              if (numValue > 0) {\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: '该项目只能填0分或负数'\r\n                });\r\n                this.$set(row, 'dePoints', 0);\r\n                return;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        // 重新计算自评分数\r\n        this.calculateSelfScore();\r\n      },\r\n\r\n      /** 计算自评分数 */\r\n      calculateSelfScore() {\r\n        let points = 0;   // 月度重点工作分数\r\n        this.list.forEach(item => {\r\n            points += Number(item.dePoints);\r\n        });\r\n        // 计算总分：基础分85 + 加减分\r\n        this.selfScore = 85 + points;\r\n      },\r\n\r\n      // 签名上传相关\r\n      uploadSignature(){\r\n        const { isEmpty, data } = this.$refs.signaturePad.saveSignature();\r\n        console.log(isEmpty,data)\r\n        if(isEmpty){\r\n          this.$message({\r\n            type: 'warning',\r\n            message: '请签名!'\r\n          });\r\n          return false;\r\n        }else{\r\n          const blobBin = atob(data.split(',')[1]);\r\n          let array = [];\r\n          for (let i = 0; i < blobBin.length; i++) {\r\n            array.push(blobBin.charCodeAt(i));\r\n          }\r\n          const fileBlob = new Blob([new Uint8Array(array)], { type: 'image/png' });\r\n          const formData = new FormData();\r\n          formData.append('file', fileBlob, `${Date.now()}.png`);\r\n          fetch(this.upload.url, {\r\n            method: 'POST',\r\n            body: formData,\r\n          })\r\n          .then(response => response.json())\r\n          .then(data => {\r\n            console.log('Success:', data);\r\n            if(data.code == 200){\r\n              this.sign = {fileName:this.userInfo.name + \".png\",url:data.url};\r\n              this.submit();\r\n            }else{\r\n              this.$message({\r\n                type: 'error',\r\n                message: '签名上传失败'\r\n              });\r\n            }\r\n          })\r\n          .catch((error) => {\r\n            console.error('Error:', error);\r\n            this.$message({\r\n              type: 'error',\r\n              message: '签名上传异常'\r\n            });\r\n          });\r\n        }\r\n\r\n      },\r\n\r\n      /** 验证单个字段（失焦时调用） */\r\n      validateField(row, fieldType) {\r\n        // 清除之前的错误信息\r\n        this.$set(row, `${fieldType}Error`, '');\r\n\r\n        if (fieldType === 'performance') {\r\n          // 验证完成实绩\r\n          if (!row.performance || row.performance.trim() === '') {\r\n            this.$set(row, 'performanceError', '请填写完成实绩');\r\n            return false;\r\n          }\r\n        } else if (fieldType === 'dePoints') {\r\n          // 验证加减分\r\n          if (row.dePoints === null || row.dePoints === undefined || row.dePoints === '') {\r\n            this.$set(row, 'dePointsError', '请填写加减分');\r\n            return false;\r\n          }\r\n        } else if (fieldType === 'pointsReason') {\r\n          // 验证加减分原因\r\n          if (row.dePoints != 0 && (!row.pointsReason || row.pointsReason.trim() === '')) {\r\n            // 检查是否为月度重点工作\r\n            let noSpaceStr = row.item.replace(/\\s+/g, '');\r\n            if (!noSpaceStr.includes(\"月度重点工作\")) {\r\n              // 非月度重点工作需要填写原因\r\n              this.$set(row, 'pointsReasonError', '有加减分的请填写原因');\r\n              return false;\r\n            }\r\n          }\r\n        }\r\n\r\n        return true;\r\n      },\r\n\r\n      /** 清除所有验证错误 */\r\n      clearValidationErrors() {\r\n        this.list.forEach(item => {\r\n          this.$set(item, 'performanceError', '');\r\n          this.$set(item, 'dePointsError', '');\r\n          this.$set(item, 'pointsReasonError', '');\r\n        });\r\n      },\r\n\r\n      /** 定位到字段并显示详细错误信息 */\r\n      focusAndScrollToField(refName, rowNumber, fieldName) {\r\n        console.log(`开始定位到字段: ${refName}, 行号: ${rowNumber}, 字段名: ${fieldName}`);\r\n\r\n        this.$nextTick(() => {\r\n          // 等待DOM更新后再执行\r\n          setTimeout(() => {\r\n            try {\r\n              const field = this.$refs[refName];\r\n              console.log('找到的字段元素:', field);\r\n\r\n              let targetElement = null;\r\n              let inputElement = null;\r\n\r\n              if (field && field.length > 0) {\r\n                // 数组形式的ref（v-for中的ref）\r\n                inputElement = field[0];\r\n                targetElement = inputElement.$el || inputElement;\r\n              } else if (field) {\r\n                // 单个ref\r\n                inputElement = field;\r\n                targetElement = inputElement.$el || inputElement;\r\n              }\r\n\r\n              if (targetElement && inputElement) {\r\n                console.log('找到目标元素，开始定位');\r\n\r\n                // 1. 先滚动到目标位置\r\n                targetElement.scrollIntoView({\r\n                  behavior: 'smooth',\r\n                  block: 'center',\r\n                  inline: 'nearest'\r\n                });\r\n\r\n                // 2. 等待滚动完成后聚焦\r\n                setTimeout(() => {\r\n                  try {\r\n                    inputElement.focus();\r\n                    console.log('已聚焦到输入框');\r\n                  } catch (focusError) {\r\n                    console.warn('聚焦失败:', focusError);\r\n                  }\r\n                }, 500);\r\n\r\n                // 3. 显示错误信息\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: `第${rowNumber}行 ${fieldName} 未填写完整，请检查并填写`,\r\n                  duration: 5000\r\n                });\r\n\r\n              } else {\r\n                // 如果找不到具体字段，尝试定位到表格行\r\n                console.warn(`找不到字段 ${refName}，尝试定位到表格行`);\r\n                this.scrollToTableRow(rowNumber - 1);\r\n\r\n                this.$message({\r\n                  type: 'warning',\r\n                  message: `第${rowNumber}行 ${fieldName} 未填写完整，请检查并填写`,\r\n                  duration: 5000\r\n                });\r\n              }\r\n\r\n            } catch (error) {\r\n              console.error('定位字段时发生错误:', error);\r\n              // 降级处理：至少滚动到表格区域\r\n              this.scrollToTable();\r\n\r\n              this.$message({\r\n                type: 'warning',\r\n                message: `第${rowNumber}行 ${fieldName} 未填写完整，请检查并填写`,\r\n                duration: 5000\r\n              });\r\n            }\r\n          }, 100); // 给一点时间让错误状态更新\r\n        });\r\n      },\r\n\r\n      /** 滚动到指定表格行 */\r\n      scrollToTableRow(rowIndex) {\r\n        try {\r\n          const tableRows = this.$el.querySelectorAll('.el-table__body tr');\r\n          if (tableRows && tableRows[rowIndex]) {\r\n            tableRows[rowIndex].scrollIntoView({\r\n              behavior: 'smooth',\r\n              block: 'center'\r\n            });\r\n            console.log(`已滚动到第${rowIndex + 1}行`);\r\n          } else {\r\n            console.warn(`找不到第${rowIndex + 1}行，滚动到表格`);\r\n            this.scrollToTable();\r\n          }\r\n        } catch (error) {\r\n          console.error('滚动到表格行失败:', error);\r\n          this.scrollToTable();\r\n        }\r\n      },\r\n\r\n      /** 滚动到表格区域 */\r\n      scrollToTable() {\r\n        try {\r\n          const table = this.$el.querySelector('.el-table');\r\n          if (table) {\r\n            table.scrollIntoView({\r\n              behavior: 'smooth',\r\n              block: 'start'\r\n            });\r\n            console.log('已滚动到表格区域');\r\n          }\r\n        } catch (error) {\r\n          console.error('滚动到表格失败:', error);\r\n        }\r\n      }\r\n    },\r\n  };\r\n</script>\r\n<style>\r\n  .table-striped{\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n    text-align: center;\r\n    border: 1px #888;\r\n    border-collapse: collapse;\r\n  }\r\n  .table-striped th{\r\n    height: 32px;\r\n    border: 1px solid #888;\r\n    background-color: #dedede;\r\n  }\r\n  .table-striped td{\r\n    min-height: 32px;\r\n    border: 1px solid #888;\r\n  }\r\n  .table-input .el-textarea__inner{\r\n    border: 0 !important;\r\n    resize: none !important;\r\n  }\r\n  .table-input .el-input__inner{\r\n    border: 0 !important;\r\n  }\r\n\r\n  /* 错误状态样式 */\r\n  .table-input.is-error .el-input__inner,\r\n  .table-input.is-error .el-textarea__inner {\r\n    border: 1px solid #F56C6C !important;\r\n    background-color: #fef0f0 !important;\r\n  }\r\n\r\n  /* 错误提示样式 */\r\n  .el-form-item__error {\r\n    color: #F56C6C;\r\n    font-size: 12px;\r\n    line-height: 1;\r\n    padding-top: 4px;\r\n    position: absolute;\r\n    top: 100%;\r\n    left: 0;\r\n    z-index: 1;\r\n  }\r\n\r\n  /* 表格单元格相对定位，用于错误提示定位 */\r\n  .el-table .cell {\r\n    position: relative;\r\n  }\r\n  \r\n  .reason-text {\r\n    width: 100%;\r\n    margin-top: 8px;\r\n    padding: 8px 12px;\r\n    background-color: #f8f9fa;\r\n    border-left: 3px solid #409EFF;\r\n    border-radius: 4px;\r\n  }\r\n\r\n  .reason-label {\r\n    font-weight: 500;\r\n    color: #606266;\r\n    margin-right: 8px;\r\n  }\r\n\r\n  .reason-content {\r\n    color: #303133;\r\n    line-height: 1.6;\r\n  }\r\n  /* .myUpload .el-upload--picture-card{\r\n    display:none !important; \r\n  } */\r\n  </style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqQA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,gBAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAIA;EACAC,UAAA;IACAC,eAAA,EAAAA;EACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACAC,QAAA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACA;MACAC,cAAA;MACA;MACAC,QAAA;MACA;MACAC,IAAA;MACA;MACAC,KAAA,GACA;MACA;MACAC,QAAA;MACA;MACAC,QAAA;QACAC,QAAA;QACAC,YAAA;MACA;MACA;MACAC,SAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;MACA;MACAC,EAAA;MACA;MACAC,SAAA;MACA;MACAC,MAAA;MACA;MACAC,IAAA;MACA;MACAC,cAAA;MACA;MACAC,YAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;QACAC,OAAA,WAAAA,QAAA;UAAA,OAAA/B,KAAA,CAAAgC,KAAA,CAAAC,YAAA,CAAAC,YAAA;QAAA;QACAC,eAAA;MACA;MACAC,IAAA;MACAC,IAAA;MACAC,QAAA;MACAC,MAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;QACAC,WAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAtC,WAAA,CAAAI,UAAA,QAAAmC,oBAAA;IACA,KAAAlC,cAAA,QAAAL,WAAA,CAAAI,UAAA,CAAAoC,OAAA;IACA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA;IAEA;IACAH,oBAAA,WAAAA,qBAAA;MACA,IAAAI,GAAA,OAAAC,IAAA;MACA,IAAAC,UAAA,GAAAF,GAAA,CAAAG,OAAA;MAEA,IAAAC,UAAA;MACA,IAAAF,UAAA;QACA;QACAE,UAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAM,QAAA;MACA;QACA;QACAF,UAAA,OAAAH,IAAA,CAAAD,GAAA,CAAAK,WAAA,IAAAL,GAAA,CAAAM,QAAA;MACA;;MAEA;MACA,IAAAC,IAAA,GAAAH,UAAA,CAAAC,WAAA;MACA,IAAAG,KAAA,GAAAJ,UAAA,CAAAE,QAAA;MACA,UAAAG,MAAA,CAAAF,IAAA,OAAAE,MAAA,CAAAD,KAAA;IACA;IAEA;IACAV,iBAAA,WAAAA,kBAAA;MAAA,IAAAY,MAAA;MACA,IAAAZ,uBAAA,IAAAa,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAL,MAAA,CAAAM,cAAA,CAAAJ,GAAA,CAAA/D,IAAA;UACA;UACA6D,MAAA,CAAAO,iBAAA;QACA;MACA;IACA;IACA;IACAA,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,uBAAA;QAAAzD,MAAA,OAAAH,WAAA,CAAAG;MAAA,GAAAmD,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAG,MAAA,CAAA7D,WAAA,CAAAC,MAAA,GAAAsD,GAAA,CAAA/D,IAAA,CAAAwB,EAAA;UACA6C,MAAA,CAAA7D,WAAA,CAAAE,MAAA,GAAAqD,GAAA,CAAA/D,IAAA,CAAAU,MAAA;UACA2D,MAAA,CAAApD,QAAA,GAAA8C,GAAA,CAAA/D,IAAA;UACAqE,MAAA,CAAAC,OAAA;UACA;UACAD,MAAA,CAAAE,iBAAA;QACA;MACA;IACA;IAEA;IACAA,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,oBAAA;QAAA9D,MAAA,OAAAH,WAAA,CAAAG,MAAA;QAAAC,UAAA,OAAAJ,WAAA,CAAAI;MAAA,GAAAkD,IAAA,WAAAC,GAAA;QACA,IAAAnC,cAAA;QACA,IAAAmC,GAAA,CAAAG,IAAA;UACA,IAAAH,GAAA,CAAA/D,IAAA,CAAA0E,MAAA;YACAX,GAAA,CAAA/D,IAAA,CAAA2E,OAAA,WAAAC,IAAA;cACAhD,cAAA,MAAAgC,MAAA,KAAAiB,mBAAA,CAAAjF,OAAA,EAAAgC,cAAA,OAAAiD,mBAAA,CAAAjF,OAAA,EAAAgF,IAAA,CAAAE,uBAAA;YACA;YACAN,MAAA,CAAA5C,cAAA,GAAAA,cAAA;UACA;QACA;QACAoC,OAAA,CAAAC,GAAA,CAAArC,cAAA;MACA;IACA;IACA,sBACA0C,OAAA,WAAAA,QAAA;MAAA,IAAAS,MAAA;MACA,KAAA7E,OAAA;MACA,IAAA8E,mBAAA,OAAAxE,WAAA,EAAAsD,IAAA,WAAAmB,QAAA;QACAjB,OAAA,CAAAC,GAAA,CAAAgB,QAAA,CAAAjF,IAAA;QACA;QACA,IAAAkF,KAAA,CAAAC,OAAA,CAAAF,QAAA,CAAAjF,IAAA;UACA;UACA+E,MAAA,CAAAK,cAAA,CAAAH,QAAA,CAAAjF,IAAA;UACA+E,MAAA,CAAA1E,IAAA,GAAA4E,QAAA,CAAAjF,IAAA,CAAAqF,GAAA,WAAAT,IAAA;YACAA,IAAA,CAAAU,WAAA;YACA;YACA,IAAAC,UAAA,GAAAX,IAAA,CAAAA,IAAA,CAAA5B,OAAA;YACA,IAAAuC,UAAA,CAAAC,QAAA;cACAZ,IAAA,CAAAa,QAAA;YACA;cACAb,IAAA,CAAAa,QAAA;YACA;YACA,OAAAb,IAAA;UACA;UACAG,MAAA,CAAArD,MAAA;UACAqD,MAAA,CAAAzD,QAAA;UACAyD,MAAA,CAAA1D,SAAA;UACA0D,MAAA,CAAAlD,YAAA;UACAkD,MAAA,CAAAW,kBAAA;QACA;UACA;UACA,IAAA/D,IAAA,GAAAsD,QAAA,CAAAjF,IAAA;UACA,IAAAK,IAAA,GAAAsF,IAAA,CAAAC,KAAA,CAAAjE,IAAA,CAAAkE,OAAA;UACAd,MAAA,CAAAK,cAAA,CAAA/E,IAAA;UACA0E,MAAA,CAAA1E,IAAA,GAAAA,IAAA;UACA0E,MAAA,CAAAvD,EAAA,GAAAG,IAAA,CAAAH,EAAA;UACAuD,MAAA,CAAAtD,SAAA,GAAAE,IAAA,CAAAF,SAAA;UACAsD,MAAA,CAAAlD,YAAA,GAAAF,IAAA,CAAAE,YAAA;UACAkD,MAAA,CAAArD,MAAA,GAAAC,IAAA,CAAAD,MAAA;UACA,IAAAC,IAAA,CAAAU,IAAA;YACA0C,MAAA,CAAAjD,QAAA,GAAA6D,IAAA,CAAAC,KAAA,CAAAjE,IAAA,CAAAU,IAAA;UACA;UACA0C,MAAA,CAAApD,IAAA,GAAAA,IAAA;UACA,IAAAA,IAAA,CAAAD,MAAA;YACAqD,MAAA,CAAAzD,QAAA;YACAyD,MAAA,CAAA1D,SAAA;UACA;YACA0D,MAAA,CAAAzD,QAAA;YACAyD,MAAA,CAAA1D,SAAA;UACA;QACA;QACA0D,MAAA,CAAA7E,OAAA;MACA;IACA;IAEA;IACAkF,cAAA,WAAAA,eAAApF,IAAA;MACA,IAAAmB,QAAA;MACA,IAAAC,YAAA;MACA,IAAA0E,QAAA;MACA,IAAAC,YAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAhG,IAAA,CAAA0E,MAAA,EAAAsB,CAAA;QACA;QACA,IAAAA,CAAA;UACA7E,QAAA,CAAA8E,IAAA;YACAC,OAAA;YACAC,OAAA;UACA;UACA/E,YAAA,CAAA6E,IAAA;YACAC,OAAA;YACAC,OAAA;UACA;QACA;UACA;UACA,IAAAnG,IAAA,CAAAgG,CAAA,MAAApB,IAAA,IAAA5E,IAAA,CAAAgG,CAAA,EAAApB,IAAA;YACAzD,QAAA,CAAA8E,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAhF,QAAA,CAAA2E,QAAA,EAAAI,OAAA;UACA;YACA/E,QAAA,CAAA8E,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAL,QAAA,GAAAE,CAAA;UACA;UACA;UACA,IAAAhG,IAAA,CAAAgG,CAAA,MAAAI,QAAA,IAAApG,IAAA,CAAAgG,CAAA,EAAAI,QAAA;YACAhF,YAAA,CAAA6E,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACA/E,YAAA,CAAA2E,YAAA,EAAAG,OAAA;UACA;YACA9E,YAAA,CAAA6E,IAAA;cACAC,OAAA;cACAC,OAAA;YACA;YACAJ,YAAA,GAAAC,CAAA;UACA;QACA;MACA;MACA,KAAA9E,QAAA,CAAAC,QAAA,GAAAA,QAAA;MACA,KAAAD,QAAA,CAAAE,YAAA,GAAAA,YAAA;IACA;IAGA;IACA+C,cAAA,WAAAA,eAAAnE,IAAA;MACA;MACA,IAAAqG,QAAA;MACArG,IAAA,CAAA2E,OAAA,WAAAC,IAAA;QACA;QACAyB,QAAA,CAAAJ,IAAA;UACAnF,QAAA,EAAA8D,IAAA,CAAA9D,QAAA;UACAH,MAAA,EAAAiE,IAAA,CAAAjE;QACA;QACA;MACA;MACA,KAAAY,WAAA,GAAA8E,QAAA;MACA,IAAAA,QAAA,CAAA3B,MAAA;QACA,KAAAlE,WAAA,CAAAG,MAAA,GAAA0F,QAAA,IAAA1F,MAAA;QACA,KAAAG,QAAA,GAAAuF,QAAA,IAAAvF,QAAA;MACA;IACA;IACA,aACAwF,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAA1F,cAAA,QAAAL,WAAA,CAAAI,UAAA,CAAAoC,OAAA;MACA,KAAAzB,WAAA,CAAAoD,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAjE,MAAA,IAAA4F,MAAA,CAAA/F,WAAA,CAAAG,MAAA;UACA4F,MAAA,CAAAzF,QAAA,GAAA8D,IAAA,CAAA9D,QAAA;QACA;MACA;MACA,KAAAU,EAAA;MACA,KAAAG,IAAA;MACA,KAAAF,SAAA;MACA,KAAApB,IAAA;MACA,KAAAuB,cAAA;MACA,KAAAC,YAAA;MACA,KAAAP,QAAA;MACA,KAAAD,SAAA;MACA,KAAA+C,iBAAA;IACA;IAEA;IACAoC,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MACA,SAAApG,IAAA,CAAAqE,MAAA;QACA,KAAAgC,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;QACA;MACA;MACA,IAAA5G,IAAA,QAAA6G,UAAA,MAAAxG,IAAA;MACA,IAAAU,IAAA;QACAS,EAAA,OAAAA,EAAA;QACAd,MAAA,OAAAO,QAAA,CAAAP,MAAA;QACAE,UAAA,OAAAJ,WAAA,CAAAI,UAAA;QACAD,MAAA,OAAAH,WAAA,CAAAG,MAAA;QACAkF,OAAA,EAAAF,IAAA,CAAAmB,SAAA,CAAA9G,IAAA;QACA0B,MAAA;QACAjB,MAAA,OAAAD,WAAA,CAAAC,MAAA;QACAK,QAAA,OAAAA,QAAA;QACAf,IAAA,OAAAkB,QAAA,CAAAlB,IAAA;QACA0B,SAAA,OAAAA,SAAA;QACAsF,GAAA,OAAA9F,QAAA,CAAA8F,GAAA;QACAC,QAAA,OAAA/F,QAAA,CAAA+F;MACA;MACA,IAAAC,cAAA,EAAAlG,IAAA,EAAA+C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAuC,MAAA,CAAAnC,OAAA;UACAmC,MAAA,CAAAC,QAAA;YACAC,IAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAM,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAX,IAAA;MACA,GAAA7C,IAAA;QACAqD,MAAA,CAAAI,UAAA;MACA,GAAAC,KAAA,cAEA;IACA;IAEAC,QAAA,WAAAA,SAAA;MACA,SAAAC,YAAA;QACA,KAAAtH,QAAA;MACA;IACA;IAEAuH,SAAA,WAAAA,UAAA;MACA,KAAA1F,KAAA,CAAAC,YAAA,CAAA0F,cAAA;IACA;IAEA;IACAF,YAAA,WAAAA,aAAA;MACA,SAAArH,IAAA,CAAAqE,MAAA;QACA,KAAAgC,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;QACA;MACA;;MAEA;MACA,KAAAiB,qBAAA;MAEA,SAAA7B,CAAA,MAAAA,CAAA,QAAA3F,IAAA,CAAAqE,MAAA,EAAAsB,CAAA;QACA,IAAApB,IAAA,QAAAvE,IAAA,CAAA2F,CAAA;;QAEA;QACA,KAAApB,IAAA,CAAAU,WAAA,IAAAV,IAAA,CAAAU,WAAA,CAAAwC,IAAA;UACA,KAAAC,IAAA,CAAAnD,IAAA;UACA,KAAAoD,qBAAA,gBAAApE,MAAA,CAAAoC,CAAA,GAAAA,CAAA;UACA;QACA;;QAEA;QACA,IAAApB,IAAA,CAAAa,QAAA,aAAAb,IAAA,CAAAa,QAAA,KAAAwC,SAAA,IAAArD,IAAA,CAAAa,QAAA;UACA,KAAAsC,IAAA,CAAAnD,IAAA;UACA,KAAAoD,qBAAA,aAAApE,MAAA,CAAAoC,CAAA,GAAAA,CAAA;UACA;QACA;;QAEA;QACA,IAAApB,IAAA,CAAAa,QAAA,WAAAb,IAAA,CAAAsD,YAAA,IAAAtD,IAAA,CAAAsD,YAAA,CAAAJ,IAAA;UACA;UACA,IAAAvC,UAAA,GAAAX,IAAA,CAAAA,IAAA,CAAA5B,OAAA;UACA,KAAAuC,UAAA,CAAAC,QAAA;YACA;YACA,KAAAuC,IAAA,CAAAnD,IAAA;YACA,KAAAoD,qBAAA,iBAAApE,MAAA,CAAAoC,CAAA,GAAAA,CAAA;YACA;UACA;QACA;MACA;MAEA,UAAAvE,SAAA;QACA,KAAAiF,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAW,UAAA,WAAAA,WAAA;MAAA,IAAAY,MAAA;MACA,IAAAnI,IAAA,QAAA6G,UAAA,MAAAxG,IAAA;MACA,IAAAU,IAAA;QACAS,EAAA,OAAAA,EAAA;QACAd,MAAA,OAAAO,QAAA,CAAAP,MAAA;QACAE,UAAA,OAAAJ,WAAA,CAAAI,UAAA;QACAD,MAAA,OAAAH,WAAA,CAAAG,MAAA;QACAkF,OAAA,EAAAF,IAAA,CAAAmB,SAAA,CAAA9G,IAAA;QACA0B,MAAA;QACAjB,MAAA,OAAAD,WAAA,CAAAC,MAAA;QACAK,QAAA,OAAAA,QAAA;QACAf,IAAA,OAAAkB,QAAA,CAAAlB,IAAA;QACA0B,SAAA,OAAAA,SAAA;QACAsF,GAAA,OAAA9F,QAAA,CAAA8F,GAAA;QACAC,QAAA,OAAA/F,QAAA,CAAA+F,QAAA;QACAoB,eAAA,OAAAnH,QAAA,CAAAmH,eAAA;QACAC,eAAA,OAAApH,QAAA,CAAAoH,eAAA;QACAhG,IAAA,EAAAsD,IAAA,CAAAmB,SAAA,MAAAzE,IAAA;MACA;MACA,IAAAiG,gBAAA,EAAAvH,IAAA,EAAA+C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACAiE,MAAA,CAAA7D,OAAA;UACA6D,MAAA,CAAA9F,IAAA;UACA8F,MAAA,CAAAlG,KAAA,CAAAC,YAAA,CAAA0F,cAAA;UACAO,MAAA,CAAA/H,QAAA;UACA+H,MAAA,CAAAzB,QAAA;YACAC,IAAA;YACAC,OAAA;UACA;QACA,QAEA;MACA;IACA;IAEA;IACA2B,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAApB,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAX,IAAA;MACA,GAAA7C,IAAA;QACA;QACA,IAAA2E,aAAA;UAAAjH,EAAA,EAAAgH,MAAA,CAAAhH;QAAA,GAAAsC,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAG,IAAA;YACAsE,MAAA,CAAAhH,EAAA;YACAgH,MAAA,CAAA/G,SAAA;YACA;YACA+G,MAAA,CAAAlE,OAAA;YACAkE,MAAA,CAAA9B,QAAA;cACAC,IAAA;cACAC,OAAA;YACA;UACA;QACA,GAAAY,KAAA,WAAAkB,KAAA;UACA1E,OAAA,CAAA0E,KAAA,UAAAA,KAAA;UACAF,MAAA,CAAA9B,QAAA;YACAC,IAAA;YACAC,OAAA;UACA;QACA;MACA,GAAAY,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAX,UAAA,WAAAA,WAAA7G,IAAA;MACA,IAAA2I,MAAA;MACA3I,IAAA,CAAA2E,OAAA,WAAAC,IAAA;QACA,IAAA7D,IAAA;UACA6D,IAAA,EAAAA,IAAA,CAAAA,IAAA;UACAgE,QAAA,EAAAhE,IAAA,CAAAgE,QAAA;UACAC,MAAA,EAAAjE,IAAA,CAAAiE,MAAA;UACAzC,QAAA,EAAAxB,IAAA,CAAAwB,QAAA;UACAd,WAAA,EAAAV,IAAA,CAAAU,WAAA;UACAG,QAAA,EAAAb,IAAA,CAAAa,QAAA;UACAyC,YAAA,EAAAtD,IAAA,CAAAsD;QACA;QACAS,MAAA,CAAA1C,IAAA,CAAAlF,IAAA;MACA;MACA,OAAA4H,MAAA;IACA;IAEA,aACAG,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAA3E,uBAAA;QAAAzD,MAAA,OAAAH,WAAA,CAAAG;MAAA,GAAAmD,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAA,GAAA,CAAAG,IAAA;UACA,IAAAH,GAAA,CAAA/D,IAAA,CAAAwB,EAAA;YACAuH,MAAA,CAAAC,OAAA,CAAA/C,IAAA;cACAgD,IAAA;cACAC,KAAA;gBACAzI,MAAA,EAAAsD,GAAA,CAAA/D,IAAA,CAAAwB;cACA;YACA;UACA;QACA;MACA;IAEA;IAGA;IACA2H,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;QAAAC,WAAA,GAAAH,IAAA,CAAAG,WAAA;MACA;MACA,IAAAA,WAAA;QACA,YAAArI,QAAA,CAAAC,QAAA,CAAAmI,QAAA;MACA;MACA;MACA,IAAAC,WAAA;QACA,YAAArI,QAAA,CAAAE,YAAA,CAAAkI,QAAA;MACA;MACA;MACA,IAAAC,WAAA;QACA,KAAAF,GAAA,CAAAT,QAAA;UACA;YACA1C,OAAA;YACAC,OAAA;UACA;QACA;MACA;MACA,IAAAoD,WAAA;QACA,KAAAF,GAAA,CAAAT,QAAA;UACA;YACA1C,OAAA;YACAC,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAqD,qBAAA,WAAAA,sBAAAH,GAAA,EAAAI,SAAA,EAAAC,KAAA;MACA1F,OAAA,CAAAC,GAAA,CAAAoF,GAAA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,IAAAA,GAAA,CAAA5D,QAAA;QACA,KAAAsC,IAAA,CAAAsB,GAAA,cAAAM,MAAA,CAAAN,GAAA,CAAA5D,QAAA,IAAAkE,MAAA,CAAAF,SAAA,CAAAG,gBAAA;MACA;QACA,KAAA7B,IAAA,CAAAsB,GAAA,cAAAM,MAAA,CAAAF,SAAA,CAAAG,gBAAA;MACA;;MAEA;MACA,IAAAC,aAAA,GAAAJ,SAAA,CAAAK,aAAA,SAAAL,SAAA,CAAAG,gBAAA;MACA,IAAAP,GAAA,CAAAnB,YAAA;QACA,KAAAH,IAAA,CAAAsB,GAAA,kBAAAA,GAAA,CAAAnB,YAAA,SAAA2B,aAAA;MACA;QACA,KAAA9B,IAAA,CAAAsB,GAAA,kBAAAQ,aAAA;MACA;MAEA,KAAA5H,KAAA,WAAA2B,MAAA,CAAA8F,KAAA,GAAAK,UAAA;MACA;MACA,KAAAC,UAAA;IACA;IAEA;IACAA,UAAA,WAAAA,WAAA;MAAA,IAAAX,GAAA,GAAAY,SAAA,CAAAvF,MAAA,QAAAuF,SAAA,QAAAhC,SAAA,GAAAgC,SAAA;MACA;MACA,IAAAZ,GAAA,IAAAA,GAAA,CAAAzE,IAAA;QACA,IAAAW,UAAA,GAAA8D,GAAA,CAAAzE,IAAA,CAAA5B,OAAA;QACA,IAAAkH,KAAA,GAAAb,GAAA,CAAA5D,QAAA;QAEA,IAAAyE,KAAA,aAAAA,KAAA,KAAAjC,SAAA,IAAAiC,KAAA;UACA,IAAAC,QAAA,GAAAR,MAAA,CAAAO,KAAA;;UAEA;UACA,IAAA3E,UAAA,CAAAC,QAAA;YACA,eAAAA,QAAA,CAAA2E,QAAA;cACA,KAAAzD,QAAA;gBACAC,IAAA;gBACAC,OAAA;cACA;cACA,KAAAmB,IAAA,CAAAsB,GAAA;cACA;YACA;UACA;UACA;UAAA,KACA,IAAA9D,UAAA,CAAAC,QAAA;YACA,IAAA2E,QAAA;cACA,KAAAzD,QAAA;gBACAC,IAAA;gBACAC,OAAA;cACA;cACA,KAAAmB,IAAA,CAAAsB,GAAA;cACA;YACA;UACA;UACA;UAAA,KACA;YACA,IAAAc,QAAA;cACA,KAAAzD,QAAA;gBACAC,IAAA;gBACAC,OAAA;cACA;cACA,KAAAmB,IAAA,CAAAsB,GAAA;cACA;YACA;UACA;QACA;MACA;;MAEA;MACA,KAAA3D,kBAAA;IACA;IAEA,aACAA,kBAAA,WAAAA,mBAAA;MACA,IAAA0E,MAAA;MACA,KAAA/J,IAAA,CAAAsE,OAAA,WAAAC,IAAA;QACAwF,MAAA,IAAAT,MAAA,CAAA/E,IAAA,CAAAa,QAAA;MACA;MACA;MACA,KAAAhE,SAAA,QAAA2I,MAAA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,qBAAA,QAAAtI,KAAA,CAAAC,YAAA,CAAAsI,aAAA;QAAAC,OAAA,GAAAF,qBAAA,CAAAE,OAAA;QAAAzK,IAAA,GAAAuK,qBAAA,CAAAvK,IAAA;MACAgE,OAAA,CAAAC,GAAA,CAAAwG,OAAA,EAAAzK,IAAA;MACA,IAAAyK,OAAA;QACA,KAAA/D,QAAA;UACAC,IAAA;UACAC,OAAA;QACA;QACA;MACA;QACA,IAAA8D,OAAA,GAAAC,IAAA,CAAA3K,IAAA,CAAA4K,KAAA;QACA,IAAAC,KAAA;QACA,SAAA7E,CAAA,MAAAA,CAAA,GAAA0E,OAAA,CAAAhG,MAAA,EAAAsB,CAAA;UACA6E,KAAA,CAAA5E,IAAA,CAAAyE,OAAA,CAAAI,UAAA,CAAA9E,CAAA;QACA;QACA,IAAA+E,QAAA,OAAAC,IAAA,MAAAC,UAAA,CAAAJ,KAAA;UAAAlE,IAAA;QAAA;QACA,IAAAuE,QAAA,OAAAC,QAAA;QACAD,QAAA,CAAAE,MAAA,SAAAL,QAAA,KAAAnH,MAAA,CAAAR,IAAA,CAAAD,GAAA;QACAkI,KAAA,MAAA7I,MAAA,CAAAC,GAAA;UACA6I,MAAA;UACAC,IAAA,EAAAL;QACA,GACApH,IAAA,WAAAmB,QAAA;UAAA,OAAAA,QAAA,CAAAuG,IAAA;QAAA,GACA1H,IAAA,WAAA9D,IAAA;UACAgE,OAAA,CAAAC,GAAA,aAAAjE,IAAA;UACA,IAAAA,IAAA,CAAAkE,IAAA;YACAoG,OAAA,CAAAjI,IAAA;cAAAoJ,QAAA,EAAAnB,OAAA,CAAArJ,QAAA,CAAAlB,IAAA;cAAA0C,GAAA,EAAAzC,IAAA,CAAAyC;YAAA;YACA6H,OAAA,CAAApD,MAAA;UACA;YACAoD,OAAA,CAAA5D,QAAA;cACAC,IAAA;cACAC,OAAA;YACA;UACA;QACA,GACAY,KAAA,WAAAkB,KAAA;UACA1E,OAAA,CAAA0E,KAAA,WAAAA,KAAA;UACA4B,OAAA,CAAA5D,QAAA;YACAC,IAAA;YACAC,OAAA;UACA;QACA;MACA;IAEA;IAEA,oBACA8E,aAAA,WAAAA,cAAArC,GAAA,EAAAsC,SAAA;MACA;MACA,KAAA5D,IAAA,CAAAsB,GAAA,KAAAzF,MAAA,CAAA+H,SAAA;MAEA,IAAAA,SAAA;QACA;QACA,KAAAtC,GAAA,CAAA/D,WAAA,IAAA+D,GAAA,CAAA/D,WAAA,CAAAwC,IAAA;UACA,KAAAC,IAAA,CAAAsB,GAAA;UACA;QACA;MACA,WAAAsC,SAAA;QACA;QACA,IAAAtC,GAAA,CAAA5D,QAAA,aAAA4D,GAAA,CAAA5D,QAAA,KAAAwC,SAAA,IAAAoB,GAAA,CAAA5D,QAAA;UACA,KAAAsC,IAAA,CAAAsB,GAAA;UACA;QACA;MACA,WAAAsC,SAAA;QACA;QACA,IAAAtC,GAAA,CAAA5D,QAAA,WAAA4D,GAAA,CAAAnB,YAAA,IAAAmB,GAAA,CAAAnB,YAAA,CAAAJ,IAAA;UACA;UACA,IAAAvC,UAAA,GAAA8D,GAAA,CAAAzE,IAAA,CAAA5B,OAAA;UACA,KAAAuC,UAAA,CAAAC,QAAA;YACA;YACA,KAAAuC,IAAA,CAAAsB,GAAA;YACA;UACA;QACA;MACA;MAEA;IACA;IAEA,eACAxB,qBAAA,WAAAA,sBAAA;MAAA,IAAA+D,OAAA;MACA,KAAAvL,IAAA,CAAAsE,OAAA,WAAAC,IAAA;QACAgH,OAAA,CAAA7D,IAAA,CAAAnD,IAAA;QACAgH,OAAA,CAAA7D,IAAA,CAAAnD,IAAA;QACAgH,OAAA,CAAA7D,IAAA,CAAAnD,IAAA;MACA;IACA;IAEA,qBACAoD,qBAAA,WAAAA,sBAAA6D,OAAA,EAAAC,SAAA,EAAAC,SAAA;MAAA,IAAAC,OAAA;MACAhI,OAAA,CAAAC,GAAA,gDAAAL,MAAA,CAAAiI,OAAA,sBAAAjI,MAAA,CAAAkI,SAAA,4BAAAlI,MAAA,CAAAmI,SAAA;MAEA,KAAAE,SAAA;QACA;QACAC,UAAA;UACA;YACA,IAAAC,KAAA,GAAAH,OAAA,CAAA/J,KAAA,CAAA4J,OAAA;YACA7H,OAAA,CAAAC,GAAA,aAAAkI,KAAA;YAEA,IAAAC,aAAA;YACA,IAAAC,YAAA;YAEA,IAAAF,KAAA,IAAAA,KAAA,CAAAzH,MAAA;cACA;cACA2H,YAAA,GAAAF,KAAA;cACAC,aAAA,GAAAC,YAAA,CAAAC,GAAA,IAAAD,YAAA;YACA,WAAAF,KAAA;cACA;cACAE,YAAA,GAAAF,KAAA;cACAC,aAAA,GAAAC,YAAA,CAAAC,GAAA,IAAAD,YAAA;YACA;YAEA,IAAAD,aAAA,IAAAC,YAAA;cACArI,OAAA,CAAAC,GAAA;;cAEA;cACAmI,aAAA,CAAAG,cAAA;gBACAC,QAAA;gBACAC,KAAA;gBACAC,MAAA;cACA;;cAEA;cACAR,UAAA;gBACA;kBACAG,YAAA,CAAAM,KAAA;kBACA3I,OAAA,CAAAC,GAAA;gBACA,SAAA2I,UAAA;kBACA5I,OAAA,CAAA6I,IAAA,UAAAD,UAAA;gBACA;cACA;;cAEA;cACAZ,OAAA,CAAAtF,QAAA;gBACAC,IAAA;gBACAC,OAAA,WAAAhD,MAAA,CAAAkI,SAAA,aAAAlI,MAAA,CAAAmI,SAAA;gBACAe,QAAA;cACA;YAEA;cACA;cACA9I,OAAA,CAAA6I,IAAA,mCAAAjJ,MAAA,CAAAiI,OAAA;cACAG,OAAA,CAAAe,gBAAA,CAAAjB,SAAA;cAEAE,OAAA,CAAAtF,QAAA;gBACAC,IAAA;gBACAC,OAAA,WAAAhD,MAAA,CAAAkI,SAAA,aAAAlI,MAAA,CAAAmI,SAAA;gBACAe,QAAA;cACA;YACA;UAEA,SAAApE,KAAA;YACA1E,OAAA,CAAA0E,KAAA,eAAAA,KAAA;YACA;YACAsD,OAAA,CAAAgB,aAAA;YAEAhB,OAAA,CAAAtF,QAAA;cACAC,IAAA;cACAC,OAAA,WAAAhD,MAAA,CAAAkI,SAAA,aAAAlI,MAAA,CAAAmI,SAAA;cACAe,QAAA;YACA;UACA;QACA;MACA;IACA;IAEA,eACAC,gBAAA,WAAAA,iBAAAzD,QAAA;MACA;QACA,IAAA2D,SAAA,QAAAX,GAAA,CAAAY,gBAAA;QACA,IAAAD,SAAA,IAAAA,SAAA,CAAA3D,QAAA;UACA2D,SAAA,CAAA3D,QAAA,EAAAiD,cAAA;YACAC,QAAA;YACAC,KAAA;UACA;UACAzI,OAAA,CAAAC,GAAA,kCAAAL,MAAA,CAAA0F,QAAA;QACA;UACAtF,OAAA,CAAA6I,IAAA,4BAAAjJ,MAAA,CAAA0F,QAAA;UACA,KAAA0D,aAAA;QACA;MACA,SAAAtE,KAAA;QACA1E,OAAA,CAAA0E,KAAA,cAAAA,KAAA;QACA,KAAAsE,aAAA;MACA;IACA;IAEA,cACAA,aAAA,WAAAA,cAAA;MACA;QACA,IAAAG,KAAA,QAAAb,GAAA,CAAAc,aAAA;QACA,IAAAD,KAAA;UACAA,KAAA,CAAAZ,cAAA;YACAC,QAAA;YACAC,KAAA;UACA;UACAzI,OAAA,CAAAC,GAAA;QACA;MACA,SAAAyE,KAAA;QACA1E,OAAA,CAAA0E,KAAA,aAAAA,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}