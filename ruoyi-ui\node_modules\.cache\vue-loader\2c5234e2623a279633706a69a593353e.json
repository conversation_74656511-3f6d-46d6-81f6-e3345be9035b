{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardMain\\index.vue?vue&type=template&id=5eb4b045&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardMain\\index.vue", "mtime": 1756456493834}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}