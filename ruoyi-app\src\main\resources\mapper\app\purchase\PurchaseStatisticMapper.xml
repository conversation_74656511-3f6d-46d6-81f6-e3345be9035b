<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.purchase.mapper.PurchaseStatisticMapper">

    <resultMap type="PurchaseStatistic" id="PurchaseStatisticResult">
        <result property="recCreator"    column="REC_CREATOR"    />
        <result property="recCreateTime"    column="REC_CREATE_TIME"    />
        <result property="recRevisor"    column="REC_REVISOR"    />
        <result property="recReviseTime"    column="REC_REVISE_TIME"    />
        <result property="qty"    column="QTY"    />
        <result property="bpoQty"    column="BPO_QTY"    />
        <result property="orderQty"    column="ORDER_QTY"    />
        <result property="inNum"    column="IN_NUM"    />
        <result property="ckQty"    column="CK_QTY"    />
        <result property="reserve1"    column="RESERVE1"    />
        <result property="reserve2"    column="RESERVE2"    />
        <result property="reserve3"    column="RESERVE3"    />
        <result property="bpoExpireNum"    column="BPO_EXPIRE_NUM"    />
        <result property="suppRiskNum"    column="SUPP_RISK_NUM"    />
        <result property="arriveRate"    column="ARRIVE_RATE"    />
        <result property="timeFlag"    column="TIME_FLAG"    />
        <result property="itemType"    column="ITEM_TYPE"    />
        <result property="rejectNum1"    column="REJECT_NUM1"    />
        <result property="rejectNum2"    column="REJECT_NUM2"    />
        <result property="planTotalNum"    column="PLAN_TOTAL_NUM"    />
        <result property="executeRate"    column="EXECUTE_RATE"    />
        <result property="suppTotalNum"    column="SUPP_TOTAL_NUM"    />
        <result property="suppOutNum"    column="SUPP_OUT_NUM"    />
    </resultMap>

    <sql id="selectPurchaseStatisticVo">
        select REC_CREATOR, REC_CREATE_TIME, REC_REVISOR, REC_REVISE_TIME, QTY, BPO_QTY, ORDER_QTY, IN_NUM, CK_QTY, RESERVE1, RESERVE2, RESERVE3, BPO_EXPIRE_NUM, SUPP_RISK_NUM, ARRIVE_RATE, TIME_FLAG, ITEM_TYPE, REJECT_NUM1, REJECT_NUM2,EXECUTE_RATE,PLAN_TOTAL_NUM,SUPP_TOTAL_NUM,SUPP_OUT_NUM,RESERVE4 from TPOPI99_TOTAL_NUM
    </sql>

    <select id="selectPurchaseStatisticList" parameterType="PurchaseStatistic" resultMap="PurchaseStatisticResult">
        <include refid="selectPurchaseStatisticVo"/>
        <where>
            <if test="recCreator != null  and recCreator != ''"> and REC_CREATOR = #{recCreator}</if>
            <if test="recCreateTime != null  and recCreateTime != ''"> and REC_CREATE_TIME = #{recCreateTime}</if>
            <if test="recRevisor != null  and recRevisor != ''"> and REC_REVISOR = #{recRevisor}</if>
            <if test="recReviseTime != null  and recReviseTime != ''"> and REC_REVISE_TIME = #{recReviseTime}</if>
            <if test="qty != null "> and QTY = #{qty}</if>
            <if test="bpoQty != null "> and BPO_QTY = #{bpoQty}</if>
            <if test="orderQty != null "> and ORDER_QTY = #{orderQty}</if>
            <if test="inNum != null "> and IN_NUM = #{inNum}</if>
            <if test="ckQty != null "> and CK_QTY = #{ckQty}</if>
            <if test="reserve1 != null  and reserve1 != ''"> and RESERVE1 = #{reserve1}</if>
            <if test="reserve2 != null  and reserve2 != ''"> and RESERVE2 = #{reserve2}</if>
            <if test="reserve3 != null "> and RESERVE3 = #{reserve3}</if>
            <if test="reserve4 != null "> and RESERVE4 = #{reserve4}</if>
            <if test="timeFlag != null  and timeFlag != ''"> and TIME_FLAG = #{timeFlag}</if>
            <if test="itemType != null  and itemType != ''"> and ITEM_TYPE = #{itemType}</if>
        </where>
    </select>

    <select id="selectPurchaseStatisticById" parameterType="String" resultMap="PurchaseStatisticResult">
        <include refid="selectPurchaseStatisticVo"/>
        where REC_CREATOR = #{recCreator}
    </select>

    <insert id="insertPurchaseStatistic" parameterType="PurchaseStatistic">
        insert into TPOPI99_TOTAL_NUM
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recCreator != null">REC_CREATOR,</if>
            <if test="recCreateTime != null">REC_CREATE_TIME,</if>
            <if test="recRevisor != null">REC_REVISOR,</if>
            <if test="recReviseTime != null">REC_REVISE_TIME,</if>
            <if test="qty != null">QTY,</if>
            <if test="bpoQty != null">BPO_QTY,</if>
            <if test="orderQty != null">ORDER_QTY,</if>
            <if test="inNum != null">IN_NUM,</if>
            <if test="ckQty != null">CK_QTY,</if>
            <if test="reserve1 != null">RESERVE1,</if>
            <if test="reserve2 != null">RESERVE2,</if>
            <if test="reserve3 != null">RESERVE3,</if>
            <if test="reserve4 != null">RESERVE4,</if>
            <if test="timeFlag != null">TIME_FLAG,</if>
            <if test="itemType != null">ITEM_TYPE,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recCreator != null">#{recCreator},</if>
            <if test="recCreateTime != null">#{recCreateTime},</if>
            <if test="recRevisor != null">#{recRevisor},</if>
            <if test="recReviseTime != null">#{recReviseTime},</if>
            <if test="qty != null">#{qty},</if>
            <if test="bpoQty != null">#{bpoQty},</if>
            <if test="orderQty != null">#{orderQty},</if>
            <if test="inNum != null">#{inNum},</if>
            <if test="ckQty != null">#{ckQty},</if>
            <if test="reserve1 != null">#{reserve1},</if>
            <if test="reserve2 != null">#{reserve2},</if>
            <if test="reserve3 != null">#{reserve3},</if>
            <if test="reserve4 != null">#{reserve4},</if>
            <if test="timeFlag != null">#{timeFlag},</if>
            <if test="itemType != null">#{itemType},</if>
         </trim>
    </insert>

    <update id="updatePurchaseStatistic" parameterType="PurchaseStatistic">
        update TPOPI99_TOTAL_NUM
        <trim prefix="SET" suffixOverrides=",">
            <if test="recCreateTime != null">REC_CREATE_TIME = #{recCreateTime},</if>
            <if test="recRevisor != null">REC_REVISOR = #{recRevisor},</if>
            <if test="recReviseTime != null">REC_REVISE_TIME = #{recReviseTime},</if>
            <if test="qty != null">QTY = #{qty},</if>
            <if test="bpoQty != null">BPO_QTY = #{bpoQty},</if>
            <if test="orderQty != null">ORDER_QTY = #{orderQty},</if>
            <if test="inNum != null">IN_NUM = #{inNum},</if>
            <if test="ckQty != null">CK_QTY = #{ckQty},</if>
            <if test="reserve1 != null">RESERVE1 = #{reserve1},</if>
            <if test="reserve2 != null">RESERVE2 = #{reserve2},</if>
            <if test="reserve3 != null">RESERVE3 = #{reserve3},</if>
            <if test="reserve4 != null">RESERVE4 = #{reserve4},</if>
            <if test="timeFlag != null">TIME_FLAG = #{timeFlag},</if>
            <if test="itemType != null">ITEM_TYPE = #{itemType},</if>
        </trim>
        where REC_CREATOR = #{recCreator}
    </update>

    <delete id="deletePurchaseStatisticById" parameterType="String">
        delete from TPOPI99_TOTAL_NUM where REC_CREATOR = #{recCreator}
    </delete>

    <delete id="deletePurchaseStatisticByIds" parameterType="String">
        delete from TPOPI99_TOTAL_NUM where REC_CREATOR in
        <foreach item="recCreator" collection="array" open="(" separator="," close=")">
            #{recCreator}
        </foreach>
    </delete>

</mapper>
