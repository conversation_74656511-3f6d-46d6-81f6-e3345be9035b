package com.ruoyi.web.controller.xctg;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import com.mchange.v2.lang.ObjectUtils;
import com.ruoyi.app.v1.controller.AppBaseV1Controller;
import com.ruoyi.app.v1.domain.HrLateralAssessUser;
import com.ruoyi.app.v1.domain.HrSelfAssessTargetExcel;
import com.ruoyi.app.v1.service.IHrLateralAssessUserService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.app.v1.domain.HrSelfAssessUser;
import com.ruoyi.app.v1.service.IHrSelfAssessUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 绩效考核-干部自评人员配置Controller
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */
@RestController
@RequestMapping("web/selfAssessUser")
public class WebHrSelfAssessUserController extends BaseController

{
    @Autowired
    private IHrSelfAssessUserService hrSelfAssessUserService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IHrLateralAssessUserService hrLateralAssessUserService;

    /**
     * 查询绩效考核-干部自评人员配置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HrSelfAssessUser hrSelfAssessUser)
    {
        startPage();
        List<HrSelfAssessUser> list = hrSelfAssessUserService.findList(hrSelfAssessUser);
        return getDataTable(list);
    }

    @GetMapping("/listAll")
    public AjaxResult listAll(HrSelfAssessUser hrSelfAssessUser)
    {
        List<HrSelfAssessUser> list = hrSelfAssessUserService.findList(hrSelfAssessUser);
        return success(list);
    }

    /**
     * 查询绩效考核-干部自评人员配置列表
     */
    @GetMapping("/listAvailable")
    public AjaxResult listAvailable(HrSelfAssessUser hrSelfAssessUser)
    {
        String workNo = SecurityUtils.getUsername();
        HrLateralAssessUser user = hrLateralAssessUserService.selectHrLateralAssessUserById(workNo);
        if(StringUtils.isNotNull(user)){
            // 如果前端传递了deptId参数，需要验证是否在用户权限范围内
            if(StringUtils.isNotNull(hrSelfAssessUser.getDeptId())){
                Long[] userDeptIds = user.getDeptIds();
                boolean hasPermission = false;
                for(Long deptId : userDeptIds){
                    if(deptId.equals(hrSelfAssessUser.getDeptId())){
                        hasPermission = true;
                        break;
                    }
                }
                // 如果用户有权限查看该部门，则只查询该部门；否则按原权限查询
                if(hasPermission){
                    hrSelfAssessUser.setDeptIds(new Long[]{hrSelfAssessUser.getDeptId()});
                } else {
                    hrSelfAssessUser.setDeptIds(user.getDeptIds());
                }
            } else {
                hrSelfAssessUser.setDeptIds(user.getDeptIds());
            }
            List<HrSelfAssessUser> list = hrSelfAssessUserService.listAvailable(hrSelfAssessUser);
            return AjaxResult.success(list);
        }
        return AjaxResult.success();
    }

    /**
     * 批量获取用户填写状态
     */
    @GetMapping("/listAvailableWithStatus")
    public AjaxResult listAvailableWithStatus(HrSelfAssessUser hrSelfAssessUser, String assessDate)
    {
        if(StringUtils.isBlank(assessDate)) {
            return error("缺少必要参数：考核年月");
        }

        String workNo = SecurityUtils.getUsername();
        HrLateralAssessUser user = hrLateralAssessUserService.selectHrLateralAssessUserById(workNo);
        if(StringUtils.isNotNull(user)){
            // 如果前端传递了deptId参数，需要验证是否在用户权限范围内
            if(StringUtils.isNotNull(hrSelfAssessUser.getDeptId())){
                Long[] userDeptIds = user.getDeptIds();
                boolean hasPermission = false;
                for(Long deptId : userDeptIds){
                    if(deptId.equals(hrSelfAssessUser.getDeptId())){
                        hasPermission = true;
                        break;
                    }
                }
                // 如果用户有权限查看该部门，则只查询该部门；否则按原权限查询
                if(hasPermission){
                    hrSelfAssessUser.setDeptIds(new Long[]{hrSelfAssessUser.getDeptId()});
                } else {
                    hrSelfAssessUser.setDeptIds(user.getDeptIds());
                }
            } else {
                hrSelfAssessUser.setDeptIds(user.getDeptIds());
            }
            List<HrSelfAssessUser> list = hrSelfAssessUserService.listAvailableWithStatus(hrSelfAssessUser, assessDate);
            return AjaxResult.success(list);
        }
        return AjaxResult.success();
    }

    /**
     * 导出绩效考核-干部自评人员配置列表
     */
    @Log(title = "绩效考核-干部自评人员配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HrSelfAssessUser hrSelfAssessUser)
    {
        List<HrSelfAssessUser> list = hrSelfAssessUserService.findList(hrSelfAssessUser);
        ExcelUtil<HrSelfAssessUser> util = new ExcelUtil<HrSelfAssessUser>(HrSelfAssessUser.class);
        util.exportExcel(list, "绩效考核-干部自评人员配置数据");
    }

    /**
     * 获取绩效考核-干部自评人员配置详细信息
     */
    @GetMapping("/getInfo")
    public AjaxResult getInfo(String id)
    {
        if(StringUtils.isBlank(id) ) return error("缺少必要参数id");
        return success(hrSelfAssessUserService.get(id));
    }

    /**
     * 获取绩效考核-干部自评人员配置详细信息(弃用)
     */
    @GetMapping("/getInfoByWorkNo")
    public AjaxResult getInfoByWorkNo(String workNo)
    {
        if(StringUtils.isBlank(workNo)) return error("缺少必要参数");
        return success(hrSelfAssessUserService.getByWorkNo(workNo));
    }

    /**
     * 获取自评填写人员部门列表
     */
    @GetMapping("/getReportDeptList")
    public AjaxResult getReportDeptList()
    {
        String workNo = SecurityUtils.getUsername();
        return success(hrSelfAssessUserService.getReportDeptList(workNo));
    }

    /**
     * 获取自评审批人员部门列表
     */
    @GetMapping("/getCheckDeptList")
    public AjaxResult getCheckDeptList()
    {
        String workNo = SecurityUtils.getUsername();
        return success(hrSelfAssessUserService.getCheckDeptList(workNo));
    }

    /**
     * 获取自评填写人员部门列表
     */
    @GetMapping("/getByWorkNoDeptId")
    public AjaxResult getByWorkNoDeptId(HrSelfAssessUser hrSelfAssessUser)
    {
        if(StringUtils.isNull(hrSelfAssessUser.getDeptId())) return error("缺少必要参数:部门");
        if(StringUtils.isNull(hrSelfAssessUser.getWorkNo())){
            String workNo = SecurityUtils.getUsername();
            hrSelfAssessUser.setWorkNo(workNo);
        }
        return success(hrSelfAssessUserService.getByWorkNoDeptId(hrSelfAssessUser));
    }

    /**
     * 获取绩效考核-干部自评人员配置详细信息
     */
    @GetMapping("/getSelfAssessUserInfo")
    public AjaxResult getSelfAssessUserInfo(HrSelfAssessUser hrSelfAssessUser)
    {
        if(StringUtils.isNull(hrSelfAssessUser.getDeptIds())) return error("缺少必要参数");
        String workNo = SecurityUtils.getUsername();
        hrSelfAssessUser.setWorkNo(workNo);
        return success(hrSelfAssessUserService.getByUserInfo(hrSelfAssessUser));
    }

    /**
     * 新增绩效考核-干部自评人员配置
     */
    @Log(title = "绩效考核-干部自评人员配置", businessType = BusinessType.INSERT)
    @PostMapping("/insert")
    public AjaxResult insert(@RequestBody HrSelfAssessUser hrSelfAssessUser)
    {
        SysUser sysUser = sysUserService.selectUserByUserName(hrSelfAssessUser.getWorkNo());
        if(Objects.isNull(sysUser)){
            return AjaxResult.error("系统中无该用户，请先添加系统用户："+hrSelfAssessUser.getWorkNo());
        }
        if(!hrSelfAssessUserService.checkUserUnique(hrSelfAssessUser)){
            return AjaxResult.error("部门-用户已存在");
        }
        return toAjax(hrSelfAssessUserService.insert(hrSelfAssessUser));
    }

    /**
     * 新增绩效考核-干部自评人员配置
     */
    @Log(title = "绩效考核-导入干部自评人员配置", businessType = BusinessType.INSERT)
    @PostMapping("/importInfo")
    public AjaxResult importInfo(MultipartFile file) throws Exception
    {
        ExcelUtil<HrSelfAssessUser> util = new ExcelUtil<>(HrSelfAssessUser.class);
        List<HrSelfAssessUser> importList = util.importExcel(file.getInputStream());
//        return AjaxResult.success(importList);
        return success(hrSelfAssessUserService.importInfo(importList));
    }

    /**
     * 修改绩效考核-干部自评人员配置
     */
    @Log(title = "绩效考核-干部自评人员配置", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public AjaxResult update(@RequestBody HrSelfAssessUser hrSelfAssessUser)
    {
        if(StringUtils.isBlank(hrSelfAssessUser.getId()) ) return error("缺少必要参数id");
//        HrSelfAssessUser user = hrSelfAssessUserService.get(hrSelfAssessUser.getId());
//        if(!Arrays.equals(user.getDeptIds(), hrSelfAssessUser.getDeptIds())){
            if(!hrSelfAssessUserService.checkUserUnique(hrSelfAssessUser)){
                return AjaxResult.error("部门-用户已存在");
            }
//        }
        return toAjax(hrSelfAssessUserService.update(hrSelfAssessUser));
    }

    /**
     * 删除绩效考核-干部自评人员配置
     */
    @Log(title = "绩效考核-干部自评人员配置", businessType = BusinessType.DELETE)
	@PutMapping("/delete")
    public AjaxResult PutMapping(@RequestBody HrSelfAssessUser hrSelfAssessUser)
    {
        return toAjax(hrSelfAssessUserService.delete(hrSelfAssessUser));
    }

    /**
     * 修改绩效考核-干部自评人员配置
     */
    @Log(title = "绩效考核-干部自评人员配置", businessType = BusinessType.UPDATE)
    @PostMapping("/permissionRefresh")
    public AjaxResult permissionRefresh()
    {
        return toAjax(hrSelfAssessUserService.permissionRefresh());
    }
}
