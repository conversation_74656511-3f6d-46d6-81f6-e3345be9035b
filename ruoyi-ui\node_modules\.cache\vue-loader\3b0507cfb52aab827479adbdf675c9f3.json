{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPrice\\index.vue?vue&type=template&id=183b5093&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPrice\\index.vue", "mtime": 1756456493853}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}