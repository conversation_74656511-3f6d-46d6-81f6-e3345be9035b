# 物料采购驾驶舱

## 概述
这是一个基于Vue.js和ECharts的数据可视化驾驶舱页面，采用科技蓝色主题，展示采购和销售相关的关键指标和数据分析。

## 功能特性

### 页面布局
- **左侧面板**（3个模块）：
  1. 个人消费情况 - 饼图展示不同地区的消费分布
  2. 采购分析 - 折线图展示月度采购趋势
  3. 产品分析 - 条形图展示各产品类别的销售情况

- **中间面板**（4个模块）：
  1. 实时数据统计 - 显示销售额和订单数量
  2. 地图分析 - 散点图展示各地区业务分布
  3. 进度指示器 - 水位图显示完成度
  4. 底部统计卡片 - 展示关键业务指标

- **右侧面板**（3个模块）：
  1. 销售漏斗分析 - 展示客户转化情况
  2. 采购趋势分析 - 折线图和统计数据
  3. 供应商分析 - 饼图展示供应商占比

### 技术特性
- 响应式设计，支持不同屏幕尺寸
- 科技蓝色主题，符合大屏展示需求
- 使用@jiaminghi/data-view组件库提供炫酷边框效果
- ECharts图表自适应窗口大小变化
- 模块化设计，易于维护和扩展

## 技术栈
- **Vue.js 2.x** - 前端框架
- **ECharts 4.9.0** - 图表库
- **@jiaminghi/data-view** - 大屏组件库
- **Element UI** - UI组件库
- **SCSS** - 样式预处理器

## 文件结构
```
purchaseDashboardMain/
├── index.vue          # 主页面组件
├── README.md          # 说明文档
└── api/
    └── index.js       # API接口定义
```

## 使用方法

### 1. 路由访问
页面路由已配置为：`/purchaseDashboardMain`

### 2. 全屏显示
页面使用了`dv-full-screen-container`组件，支持全屏展示效果。

### 3. 数据接口
API接口定义在`@/api/purchaseDashboardMain/index.js`中，包含以下接口：
- `getDashboardData()` - 获取驾驶舱总体数据
- `getPersonalConsumption()` - 获取个人消费数据
- `getPurchaseAnalysis()` - 获取采购分析数据
- `getProductAnalysis()` - 获取产品分析数据
- `getMapData()` - 获取地图数据
- `getRealTimeStats()` - 获取实时统计数据
- `getSalesFunnel()` - 获取销售漏斗数据
- `getPurchaseTrend()` - 获取采购趋势数据
- `getSupplierAnalysis()` - 获取供应商分析数据
- `getInventoryAnalysis()` - 获取库存分析数据

### 4. 自定义配置
可以通过修改以下部分来自定义页面：

#### 修改标题
```vue
<h1 class="main-title">物料采购驾驶舱</h1>
```

#### 修改颜色主题
在SCSS样式中修改颜色变量：
```scss
$primary-color: #00BAFF;
$secondary-color: #3DE7C9;
```

#### 修改图表配置
在对应的图表初始化方法中修改ECharts配置选项。

## 响应式设计
页面支持以下断点：
- **大屏幕** (>1600px): 完整布局
- **中等屏幕** (1200px-1600px): 调整字体和间距
- **小屏幕** (<1200px): 垂直布局

## 注意事项
1. 确保项目中已安装所需依赖包
2. 图表会自动适应容器大小变化
3. 页面使用了大量CSS3特效，建议在现代浏览器中使用
4. 数据接口需要后端支持，当前使用模拟数据

## 扩展建议
1. 可以添加更多图表类型（雷达图、仪表盘等）
2. 支持数据实时更新和WebSocket连接
3. 添加数据导出功能
4. 支持主题切换
5. 添加数据钻取功能

## 维护说明
- 图表实例会在组件销毁时自动清理
- 窗口大小变化会触发图表重绘
- 所有异步操作都有错误处理机制
