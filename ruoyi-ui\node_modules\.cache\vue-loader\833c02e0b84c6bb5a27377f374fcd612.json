{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\dashboard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\qualityCost\\dashboard\\index.vue", "mtime": 1756456493915}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQppbXBvcnQgeyBjb3N0Q2VudGVybGlzdCB9IGZyb20gIkAvYXBpL3F1YWxpdHlDb3N0L3F1YWxpdHlDb3N0RGV0YWlsIjsNCmltcG9ydCB7IGdldFBpZUNoYXJ0RGF0YSwgZ2V0TXVsdGlMaW5lQ2hhcnREYXRhLCBnZXRRdWFsaXR5Q29zdERldGFpbCwgZ2V0RXh0ZXJuYWxDb3N0RGV0YWlsLCBnZXRJbnRlcm5hbENvc3REZXRhaWwsIGdldENvbWJvQ2hhcnREZXRhaWwsZ2V0V2F0ZXJmYWxsQ2hhcnREZXRhaWwsZ2V0U2NyYXBMb3NzQ2hhcnREZXRhaWxzRGV0YWlsLGdldFF1YWxpdHlPYmplY3Rpb25Mb3NzRGV0YWlsLGdldEZhY3RvcnlSZWplY3Rpb25DaGFydERldGFpbCxnZXRGYWN0b3J5U2NyYXBDaGFydERldGFpbCxnZXRGYWN0b3J5Q29udHJhY3RDaGFydERldGFpbCxnZXRGYWN0b3J5UmV0dXJuQ2hhcnREZXRhaWwgfSBmcm9tICJAL2FwaS9xdWFsaXR5Q29zdC9kYXNoYm9hcmQiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdRdWFsaXR5Q29zdERhc2hib2FyZCcsDQogIGRhdGEoKSB7DQogICAgLy8g6I635Y+W6buY6K6k5Lya6K6h5pyf77yI5LiK5Liq5pyI77yJDQogICAgY29uc3QgZ2V0RGVmYXVsdFllYXJNb250aCA9ICgpID0+IHsNCiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7DQogICAgICBjb25zdCB5ZWFyID0gbm93LmdldEZ1bGxZZWFyKCk7DQogICAgICBjb25zdCBtb250aCA9IG5vdy5nZXRNb250aCgpICsgMTsgLy8gMS0xMg0KICAgICAgY29uc3QgZGF5ID0gbm93LmdldERhdGUoKTsNCiAgICAgIGNvbnN0IGhvdXIgPSBub3cuZ2V0SG91cnMoKTsNCg0KICAgICAgLy8g5aaC5p6c5LuK5aSp5piv5pys5pyIMjXlj7c454K55YmN77yI5ZCrMjXlj7c3OjU577yJ77yM5YiZ55So5LiK5Liq5pyIDQogICAgICBpZiAoZGF5IDwgMjggfHwgKGRheSA9PT0gMjggJiYgaG91ciA8IDEpKSB7DQogICAgICAgIC8vIOWkhOeQhjHmnIjml7bnmoTot6jlubQNCiAgICAgICAgY29uc3QgcHJldk1vbnRoID0gbW9udGggPT09IDEgPyAxMiA6IG1vbnRoIC0gMTsNCiAgICAgICAgY29uc3QgcHJldlllYXIgPSBtb250aCA9PT0gMSA/IHllYXIgLSAxIDogeWVhcjsNCiAgICAgICAgcmV0dXJuIGAke3ByZXZZZWFyfS0ke1N0cmluZyhwcmV2TW9udGgpLnBhZFN0YXJ0KDIsICcwJyl9YDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJldHVybiBgJHt5ZWFyfS0ke1N0cmluZyhtb250aCkucGFkU3RhcnQoMiwgJzAnKX1gOw0KICAgICAgfQ0KICAgIH07DQoNCiAgICByZXR1cm4gew0KICAgICAgdXBkYXRlVGltZTogJzIwMjMtMTAtMjcgMTA6MDAnLA0KICAgICAgY2hhcnRzOiB7fSwNCiAgICAgIC8vIOaIkOacrOS4reW/g+WSjOS8muiuoeacnw0KICAgICAgY29zdENlbnRlcjogJycsDQogICAgICBhY2NvdW50aW5nUGVyaW9kOiBnZXREZWZhdWx0WWVhck1vbnRoKCksDQogICAgICAvLyDotKjph4/miJDmnKznsbvlnovvvIzpu5jorqTlgLzkuLox77yI5LiN5ZCr5YiX5YWl6aG577yJDQogICAgICBjb250YWluVHlwZTogMSwNCiAgICAgIC8vIOaIkOacrOS4reW/g+mAiemhuQ0KICAgICAgY29zdENlbnRlck9wdGlvbnM6IFtdLA0KICAgICAgY29zdENlbnRlckxvYWRpbmc6IGZhbHNlLA0KICAgICAgcXVhbGl0eUNvc3REZXRhaWw6IHt9LA0KICAgICAgcXVhbGl0eUNvc3REYXRhOiB7fQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICAvLyDnm5HlkKzmiJDmnKzkuK3lv4Plj5jljJYNCiAgICBjb3N0Q2VudGVyOiB7DQogICAgICBoYW5kbGVyKCkgew0KICAgICAgICBjb25zb2xlLmxvZygn5oiQ5pys5Lit5b+D5Y+Y5YyWOicsIHRoaXMuY29zdENlbnRlcik7DQogICAgICAgIHRoaXMucmVmcmVzaENoYXJ0RGF0YSgpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g55uR5ZCs5Lya6K6h5pyf5Y+Y5YyWDQogICAgYWNjb3VudGluZ1BlcmlvZDogew0KICAgICAgaGFuZGxlcigpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+S8muiuoeacn+WPmOWMljonLCB0aGlzLmFjY291bnRpbmdQZXJpb2QpOw0KICAgICAgICB0aGlzLnJlZnJlc2hDaGFydERhdGEoKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOebkeWQrOi0qOmHj+aIkOacrOexu+Wei+WPmOWMlg0KICAgIGNvbnRhaW5UeXBlOiB7DQogICAgICBoYW5kbGVyKCkgew0KICAgICAgICBjb25zb2xlLmxvZygn6LSo6YeP5oiQ5pys57G75Z6L5Y+Y5YyWOicsIHRoaXMuY29udGFpblR5cGUpOw0KICAgICAgICB0aGlzLnJlZnJlc2hDaGFydERhdGEoKTsNCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5nZXRDb3N0Q2VudGVyTGlzdCgpOw0KICAgIC8v6LSo6YeP5oiQ5pys5Zub5aSn57G75Yir5Y2g5q+UDQoNCiAgICB0aGlzLmluaXRDaGFydHMoKTsNCiAgICB0aGlzLnJlc2l6ZU9ic2VydmVyID0gbmV3IFJlc2l6ZU9ic2VydmVyKCgpID0+IHsNCiAgICAgIHRoaXMucmVzaXplQ2hhcnRzKCkNCiAgICB9KQ0KICAgIHRoaXMucmVzaXplT2JzZXJ2ZXIub2JzZXJ2ZSh0aGlzLiRlbCkNCiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgdGhpcy5yZXNpemVDaGFydHMpDQogIH0sDQogIGJlZm9yZURlc3Ryb3koKSB7DQogICAgLy8g6ZSA5q+B5omA5pyJ5Zu+6KGo5a6e5L6LDQogICAgT2JqZWN0LnZhbHVlcyh0aGlzLmNoYXJ0cykuZm9yRWFjaChjaGFydCA9PiB7DQogICAgICBpZiAoY2hhcnQpIHsNCiAgICAgICAgY2hhcnQuZGlzcG9zZSgpDQogICAgICB9DQogICAgfSkNCiAgICBpZiAodGhpcy5yZXNpemVPYnNlcnZlcikgew0KICAgICAgdGhpcy5yZXNpemVPYnNlcnZlci5kaXNjb25uZWN0KCkNCiAgICB9DQogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVzaXplQ2hhcnRzKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5Yik5pat55m+5YiG5q+U5piv5ZCm5Li66LSf5pWwDQogICAgaXNOZWdhdGl2ZVBlcmNlbnRhZ2UocGVyY2VudGFnZSkgew0KICAgICAgaWYgKCFwZXJjZW50YWdlKSByZXR1cm4gZmFsc2U7DQogICAgICByZXR1cm4gcGVyY2VudGFnZS50b1N0cmluZygpLnN0YXJ0c1dpdGgoJy0nKTsNCiAgICB9LA0KDQogICAgLy8g5qC55o2u55m+5YiG5q+U5q2j6LSf5YC86L+U5Zue5a+55bqU55qEQ1NT57G7DQogICAgZ2V0UGVyY2VudGFnZUNsYXNzKHBlcmNlbnRhZ2UpIHsNCiAgICAgIGlmICghcGVyY2VudGFnZSkgcmV0dXJuICduZXV0cmFsJzsNCiAgICAgIHJldHVybiB0aGlzLmlzTmVnYXRpdmVQZXJjZW50YWdlKHBlcmNlbnRhZ2UpID8gJ25lZ2F0aXZlJyA6ICdwb3NpdGl2ZSc7DQogICAgfSwNCg0KICAgIC8vIOagvOW8j+WMluaVsOWtl++8jOacgOWkmuS/neeVmeS4pOS9jeWwj+aVsA0KICAgIGZvcm1hdE51bWJlcihudW0pIHsNCiAgICAgIGlmIChudW0gPT09IG51bGwgfHwgbnVtID09PSB1bmRlZmluZWQgfHwgbnVtID09PSAnJykgew0KICAgICAgICByZXR1cm4gJzAnOw0KICAgICAgfQ0KICAgICAgY29uc3QgbnVtYmVyID0gTnVtYmVyKG51bSk7DQogICAgICBpZiAoaXNOYU4obnVtYmVyKSkgew0KICAgICAgICByZXR1cm4gJzAnOw0KICAgICAgfQ0KICAgICAgLy8g5L2/55SodG9GaXhlZCgyKeS/neeVmeS4pOS9jeWwj+aVsO+8jOeEtuWQjueUqHBhcnNlRmxvYXTljrvmjonmnKvlsL7nmoQwDQogICAgICByZXR1cm4gcGFyc2VGbG9hdChudW1iZXIudG9GaXhlZCgyKSkudG9TdHJpbmcoKTsNCiAgICB9LA0KDQogICAgLy8g5re75Yqg5Y2D5YiG5L2N5YiG6ZqU56ymDQogICAgYWRkVGhvdXNhbmRTZXBhcmF0b3IobnVtKSB7DQogICAgICByZXR1cm4gbnVtLnRvU3RyaW5nKCkucmVwbGFjZSgvXEIoPz0oXGR7M30pKyg/IVxkKSkvZywgJywnKTsNCiAgICB9LA0KDQogICAgLy8g5qC85byP5YyW5Lqn6YePL+mUgOmHj+S4uuS4h+WQqOWNleS9jQ0KICAgIGZvcm1hdFRvbm5hZ2UobnVtKSB7DQogICAgICBpZiAobnVtID09PSBudWxsIHx8IG51bSA9PT0gdW5kZWZpbmVkIHx8IG51bSA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuICcw5LiH5ZCoJzsNCiAgICAgIH0NCiAgICAgIGNvbnN0IG51bWJlciA9IE51bWJlcihudW0pOw0KICAgICAgaWYgKGlzTmFOKG51bWJlcikpIHsNCiAgICAgICAgcmV0dXJuICcw5LiH5ZCoJzsNCiAgICAgIH0NCiAgICAgIC8vIOi9rOaNouS4uuS4h+WQqOW5tuS/neeVmeS4pOS9jeWwj+aVsO+8jOa3u+WKoOWNg+WIhuS9jeWIhumalOespg0KICAgICAgY29uc3QgcmVzdWx0ID0gKG51bWJlciAvIDEwMDAwKS50b0ZpeGVkKDIpOw0KICAgICAgcmV0dXJuIGAke3RoaXMuYWRkVGhvdXNhbmRTZXBhcmF0b3IocmVzdWx0KX3kuIflkKhgOw0KICAgIH0sDQoNCiAgICAvLyDmoLzlvI/ljJbmgLvph5Hpop3kuLrkuIflhYPljZXkvY0NCiAgICBmb3JtYXRBbW91bnQobnVtKSB7DQogICAgICBpZiAobnVtID09PSBudWxsIHx8IG51bSA9PT0gdW5kZWZpbmVkIHx8IG51bSA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuICcw5LiH5YWDJzsNCiAgICAgIH0NCiAgICAgIGNvbnN0IG51bWJlciA9IE51bWJlcihudW0pOw0KICAgICAgaWYgKGlzTmFOKG51bWJlcikpIHsNCiAgICAgICAgcmV0dXJuICcw5LiH5YWDJzsNCiAgICAgIH0NCiAgICAgIC8vIOi9rOaNouS4uuS4h+WFg+W5tuS/neeVmeS4pOS9jeWwj+aVsO+8jOa3u+WKoOWNg+WIhuS9jeWIhumalOespg0KICAgICAgY29uc3QgcmVzdWx0ID0gKG51bWJlciAvIDEwMDAwKS50b0ZpeGVkKDIpOw0KICAgICAgcmV0dXJuIGAke3RoaXMuYWRkVGhvdXNhbmRTZXBhcmF0b3IocmVzdWx0KX3kuIflhYNgOw0KICAgIH0sDQoNCiAgICAvLyDmoLzlvI/ljJblkKjpkqLmiJDmnKzkuLrlhYMv5ZCo5Y2V5L2NDQogICAgZm9ybWF0VW5pdENvc3QobnVtKSB7DQogICAgICBpZiAobnVtID09PSBudWxsIHx8IG51bSA9PT0gdW5kZWZpbmVkIHx8IG51bSA9PT0gJycpIHsNCiAgICAgICAgcmV0dXJuICcw5YWDL+WQqCc7DQogICAgICB9DQogICAgICBjb25zdCBudW1iZXIgPSBOdW1iZXIobnVtKTsNCiAgICAgIGlmIChpc05hTihudW1iZXIpKSB7DQogICAgICAgIHJldHVybiAnMOWFgy/lkKgnOw0KICAgICAgfQ0KICAgICAgLy8g5L+d55WZ5Lik5L2N5bCP5pWw5bm25re75Yqg5Y2V5L2N77yM5re75Yqg5Y2D5YiG5L2N5YiG6ZqU56ymDQogICAgICBjb25zdCByZXN1bHQgPSBudW1iZXIudG9GaXhlZCgyKTsNCiAgICAgIHJldHVybiBgJHt0aGlzLmFkZFRob3VzYW5kU2VwYXJhdG9yKHJlc3VsdCl95YWDL+WQqGA7DQogICAgfSwNCg0KICAgIGdldEZhY3RvcnlSZWplY3Rpb25DaGFydERldGFpbCgpIHsNCiAgICAgIC8vIOWPquacieW9k+aIkOacrOS4reW/g+WSjOS8muiuoeacn+mDveacieWAvOaXtuaJjeivt+axgg0KICAgICAgaWYgKCF0aGlzLmNvc3RDZW50ZXIgfHwgIXRoaXMuYWNjb3VudGluZ1BlcmlvZCB8fCAodGhpcy5jb250YWluVHlwZSAhPT0gMiAmJiB0aGlzLmNvbnRhaW5UeXBlICE9PSAxKSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5oiQ5pys5Lit5b+D44CB5Lya6K6h5pyf5oiW6LSo6YeP5oiQ5pys57G75Z6L5Li656m677yM6Lez6L+H5pWw5o2u6K+35rGCJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICB5ZWFyTW9udGg6IHRoaXMuYWNjb3VudGluZ1BlcmlvZC5yZXBsYWNlKCctJywgJycpLCAvLyDlsIYgMjAyNS0wNiDovazmjaLkuLogMjAyNTA2DQogICAgICAgIGNvbnRhaW5UeXBlOiB0aGlzLmNvbnRhaW5UeXBlDQogICAgICB9Ow0KDQogICAgICBnZXRGYWN0b3J5UmVqZWN0aW9uQ2hhcnREZXRhaWwocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ2dldEZhY3RvcnlSZWplY3Rpb25DaGFydERldGFpbDonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgLy8g5pu05pawV2F0ZXJmYWxsQ2hhcnTmn7Hnirblm74NCiAgICAgICAgICB0aGlzLnVwZGF0ZUZhY3RvcnlSZWplY3Rpb25DaGFydChyZXNwb25zZS5kYXRhKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5ZXYXRlcmZhbGxDaGFydOaVsOaNruWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlldhdGVyZmFsbENoYXJ05pWw5o2u5aSx6LSlJyk7DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgZ2V0RmFjdG9yeVNjcmFwQ2hhcnREZXRhaWwoKSB7DQogICAgICAvLyDlj6rmnInlvZPmiJDmnKzkuK3lv4PlkozkvJrorqHmnJ/pg73mnInlgLzml7bmiY3or7fmsYINCiAgICAgIGlmICghdGhpcy5jb3N0Q2VudGVyIHx8ICF0aGlzLmFjY291bnRpbmdQZXJpb2QgfHwgKHRoaXMuY29udGFpblR5cGUgIT09IDIgJiYgdGhpcy5jb250YWluVHlwZSAhPT0gMSkpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aIkOacrOS4reW/g+OAgeS8muiuoeacn+aIlui0qOmHj+aIkOacrOexu+Wei+S4uuepuu+8jOi3s+i/h+aVsOaNruivt+axgicpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgeWVhck1vbnRoOiB0aGlzLmFjY291bnRpbmdQZXJpb2QucmVwbGFjZSgnLScsICcnKSwgLy8g5bCGIDIwMjUtMDYg6L2s5o2i5Li6IDIwMjUwNg0KICAgICAgICBjb250YWluVHlwZTogdGhpcy5jb250YWluVHlwZQ0KICAgICAgfTsNCg0KICAgICAgZ2V0RmFjdG9yeVNjcmFwQ2hhcnREZXRhaWwocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ2dldEZhY3RvcnlTY3JhcENoYXJ0RGV0YWlsOicsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICAvLyDmm7TmlrDlkITliIbljoLmiqXlup/msYfmgLvmn7Hnirblm74NCiAgICAgICAgICB0aGlzLnVwZGF0ZUZhY3RvcnlTY3JhcENoYXJ0KHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWQhOWIhuWOguaKpeW6n+axh+aAu+aVsOaNruWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluWQhOWIhuWOguaKpeW6n+axh+aAu+aVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGdldEZhY3RvcnlDb250cmFjdENoYXJ0RGV0YWlsKCkgew0KICAgICAgLy8g5Y+q5pyJ5b2T5oiQ5pys5Lit5b+D5ZKM5Lya6K6h5pyf6YO95pyJ5YC85pe25omN6K+35rGCDQogICAgICBpZiAoIXRoaXMuY29zdENlbnRlciB8fCAhdGhpcy5hY2NvdW50aW5nUGVyaW9kIHx8ICh0aGlzLmNvbnRhaW5UeXBlICE9PSAyICYmIHRoaXMuY29udGFpblR5cGUgIT09IDEpKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmiJDmnKzkuK3lv4PjgIHkvJrorqHmnJ/miJbotKjph4/miJDmnKznsbvlnovkuLrnqbrvvIzot7Pov4fmlbDmja7or7fmsYInKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIHllYXJNb250aDogdGhpcy5hY2NvdW50aW5nUGVyaW9kLnJlcGxhY2UoJy0nLCAnJyksIC8vIOWwhiAyMDI1LTA2IOi9rOaNouS4uiAyMDI1MDYNCiAgICAgICAgY29udGFpblR5cGU6IHRoaXMuY29udGFpblR5cGUNCiAgICAgIH07DQoNCiAgICAgIGdldEZhY3RvcnlDb250cmFjdENoYXJ0RGV0YWlsKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCdnZXRGYWN0b3J5Q29udHJhY3RDaGFydERldGFpbDonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgLy8g5pu05paw5ZCE5YiG5Y6C6ISx5ZCI5ZCM5rGH5oC75p+x54q25Zu+DQogICAgICAgICAgdGhpcy51cGRhdGVGYWN0b3J5Q29udHJhY3RDaGFydChyZXNwb25zZS5kYXRhKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5blkITliIbljoLohLHlkIjlkIzmsYfmgLvmlbDmja7lpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5blkITliIbljoLohLHlkIjlkIzmsYfmgLvmlbDmja7lpLHotKUnKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBnZXRGYWN0b3J5UmV0dXJuQ2hhcnREZXRhaWwoKSB7DQogICAgICAvLyDlj6rmnInlvZPmiJDmnKzkuK3lv4PlkozkvJrorqHmnJ/pg73mnInlgLzml7bmiY3or7fmsYINCiAgICAgIGlmICghdGhpcy5jb3N0Q2VudGVyIHx8ICF0aGlzLmFjY291bnRpbmdQZXJpb2QgfHwgKHRoaXMuY29udGFpblR5cGUgIT09IDIgJiYgdGhpcy5jb250YWluVHlwZSAhPT0gMSkpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aIkOacrOS4reW/g+OAgeS8muiuoeacn+aIlui0qOmHj+aIkOacrOexu+Wei+S4uuepuu+8jOi3s+i/h+aVsOaNruivt+axgicpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgeWVhck1vbnRoOiB0aGlzLmFjY291bnRpbmdQZXJpb2QucmVwbGFjZSgnLScsICcnKSwgLy8g5bCGIDIwMjUtMDYg6L2s5o2i5Li6IDIwMjUwNg0KICAgICAgICBjb250YWluVHlwZTogdGhpcy5jb250YWluVHlwZQ0KICAgICAgfTsNCg0KICAgICAgZ2V0RmFjdG9yeVJldHVybkNoYXJ0RGV0YWlsKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCdnZXRGYWN0b3J5UmV0dXJuQ2hhcnREZXRhaWw6JywgcmVzcG9uc2UpOw0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIC8vIOabtOaWsOWQhOWIhuWOgumAgOi0p+axh+aAu+afseeKtuWbvg0KICAgICAgICAgIHRoaXMudXBkYXRlRmFjdG9yeVJldHVybkNoYXJ0KHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWQhOWIhuWOgumAgOi0p+axh+aAu+aVsOaNruWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluWQhOWIhuWOgumAgOi0p+axh+aAu+aVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIHVwZGF0ZUZhY3RvcnlSZXR1cm5DaGFydChkYXRhKSB7DQogICAgICBpZiAodGhpcy5jaGFydHMuZmFjdG9yeVJldHVybkNoYXJ0ICYmIGRhdGEpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aOpeaUtuWIsOeahEZhY3RvcnlSZXR1cm5DaGFydOaVsOaNrjonLCBkYXRhKTsNCiAgICAgICAgY29uc29sZS5sb2coJ2ZhY3RvcnlSZXR1cm5NYXA6JywgZGF0YS5mYWN0b3J5UmV0dXJuTWFwKTsNCg0KICAgICAgICAvLyDlpITnkIblkITliIbljoLpgIDotKfmlbDmja7vvIzljZXkvY3kuLrlhYMNCiAgICAgICAgY29uc3QgeEF4aXNEYXRhID0gW107ICAgICAgLy8geOi9tOWIhuWOguWQjeensOaVsOaNrg0KICAgICAgICBjb25zdCBzZXJpZXNEYXRhID0gW107ICAgICAvLyDmn7Hnirblm77mlbDmja4NCiAgICAgICAgY29uc3QgY29sb3JzID0gWycjOTNDNUZEJywgJyM4NkVGQUMnLCAnI0ZERTY4QScsICcjRkNBNUE1JywgJyNDNEI1RkQnLCAnI0YzRThGRicsICcjN0REM0ZDJywgJyNGOUE4RDQnLCAnI0JFRjI2NCcsICcjQTc4QkZBJywgJyNGNTlFMEInLCAnIzEwQjk4MScsICcjRUY0NDQ0JywgJyM4QjVDRjYnLCAnI0VDNDg5OScsICcjMDZCNkQ0JywgJyM4NENDMTYnLCAnI0Y5NzMxNicsICcjNjM2NkYxJywgJyMxNEI4QTYnXTsNCg0KICAgICAgICBpZiAoZGF0YS5mYWN0b3J5UmV0dXJuTWFwKSB7DQogICAgICAgICAgLy8g5bCGZmFjdG9yeVJldHVybk1hcOWvueixoei9rOaNouS4uuaVsOe7hO+8jOWNleS9jeS4uuWFg++8jOS/neeVmeS4pOS9jeWwj+aVsA0KICAgICAgICAgIGNvbnN0IGRhdGFJdGVtcyA9IE9iamVjdC5lbnRyaWVzKGRhdGEuZmFjdG9yeVJldHVybk1hcCkubWFwKChba2V5LCB2YWx1ZV0pID0+ICh7DQogICAgICAgICAgICBuYW1lOiBrZXksICAgIC8vIOWIhuWOguWQjeensA0KICAgICAgICAgICAgdmFsdWU6IChOdW1iZXIodmFsdWUpIHx8IDApLnRvRml4ZWQoMikgIC8vIOmAgOi0p+mHkemine+8jOWNleS9jeS4uuWFg++8jOS/neeVmeS4pOS9jeWwj+aVsA0KICAgICAgICAgIH0pKTsNCg0KICAgICAgICAgIGNvbnNvbGUubG9nKCflpITnkIblkI7nmoTliIbljoLpgIDotKfmlbDmja46JywgZGF0YUl0ZW1zKTsNCg0KICAgICAgICAgIGlmIChkYXRhSXRlbXMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgLy8g5oyJ5pWw5YC85LuO6auY5Yiw5L2O5o6S5bqPDQogICAgICAgICAgICBkYXRhSXRlbXMuc29ydCgoYSwgYikgPT4gYi52YWx1ZSAtIGEudmFsdWUpOw0KDQogICAgICAgICAgICAvLyDliIbnprvmjpLluo/lkI7nmoTmlbDmja7vvIznoa7kv53mr4/kuKrmn7HlrZDpopzoibLkuI3lkIwNCiAgICAgICAgICAgIGRhdGFJdGVtcy5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgICB4QXhpc0RhdGEucHVzaChpdGVtLm5hbWUpOw0KICAgICAgICAgICAgICBzZXJpZXNEYXRhLnB1c2goew0KICAgICAgICAgICAgICAgIHZhbHVlOiBpdGVtLnZhbHVlLA0KICAgICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogY29sb3JzW2luZGV4ICUgY29sb3JzLmxlbmd0aF0gfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBjb25zb2xlLndhcm4oJ+ayoeacieaJvuWIsOacieaViOeahOWIhuWOgumAgOi0p+aVsOaNricpOw0KICAgICAgICAgICAgLy8g5re75Yqg6buY6K6k5pWw5o2u5Lul5L6/5rWL6K+VDQogICAgICAgICAgICB4QXhpc0RhdGEucHVzaCgn5peg5pWw5o2uJyk7DQogICAgICAgICAgICBzZXJpZXNEYXRhLnB1c2goew0KICAgICAgICAgICAgICB2YWx1ZTogMCwNCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiBjb2xvcnNbMF0gfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUud2FybignZmFjdG9yeVJldHVybk1hcOaVsOaNruS4jeWtmOWcqCcpOw0KICAgICAgICAgIC8vIOa3u+WKoOm7mOiupOaVsOaNruS7peS+v+a1i+ivlQ0KICAgICAgICAgIHhBeGlzRGF0YS5wdXNoKCfml6DmlbDmja4nKTsNCiAgICAgICAgICBzZXJpZXNEYXRhLnB1c2goew0KICAgICAgICAgICAgdmFsdWU6IDAsDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6IGNvbG9yc1swXSB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygneOi9tOWIhuWOguaVsOaNrjonLCB4QXhpc0RhdGEpOw0KICAgICAgICBjb25zb2xlLmxvZygn5p+x54q25Zu+5pWw5o2uOicsIHNlcmllc0RhdGEpOw0KDQogICAgICAgIC8vIOabtOaWsOWbvuihqOmFjee9rg0KICAgICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgICAgZ3JpZDogeyBsZWZ0OiAnOCUnLCByaWdodDogJzQlJywgYm90dG9tOiAnMyUnLCBjb250YWluTGFiZWw6IHRydWUgfSwNCiAgICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzkzQzVGRCcsDQogICAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiAocGFyYW1zKSB7DQogICAgICAgICAgICAgIGxldCByZXN1bHQgPSBwYXJhbXNbMF0ubmFtZSArICc8YnIvPic7DQogICAgICAgICAgICAgIHBhcmFtcy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgICAgY29uc3QgZm9ybWF0dGVkVmFsdWUgPSBwYXJzZUZsb2F0KGl0ZW0udmFsdWUpLnRvRml4ZWQoMikudG9TdHJpbmcoKS5yZXBsYWNlKC9cQig/PShcZHszfSkrKD8hXGQpKS9nLCAnLCcpOw0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSBpdGVtLm1hcmtlciArICcgJyArIGl0ZW0uc2VyaWVzTmFtZSArICc6ICcgKyBmb3JtYXR0ZWRWYWx1ZSArICflhYM8YnIvPic7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0Ow0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgICBkYXRhOiB4QXhpc0RhdGEsDQogICAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjOUNBM0FGJywNCiAgICAgICAgICAgICAgaW50ZXJ2YWw6IDAsIC8vIOaYvuekuuaJgOacieagh+etvg0KICAgICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicgLy8g5bGF5Lit5a+56b2QDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB5QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIG5hbWU6ICfph5Hpop0gKOWFgyknLA0KICAgICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjOUNBM0FGJywNCiAgICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbih2YWx1ZSkgew0KICAgICAgICAgICAgICAgIC8vIOWcqFnovbTmoIfnrb7kuIrkuZ/mmL7npLrljYPliIbkvY3liIbpmpTnrKYNCiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUudG9TdHJpbmcoKS5yZXBsYWNlKC9cQig/PShcZHszfSkrKD8hXGQpKS9nLCAnLCcpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9LA0KICAgICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbew0KICAgICAgICAgICAgbmFtZTogJ+mAgOi0p+mHkeminScsDQogICAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICAgIGRhdGE6IHNlcmllc0RhdGENCiAgICAgICAgICB9XQ0KICAgICAgICB9Ow0KDQogICAgICAgIHRoaXMuY2hhcnRzLmZhY3RvcnlSZXR1cm5DaGFydC5zZXRPcHRpb24ob3B0aW9uLCB0cnVlKTsgLy8g5L2/55SodHJ1ZeW8uuWItuWIt+aWsA0KICAgICAgICBjb25zb2xlLmxvZygnRmFjdG9yeVJldHVybkNoYXJ05p+x54q25Zu+5pWw5o2u5bey5pu05pawJyk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zb2xlLmVycm9yKCdGYWN0b3J5UmV0dXJuQ2hhcnTlrp7kvovkuI3lrZjlnKjmiJbmlbDmja7kuLrnqbonKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgdXBkYXRlRmFjdG9yeVJlamVjdGlvbkNoYXJ0KGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmNoYXJ0cy5mYWN0b3J5UmVqZWN0aW9uQ2hhcnQgJiYgZGF0YSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5o6l5pS25Yiw55qERmFjdG9yeVJlamVjdGlvbkNoYXJ05pWw5o2uOicsIGRhdGEpOw0KICAgICAgICBjb25zb2xlLmxvZygnZmFjdG9yeVJlamVjdGlvbk1hcDonLCBkYXRhLmZhY3RvcnlSZWplY3Rpb25NYXApOw0KDQogICAgICAgIC8vIOWkhOeQhuWQhOWIhuWOguaUueWIpOaVsOaNru+8jOWNleS9jeS4uuWFgw0KICAgICAgICBjb25zdCB4QXhpc0RhdGEgPSBbXTsgICAgICAvLyB46L205YiG5Y6C5ZCN56ew5pWw5o2uDQogICAgICAgIGNvbnN0IHNlcmllc0RhdGEgPSBbXTsgICAgIC8vIOafseeKtuWbvuaVsOaNrg0KICAgICAgICBjb25zdCBjb2xvcnMgPSBbJyM5M0M1RkQnLCAnIzg2RUZBQycsICcjRkRFNjhBJywgJyNGQ0E1QTUnLCAnI0M0QjVGRCcsICcjRjNFOEZGJywgJyM3REQzRkMnLCAnI0Y5QThENCcsICcjQkVGMjY0JywgJyNBNzhCRkEnLCAnI0Y1OUUwQicsICcjMTBCOTgxJywgJyNFRjQ0NDQnLCAnIzhCNUNGNicsICcjRUM0ODk5JywgJyMwNkI2RDQnLCAnIzg0Q0MxNicsICcjRjk3MzE2JywgJyM2MzY2RjEnLCAnIzE0QjhBNiddOw0KDQogICAgICAgIGlmIChkYXRhLmZhY3RvcnlSZWplY3Rpb25NYXApIHsNCiAgICAgICAgICAvLyDlsIZmYWN0b3J5UmVqZWN0aW9uTWFw5a+56LGh6L2s5o2i5Li65pWw57uE77yM5Y2V5L2N5Li65YWD77yM5L+d55WZ5Lik5L2N5bCP5pWwDQogICAgICAgICAgY29uc3QgZGF0YUl0ZW1zID0gT2JqZWN0LmVudHJpZXMoZGF0YS5mYWN0b3J5UmVqZWN0aW9uTWFwKS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgIG5hbWU6IGtleSwgICAgLy8g5YiG5Y6C5ZCN56ewDQogICAgICAgICAgICB2YWx1ZTogKE51bWJlcih2YWx1ZSkgfHwgMCkudG9GaXhlZCgyKSAgLy8g5pS55Yik6YeR6aKd77yM5Y2V5L2N5Li65YWD77yM5L+d55WZ5Lik5L2N5bCP5pWwDQogICAgICAgICAgfSkpOw0KDQogICAgICAgICAgY29uc29sZS5sb2coJ+WkhOeQhuWQjueahOWIhuWOguaUueWIpOaVsOaNrjonLCBkYXRhSXRlbXMpOw0KDQogICAgICAgICAgaWYgKGRhdGFJdGVtcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAvLyDmjInmlbDlgLzku47pq5jliLDkvY7mjpLluo8NCiAgICAgICAgICAgIGRhdGFJdGVtcy5zb3J0KChhLCBiKSA9PiBiLnZhbHVlIC0gYS52YWx1ZSk7DQoNCiAgICAgICAgICAgIC8vIOWIhuemu+aOkuW6j+WQjueahOaVsOaNru+8jOehruS/neavj+S4quafseWtkOminOiJsuS4jeWQjA0KICAgICAgICAgICAgZGF0YUl0ZW1zLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgICAgICAgIHhBeGlzRGF0YS5wdXNoKGl0ZW0ubmFtZSk7DQogICAgICAgICAgICAgIHNlcmllc0RhdGEucHVzaCh7DQogICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0udmFsdWUsDQogICAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiBjb2xvcnNbaW5kZXggJSBjb2xvcnMubGVuZ3RoXSB9DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGNvbnNvbGUud2Fybign5rKh5pyJ5om+5Yiw5pyJ5pWI55qE5YiG5Y6C5pS55Yik5pWw5o2uJyk7DQogICAgICAgICAgICAvLyDmt7vliqDpu5jorqTmlbDmja7ku6Xkvr/mtYvor5UNCiAgICAgICAgICAgIHhBeGlzRGF0YS5wdXNoKCfml6DmlbDmja4nKTsNCiAgICAgICAgICAgIHNlcmllc0RhdGEucHVzaCh7DQogICAgICAgICAgICAgIHZhbHVlOiAwLA0KICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6IGNvbG9yc1swXSB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCdmYWN0b3J5UmVqZWN0aW9uTWFw5pWw5o2u5LiN5a2Y5ZyoJyk7DQogICAgICAgICAgLy8g5re75Yqg6buY6K6k5pWw5o2u5Lul5L6/5rWL6K+VDQogICAgICAgICAgeEF4aXNEYXRhLnB1c2goJ+aXoOaVsOaNricpOw0KICAgICAgICAgIHNlcmllc0RhdGEucHVzaCh7DQogICAgICAgICAgICB2YWx1ZTogMCwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogY29sb3JzWzBdIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCd46L205YiG5Y6C5pWw5o2uOicsIHhBeGlzRGF0YSk7DQogICAgICAgIGNvbnNvbGUubG9nKCfmn7Hnirblm77mlbDmja46Jywgc2VyaWVzRGF0YSk7DQoNCiAgICAgICAgLy8g5pu05paw5Zu+6KGo6YWN572uDQogICAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgICBncmlkOiB7IGxlZnQ6ICc4JScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICAgIGF4aXNQb2ludGVyOiB7IHR5cGU6ICdzaGFkb3cnIH0sDQogICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDMwLCA0MSwgNTksIDAuOSknLA0KICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0sDQogICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uIChwYXJhbXMpIHsNCiAgICAgICAgICAgICAgbGV0IHJlc3VsdCA9IHBhcmFtc1swXS5uYW1lICsgJzxici8+JzsNCiAgICAgICAgICAgICAgcGFyYW1zLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICAgICAgICBjb25zdCBmb3JtYXR0ZWRWYWx1ZSA9IHBhcnNlRmxvYXQoaXRlbS52YWx1ZSkudG9GaXhlZCgyKS50b1N0cmluZygpLnJlcGxhY2UoL1xCKD89KFxkezN9KSsoPyFcZCkpL2csICcsJyk7DQogICAgICAgICAgICAgICAgcmVzdWx0ICs9IGl0ZW0ubWFya2VyICsgJyAnICsgaXRlbS5zZXJpZXNOYW1lICsgJzogJyArIGZvcm1hdHRlZFZhbHVlICsgJ+WFgzxici8+JzsNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB4QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICAgIGRhdGE6IHhBeGlzRGF0YSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnLA0KICAgICAgICAgICAgICBpbnRlcnZhbDogMCwgLy8g5pi+56S65omA5pyJ5qCH562+DQogICAgICAgICAgICAgIHJvdGF0ZTogMCwgLy8g5rC05bmz5pi+56S65qCH562+DQogICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnLA0KICAgICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uKHZhbHVlKSB7DQogICAgICAgICAgICAgICAgLy8g5ZyoWei9tOagh+etvuS4iuS5n+aYvuekuuWNg+WIhuS9jeWIhumalOespg0KICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZS50b1N0cmluZygpLnJlcGxhY2UoL1xCKD89KFxkezN9KSsoPyFcZCkpL2csICcsJyk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgICBuYW1lOiAn5pS55Yik6YeR6aKdJywNCiAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgZGF0YTogc2VyaWVzRGF0YQ0KICAgICAgICAgIH1dDQogICAgICAgIH07DQoNCiAgICAgICAgdGhpcy5jaGFydHMuZmFjdG9yeVJlamVjdGlvbkNoYXJ0LnNldE9wdGlvbihvcHRpb24sIHRydWUpOyAvLyDkvb/nlKh0cnVl5by65Yi25Yi35pawDQogICAgICAgIGNvbnNvbGUubG9nKCdGYWN0b3J5UmVqZWN0aW9uQ2hhcnTmn7Hnirblm77mlbDmja7lt7Lmm7TmlrAnKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhY3RvcnlSZWplY3Rpb25DaGFydOWunuS+i+S4jeWtmOWcqOaIluaVsOaNruS4uuepuicpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICB1cGRhdGVGYWN0b3J5U2NyYXBDaGFydChkYXRhKSB7DQogICAgICBpZiAodGhpcy5jaGFydHMuZmFjdG9yeVNjcmFwQ2hhcnQgJiYgZGF0YSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5o6l5pS25Yiw55qERmFjdG9yeVNjcmFwQ2hhcnTmlbDmja46JywgZGF0YSk7DQogICAgICAgIGNvbnNvbGUubG9nKCdmYWN0b3J5U2NyYXBNYXA6JywgZGF0YS5mYWN0b3J5U2NyYXBNYXApOw0KDQogICAgICAgIC8vIOWkhOeQhuWQhOWIhuWOguaKpeW6n+aVsOaNru+8jOWNleS9jeS4uuWFgw0KICAgICAgICBjb25zdCB4QXhpc0RhdGEgPSBbXTsgICAgICAvLyB46L205YiG5Y6C5ZCN56ew5pWw5o2uDQogICAgICAgIGNvbnN0IHNlcmllc0RhdGEgPSBbXTsgICAgIC8vIOafseeKtuWbvuaVsOaNrg0KICAgICAgICBjb25zdCBjb2xvcnMgPSBbJyM5M0M1RkQnLCAnIzg2RUZBQycsICcjRkRFNjhBJywgJyNGQ0E1QTUnLCAnI0M0QjVGRCcsICcjRjNFOEZGJywgJyM3REQzRkMnLCAnI0Y5QThENCcsICcjQkVGMjY0JywgJyNBNzhCRkEnLCAnI0Y1OUUwQicsICcjMTBCOTgxJywgJyNFRjQ0NDQnLCAnIzhCNUNGNicsICcjRUM0ODk5JywgJyMwNkI2RDQnLCAnIzg0Q0MxNicsICcjRjk3MzE2JywgJyM2MzY2RjEnLCAnIzE0QjhBNiddOw0KDQogICAgICAgIGlmIChkYXRhLmZhY3RvcnlTY3JhcE1hcCkgew0KICAgICAgICAgIC8vIOWwhmZhY3RvcnlTY3JhcE1hcOWvueixoei9rOaNouS4uuaVsOe7hO+8jOWNleS9jeS4uuWFg++8jOS/neeVmeS4pOS9jeWwj+aVsA0KICAgICAgICAgIGNvbnN0IGRhdGFJdGVtcyA9IE9iamVjdC5lbnRyaWVzKGRhdGEuZmFjdG9yeVNjcmFwTWFwKS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgIG5hbWU6IGtleSwgICAgLy8g5YiG5Y6C5ZCN56ewDQogICAgICAgICAgICB2YWx1ZTogKE51bWJlcih2YWx1ZSkgfHwgMCkudG9GaXhlZCgyKSAgLy8g5oql5bqf6YeR6aKd77yM5Y2V5L2N5Li65YWD77yM5L+d55WZ5Lik5L2N5bCP5pWwDQogICAgICAgICAgfSkpOw0KDQogICAgICAgICAgY29uc29sZS5sb2coJ+WkhOeQhuWQjueahOWIhuWOguaKpeW6n+aVsOaNrjonLCBkYXRhSXRlbXMpOw0KDQogICAgICAgICAgaWYgKGRhdGFJdGVtcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAvLyDmjInmlbDlgLzku47pq5jliLDkvY7mjpLluo8NCiAgICAgICAgICAgIGRhdGFJdGVtcy5zb3J0KChhLCBiKSA9PiBiLnZhbHVlIC0gYS52YWx1ZSk7DQoNCiAgICAgICAgICAgIC8vIOWIhuemu+aOkuW6j+WQjueahOaVsOaNru+8jOehruS/neavj+S4quafseWtkOminOiJsuS4jeWQjA0KICAgICAgICAgICAgZGF0YUl0ZW1zLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgICAgICAgIHhBeGlzRGF0YS5wdXNoKGl0ZW0ubmFtZSk7DQogICAgICAgICAgICAgIHNlcmllc0RhdGEucHVzaCh7DQogICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0udmFsdWUsDQogICAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiBjb2xvcnNbaW5kZXggJSBjb2xvcnMubGVuZ3RoXSB9DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGNvbnNvbGUud2Fybign5rKh5pyJ5om+5Yiw5pyJ5pWI55qE5YiG5Y6C5oql5bqf5pWw5o2uJyk7DQogICAgICAgICAgICAvLyDmt7vliqDpu5jorqTmlbDmja7ku6Xkvr/mtYvor5UNCiAgICAgICAgICAgIHhBeGlzRGF0YS5wdXNoKCfml6DmlbDmja4nKTsNCiAgICAgICAgICAgIHNlcmllc0RhdGEucHVzaCh7DQogICAgICAgICAgICAgIHZhbHVlOiAwLA0KICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6IGNvbG9yc1swXSB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCdmYWN0b3J5U2NyYXBNYXDmlbDmja7kuI3lrZjlnKgnKTsNCiAgICAgICAgICAvLyDmt7vliqDpu5jorqTmlbDmja7ku6Xkvr/mtYvor5UNCiAgICAgICAgICB4QXhpc0RhdGEucHVzaCgn5peg5pWw5o2uJyk7DQogICAgICAgICAgc2VyaWVzRGF0YS5wdXNoKHsNCiAgICAgICAgICAgIHZhbHVlOiAwLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiBjb2xvcnNbMF0gfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ3jovbTliIbljoLmlbDmja46JywgeEF4aXNEYXRhKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+afseeKtuWbvuaVsOaNrjonLCBzZXJpZXNEYXRhKTsNCg0KICAgICAgICAvLyDmm7TmlrDlm77ooajphY3nva4NCiAgICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICAgIGdyaWQ6IHsgbGVmdDogJzglJywgcmlnaHQ6ICc0JScsIGJvdHRvbTogJzMlJywgY29udGFpbkxhYmVsOiB0cnVlIH0sDQogICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgICAgYXhpc1BvaW50ZXI6IHsgdHlwZTogJ3NoYWRvdycgfSwNCiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI2ZmZicgfSwNCiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gKHBhcmFtcykgew0KICAgICAgICAgICAgICBsZXQgcmVzdWx0ID0gcGFyYW1zWzBdLm5hbWUgKyAnPGJyLz4nOw0KICAgICAgICAgICAgICBwYXJhbXMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZFZhbHVlID0gcGFyc2VGbG9hdChpdGVtLnZhbHVlKS50b0ZpeGVkKDIpLnRvU3RyaW5nKCkucmVwbGFjZSgvXEIoPz0oXGR7M30pKyg/IVxkKSkvZywgJywnKTsNCiAgICAgICAgICAgICAgICByZXN1bHQgKz0gaXRlbS5tYXJrZXIgKyAnICcgKyBpdGVtLnNlcmllc05hbWUgKyAnOiAnICsgZm9ybWF0dGVkVmFsdWUgKyAn5YWDPGJyLz4nOw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgICAgZGF0YTogeEF4aXNEYXRhLA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicsDQogICAgICAgICAgICAgIGludGVydmFsOiAwLCAvLyDmmL7npLrmiYDmnInmoIfnrb4NCiAgICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInIC8vIOWxheS4reWvuem9kA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgICBuYW1lOiAn6YeR6aKdICjlhYMpJywNCiAgICAgICAgICAgIG5hbWVUZXh0U3R5bGU6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicsDQogICAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24odmFsdWUpIHsNCiAgICAgICAgICAgICAgICAvLyDlnKhZ6L205qCH562+5LiK5Lmf5pi+56S65Y2D5YiG5L2N5YiG6ZqU56ymDQogICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlLnRvU3RyaW5nKCkucmVwbGFjZSgvXEIoPz0oXGR7M30pKyg/IVxkKSkvZywgJywnKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfSwNCiAgICAgICAgICAgIHNwbGl0TGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjMzc0MTUxJyB9IH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICAgIG5hbWU6ICfmiqXlup/ph5Hpop0nLA0KICAgICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgICBkYXRhOiBzZXJpZXNEYXRhDQogICAgICAgICAgfV0NCiAgICAgICAgfTsNCg0KICAgICAgICB0aGlzLmNoYXJ0cy5mYWN0b3J5U2NyYXBDaGFydC5zZXRPcHRpb24ob3B0aW9uLCB0cnVlKTsgLy8g5L2/55SodHJ1ZeW8uuWItuWIt+aWsA0KICAgICAgICBjb25zb2xlLmxvZygnRmFjdG9yeVNjcmFwQ2hhcnTmn7Hnirblm77mlbDmja7lt7Lmm7TmlrAnKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhY3RvcnlTY3JhcENoYXJ05a6e5L6L5LiN5a2Y5Zyo5oiW5pWw5o2u5Li656m6Jyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIHVwZGF0ZUZhY3RvcnlDb250cmFjdENoYXJ0KGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmNoYXJ0cy5mYWN0b3J5Q29udHJhY3RDaGFydCAmJiBkYXRhKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmjqXmlLbliLDnmoRGYWN0b3J5Q29udHJhY3RDaGFydOaVsOaNrjonLCBkYXRhKTsNCiAgICAgICAgY29uc29sZS5sb2coJ2ZhY3RvcnlDb250cmFjdE1hcDonLCBkYXRhLmZhY3RvcnlDb250cmFjdE1hcCk7DQoNCiAgICAgICAgLy8g5aSE55CG5ZCE5YiG5Y6C6ISx5ZCI5ZCM5pWw5o2u77yM5Y2V5L2N5Li65YWDDQogICAgICAgIGNvbnN0IHhBeGlzRGF0YSA9IFtdOyAgICAgIC8vIHjovbTliIbljoLlkI3np7DmlbDmja4NCiAgICAgICAgY29uc3Qgc2VyaWVzRGF0YSA9IFtdOyAgICAgLy8g5p+x54q25Zu+5pWw5o2uDQogICAgICAgIGNvbnN0IGNvbG9ycyA9IFsnIzkzQzVGRCcsICcjODZFRkFDJywgJyNGREU2OEEnLCAnI0ZDQTVBNScsICcjQzRCNUZEJywgJyNGM0U4RkYnLCAnIzdERDNGQycsICcjRjlBOEQ0JywgJyNCRUYyNjQnLCAnI0E3OEJGQScsICcjRjU5RTBCJywgJyMxMEI5ODEnLCAnI0VGNDQ0NCcsICcjOEI1Q0Y2JywgJyNFQzQ4OTknLCAnIzA2QjZENCcsICcjODRDQzE2JywgJyNGOTczMTYnLCAnIzYzNjZGMScsICcjMTRCOEE2J107DQoNCiAgICAgICAgaWYgKGRhdGEuZmFjdG9yeUNvbnRyYWN0TWFwKSB7DQogICAgICAgICAgLy8g5bCGZmFjdG9yeUNvbnRyYWN0TWFw5a+56LGh6L2s5o2i5Li65pWw57uE77yM5Y2V5L2N5Li65YWD77yM5L+d55WZ5Lik5L2N5bCP5pWwDQogICAgICAgICAgY29uc3QgZGF0YUl0ZW1zID0gT2JqZWN0LmVudHJpZXMoZGF0YS5mYWN0b3J5Q29udHJhY3RNYXApLm1hcCgoW2tleSwgdmFsdWVdKSA9PiAoew0KICAgICAgICAgICAgbmFtZToga2V5LCAgICAvLyDliIbljoLlkI3np7ANCiAgICAgICAgICAgIHZhbHVlOiAoTnVtYmVyKHZhbHVlKSB8fCAwKS50b0ZpeGVkKDIpICAvLyDohLHlkIjlkIzph5Hpop3vvIzljZXkvY3kuLrlhYPvvIzkv53nlZnkuKTkvY3lsI/mlbANCiAgICAgICAgICB9KSk7DQoNCiAgICAgICAgICBjb25zb2xlLmxvZygn5aSE55CG5ZCO55qE5YiG5Y6C6ISx5ZCI5ZCM5pWw5o2uOicsIGRhdGFJdGVtcyk7DQoNCiAgICAgICAgICBpZiAoZGF0YUl0ZW1zLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIC8vIOaMieaVsOWAvOS7jumrmOWIsOS9juaOkuW6jw0KICAgICAgICAgICAgZGF0YUl0ZW1zLnNvcnQoKGEsIGIpID0+IGIudmFsdWUgLSBhLnZhbHVlKTsNCg0KICAgICAgICAgICAgLy8g5YiG56a75o6S5bqP5ZCO55qE5pWw5o2u77yM56Gu5L+d5q+P5Liq5p+x5a2Q6aKc6Imy5LiN5ZCMDQogICAgICAgICAgICBkYXRhSXRlbXMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgICAgeEF4aXNEYXRhLnB1c2goaXRlbS5uYW1lKTsNCiAgICAgICAgICAgICAgc2VyaWVzRGF0YS5wdXNoKHsNCiAgICAgICAgICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZSwNCiAgICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6IGNvbG9yc1tpbmRleCAlIGNvbG9ycy5sZW5ndGhdIH0NCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY29uc29sZS53YXJuKCfmsqHmnInmib7liLDmnInmlYjnmoTliIbljoLohLHlkIjlkIzmlbDmja4nKTsNCiAgICAgICAgICAgIC8vIOa3u+WKoOm7mOiupOaVsOaNruS7peS+v+a1i+ivlQ0KICAgICAgICAgICAgeEF4aXNEYXRhLnB1c2goJ+aXoOaVsOaNricpOw0KICAgICAgICAgICAgc2VyaWVzRGF0YS5wdXNoKHsNCiAgICAgICAgICAgICAgdmFsdWU6IDAsDQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogY29sb3JzWzBdIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oJ2ZhY3RvcnlDb250cmFjdE1hcOaVsOaNruS4jeWtmOWcqCcpOw0KICAgICAgICAgIC8vIOa3u+WKoOm7mOiupOaVsOaNruS7peS+v+a1i+ivlQ0KICAgICAgICAgIHhBeGlzRGF0YS5wdXNoKCfml6DmlbDmja4nKTsNCiAgICAgICAgICBzZXJpZXNEYXRhLnB1c2goew0KICAgICAgICAgICAgdmFsdWU6IDAsDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6IGNvbG9yc1swXSB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygneOi9tOWIhuWOguaVsOaNrjonLCB4QXhpc0RhdGEpOw0KICAgICAgICBjb25zb2xlLmxvZygn5p+x54q25Zu+5pWw5o2uOicsIHNlcmllc0RhdGEpOw0KDQogICAgICAgIC8vIOabtOaWsOWbvuihqOmFjee9rg0KICAgICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgICAgZ3JpZDogeyBsZWZ0OiAnOCUnLCByaWdodDogJzQlJywgYm90dG9tOiAnMyUnLCBjb250YWluTGFiZWw6IHRydWUgfSwNCiAgICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzkzQzVGRCcsDQogICAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiAocGFyYW1zKSB7DQogICAgICAgICAgICAgIGxldCByZXN1bHQgPSBwYXJhbXNbMF0ubmFtZSArICc8YnIvPic7DQogICAgICAgICAgICAgIHBhcmFtcy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgICAgY29uc3QgZm9ybWF0dGVkVmFsdWUgPSBwYXJzZUZsb2F0KGl0ZW0udmFsdWUpLnRvRml4ZWQoMikudG9TdHJpbmcoKS5yZXBsYWNlKC9cQig/PShcZHszfSkrKD8hXGQpKS9nLCAnLCcpOw0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSBpdGVtLm1hcmtlciArICcgJyArIGl0ZW0uc2VyaWVzTmFtZSArICc6ICcgKyBmb3JtYXR0ZWRWYWx1ZSArICflhYM8YnIvPic7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0Ow0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgICBkYXRhOiB4QXhpc0RhdGEsDQogICAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjOUNBM0FGJywNCiAgICAgICAgICAgICAgaW50ZXJ2YWw6IDAsIC8vIOaYvuekuuaJgOacieagh+etvg0KICAgICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicgLy8g5bGF5Lit5a+56b2QDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB5QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIG5hbWU6ICfph5Hpop0gKOWFgyknLA0KICAgICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjOUNBM0FGJywNCiAgICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbih2YWx1ZSkgew0KICAgICAgICAgICAgICAgIC8vIOWcqFnovbTmoIfnrb7kuIrkuZ/mmL7npLrljYPliIbkvY3liIbpmpTnrKYNCiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUudG9TdHJpbmcoKS5yZXBsYWNlKC9cQig/PShcZHszfSkrKD8hXGQpKS9nLCAnLCcpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9LA0KICAgICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbew0KICAgICAgICAgICAgbmFtZTogJ+iEseWQiOWQjOmHkeminScsDQogICAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICAgIGRhdGE6IHNlcmllc0RhdGENCiAgICAgICAgICB9XQ0KICAgICAgICB9Ow0KDQogICAgICAgIHRoaXMuY2hhcnRzLmZhY3RvcnlDb250cmFjdENoYXJ0LnNldE9wdGlvbihvcHRpb24sIHRydWUpOyAvLyDkvb/nlKh0cnVl5by65Yi25Yi35pawDQogICAgICAgIGNvbnNvbGUubG9nKCdGYWN0b3J5Q29udHJhY3RDaGFydOafseeKtuWbvuaVsOaNruW3suabtOaWsCcpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY29uc29sZS5lcnJvcignRmFjdG9yeUNvbnRyYWN0Q2hhcnTlrp7kvovkuI3lrZjlnKjmiJbmlbDmja7kuLrnqbonKTsNCiAgICAgIH0NCiAgICB9LA0KDQoNCiAgICBnZXRXYXRlcmZhbGxDaGFydERldGFpbCgpIHsNCiAgICAgIC8vIOWPquacieW9k+aIkOacrOS4reW/g+WSjOS8muiuoeacn+mDveacieWAvOaXtuaJjeivt+axgg0KICAgICAgaWYgKCF0aGlzLmNvc3RDZW50ZXIgfHwgIXRoaXMuYWNjb3VudGluZ1BlcmlvZCB8fCAodGhpcy5jb250YWluVHlwZSAhPT0gMiAmJiB0aGlzLmNvbnRhaW5UeXBlICE9PSAxKSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5oiQ5pys5Lit5b+D44CB5Lya6K6h5pyf5oiW6LSo6YeP5oiQ5pys57G75Z6L5Li656m677yM6Lez6L+H5pWw5o2u6K+35rGCJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICBjb3N0Q2VudGVyOiB0aGlzLmNvc3RDZW50ZXIsDQogICAgICAgIHllYXJNb250aDogdGhpcy5hY2NvdW50aW5nUGVyaW9kLnJlcGxhY2UoJy0nLCAnJyksIC8vIOWwhiAyMDI1LTA2IOi9rOaNouS4uiAyMDI1MDYNCiAgICAgICAgY29udGFpblR5cGU6IHRoaXMuY29udGFpblR5cGUNCiAgICAgIH07DQoNCiAgICAgIGdldFdhdGVyZmFsbENoYXJ0RGV0YWlsKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCdnZXRXYXRlcmZhbGxDaGFydERldGFpbDonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgLy8g5pu05pawV2F0ZXJmYWxsQ2hhcnTmn7Hnirblm74NCiAgICAgICAgICB0aGlzLnVwZGF0ZVdhdGVyZmFsbENoYXJ0KHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlldhdGVyZmFsbENoYXJ05pWw5o2u5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+WV2F0ZXJmYWxsQ2hhcnTmlbDmja7lpLHotKUnKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDmm7TmlrBXYXRlcmZhbGxDaGFydOafseeKtuWbvg0KICAgIHVwZGF0ZVdhdGVyZmFsbENoYXJ0KGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmNoYXJ0cy53YXRlcmZhbGxDaGFydCAmJiBkYXRhKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmjqXmlLbliLDnmoRXYXRlcmZhbGxDaGFydOaVsOaNrjonLCBkYXRhKTsNCg0KICAgICAgICAvLyDlpITnkIZyZXNjdWVQcm9qZWN05pWw5o2uDQogICAgICAgIGNvbnN0IHhBeGlzRGF0YSA9IFtdOyAgICAgIC8vIHjovbTnu7TluqbmlbDmja4NCiAgICAgICAgY29uc3Qgc2VyaWVzRGF0YSA9IFtdOyAgICAgLy8g5p+x54q25Zu+5pWw5o2uDQogICAgICAgIGNvbnN0IGNvbG9ycyA9IFsnIzkzQzVGRCcsICcjODZFRkFDJywgJyNGREU2OEEnLCAnI0ZDQTVBNScsICcjQzRCNUZEJywgJyNGM0U4RkYnXTsNCg0KICAgICAgICBsZXQgZGF0YUl0ZW1zID0gW107DQoNCiAgICAgICAgaWYgKGRhdGEucmVzY3VlUHJvamVjdCkgew0KICAgICAgICAgIC8vIOWwhnJlc2N1ZVByb2plY3Tlr7nosaHovazmjaLkuLrmlbDnu4TvvIzovazmjaLkuLrkuIflhYMNCiAgICAgICAgICBkYXRhSXRlbXMgPSBPYmplY3QuZW50cmllcyhkYXRhLnJlc2N1ZVByb2plY3QpLm1hcCgoW2tleSwgdmFsdWVdKSA9PiAoew0KICAgICAgICAgICAgbmFtZToga2V5LCAgICAvLyDnrKzkuIDpobnkuLrnu7TluqblkI3np7ANCiAgICAgICAgICAgIHZhbHVlOiAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKSAgLy8g56ys5LqM6aG55Li65a+55bqU57u05bqm55qE5YC877yM6L2s5o2i5Li65LiH5YWDDQogICAgICAgICAgfSkpOw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ+WkhOeQhuWQjueahOaVsOaNrumhuTonLCBkYXRhSXRlbXMpOw0KDQogICAgICAgIGlmIChkYXRhSXRlbXMubGVuZ3RoID4gMCkgew0KICAgICAgICAgIC8vIOaMieaVsOWAvOS7jumrmOWIsOS9juaOkuW6jw0KICAgICAgICAgIGRhdGFJdGVtcy5zb3J0KChhLCBiKSA9PiBiLnZhbHVlIC0gYS52YWx1ZSk7DQoNCiAgICAgICAgICAvLyDlj6rlj5bliY3ljYHkuKrmnIDlpKfnmoTmlbDmja4NCiAgICAgICAgICBjb25zdCB0b3BUZW5JdGVtcyA9IGRhdGFJdGVtcy5zbGljZSgwLCAxMCk7DQogICAgICAgICAgY29uc29sZS5sb2coJ+WPluWJjeWNgeS4quacgOWkp+aVsOaNrjonLCB0b3BUZW5JdGVtcyk7DQoNCiAgICAgICAgICAvLyDliIbnprvmjpLluo/lkI7nmoTmlbDmja4NCiAgICAgICAgICB0b3BUZW5JdGVtcy5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICAgICAgeEF4aXNEYXRhLnB1c2goaXRlbS5uYW1lKTsNCiAgICAgICAgICAgIHNlcmllc0RhdGEucHVzaCh7DQogICAgICAgICAgICAgIHZhbHVlOiBpdGVtLnZhbHVlLA0KICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6IGNvbG9yc1tpbmRleCAlIGNvbG9ycy5sZW5ndGhdIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUud2Fybign5rKh5pyJ5om+5Yiw5pyJ5pWI55qE5pWw5o2u6aG5Jyk7DQogICAgICAgICAgLy8g5re75Yqg6buY6K6k5pWw5o2u5Lul5L6/5rWL6K+VDQogICAgICAgICAgeEF4aXNEYXRhLnB1c2goJ+aXoOaVsOaNricpOw0KICAgICAgICAgIHNlcmllc0RhdGEucHVzaCh7DQogICAgICAgICAgICB2YWx1ZTogMCwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogY29sb3JzWzBdIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCd46L2057u05bqm5pWw5o2uOicsIHhBeGlzRGF0YSk7DQogICAgICAgIGNvbnNvbGUubG9nKCfmn7Hnirblm77mlbDmja46Jywgc2VyaWVzRGF0YSk7DQoNCiAgICAgICAgLy8g5pu05paw5Zu+6KGo6YWN572uDQogICAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgICBncmlkOiB7IGxlZnQ6ICc4JScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICAgIGF4aXNQb2ludGVyOiB7IHR5cGU6ICdzaGFkb3cnIH0sDQogICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDMwLCA0MSwgNTksIDAuOSknLA0KICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0sDQogICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uIChwYXJhbXMpIHsNCiAgICAgICAgICAgICAgbGV0IHJlc3VsdCA9IHBhcmFtc1swXS5uYW1lICsgJzxici8+JzsNCiAgICAgICAgICAgICAgcGFyYW1zLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsNCiAgICAgICAgICAgICAgICBjb25zdCBmb3JtYXR0ZWRWYWx1ZSA9IHBhcnNlRmxvYXQoaXRlbS52YWx1ZSkudG9GaXhlZCgyKS50b1N0cmluZygpLnJlcGxhY2UoL1xCKD89KFxkezN9KSsoPyFcZCkpL2csICcsJyk7DQogICAgICAgICAgICAgICAgcmVzdWx0ICs9IGl0ZW0ubWFya2VyICsgJyAnICsgaXRlbS5zZXJpZXNOYW1lICsgJzogJyArIGZvcm1hdHRlZFZhbHVlICsgJ+S4h+WFgzxici8+JzsNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB4QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICAgIGRhdGE6IHhBeGlzRGF0YSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnLA0KICAgICAgICAgICAgICBpbnRlcnZhbDogMCwgLy8g5pi+56S65omA5pyJ5qCH562+DQogICAgICAgICAgICAgIHJvdGF0ZTogMCwgLy8g5rC05bmz5pi+56S65qCH562+DQogICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgICAgbmFtZTogJ+mHkeminSAo5LiH5YWDKScsDQogICAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgICBuYW1lOiAn5oy95pWR5aSE55CG5oiQ5pysJywNCiAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgZGF0YTogc2VyaWVzRGF0YQ0KICAgICAgICAgIH1dDQogICAgICAgIH07DQoNCiAgICAgICAgdGhpcy5jaGFydHMud2F0ZXJmYWxsQ2hhcnQuc2V0T3B0aW9uKG9wdGlvbiwgdHJ1ZSk7IC8vIOS9v+eUqHRydWXlvLrliLbliLfmlrANCiAgICAgICAgY29uc29sZS5sb2coJ1dhdGVyZmFsbENoYXJ05p+x54q25Zu+5pWw5o2u5bey5pu05pawJyk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zb2xlLmVycm9yKCdXYXRlcmZhbGxDaGFydOWunuS+i+S4jeWtmOWcqOaIluaVsOaNruS4uuepuicpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmm7TmlrBTY3JhcExvc3NDaGFydOafseeKtuWbvg0KICAgIHVwZGF0ZVNjcmFwTG9zc0NoYXJ0KGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmNoYXJ0cy5zY3JhcExvc3NDaGFydCAmJiBkYXRhKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmjqXmlLbliLDnmoRTY3JhcExvc3NDaGFydOaVsOaNrjonLCBkYXRhKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+aVsOaNruexu+WeizonLCB0eXBlb2YgZGF0YSk7DQogICAgICAgIGNvbnNvbGUubG9nKCfmlbDmja7plK46JywgT2JqZWN0LmtleXMoZGF0YSkpOw0KDQogICAgICAgIC8vIOWkhOeQhuaKpeW6n+aNn+WkseaVsOaNru+8jOWwneivleWkmuenjeWPr+iDveeahOaVsOaNrue7k+aehA0KICAgICAgICBjb25zdCB4QXhpc0RhdGEgPSBbXTsgICAgICAvLyB46L2057u05bqm5pWw5o2uDQogICAgICAgIGNvbnN0IHNlcmllc0RhdGEgPSBbXTsgICAgIC8vIOafseeKtuWbvuaVsOaNrg0KICAgICAgICBjb25zdCBjb2xvcnMgPSBbJyM5M0M1RkQnLCAnIzg2RUZBQycsICcjRkRFNjhBJywgJyNGQ0E1QTUnLCAnI0M0QjVGRCcsICcjRjNFOEZGJ107DQogICAgICAgIGxldCBkYXRhSXRlbXMgPSBbXTsNCg0KICAgICAgICAvLyDlsJ3or5XkuI3lkIznmoTmlbDmja7nu5PmnoTvvIzovazmjaLkuLrkuIflhYMNCiAgICAgICAgaWYgKGRhdGEuc2NyYXBMb3NzTWFwKSB7DQogICAgICAgICAgLy8g5oOF5Ya1MTog5L2/55Soc2NyYXBMb3NzTWFw5pWw5o2u77yI5qC55o2u5a6e6ZmFQVBJ6L+U5Zue55qE5pWw5o2u57uT5p6E77yJDQogICAgICAgICAgY29uc29sZS5sb2coJ+S9v+eUqHNjcmFwTG9zc01hcOaVsOaNricpOw0KICAgICAgICAgIGRhdGFJdGVtcyA9IE9iamVjdC5lbnRyaWVzKGRhdGEuc2NyYXBMb3NzTWFwKS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgIG5hbWU6IGtleSwNCiAgICAgICAgICAgIHZhbHVlOiAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKQ0KICAgICAgICAgIH0pKTsNCiAgICAgICAgfSBlbHNlIGlmIChkYXRhLnJlc2N1ZVByb2plY3QpIHsNCiAgICAgICAgICAvLyDmg4XlhrUyOiDkvb/nlKhyZXNjdWVQcm9qZWN05pWw5o2u77yI5LiOV2F0ZXJmYWxsQ2hhcnTnm7jlkIzvvIkNCiAgICAgICAgICBjb25zb2xlLmxvZygn5L2/55SocmVzY3VlUHJvamVjdOaVsOaNricpOw0KICAgICAgICAgIGRhdGFJdGVtcyA9IE9iamVjdC5lbnRyaWVzKGRhdGEucmVzY3VlUHJvamVjdCkubWFwKChba2V5LCB2YWx1ZV0pID0+ICh7DQogICAgICAgICAgICBuYW1lOiBrZXksDQogICAgICAgICAgICB2YWx1ZTogKChOdW1iZXIodmFsdWUpIHx8IDApIC8gMTAwMDApLnRvRml4ZWQoMikNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0gZWxzZSBpZiAoZGF0YS5zY3JhcExvc3MpIHsNCiAgICAgICAgICAvLyDmg4XlhrUzOiDkvb/nlKhzY3JhcExvc3PmlbDmja4NCiAgICAgICAgICBjb25zb2xlLmxvZygn5L2/55Soc2NyYXBMb3Nz5pWw5o2uJyk7DQogICAgICAgICAgZGF0YUl0ZW1zID0gT2JqZWN0LmVudHJpZXMoZGF0YS5zY3JhcExvc3MpLm1hcCgoW2tleSwgdmFsdWVdKSA9PiAoew0KICAgICAgICAgICAgbmFtZToga2V5LA0KICAgICAgICAgICAgdmFsdWU6ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpDQogICAgICAgICAgfSkpOw0KICAgICAgICB9IGVsc2UgaWYgKGRhdGEuc2NyYXBMb3NzUHJvamVjdCkgew0KICAgICAgICAgIC8vIOaDheWGtTQ6IOS9v+eUqHNjcmFwTG9zc1Byb2plY3TmlbDmja4NCiAgICAgICAgICBjb25zb2xlLmxvZygn5L2/55Soc2NyYXBMb3NzUHJvamVjdOaVsOaNricpOw0KICAgICAgICAgIGRhdGFJdGVtcyA9IE9iamVjdC5lbnRyaWVzKGRhdGEuc2NyYXBMb3NzUHJvamVjdCkubWFwKChba2V5LCB2YWx1ZV0pID0+ICh7DQogICAgICAgICAgICBuYW1lOiBrZXksDQogICAgICAgICAgICB2YWx1ZTogKChOdW1iZXIodmFsdWUpIHx8IDApIC8gMTAwMDApLnRvRml4ZWQoMikNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5oOF5Ya1NTog55u05o6l5L2/55SoZGF0YeS9nOS4uuWvueixoQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCfnm7TmjqXkvb/nlKhkYXRh5a+56LGhJyk7DQogICAgICAgICAgZGF0YUl0ZW1zID0gT2JqZWN0LmVudHJpZXMoZGF0YSkubWFwKChba2V5LCB2YWx1ZV0pID0+ICh7DQogICAgICAgICAgICBuYW1lOiBrZXksDQogICAgICAgICAgICB2YWx1ZTogKChOdW1iZXIodmFsdWUpIHx8IDApIC8gMTAwMDApLnRvRml4ZWQoMikNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygn5aSE55CG5ZCO55qE5pWw5o2u6aG5OicsIGRhdGFJdGVtcyk7DQoNCiAgICAgICAgaWYgKGRhdGFJdGVtcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgLy8g5oyJ5pWw5YC85LuO6auY5Yiw5L2O5o6S5bqPDQogICAgICAgICAgZGF0YUl0ZW1zLnNvcnQoKGEsIGIpID0+IGIudmFsdWUgLSBhLnZhbHVlKTsNCg0KICAgICAgICAgIC8vIOWPquWPluWJjeWNgeS4quacgOWkp+eahOaVsOaNrg0KICAgICAgICAgIGNvbnN0IHRvcFRlbkl0ZW1zID0gZGF0YUl0ZW1zLnNsaWNlKDAsIDEwKTsNCiAgICAgICAgICBjb25zb2xlLmxvZygn5Y+W5YmN5Y2B5Liq5pyA5aSn5pWw5o2uOicsIHRvcFRlbkl0ZW1zKTsNCg0KICAgICAgICAgIC8vIOWIhuemu+aOkuW6j+WQjueahOaVsOaNrg0KICAgICAgICAgIHRvcFRlbkl0ZW1zLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgICAgICB4QXhpc0RhdGEucHVzaChpdGVtLm5hbWUpOw0KICAgICAgICAgICAgc2VyaWVzRGF0YS5wdXNoKHsNCiAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0udmFsdWUsDQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogY29sb3JzW2luZGV4ICUgY29sb3JzLmxlbmd0aF0gfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc29sZS53YXJuKCfmsqHmnInmib7liLDmnInmlYjnmoTmlbDmja7pobknKTsNCiAgICAgICAgICAvLyDmt7vliqDpu5jorqTmlbDmja7ku6Xkvr/mtYvor5UNCiAgICAgICAgICB4QXhpc0RhdGEucHVzaCgn5peg5pWw5o2uJyk7DQogICAgICAgICAgc2VyaWVzRGF0YS5wdXNoKHsNCiAgICAgICAgICAgIHZhbHVlOiAwLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiBjb2xvcnNbMF0gfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQoNCiAgICAgICAgY29uc29sZS5sb2coJ3jovbTnu7TluqbmlbDmja46JywgeEF4aXNEYXRhKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+afseeKtuWbvuaVsOaNrjonLCBzZXJpZXNEYXRhKTsNCg0KICAgICAgICAvLyDmm7TmlrDlm77ooajphY3nva4NCiAgICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICAgIGdyaWQ6IHsgbGVmdDogJzglJywgcmlnaHQ6ICc0JScsIGJvdHRvbTogJzMlJywgY29udGFpbkxhYmVsOiB0cnVlIH0sDQogICAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgICAgYXhpc1BvaW50ZXI6IHsgdHlwZTogJ3NoYWRvdycgfSwNCiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI2ZmZicgfSwNCiAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gKHBhcmFtcykgew0KICAgICAgICAgICAgICBsZXQgcmVzdWx0ID0gcGFyYW1zWzBdLm5hbWUgKyAnPGJyLz4nOw0KICAgICAgICAgICAgICBwYXJhbXMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZFZhbHVlID0gcGFyc2VGbG9hdChpdGVtLnZhbHVlKS50b0ZpeGVkKDIpLnRvU3RyaW5nKCkucmVwbGFjZSgvXEIoPz0oXGR7M30pKyg/IVxkKSkvZywgJywnKTsNCiAgICAgICAgICAgICAgICByZXN1bHQgKz0gaXRlbS5tYXJrZXIgKyAnICcgKyBpdGVtLnNlcmllc05hbWUgKyAnOiAnICsgZm9ybWF0dGVkVmFsdWUgKyAn5LiH5YWDPGJyLz4nOw0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgICAgZGF0YTogeEF4aXNEYXRhLA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicsDQogICAgICAgICAgICAgIGludGVydmFsOiAwLCAvLyDmmL7npLrmiYDmnInmoIfnrb4NCiAgICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInIC8vIOWxheS4reWvuem9kA0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgICBuYW1lOiAn6YeR6aKdICjkuIflhYMpJywNCiAgICAgICAgICAgIG5hbWVUZXh0U3R5bGU6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfSwNCiAgICAgICAgICAgIHNwbGl0TGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjMzc0MTUxJyB9IH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICAgIG5hbWU6ICfmiqXlup/mjZ/lpLHmiJDmnKwnLA0KICAgICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgICBkYXRhOiBzZXJpZXNEYXRhDQogICAgICAgICAgfV0NCiAgICAgICAgfTsNCg0KICAgICAgICB0aGlzLmNoYXJ0cy5zY3JhcExvc3NDaGFydC5zZXRPcHRpb24ob3B0aW9uLCB0cnVlKTsgLy8g5L2/55SodHJ1ZeW8uuWItuWIt+aWsA0KICAgICAgICBjb25zb2xlLmxvZygnU2NyYXBMb3NzQ2hhcnTmn7Hnirblm77mlbDmja7lt7Lmm7TmlrAnKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ1NjcmFwTG9zc0NoYXJ05a6e5L6L5LiN5a2Y5Zyo5oiW5pWw5o2u5Li656m6Jyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOabtOaWsFF1YWxpdHlPYmplY3Rpb25DaGFydOafseeKtuWbvg0KICAgIHVwZGF0ZVF1YWxpdHlPYmplY3Rpb25DaGFydChkYXRhKSB7DQogICAgICBpZiAodGhpcy5jaGFydHMucXVhbGl0eU9iamVjdGlvbkNoYXJ0ICYmIGRhdGEpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aOpeaUtuWIsOeahFF1YWxpdHlPYmplY3Rpb25DaGFydOaVsOaNrjonLCBkYXRhKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+aVsOaNruexu+WeizonLCB0eXBlb2YgZGF0YSk7DQogICAgICAgIGNvbnNvbGUubG9nKCfmlbDmja7plK46JywgT2JqZWN0LmtleXMoZGF0YSkpOw0KDQogICAgICAgIC8vIOWkhOeQhui0qOmHj+W8guiuruaNn+WkseaVsOaNru+8jOWwneivleWkmuenjeWPr+iDveeahOaVsOaNrue7k+aehA0KICAgICAgICBjb25zdCB4QXhpc0RhdGEgPSBbXTsgICAgICAvLyB46L2057u05bqm5pWw5o2uDQogICAgICAgIGNvbnN0IHNlcmllc0RhdGEgPSBbXTsgICAgIC8vIOafseeKtuWbvuaVsOaNrg0KICAgICAgICBjb25zdCBjb2xvcnMgPSBbJyM5M0M1RkQnLCAnIzg2RUZBQycsICcjRkRFNjhBJywgJyNGQ0E1QTUnLCAnI0M0QjVGRCcsICcjRjNFOEZGJ107DQogICAgICAgIGxldCBkYXRhSXRlbXMgPSBbXTsNCg0KICAgICAgICAvLyDlsJ3or5XkuI3lkIznmoTmlbDmja7nu5PmnoTvvIzovazmjaLkuLrkuIflhYMNCiAgICAgICAgaWYgKGRhdGEucXVhbGl0eU9iamVjdGlvbkxvc3NNYXApIHsNCiAgICAgICAgICAvLyDmg4XlhrUxOiDkvb/nlKhxdWFsaXR5T2JqZWN0aW9uTG9zc01hcOaVsOaNrg0KICAgICAgICAgIGNvbnNvbGUubG9nKCfkvb/nlKhxdWFsaXR5T2JqZWN0aW9uTG9zc01hcOaVsOaNricpOw0KICAgICAgICAgIGRhdGFJdGVtcyA9IE9iamVjdC5lbnRyaWVzKGRhdGEucXVhbGl0eU9iamVjdGlvbkxvc3NNYXApLm1hcCgoW2tleSwgdmFsdWVdKSA9PiAoew0KICAgICAgICAgICAgbmFtZToga2V5LA0KICAgICAgICAgICAgdmFsdWU6ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpDQogICAgICAgICAgfSkpOw0KICAgICAgICB9IGVsc2UgaWYgKGRhdGEucmVzY3VlUHJvamVjdCkgew0KICAgICAgICAgIC8vIOaDheWGtTI6IOS9v+eUqHJlc2N1ZVByb2plY3TmlbDmja7vvIjkuI5XYXRlcmZhbGxDaGFydOebuOWQjO+8iQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCfkvb/nlKhyZXNjdWVQcm9qZWN05pWw5o2uJyk7DQogICAgICAgICAgZGF0YUl0ZW1zID0gT2JqZWN0LmVudHJpZXMoZGF0YS5yZXNjdWVQcm9qZWN0KS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgIG5hbWU6IGtleSwNCiAgICAgICAgICAgIHZhbHVlOiAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKQ0KICAgICAgICAgIH0pKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDmg4XlhrUzOiDnm7TmjqXkvb/nlKhkYXRh5L2c5Li65a+56LGhDQogICAgICAgICAgY29uc29sZS5sb2coJ+ebtOaOpeS9v+eUqGRhdGHlr7nosaEnKTsNCiAgICAgICAgICBkYXRhSXRlbXMgPSBPYmplY3QuZW50cmllcyhkYXRhKS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gKHsNCiAgICAgICAgICAgIG5hbWU6IGtleSwNCiAgICAgICAgICAgIHZhbHVlOiAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKQ0KICAgICAgICAgIH0pKTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCflpITnkIblkI7nmoTmlbDmja7pobk6JywgZGF0YUl0ZW1zKTsNCg0KICAgICAgICBpZiAoZGF0YUl0ZW1zLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAvLyDmjInmlbDlgLzku47pq5jliLDkvY7mjpLluo8NCiAgICAgICAgICBkYXRhSXRlbXMuc29ydCgoYSwgYikgPT4gYi52YWx1ZSAtIGEudmFsdWUpOw0KDQogICAgICAgICAgLy8g5Y+q5Y+W5YmN5Y2B5Liq5pyA5aSn55qE5pWw5o2uDQogICAgICAgICAgY29uc3QgdG9wVGVuSXRlbXMgPSBkYXRhSXRlbXMuc2xpY2UoMCwgMTApOw0KICAgICAgICAgIGNvbnNvbGUubG9nKCflj5bliY3ljYHkuKrmnIDlpKfmlbDmja46JywgdG9wVGVuSXRlbXMpOw0KDQogICAgICAgICAgLy8g5YiG56a75o6S5bqP5ZCO55qE5pWw5o2uDQogICAgICAgICAgdG9wVGVuSXRlbXMuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICAgIHhBeGlzRGF0YS5wdXNoKGl0ZW0ubmFtZSk7DQogICAgICAgICAgICBzZXJpZXNEYXRhLnB1c2goew0KICAgICAgICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZSwNCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiBjb2xvcnNbaW5kZXggJSBjb2xvcnMubGVuZ3RoXSB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLndhcm4oJ+ayoeacieaJvuWIsOacieaViOeahOaVsOaNrumhuScpOw0KICAgICAgICAgIC8vIOa3u+WKoOm7mOiupOaVsOaNruS7peS+v+a1i+ivlQ0KICAgICAgICAgIHhBeGlzRGF0YS5wdXNoKCfml6DmlbDmja4nKTsNCiAgICAgICAgICBzZXJpZXNEYXRhLnB1c2goew0KICAgICAgICAgICAgdmFsdWU6IDAsDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6IGNvbG9yc1swXSB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICBjb25zb2xlLmxvZygneOi9tOe7tOW6puaVsOaNrjonLCB4QXhpc0RhdGEpOw0KICAgICAgICBjb25zb2xlLmxvZygn5p+x54q25Zu+5pWw5o2uOicsIHNlcmllc0RhdGEpOw0KDQogICAgICAgIC8vIOabtOaWsOWbvuihqOmFjee9rg0KICAgICAgICBjb25zdCBvcHRpb24gPSB7DQogICAgICAgICAgZ3JpZDogeyBsZWZ0OiAnOCUnLCByaWdodDogJzQlJywgYm90dG9tOiAnMyUnLCBjb250YWluTGFiZWw6IHRydWUgfSwNCiAgICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzkzQzVGRCcsDQogICAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiAocGFyYW1zKSB7DQogICAgICAgICAgICAgIGxldCByZXN1bHQgPSBwYXJhbXNbMF0ubmFtZSArICc8YnIvPic7DQogICAgICAgICAgICAgIHBhcmFtcy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgICAgY29uc3QgZm9ybWF0dGVkVmFsdWUgPSBwYXJzZUZsb2F0KGl0ZW0udmFsdWUpLnRvRml4ZWQoMikudG9TdHJpbmcoKS5yZXBsYWNlKC9cQig/PShcZHszfSkrKD8hXGQpKS9nLCAnLCcpOw0KICAgICAgICAgICAgICAgIHJlc3VsdCArPSBpdGVtLm1hcmtlciArICcgJyArIGl0ZW0uc2VyaWVzTmFtZSArICc6ICcgKyBmb3JtYXR0ZWRWYWx1ZSArICfkuIflhYM8YnIvPic7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0Ow0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgICBkYXRhOiB4QXhpc0RhdGEsDQogICAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgICAgY29sb3I6ICcjOUNBM0FGJywNCiAgICAgICAgICAgICAgaW50ZXJ2YWw6IDAsIC8vIOaYvuekuuaJgOacieagh+etvg0KICAgICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicgLy8g5bGF5Lit5a+56b2QDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB5QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIG5hbWU6ICfph5Hpop0gKOS4h+WFgyknLA0KICAgICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9LA0KICAgICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbew0KICAgICAgICAgICAgbmFtZTogJ+i0qOmHj+W8guiuruaNn+WkseaIkOacrCcsDQogICAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICAgIGRhdGE6IHNlcmllc0RhdGENCiAgICAgICAgICB9XQ0KICAgICAgICB9Ow0KDQogICAgICAgIHRoaXMuY2hhcnRzLnF1YWxpdHlPYmplY3Rpb25DaGFydC5zZXRPcHRpb24ob3B0aW9uLCB0cnVlKTsgLy8g5L2/55SodHJ1ZeW8uuWItuWIt+aWsA0KICAgICAgICBjb25zb2xlLmxvZygnUXVhbGl0eU9iamVjdGlvbkNoYXJ05p+x54q25Zu+5pWw5o2u5bey5pu05pawJyk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjb25zb2xlLmVycm9yKCdRdWFsaXR5T2JqZWN0aW9uQ2hhcnTlrp7kvovkuI3lrZjlnKjmiJbmlbDmja7kuLrnqbonKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgZ2V0UXVhbGl0eU9iamVjdGlvbkxvc3NEZXRhaWwoKSB7DQogICAgICAvLyDlj6rmnInlvZPmiJDmnKzkuK3lv4PlkozkvJrorqHmnJ/pg73mnInlgLzml7bmiY3or7fmsYINCiAgICAgIGlmICghdGhpcy5jb3N0Q2VudGVyIHx8ICF0aGlzLmFjY291bnRpbmdQZXJpb2QgfHwgKHRoaXMuY29udGFpblR5cGUgIT09IDIgJiYgdGhpcy5jb250YWluVHlwZSAhPT0gMSkpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aIkOacrOS4reW/g+OAgeS8muiuoeacn+aIlui0qOmHj+aIkOacrOexu+Wei+S4uuepuu+8jOi3s+i/h+aVsOaNruivt+axgicpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgY29zdENlbnRlcjogdGhpcy5jb3N0Q2VudGVyLA0KICAgICAgICB5ZWFyTW9udGg6IHRoaXMuYWNjb3VudGluZ1BlcmlvZC5yZXBsYWNlKCctJywgJycpLCAvLyDlsIYgMjAyNS0wNiDovazmjaLkuLogMjAyNTA2DQogICAgICAgIGNvbnRhaW5UeXBlOiB0aGlzLmNvbnRhaW5UeXBlDQogICAgICB9Ow0KDQogICAgICBnZXRRdWFsaXR5T2JqZWN0aW9uTG9zc0RldGFpbChwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygnZ2V0UXVhbGl0eU9iamVjdGlvbkxvc3NEZXRhaWw6JywgcmVzcG9uc2UpOw0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIC8vIOabtOaWsFF1YWxpdHlPYmplY3Rpb25DaGFydOafseeKtuWbvg0KICAgICAgICAgIHRoaXMudXBkYXRlUXVhbGl0eU9iamVjdGlvbkNoYXJ0KHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPllF1YWxpdHlPYmplY3Rpb25DaGFydOaVsOaNruWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPluS6p+WTgei0qOmHj+W8guiuruaNn+WkseaYjue7huaVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGdldFNjcmFwTG9zc0NoYXJ0RGV0YWlsc0RldGFpbCgpIHsNCiAgICAgIC8vIOWPquacieW9k+aIkOacrOS4reW/g+WSjOS8muiuoeacn+mDveacieWAvOaXtuaJjeivt+axgg0KICAgICAgaWYgKCF0aGlzLmNvc3RDZW50ZXIgfHwgIXRoaXMuYWNjb3VudGluZ1BlcmlvZCB8fCAodGhpcy5jb250YWluVHlwZSAhPT0gMiAmJiB0aGlzLmNvbnRhaW5UeXBlICE9PSAxKSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5oiQ5pys5Lit5b+D44CB5Lya6K6h5pyf5oiW6LSo6YeP5oiQ5pys57G75Z6L5Li656m677yM6Lez6L+H5pWw5o2u6K+35rGCJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICBjb3N0Q2VudGVyOiB0aGlzLmNvc3RDZW50ZXIsDQogICAgICAgIHllYXJNb250aDogdGhpcy5hY2NvdW50aW5nUGVyaW9kLnJlcGxhY2UoJy0nLCAnJyksIC8vIOWwhiAyMDI1LTA2IOi9rOaNouS4uiAyMDI1MDYNCiAgICAgICAgY29udGFpblR5cGU6IHRoaXMuY29udGFpblR5cGUNCiAgICAgIH07DQoNCiAgICAgIGdldFNjcmFwTG9zc0NoYXJ0RGV0YWlsc0RldGFpbChwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygnZ2V0U2NyYXBMb3NzQ2hhcnREZXRhaWxzRGV0YWlsOicsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICAvLyDmm7TmlrBTY3JhcExvc3NDaGFydOafseeKtuWbvg0KICAgICAgICAgIHRoaXMudXBkYXRlU2NyYXBMb3NzQ2hhcnQocmVzcG9uc2UuZGF0YSk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+WU2NyYXBMb3NzQ2hhcnTmlbDmja7lpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5bkuqflk4HmiqXlup/mjZ/lpLHmmI7nu4bmlbDmja7lpLHotKUnKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBnZXRFeHRlcm5hbENvc3REZXRhaWwoKSB7DQogICAgICAvLyDlj6rmnInlvZPmiJDmnKzkuK3lv4PlkozkvJrorqHmnJ/pg73mnInlgLzml7bmiY3or7fmsYINCiAgICAgIGlmICghdGhpcy5jb3N0Q2VudGVyIHx8ICF0aGlzLmFjY291bnRpbmdQZXJpb2QgfHwgKHRoaXMuY29udGFpblR5cGUgIT09IDIgJiYgdGhpcy5jb250YWluVHlwZSAhPT0gMSkpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aIkOacrOS4reW/g+OAgeS8muiuoeacn+aIlui0qOmHj+aIkOacrOexu+Wei+S4uuepuu+8jOi3s+i/h+aVsOaNruivt+axgicpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgY29zdENlbnRlcjogdGhpcy5jb3N0Q2VudGVyLA0KICAgICAgICB5ZWFyTW9udGg6IHRoaXMuYWNjb3VudGluZ1BlcmlvZC5yZXBsYWNlKCctJywgJycpLCAvLyDlsIYgMjAyNS0wNiDovazmjaLkuLogMjAyNTA2DQogICAgICAgIGNvbnRhaW5UeXBlOiB0aGlzLmNvbnRhaW5UeXBlDQogICAgICB9Ow0KDQogICAgICBnZXRFeHRlcm5hbENvc3REZXRhaWwocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ2dldEV4dGVybmFsQ29zdERldGFpbDonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgLy8g5pu05paw5aSW6YOo5o2f5aSx5oiQ5pys5p6E5oiQ5Zu+6KGoDQogICAgICAgICAgdGhpcy51cGRhdGVFeHRlcm5hbENvc3REZXRhaWxDaGFydChyZXNwb25zZS5kYXRhKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICAvLyBjb25zb2xlLmVycm9yKCfojrflj5blpJbpg6jmjZ/lpLHmiJDmnKzmlbDmja7lpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5blpJbpg6jmjZ/lpLHmiJDmnKzmlbDmja7lpLHotKUnKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBnZXRJbnRlcm5hbENvc3REZXRhaWwoKSB7DQogICAgICAvLyDlj6rmnInlvZPmiJDmnKzkuK3lv4PlkozkvJrorqHmnJ/pg73mnInlgLzml7bmiY3or7fmsYINCiAgICAgIGlmICghdGhpcy5jb3N0Q2VudGVyIHx8ICF0aGlzLmFjY291bnRpbmdQZXJpb2QgfHwgKHRoaXMuY29udGFpblR5cGUgIT09IDIgJiYgdGhpcy5jb250YWluVHlwZSAhPT0gMSkpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aIkOacrOS4reW/g+OAgeS8muiuoeacn+aIlui0qOmHj+aIkOacrOexu+Wei+S4uuepuu+8jOi3s+i/h+aVsOaNruivt+axgicpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgY29zdENlbnRlcjogdGhpcy5jb3N0Q2VudGVyLA0KICAgICAgICB5ZWFyTW9udGg6IHRoaXMuYWNjb3VudGluZ1BlcmlvZC5yZXBsYWNlKCctJywgJycpLCAvLyDlsIYgMjAyNS0wNiDovazmjaLkuLogMjAyNTA2DQogICAgICAgIGNvbnRhaW5UeXBlOiB0aGlzLmNvbnRhaW5UeXBlDQogICAgICB9Ow0KDQogICAgICBnZXRJbnRlcm5hbENvc3REZXRhaWwocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ2dldEludGVybmFsQ29zdERldGFpbDonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgLy8g5pu05paw5YaF6YOo5o2f5aSx5oiQ5pys5p6E5oiQ5Zu+6KGoDQogICAgICAgICAgdGhpcy51cGRhdGVJbnRlcm5hbENvc3REZXRhaWxDaGFydChyZXNwb25zZS5kYXRhKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5blhoXpg6jmjZ/lpLHmiJDmnKzmlbDmja7lpLHotKUnKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyDmm7TmlrDlhoXpg6jmjZ/lpLHmiJDmnKzmnoTmiJDlm77ooagNCiAgICB1cGRhdGVJbnRlcm5hbENvc3REZXRhaWxDaGFydChkYXRhKSB7DQogICAgICBpZiAodGhpcy5jaGFydHMuaW50ZXJuYWxDb3N0RGV0YWlsQ2hhcnQgJiYgZGF0YSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5o6l5pS25Yiw55qE5YaF6YOo5o2f5aSx5oiQ5pys5pWw5o2uOicsIGRhdGEpOw0KDQogICAgICAgIC8vIOaUtumbhuaJgOacieaVsOaNrumhuQ0KICAgICAgICBjb25zdCBhbGxEYXRhSXRlbXMgPSBbXTsNCiAgICAgICAgY29uc3QgY29sb3JzID0gWycjOTNDNUZEJywgJyM4NkVGQUMnLCAnI0ZERTY4QScsICcjRkNBNUE1JywgJyNDNEI1RkQnXTsNCg0KICAgICAgICAvLyDlpITnkIblkITkuKrmiJDmnKzpobnvvIzmlLbpm4bliLDnu5/kuIDmlbDnu4TkuK3vvIzovazmjaLkuLrkuIflhYMNCiAgICAgICAgaWYgKGRhdGEuY29udHJhY3Rpb25Mb3NzKSB7DQogICAgICAgICAgT2JqZWN0LmVudHJpZXMoZGF0YS5jb250cmFjdGlvbkxvc3MpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4gew0KICAgICAgICAgICAgLy8g56Gu5L+d5pWw5YC86L2s5o2i77yM5YyF5ousMOWAvOS5n+imgeaYvuekuu+8jOi9rOaNouS4uuS4h+WFgw0KICAgICAgICAgICAgY29uc3QgbnVtVmFsdWUgPSAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgIGFsbERhdGFJdGVtcy5wdXNoKHsgbmFtZToga2V5LCB2YWx1ZTogbnVtVmFsdWUgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICBpZiAoZGF0YS5yZXNjdWVDb3N0KSB7DQogICAgICAgICAgT2JqZWN0LmVudHJpZXMoZGF0YS5yZXNjdWVDb3N0KS5mb3JFYWNoKChba2V5LCB2YWx1ZV0pID0+IHsNCiAgICAgICAgICAgIC8vIOehruS/neaVsOWAvOi9rOaNou+8jOWMheaLrDDlgLzkuZ/opoHmmL7npLrvvIzovazmjaLkuLrkuIflhYMNCiAgICAgICAgICAgIGNvbnN0IG51bVZhbHVlID0gKChOdW1iZXIodmFsdWUpIHx8IDApIC8gMTAwMDApLnRvRml4ZWQoMik7DQogICAgICAgICAgICBhbGxEYXRhSXRlbXMucHVzaCh7IG5hbWU6IGtleSwgdmFsdWU6IG51bVZhbHVlIH0pOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKGRhdGEucmV2aXNpb25Mb3NzKSB7DQogICAgICAgICAgT2JqZWN0LmVudHJpZXMoZGF0YS5yZXZpc2lvbkxvc3MpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4gew0KICAgICAgICAgICAgLy8g56Gu5L+d5pWw5YC86L2s5o2i77yM5YyF5ousMOWAvOS5n+imgeaYvuekuu+8jOi9rOaNouS4uuS4h+WFgw0KICAgICAgICAgICAgY29uc3QgbnVtVmFsdWUgPSAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgIGFsbERhdGFJdGVtcy5wdXNoKHsgbmFtZToga2V5LCB2YWx1ZTogbnVtVmFsdWUgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICBpZiAoZGF0YS5zY3JhcExvc3MpIHsNCiAgICAgICAgICBPYmplY3QuZW50cmllcyhkYXRhLnNjcmFwTG9zcykuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7DQogICAgICAgICAgICAvLyDnoa7kv53mlbDlgLzovazmjaLvvIzljIXmi6ww5YC85Lmf6KaB5pi+56S677yM6L2s5o2i5Li65LiH5YWDDQogICAgICAgICAgICBjb25zdCBudW1WYWx1ZSA9ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpOw0KICAgICAgICAgICAgYWxsRGF0YUl0ZW1zLnB1c2goeyBuYW1lOiBrZXksIHZhbHVlOiBudW1WYWx1ZSB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnNvbGUubG9nKCfmlLbpm4bliLDnmoTmiYDmnInmlbDmja7pobnvvIjljIXlkKsw5YC877yJOicsIGFsbERhdGFJdGVtcyk7DQoNCiAgICAgICAgLy8g5oyJ5pWw5YC85LuO6auY5Yiw5L2O5o6S5bqP77yIMOWAvOS8muaOkuWcqOi0n+WAvOS5i+WJje+8jOato+WAvOS5i+WQju+8iQ0KICAgICAgICBhbGxEYXRhSXRlbXMuc29ydCgoYSwgYikgPT4gYi52YWx1ZSAtIGEudmFsdWUpOw0KDQogICAgICAgIGNvbnNvbGUubG9nKCfmjpLluo/lkI7nmoTmlbDmja7vvIjljIXlkKsw5YC877yJOicsIGFsbERhdGFJdGVtcyk7DQoNCiAgICAgICAgLy8g5YiG56a75o6S5bqP5ZCO55qE5pWw5o2u77yM5Y+N6L2s6aG65bqP5L2/6YeR6aKd5aSn55qE5pi+56S65Zyo5LiK6Z2iDQogICAgICAgIGNvbnN0IHlBeGlzRGF0YSA9IFtdOw0KICAgICAgICBjb25zdCBzZXJpZXNEYXRhID0gW107DQoNCiAgICAgICAgLy8g5Y+N6L2s5pWw57uE77yM5L2/6YeR6aKd5aSn55qE5pi+56S65Zyo5Zu+6KGo5LiK5pa5DQogICAgICAgIGFsbERhdGFJdGVtcy5yZXZlcnNlKCkuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsNCiAgICAgICAgICB5QXhpc0RhdGEucHVzaChpdGVtLm5hbWUpOw0KICAgICAgICAgIHNlcmllc0RhdGEucHVzaCh7DQogICAgICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogY29sb3JzW2luZGV4ICUgY29sb3JzLmxlbmd0aF0gfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9KTsNCg0KICAgICAgICBjb25zb2xlLmxvZygneei9tOaVsOaNrjonLCB5QXhpc0RhdGEpOw0KICAgICAgICBjb25zb2xlLmxvZygn57O75YiX5pWw5o2u77yI5YyF5ZCrMOWAvO+8iTonLCBzZXJpZXNEYXRhKTsNCg0KICAgICAgICAvLyDmm7TmlrDlm77ooajphY3nva4NCiAgICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgICAgZGF0YTogeUF4aXNEYXRhLA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbew0KICAgICAgICAgICAgbmFtZTogJ+mHkeminSAo5LiH5YWDKScsDQogICAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICAgIGRhdGE6IHNlcmllc0RhdGENCiAgICAgICAgICB9XQ0KICAgICAgICB9Ow0KDQogICAgICAgIHRoaXMuY2hhcnRzLmludGVybmFsQ29zdERldGFpbENoYXJ0LnNldE9wdGlvbihvcHRpb24pOw0KICAgICAgICBjb25zb2xlLmxvZygn5YaF6YOo5o2f5aSx5oiQ5pys5p6E5oiQ5Zu+6KGo5pWw5o2u5bey5pu05paw77yI5YyF5ZCrMOWAvO+8jOaMieaVsOWAvOS7jumrmOWIsOS9juaOkuW6j++8iScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmm7TmlrDlpJbpg6jmjZ/lpLHmiJDmnKzmnoTmiJDlm77ooagNCiAgICB1cGRhdGVFeHRlcm5hbENvc3REZXRhaWxDaGFydChkYXRhKSB7DQogICAgICBpZiAodGhpcy5jaGFydHMuZXh0ZXJuYWxDb3N0RGV0YWlsQ2hhcnQgJiYgZGF0YSkgew0KICAgICAgICAvLyDmlLbpm4bmiYDmnInmlbDmja7pobkNCiAgICAgICAgY29uc3QgYWxsRGF0YUl0ZW1zID0gW107DQogICAgICAgIGNvbnN0IGNvbG9ycyA9IFsnI0ZDQTVBNScsICcjRkRFNjhBJywgJyM4NkVGQUMnLCAnIzkzQzVGRCcsICcjQzRCNUZEJ107DQoNCiAgICAgICAgLy8g5aSE55CG5ZCE5Liq5oiQ5pys6aG577yM5pS26ZuG5Yiw57uf5LiA5pWw57uE5Lit77yM6L2s5o2i5Li65LiH5YWDDQogICAgICAgIGlmIChkYXRhLmN1c3RvbWVyQ2xhaW1Db3N0KSB7DQogICAgICAgICAgT2JqZWN0LmVudHJpZXMoZGF0YS5jdXN0b21lckNsYWltQ29zdCkuZm9yRWFjaCgoW2tleSwgdmFsdWVdKSA9PiB7DQogICAgICAgICAgICBhbGxEYXRhSXRlbXMucHVzaCh7IG5hbWU6IGtleSwgdmFsdWU6ICgoTnVtYmVyKHZhbHVlKSB8fCAwKSAvIDEwMDAwKS50b0ZpeGVkKDIpIH0pOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQoNCiAgICAgICAgaWYgKGRhdGEucXVhbGl0eU9iamVjdGlvbkZlZUNvc3QpIHsNCiAgICAgICAgICBPYmplY3QuZW50cmllcyhkYXRhLnF1YWxpdHlPYmplY3Rpb25GZWVDb3N0KS5mb3JFYWNoKChba2V5LCB2YWx1ZV0pID0+IHsNCiAgICAgICAgICAgIGFsbERhdGFJdGVtcy5wdXNoKHsgbmFtZToga2V5LCB2YWx1ZTogKChOdW1iZXIodmFsdWUpIHx8IDApIC8gMTAwMDApLnRvRml4ZWQoMikgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCg0KICAgICAgICBpZiAoZGF0YS5xdWFsaXR5T2JqZWN0aW9uVHJhdmVsQ29zdCkgew0KICAgICAgICAgIE9iamVjdC5lbnRyaWVzKGRhdGEucXVhbGl0eU9iamVjdGlvblRyYXZlbENvc3QpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4gew0KICAgICAgICAgICAgYWxsRGF0YUl0ZW1zLnB1c2goeyBuYW1lOiBrZXksIHZhbHVlOiAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKSB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KDQogICAgICAgIGlmIChkYXRhLnJldHVybkxvc3MpIHsNCiAgICAgICAgICBPYmplY3QuZW50cmllcyhkYXRhLnJldHVybkxvc3MpLmZvckVhY2goKFtrZXksIHZhbHVlXSkgPT4gew0KICAgICAgICAgICAgYWxsRGF0YUl0ZW1zLnB1c2goeyBuYW1lOiBrZXksIHZhbHVlOiAoKE51bWJlcih2YWx1ZSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKSB9KTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOaMieaVsOWAvOS7jumrmOWIsOS9juaOkuW6jw0KICAgICAgICBhbGxEYXRhSXRlbXMuc29ydCgoYSwgYikgPT4gYi52YWx1ZSAtIGEudmFsdWUpOw0KDQogICAgICAgIC8vIOWIhuemu+aOkuW6j+WQjueahOaVsOaNru+8jOWPjei9rOmhuuW6j+S9v+mHkemineWkp+eahOaYvuekuuWcqOS4iumdog0KICAgICAgICBjb25zdCB5QXhpc0RhdGEgPSBbXTsNCiAgICAgICAgY29uc3Qgc2VyaWVzRGF0YSA9IFtdOw0KDQogICAgICAgIC8vIOWPjei9rOaVsOe7hO+8jOS9v+mHkemineWkp+eahOaYvuekuuWcqOWbvuihqOS4iuaWuQ0KICAgICAgICBhbGxEYXRhSXRlbXMucmV2ZXJzZSgpLmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgICAgeUF4aXNEYXRhLnB1c2goaXRlbS5uYW1lKTsNCiAgICAgICAgICBzZXJpZXNEYXRhLnB1c2goew0KICAgICAgICAgICAgdmFsdWU6IGl0ZW0udmFsdWUsDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6IGNvbG9yc1tpbmRleCAlIGNvbG9ycy5sZW5ndGhdIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQoNCiAgICAgICAgLy8g5pu05paw5Zu+6KGo6YWN572uDQogICAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgICB5QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICAgIGRhdGE6IHlBeGlzRGF0YSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICAgIG5hbWU6ICfph5Hpop0gKOS4h+WFgyknLA0KICAgICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgICBkYXRhOiBzZXJpZXNEYXRhDQogICAgICAgICAgfV0NCiAgICAgICAgfTsNCg0KICAgICAgICB0aGlzLmNoYXJ0cy5leHRlcm5hbENvc3REZXRhaWxDaGFydC5zZXRPcHRpb24ob3B0aW9uKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+WklumDqOaNn+WkseaIkOacrOaehOaIkOWbvuihqOaVsOaNruW3suabtOaWsO+8iOW3suaMieaVsOWAvOS7jumrmOWIsOS9juaOkuW6j++8iScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBnZXRRdWFsaXR5Q29zdERldGFpbCgpIHsNCiAgICAgIC8vIOWPquacieW9k+aIkOacrOS4reW/g+WSjOS8muiuoeacn+mDveacieWAvOaXtuaJjeivt+axgg0KICAgICAgaWYgKCF0aGlzLmNvc3RDZW50ZXIgfHwgIXRoaXMuYWNjb3VudGluZ1BlcmlvZCB8fCAodGhpcy5jb250YWluVHlwZSAhPT0gMiAmJiB0aGlzLmNvbnRhaW5UeXBlICE9PSAxKSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5oiQ5pys5Lit5b+D44CB5Lya6K6h5pyf5oiW6LSo6YeP5oiQ5pys57G75Z6L5Li656m677yM6Lez6L+H5pWw5o2u6K+35rGCJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICBjb3N0Q2VudGVyOiB0aGlzLmNvc3RDZW50ZXIsDQogICAgICAgIHllYXJNb250aDogdGhpcy5hY2NvdW50aW5nUGVyaW9kLnJlcGxhY2UoJy0nLCAnJyksIC8vIOWwhiAyMDI1LTA2IOi9rOaNouS4uiAyMDI1MDYNCiAgICAgICAgY29udGFpblR5cGU6IHRoaXMuY29udGFpblR5cGUNCiAgICAgIH07DQoNCiAgICAgIGdldFF1YWxpdHlDb3N0RGV0YWlsKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCdnZXRRdWFsaXR5Q29zdERldGFpbDonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy5xdWFsaXR5Q29zdERhdGEgPSByZXNwb25zZS5kYXRhLnF1YWxpdHlDb3N0RGF0YTsNCiAgICAgICAgICB0aGlzLnF1YWxpdHlDb3N0RGV0YWlsID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICAvLyBjb25zb2xlLmVycm9yKCfojrflj5bppbzlm77mlbDmja7lpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5botKjph4/miJDmnKzmlbDmja7lpLHotKUnKTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvL+i0qOmHj+aIkOacrOWbm+Wkp+exu+WIq+WNoOavlA0KICAgIGdldE11bHRpTGluZUNoYXJ0RGF0YSgpIHsNCiAgICAgIC8vIOWPquacieW9k+aIkOacrOS4reW/g+WSjOS8muiuoeacn+mDveacieWAvOaXtuaJjeivt+axgg0KICAgICAgaWYgKCF0aGlzLmNvc3RDZW50ZXIgfHwgIXRoaXMuYWNjb3VudGluZ1BlcmlvZCB8fCAodGhpcy5jb250YWluVHlwZSAhPT0gMiAmJiB0aGlzLmNvbnRhaW5UeXBlICE9PSAxKSkgew0KICAgICAgICBjb25zb2xlLmxvZygn5oiQ5pys5Lit5b+D44CB5Lya6K6h5pyf5oiW6LSo6YeP5oiQ5pys57G75Z6L5Li656m677yM6Lez6L+H5pWw5o2u6K+35rGCJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICBjb3N0Q2VudGVyOiB0aGlzLmNvc3RDZW50ZXIsDQogICAgICAgIHllYXJNb250aDogdGhpcy5hY2NvdW50aW5nUGVyaW9kLnJlcGxhY2UoJy0nLCAnJyksIC8vIOWwhiAyMDI1LTA2IOi9rOaNouS4uiAyMDI1MDYNCiAgICAgICAgY29udGFpblR5cGU6IHRoaXMuY29udGFpblR5cGUNCiAgICAgIH07DQoNCiAgICAgIGdldE11bHRpTGluZUNoYXJ0RGF0YShwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zb2xlLmxvZygnZ2V0TXVsdGlMaW5lQ2hhcnREYXRhOicsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICB0aGlzLnVwZGF0ZU11bHRpTGluZUNoYXJ0KHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICB9DQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7DQogICAgICAgIC8vIGNvbnNvbGUuZXJyb3IoJ+iOt+WPlumlvOWbvuaVsOaNruWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+iOt+WPlui0qOmHj+aIkOacrOaVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGdldENvbWJvQ2hhcnREZXRhaWwoKSB7DQogICAgICAvLyDlj6rmnInlvZPmiJDmnKzkuK3lv4PlkozkvJrorqHmnJ/pg73mnInlgLzml7bmiY3or7fmsYINCiAgICAgIGlmICghdGhpcy5jb3N0Q2VudGVyIHx8ICF0aGlzLmFjY291bnRpbmdQZXJpb2QgfHwgKHRoaXMuY29udGFpblR5cGUgIT09IDIgJiYgdGhpcy5jb250YWluVHlwZSAhPT0gMSkpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aIkOacrOS4reW/g+OAgeS8muiuoeacn+aIlui0qOmHj+aIkOacrOexu+Wei+S4uuepuu+8jOi3s+i/h+aVsOaNruivt+axgicpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgY29zdENlbnRlcjogdGhpcy5jb3N0Q2VudGVyLA0KICAgICAgICB5ZWFyTW9udGg6IHRoaXMuYWNjb3VudGluZ1BlcmlvZC5yZXBsYWNlKCctJywgJycpLCAvLyDlsIYgMjAyNS0wNiDovazmjaLkuLogMjAyNTA2DQogICAgICAgIGNvbnRhaW5UeXBlOiB0aGlzLmNvbnRhaW5UeXBlDQogICAgICB9Ow0KDQogICAgICBnZXRDb21ib0NoYXJ0RGV0YWlsKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGNvbnNvbGUubG9nKCdnZXRDb21ib0NoYXJ0RGV0YWlsOicsIHJlc3BvbnNlKTsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEpIHsNCiAgICAgICAgICB0aGlzLnVwZGF0ZUNvbWJvQ2hhcnQocmVzcG9uc2UuZGF0YSk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+WQ29tYm9DaGFydOaVsOaNruWksei0pScpOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOabtOaWsENvbWJvQ2hhcnTlm77ooagNCiAgICB1cGRhdGVDb21ib0NoYXJ0KGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmNoYXJ0cy5jb21ib0NoYXJ0ICYmIGRhdGEpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+aOpeaUtuWIsOeahENvbWJvQ2hhcnTmlbDmja46JywgZGF0YSk7DQoNCiAgICAgICAgLy8g5Z+65LqO5Lya6K6h5pyf55Sf5oiQ6L+RNuS4quaciOeahOaciOS7veagh+etvuS9nOS4unjovbTmlbDmja4NCiAgICAgICAgY29uc3QgbW9udGhzID0gdGhpcy5nZW5lcmF0ZUNvbWJvQ2hhcnRNb250aHNCeUFjY291bnRpbmdQZXJpb2QoKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+eUn+aIkOeahOaciOS7veagh+etvjonLCBtb250aHMpOw0KDQogICAgICAgIC8vIOeUn+aIkOWvueW6lOeahOW5tOaciOagvOW8j+eUqOS6juaVsOaNruWMuemFjQ0KICAgICAgICBjb25zdCB5ZWFyTW9udGhzID0gdGhpcy5nZW5lcmF0ZVllYXJNb250aHNCeUFjY291bnRpbmdQZXJpb2QoKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+eUn+aIkOeahOW5tOaciOagvOW8jzonLCB5ZWFyTW9udGhzKTsNCg0KICAgICAgICBjb25zdCBmYWlsdXJlQ29zdERhdGEgPSBbXTsgICAgIC8vIOWksei0peaIkOacrOaVsOaNrg0KICAgICAgICBjb25zdCBjb250cm9sbGluZ0Nvc3REYXRhID0gW107IC8vIOaOp+WItuaIkOacrOaVsOaNrg0KDQogICAgICAgIC8vIOS4uuavj+S4quaciOS7veaPkOWPluWvueW6lOeahOaVsOWAvO+8jOi9rOaNouS4uuS4h+WFgw0KICAgICAgICB5ZWFyTW9udGhzLmZvckVhY2goeWVhck1vbnRoID0+IHsNCiAgICAgICAgICAvLyDojrflj5blpLHotKXmiJDmnKzmlbDmja7vvIzovazmjaLkuLrkuIflhYMNCiAgICAgICAgICBjb25zdCBmYWlsdXJlVmFsdWUgPSBkYXRhLmZhaWx1cmVDb3N0TWFwICYmIGRhdGEuZmFpbHVyZUNvc3RNYXBbeWVhck1vbnRoXQ0KICAgICAgICAgICAgPyAoKE51bWJlcihkYXRhLmZhaWx1cmVDb3N0TWFwW3llYXJNb250aF0pIHx8IDApIC8gMTAwMDApLnRvRml4ZWQoMikNCiAgICAgICAgICAgIDogMDsNCiAgICAgICAgICBmYWlsdXJlQ29zdERhdGEucHVzaChmYWlsdXJlVmFsdWUpOw0KDQogICAgICAgICAgLy8g6I635Y+W5o6n5Yi25oiQ5pys5pWw5o2u77yM6L2s5o2i5Li65LiH5YWDDQogICAgICAgICAgY29uc3QgY29udHJvbGxpbmdWYWx1ZSA9IGRhdGEuY29udHJvbGxpbmdDb3N0TWFwICYmIGRhdGEuY29udHJvbGxpbmdDb3N0TWFwW3llYXJNb250aF0NCiAgICAgICAgICAgID8gKChOdW1iZXIoZGF0YS5jb250cm9sbGluZ0Nvc3RNYXBbeWVhck1vbnRoXSkgfHwgMCkgLyAxMDAwMCkudG9GaXhlZCgyKQ0KICAgICAgICAgICAgOiAwOw0KICAgICAgICAgIGNvbnRyb2xsaW5nQ29zdERhdGEucHVzaChjb250cm9sbGluZ1ZhbHVlKTsNCiAgICAgICAgfSk7DQoNCiAgICAgICAgY29uc29sZS5sb2coJ3jovbTmnIjku73mlbDmja46JywgbW9udGhzLm1hcChtb250aCA9PiBgJHttb250aH3mnIhgKSk7DQogICAgICAgIGNvbnNvbGUubG9nKCflpLHotKXmiJDmnKzmlbDmja46JywgZmFpbHVyZUNvc3REYXRhKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+aOp+WItuaIkOacrOaVsOaNrjonLCBjb250cm9sbGluZ0Nvc3REYXRhKTsNCg0KICAgICAgICAvLyDmm7TmlrDlm77ooajphY3nva4NCiAgICAgICAgY29uc3Qgb3B0aW9uID0gew0KICAgICAgICAgIC8vIOWbvuS+i+mFjee9riAtIOagh+azqOminOiJsuWvueW6lOeahOe7tOW6pg0KICAgICAgICAgIGxlZ2VuZDogew0KICAgICAgICAgICAgZGF0YTogWyflpLHotKXmiJDmnKwnLCAn5o6n5Yi25oiQ5pysJ10sIC8vIOWksei0peaIkOacrCjnuqLoibIjRkNBNUE1Ke+8jOaOp+WItuaIkOacrCjnu7/oibIjODZFRkFDKQ0KICAgICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI0U1RTdFQicgfQ0KICAgICAgICAgIH0sDQogICAgICAgICAgZ3JpZDogeyBsZWZ0OiAnMyUnLCByaWdodDogJzQlJywgYm90dG9tOiAnMyUnLCBjb250YWluTGFiZWw6IHRydWUgfSwNCiAgICAgICAgICB4QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICAgIGJvdW5kYXJ5R2FwOiBmYWxzZSwNCiAgICAgICAgICAgIGRhdGE6IG1vbnRocy5tYXAobW9udGggPT4gYCR7bW9udGh95pyIYCksIC8vIOi/kTbkuKrmnIjnmoTmnIjku70NCiAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnLA0KICAgICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicgLy8g5bGF5Lit5a+56b2QDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB5QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIG5hbWU6ICfmiJDmnKwgKOS4h+WFgyknLA0KICAgICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9LA0KICAgICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICAgIH0sDQoNCiAgICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbmFtZTogJ+Wksei0peaIkOacrCcsIC8vIOe6ouiJsuabsue6vyAjRkNBNUE1DQogICAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgICAgZGF0YTogZmFpbHVyZUNvc3REYXRhLA0KICAgICAgICAgICAgICBzbW9vdGg6IHRydWUsIC8vIOWQr+eUqOW5s+a7keabsue6vw0KICAgICAgICAgICAgICBsaW5lU3R5bGU6IHsgY29sb3I6ICcjRkNBNUE1Jywgd2lkdGg6IDMgfSwNCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI0ZDQTVBNScgfSwNCiAgICAgICAgICAgICAgc3ltYm9sOiAnY2lyY2xlJywNCiAgICAgICAgICAgICAgc3ltYm9sU2l6ZTogNg0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgbmFtZTogJ+aOp+WItuaIkOacrCcsIC8vIOe7v+iJsuabsue6vyAjODZFRkFDDQogICAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgICAgZGF0YTogY29udHJvbGxpbmdDb3N0RGF0YSwNCiAgICAgICAgICAgICAgc21vb3RoOiB0cnVlLCAvLyDlkK/nlKjlubPmu5Hmm7Lnur8NCiAgICAgICAgICAgICAgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzg2RUZBQycsIHdpZHRoOiAzIH0sDQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyM4NkVGQUMnIH0sDQogICAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICAgIHN5bWJvbFNpemU6IDYNCiAgICAgICAgICAgIH0NCiAgICAgICAgICBdDQogICAgICAgIH07DQoNCiAgICAgICAgdGhpcy5jaGFydHMuY29tYm9DaGFydC5zZXRPcHRpb24ob3B0aW9uKTsNCiAgICAgICAgY29uc29sZS5sb2coJ0NvbWJvQ2hhcnTlm77ooajmlbDmja7lt7Lmm7TmlrAnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g55Sf5oiQQ29tYm9DaGFydOeahOaciOS7veagh+etvu+8iOW9k+WJjeaciOS7veWSjOS5i+WJjeeahDXkuKrmnIjvvIkNCiAgICBnZW5lcmF0ZUNvbWJvQ2hhcnRNb250aHMoKSB7DQogICAgICBjb25zdCBtb250aHMgPSBbXTsNCiAgICAgIGNvbnN0IGN1cnJlbnREYXRlID0gbmV3IERhdGUoKTsNCg0KICAgICAgZm9yIChsZXQgaSA9IDU7IGkgPj0gMDsgaS0tKSB7DQogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShjdXJyZW50RGF0ZS5nZXRGdWxsWWVhcigpLCBjdXJyZW50RGF0ZS5nZXRNb250aCgpIC0gaSwgMSk7DQogICAgICAgIGNvbnN0IG1vbnRoID0gZGF0ZS5nZXRNb250aCgpICsgMTsNCiAgICAgICAgbW9udGhzLnB1c2gobW9udGgpOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4gbW9udGhzOw0KICAgIH0sDQoNCiAgICAvLyDnlJ/miJDlr7nlupTnmoTlubTmnIjmoLzlvI/vvIjlvZPliY3mnIjku73lkozkuYvliY3nmoQ15Liq5pyI77yM5aaCMjAyNTAxLCAyMDI1MDLnrYnvvIkNCiAgICBnZW5lcmF0ZVllYXJNb250aHMoKSB7DQogICAgICBjb25zdCB5ZWFyTW9udGhzID0gW107DQogICAgICBjb25zdCBjdXJyZW50RGF0ZSA9IG5ldyBEYXRlKCk7DQoNCiAgICAgIGZvciAobGV0IGkgPSA1OyBpID49IDA7IGktLSkgew0KICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoY3VycmVudERhdGUuZ2V0RnVsbFllYXIoKSwgY3VycmVudERhdGUuZ2V0TW9udGgoKSAtIGksIDEpOw0KICAgICAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpOw0KICAgICAgICBjb25zdCBtb250aCA9IGRhdGUuZ2V0TW9udGgoKSArIDE7DQogICAgICAgIGNvbnN0IHllYXJNb250aCA9IGAke3llYXJ9JHtTdHJpbmcobW9udGgpLnBhZFN0YXJ0KDIsICcwJyl9YDsNCiAgICAgICAgeWVhck1vbnRocy5wdXNoKHllYXJNb250aCk7DQogICAgICB9DQoNCiAgICAgIHJldHVybiB5ZWFyTW9udGhzOw0KICAgIH0sDQoNCiAgICAvLyDln7rkuo7kvJrorqHmnJ/nlJ/miJBDb21ib0NoYXJ055qE5pyI5Lu95qCH562+77yI5Lya6K6h5pyf5b2T5YmN5pyI5Lu95ZKM5LmL5YmN55qENeS4quaciO+8iQ0KICAgIGdlbmVyYXRlQ29tYm9DaGFydE1vbnRoc0J5QWNjb3VudGluZ1BlcmlvZCgpIHsNCiAgICAgIGNvbnN0IG1vbnRocyA9IFtdOw0KDQogICAgICBpZiAoIXRoaXMuYWNjb3VudGluZ1BlcmlvZCkgew0KICAgICAgICBjb25zb2xlLndhcm4oJ+S8muiuoeacn+S4uuepuu+8jOS9v+eUqOezu+e7n+W9k+WJjeaXtumXtCcpOw0KICAgICAgICByZXR1cm4gdGhpcy5nZW5lcmF0ZUNvbWJvQ2hhcnRNb250aHMoKTsNCiAgICAgIH0NCg0KICAgICAgLy8g6Kej5p6Q5Lya6K6h5pyfICjmoLzlvI86IDIwMjUtMDYpDQogICAgICBjb25zdCBbeWVhciwgbW9udGhdID0gdGhpcy5hY2NvdW50aW5nUGVyaW9kLnNwbGl0KCctJykubWFwKE51bWJlcik7DQogICAgICBjb25zdCBhY2NvdW50aW5nRGF0ZSA9IG5ldyBEYXRlKHllYXIsIG1vbnRoIC0gMSwgMSk7IC8vIG1vbnRoLTEg5Zug5Li6RGF0ZeeahOaciOS7veS7jjDlvIDlp4sNCg0KICAgICAgZm9yIChsZXQgaSA9IDU7IGkgPj0gMDsgaS0tKSB7DQogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShhY2NvdW50aW5nRGF0ZS5nZXRGdWxsWWVhcigpLCBhY2NvdW50aW5nRGF0ZS5nZXRNb250aCgpIC0gaSwgMSk7DQogICAgICAgIGNvbnN0IG1vbnRoTnVtID0gZGF0ZS5nZXRNb250aCgpICsgMTsNCiAgICAgICAgbW9udGhzLnB1c2gobW9udGhOdW0pOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4gbW9udGhzOw0KICAgIH0sDQoNCiAgICAvLyDln7rkuo7kvJrorqHmnJ/nlJ/miJDlr7nlupTnmoTlubTmnIjmoLzlvI/vvIjkvJrorqHmnJ/lvZPliY3mnIjku73lkozkuYvliY3nmoQ15Liq5pyI77yJDQogICAgZ2VuZXJhdGVZZWFyTW9udGhzQnlBY2NvdW50aW5nUGVyaW9kKCkgew0KICAgICAgY29uc3QgeWVhck1vbnRocyA9IFtdOw0KDQogICAgICBpZiAoIXRoaXMuYWNjb3VudGluZ1BlcmlvZCkgew0KICAgICAgICBjb25zb2xlLndhcm4oJ+S8muiuoeacn+S4uuepuu+8jOS9v+eUqOezu+e7n+W9k+WJjeaXtumXtCcpOw0KICAgICAgICByZXR1cm4gdGhpcy5nZW5lcmF0ZVllYXJNb250aHMoKTsNCiAgICAgIH0NCg0KICAgICAgLy8g6Kej5p6Q5Lya6K6h5pyfICjmoLzlvI86IDIwMjUtMDYpDQogICAgICBjb25zdCBbeWVhciwgbW9udGhdID0gdGhpcy5hY2NvdW50aW5nUGVyaW9kLnNwbGl0KCctJykubWFwKE51bWJlcik7DQogICAgICBjb25zdCBhY2NvdW50aW5nRGF0ZSA9IG5ldyBEYXRlKHllYXIsIG1vbnRoIC0gMSwgMSk7IC8vIG1vbnRoLTEg5Zug5Li6RGF0ZeeahOaciOS7veS7jjDlvIDlp4sNCg0KICAgICAgZm9yIChsZXQgaSA9IDU7IGkgPj0gMDsgaS0tKSB7DQogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShhY2NvdW50aW5nRGF0ZS5nZXRGdWxsWWVhcigpLCBhY2NvdW50aW5nRGF0ZS5nZXRNb250aCgpIC0gaSwgMSk7DQogICAgICAgIGNvbnN0IHllYXJOdW0gPSBkYXRlLmdldEZ1bGxZZWFyKCk7DQogICAgICAgIGNvbnN0IG1vbnRoTnVtID0gZGF0ZS5nZXRNb250aCgpICsgMTsNCiAgICAgICAgY29uc3QgeWVhck1vbnRoID0gYCR7eWVhck51bX0ke1N0cmluZyhtb250aE51bSkucGFkU3RhcnQoMiwgJzAnKX1gOw0KICAgICAgICB5ZWFyTW9udGhzLnB1c2goeWVhck1vbnRoKTsNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIHllYXJNb250aHM7DQogICAgfSwNCg0KICAgIC8v6LSo6YeP5oiQ5pys5Zub5aSn57G75Yir5Y2g5q+UDQogICAgZ2V0UGllQ2hhcnREYXRhKCkgew0KICAgICAgLy8g5Y+q5pyJ5b2T5oiQ5pys5Lit5b+D5ZKM5Lya6K6h5pyf6YO95pyJ5YC85pe25omN6K+35rGCDQogICAgICBpZiAoIXRoaXMuY29zdENlbnRlciB8fCAhdGhpcy5hY2NvdW50aW5nUGVyaW9kIHx8ICh0aGlzLmNvbnRhaW5UeXBlICE9PSAyICYmIHRoaXMuY29udGFpblR5cGUgIT09IDEpKSB7DQogICAgICAgIGNvbnNvbGUubG9nKCfmiJDmnKzkuK3lv4PjgIHkvJrorqHmnJ/miJbotKjph4/miJDmnKznsbvlnovkuLrnqbrvvIzot7Pov4fmlbDmja7or7fmsYInKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIGNvc3RDZW50ZXI6IHRoaXMuY29zdENlbnRlciwNCiAgICAgICAgeWVhck1vbnRoOiB0aGlzLmFjY291bnRpbmdQZXJpb2QucmVwbGFjZSgnLScsICcnKSwgLy8g5bCGIDIwMjUtMDYg6L2s5o2i5Li6IDIwMjUwNg0KICAgICAgICBjb250YWluVHlwZTogdGhpcy5jb250YWluVHlwZQ0KICAgICAgfTsNCg0KDQogICAgICBnZXRQaWVDaGFydERhdGEocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coJ2dldFBpZUNoYXJ0RGF0YTonLCByZXNwb25zZSk7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7DQogICAgICAgICAgdGhpcy51cGRhdGVQaWVDaGFydChyZXNwb25zZS5kYXRhKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5bppbzlm77mlbDmja7lpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfojrflj5botKjph4/miJDmnKzmlbDmja7lpLHotKUnKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g6I635Y+W5oiQ5pys5Lit5b+D5YiX6KGoDQogICAgZ2V0Q29zdENlbnRlckxpc3QoKSB7DQogICAgICB0aGlzLmNvc3RDZW50ZXJMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGNvc3RDZW50ZXJsaXN0KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY29zdENlbnRlck9wdGlvbnMgPSByZXNwb25zZS5kYXRhIHx8IFtdOw0KICAgICAgICAvLyDlpoLmnpzmnInmlbDmja7vvIzorr7nva7pu5jorqTpgInkuK3nrKzkuIDkuKoNCiAgICAgICAgaWYgKHRoaXMuY29zdENlbnRlck9wdGlvbnMubGVuZ3RoID4gMCkgew0KICAgICAgICAgIGNvbnNvbGUubG9nKCfojrflj5bmiJDmnKzkuK3lv4PliJfooag6JywgdGhpcy5jb3N0Q2VudGVyT3B0aW9ucyk7DQogICAgICAgICAgdGhpcy5jb3N0Q2VudGVyID0gdGhpcy5jb3N0Q2VudGVyT3B0aW9uc1swXS5rZXk7DQogICAgICAgICAgLy8g6K6+572u6buY6K6k5YC85ZCO77yM5Li75Yqo6Kem5Y+R5LiA5qyh5pWw5o2u5Yi35pawDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5yZWZyZXNoQ2hhcnREYXRhKCk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5oiQ5pys5Lit5b+D5YiX6KGo5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6I635Y+W5oiQ5pys5Lit5b+D5YiX6KGo5aSx6LSlJyk7DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5jb3N0Q2VudGVyTG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOabtOaWsOmlvOWbvuaVsOaNrg0KICAgIHVwZGF0ZVBpZUNoYXJ0KGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmNoYXJ0cy5waWVDaGFydCAmJiBkYXRhKSB7DQogICAgICAgIC8vIOabtOaWsOmlvOWbvueahOaVsOaNru+8jOi9rOaNouS4uuS4h+WFgw0KICAgICAgICBjb25zdCBvcHRpb24gPSB0aGlzLmNoYXJ0cy5waWVDaGFydC5nZXRPcHRpb24oKTsNCiAgICAgICAgaWYgKG9wdGlvbiAmJiBvcHRpb24uc2VyaWVzICYmIG9wdGlvbi5zZXJpZXNbMF0pIHsNCiAgICAgICAgICBvcHRpb24uc2VyaWVzWzBdLmRhdGEgPSBbDQogICAgICAgICAgICB7IHZhbHVlOiAoZGF0YS5wcmV2ZW50aW9uQ29zdCAvIDEwMDAwKS50b0ZpeGVkKDIpLCBuYW1lOiAn6aKE6Ziy5oiQ5pysJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzkzQzVGRCcgfSB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogKGRhdGEuYXBwcmFpc2FsQ29zdCAvIDEwMDAwKS50b0ZpeGVkKDIpLCBuYW1lOiAn6Ym05a6a5oiQ5pysJywgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzg2RUZBQycgfSB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogKGRhdGEuaW50ZXJuYWxDb3N0IC8gMTAwMDApLnRvRml4ZWQoMiksIG5hbWU6ICflhoXpg6jmjZ/lpLHmiJDmnKwnLCBpdGVtU3R5bGU6IHsgY29sb3I6ICcjRkRFNjhBJyB9IH0sDQogICAgICAgICAgICB7IHZhbHVlOiAoZGF0YS5leHRlcm5hbENvc3QgLyAxMDAwMCkudG9GaXhlZCgyKSwgbmFtZTogJ+WklumDqOaNn+WkseaIkOacrCcsIGl0ZW1TdHlsZTogeyBjb2xvcjogJyNGQ0E1QTUnIH0gfSwNCiAgICAgICAgICBdLA0KICAgICAgICAgICAgdGhpcy5jaGFydHMucGllQ2hhcnQuc2V0T3B0aW9uKG9wdGlvbik7DQogICAgICAgICAgLy8gY29uc29sZS5sb2coJ+mlvOWbvuaVsOaNruW3suabtOaWsCcpOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOabtOaWsOWkmue6v+WbvuaVsOaNrg0KICAgIHVwZGF0ZU11bHRpTGluZUNoYXJ0KGRhdGEpIHsNCiAgICAgIGlmICh0aGlzLmNoYXJ0cy5tdWx0aUxpbmVDaGFydCAmJiBkYXRhKSB7DQogICAgICAgIC8vIOWfuuS6juS8muiuoeacn+eUn+aIkOaciOS7veagh+etvuWSjOWvueW6lOeahOW5tOaciOaVsOWtlw0KICAgICAgICBjb25zdCBtb250aHMgPSB0aGlzLmdlbmVyYXRlQ29tYm9DaGFydE1vbnRoc0J5QWNjb3VudGluZ1BlcmlvZCgpOw0KICAgICAgICBjb25zdCB5ZWFyTW9udGhzID0gdGhpcy5nZW5lcmF0ZVllYXJNb250aHNCeUFjY291bnRpbmdQZXJpb2QoKTsNCg0KICAgICAgICAvLyDlpITnkIblkITnp43miJDmnKzmlbDmja7vvIzovazmjaLkuLrkuIflhYMNCiAgICAgICAgY29uc3QgcHJldmVudGlvbkRhdGEgPSB0aGlzLnByb2Nlc3NNYXBEYXRhKGRhdGEucHJldmVudGlvbkNvc3RNYXAsIHllYXJNb250aHMsIHRydWUpOw0KICAgICAgICBjb25zdCBhcHByYWlzYWxEYXRhID0gdGhpcy5wcm9jZXNzTWFwRGF0YShkYXRhLmFwcHJhaXNhbENvc3RNYXAsIHllYXJNb250aHMsIHRydWUpOw0KICAgICAgICBjb25zdCBpbnRlcm5hbERhdGEgPSB0aGlzLnByb2Nlc3NNYXBEYXRhKGRhdGEuaW50ZXJuYWxDb3N0TWFwLCB5ZWFyTW9udGhzLCB0cnVlKTsNCiAgICAgICAgY29uc3QgZXh0ZXJuYWxEYXRhID0gdGhpcy5wcm9jZXNzTWFwRGF0YShkYXRhLmV4dGVybmFsQ29zdE1hcCwgeWVhck1vbnRocywgdHJ1ZSk7DQoNCiAgICAgICAgLy8g5pu05paw5aSa57q/5Zu+55qE6YWN572uDQogICAgICAgIGNvbnN0IG9wdGlvbiA9IHsNCiAgICAgICAgICB4QXhpczogew0KICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICAgIGJvdW5kYXJ5R2FwOiBmYWxzZSwNCiAgICAgICAgICAgIGRhdGE6IG1vbnRocy5tYXAobW9udGggPT4gYCR7bW9udGh95pyIYCksIC8vIOagvOW8j+WMluS4uiJY5pyIIg0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicsDQogICAgICAgICAgICAgIHJvdGF0ZTogMCwgLy8g5rC05bmz5pi+56S65qCH562+DQogICAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHNlcmllczogWw0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBuYW1lOiAn6aKE6Ziy5oiQ5pysJywNCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgICBkYXRhOiBwcmV2ZW50aW9uRGF0YSwNCiAgICAgICAgICAgICAgc21vb3RoOiB0cnVlLCAvLyDlkK/nlKjlubPmu5Hmm7Lnur8NCiAgICAgICAgICAgICAgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzkzQzVGRCcsIHdpZHRoOiAzIH0sDQogICAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyM5M0M1RkQnIH0sDQogICAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICAgIHN5bWJvbFNpemU6IDYNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIG5hbWU6ICfpibTlrprmiJDmnKwnLA0KICAgICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICAgIGRhdGE6IGFwcHJhaXNhbERhdGEsDQogICAgICAgICAgICAgIHNtb290aDogdHJ1ZSwgLy8g5ZCv55So5bmz5ruR5puy57q/DQogICAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyM4NkVGQUMnLCB3aWR0aDogMyB9LA0KICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjODZFRkFDJyB9LA0KICAgICAgICAgICAgICBzeW1ib2w6ICdjaXJjbGUnLA0KICAgICAgICAgICAgICBzeW1ib2xTaXplOiA2DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBuYW1lOiAn5YaF6YOo5o2f5aSx5oiQ5pysJywNCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgICBkYXRhOiBpbnRlcm5hbERhdGEsDQogICAgICAgICAgICAgIHNtb290aDogdHJ1ZSwgLy8g5ZCv55So5bmz5ruR5puy57q/DQogICAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyNGREU2OEEnLCB3aWR0aDogMyB9LA0KICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjRkRFNjhBJyB9LA0KICAgICAgICAgICAgICBzeW1ib2w6ICdjaXJjbGUnLA0KICAgICAgICAgICAgICBzeW1ib2xTaXplOiA2DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBuYW1lOiAn5aSW6YOo5o2f5aSx5oiQ5pysJywNCiAgICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgICBkYXRhOiBleHRlcm5hbERhdGEsDQogICAgICAgICAgICAgIHNtb290aDogdHJ1ZSwgLy8g5ZCv55So5bmz5ruR5puy57q/DQogICAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyNGQ0E1QTUnLCB3aWR0aDogMyB9LA0KICAgICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjRkNBNUE1JyB9LA0KICAgICAgICAgICAgICBzeW1ib2w6ICdjaXJjbGUnLA0KICAgICAgICAgICAgICBzeW1ib2xTaXplOiA2DQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9Ow0KDQogICAgICAgIHRoaXMuY2hhcnRzLm11bHRpTGluZUNoYXJ0LnNldE9wdGlvbihvcHRpb24pOw0KICAgICAgICBjb25zb2xlLmxvZygn5aSa57q/5Zu+5pWw5o2u5bey5pu05pawJyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhk1hcOaVsOaNru+8jOagueaNruW5tOaciOWMuemFjeWvueW6lOeahOWAvA0KICAgIHByb2Nlc3NNYXBEYXRhKGNvc3RNYXAsIHllYXJNb250aHMsIGNvbnZlcnRUb1dhbll1YW4gPSBmYWxzZSkgew0KICAgICAgaWYgKCFjb3N0TWFwKSByZXR1cm4gbmV3IEFycmF5KHllYXJNb250aHMubGVuZ3RoKS5maWxsKDApOw0KDQogICAgICByZXR1cm4geWVhck1vbnRocy5tYXAoeWVhck1vbnRoID0+IHsNCiAgICAgICAgY29uc3QgdmFsdWUgPSBjb3N0TWFwW3llYXJNb250aF0gfHwgMDsNCiAgICAgICAgcmV0dXJuIGNvbnZlcnRUb1dhbll1YW4gPyAodmFsdWUgLyAxMDAwMCkudG9GaXhlZCgyKSA6IHZhbHVlOw0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOeUn+aIkOaciOS7veagh+etvu+8iOW9k+WJjeaciOS7veWPiuWJjeS6lOS4quaciOS7ve+8iQ0KICAgIGdlbmVyYXRlTW9udGhMYWJlbHMoKSB7DQogICAgICBjb25zdCBtb250aHMgPSBbXTsNCiAgICAgIGNvbnN0IHllYXJNb250aHMgPSBbXTsNCiAgICAgIGNvbnN0IGN1cnJlbnREYXRlID0gbmV3IERhdGUoKTsNCg0KICAgICAgZm9yIChsZXQgaSA9IDU7IGkgPj0gMDsgaS0tKSB7DQogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShjdXJyZW50RGF0ZS5nZXRGdWxsWWVhcigpLCBjdXJyZW50RGF0ZS5nZXRNb250aCgpIC0gaSwgMSk7DQogICAgICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCk7DQogICAgICAgIGNvbnN0IG1vbnRoID0gZGF0ZS5nZXRNb250aCgpICsgMTsNCg0KICAgICAgICBtb250aHMucHVzaChgJHttb250aH3mnIhgKTsNCiAgICAgICAgeWVhck1vbnRocy5wdXNoKHBhcnNlSW50KGAke3llYXJ9JHtTdHJpbmcobW9udGgpLnBhZFN0YXJ0KDIsICcwJyl9YCkpOw0KICAgICAgfQ0KDQogICAgICByZXR1cm4geyBtb250aHMsIHllYXJNb250aHMgfTsNCiAgICB9LA0KDQogICAgLy8g5Yi35paw5Zu+6KGo5pWw5o2uDQogICAgcmVmcmVzaENoYXJ0RGF0YSgpIHsNCiAgICAgIC8vIOWPquacieW9k+aIkOacrOS4reW/g+WSjOS8muiuoeacn+mDveacieWAvOaXtuaJjeWIt+aWsA0KICAgICAgaWYgKCF0aGlzLmNvc3RDZW50ZXIgfHwgIXRoaXMuYWNjb3VudGluZ1BlcmlvZCB8fCAodGhpcy5jb250YWluVHlwZSAhPT0gMiAmJiB0aGlzLmNvbnRhaW5UeXBlICE9PSAxKSkgew0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIHRoaXMuZ2V0UXVhbGl0eUNvc3REZXRhaWwoKTsNCiAgICAgIHRoaXMuZ2V0UGllQ2hhcnREYXRhKCk7DQogICAgICB0aGlzLmdldE11bHRpTGluZUNoYXJ0RGF0YSgpOw0KICAgICAgdGhpcy5nZXRFeHRlcm5hbENvc3REZXRhaWwoKTsNCiAgICAgIHRoaXMuZ2V0SW50ZXJuYWxDb3N0RGV0YWlsKCk7DQogICAgICB0aGlzLmdldENvbWJvQ2hhcnREZXRhaWwoKTsNCiAgICAgIHRoaXMuZ2V0V2F0ZXJmYWxsQ2hhcnREZXRhaWwoKTsNCiAgICAgIHRoaXMuZ2V0U2NyYXBMb3NzQ2hhcnREZXRhaWxzRGV0YWlsKCk7DQogICAgICB0aGlzLmdldFF1YWxpdHlPYmplY3Rpb25Mb3NzRGV0YWlsKCk7DQogICAgICB0aGlzLmdldEZhY3RvcnlSZWplY3Rpb25DaGFydERldGFpbCgpOw0KICAgICAgdGhpcy5nZXRGYWN0b3J5U2NyYXBDaGFydERldGFpbCgpOw0KICAgICAgdGhpcy5nZXRGYWN0b3J5Q29udHJhY3RDaGFydERldGFpbCgpOw0KICAgICAgdGhpcy5nZXRGYWN0b3J5UmV0dXJuQ2hhcnREZXRhaWwoKTsNCg0KICAgICAgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg5YW25LuW5Zu+6KGo55qE5pWw5o2u5Yi35pawDQogICAgICAvLyB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOW3suWIh+aNouWIsOaIkOacrOS4reW/gzogJHt0aGlzLmNvc3RDZW50ZXJ9LCDkvJrorqHmnJ86ICR7dGhpcy5hY2NvdW50aW5nUGVyaW9kfWApOw0KICAgIH0sDQoNCiAgICAvKiog5p+l6K+i5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnJlZnJlc2hDaGFydERhdGEoKTsNCiAgICB9LA0KDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICAvLyDph43nva7kuLrpu5jorqTlgLwNCiAgICAgIGlmICh0aGlzLmNvc3RDZW50ZXJPcHRpb25zLmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy5jb3N0Q2VudGVyID0gdGhpcy5jb3N0Q2VudGVyT3B0aW9uc1swXS5rZXk7DQogICAgICB9DQoNCiAgICAgIC8vIOiOt+WPlum7mOiupOS8muiuoeacnw0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsNCiAgICAgIGNvbnN0IHllYXIgPSBub3cuZ2V0RnVsbFllYXIoKTsNCiAgICAgIGNvbnN0IG1vbnRoID0gbm93LmdldE1vbnRoKCk7DQogICAgICBjb25zdCBwcmV2TW9udGggPSBtb250aCA9PT0gMCA/IDEyIDogbW9udGg7DQogICAgICBjb25zdCBwcmV2WWVhciA9IG1vbnRoID09PSAwID8geWVhciAtIDEgOiB5ZWFyOw0KICAgICAgdGhpcy5hY2NvdW50aW5nUGVyaW9kID0gYCR7cHJldlllYXJ9LSR7U3RyaW5nKHByZXZNb250aCkucGFkU3RhcnQoMiwgJzAnKX1gOw0KDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+afpeivouadoeS7tuW3sumHjee9ricpOw0KICAgIH0sDQoNCiAgICBpbml0Q2hhcnRzKCkgew0KICAgICAgY29uc3QgVEhFTUUgPSAnZGFyaycNCg0KICAgICAgLy8g5a6a5LmJ5ZWG5Yqh6aOO5reh6Imy57O76Imy5b2p5pa55qGIDQogICAgICB0aGlzLmJ1c2luZXNzQ29sb3JzID0gew0KICAgICAgICBsaWdodDogWycjOTNDNUZEJywgJyM4NkVGQUMnLCAnI0ZERTY4QScsICcjRkNBNUE1JywgJyNDNEI1RkQnLCAnIzdERDNGQycsICcjRjlBOEQ0JywgJyNCRUYyNjQnXSwNCiAgICAgICAgZ3JhZGllbnQ6IFsNCiAgICAgICAgICB7IG9mZnNldDogMCwgY29sb3I6ICcjM0I4MkY2JyB9LA0KICAgICAgICAgIHsgb2Zmc2V0OiAxLCBjb2xvcjogJyMxRTQwQUYnIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KDQogICAgICAvLyDliJ3lp4vljJbmiYDmnInlm77ooagNCiAgICAgIHRoaXMuY2hhcnRzLnBpZUNoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMucGllQ2hhcnQsIFRIRU1FKQ0KICAgICAgdGhpcy5jaGFydHMubXVsdGlMaW5lQ2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy5tdWx0aUxpbmVDaGFydCwgVEhFTUUpDQogICAgICB0aGlzLmNoYXJ0cy5leHRlcm5hbENvc3REZXRhaWxDaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLmV4dGVybmFsQ29zdERldGFpbENoYXJ0LCBUSEVNRSkNCiAgICAgIHRoaXMuY2hhcnRzLmludGVybmFsQ29zdERldGFpbENoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMuaW50ZXJuYWxDb3N0RGV0YWlsQ2hhcnQsIFRIRU1FKQ0KICAgICAgdGhpcy5jaGFydHMud2F0ZXJmYWxsQ2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy53YXRlcmZhbGxDaGFydCwgVEhFTUUpDQogICAgICB0aGlzLmNoYXJ0cy5jb21ib0NoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMuY29tYm9DaGFydCwgVEhFTUUpDQogICAgICB0aGlzLmNoYXJ0cy5zY3JhcExvc3NDaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLnNjcmFwTG9zc0NoYXJ0LCBUSEVNRSkNCiAgICAgIHRoaXMuY2hhcnRzLnF1YWxpdHlPYmplY3Rpb25DaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLnF1YWxpdHlPYmplY3Rpb25DaGFydCwgVEhFTUUpDQogICAgICB0aGlzLmNoYXJ0cy5mYWN0b3J5UmVqZWN0aW9uQ2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy5mYWN0b3J5UmVqZWN0aW9uQ2hhcnQsIFRIRU1FKQ0KICAgICAgdGhpcy5jaGFydHMuZmFjdG9yeVNjcmFwQ2hhcnQgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy5mYWN0b3J5U2NyYXBDaGFydCwgVEhFTUUpDQogICAgICB0aGlzLmNoYXJ0cy5mYWN0b3J5Q29udHJhY3RDaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLmZhY3RvcnlDb250cmFjdENoYXJ0LCBUSEVNRSkNCiAgICAgIHRoaXMuY2hhcnRzLmZhY3RvcnlSZXR1cm5DaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLmZhY3RvcnlSZXR1cm5DaGFydCwgVEhFTUUpDQoNCiAgICAgIC8vIOmFjee9ruaJgOacieWbvuihqA0KICAgICAgdGhpcy5zZXRQaWVDaGFydE9wdGlvbigpDQogICAgICB0aGlzLnNldE11bHRpTGluZUNoYXJ0T3B0aW9uKCkNCiAgICAgIHRoaXMuc2V0RXh0ZXJuYWxDb3N0RGV0YWlsQ2hhcnRPcHRpb24oKQ0KICAgICAgdGhpcy5zZXRJbnRlcm5hbENvc3REZXRhaWxDaGFydE9wdGlvbigpDQogICAgICB0aGlzLnNldFdhdGVyZmFsbENoYXJ0T3B0aW9uKCkNCiAgICAgIHRoaXMuc2V0Q29tYm9DaGFydE9wdGlvbigpDQogICAgICB0aGlzLnNldFNjcmFwTG9zc0NoYXJ0T3B0aW9uKCkNCiAgICAgIHRoaXMuc2V0UXVhbGl0eU9iamVjdGlvbkNoYXJ0T3B0aW9uKCkNCiAgICAgIHRoaXMuc2V0RmFjdG9yeVJlamVjdGlvbkNoYXJ0T3B0aW9uKCkNCiAgICAgIHRoaXMuc2V0RmFjdG9yeVNjcmFwQ2hhcnRPcHRpb24oKQ0KICAgICAgdGhpcy5zZXRGYWN0b3J5Q29udHJhY3RDaGFydE9wdGlvbigpDQogICAgICB0aGlzLnNldEZhY3RvcnlSZXR1cm5DaGFydE9wdGlvbigpDQogICAgfSwNCg0KICAgIHNldFBpZUNoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMucGllQ2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgY29sb3I6IFsnIzkzQzVGRCcsICcjODZFRkFDJywgJyNGREU2OEEnLCAnI0ZDQTVBNSddLA0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2l0ZW0nLA0KICAgICAgICAgIGZvcm1hdHRlcjogKHBhcmFtcykgPT4gew0KICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBwYXJzZUZsb2F0KHBhcmFtcy52YWx1ZSkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZFZhbHVlID0gdmFsdWUudG9TdHJpbmcoKS5yZXBsYWNlKC9cQig/PShcZHszfSkrKD8hXGQpKS9nLCAnLCcpOw0KICAgICAgICAgICAgcmV0dXJuIGAke3BhcmFtcy5zZXJpZXNOYW1lfSA8YnIvPiR7cGFyYW1zLm5hbWV9OiAke2Zvcm1hdHRlZFZhbHVlfeS4h+WFgyAoJHtwYXJhbXMucGVyY2VudH0lKWA7DQogICAgICAgICAgfSwNCiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDMwLCA0MSwgNTksIDAuOSknLA0KICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzkzQzVGRCcsDQogICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI2ZmZicgfQ0KICAgICAgICB9LA0KICAgICAgICBsZWdlbmQ6IHsNCiAgICAgICAgICB0b3A6ICdib3R0b20nLA0KICAgICAgICAgIGxlZnQ6ICdjZW50ZXInLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNFNUU3RUInLCBmb250U2l6ZTogMTIgfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+aIkOacrOexu+WIqycsDQogICAgICAgICAgdHlwZTogJ3BpZScsDQogICAgICAgICAgcmFkaXVzOiAnNjUlJywNCiAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICBlbXBoYXNpczogew0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIHNoYWRvd0JsdXI6IDE1LA0KICAgICAgICAgICAgICBzaGFkb3dPZmZzZXRYOiAwLA0KICAgICAgICAgICAgICBzaGFkb3dDb2xvcjogJ3JnYmEoMTQ3LCAxOTcsIDI1MywgMC42KScNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIGxhYmVsTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjOUNBM0FGJyB9IH0sDQogICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnI0U1RTdFQicsDQogICAgICAgICAgICBmb3JtYXR0ZXI6IChwYXJhbXMpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBwYXJzZUZsb2F0KHBhcmFtcy52YWx1ZSkudG9GaXhlZCgyKTsNCiAgICAgICAgICAgICAgY29uc3QgZm9ybWF0dGVkVmFsdWUgPSB2YWx1ZS50b1N0cmluZygpLnJlcGxhY2UoL1xCKD89KFxkezN9KSsoPyFcZCkpL2csICcsJyk7DQogICAgICAgICAgICAgIHJldHVybiBgJHtwYXJhbXMubmFtZX0oJHtmb3JtYXR0ZWRWYWx1ZX3kuIflhYMsICR7cGFyYW1zLnBlcmNlbnR9JSlgOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfSwNCg0KDQoNCiAgICBzZXRDb21ib0NoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMuY29tYm9DaGFydC5zZXRPcHRpb24oew0KICAgICAgICBjb2xvcjogWycjRkNBNUE1JywgJyM4NkVGQUMnXSwNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnY3Jvc3MnIH0sDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0NCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgZGF0YTogWyflpLHotKXmiJDmnKwnLCAn5o6n5Yi25oiQ5pysJ10sDQogICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI0U1RTdFQicgfQ0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7IGxlZnQ6ICczJScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgYm91bmRhcnlHYXA6IGZhbHNlLA0KICAgICAgICAgIGRhdGE6IFsnMeaciCcsICcy5pyIJywgJzPmnIgnLCAnNOaciCcsICc15pyIJywgJzbmnIgnXSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicsDQogICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInIC8vIOWxheS4reWvuem9kA0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBuYW1lOiAn5oiQ5pysICjkuIflhYMpJywNCiAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICflpLHotKXmiJDmnKwnLCAvLyDnuqLoibLmm7Lnur8gI0ZDQTVBNQ0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgZGF0YTogWzI4MCwgMjYwLCAyNDAsIDIyMCwgMjAwLCAxODBdLA0KICAgICAgICAgICAgc21vb3RoOiB0cnVlLCAvLyDlkK/nlKjlubPmu5Hmm7Lnur8NCiAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyNGQ0E1QTUnLCB3aWR0aDogMyB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI0ZDQTVBNScgfSwNCiAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICBzeW1ib2xTaXplOiA2DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5o6n5Yi25oiQ5pysJywgLy8g57u/6Imy5puy57q/ICM4NkVGQUMNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIGRhdGE6IFsxMjAsIDEyNSwgMTMwLCAxMzUsIDE0MCwgMTQ1XSwNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZSwgLy8g5ZCv55So5bmz5ruR5puy57q/DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsgY29sb3I6ICcjODZFRkFDJywgd2lkdGg6IDMgfSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyM4NkVGQUMnIH0sDQogICAgICAgICAgICBzeW1ib2w6ICdjaXJjbGUnLA0KICAgICAgICAgICAgc3ltYm9sU2l6ZTogNg0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgc2V0TXVsdGlMaW5lQ2hhcnRPcHRpb24oKSB7DQogICAgICB0aGlzLmNoYXJ0cy5tdWx0aUxpbmVDaGFydC5zZXRPcHRpb24oew0KICAgICAgICBjb2xvcjogWycjOTNDNUZEJywgJyM4NkVGQUMnLCAnI0ZERTY4QScsICcjRkNBNUE1J10sDQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0NCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgZGF0YTogWyfpooTpmLLmiJDmnKwnLCAn6Ym05a6a5oiQ5pysJywgJ+WGhemDqOaNn+WkseaIkOacrCcsICflpJbpg6jmjZ/lpLHmiJDmnKwnXSwNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjRTVFN0VCJyB9DQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsgbGVmdDogJzMlJywgcmlnaHQ6ICc0JScsIGJvdHRvbTogJzMlJywgY29udGFpbkxhYmVsOiB0cnVlIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBib3VuZGFyeUdhcDogZmFsc2UsDQogICAgICAgICAgZGF0YTogWycx5pyIJywgJzLmnIgnLCAnM+aciCcsICc05pyIJywgJzXmnIgnLCAnNuaciCddLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgY29sb3I6ICcjOUNBM0FGJywNCiAgICAgICAgICAgIHJvdGF0ZTogMCwgLy8g5rC05bmz5pi+56S65qCH562+DQogICAgICAgICAgICBhbGlnbjogJ2NlbnRlcicgLy8g5bGF5Lit5a+56b2QDQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgIG5hbWU6ICfmiJDmnKwgKOS4h+WFgyknLA0KICAgICAgICAgIG5hbWVUZXh0U3R5bGU6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMYWJlbDogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9LA0KICAgICAgICAgIHNwbGl0TGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjMzc0MTUxJyB9IH0NCiAgICAgICAgfSwNCiAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+mihOmYsuaIkOacrCcsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBkYXRhOiBbODAsIDgyLCA4NSwgODgsIDkwLCA5NV0sDQogICAgICAgICAgICBzbW9vdGg6IHRydWUsIC8vIOWQr+eUqOW5s+a7keabsue6vw0KICAgICAgICAgICAgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzkzQzVGRCcsIHdpZHRoOiAzIH0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjOTNDNUZEJyB9LA0KICAgICAgICAgICAgc3ltYm9sOiAnY2lyY2xlJywNCiAgICAgICAgICAgIHN5bWJvbFNpemU6IDYNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfpibTlrprmiJDmnKwnLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgZGF0YTogWzEyMCwgMTIyLCAxMjUsIDEyOCwgMTMwLCAxMzVdLA0KICAgICAgICAgICAgc21vb3RoOiB0cnVlLCAvLyDlkK/nlKjlubPmu5Hmm7Lnur8NCiAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyM4NkVGQUMnLCB3aWR0aDogMyB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnIzg2RUZBQycgfSwNCiAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICBzeW1ib2xTaXplOiA2DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5YaF6YOo5o2f5aSx5oiQ5pysJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIGRhdGE6IFs0NTAsIDQzMCwgNDEwLCAzODAsIDM1MCwgMzIwXSwNCiAgICAgICAgICAgIHNtb290aDogdHJ1ZSwgLy8g5ZCv55So5bmz5ruR5puy57q/DQogICAgICAgICAgICBsaW5lU3R5bGU6IHsgY29sb3I6ICcjRkRFNjhBJywgd2lkdGg6IDMgfSwNCiAgICAgICAgICAgIGl0ZW1TdHlsZTogeyBjb2xvcjogJyNGREU2OEEnIH0sDQogICAgICAgICAgICBzeW1ib2w6ICdjaXJjbGUnLA0KICAgICAgICAgICAgc3ltYm9sU2l6ZTogNg0KICAgICAgICAgIH0sDQogICAgICAgICAgew0KICAgICAgICAgICAgbmFtZTogJ+WklumDqOaNn+WkseaIkOacrCcsDQogICAgICAgICAgICB0eXBlOiAnbGluZScsDQogICAgICAgICAgICBkYXRhOiBbMzUwLCAzNDAsIDMxMCwgMjkwLCAyNjAsIDIzMF0sDQogICAgICAgICAgICBzbW9vdGg6IHRydWUsIC8vIOWQr+eUqOW5s+a7keabsue6vw0KICAgICAgICAgICAgbGluZVN0eWxlOiB7IGNvbG9yOiAnI0ZDQTVBNScsIHdpZHRoOiAzIH0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjRkNBNUE1JyB9LA0KICAgICAgICAgICAgc3ltYm9sOiAnY2lyY2xlJywNCiAgICAgICAgICAgIHN5bWJvbFNpemU6IDYNCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH0pDQogICAgfSwNCg0KDQoNCg0KDQogICAgc2V0UGFyZXRvQ2hhcnRPcHRpb24oKSB7DQogICAgICB0aGlzLmNoYXJ0cy5wYXJldG9DaGFydC5zZXRPcHRpb24oew0KICAgICAgICBjb2xvcjogWycjOTNDNUZEJywgJyNGREU2OEEnXSwNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnY3Jvc3MnIH0sDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0NCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogeyByaWdodDogJzIwJScgfSwNCiAgICAgICAgeEF4aXM6IFt7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBbJ+S6p+WTgeaKpeW6nycsICfkuqflk4HmlLnliKQnLCAn6K6+5aSH5pWF6ZqcJywgJ+W3peiJuuW6n+aWmScsICflhbbku5YnXSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGludGVydmFsOiAwLA0KICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJywgLy8g5bGF5Lit5a+56b2QDQogICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnDQogICAgICAgICAgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgfV0sDQogICAgICAgIHlBeGlzOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICAgIG5hbWU6ICfmjZ/lpLHph5Hpop0o5YWDKScsDQogICAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgICAgbmFtZTogJ+e0r+iuoeWNoOavlCcsDQogICAgICAgICAgICBtaW46IDAsDQogICAgICAgICAgICBtYXg6IDEwMCwNCiAgICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgICBmb3JtYXR0ZXI6ICd7dmFsdWV9ICUnLA0KICAgICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0NCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG5hbWU6ICfmjZ/lpLHph5Hpop0nLA0KICAgICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgICBkYXRhOiBbMjgwLCAxMTAsIDM1LCAyMCwgNV0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjOTNDNUZEJyB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn57Sv6K6h5Y2g5q+UJywNCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywNCiAgICAgICAgICAgIHlBeGlzSW5kZXg6IDEsDQogICAgICAgICAgICBkYXRhOiBbNjIuMiwgODYuNywgOTQuNCwgOTguOSwgMTAwXSwNCiAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyNGREU2OEEnLCB3aWR0aDogMyB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI0ZERTY4QScgfSwNCiAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICBzeW1ib2xTaXplOiA4DQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICBzZXRFeHRlcm5hbENvc3REZXRhaWxDaGFydE9wdGlvbigpIHsNCiAgICAgIHRoaXMuY2hhcnRzLmV4dGVybmFsQ29zdERldGFpbENoYXJ0LnNldE9wdGlvbih7DQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgYXhpc1BvaW50ZXI6IHsgdHlwZTogJ3NoYWRvdycgfSwNCiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDMwLCA0MSwgNTksIDAuOSknLA0KICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzkzQzVGRCcsDQogICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI2ZmZicgfSwNCiAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uIChwYXJhbXMpIHsNCiAgICAgICAgICAgIGxldCByZXN1bHQgPSBwYXJhbXNbMF0ubmFtZSArICc8YnIvPic7DQogICAgICAgICAgICBwYXJhbXMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgew0KICAgICAgICAgICAgICBjb25zdCBmb3JtYXR0ZWRWYWx1ZSA9IHBhcnNlRmxvYXQoaXRlbS52YWx1ZSkudG9GaXhlZCgyKS50b1N0cmluZygpLnJlcGxhY2UoL1xCKD89KFxkezN9KSsoPyFcZCkpL2csICcsJyk7DQogICAgICAgICAgICAgIHJlc3VsdCArPSBpdGVtLm1hcmtlciArICcg6YeR6aKdOiAnICsgZm9ybWF0dGVkVmFsdWUgKyAn5LiH5YWDPGJyLz4nOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICByZXR1cm4gcmVzdWx0Ow0KICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogeyBsZWZ0OiAnNSUnLCByaWdodDogJzE1JScsIHRvcDogJzEyJScsIGJvdHRvbTogJzEyJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+mHkemine+8iOS4h+WFg++8iScsDQogICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQoNCiAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgZGF0YTogW10NCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNldEludGVybmFsQ29zdERldGFpbENoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMuaW50ZXJuYWxDb3N0RGV0YWlsQ2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24gKHBhcmFtcykgew0KICAgICAgICAgICAgbGV0IHJlc3VsdCA9IHBhcmFtc1swXS5uYW1lICsgJzxici8+JzsNCiAgICAgICAgICAgIHBhcmFtcy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7DQogICAgICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZFZhbHVlID0gcGFyc2VGbG9hdChpdGVtLnZhbHVlKS50b0ZpeGVkKDIpLnRvU3RyaW5nKCkucmVwbGFjZSgvXEIoPz0oXGR7M30pKyg/IVxkKSkvZywgJywnKTsNCiAgICAgICAgICAgICAgcmVzdWx0ICs9IGl0ZW0ubWFya2VyICsgJyDph5Hpop06ICcgKyBmb3JtYXR0ZWRWYWx1ZSArICfkuIflhYM8YnIvPic7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHJldHVybiByZXN1bHQ7DQogICAgICAgICAgfQ0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7IGxlZnQ6ICc1JScsIHJpZ2h0OiAnMTUlJywgdG9wOiAnMTIlJywgYm90dG9tOiAnMTIlJywgY29udGFpbkxhYmVsOiB0cnVlIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBuYW1lOiAn6YeR6aKd77yI5LiH5YWD77yJJywNCiAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgZGF0YTogW10NCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNldFdhdGVyZmFsbENoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMud2F0ZXJmYWxsQ2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7IGxlZnQ6ICczJScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogWyfliJ3lp4vmiJDmnKwnLCAn5L+u56OoJywgJ+efq+ebtCcsICfmjqLkvKQnLCAn54Ot5aSE55CGJywgJ+aAu+aIkOacrCddLA0KICAgICAgICAgIGF4aXNMYWJlbDogew0KICAgICAgICAgICAgaW50ZXJ2YWw6IDAsDQogICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInLCAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicNCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn6L6F5YqpJywNCiAgICAgICAgICAgIHR5cGU6ICdiYXInLA0KICAgICAgICAgICAgc3RhY2s6ICfmgLvph48nLA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgwLDAsMCwwKScsDQogICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAncmdiYSgwLDAsMCwwKScsDQogICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAwDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZW1waGFzaXM6IHsNCiAgICAgICAgICAgICAgaXRlbVN0eWxlOiB7DQogICAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDAsMCwwLDApJw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZGF0YTogWzAsIDAsIDUwLCA4MCwgMTA1LCAwXQ0KICAgICAgICAgIH0sDQogICAgICAgIF0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNldFNjcmFwTG9zc0NoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMuc2NyYXBMb3NzQ2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7IGxlZnQ6ICczJScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogW10sDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnLA0KICAgICAgICAgICAgaW50ZXJ2YWw6IDAsIC8vIOaYvuekuuaJgOacieagh+etvg0KICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+aKpeW6n+aNn+WkseaIkOacrCcsDQogICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgZGF0YTogW10NCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNldFF1YWxpdHlPYmplY3Rpb25DaGFydE9wdGlvbigpIHsNCiAgICAgIHRoaXMuY2hhcnRzLnF1YWxpdHlPYmplY3Rpb25DaGFydC5zZXRPcHRpb24oew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgIGF4aXNQb2ludGVyOiB7IHR5cGU6ICdzaGFkb3cnIH0sDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0sDQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsgbGVmdDogJzMlJywgcmlnaHQ6ICc0JScsIGJvdHRvbTogJzMlJywgY29udGFpbkxhYmVsOiB0cnVlIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBbXSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicsDQogICAgICAgICAgICBpbnRlcnZhbDogMCwgLy8g5pi+56S65omA5pyJ5qCH562+DQogICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInIC8vIOWxheS4reWvuem9kA0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBuYW1lOiAn6YeR6aKdICjlhYMpJywNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICBuYW1lOiAn6LSo6YeP5byC6K6u5o2f5aSx5oiQ5pysJywNCiAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICBkYXRhOiBbXQ0KICAgICAgICB9XQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgc2V0RmFjdG9yeVJlamVjdGlvbkNoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMuZmFjdG9yeVJlamVjdGlvbkNoYXJ0LnNldE9wdGlvbih7DQogICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsDQogICAgICAgICAgYXhpc1BvaW50ZXI6IHsgdHlwZTogJ3NoYWRvdycgfSwNCiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDMwLCA0MSwgNTksIDAuOSknLA0KICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzkzQzVGRCcsDQogICAgICAgICAgdGV4dFN0eWxlOiB7IGNvbG9yOiAnI2ZmZicgfSwNCiAgICAgICAgfSwNCiAgICAgICAgZ3JpZDogeyBsZWZ0OiAnOCUnLCByaWdodDogJzQlJywgYm90dG9tOiAnMyUnLCBjb250YWluTGFiZWw6IHRydWUgfSwNCiAgICAgICAgeEF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLA0KICAgICAgICAgIGRhdGE6IFtdLCAvLyDliqjmgIHmlbDmja7vvIznlLF1cGRhdGVGYWN0b3J5UmVqZWN0aW9uQ2hhcnTmlrnms5XloavlhYUNCiAgICAgICAgICBheGlzTGFiZWw6IHsNCiAgICAgICAgICAgIGNvbG9yOiAnIzlDQTNBRicsDQogICAgICAgICAgICBpbnRlcnZhbDogMCwgLy8g5pi+56S65omA5pyJ5qCH562+DQogICAgICAgICAgICByb3RhdGU6IDAsIC8vIOawtOW5s+aYvuekuuagh+etvg0KICAgICAgICAgICAgYWxpZ246ICdjZW50ZXInIC8vIOWxheS4reWvuem9kA0KICAgICAgICAgIH0sDQogICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgIH0sDQogICAgICAgIHlBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ3ZhbHVlJywNCiAgICAgICAgICBuYW1lOiAn6YeR6aKdICjkuIflhYMpJywNCiAgICAgICAgICBuYW1lVGV4dFN0eWxlOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfSwNCiAgICAgICAgICBzcGxpdExpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzM3NDE1MScgfSB9DQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogW3sNCiAgICAgICAgICBuYW1lOiAn5pS55Yik6YeR6aKdJywNCiAgICAgICAgICB0eXBlOiAnYmFyJywNCiAgICAgICAgICBkYXRhOiBbXSAvLyDliqjmgIHmlbDmja7vvIznlLF1cGRhdGVGYWN0b3J5UmVqZWN0aW9uQ2hhcnTmlrnms5XloavlhYUNCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNldEZhY3RvcnlTY3JhcENoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMuZmFjdG9yeVNjcmFwQ2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7IGxlZnQ6ICc4JScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogW10sIC8vIOWKqOaAgeaVsOaNru+8jOeUsXVwZGF0ZUZhY3RvcnlTY3JhcENoYXJ05pa55rOV5aGr5YWFDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnLA0KICAgICAgICAgICAgaW50ZXJ2YWw6IDAsIC8vIOaYvuekuuaJgOacieagh+etvg0KICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+aKpeW6n+mHkeminScsDQogICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgZGF0YTogW10gLy8g5Yqo5oCB5pWw5o2u77yM55SxdXBkYXRlRmFjdG9yeVNjcmFwQ2hhcnTmlrnms5XloavlhYUNCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNldEZhY3RvcnlDb250cmFjdENoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMuZmFjdG9yeUNvbnRyYWN0Q2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnc2hhZG93JyB9LA0KICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMzAsIDQxLCA1OSwgMC45KScsDQogICAgICAgICAgYm9yZGVyQ29sb3I6ICcjOTNDNUZEJywNCiAgICAgICAgICB0ZXh0U3R5bGU6IHsgY29sb3I6ICcjZmZmJyB9LA0KICAgICAgICB9LA0KICAgICAgICBncmlkOiB7IGxlZnQ6ICc4JScsIHJpZ2h0OiAnNCUnLCBib3R0b206ICczJScsIGNvbnRhaW5MYWJlbDogdHJ1ZSB9LA0KICAgICAgICB4QXhpczogew0KICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsDQogICAgICAgICAgZGF0YTogW10sIC8vIOWKqOaAgeaVsOaNru+8jOeUsXVwZGF0ZUZhY3RvcnlDb250cmFjdENoYXJ05pa55rOV5aGr5YWFDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnLA0KICAgICAgICAgICAgaW50ZXJ2YWw6IDAsIC8vIOaYvuekuuaJgOacieagh+etvg0KICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+iEseWQiOWQjOmHkeminScsDQogICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgZGF0YTogW10gLy8g5Yqo5oCB5pWw5o2u77yM55SxdXBkYXRlRmFjdG9yeUNvbnRyYWN0Q2hhcnTmlrnms5XloavlhYUNCiAgICAgICAgfV0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIHNldEZhY3RvcnlSZXR1cm5DaGFydE9wdGlvbigpIHsNCiAgICAgIHRoaXMuY2hhcnRzLmZhY3RvcnlSZXR1cm5DaGFydC5zZXRPcHRpb24oew0KICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgdHJpZ2dlcjogJ2F4aXMnLA0KICAgICAgICAgIGF4aXNQb2ludGVyOiB7IHR5cGU6ICdzaGFkb3cnIH0sDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0sDQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsgbGVmdDogJzglJywgcmlnaHQ6ICc0JScsIGJvdHRvbTogJzMlJywgY29udGFpbkxhYmVsOiB0cnVlIH0sDQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBbXSwgLy8g5Yqo5oCB5pWw5o2u77yM55SxdXBkYXRlRmFjdG9yeVJldHVybkNoYXJ05pa55rOV5aGr5YWFDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnLA0KICAgICAgICAgICAgaW50ZXJ2YWw6IDAsIC8vIOaYvuekuuaJgOacieagh+etvg0KICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICB5QXhpczogew0KICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgbmFtZTogJ+mHkeminSAo5YWDKScsDQogICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICBheGlzTGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjNEI1NTYzJyB9IH0sDQogICAgICAgICAgc3BsaXRMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyMzNzQxNTEnIH0gfQ0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFt7DQogICAgICAgICAgbmFtZTogJ+mAgOi0p+mHkeminScsDQogICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgZGF0YTogW10gLy8g5Yqo5oCB5pWw5o2u77yM55SxdXBkYXRlRmFjdG9yeVJldHVybkNoYXJ05pa55rOV5aGr5YWFDQogICAgICAgIH1dDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICBzZXREdWFsWUNoYXJ0T3B0aW9uKCkgew0KICAgICAgdGhpcy5jaGFydHMuZHVhbFlDaGFydC5zZXRPcHRpb24oew0KICAgICAgICBjb2xvcjogWycjOTNDNUZEJywgJyNGREU2OEEnXSwNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywNCiAgICAgICAgICBheGlzUG9pbnRlcjogeyB0eXBlOiAnY3Jvc3MnIH0sDQogICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgzMCwgNDEsIDU5LCAwLjkpJywNCiAgICAgICAgICBib3JkZXJDb2xvcjogJyM5M0M1RkQnLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNmZmYnIH0NCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgZGF0YTogWyfkuqfph48o5ZCoKScsICflkKjpkqLmiJDmnKwo5YWDKSddLA0KICAgICAgICAgIHRleHRTdHlsZTogeyBjb2xvcjogJyNFNUU3RUInIH0NCiAgICAgICAgfSwNCiAgICAgICAgeEF4aXM6IFt7DQogICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywNCiAgICAgICAgICBkYXRhOiBbJzHmnIgnLCAnMuaciCcsICcz5pyIJywgJzTmnIgnLCAnNeaciCcsICc25pyIJ10sDQogICAgICAgICAgYXhpc0xhYmVsOiB7DQogICAgICAgICAgICBjb2xvcjogJyM5Q0EzQUYnLA0KICAgICAgICAgICAgcm90YXRlOiAwLCAvLyDmsLTlubPmmL7npLrmoIfnrb4NCiAgICAgICAgICAgIGFsaWduOiAnY2VudGVyJyAvLyDlsYXkuK3lr7npvZANCiAgICAgICAgICB9LA0KICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfQ0KICAgICAgICB9XSwNCiAgICAgICAgeUF4aXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICB0eXBlOiAndmFsdWUnLA0KICAgICAgICAgICAgbmFtZTogJ+S6p+mHjyjlkKgpJywNCiAgICAgICAgICAgIG5hbWVUZXh0U3R5bGU6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgICAgYXhpc0xhYmVsOiB7IGNvbG9yOiAnIzlDQTNBRicgfSwNCiAgICAgICAgICAgIGF4aXNMaW5lOiB7IGxpbmVTdHlsZTogeyBjb2xvcjogJyM0QjU1NjMnIH0gfSwNCiAgICAgICAgICAgIHNwbGl0TGluZTogeyBsaW5lU3R5bGU6IHsgY29sb3I6ICcjMzc0MTUxJyB9IH0NCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHR5cGU6ICd2YWx1ZScsDQogICAgICAgICAgICBuYW1lOiAn5ZCo6ZKi5oiQ5pysKOWFgyknLA0KICAgICAgICAgICAgbmFtZVRleHRTdHlsZTogeyBjb2xvcjogJyM5Q0EzQUYnIH0sDQogICAgICAgICAgICBheGlzTGFiZWw6IHsgY29sb3I6ICcjOUNBM0FGJyB9LA0KICAgICAgICAgICAgYXhpc0xpbmU6IHsgbGluZVN0eWxlOiB7IGNvbG9yOiAnIzRCNTU2MycgfSB9DQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5Lqn6YePKOWQqCknLA0KICAgICAgICAgICAgdHlwZTogJ2JhcicsDQogICAgICAgICAgICBkYXRhOiBbODAwMDAsIDgyMDAwLCA4NTAwMCwgODMwMDAsIDg4MDAwLCA5MDAwMF0sDQogICAgICAgICAgICBpdGVtU3R5bGU6IHsgY29sb3I6ICcjOTNDNUZEJyB9DQogICAgICAgICAgfSwNCiAgICAgICAgICB7DQogICAgICAgICAgICBuYW1lOiAn5ZCo6ZKi5oiQ5pysKOWFgyknLA0KICAgICAgICAgICAgdHlwZTogJ2xpbmUnLA0KICAgICAgICAgICAgeUF4aXNJbmRleDogMSwNCiAgICAgICAgICAgIGRhdGE6IFsxMy4xLCAxMi44LCAxMi41LCAxMi42LCAxMi4yLCAxMi4wXSwNCiAgICAgICAgICAgIGxpbmVTdHlsZTogeyBjb2xvcjogJyNGREU2OEEnLCB3aWR0aDogMyB9LA0KICAgICAgICAgICAgaXRlbVN0eWxlOiB7IGNvbG9yOiAnI0ZERTY4QScgfSwNCiAgICAgICAgICAgIHN5bWJvbDogJ2NpcmNsZScsDQogICAgICAgICAgICBzeW1ib2xTaXplOiA4DQogICAgICAgICAgfQ0KICAgICAgICBdDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICByZXNpemVDaGFydHMoKSB7DQogICAgICBPYmplY3QudmFsdWVzKHRoaXMuY2hhcnRzKS5mb3JFYWNoKGNoYXJ0ID0+IHsNCiAgICAgICAgaWYgKGNoYXJ0KSB7DQogICAgICAgICAgY2hhcnQucmVzaXplKCkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2JA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qualityCost/dashboard", "sourcesContent": ["<template>\r\n  <div class=\"quality-cost-dashboard\">\r\n    <header class=\"header\">\r\n      <div class=\"header-wrapper\">\r\n        <h1>兴澄特钢质量成本看板</h1>\r\n        <!-- 标题右下角筛选区域 -->\r\n        <div class=\"header-filters\">\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">成本中心：</span>\r\n            <el-select v-model=\"costCenter\" placeholder=\"请选择成本中心\" style=\"width: 160px;\" :loading=\"costCenterLoading\"\r\n              size=\"small\">\r\n              <el-option v-for=\"item in costCenterOptions\" :key=\"item.key\" :label=\"item.label\" :value=\"item.key\">\r\n              </el-option>\r\n            </el-select>\r\n          </div>\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">会计期：</span>\r\n            <el-date-picker v-model=\"accountingPeriod\" type=\"month\" placeholder=\"请选择年月\" format=\"yyyy-MM\"\r\n              value-format=\"yyyy-MM\" style=\"width: 130px;\" size=\"small\">\r\n            </el-date-picker>\r\n          </div>\r\n          <div class=\"filter-item\">\r\n            <span class=\"label\">质量成本类型：</span>\r\n            <el-select v-model=\"containType\" placeholder=\"请选择质量成本类型\" style=\"width: 130px;\" size=\"small\">\r\n              <el-option label=\"含不列入项\" :value=\"2\"></el-option>\r\n              <el-option label=\"不含列入项\" :value=\"1\"></el-option>\r\n            </el-select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- <p>数据更新时间: {{ updateTime }}</p> -->\r\n    </header>\r\n\r\n    <div class=\"dashboard-grid\">\r\n      <!-- 第四类：核心绩效指标（KPI）看板 -->\r\n      <div class=\"kpi-grid\">\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">{{ costCenter === 'JYXCTZG' ? '销量' : '产量' }}</div>\r\n          <div class=\"value\">{{ formatTonnage(containType === 2 ? qualityCostData.allcTon : qualityCostData.costTon) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costTonUpPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costTonUpPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costTonUpPercent)\">{{ qualityCostDetail.costTonUpPercent\r\n            }}\r\n              vs 上期</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">总金额</div>\r\n          <div class=\"value\">{{ formatAmount(containType === 2 ? qualityCostData.allcEx : qualityCostData.costEx) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costExPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costExPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costExPercent)\">{{ qualityCostDetail.costExPercent }} vs\r\n              上期</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"kpi-card\">\r\n          <div class=\"title\">吨钢成本</div>\r\n          <div class=\"value\">{{ formatUnitCost(containType === 2 ? qualityCostData.allcPerEx : qualityCostData.costPerEx) }}</div>\r\n          <div class=\"comparison\">\r\n            <svg :class=\"['arrow', getPercentageClass(qualityCostDetail.costPerExPercent)]\" fill=\"none\"\r\n              stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n              <path v-if=\"isNegativePercentage(qualityCostDetail.costPerExPercent)\" stroke-linecap=\"round\"\r\n                stroke-linejoin=\"round\" d=\"M17 13l-5 5m0 0l-5-5m5 5V6\"></path>\r\n              <path v-else stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M7 11l5-5m0 0l5 5m-5-5v12\"></path>\r\n            </svg>\r\n            <span :class=\"getPercentageClass(qualityCostDetail.costPerExPercent)\">{{ qualityCostDetail.costPerExPercent\r\n            }}\r\n              vs 上期</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 第2行：质量成本四大类别占比（占1/2宽度）+ 四大质量成本趋势（占1/2宽度） -->\r\n      <div class=\"chart-container\" style=\"grid-column: span 2;\">\r\n        <h3>1. 质量成本四大类别占比</h3>\r\n        <div ref=\"pieChart\" class=\"chart\"></div>\r\n      </div>\r\n      <div class=\"chart-container\" style=\"grid-column: span 2;\">\r\n        <h3>2. 四大质量成本趋势</h3>\r\n        <div ref=\"multiLineChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第3行：外部损失成本构成图表 + 内部损失成本构成图表 - 每个占据行宽的50% -->\r\n      <div class=\"chart-container large-chart\" style=\"grid-column: span 2;\">\r\n        <h3>3. 外部损失成本构成</h3>\r\n        <div ref=\"externalCostDetailChart\" class=\"chart\"></div>\r\n      </div>\r\n      <div class=\"chart-container large-chart\" style=\"grid-column: span 2;\">\r\n        <h3>4. 内部损失成本构成</h3>\r\n        <div ref=\"internalCostDetailChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第3行：产品挽救处理成本分析图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>5. 产品挽救处理成本分析</h3>\r\n        <div ref=\"waterfallChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第4行：产品报废损失明细图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>6. 产品报废损失明细</h3>\r\n        <div ref=\"scrapLossChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第5行：产品质量异议损失明细图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>7. 产品质量异议损失明细</h3>\r\n        <div ref=\"qualityObjectionChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第7行：控制成本 vs 失败成本对比（占满整行） -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>8. \"控制成本\" vs \"失败成本\" 对比</h3>\r\n        <div ref=\"comboChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第8行：各分厂改判汇总图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>9. 各分厂改判汇总</h3>\r\n        <div ref=\"factoryRejectionChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第9行：各分厂报废汇总图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>10. 各分厂报废汇总</h3>\r\n        <div ref=\"factoryScrapChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第10行：各分厂脱合同汇总图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>11. 各分厂脱合同汇总</h3>\r\n        <div ref=\"factoryContractChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n      <!-- 第11行：各分厂退货汇总图表 - 占据整行宽度 -->\r\n      <div class=\"chart-container\" style=\"grid-column: 1 / -1;\">\r\n        <h3>12. 各分厂退货汇总</h3>\r\n        <div ref=\"factoryReturnChart\" class=\"chart\"></div>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport { costCenterlist } from \"@/api/qualityCost/qualityCostDetail\";\r\nimport { getPieChartData, getMultiLineChartData, getQualityCostDetail, getExternalCostDetail, getInternalCostDetail, getComboChartDetail,getWaterfallChartDetail,getScrapLossChartDetailsDetail,getQualityObjectionLossDetail,getFactoryRejectionChartDetail,getFactoryScrapChartDetail,getFactoryContractChartDetail,getFactoryReturnChartDetail } from \"@/api/qualityCost/dashboard\";\r\n\r\nexport default {\r\n  name: 'QualityCostDashboard',\r\n  data() {\r\n    // 获取默认会计期（上个月）\r\n    const getDefaultYearMonth = () => {\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth() + 1; // 1-12\r\n      const day = now.getDate();\r\n      const hour = now.getHours();\r\n\r\n      // 如果今天是本月25号8点前（含25号7:59），则用上个月\r\n      if (day < 28 || (day === 28 && hour < 1)) {\r\n        // 处理1月时的跨年\r\n        const prevMonth = month === 1 ? 12 : month - 1;\r\n        const prevYear = month === 1 ? year - 1 : year;\r\n        return `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n      } else {\r\n        return `${year}-${String(month).padStart(2, '0')}`;\r\n      }\r\n    };\r\n\r\n    return {\r\n      updateTime: '2023-10-27 10:00',\r\n      charts: {},\r\n      // 成本中心和会计期\r\n      costCenter: '',\r\n      accountingPeriod: getDefaultYearMonth(),\r\n      // 质量成本类型，默认值为1（不含列入项）\r\n      containType: 1,\r\n      // 成本中心选项\r\n      costCenterOptions: [],\r\n      costCenterLoading: false,\r\n      qualityCostDetail: {},\r\n      qualityCostData: {}\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听成本中心变化\r\n    costCenter: {\r\n      handler() {\r\n        console.log('成本中心变化:', this.costCenter);\r\n        this.refreshChartData();\r\n      }\r\n    },\r\n    // 监听会计期变化\r\n    accountingPeriod: {\r\n      handler() {\r\n        console.log('会计期变化:', this.accountingPeriod);\r\n        this.refreshChartData();\r\n      }\r\n    },\r\n    // 监听质量成本类型变化\r\n    containType: {\r\n      handler() {\r\n        console.log('质量成本类型变化:', this.containType);\r\n        this.refreshChartData();\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getCostCenterList();\r\n    //质量成本四大类别占比\r\n\r\n    this.initCharts();\r\n    this.resizeObserver = new ResizeObserver(() => {\r\n      this.resizeCharts()\r\n    })\r\n    this.resizeObserver.observe(this.$el)\r\n    window.addEventListener('resize', this.resizeCharts)\r\n  },\r\n  beforeDestroy() {\r\n    // 销毁所有图表实例\r\n    Object.values(this.charts).forEach(chart => {\r\n      if (chart) {\r\n        chart.dispose()\r\n      }\r\n    })\r\n    if (this.resizeObserver) {\r\n      this.resizeObserver.disconnect()\r\n    }\r\n    window.removeEventListener('resize', this.resizeCharts)\r\n  },\r\n  methods: {\r\n    // 判断百分比是否为负数\r\n    isNegativePercentage(percentage) {\r\n      if (!percentage) return false;\r\n      return percentage.toString().startsWith('-');\r\n    },\r\n\r\n    // 根据百分比正负值返回对应的CSS类\r\n    getPercentageClass(percentage) {\r\n      if (!percentage) return 'neutral';\r\n      return this.isNegativePercentage(percentage) ? 'negative' : 'positive';\r\n    },\r\n\r\n    // 格式化数字，最多保留两位小数\r\n    formatNumber(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0';\r\n      }\r\n      // 使用toFixed(2)保留两位小数，然后用parseFloat去掉末尾的0\r\n      return parseFloat(number.toFixed(2)).toString();\r\n    },\r\n\r\n    // 添加千分位分隔符\r\n    addThousandSeparator(num) {\r\n      return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    },\r\n\r\n    // 格式化产量/销量为万吨单位\r\n    formatTonnage(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0万吨';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0万吨';\r\n      }\r\n      // 转换为万吨并保留两位小数，添加千分位分隔符\r\n      const result = (number / 10000).toFixed(2);\r\n      return `${this.addThousandSeparator(result)}万吨`;\r\n    },\r\n\r\n    // 格式化总金额为万元单位\r\n    formatAmount(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0万元';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0万元';\r\n      }\r\n      // 转换为万元并保留两位小数，添加千分位分隔符\r\n      const result = (number / 10000).toFixed(2);\r\n      return `${this.addThousandSeparator(result)}万元`;\r\n    },\r\n\r\n    // 格式化吨钢成本为元/吨单位\r\n    formatUnitCost(num) {\r\n      if (num === null || num === undefined || num === '') {\r\n        return '0元/吨';\r\n      }\r\n      const number = Number(num);\r\n      if (isNaN(number)) {\r\n        return '0元/吨';\r\n      }\r\n      // 保留两位小数并添加单位，添加千分位分隔符\r\n      const result = number.toFixed(2);\r\n      return `${this.addThousandSeparator(result)}元/吨`;\r\n    },\r\n\r\n    getFactoryRejectionChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getFactoryRejectionChartDetail(params).then(response => {\r\n        console.log('getFactoryRejectionChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新WaterfallChart柱状图\r\n          this.updateFactoryRejectionChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取WaterfallChart数据失败:', error);\r\n        this.$message.error('获取WaterfallChart数据失败');\r\n      });\r\n    },\r\n\r\n    getFactoryScrapChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getFactoryScrapChartDetail(params).then(response => {\r\n        console.log('getFactoryScrapChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新各分厂报废汇总柱状图\r\n          this.updateFactoryScrapChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取各分厂报废汇总数据失败:', error);\r\n        this.$message.error('获取各分厂报废汇总数据失败');\r\n      });\r\n    },\r\n\r\n    getFactoryContractChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getFactoryContractChartDetail(params).then(response => {\r\n        console.log('getFactoryContractChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新各分厂脱合同汇总柱状图\r\n          this.updateFactoryContractChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取各分厂脱合同汇总数据失败:', error);\r\n        this.$message.error('获取各分厂脱合同汇总数据失败');\r\n      });\r\n    },\r\n\r\n    getFactoryReturnChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getFactoryReturnChartDetail(params).then(response => {\r\n        console.log('getFactoryReturnChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新各分厂退货汇总柱状图\r\n          this.updateFactoryReturnChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取各分厂退货汇总数据失败:', error);\r\n        this.$message.error('获取各分厂退货汇总数据失败');\r\n      });\r\n    },\r\n\r\n    updateFactoryReturnChart(data) {\r\n      if (this.charts.factoryReturnChart && data) {\r\n        console.log('接收到的FactoryReturnChart数据:', data);\r\n        console.log('factoryReturnMap:', data.factoryReturnMap);\r\n\r\n        // 处理各分厂退货数据，单位为元\r\n        const xAxisData = [];      // x轴分厂名称数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];\r\n\r\n        if (data.factoryReturnMap) {\r\n          // 将factoryReturnMap对象转换为数组，单位为元，保留两位小数\r\n          const dataItems = Object.entries(data.factoryReturnMap).map(([key, value]) => ({\r\n            name: key,    // 分厂名称\r\n            value: (Number(value) || 0).toFixed(2)  // 退货金额，单位为元，保留两位小数\r\n          }));\r\n\r\n          console.log('处理后的分厂退货数据:', dataItems);\r\n\r\n          if (dataItems.length > 0) {\r\n            // 按数值从高到低排序\r\n            dataItems.sort((a, b) => b.value - a.value);\r\n\r\n            // 分离排序后的数据，确保每个柱子颜色不同\r\n            dataItems.forEach((item, index) => {\r\n              xAxisData.push(item.name);\r\n              seriesData.push({\r\n                value: item.value,\r\n                itemStyle: { color: colors[index % colors.length] }\r\n              });\r\n            });\r\n          } else {\r\n            console.warn('没有找到有效的分厂退货数据');\r\n            // 添加默认数据以便测试\r\n            xAxisData.push('无数据');\r\n            seriesData.push({\r\n              value: 0,\r\n              itemStyle: { color: colors[0] }\r\n            });\r\n          }\r\n        } else {\r\n          console.warn('factoryReturnMap数据不存在');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴分厂数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              formatter: function(value) {\r\n                // 在Y轴标签上也显示千分位分隔符\r\n                return value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              }\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '退货金额',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.factoryReturnChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('FactoryReturnChart柱状图数据已更新');\r\n      } else {\r\n        console.error('FactoryReturnChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    updateFactoryRejectionChart(data) {\r\n      if (this.charts.factoryRejectionChart && data) {\r\n        console.log('接收到的FactoryRejectionChart数据:', data);\r\n        console.log('factoryRejectionMap:', data.factoryRejectionMap);\r\n\r\n        // 处理各分厂改判数据，单位为元\r\n        const xAxisData = [];      // x轴分厂名称数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];\r\n\r\n        if (data.factoryRejectionMap) {\r\n          // 将factoryRejectionMap对象转换为数组，单位为元，保留两位小数\r\n          const dataItems = Object.entries(data.factoryRejectionMap).map(([key, value]) => ({\r\n            name: key,    // 分厂名称\r\n            value: (Number(value) || 0).toFixed(2)  // 改判金额，单位为元，保留两位小数\r\n          }));\r\n\r\n          console.log('处理后的分厂改判数据:', dataItems);\r\n\r\n          if (dataItems.length > 0) {\r\n            // 按数值从高到低排序\r\n            dataItems.sort((a, b) => b.value - a.value);\r\n\r\n            // 分离排序后的数据，确保每个柱子颜色不同\r\n            dataItems.forEach((item, index) => {\r\n              xAxisData.push(item.name);\r\n              seriesData.push({\r\n                value: item.value,\r\n                itemStyle: { color: colors[index % colors.length] }\r\n              });\r\n            });\r\n          } else {\r\n            console.warn('没有找到有效的分厂改判数据');\r\n            // 添加默认数据以便测试\r\n            xAxisData.push('无数据');\r\n            seriesData.push({\r\n              value: 0,\r\n              itemStyle: { color: colors[0] }\r\n            });\r\n          }\r\n        } else {\r\n          console.warn('factoryRejectionMap数据不存在');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴分厂数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              formatter: function(value) {\r\n                // 在Y轴标签上也显示千分位分隔符\r\n                return value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              }\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '改判金额',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.factoryRejectionChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('FactoryRejectionChart柱状图数据已更新');\r\n      } else {\r\n        console.error('FactoryRejectionChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    updateFactoryScrapChart(data) {\r\n      if (this.charts.factoryScrapChart && data) {\r\n        console.log('接收到的FactoryScrapChart数据:', data);\r\n        console.log('factoryScrapMap:', data.factoryScrapMap);\r\n\r\n        // 处理各分厂报废数据，单位为元\r\n        const xAxisData = [];      // x轴分厂名称数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];\r\n\r\n        if (data.factoryScrapMap) {\r\n          // 将factoryScrapMap对象转换为数组，单位为元，保留两位小数\r\n          const dataItems = Object.entries(data.factoryScrapMap).map(([key, value]) => ({\r\n            name: key,    // 分厂名称\r\n            value: (Number(value) || 0).toFixed(2)  // 报废金额，单位为元，保留两位小数\r\n          }));\r\n\r\n          console.log('处理后的分厂报废数据:', dataItems);\r\n\r\n          if (dataItems.length > 0) {\r\n            // 按数值从高到低排序\r\n            dataItems.sort((a, b) => b.value - a.value);\r\n\r\n            // 分离排序后的数据，确保每个柱子颜色不同\r\n            dataItems.forEach((item, index) => {\r\n              xAxisData.push(item.name);\r\n              seriesData.push({\r\n                value: item.value,\r\n                itemStyle: { color: colors[index % colors.length] }\r\n              });\r\n            });\r\n          } else {\r\n            console.warn('没有找到有效的分厂报废数据');\r\n            // 添加默认数据以便测试\r\n            xAxisData.push('无数据');\r\n            seriesData.push({\r\n              value: 0,\r\n              itemStyle: { color: colors[0] }\r\n            });\r\n          }\r\n        } else {\r\n          console.warn('factoryScrapMap数据不存在');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴分厂数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              formatter: function(value) {\r\n                // 在Y轴标签上也显示千分位分隔符\r\n                return value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              }\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '报废金额',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.factoryScrapChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('FactoryScrapChart柱状图数据已更新');\r\n      } else {\r\n        console.error('FactoryScrapChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    updateFactoryContractChart(data) {\r\n      if (this.charts.factoryContractChart && data) {\r\n        console.log('接收到的FactoryContractChart数据:', data);\r\n        console.log('factoryContractMap:', data.factoryContractMap);\r\n\r\n        // 处理各分厂脱合同数据，单位为元\r\n        const xAxisData = [];      // x轴分厂名称数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF', '#7DD3FC', '#F9A8D4', '#BEF264', '#A78BFA', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1', '#14B8A6'];\r\n\r\n        if (data.factoryContractMap) {\r\n          // 将factoryContractMap对象转换为数组，单位为元，保留两位小数\r\n          const dataItems = Object.entries(data.factoryContractMap).map(([key, value]) => ({\r\n            name: key,    // 分厂名称\r\n            value: (Number(value) || 0).toFixed(2)  // 脱合同金额，单位为元，保留两位小数\r\n          }));\r\n\r\n          console.log('处理后的分厂脱合同数据:', dataItems);\r\n\r\n          if (dataItems.length > 0) {\r\n            // 按数值从高到低排序\r\n            dataItems.sort((a, b) => b.value - a.value);\r\n\r\n            // 分离排序后的数据，确保每个柱子颜色不同\r\n            dataItems.forEach((item, index) => {\r\n              xAxisData.push(item.name);\r\n              seriesData.push({\r\n                value: item.value,\r\n                itemStyle: { color: colors[index % colors.length] }\r\n              });\r\n            });\r\n          } else {\r\n            console.warn('没有找到有效的分厂脱合同数据');\r\n            // 添加默认数据以便测试\r\n            xAxisData.push('无数据');\r\n            seriesData.push({\r\n              value: 0,\r\n              itemStyle: { color: colors[0] }\r\n            });\r\n          }\r\n        } else {\r\n          console.warn('factoryContractMap数据不存在');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴分厂数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              formatter: function(value) {\r\n                // 在Y轴标签上也显示千分位分隔符\r\n                return value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              }\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '脱合同金额',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.factoryContractChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('FactoryContractChart柱状图数据已更新');\r\n      } else {\r\n        console.error('FactoryContractChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n\r\n    getWaterfallChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getWaterfallChartDetail(params).then(response => {\r\n        console.log('getWaterfallChartDetail:', response);\r\n        if (response.data) {\r\n          // 更新WaterfallChart柱状图\r\n          this.updateWaterfallChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取WaterfallChart数据失败:', error);\r\n        this.$message.error('获取WaterfallChart数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新WaterfallChart柱状图\r\n    updateWaterfallChart(data) {\r\n      if (this.charts.waterfallChart && data) {\r\n        console.log('接收到的WaterfallChart数据:', data);\r\n\r\n        // 处理rescueProject数据\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n\r\n        let dataItems = [];\r\n\r\n        if (data.rescueProject) {\r\n          // 将rescueProject对象转换为数组，转换为万元\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,    // 第一项为维度名称\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)  // 第二项为对应维度的值，转换为万元\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '挽救处理成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.waterfallChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('WaterfallChart柱状图数据已更新');\r\n      } else {\r\n        console.error('WaterfallChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    // 更新ScrapLossChart柱状图\r\n    updateScrapLossChart(data) {\r\n      if (this.charts.scrapLossChart && data) {\r\n        console.log('接收到的ScrapLossChart数据:', data);\r\n        console.log('数据类型:', typeof data);\r\n        console.log('数据键:', Object.keys(data));\r\n\r\n        // 处理报废损失数据，尝试多种可能的数据结构\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n        let dataItems = [];\r\n\r\n        // 尝试不同的数据结构，转换为万元\r\n        if (data.scrapLossMap) {\r\n          // 情况1: 使用scrapLossMap数据（根据实际API返回的数据结构）\r\n          console.log('使用scrapLossMap数据');\r\n          dataItems = Object.entries(data.scrapLossMap).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.rescueProject) {\r\n          // 情况2: 使用rescueProject数据（与WaterfallChart相同）\r\n          console.log('使用rescueProject数据');\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.scrapLoss) {\r\n          // 情况3: 使用scrapLoss数据\r\n          console.log('使用scrapLoss数据');\r\n          dataItems = Object.entries(data.scrapLoss).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.scrapLossProject) {\r\n          // 情况4: 使用scrapLossProject数据\r\n          console.log('使用scrapLossProject数据');\r\n          dataItems = Object.entries(data.scrapLossProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else {\r\n          // 情况5: 直接使用data作为对象\r\n          console.log('直接使用data对象');\r\n          dataItems = Object.entries(data).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '报废损失成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.scrapLossChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('ScrapLossChart柱状图数据已更新');\r\n      } else {\r\n        console.error('ScrapLossChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    // 更新QualityObjectionChart柱状图\r\n    updateQualityObjectionChart(data) {\r\n      if (this.charts.qualityObjectionChart && data) {\r\n        console.log('接收到的QualityObjectionChart数据:', data);\r\n        console.log('数据类型:', typeof data);\r\n        console.log('数据键:', Object.keys(data));\r\n\r\n        // 处理质量异议损失数据，尝试多种可能的数据结构\r\n        const xAxisData = [];      // x轴维度数据\r\n        const seriesData = [];     // 柱状图数据\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#F3E8FF'];\r\n        let dataItems = [];\r\n\r\n        // 尝试不同的数据结构，转换为万元\r\n        if (data.qualityObjectionLossMap) {\r\n          // 情况1: 使用qualityObjectionLossMap数据\r\n          console.log('使用qualityObjectionLossMap数据');\r\n          dataItems = Object.entries(data.qualityObjectionLossMap).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else if (data.rescueProject) {\r\n          // 情况2: 使用rescueProject数据（与WaterfallChart相同）\r\n          console.log('使用rescueProject数据');\r\n          dataItems = Object.entries(data.rescueProject).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        } else {\r\n          // 情况3: 直接使用data作为对象\r\n          console.log('直接使用data对象');\r\n          dataItems = Object.entries(data).map(([key, value]) => ({\r\n            name: key,\r\n            value: ((Number(value) || 0) / 10000).toFixed(2)\r\n          }));\r\n        }\r\n\r\n        console.log('处理后的数据项:', dataItems);\r\n\r\n        if (dataItems.length > 0) {\r\n          // 按数值从高到低排序\r\n          dataItems.sort((a, b) => b.value - a.value);\r\n\r\n          // 只取前十个最大的数据\r\n          const topTenItems = dataItems.slice(0, 10);\r\n          console.log('取前十个最大数据:', topTenItems);\r\n\r\n          // 分离排序后的数据\r\n          topTenItems.forEach((item, index) => {\r\n            xAxisData.push(item.name);\r\n            seriesData.push({\r\n              value: item.value,\r\n              itemStyle: { color: colors[index % colors.length] }\r\n            });\r\n          });\r\n        } else {\r\n          console.warn('没有找到有效的数据项');\r\n          // 添加默认数据以便测试\r\n          xAxisData.push('无数据');\r\n          seriesData.push({\r\n            value: 0,\r\n            itemStyle: { color: colors[0] }\r\n          });\r\n        }\r\n\r\n        console.log('x轴维度数据:', xAxisData);\r\n        console.log('柱状图数据:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n          tooltip: {\r\n            trigger: 'axis',\r\n            axisPointer: { type: 'shadow' },\r\n            backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n            borderColor: '#93C5FD',\r\n            textStyle: { color: '#fff' },\r\n            formatter: function (params) {\r\n              let result = params[0].name + '<br/>';\r\n              params.forEach(function (item) {\r\n                const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n                result += item.marker + ' ' + item.seriesName + ': ' + formattedValue + '万元<br/>';\r\n              });\r\n              return result;\r\n            }\r\n          },\r\n          xAxis: {\r\n            type: 'category',\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              interval: 0, // 显示所有标签\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '金额 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          series: [{\r\n            name: '质量异议损失成本',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.qualityObjectionChart.setOption(option, true); // 使用true强制刷新\r\n        console.log('QualityObjectionChart柱状图数据已更新');\r\n      } else {\r\n        console.error('QualityObjectionChart实例不存在或数据为空');\r\n      }\r\n    },\r\n\r\n    getQualityObjectionLossDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getQualityObjectionLossDetail(params).then(response => {\r\n        console.log('getQualityObjectionLossDetail:', response);\r\n        if (response.data) {\r\n          // 更新QualityObjectionChart柱状图\r\n          this.updateQualityObjectionChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取QualityObjectionChart数据失败:', error);\r\n        this.$message.error('获取产品质量异议损失明细数据失败');\r\n      });\r\n    },\r\n\r\n    getScrapLossChartDetailsDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getScrapLossChartDetailsDetail(params).then(response => {\r\n        console.log('getScrapLossChartDetailsDetail:', response);\r\n        if (response.data) {\r\n          // 更新ScrapLossChart柱状图\r\n          this.updateScrapLossChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取ScrapLossChart数据失败:', error);\r\n        this.$message.error('获取产品报废损失明细数据失败');\r\n      });\r\n    },\r\n\r\n    getExternalCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getExternalCostDetail(params).then(response => {\r\n        console.log('getExternalCostDetail:', response);\r\n        if (response.data) {\r\n          // 更新外部损失成本构成图表\r\n          this.updateExternalCostDetailChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取外部损失成本数据失败:', error);\r\n        this.$message.error('获取外部损失成本数据失败');\r\n      });\r\n    },\r\n\r\n    getInternalCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getInternalCostDetail(params).then(response => {\r\n        console.log('getInternalCostDetail:', response);\r\n        if (response.data) {\r\n          // 更新内部损失成本构成图表\r\n          this.updateInternalCostDetailChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取内部损失成本数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新内部损失成本构成图表\r\n    updateInternalCostDetailChart(data) {\r\n      if (this.charts.internalCostDetailChart && data) {\r\n        console.log('接收到的内部损失成本数据:', data);\r\n\r\n        // 收集所有数据项\r\n        const allDataItems = [];\r\n        const colors = ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD'];\r\n\r\n        // 处理各个成本项，收集到统一数组中，转换为万元\r\n        if (data.contractionLoss) {\r\n          Object.entries(data.contractionLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.rescueCost) {\r\n          Object.entries(data.rescueCost).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.revisionLoss) {\r\n          Object.entries(data.revisionLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        if (data.scrapLoss) {\r\n          Object.entries(data.scrapLoss).forEach(([key, value]) => {\r\n            // 确保数值转换，包括0值也要显示，转换为万元\r\n            const numValue = ((Number(value) || 0) / 10000).toFixed(2);\r\n            allDataItems.push({ name: key, value: numValue });\r\n          });\r\n        }\r\n\r\n        console.log('收集到的所有数据项（包含0值）:', allDataItems);\r\n\r\n        // 按数值从高到低排序（0值会排在负值之前，正值之后）\r\n        allDataItems.sort((a, b) => b.value - a.value);\r\n\r\n        console.log('排序后的数据（包含0值）:', allDataItems);\r\n\r\n        // 分离排序后的数据，反转顺序使金额大的显示在上面\r\n        const yAxisData = [];\r\n        const seriesData = [];\r\n\r\n        // 反转数组，使金额大的显示在图表上方\r\n        allDataItems.reverse().forEach((item, index) => {\r\n          yAxisData.push(item.name);\r\n          seriesData.push({\r\n            value: item.value,\r\n            itemStyle: { color: colors[index % colors.length] }\r\n          });\r\n        });\r\n\r\n        console.log('y轴数据:', yAxisData);\r\n        console.log('系列数据（包含0值）:', seriesData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          yAxis: {\r\n            type: 'category',\r\n            data: yAxisData,\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [{\r\n            name: '金额 (万元)',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.internalCostDetailChart.setOption(option);\r\n        console.log('内部损失成本构成图表数据已更新（包含0值，按数值从高到低排序）');\r\n      }\r\n    },\r\n\r\n    // 更新外部损失成本构成图表\r\n    updateExternalCostDetailChart(data) {\r\n      if (this.charts.externalCostDetailChart && data) {\r\n        // 收集所有数据项\r\n        const allDataItems = [];\r\n        const colors = ['#FCA5A5', '#FDE68A', '#86EFAC', '#93C5FD', '#C4B5FD'];\r\n\r\n        // 处理各个成本项，收集到统一数组中，转换为万元\r\n        if (data.customerClaimCost) {\r\n          Object.entries(data.customerClaimCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.qualityObjectionFeeCost) {\r\n          Object.entries(data.qualityObjectionFeeCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.qualityObjectionTravelCost) {\r\n          Object.entries(data.qualityObjectionTravelCost).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        if (data.returnLoss) {\r\n          Object.entries(data.returnLoss).forEach(([key, value]) => {\r\n            allDataItems.push({ name: key, value: ((Number(value) || 0) / 10000).toFixed(2) });\r\n          });\r\n        }\r\n\r\n        // 按数值从高到低排序\r\n        allDataItems.sort((a, b) => b.value - a.value);\r\n\r\n        // 分离排序后的数据，反转顺序使金额大的显示在上面\r\n        const yAxisData = [];\r\n        const seriesData = [];\r\n\r\n        // 反转数组，使金额大的显示在图表上方\r\n        allDataItems.reverse().forEach((item, index) => {\r\n          yAxisData.push(item.name);\r\n          seriesData.push({\r\n            value: item.value,\r\n            itemStyle: { color: colors[index % colors.length] }\r\n          });\r\n        });\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          yAxis: {\r\n            type: 'category',\r\n            data: yAxisData,\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [{\r\n            name: '金额 (万元)',\r\n            type: 'bar',\r\n            data: seriesData\r\n          }]\r\n        };\r\n\r\n        this.charts.externalCostDetailChart.setOption(option);\r\n        console.log('外部损失成本构成图表数据已更新（已按数值从高到低排序）');\r\n      }\r\n    },\r\n\r\n    getQualityCostDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getQualityCostDetail(params).then(response => {\r\n        console.log('getQualityCostDetail:', response);\r\n        if (response.data) {\r\n          this.qualityCostData = response.data.qualityCostData;\r\n          this.qualityCostDetail = response.data;\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n\r\n    //质量成本四大类别占比\r\n    getMultiLineChartData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getMultiLineChartData(params).then(response => {\r\n        console.log('getMultiLineChartData:', response);\r\n        if (response.data) {\r\n          this.updateMultiLineChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        // console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n\r\n    getComboChartDetail() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n      getComboChartDetail(params).then(response => {\r\n        console.log('getComboChartDetail:', response);\r\n        if (response.data) {\r\n          this.updateComboChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        this.$message.error('获取ComboChart数据失败');\r\n      });\r\n    },\r\n\r\n    // 更新ComboChart图表\r\n    updateComboChart(data) {\r\n      if (this.charts.comboChart && data) {\r\n        console.log('接收到的ComboChart数据:', data);\r\n\r\n        // 基于会计期生成近6个月的月份标签作为x轴数据\r\n        const months = this.generateComboChartMonthsByAccountingPeriod();\r\n        console.log('生成的月份标签:', months);\r\n\r\n        // 生成对应的年月格式用于数据匹配\r\n        const yearMonths = this.generateYearMonthsByAccountingPeriod();\r\n        console.log('生成的年月格式:', yearMonths);\r\n\r\n        const failureCostData = [];     // 失败成本数据\r\n        const controllingCostData = []; // 控制成本数据\r\n\r\n        // 为每个月份提取对应的数值，转换为万元\r\n        yearMonths.forEach(yearMonth => {\r\n          // 获取失败成本数据，转换为万元\r\n          const failureValue = data.failureCostMap && data.failureCostMap[yearMonth]\r\n            ? ((Number(data.failureCostMap[yearMonth]) || 0) / 10000).toFixed(2)\r\n            : 0;\r\n          failureCostData.push(failureValue);\r\n\r\n          // 获取控制成本数据，转换为万元\r\n          const controllingValue = data.controllingCostMap && data.controllingCostMap[yearMonth]\r\n            ? ((Number(data.controllingCostMap[yearMonth]) || 0) / 10000).toFixed(2)\r\n            : 0;\r\n          controllingCostData.push(controllingValue);\r\n        });\r\n\r\n        console.log('x轴月份数据:', months.map(month => `${month}月`));\r\n        console.log('失败成本数据:', failureCostData);\r\n        console.log('控制成本数据:', controllingCostData);\r\n\r\n        // 更新图表配置\r\n        const option = {\r\n          // 图例配置 - 标注颜色对应的维度\r\n          legend: {\r\n            data: ['失败成本', '控制成本'], // 失败成本(红色#FCA5A5)，控制成本(绿色#86EFAC)\r\n            textStyle: { color: '#E5E7EB' }\r\n          },\r\n          grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n          xAxis: {\r\n            type: 'category',\r\n            boundaryGap: false,\r\n            data: months.map(month => `${month}月`), // 近6个月的月份\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          yAxis: {\r\n            type: 'value',\r\n            name: '成本 (万元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n\r\n          series: [\r\n            {\r\n              name: '失败成本', // 红色曲线 #FCA5A5\r\n              type: 'line',\r\n              data: failureCostData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FCA5A5', width: 3 },\r\n              itemStyle: { color: '#FCA5A5' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '控制成本', // 绿色曲线 #86EFAC\r\n              type: 'line',\r\n              data: controllingCostData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#86EFAC', width: 3 },\r\n              itemStyle: { color: '#86EFAC' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n\r\n        this.charts.comboChart.setOption(option);\r\n        console.log('ComboChart图表数据已更新');\r\n      }\r\n    },\r\n\r\n    // 生成ComboChart的月份标签（当前月份和之前的5个月）\r\n    generateComboChartMonths() {\r\n      const months = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const month = date.getMonth() + 1;\r\n        months.push(month);\r\n      }\r\n\r\n      return months;\r\n    },\r\n\r\n    // 生成对应的年月格式（当前月份和之前的5个月，如202501, 202502等）\r\n    generateYearMonths() {\r\n      const yearMonths = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const year = date.getFullYear();\r\n        const month = date.getMonth() + 1;\r\n        const yearMonth = `${year}${String(month).padStart(2, '0')}`;\r\n        yearMonths.push(yearMonth);\r\n      }\r\n\r\n      return yearMonths;\r\n    },\r\n\r\n    // 基于会计期生成ComboChart的月份标签（会计期当前月份和之前的5个月）\r\n    generateComboChartMonthsByAccountingPeriod() {\r\n      const months = [];\r\n\r\n      if (!this.accountingPeriod) {\r\n        console.warn('会计期为空，使用系统当前时间');\r\n        return this.generateComboChartMonths();\r\n      }\r\n\r\n      // 解析会计期 (格式: 2025-06)\r\n      const [year, month] = this.accountingPeriod.split('-').map(Number);\r\n      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);\r\n        const monthNum = date.getMonth() + 1;\r\n        months.push(monthNum);\r\n      }\r\n\r\n      return months;\r\n    },\r\n\r\n    // 基于会计期生成对应的年月格式（会计期当前月份和之前的5个月）\r\n    generateYearMonthsByAccountingPeriod() {\r\n      const yearMonths = [];\r\n\r\n      if (!this.accountingPeriod) {\r\n        console.warn('会计期为空，使用系统当前时间');\r\n        return this.generateYearMonths();\r\n      }\r\n\r\n      // 解析会计期 (格式: 2025-06)\r\n      const [year, month] = this.accountingPeriod.split('-').map(Number);\r\n      const accountingDate = new Date(year, month - 1, 1); // month-1 因为Date的月份从0开始\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(accountingDate.getFullYear(), accountingDate.getMonth() - i, 1);\r\n        const yearNum = date.getFullYear();\r\n        const monthNum = date.getMonth() + 1;\r\n        const yearMonth = `${yearNum}${String(monthNum).padStart(2, '0')}`;\r\n        yearMonths.push(yearMonth);\r\n      }\r\n\r\n      return yearMonths;\r\n    },\r\n\r\n    //质量成本四大类别占比\r\n    getPieChartData() {\r\n      // 只有当成本中心和会计期都有值时才请求\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        console.log('成本中心、会计期或质量成本类型为空，跳过数据请求');\r\n        return;\r\n      }\r\n\r\n      const params = {\r\n        costCenter: this.costCenter,\r\n        yearMonth: this.accountingPeriod.replace('-', ''), // 将 2025-06 转换为 202506\r\n        containType: this.containType\r\n      };\r\n\r\n\r\n      getPieChartData(params).then(response => {\r\n        console.log('getPieChartData:', response);\r\n        if (response.data) {\r\n          this.updatePieChart(response.data);\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取饼图数据失败:', error);\r\n        this.$message.error('获取质量成本数据失败');\r\n      });\r\n    },\r\n    // 获取成本中心列表\r\n    getCostCenterList() {\r\n      this.costCenterLoading = true;\r\n      costCenterlist().then(response => {\r\n        this.costCenterOptions = response.data || [];\r\n        // 如果有数据，设置默认选中第一个\r\n        if (this.costCenterOptions.length > 0) {\r\n          console.log('获取成本中心列表:', this.costCenterOptions);\r\n          this.costCenter = this.costCenterOptions[0].key;\r\n          // 设置默认值后，主动触发一次数据刷新\r\n          this.$nextTick(() => {\r\n            this.refreshChartData();\r\n          });\r\n        }\r\n      }).catch(error => {\r\n        console.error('获取成本中心列表失败:', error);\r\n        this.$message.error('获取成本中心列表失败');\r\n      }).finally(() => {\r\n        this.costCenterLoading = false;\r\n      });\r\n    },\r\n\r\n    // 更新饼图数据\r\n    updatePieChart(data) {\r\n      if (this.charts.pieChart && data) {\r\n        // 更新饼图的数据，转换为万元\r\n        const option = this.charts.pieChart.getOption();\r\n        if (option && option.series && option.series[0]) {\r\n          option.series[0].data = [\r\n            { value: (data.preventionCost / 10000).toFixed(2), name: '预防成本', itemStyle: { color: '#93C5FD' } },\r\n            { value: (data.appraisalCost / 10000).toFixed(2), name: '鉴定成本', itemStyle: { color: '#86EFAC' } },\r\n            { value: (data.internalCost / 10000).toFixed(2), name: '内部损失成本', itemStyle: { color: '#FDE68A' } },\r\n            { value: (data.externalCost / 10000).toFixed(2), name: '外部损失成本', itemStyle: { color: '#FCA5A5' } },\r\n          ],\r\n            this.charts.pieChart.setOption(option);\r\n          // console.log('饼图数据已更新');\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新多线图数据\r\n    updateMultiLineChart(data) {\r\n      if (this.charts.multiLineChart && data) {\r\n        // 基于会计期生成月份标签和对应的年月数字\r\n        const months = this.generateComboChartMonthsByAccountingPeriod();\r\n        const yearMonths = this.generateYearMonthsByAccountingPeriod();\r\n\r\n        // 处理各种成本数据，转换为万元\r\n        const preventionData = this.processMapData(data.preventionCostMap, yearMonths, true);\r\n        const appraisalData = this.processMapData(data.appraisalCostMap, yearMonths, true);\r\n        const internalData = this.processMapData(data.internalCostMap, yearMonths, true);\r\n        const externalData = this.processMapData(data.externalCostMap, yearMonths, true);\r\n\r\n        // 更新多线图的配置\r\n        const option = {\r\n          xAxis: {\r\n            type: 'category',\r\n            boundaryGap: false,\r\n            data: months.map(month => `${month}月`), // 格式化为\"X月\"\r\n            axisLabel: {\r\n              color: '#9CA3AF',\r\n              rotate: 0, // 水平显示标签\r\n              align: 'center' // 居中对齐\r\n            },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          },\r\n          series: [\r\n            {\r\n              name: '预防成本',\r\n              type: 'line',\r\n              data: preventionData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#93C5FD', width: 3 },\r\n              itemStyle: { color: '#93C5FD' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '鉴定成本',\r\n              type: 'line',\r\n              data: appraisalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#86EFAC', width: 3 },\r\n              itemStyle: { color: '#86EFAC' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '内部损失成本',\r\n              type: 'line',\r\n              data: internalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FDE68A', width: 3 },\r\n              itemStyle: { color: '#FDE68A' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            },\r\n            {\r\n              name: '外部损失成本',\r\n              type: 'line',\r\n              data: externalData,\r\n              smooth: true, // 启用平滑曲线\r\n              lineStyle: { color: '#FCA5A5', width: 3 },\r\n              itemStyle: { color: '#FCA5A5' },\r\n              symbol: 'circle',\r\n              symbolSize: 6\r\n            }\r\n          ]\r\n        };\r\n\r\n        this.charts.multiLineChart.setOption(option);\r\n        console.log('多线图数据已更新');\r\n      }\r\n    },\r\n\r\n    // 处理Map数据，根据年月匹配对应的值\r\n    processMapData(costMap, yearMonths, convertToWanYuan = false) {\r\n      if (!costMap) return new Array(yearMonths.length).fill(0);\r\n\r\n      return yearMonths.map(yearMonth => {\r\n        const value = costMap[yearMonth] || 0;\r\n        return convertToWanYuan ? (value / 10000).toFixed(2) : value;\r\n      });\r\n    },\r\n\r\n    // 生成月份标签（当前月份及前五个月份）\r\n    generateMonthLabels() {\r\n      const months = [];\r\n      const yearMonths = [];\r\n      const currentDate = new Date();\r\n\r\n      for (let i = 5; i >= 0; i--) {\r\n        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n        const year = date.getFullYear();\r\n        const month = date.getMonth() + 1;\r\n\r\n        months.push(`${month}月`);\r\n        yearMonths.push(parseInt(`${year}${String(month).padStart(2, '0')}`));\r\n      }\r\n\r\n      return { months, yearMonths };\r\n    },\r\n\r\n    // 刷新图表数据\r\n    refreshChartData() {\r\n      // 只有当成本中心和会计期都有值时才刷新\r\n      if (!this.costCenter || !this.accountingPeriod || (this.containType !== 2 && this.containType !== 1)) {\r\n        return;\r\n      }\r\n\r\n      this.getQualityCostDetail();\r\n      this.getPieChartData();\r\n      this.getMultiLineChartData();\r\n      this.getExternalCostDetail();\r\n      this.getInternalCostDetail();\r\n      this.getComboChartDetail();\r\n      this.getWaterfallChartDetail();\r\n      this.getScrapLossChartDetailsDetail();\r\n      this.getQualityObjectionLossDetail();\r\n      this.getFactoryRejectionChartDetail();\r\n      this.getFactoryScrapChartDetail();\r\n      this.getFactoryContractChartDetail();\r\n      this.getFactoryReturnChartDetail();\r\n\r\n      // 这里可以添加其他图表的数据刷新\r\n      // this.$message.success(`已切换到成本中心: ${this.costCenter}, 会计期: ${this.accountingPeriod}`);\r\n    },\r\n\r\n    /** 查询按钮操作 */\r\n    handleQuery() {\r\n      this.refreshChartData();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      // 重置为默认值\r\n      if (this.costCenterOptions.length > 0) {\r\n        this.costCenter = this.costCenterOptions[0].key;\r\n      }\r\n\r\n      // 获取默认会计期\r\n      const now = new Date();\r\n      const year = now.getFullYear();\r\n      const month = now.getMonth();\r\n      const prevMonth = month === 0 ? 12 : month;\r\n      const prevYear = month === 0 ? year - 1 : year;\r\n      this.accountingPeriod = `${prevYear}-${String(prevMonth).padStart(2, '0')}`;\r\n\r\n      this.$message.success('查询条件已重置');\r\n    },\r\n\r\n    initCharts() {\r\n      const THEME = 'dark'\r\n\r\n      // 定义商务风淡色系色彩方案\r\n      this.businessColors = {\r\n        light: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5', '#C4B5FD', '#7DD3FC', '#F9A8D4', '#BEF264'],\r\n        gradient: [\r\n          { offset: 0, color: '#3B82F6' },\r\n          { offset: 1, color: '#1E40AF' }\r\n        ]\r\n      }\r\n\r\n      // 初始化所有图表\r\n      this.charts.pieChart = echarts.init(this.$refs.pieChart, THEME)\r\n      this.charts.multiLineChart = echarts.init(this.$refs.multiLineChart, THEME)\r\n      this.charts.externalCostDetailChart = echarts.init(this.$refs.externalCostDetailChart, THEME)\r\n      this.charts.internalCostDetailChart = echarts.init(this.$refs.internalCostDetailChart, THEME)\r\n      this.charts.waterfallChart = echarts.init(this.$refs.waterfallChart, THEME)\r\n      this.charts.comboChart = echarts.init(this.$refs.comboChart, THEME)\r\n      this.charts.scrapLossChart = echarts.init(this.$refs.scrapLossChart, THEME)\r\n      this.charts.qualityObjectionChart = echarts.init(this.$refs.qualityObjectionChart, THEME)\r\n      this.charts.factoryRejectionChart = echarts.init(this.$refs.factoryRejectionChart, THEME)\r\n      this.charts.factoryScrapChart = echarts.init(this.$refs.factoryScrapChart, THEME)\r\n      this.charts.factoryContractChart = echarts.init(this.$refs.factoryContractChart, THEME)\r\n      this.charts.factoryReturnChart = echarts.init(this.$refs.factoryReturnChart, THEME)\r\n\r\n      // 配置所有图表\r\n      this.setPieChartOption()\r\n      this.setMultiLineChartOption()\r\n      this.setExternalCostDetailChartOption()\r\n      this.setInternalCostDetailChartOption()\r\n      this.setWaterfallChartOption()\r\n      this.setComboChartOption()\r\n      this.setScrapLossChartOption()\r\n      this.setQualityObjectionChartOption()\r\n      this.setFactoryRejectionChartOption()\r\n      this.setFactoryScrapChartOption()\r\n      this.setFactoryContractChartOption()\r\n      this.setFactoryReturnChartOption()\r\n    },\r\n\r\n    setPieChartOption() {\r\n      this.charts.pieChart.setOption({\r\n        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: (params) => {\r\n            const value = parseFloat(params.value).toFixed(2);\r\n            const formattedValue = value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n            return `${params.seriesName} <br/>${params.name}: ${formattedValue}万元 (${params.percent}%)`;\r\n          },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          top: 'bottom',\r\n          left: 'center',\r\n          textStyle: { color: '#E5E7EB', fontSize: 12 }\r\n        },\r\n        series: [{\r\n          name: '成本类别',\r\n          type: 'pie',\r\n          radius: '65%',\r\n          data: [],\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 15,\r\n              shadowOffsetX: 0,\r\n              shadowColor: 'rgba(147, 197, 253, 0.6)'\r\n            }\r\n          },\r\n          labelLine: { lineStyle: { color: '#9CA3AF' } },\r\n          label: {\r\n            color: '#E5E7EB',\r\n            formatter: (params) => {\r\n              const value = parseFloat(params.value).toFixed(2);\r\n              const formattedValue = value.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              return `${params.name}(${formattedValue}万元, ${params.percent}%)`;\r\n            }\r\n          }\r\n        }]\r\n      })\r\n    },\r\n\r\n\r\n\r\n    setComboChartOption() {\r\n      this.charts.comboChart.setOption({\r\n        color: ['#FCA5A5', '#86EFAC'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['失败成本', '控制成本'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '成本 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '失败成本', // 红色曲线 #FCA5A5\r\n            type: 'line',\r\n            data: [280, 260, 240, 220, 200, 180],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FCA5A5', width: 3 },\r\n            itemStyle: { color: '#FCA5A5' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '控制成本', // 绿色曲线 #86EFAC\r\n            type: 'line',\r\n            data: [120, 125, 130, 135, 140, 145],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#86EFAC', width: 3 },\r\n            itemStyle: { color: '#86EFAC' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    setMultiLineChartOption() {\r\n      this.charts.multiLineChart.setOption({\r\n        color: ['#93C5FD', '#86EFAC', '#FDE68A', '#FCA5A5'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['预防成本', '鉴定成本', '内部损失成本', '外部损失成本'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '成本 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '预防成本',\r\n            type: 'line',\r\n            data: [80, 82, 85, 88, 90, 95],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#93C5FD', width: 3 },\r\n            itemStyle: { color: '#93C5FD' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '鉴定成本',\r\n            type: 'line',\r\n            data: [120, 122, 125, 128, 130, 135],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#86EFAC', width: 3 },\r\n            itemStyle: { color: '#86EFAC' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '内部损失成本',\r\n            type: 'line',\r\n            data: [450, 430, 410, 380, 350, 320],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          },\r\n          {\r\n            name: '外部损失成本',\r\n            type: 'line',\r\n            data: [350, 340, 310, 290, 260, 230],\r\n            smooth: true, // 启用平滑曲线\r\n            lineStyle: { color: '#FCA5A5', width: 3 },\r\n            itemStyle: { color: '#FCA5A5' },\r\n            symbol: 'circle',\r\n            symbolSize: 6\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n\r\n\r\n\r\n\r\n    setParetoChartOption() {\r\n      this.charts.paretoChart.setOption({\r\n        color: ['#93C5FD', '#FDE68A'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        grid: { right: '20%' },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['产品报废', '产品改判', '设备故障', '工艺废料', '其他'],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center', // 居中对齐\r\n            color: '#9CA3AF'\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        }],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '损失金额(元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '累计占比',\r\n            min: 0,\r\n            max: 100,\r\n            axisLabel: {\r\n              formatter: '{value} %',\r\n              color: '#9CA3AF'\r\n            },\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '损失金额',\r\n            type: 'bar',\r\n            data: [280, 110, 35, 20, 5],\r\n            itemStyle: { color: '#93C5FD' }\r\n          },\r\n          {\r\n            name: '累计占比',\r\n            type: 'line',\r\n            yAxisIndex: 1,\r\n            data: [62.2, 86.7, 94.4, 98.9, 100],\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 8\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    setExternalCostDetailChartOption() {\r\n      this.charts.externalCostDetailChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>';\r\n            params.forEach(function (item) {\r\n              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';\r\n            });\r\n            return result;\r\n          }\r\n        },\r\n        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '金额（万元）',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n\r\n          data: [],\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        series: [{\r\n          name: '金额 (元)',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setInternalCostDetailChartOption() {\r\n      this.charts.internalCostDetailChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n          formatter: function (params) {\r\n            let result = params[0].name + '<br/>';\r\n            params.forEach(function (item) {\r\n              const formattedValue = parseFloat(item.value).toFixed(2).toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n              result += item.marker + ' 金额: ' + formattedValue + '万元<br/>';\r\n            });\r\n            return result;\r\n          }\r\n        },\r\n        grid: { left: '5%', right: '15%', top: '12%', bottom: '12%', containLabel: true },\r\n        xAxis: {\r\n          type: 'value',\r\n          name: '金额（万元）',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        yAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        series: [{\r\n          name: '金额 (元)',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setWaterfallChartOption() {\r\n      this.charts.waterfallChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: ['初始成本', '修磨', '矫直', '探伤', '热处理', '总成本'],\r\n          axisLabel: {\r\n            interval: 0,\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center', // 居中对齐\r\n            color: '#9CA3AF'\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [\r\n          {\r\n            name: '辅助',\r\n            type: 'bar',\r\n            stack: '总量',\r\n            itemStyle: {\r\n              color: 'rgba(0,0,0,0)',\r\n              borderColor: 'rgba(0,0,0,0)',\r\n              borderWidth: 0\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                color: 'rgba(0,0,0,0)'\r\n              }\r\n            },\r\n            data: [0, 0, 50, 80, 105, 0]\r\n          },\r\n        ]\r\n      })\r\n    },\r\n\r\n    setScrapLossChartOption() {\r\n      this.charts.scrapLossChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '报废损失成本',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setQualityObjectionChartOption() {\r\n      this.charts.qualityObjectionChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '质量异议损失成本',\r\n          type: 'bar',\r\n          data: []\r\n        }]\r\n      })\r\n    },\r\n\r\n    setFactoryRejectionChartOption() {\r\n      this.charts.factoryRejectionChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [], // 动态数据，由updateFactoryRejectionChart方法填充\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (万元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '改判金额',\r\n          type: 'bar',\r\n          data: [] // 动态数据，由updateFactoryRejectionChart方法填充\r\n        }]\r\n      })\r\n    },\r\n\r\n    setFactoryScrapChartOption() {\r\n      this.charts.factoryScrapChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [], // 动态数据，由updateFactoryScrapChart方法填充\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '报废金额',\r\n          type: 'bar',\r\n          data: [] // 动态数据，由updateFactoryScrapChart方法填充\r\n        }]\r\n      })\r\n    },\r\n\r\n    setFactoryContractChartOption() {\r\n      this.charts.factoryContractChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [], // 动态数据，由updateFactoryContractChart方法填充\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '脱合同金额',\r\n          type: 'bar',\r\n          data: [] // 动态数据，由updateFactoryContractChart方法填充\r\n        }]\r\n      })\r\n    },\r\n\r\n    setFactoryReturnChartOption() {\r\n      this.charts.factoryReturnChart.setOption({\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'shadow' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' },\r\n        },\r\n        grid: { left: '8%', right: '4%', bottom: '3%', containLabel: true },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: [], // 动态数据，由updateFactoryReturnChart方法填充\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            interval: 0, // 显示所有标签\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '金额 (元)',\r\n          nameTextStyle: { color: '#9CA3AF' },\r\n          axisLabel: { color: '#9CA3AF' },\r\n          axisLine: { lineStyle: { color: '#4B5563' } },\r\n          splitLine: { lineStyle: { color: '#374151' } }\r\n        },\r\n        series: [{\r\n          name: '退货金额',\r\n          type: 'bar',\r\n          data: [] // 动态数据，由updateFactoryReturnChart方法填充\r\n        }]\r\n      })\r\n    },\r\n\r\n    setDualYChartOption() {\r\n      this.charts.dualYChart.setOption({\r\n        color: ['#93C5FD', '#FDE68A'],\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: { type: 'cross' },\r\n          backgroundColor: 'rgba(30, 41, 59, 0.9)',\r\n          borderColor: '#93C5FD',\r\n          textStyle: { color: '#fff' }\r\n        },\r\n        legend: {\r\n          data: ['产量(吨)', '吨钢成本(元)'],\r\n          textStyle: { color: '#E5E7EB' }\r\n        },\r\n        xAxis: [{\r\n          type: 'category',\r\n          data: ['1月', '2月', '3月', '4月', '5月', '6月'],\r\n          axisLabel: {\r\n            color: '#9CA3AF',\r\n            rotate: 0, // 水平显示标签\r\n            align: 'center' // 居中对齐\r\n          },\r\n          axisLine: { lineStyle: { color: '#4B5563' } }\r\n        }],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '产量(吨)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } },\r\n            splitLine: { lineStyle: { color: '#374151' } }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '吨钢成本(元)',\r\n            nameTextStyle: { color: '#9CA3AF' },\r\n            axisLabel: { color: '#9CA3AF' },\r\n            axisLine: { lineStyle: { color: '#4B5563' } }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '产量(吨)',\r\n            type: 'bar',\r\n            data: [80000, 82000, 85000, 83000, 88000, 90000],\r\n            itemStyle: { color: '#93C5FD' }\r\n          },\r\n          {\r\n            name: '吨钢成本(元)',\r\n            type: 'line',\r\n            yAxisIndex: 1,\r\n            data: [13.1, 12.8, 12.5, 12.6, 12.2, 12.0],\r\n            lineStyle: { color: '#FDE68A', width: 3 },\r\n            itemStyle: { color: '#FDE68A' },\r\n            symbol: 'circle',\r\n            symbolSize: 8\r\n          }\r\n        ]\r\n      })\r\n    },\r\n\r\n    resizeCharts() {\r\n      Object.values(this.charts).forEach(chart => {\r\n        if (chart) {\r\n          chart.resize()\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.quality-cost-dashboard {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';\r\n  background-color: #111827;\r\n  /* 深色背景 */\r\n  color: #d1d5db;\r\n  /* 浅色文字 */\r\n  margin: 0;\r\n  padding: 24px;\r\n  min-height: 100vh;\r\n}\r\n\r\n.header {\r\n  margin-bottom: 24px;\r\n  text-align: center;\r\n  position: relative;\r\n}\r\n\r\n.header-wrapper {\r\n  display: inline-block;\r\n  position: relative;\r\n}\r\n\r\n.header h1 {\r\n  font-size: 28px;\r\n  color: #f9fafb;\r\n  /* 白色标题 */\r\n  font-weight: 600;\r\n  margin: 0;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.header p {\r\n  font-size: 16px;\r\n  color: #9ca3af;\r\n  /* 中灰色文字 */\r\n  margin: 8px 0 0 0;\r\n}\r\n\r\n.header-filters {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex-wrap: nowrap;\r\n  justify-content: flex-start;\r\n  margin-top: 12px;\r\n  margin-left: 950px;\r\n  /* 向左对齐 */\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.filter-item .label {\r\n  color: #d1d5db;\r\n  /* 浅色标签文字 */\r\n  font-size: 14px;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 右上角筛选区域的样式 */\r\n.header-filters .el-input__inner,\r\n.header-filters .el-select .el-input__inner {\r\n  background-color: #111827; /* 与页面背景一致的深色 */\r\n  border-color: #374151;\r\n  color: #ffffff; /* 白色字体 */\r\n}\r\n\r\n.header-filters .el-input__inner:focus,\r\n.header-filters .el-select .el-input__inner:focus {\r\n  border-color: #93c5fd;\r\n  background-color: #111827; /* 聚焦时保持背景色 */\r\n}\r\n\r\n.header-filters .el-select-dropdown {\r\n  background-color: #111827; /* 下拉菜单背景与页面一致 */\r\n  border-color: #374151;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item {\r\n  color: #ffffff; /* 下拉选项白色字体 */\r\n  background-color: #111827;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item:hover {\r\n  background-color: #1f2937; /* 悬浮时稍微亮一点 */\r\n  color: #ffffff;\r\n}\r\n\r\n.header-filters .el-select-dropdown .el-select-dropdown__item.selected {\r\n  background-color: #374151; /* 选中项背景 */\r\n  color: #ffffff;\r\n}\r\n\r\n/* 下拉框箭头颜色 */\r\n.header-filters .el-select .el-input__suffix {\r\n  color: #ffffff;\r\n}\r\n\r\n.header-filters .el-select .el-select__caret {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 占位符文字颜色 */\r\n.header-filters .el-input__inner::placeholder {\r\n  color: #9ca3af;\r\n}\r\n\r\n/* 清除按钮颜色 */\r\n.header-filters .el-select .el-select__clear {\r\n  color: #9ca3af;\r\n}\r\n\r\n.header-filters .el-select .el-select__clear:hover {\r\n  color: #ffffff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1024px) {\r\n  .header-filters {\r\n    flex-wrap: wrap;\r\n    gap: 8px;\r\n    margin-left: 0;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .header {\r\n    text-align: center;\r\n  }\r\n\r\n  .header-filters {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    align-items: center;\r\n    margin-left: 0;\r\n    /* 在小屏幕上取消左边距 */\r\n  }\r\n\r\n  .filter-item {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n.dashboard-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(4, 1fr); /* 改为四列布局，支持1/4和3/4分配 */\r\n  gap: 24px;\r\n}\r\n\r\n/* 响应式设计：在小屏幕上改为单列 */\r\n@media (max-width: 1200px) {\r\n  .dashboard-grid {\r\n    grid-template-columns: 1fr; /* 小屏幕时改为单列 */\r\n  }\r\n\r\n  /* 小屏幕时重置所有grid-column样式 */\r\n  .dashboard-grid .chart-container {\r\n    grid-column: 1 !important;\r\n  }\r\n}\r\n\r\n.chart-container,\r\n.kpi-card {\r\n  background-color: #1f2937;\r\n  /* 深色卡片背景 */\r\n  border-radius: 8px;\r\n  border: 1px solid #374151;\r\n  /* 边框 */\r\n  box-shadow: none;\r\n  padding: 24px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.chart-container {\r\n  height: 400px;\r\n}\r\n\r\n/* 大图表样式 - 用于两两排列的图表 */\r\n.chart-container.large-chart {\r\n  height: 500px; /* 增加高度 */\r\n  min-height: 500px;\r\n}\r\n\r\n.chart-container h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 16px;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #f9fafb;\r\n  /* 白色卡片标题 */\r\n}\r\n\r\n.chart {\r\n  width: 100%;\r\n  flex-grow: 1;\r\n}\r\n\r\n.kpi-grid {\r\n  grid-column: 1 / -1;\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 24px;\r\n}\r\n\r\n.kpi-card {\r\n  justify-content: space-between;\r\n}\r\n\r\n.kpi-card .title {\r\n  font-size: 14px;\r\n  color: #9ca3af;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.kpi-card .value {\r\n  font-size: 32px;\r\n  font-weight: 700;\r\n  color: #f9fafb;\r\n}\r\n\r\n.kpi-card .comparison {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 14px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.kpi-card .comparison .arrow {\r\n  width: 20px;\r\n  height: 20px;\r\n  margin-right: 4px;\r\n  stroke-width: 2.5px;\r\n}\r\n\r\n.kpi-card .comparison .positive {\r\n  color: #34d399;\r\n  /* 亮绿色 */\r\n}\r\n\r\n.kpi-card .comparison .negative {\r\n  color: #f87171;\r\n  /* 亮红色 */\r\n}\r\n</style>\r\n"]}]}