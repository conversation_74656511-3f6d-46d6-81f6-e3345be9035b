package com.ruoyi.quartz.task;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.app.qs.domain.QsTrace;
import com.ruoyi.app.qs.enums.QsRoleType;
import com.ruoyi.app.qs.mapper.QsUserMapper;
import com.ruoyi.app.qs.service.impl.QsTraceServiceImpl;
import com.ruoyi.app.v1.domain.HrLeaveInfo;
import com.ruoyi.app.v1.service.IHrLeaveInfoService;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.qyhl.SFLUrl;
import com.ruoyi.common.enums.saleScore.FrequencyDesc;
import com.ruoyi.common.enums.saleScore.ScoreItemStatus;
import com.ruoyi.common.ftp.IWarrantyFtpService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StreamUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.email.MailUtils;
import com.ruoyi.common.utils.http.HttpUtil;
import com.ruoyi.common.utils.http.HttpUtils;
import com.ruoyi.common.utils.pdf.PDFFileUtils;
import com.ruoyi.common.utils.sm4.SM4Utils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.measure.service.IPlanTaskService;
import com.ruoyi.quartz.domain.AuditVisit;
import com.ruoyi.quartz.domain.CateringOrder;
import com.ruoyi.quartz.domain.SflInventory;
import com.ruoyi.quartz.mapper.*;
import com.ruoyi.socket.controller.SocketController;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import com.ruoyi.xcerp.domain.CompanyInfoList;
import com.ruoyi.xcerp.mapper.CompanyInfoListMapper;
import com.ruoyi.xcerp.mapper.XctgCompanyMapper;
import com.ruoyi.xcerp.qyhl.sfl.domain.SFLMsgRes;
import com.ruoyi.xcerp.qyhl.sfl.domain.TSflCertificate;
import com.ruoyi.xcerp.qyhl.sfl.service.ITSflCertificateService;
import com.ruoyi.xcerp.qyhl.sfl.service.impl.SFLMsgResServiceImpl;
import com.ruoyi.xcerp.qyhl.vestas.domain.TQMTCVST;
import com.ruoyi.xcerp.qyhl.vestas.domain.VestasMsg;
import com.ruoyi.xcerp.qyhl.vestas.service.ITQMTCVSTServive;
import com.ruoyi.xcerp.qyhl.vestas.service.IVestasMsgService;
import com.ruoyi.xcerp.qyhl.zj.domain.ZJDelivery;
import com.ruoyi.xcerp.qyhl.zj.domain.ZJMsgRes;
import com.ruoyi.xcerp.qyhl.zj.service.IZJDeliveryService;
import com.ruoyi.xcerp.qyhl.zj.service.IZJMsgResService;
import com.ruoyi.xcerp.service.IQDService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.apache.poi.hpsf.Decimal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Component("ryTask")
public class RyTask {
    private static final Logger log = LoggerFactory.getLogger(RyTask.class);
    private static final String measurePlanUrl = "http://172.16.64.235:65522/app-transport/api/measure";
    @Autowired
    IWarrantyFtpService warrantyFtpService;
    @Autowired
    private EmployeeMapper employeeMapper;
    @Autowired
    private UserAccessMapper userAccessMapper;
    @Autowired
    private CateringOrderMapper cateringOrderMapper;
    @Autowired
    private AuditVisitMapper auditVisitMapper;
    @Autowired
    private SflInventoryMapper sflInventoryMapper;
    @Autowired
    private SflDeliverMapper sflDeliverMapper;
    @Autowired
    private ITSflCertificateService sflCertificateService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SaleScoreMapper saleScoreMapper;
    @Autowired
    private XCERPMapper xcerpMapper;
    @Autowired
    private IZJDeliveryService zjDeliveryService;
    @Autowired
    private IZJMsgResService zjMsgResService;
    @Autowired
    private IPlanTaskService iPlanTaskService;
    @Autowired
    private IHrLeaveInfoService hrLeaveInfoService;
    @Autowired
    private com.ruoyi.app.v1.service.ITWgbPointsRecordService pointsRecordService;
    @Value("${qyhl.zj.accessKeyId}")
    private String accessKeyId;
    @Value("${qyhl.zj.accessKeySecret}")
    private String accessKeySecret;
    @Value("${qyhl.zj.delivery.url}")
    private String deliveryUrl;
    @Autowired
    private ITQMTCVSTServive tqmtcvstServive;
    @Autowired
    private IVestasMsgService vestasMsgService;
    @Value("${qyhl.vestas.url}")
    private String vestasUrl;
    @Value("${qyhl.vestas.accountId}")
    private String vestasAccountId;
    @Value("${qyhl.vestas.supplierKey}")
    private String vestasSupplierKey;
    @Value("${qyhl.vestas.supplierIdentifier}")
    private String vestasSupplierIdentifier;
    @Value("${qyhl.vestas.receiverKey}")
    private String vestasReceiverKey;


    /**
     * 定时获取主页用户访问数据
     */
    public void sendZJDeliveryInfo() {
        //获取用户访问数据
        log.info("定时发送中建科工提单数据------开始");
        String host = "https://kghc.cscecsteel.com";
        List<ZJDelivery> deliveys = zjDeliveryService.selectZJDeliveryList();
        deliveys.forEach(item -> {
            try {
                ZJMsgRes zjMsgRes = new ZJMsgRes();
                zjMsgRes.setType("发货单定时上传接口");
                zjMsgRes.setContent(item.getDeliveryVoucher());
                zjMsgRes.setRes("200");
                List<ZJMsgRes> historyMsg = zjMsgResService.selectZJMsgResList(zjMsgRes);
                if (historyMsg.size() <= 0) {
                    String res = HttpUtils.QYHLSendZJ(host, deliveryUrl, item, accessKeyId, accessKeySecret);
                    JSONObject msg = JSON.parseObject(res);
                    zjMsgRes.setContent(JSON.toJSONString(item));
                    zjMsgRes.setRes(msg.get("code").toString());
                    zjMsgRes.setResContent(res);
                    zjMsgResService.insertZJMsgRes(zjMsgRes);
                }
            } catch (IOException e) {
                ZJMsgRes zjMsgRes = new ZJMsgRes();
                zjMsgRes.setType("发货单定时上传接口");
                zjMsgRes.setContent(JSON.toJSONString(item));
                zjMsgRes.setRes("false");
                zjMsgRes.setResContent(e.getMessage());
                zjMsgResService.insertZJMsgRes(zjMsgRes);
            }
        });


        log.info("定时发送中建科工提单数据------结束");

    }


    public void ryMultipleParams(String s, Boolean b, Long l, Double d, Integer i) {
        System.out.println(StringUtils.format("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
    }

    public void ryParams(String params) {
        System.out.println("执行有参方法：" + params);
    }

    public void ryNoParams() {
        System.out.println("执行无参方法");
    }

    /**
     * 定时创建员工日志表
     */
    public void createEmployeeLogTable() {
        // 根据月份生成表名
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        String month = DateFormatUtils.format(calendar.getTime(), "yyMM");
        // 初始值
        employeeMapper.createLogTable("employee_log_" + month);
    }

    /**
     * 定时获取主页用户访问数据
     */
    public void getUserAccessData() {
        //获取用户访问数据
        Map<String, Object> map = new HashMap<>();
        map.put("wxUserCount", userAccessMapper.wxUserCount());
        map.put("workNoCount", userAccessMapper.boundWorkNoCount());
        map.put("yesUserCount", userAccessMapper.yesUserCount());
        map.put("toUserCount", userAccessMapper.toUserCount());
        map.put("currloginStatics", userAccessMapper.currDateloginTimeStatics());
        map.put("yesloginStatics", userAccessMapper.yesDateloginTimeStatics());
        //存入缓存Map
        redisCache.setCacheMap("sys_home:" + "userAccessData", map);
        //设置有效时间
        redisCache.expire("sys_home:" + "userAccessData", 2, TimeUnit.HOURS);
    }


    /**
     * 定时舍弗勒接收库存数据结果
     */
    public void sendSFLInventory() {
        List<SflInventory> list = sflInventoryMapper.selectInventoryList();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userAccount", "XC");
        jsonObject.put("userPassWord", "8f6249261b0afd36");
        JSONArray DataMsg = JSONArray.parseArray(JSON.toJSONString(list));
        jsonObject.put("DataMsg", DataMsg);
        log.info("库存数据:" + jsonObject.toJSONString());
        String result = HttpUtils.sendPostByJson(SFLUrl.Inventory.getUrl(), jsonObject.toJSONString());
        log.info("舍弗勒接收库存数据结果:" + result);
        JSONObject resMsg = JSON.parseObject(result);
        SFLMsgRes sflMsgRes = new SFLMsgRes();
        sflMsgRes.setType("库存定时");
//        sflMsgRes.setContent(jsonObject.toString());
        sflMsgRes.setRes(resMsg.get("success").toString());
        sflMsgRes.setResContent(resMsg.toString());
        SpringUtils.getBean(SFLMsgResServiceImpl.class).insertSFLMsgRes(sflMsgRes);
    }

//
//    /**
//     * 定时舍弗勒接收发货数据结果
//     */
//    public void sendSFLDeliver() {
//        //得到一个Calendar实例
//        Calendar calendar = Calendar.getInstance();
//        //calendar的日期设为今天
//        calendar.setTime(new Date());
//        //设置calendar为昨天
//        calendar.add(Calendar.DATE, -1);
//        Date yesterday = calendar.getTime();
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
//        //前一天的字符串
//        String pre = simpleDateFormat.format(yesterday);
//        //前一天凌晨0点的字符串
//        String RecCreateTime = pre.substring(0, 8) + "000000";
//        List<SflDeliver> list = sflDeliverMapper.selectDeliverList(RecCreateTime);
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("userAccount", "XC");
//        jsonObject.put("userPassWord", "8f6249261b0afd36");
//        JSONArray DataMsg = JSONArray.parseArray(JSON.toJSONString(list));
//        jsonObject.put("DataMsg",DataMsg);
//        String url = "http://47.103.192.93:8002/SchaefflerApi/SmDeliver/GetDeliver";
//        log.info("发货数据:" + jsonObject.toJSONString());
//        String result = HttpUtils.sendPostByJson(url, jsonObject.toJSONString());
//        log.info("舍弗勒接收发货数据结果:" + result);
//        JSONObject resMsg = JSON.parseObject(result);
//        SFLMsgRes sflMsgRes = new SFLMsgRes();
//        sflMsgRes.setType("发货定时");
////        sflMsgRes.setContent(jsonObject.toString());
//        sflMsgRes.setRes(resMsg.get("success").toString());
//        sflMsgRes.setResContent(resMsg.toString());
//        SpringUtils.getBean(SFLMsgResServiceImpl.class).insertSFLMsgRes(sflMsgRes);
//    }

    /**
     * 定时舍弗勒接收质保书数据结果
     */
    public void sendCertificate() {
        log.info("定时发送舍弗勒质保书------开始");
        TSflCertificate tSflCertificate = new TSflCertificate();
        tSflCertificate.setState("0");
        tSflCertificate.setCreateTime(DateUtils.addMinutes(DateUtils.getNowDate(), -20));
        List<TSflCertificate> list = sflCertificateService.selectTSflCertificateList(tSflCertificate);
        for (TSflCertificate item : list) {
            JSONObject jsonObject = new JSONObject();

            jsonObject.put("userAccount", "XC");
            jsonObject.put("userPassWord", "8f6249261b0afd36");
            JSONArray DataMsg = new JSONArray();
            JSONObject data1 = JSON.parseObject(item.getMsgInfo());
            log.info("定时发送舍弗勒接收质保书数据------" + data1.get("millsheetno"));
            JSONObject data2 = new JSONObject();
            data2.put("Ng", data1.get("ng"));
            data2.put("CustNo", data1.get("custno"));
            data2.put("MillSheetno", data1.get("millsheetno"));
            data2.put("OrDerno", data1.get("orderno"));
            data2.put("OrderItem", data1.get("orderitem"));
            data2.put("Displistno", data1.get("displistno"));
            data2.put("Factory", data1.get("factory"));
            data2.put("Prodspecno", data1.get("prodspecno"));
            data2.put("CreateDate", data1.get("createdate"));
            data2.put("CreateDate", data1.get("createdate"));
            data2.put("MillsheetName", data1.get("millsheetno") + ".pdf");
            data2.put("Type", data1.get("type"));
//            data2.put("Pdfdir",data1.get("pdfdir"));
            data2.put("OrderSize", data1.get("ordersize"));
            data2.put("LoadNo", data1.get("loadno"));
            data2.put("PsrNo", data1.get("psrno"));
            data2.put("Heatno", data1.get("heatno"));
            data2.put("SendTime", StringUtils.getBigString(data1.getString("sendtime1"), data1.getString("sendtime2")));
            data2.put("CreateDate", StringUtils.getBigString(data1.getString("createdate1"), data1.getString("createdate2")));
            data2.put("ShipDate", StringUtils.getBigString(data1.getString("shipdate1"), data1.getString("shipdate2")));
            data2.put("State", "8");
            DataMsg.add(data2);
            jsonObject.put("DataMsg", DataMsg);
            log.info("login request:" + jsonObject.toJSONString());
            String dataResult = HttpUtils.sendPostByJson(SFLUrl.Certificate.getUrl(), jsonObject.toJSONString());
            log.info("login response:" + dataResult);
            JSONObject msg = JSON.parseObject(dataResult);
            if (!msg.getBoolean("success")) {
                item.setState("2");
                item.setUpdateTime(DateUtils.getNowDate());
                sflCertificateService.updateTSflCertificate(item);
                SpringUtils.getBean(SFLMsgResServiceImpl.class).insertSFLMsgRes("质保书数据", jsonObject.toJSONString(), msg.getString("success"), dataResult);
                continue;

            }
            SpringUtils.getBean(SFLMsgResServiceImpl.class).insertSFLMsgRes("质保书数据", jsonObject.toJSONString(), msg.getString("success"), dataResult);

            String fileResult = warrantyFtpService.readFtpFile(data1.getString("pdfdir"), data1.get("millsheetno") + ".pdf", SFLUrl.CertificateUpload.getUrl(), data1.getString("millsheetname"));
//        String fileResult = warrantyFtpService.readFtpFile("/targetpdflimit/202242/","1883210132901001.pdf",FileUrl,"1883210132901001.pdf");
            if (StringUtils.isBlank(fileResult)) {
                item.setState("3");
                item.setUpdateTime(DateUtils.getNowDate());
                sflCertificateService.updateTSflCertificate(item);
                SpringUtils.getBean(SFLMsgResServiceImpl.class).insertSFLMsgRes("质保书文件", data1.getString("millsheetname"), "false", fileResult);
                continue;
            }
            JSONObject res = JSON.parseObject(fileResult);
            if (res.getBoolean("success")) {
                item.setState("1");
                item.setUpdateTime(DateUtils.getNowDate());
                sflCertificateService.updateTSflCertificate(item);
            } else {
                item.setState("3");
                item.setUpdateTime(DateUtils.getNowDate());
                sflCertificateService.updateTSflCertificate(item);
            }
            SpringUtils.getBean(SFLMsgResServiceImpl.class).insertSFLMsgRes("质保书文件", data1.getString("millsheetname"), res.getString("success"), fileResult);

        }

    }


    /**
     * 定时获取主页用户访问数据
     */
    public void updateCaterOrderStatus() {
        //获取用户访问数据
        log.info("定时结束点餐订单------开始");
        Date nowDate = DateUtils.getNowDate();
        String orderDate = DateFormatUtils.format(nowDate, "yyyy-MM-dd");
        List<CateringOrder> cateringOrders = cateringOrderMapper.selectCateringOrderList(orderDate);
        if (cateringOrders.size() > 0) {
            for (CateringOrder order : cateringOrders) {
                order.setStatus("1");
                order.setUpdateTime(DateUtils.getNowDate());
                cateringOrderMapper.updateCateringOrder(order);
            }
        }
        log.info("定时结束点餐订单------结束");

    }

    /**
     * 插入操作记录
     *
     * @param visitNo
     * @param remark
     */
    public void insertLog(String visitNo, String remark) {
        AuditVisit visitLog = new AuditVisit();
        visitLog.setVisitNo(visitNo);
        visitLog.setRemark(remark);
        visitLog.setCreateTime(DateUtils.getNowDate());
        auditVisitMapper.insertLog(visitLog);
    }

    /**
     * 通过8小时内未响应客户走访申请
     */
    public void batchAuditVisit() {
        //获取用户访问数据
        log.info("定时通过走访申请------开始");
        Date nowDate = DateUtils.getNowDate();
        //查询超过8小时未通过的走访申请
        List<AuditVisit> list = auditVisitMapper.selectAwaitVerifyList(nowDate);
        //批量通过申请
        int count = auditVisitMapper.updateVisitProcess(nowDate);
        if (count > 0) {
            log.info("数据更新成功");
        } else {
            log.info("数据未更新");
        }
        //批量插入走访记录
        for (AuditVisit item : list) {
            insertLog(item.getVisitNo(), item.getApproverName() + "通过走访计划 ");
        }
        log.info("定时任务结束------结束");
    }


    /**
     * 删除超过一个月没有提交的暂存状态或者驳回状态的请假信息
     */
    public void crondeletehrleaveinfo() {
        //获取用户访问数据
        log.info("定时删除请假申请------开始");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);
        Date m = c.getTime();
        HrLeaveInfo hrLeaveInfo = new HrLeaveInfo();
        hrLeaveInfo.setCreateTime(m);
        int count = hrLeaveInfoService.crondelete(hrLeaveInfo);
        if (count > 0) {
            log.info("数据清理成功");
        } else {
            log.info("数据未清理");
        }
        log.info("定时任务结束------结束");
    }

    /**
     * 销售评分初始化
     */
    public void initSaleScoreItem() {
        for (FrequencyDesc item : FrequencyDesc.values()) {
            String frequency = item.getCode();
            //按月评价
            if (frequency.equals(FrequencyDesc.Month.getCode())) {
                //根据评率获取规则list
                List<Map> ruleList = saleScoreMapper.getRuleListByFrequency(frequency);
                for (Map ruleItem : ruleList) {
                    //content添加真实评分0；
                    List<Map> content = JSONArray.parseArray(ruleItem.get("content").toString(), Map.class);
                    for (Map contentItem : content) {
                        contentItem.put("grade", contentItem.get("itemCount"));
                    }


                    //createTime
                    Date createTime = DateUtils.getNowDate();
                    //status、scoreTime
                    Integer status = ScoreItemStatus.Init.getCode();
                    String scoreTime = DateFormatUtils.format(DateUtils.getNowDate(), "yyyy-MM");

                    //根据ruleId获取userList
                    List<Map> userList = saleScoreMapper.getUserListByRuleList(Integer.valueOf(ruleItem.get("ruleId").toString()));
                    //根据被评roleCode 划分为被评 markedList  和打分人 markList
                    List<Map> markedList = userList.stream().filter(x -> {
                                if (Objects.nonNull(x.get("roleCode")) && x.get("roleCode").toString().equals(ruleItem.get("roleCode").toString())) {
                                    return true;
                                }
                                return false;
                            }
                    ).collect(Collectors.toList());
                    List<Map> markList = userList.stream().filter(x -> {
                                if (Objects.nonNull(x.get("roleCode")) && x.get("roleCode").toString().equals(ruleItem.get("roleCode").toString())) {
                                    return false;
                                }
                                return true;
                            }
                    ).collect(Collectors.toList());

                    for (Map markedUser : markedList) {
                        //筛选某被评人同销售公司人员（防止多个后勤人员）
                        List<Map> userMarkList = markList.stream().filter(x -> {
                            if (Objects.nonNull(x.get("saleCode")) && x.get("saleCode").toString().equals(markedUser.get("saleCode").toString())) {
                                return true;
                            }
                            return false;
                        }).collect(Collectors.toList());

                        List<Map> scoreItemList = new ArrayList<>();
                        for (Map userMarkItem : userMarkList) {
                            Map<String, Object> scoreItem = new HashMap<>();
                            scoreItem.put("ruleId", Integer.valueOf(ruleItem.get("ruleId").toString()));
                            scoreItem.put("status", status);
                            scoreItem.put("scoreTime", scoreTime);
                            scoreItem.put("markNo", markedUser.get("workNo").toString());
                            scoreItem.put("markName", markedUser.get("workName").toString());
                            scoreItem.put("userId", Integer.valueOf(userMarkItem.get("userId").toString()));
                            scoreItem.put("scoreContent", JSON.toJSONString(content));
                            scoreItem.put("createTime", createTime);
                            scoreItemList.add(scoreItem);
                        }
                        saleScoreMapper.insertScoreItemTask(scoreItemList);

                    }
                }

            }

        }
    }

    /**
     * 同步产销精整列表
     */
    public void updateTMMZGLZKList() {
        List<Map> list = xcerpMapper.selectTMMZGLZKList();
        if (list.size() > 0) {
            xcerpMapper.deleteTMMZGLZKList();
            xcerpMapper.insertTMMZGLZKList(list);
        }
    }


    /**
     * 计量计划号详情发送智慧物流
     */
    public void sendPlanTask() {
        try {
            log.info("定时任务开始------发送智慧物流计划详情开始");
            String params = JSON.toJSONString(iPlanTaskService.getPlanTaskbyTime());
            Header[] headers = {new BasicHeader("appKey", "nx23102000"),
                    new BasicHeader("appSecret", "zWhDnVjPsWYH3ZxVEjG8")};
            log.info("智慧物流计划号推送请求：" + params);
            String res = HttpUtil.doPost(measurePlanUrl, headers, params);
            log.info("智慧物流计划号推送结果：" + res);
            JSONObject msg = JSON.parseObject(res);
            if (!"200".equals(msg.get("code").toString())) {
                log.info("请求失败，返回结果：" + res);
                throw new RuntimeException("返回码异常");
            }
            log.info("定时任务结束------结束");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("智慧物流计划号推送异常", e);
            throw new RuntimeException("xx定时任务异常");
        }
    }

    public void remindQsTrace() {
        for (QsRoleType item : QsRoleType.values()) {
            QsTrace dto = new QsTrace();
            dto.setUnionName(item.getName());
            dto.setWrittenYm(DateFormatUtils.format(DateUtils.getNowDate(), "yyyy-MM"));
            List<QsTrace> list = SpringUtils.getBean(QsTraceServiceImpl.class).findList(dto);
            if (list.size() <= 0)
                MailUtils.sendWorkEmail(item.getWorkNo(), "各分工会主席：请于25日前填写本月脱单工程跟踪表,系统可从OA首页信息系统->兴澄数智应用平台进入,进入系统后在左侧菜单找到脱单工程目录点开分工会跟踪页面即可填报");
        }

    }

    public void sysUserUpdate() throws ParseException {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -3);
        Date yesterday = calendar.getTime();
        List<Map> list = SpringUtils.getBean(QsUserMapper.class).getRSUserMap(yesterday);
        list = list.stream().filter(StreamUtils.distinctByKey(b -> b.get("CODE"))).collect(Collectors.toList());
        List<Map> allUsers = SpringUtils.getBean(SysUserMapper.class).selectAllUserNames();
        List<String> userNames = allUsers.stream().map(x -> x.get("userName").toString()).collect(Collectors.toList());

        List<SysUser> userList = new ArrayList();
        List<SysUser> OAUserList = new ArrayList();
        list.forEach(item -> {
            SysUser user = new SysUser();
            user.setUserName(item.get("CODE").toString());
            user.setNickName(item.get("NAME").toString());
            user.setRsDeptCode(item.get("CORCODE").toString());
            user.setRsDeptName(item.get("CODEITEMDESC").toString());

            if (!userNames.contains(item.get("CODE").toString())) {
                log.info(item.get("CODE").toString());
                log.info(item.get("CARD").toString());
                user.setIdCard(item.get("CARD").toString());
                user.setPassword(item.get("CARD").toString());
                userList.add(user);
            } else {
                Map userItem = allUsers.stream().filter(x -> x.get("userName").toString().equals(item.get("CODE").toString())).findFirst().orElse(null);

                if (userItem.get("delFlag").toString().equals("0")) {
                    if (Objects.nonNull(userItem.get("rsDeptCodeOld")))
                        user.setRsDeptCodeOld(userItem.get("rsDeptCodeOld").toString());
                    if (Objects.nonNull(userItem.get("rsDeptNameOld")))
                        user.setRsDeptNameOld(userItem.get("rsDeptNameOld").toString());

                    if (!user.getRsDeptCode().equals(user.getRsDeptCodeOld())) userList.add(user);
                    // 组织电文信息
                    if (Objects.nonNull(user.getRsDeptCodeOld()) && !user.getRsDeptCode().equals(user.getRsDeptCodeOld())) {
                        OAUserList.add(user);
//                        Map<String, Object> map = new HashMap<>();
//                        map.put("Ename", user.getUserName().substring(1));
//                        map.put("Cname", user.getNickName());
//                        map.put("deptCodeOld", user.getRsDeptCodeOld());
//                        map.put("deptNameOld", user.getRsDeptNameOld());
//                        map.put("deptCode", user.getRsDeptCode());
//                        map.put("deptName", user.getRsDeptName());
//                        map.put("receiver", "XCC1");
//                        map.put("msgNo", "APXXS2");
//                        // 发送产销电文
//                        SpringUtils.getBean(SocketController.class).sendJson(map);
//
//                        map.put("receiver", "XCB1");
//                        map.put("msgNo", "APXBS2");
//                        // 发送产销电文
//                        SpringUtils.getBean(SocketController.class).sendJson(map);
//                        List<SysUserRole> userRoleList = SpringUtils.getBean(SysUserRoleMapper.class).selectByUserId(new Long(userItem.get("userId").toString()));
//                        if(userRoleList.size()>0)SpringUtils.getBean(SysUserRoleMapper.class).batchUserRoleBack(userRoleList);
//                        SpringUtils.getBean(SysUserRoleMapper.class).deleteUserRoleByUserId(new Long(userItem.get("userId").toString()));

                    }
                }


            }


        });
        if (OAUserList.size() > 0) {
            StringBuilder content = new StringBuilder();
            content.append("<h1>部门更新名单</h1>");
            content.append("<table border=1 >");
            content.append("  <tr>" +
                    "    <th width = 200>工号</th>" +
                    "    <th width = 100>姓名</th>" +

                    "    <th width = 100>原部门code</th>" +
                    "    <th width = 100>原部门名称</th>" +
                    "    <th width = 100>新部门code</th>" +
                    "    <th width = 100>新部门名称</th>" +
                    "  </tr>");
            OAUserList.forEach(item -> {
                content.append("  <tr>" +
                        "    <th>" + item.getUserName() + "</th>" +
                        "    <th>" + item.getNickName() + "</th>" +

                        "    <th>" + (Objects.nonNull(item.getRsDeptCodeOld()) ? item.getRsDeptCodeOld() : " ") + "</th>" +
                        "    <th>" + (Objects.nonNull(item.getRsDeptCodeOld()) ? item.getRsDeptNameOld() : " ") + "</th>" +
                        "    <th>" + item.getRsDeptCode() + "</th>" +
                        "    <th>" + item.getRsDeptName() + "</th>" +
                        "  </tr>");
            });

            content.append("</table>");

            MailUtils.sendWorkEmail("X6001729073", content.toString());
            log.info(content.toString());
        }
//            MailUtils.sendWorkEmail("X20027605",content.toString());
        if (userList.size() > 0) {
            String message = SpringUtils.getBean(SysUserServiceImpl.class).importUser(userList, true, "system");

            log.info(message);
        }


    }
//
//    public void sendVestasCertificate() {
//        String url = "http://172.1.203.127:8092/workflows/b0bdfee1156540aab65d7ceef430a443/triggers/manual/paths/invoke?api-version=2016-10-01&sp=/triggers/manual/run&sv=1.0&sig=KZw_etY31yP264E9qRBzOzXJvEvoPkKjGnajADYgIas" ;
//        File pdfFile = new File("E:\\项目代码\\xctg\\test111.pdf");
////        File pdfFile = new File("/usr/local/ruoyi/pdf/test111.pdf");
//        JSONObject params = new JSONObject();
//        params.put("AccountId","8e596f67-4e54-4e49-a9fd-1eb14be49a28");
//
//        params.put("SupplierPlantMappingKey", "CiticSteel");
//        params.put("SupplierPlantMappingIdentifier", "CiticSteel");
//
//        params.put("ReceiverPlantMappingKey", "VestasSAP");
//
//        /* 要做对应关系 */
//        params.put("ReceiverPlantMappingIdentifier", "3411");//vestas 供应商代码
//
//        params.put("PurchaseOrder", "**********");//合同号
//        params.put("PurchaseOrderLine", "1112");
//        params.put("VestasPurchaseOrder", true);
//
//        params.put("ItemNumber", "555555");//订单子项号
//
//
//        /*可不填*/
//        params.put("ItemDescription", "111");
//        params.put("ItemCategory", "111");
//
//        params.put("SerialNumber", "T35260810314");//材料号
//        params.put("BatchNumber", "S307588");//炉号
//
//        JSONObject CustomProperties = new JSONObject();
//
//        /* 钢种要筛选 */
//        CustomProperties.put("SteelGrade", "S355J0");//钢种
//        CustomProperties.put("Weight", 5362);
//        CustomProperties.put("Length", 12609);
//        CustomProperties.put("Width", 2763);
//        CustomProperties.put("Height", 20.2);
//        CustomProperties.put("TensileStrength", 5362);
//        CustomProperties.put("YieldStrength", 12609);
//        CustomProperties.put("CharpyValue", 2763);
//        CustomProperties.put("Elongation", 20.2);
//        CustomProperties.put("CEQ-CEV", 20.2);
//
//        params.put("Content-Type", "application/pdf");
//        params.put("Content-Length", pdfFile.length());
//        params.put("Content-Disposition", "attachment;filename='"+pdfFile.getName()+"'");
//        params.put("Byte64EncodedFileData", PDFFileUtils.PDFToBase64(pdfFile));
//        String res = HttpUtils.sendVestasPostByJson(url, params);
//        log.info(res);
//
//
//
//    }

    public void sendVestasCertificate() {
        List<TQMTCVST> list = tqmtcvstServive.findListTimeTask();
        for (TQMTCVST item:list){
            JSONObject params = new JSONObject();
            VestasMsg msg = new VestasMsg();
            params.put("AccountId", vestasAccountId);
            params.put("SupplierPlantMappingKey", vestasSupplierKey);
            params.put("SupplierPlantMappingIdentifier", vestasSupplierIdentifier);

            params.put("ReceiverPlantMappingKey", vestasReceiverKey);

            /* 要做对应关系 */
            params.put("ReceiverPlantMappingIdentifier", item.getSALE_ORG());//vestas 供应商代码

            if(StringUtils.isBlank(item.getSALE_ORG())||StringUtils.isBlank(item.getCUSTOMERNO())||StringUtils.isBlank(item.getPOLINENUM())) continue;

            params.put("PurchaseOrder", (StringUtils.isNotBlank(item.getPOLINENUM())) ? item.getPOLINENUM() : item.getORDER_NO());//合同号
            params.put("PurchaseOrderLine", (StringUtils.isNotBlank(item.getPOLINENUM())) ? item.getPOLINENUM() : item.getORDER_NO());
            params.put("VestasPurchaseOrder", false);
            params.put("ItemNumber", (StringUtils.isNotBlank(item.getCUSTOMERNO())) ? item.getCUSTOMERNO() : item.getORDER_NO());//订单子项号


            /*可不填*/
            params.put("ItemDescription", "plate");
            params.put("ItemCategory", "plate");

            String SerialNumber = item.getPDFFILE().replace(".pdf", "");
            params.put("SerialNumber", SerialNumber);//材料号
            params.put("BatchNumber", item.getSERIALNO());//材料号
            JSONObject CustomProperties = new JSONObject();

            /* 钢种要筛选 */
            CustomProperties.put("SteelGrade", item.getSTEELGRADE());//钢种
            CustomProperties.put("Weight", item.getMATWEIGHT());
            CustomProperties.put("Length", item.getMATLENGTH());
            CustomProperties.put("Width", item.getMATWIDTH());
            CustomProperties.put("Height", item.getMATHEIGHT());
            CustomProperties.put("TensileStrength", item.getT_S());
            CustomProperties.put("YieldStrength", item.getY_S());
            CustomProperties.put("CharpyValue", item.getCHARPY_AVG());
            CustomProperties.put("Elongation", item.getELONGATION());
            CustomProperties.put("CEQ-CEV", item.getCEQ());

            params.put("CustomProperties", CustomProperties);
            msg.setOrderNo(params.getString("PurchaseOrder"));
            msg.setMatNo(params.getString("BatchNumber"));
            msg.setContent(params.toJSONString());

            if(StringUtils.isBlank(item.getPDFPATH())) continue;
            File pdfFile = warrantyFtpService.getFtpFile("/targetpdflimit/" + item.getPDFPATH(), item.getPDFFILE());
            if (Objects.nonNull(pdfFile) || pdfFile.length() == 0) {
                params.put("Content-Type", "application/pdf");
                params.put("Content-Length", pdfFile.length());
                params.put("Content-Disposition", "attachment;filename='" + pdfFile.getName() + "'");
                params.put("Byte64EncodedFileData", PDFFileUtils.PDFToBase64(pdfFile));
                String res = HttpUtils.sendVestasPostByJson(vestasUrl, params);
                 pdfFile.delete();
                log.info(res);
                if (StringUtils.isNotBlank(res) && res.contains("Success")) msg.setRes("1");
                else msg.setRes("0");
                msg.setResContent(res);
            } else {
                pdfFile.delete();
                msg.setRes("0");
                msg.setResContent("质保书不存在");
            }

            msg.setUserName("system");
            vestasMsgService.insert(msg);
        };


    }


    public void updateCPSCompany() throws ParseException {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -2);
        Date yesterday = calendar.getTime();
        CompanyInfoList companyInfoList = new CompanyInfoList();
        companyInfoList.setStartTimeTemp(DateUtils.parseDateTimeToStr("yyyy-MM-dd HH:mm:ss", new DateTime(yesterday.getTime())));
        List<CompanyInfoList> list = SpringUtils.getBean(CompanyInfoListMapper.class).selectCompanyListList(companyInfoList);
        Map params = new HashMap();
        params.put("params", JSON.toJSONString(list));
        if (list.size() > 0) {
            String url = "http://172.1.203.27:8080/system/xctg/company/updateCompany";
            String s = HttpUtil.doPost(url, params);
            log.info(s);

        }
    }

    @Autowired
    private IQDService qdService;

    public void updateQDFile() throws ParseException {
        qdService.uploadFile();
    }

    private final String mentalHealthUrl = "https://booking.yuefly.com/index/index/synchronous";

    public void synchronousMentalHealthUsers() throws ParseException {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -3);
        Date yesterday = calendar.getTime();
        List<Map> list = SpringUtils.getBean(QsUserMapper.class).getRSUserMapAndPhone(yesterday);
        JSONObject jsonObject = new JSONObject();
        JSONArray params = new JSONArray();
        log.info("心理咨询人员本次同步人数为：" + list.size());
        list.forEach(item -> {
            JSONObject obj = new JSONObject();
            obj.put("real_name", item.get("NAME"));
            obj.put("phone", Objects.nonNull(item.get("phone")) ? SecurityUtils.encryptPassword(item.get("phone").toString()) : "");
            obj.put("card_id", Objects.nonNull(item.get("CARD")) ? SecurityUtils.encryptPassword(item.get("CARD").toString()) : "");
//            obj.put("phone", item.get("phone"));
//            obj.put("card_id",item.get("CARD"));
            obj.put("status", new BigDecimal(1.00).compareTo(new BigDecimal(item.get("DELFLAG").toString())) == 0 ? 1 : 2);
            params.add(obj);
            if (params.size() > 49) {
                jsonObject.put("data", params);
                String msg = jsonObject.toJSONString();
                if (params.size() > 0) {
                    String res = HttpUtil.doPostNoSSLHeader(mentalHealthUrl, jsonObject, null);
                    log.info("心理咨询人员信息同步(" + params.size() + ")：" + res);
                    params.clear();
                }
            }
        });

        jsonObject.put("data", params);
        String msg = jsonObject.toJSONString();
        if (params.size() > 0) {
            String res = HttpUtil.doPostNoSSLHeader(mentalHealthUrl, jsonObject, null);
            log.info("心理咨询人员信息同步(" + params.size() + ")：" + res);
        }
    }
    
    /**
     * 定时统计当天提交并当天审核的积分记录，为审核人添加积分
     * 每日23:59执行，每审核1条获得1积分
     */
    public void dailyCheckPointsReward() {
        log.info("开始执行定时任务：每日审核积分奖励统计");
        try {
            pointsRecordService.dailyCheckPointsTask();
            log.info("每日审核积分奖励统计任务执行完成");
        } catch (Exception e) {
            log.error("每日审核积分奖励统计任务执行失败", e);
        }
    }
}
