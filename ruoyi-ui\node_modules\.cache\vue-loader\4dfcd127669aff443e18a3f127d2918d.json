{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardMain\\index.vue?vue&type=template&id=5eb4b045&scoped=true", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardMain\\index.vue", "mtime": 1756456493834}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}