package com.ruoyi.app.v1.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseSoEntity;

import java.util.List;


/**
 * 绩效考核-干部自评人员配置对象 hr_self_assess_user
 * 
 * <AUTHOR>
 * @date 2024-03-08
 */


@Data
public class HrSelfAssessUser extends BaseSoEntity <HrSelfAssessUser>
{
    /** 编号 */
    private String id;

    /** 工号 */
    @Excel(name = "工号")
    private String workNo;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 身份 */
    @Excel(name = "身份", dictType = "self_assess_role")
    private String assessRole;

    /** 是否挂钩公司效益 */
    @Excel(name = "是否100%挂钩公司效益", dictType = "sys_yes_no")
    private String benefitLinkFlag;

    /** 是否挂勾钢铁轧平均分 */
    @Excel(name = "是否挂钩钢铁轧平均分", dictType = "sys_yes_no")
    private String averageLinkFlag;

    @Excel(name="部门")
    private String deptNames;

    private Long deptId;

    // 部门id列表
    private Long[] deptIds;

    // 横向部门列表
    private List<HrLateralAssessDept> deptList;

    // 岗位类型
    @Excel(name = "岗位类型", dictType = "self_assess_post_type")
    private String postType;

    // 岗位类型
    @Excel(name = "职务")
    private String job;

    // 交叉评分领导工号数组
    private String[] leaders;

    @Excel(name = "条线领导",separator = ",")
    private String leaderNames;

    private List<HrSelfAssessUser> leaderList;

    // 填写状态（前端显示用）
    private String assessStatus;
}
