<template>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-row>
        <el-form-item label="考核年月" prop="assessDate">
          <el-date-picker
            v-model="queryParams.assessDate"
            type="month"
            value-format="yyyy-M"
            format="yyyy 年 M 月"
            placeholder="选择考核年月"
            @change="handleQuery"
            :clearable="false">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="部门" prop="deptId">
          <el-select v-model="queryParams.deptId" placeholder="请选择部门" 
            @change="handleQuery">
            <el-option
              v-for="item in deptOptions"
              :key="item.deptId"
              :label="item.deptName"
              :value="item.deptId"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button type="warning" icon="el-icon-s-tools" size="mini" @click="handleConfig">指标配置</el-button>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="当前状态">
          <el-tag v-if="status == '0' && rejectReason" type="danger">退 回</el-tag>
          <el-tag v-if="status == '0' && !rejectReason" type="info">未提交</el-tag>
          <el-tag v-if="status == '1'" type="warning">部门领导评分</el-tag>
          <el-tag v-if="status == '2'" type="warning">事业部评分</el-tag>
          <el-tag v-if="status == '3'" type="warning">运改部/组织部审核</el-tag>
          <el-tag v-if="status == '4'" type="warning">总经理部评分</el-tag>
          <el-tag v-if="status == '5'" type="success">已完成</el-tag>
        </el-form-item>
      </el-row>
      <el-row v-if="rejectReason">
        <el-form-item label="退回原因" prop="rejectReason">
            <span class="el-icon-warning" style="color: #f56c6c;"></span>
            {{ rejectReason }}
        </el-form-item>
      </el-row>
    </el-form>
      <h3 style="text-align: center;">月度业绩考核表</h3>
      <el-descriptions class="margin-top" :column="3">
        <el-descriptions-item>
          <template slot="label">
            姓名
          </template>
          {{ userInfo.name }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            部门
          </template>
          {{ deptName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            考核年月
          </template>
          {{ assessDateText }}
        </el-descriptions-item>
      </el-descriptions>
      <el-table v-loading="loading" :data="list"
        :span-method="objectSpanMethod" border>
        <el-table-column label="类型" align="center" prop="item" width="120"/>
        <el-table-column label="指标" align="center" prop="category" width="140"/>
        <el-table-column label="目标" align="center" prop="target" width="150" />
        <el-table-column label="评分标准" align="center" prop="standard" />
        <el-table-column label="完成实绩（若扣分，写明原因）" align="center" prop="performance" width="440">
            <template slot-scope="scope">
                <span v-if="readOnly">{{ scope.row.performance }}</span>
                <div v-else style="display: flex">
                  <!-- <el-button icon="el-icon-search" size="small"></el-button> -->
                  <el-popover
                    placement="left"
                    width="636"
                    trigger="click"
                    :ref="'popover' + scope.$index">
                    <el-table :data="beAssessedList">
                      <el-table-column width="150" property="assessDeptName" label="提出考核单位"></el-table-column>
                      <el-table-column width="300" property="assessContent" label="事项"></el-table-column>
                      <el-table-column width="80" property="deductionOfPoint" label="加减分"></el-table-column>
                      <el-table-column width="80" label="操作">
                        <template slot-scope="beAssessed">
                          <el-button
                            size="mini"
                            type="text"
                            icon="el-icon-edit"
                            @click="handleBeAssessedClick(scope.row,beAssessed.row,scope.$index)"
                          >填入</el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button slot="reference" icon="el-icon-search" size="small"></el-button>
                  </el-popover>
                  <el-input
                    :ref="`performance_${scope.$index}`"
                    class="table-input"
                    type="textarea"
                    autosize
                    v-model="scope.row.performance"
                    placeholder="请输入完成实绩"
                    @blur="validateField(scope.row, 'performance')"
                    :class="{'is-error': scope.row.performanceError}" />
                  <div v-if="scope.row.performanceError" class="el-form-item__error">{{ scope.row.performanceError }}</div>
                </div>
            </template>
        </el-table-column>
        <el-table-column label="加减分" align="center" prop="dePoints" width="108">
            <template slot-scope="scope">
                <span v-if="readOnly">{{ scope.row.dePoints }}</span>
                <div v-else>
                  <el-input
                    :ref="`dePoints_${scope.$index}`"
                    class="table-input"
                    type="number"
                    autosize
                    v-model="scope.row.dePoints"
                    placeholder="请输入加减分"
                    @input="scoreInput(scope.row)"
                    @blur="validateField(scope.row, 'dePoints')"
                    :class="{'is-error': scope.row.dePointsError}" />
                  <div v-if="scope.row.dePointsError" class="el-form-item__error">{{ scope.row.dePointsError }}</div>
                </div>
            </template>
        </el-table-column>
        <el-table-column label="加减分原因" align="center" prop="pointsReason"  width="440">
            <template slot-scope="scope">
                <span v-if="readOnly">{{ scope.row.pointsReason }}</span>
                <div v-else>
                  <el-input
                    :ref="`pointsReason_${scope.$index}`"
                    class="table-input"
                    type="textarea"
                    autosize
                    v-model="scope.row.pointsReason"
                    placeholder="请输入加减分原因"
                    @blur="validateField(scope.row, 'pointsReason')"
                    :class="{'is-error': scope.row.pointsReasonError}" />
                  <div v-if="scope.row.pointsReasonError" class="el-form-item__error">{{ scope.row.pointsReasonError }}</div>
                </div>
            </template>
        </el-table-column>
      </el-table>

      <el-form size="small" :inline="false" label-width="200px" style="margin-top: 10px;" label-position="left">
            <el-form-item v-if="readOnly" label="自评分数 / 签名：" prop="deptId">
              <div style="display: flex;">
                <span >{{ selfScore + " 分 / " }}</span>
                <span v-if="!selfSign">{{info.name}}</span>
                <el-image v-else
                  style="width: 100px; height: 46px"
                  :src="selfSign.url"></el-image>
              </div>
              
            </el-form-item>
            <el-form-item v-else label="自评分数：" prop="selfScore">
              <div style="display: flex;width: 180px;">
                <el-input type="number" autosize v-model="selfScore" placeholder="请输入分数" :readonly="readOnly" />
                <span style="margin-left: 8px;">分</span>
              </div>
            </el-form-item>
            <!-- <el-form-item v-if="status > '1' && info.deptScore && info.deptUserName" label="部门领导评分 / 签名：">
              <span >{{ info.deptScore + " 分 / " + info.deptUserName }}</span>
            </el-form-item>
            <el-form-item v-if="status > '2' && info.businessUserName && info.businessScore" label="事业部领导评分 / 签名：">
              <span>{{ info.businessScore + " 分 / " + info.businessUserName }}</span>
            </el-form-item> -->
            
            <!-- 部门领导评分 -->
            <el-form-item v-if="info && info.deptScore && info.deptUserName">
              <template slot="label">
                <span style="color: #606266;">
                  部门领导评分 / 签名：
                </span>
              </template>
              <div class="signature-content">
                <span class="score-text">{{ info.deptScore }} 分</span>
                <span class="separator">/</span>
                <span class="signature-name">{{ info.deptUserName }}</span>
                <div v-if="info.deptScoreReason" class="reason-text">
                  <span class="reason-label">加减分理由：</span>
                  <span class="reason-content">{{ info.deptScoreReason }}</span>
                </div>
              </div>
            </el-form-item>
            
            <!-- 事业部领导评分 -->
            <el-form-item v-if="info && info.businessUserName && info.businessScore">
              <template slot="label">
                <span style="color: #606266;">
                  事业部领导评分 / 签名：
                </span>
              </template>
              <div class="signature-content">
                <span class="score-text">{{ info.businessScore }} 分</span>
                <span class="separator">/</span>
                <span class="signature-name">{{ info.businessUserName }}</span>
                <div v-if="info.businessScoreReason" class="reason-text">
                  <span class="reason-label">加减分理由：</span>
                  <span class="reason-content">{{ info.businessScoreReason }}</span>
                </div>
              </div>
            </el-form-item>
            
            <!-- 运改组织部评分 -->
            <el-form-item v-if="info && info.organizationScore && info.organizationUserName">
              <template slot="label">
                <span style="color: #606266;">
                  运改组织部评分 / 签名：
                </span>
              </template>
              <div class="signature-content">
                <span class="score-text">{{ info.organizationScore }} 分</span>
                <span class="separator">/</span>
                <span class="signature-name">{{ info.organizationUserName }}</span>
                <div v-if="info.organizationScoreReason" class="reason-text">
                  <span class="reason-label">加减分理由：</span>
                  <span class="reason-content">{{ info.organizationScoreReason }}</span>
                </div>
              </div>
            </el-form-item>


            <!-- <el-form-item v-if="status > '4' && info.leaderScore && info.leaderName" label="总经理部领导评分 / 签名：" prop="deptId">
              <span >{{ info.leaderScore + " 分 / " + info.leaderName }}</span>
            </el-form-item> -->
          </el-form>
      <div v-if="!readOnly" style="text-align: center;">
        <el-button v-if="resetShow" plain type="info" @click="resetInfo">重 置 </el-button>
        <el-button type="success" @click="save">保 存</el-button>
        <el-button type="primary" @click="onSubmit">提 交</el-button>
      </div>

      <!-- 签名板 -->
      <el-dialog title="签字确认" :visible.sync="openSign" width="760px" append-to-body>
        <div style="border: 1px #ccc solid;width: 702px;">
            <vue-signature-pad
            width="700px"
            height="300px"
            ref="signaturePad"
            :options="signOptions"
          />
        </div>
        <div style="text-align: center;padding: 10px 10px;">
          <el-button style="margin-right: 20px;" type="success" @click="clearSign">清除</el-button>
          <el-button type="primary" @click="uploadSignature">确认</el-button>
        </div>
      </el-dialog>
    </div>
  </template>

  <script>
  import { getInfoByDate, saveInfo, submitInfo, delInfo, listBeAssessed } from "@/api/assess/self/info";
  // import { batchTarget, listSelfTargetAll } from "@/api/assess/self/target";
  import { getReportDeptList, getByWorkNoDeptId } from "@/api/assess/self/user";
  import { VueSignaturePad } from 'vue-signature-pad';

  export default {
    components: {
      VueSignaturePad
    },
    name: "SelfAssessReport",
    data() {
      return {
        // 遮罩层
        loading: true,
        // 显示搜索条件
        showSearch: true,
        openSign:false,
        // 绩效考核-自评指标配置表格数据
        list: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          userId:null,
          workNo: null,
          deptId:null,
          assessDate: null,
        },
        // 考核年月文本显示
        assessDateText:null,
        // 部门显示
        deptName:null,
        // 表单参数
        form: {},
        // 表单校验
        rules: {
        },
        // 用户信息
        userInfo:{},
        // 合并单元格信息
        spanList:{
          itemList:[],
          standardList:[]
        },
        // 是否显示重置按钮
        resetShow:false,
        // 是否只读
        readOnly:false,
        // 部门选项
        deptOptions:[],
        // 自评信息Id
        id:null,
        // 自评分数
        selfScore:100,
        // 状态
        status:"0",
        // 自评信息
        info:{},
        // 横向被考评信息
        beAssessedList:[],
        // 退回理由
        rejectReason:"",
        // 自评签名
        selfSign:"",
        // 签名板配置
        signOptions: {
          onBegin: () => this.$refs.signaturePad.resizeCanvas(),
          backgroundColor: 'rgba(255, 255, 255, 1)'
        },
        sign:"",
        file:null,
        fileList:[],
        upload: {
          // 上传的地址
          url: process.env.VUE_APP_BASE_API + "/app/common/uploadMinio",
          isUploading: false,
        },
      };
    },
    created() {
      this.queryParams.assessDate = this.getDefaultAssessDate()
      this.assessDateText = this.queryParams.assessDate.replace("-"," 年 ") + " 月";
      // this.getSelfAssessUser();
      this.getReportDeptList();
    },
    methods: {

      // 获取默认考核日期
      getDefaultAssessDate() {
        const now = new Date();
        const currentDay = now.getDate();

        let targetDate;
        if (currentDay < 10) {
          // 当前日期小于10日，默认为上个月
          targetDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        } else {
          // 当前日期大于等于10日，默认为当月
          targetDate = new Date(now.getFullYear(), now.getMonth(), 1);
        }

        // 格式化为 YYYY-M 格式
        const year = targetDate.getFullYear();
        const month = targetDate.getMonth() + 1;
        return `${year}-${month}`;
      },

      // 获取部门信息
      getReportDeptList(){
        getReportDeptList().then(res => {
          console.log(res)
          if(res.code == 200){
            this.handleDeptList(res.data);
            // 根据部门获取用户信息
            this.getByWorkNoDeptId();
          }
        })
      },
      // 获取用户信息
      getByWorkNoDeptId(){
        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {
          console.log(res)
          if(res.code == 200){
            this.queryParams.userId = res.data.id;
            this.queryParams.workNo = res.data.workNo;
            this.userInfo = res.data;
            this.getList();
            // 获取被考核信息
            this.getBeAssessedList();
          }
        })
      },
      
      // 获取被考核信息
      getBeAssessedList(){
        listBeAssessed({deptId:this.queryParams.deptId,assessDate:this.queryParams.assessDate}).then(res =>{
          let beAssessedList = [];
          if(res.code == 200){
            if(res.data.length > 0){
              res.data.forEach(item => {
                beAssessedList = [...beAssessedList,...item.hrLateralAssessInfoList]
              })
              this.beAssessedList = beAssessedList;
            }
          }
          console.log(beAssessedList)
        })
      },
      /** 查询绩效考核-自评指标配置列表 */
      getList() {
        this.loading = true;
        getInfoByDate(this.queryParams).then(response => {
          console.log(response.data);
          // console.log(typeof response.data);
          if (Array.isArray(response.data)) {
            // 指标配置数据
            this.handleSpanList(response.data);
            this.list = response.data.map(item => {
              item.performance = "";
              // 根据项目类型设置默认分数
              let noSpaceStr = item.item.replace(/\s+/g, '');
              if(noSpaceStr.includes("月度重点工作")){
                item.dePoints = 5; // 月度重点工作默认5分
              } else {
                item.dePoints = 0; // 其他项目默认0分
              }
              return item;
            });
            this.status = "0";
            this.readOnly = false;
            this.resetShow = false;
            this.rejectReason = null;
            this.calculateSelfScore();
          } else {
            // 自评信息
            let info = response.data;
            let list = JSON.parse(info.content);
            this.handleSpanList(list);
            this.list = list;
            this.id = info.id;
            this.selfScore = info.selfScore;
            this.rejectReason = info.rejectReason;
            this.status = info.status;
            if(info.sign){
              this.selfSign = JSON.parse(info.sign);
            }
            this.info = info;
            if(info.status == "0"){
              this.readOnly = false;
              this.resetShow = true;
            }else{
              this.readOnly = true;
              this.resetShow = false;
            }
          }
          this.loading = false;
        });
      },

      // 处理列表
      handleSpanList(data){
        let itemList = [];
        let standardList = [];
        let itemFlag = 0;
        let standardFlag = 0;
        for(let i = 0; i < data.length; i++){
          // 相同考核项、评分标准合并
          if(i == 0){
            itemList.push({
              rowspan: 1,
              colspan: 1
            })
            standardList.push({
              rowspan: 1,
              colspan: 1
            })
          }else{
            // 考核项
            if(data[i - 1].item == data[i].item){
              itemList.push({
                rowspan: 0,
                colspan: 0
              })
              itemList[itemFlag].rowspan += 1;
            }else{
              itemList.push({
                rowspan: 1,
                colspan: 1
              })
              itemFlag = i;
            }
            // 评分标准
            if(data[i - 1].standard == data[i].standard){
              standardList.push({
                rowspan: 0,
                colspan: 0
              })
              standardList[standardFlag].rowspan += 1;
            }else{
              standardList.push({
                rowspan: 1,
                colspan: 1
              })
              standardFlag = i;
            }
          }
        }
        this.spanList.itemList = itemList;
        this.spanList.standardList = standardList;
      },


      // 处理部门下拉选项
      handleDeptList(data){
        // let syb = ["炼铁事业部","炼钢事业部","轧钢事业部","特板事业部","动力事业部","物流事业部","研究院"];
        let deptList = [];
        data.forEach(item => {
          //if(syb.indexOf(item.deptName) == -1){
            deptList.push({
              deptName:item.deptName,
              deptId:item.deptId
            })
          //}
        })
        this.deptOptions = deptList;
        if(deptList.length > 0){
          this.queryParams.deptId = deptList[0].deptId;
          this.deptName = deptList[0].deptName;
        }
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.assessDateText = this.queryParams.assessDate.replace("-"," 年 ") + " 月";
        this.deptOptions.forEach(item => {
          if(item.deptId == this.queryParams.deptId){
            this.deptName = item.deptName;
          }
        })
        this.id = null;
        this.info = null;
        this.selfScore = "";
        this.list = [];
        this.beAssessedList = [];
        this.rejectReason = "";
        this.readOnly = false;
        this.resetShow = false;
        this.getByWorkNoDeptId();
      },

      // 保存
      save(){
        if(this.list.length == 0){
          this.$message({
              type: 'warning',
              message: '未配置相关信息，请先配置指标'
            });
            return false;
        }
        let data = this.handleData(this.list);
        let form = {
          id:this.id,
          workNo:this.userInfo.workNo,
          assessDate:this.queryParams.assessDate,
          deptId:this.queryParams.deptId,
          content:JSON.stringify(data),
          status:"0",
          userId:this.queryParams.userId,
          deptName:this.deptName,
          name:this.userInfo.name,
          selfScore:this.selfScore,
          job:this.userInfo.job,
          postType:this.userInfo.postType
        }
        saveInfo(form).then(res => {
          if(res.code == 200){
            this.getList();
            this.$message({
              type: 'success',
              message: '保存成功!'
            });
          }
        })
      },

      // 确认提交点击事件
      submit(){
          this.$confirm('确认后将流转至下一节点, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.submitData();
          }).catch(() => {

          });
      },

      onSubmit(){
        if(this.verifyInsert()){
          this.openSign = true;
        }
      },

      clearSign(){
        this.$refs.signaturePad.clearSignature();
      },

      // 提交数据验证
      verifyInsert(){
        if(this.list.length == 0){
          this.$message({
              type: 'warning',
              message: '未配置相关信息，请先配置指标'
            });
            return false;
        }

        // 清除之前的错误状态
        this.clearValidationErrors();

        for(let i = 0; i < this.list.length; i++){
          const item = this.list[i];

          // 验证完成实绩
          if(!item.performance || item.performance.trim() === ''){
            this.$set(item, 'performanceError', '请填写完成实绩');
            this.focusAndScrollToField(`performance_${i}`, i + 1, '完成实绩');
            return false;
          }

          // 验证加减分
          if(item.dePoints === null || item.dePoints === undefined || item.dePoints === ''){
            this.$set(item, 'dePointsError', '请填写加减分');
            this.focusAndScrollToField(`dePoints_${i}`, i + 1, '加减分');
            return false;
          }

          // 验证加减分原因
          if(item.dePoints != 0 && (!item.pointsReason || item.pointsReason.trim() === '')){
            // 检查是否为月度重点工作
            let noSpaceStr = item.item.replace(/\s+/g, '');
            if(!noSpaceStr.includes("月度重点工作")){
              // 非月度重点工作需要填写加减分原因
              this.$set(item, 'pointsReasonError', '有加减分的请填写原因');
              this.focusAndScrollToField(`pointsReason_${i}`, i + 1, '加减分原因');
              return false;
            }
          }
        }

        if(!this.selfScore){
          this.$message({
              type: 'warning',
              message: '请填写自评分数'
            });
            return false;
        }
        return true;
      },

      // 新增数据
      submitData(){
        let data = this.handleData(this.list);
        let form = {
          id:this.id,
          workNo:this.userInfo.workNo,
          assessDate:this.queryParams.assessDate,
          deptId:this.queryParams.deptId,
          content:JSON.stringify(data),
          status:"1",
          userId:this.queryParams.userId,
          deptName:this.deptName,
          name:this.userInfo.name,
          selfScore:this.selfScore,
          job:this.userInfo.job,
          postType:this.userInfo.postType,
          averageLinkFlag:this.userInfo.averageLinkFlag,
          benefitLinkFlag:this.userInfo.benefitLinkFlag,
          sign:JSON.stringify(this.sign)
        }
        submitInfo(form).then(res => {
          if(res.code == 200){
            this.getList();
            this.sign = "";
            this.$refs.signaturePad.clearSignature();
            this.openSign = false;
            this.$message({
              type: 'success',
              message: '提交成功!'
            });
          }else{

          }
        })
      },

      // 保存重置
      resetInfo(){
        this.$confirm('重置后将清空所有已填写的内容，是否确认重置?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 删除保存信息
          delInfo({id:this.id}).then(res => {
            if(res.code == 200){
              this.id = null;
              this.selfScore = null;
              // 获取配置信息
              this.getList();
              this.$message({
                type: 'success',
                message: '重置成功!'
              });
            }
          }).catch(error => {
            console.error('重置失败:', error);
            this.$message({
              type: 'error',
              message: '重置失败，请重试'
            });
          });
        }).catch(() => {
          // 用户取消重置
        });
      },

      // 处理提交数据
      handleData(data){
        let result = []
        data.forEach(item => {
          let form = {
            item: item.item,
            category: item.category,
            target: item.target,
            standard: item.standard,
            performance: item.performance,
            dePoints: item.dePoints,
            pointsReason:item.pointsReason
          };
          result.push(form);
        })
        return result
      },

      /** 标准配置跳转 */
      handleConfig(){
        getByWorkNoDeptId({deptId:this.queryParams.deptId}).then(res => {
          console.log(res)
          if(res.code == 200){
            if(res.data.id){
            this.$router.push({
              path:"/assess/self/user/detail",
              query:{
                userId:res.data.id
              }
            })
          }
          }
        })
        
      },


      // 合并单元格方法
      objectSpanMethod({ row, rowIndex, columnIndex }) {
        // 第一列相同项合并
        if (columnIndex === 0) {
          return this.spanList.itemList[rowIndex];
        }
        // 评分标准相同合并
        if(columnIndex === 3){
          return this.spanList.standardList[rowIndex];
        }
        // 类别无内容 合并
        if(columnIndex === 1){
          if(!row.category){
            return {
              rowspan: 0,
              colspan: 0
            }
          }
        }
        if(columnIndex === 2){
          if(!row.category){
            return {
              rowspan: 1,
              colspan: 2
            }
          }
        }
      },

      // 被考核事项点击事件
      handleBeAssessedClick(row,optionRow,index){
        console.log(row)
        // 将事项填入完成实绩列（弃用）
        // if(row.performance){
        //   this.$set(row, 'performance', row.performance + "；" + optionRow.assessContent);
        // }else{
        //   this.$set(row, 'performance', optionRow.assessContent);
        // }
        
        // 将分数填入加减分列
        if(row.dePoints){
          this.$set(row, 'dePoints', Number(row.dePoints) + Number(optionRow.deductionOfPoint));
        }else{
          this.$set(row, 'dePoints', Number(optionRow.deductionOfPoint));
        }
        
        // 将事项+分数填入加减分理由列
        let reasonContent = optionRow.assessContent + "(" + optionRow.deductionOfPoint + "分)";
        if(row.pointsReason){
          this.$set(row, 'pointsReason', row.pointsReason + "；" + reasonContent);
        }else{
          this.$set(row, 'pointsReason', reasonContent);
        }
        
        this.$refs[`popover${index}`].showPopper = false;
        // 重新计算自评分数
        this.scoreInput();
      },

      // 加减分输入
      scoreInput(row = null){
        // 验证加减分规则（仅当传入row参数时进行验证）
        if (row && row.item) {
          let noSpaceStr = row.item.replace(/\s+/g, '');
          let value = row.dePoints;

          if (value !== null && value !== undefined && value !== '') {
            let numValue = Number(value);

            // 月度重点工作：只能为1、3或5分
            if (noSpaceStr.includes("月度重点工作")) {
              if (![1, 3, 5].includes(numValue)) {
                this.$message({
                  type: 'warning',
                  message: '月度重点工作为得分制，只能为1分、3分或5分'
                });
                this.$set(row, 'dePoints', null);
                return;
              }
            }
            // 加分项：除月度重点工作外，只能填0
            else if (noSpaceStr.includes("加分项")) {
              if (numValue !== 0) {
                this.$message({
                  type: 'warning',
                  message: '加分项只能填0分'
                });
                this.$set(row, 'dePoints', 0);
                return;
              }
            }
            // 其他类型项：只能填0或负数
            else {
              if (numValue > 0) {
                this.$message({
                  type: 'warning',
                  message: '该项目只能填0分或负数'
                });
                this.$set(row, 'dePoints', 0);
                return;
              }
            }
          }
        }

        // 重新计算自评分数
        this.calculateSelfScore();
      },

      /** 计算自评分数 */
      calculateSelfScore() {
        let points = 0;   // 月度重点工作分数
        this.list.forEach(item => {
            points += Number(item.dePoints);
        });
        // 计算总分：基础分85 + 加减分
        this.selfScore = 85 + points;
      },

      // 签名上传相关
      uploadSignature(){
        const { isEmpty, data } = this.$refs.signaturePad.saveSignature();
        console.log(isEmpty,data)
        if(isEmpty){
          this.$message({
            type: 'warning',
            message: '请签名!'
          });
          return false;
        }else{
          const blobBin = atob(data.split(',')[1]);
          let array = [];
          for (let i = 0; i < blobBin.length; i++) {
            array.push(blobBin.charCodeAt(i));
          }
          const fileBlob = new Blob([new Uint8Array(array)], { type: 'image/png' });
          const formData = new FormData();
          formData.append('file', fileBlob, `${Date.now()}.png`);
          fetch(this.upload.url, {
            method: 'POST',
            body: formData,
          })
          .then(response => response.json())
          .then(data => {
            console.log('Success:', data);
            if(data.code == 200){
              this.sign = {fileName:this.userInfo.name + ".png",url:data.url};
              this.submit();
            }else{
              this.$message({
                type: 'error',
                message: '签名上传失败'
              });
            }
          })
          .catch((error) => {
            console.error('Error:', error);
            this.$message({
              type: 'error',
              message: '签名上传异常'
            });
          });
        }

      },

      /** 验证单个字段（失焦时调用） */
      validateField(row, fieldType) {
        // 清除之前的错误信息
        this.$set(row, `${fieldType}Error`, '');

        if (fieldType === 'performance') {
          // 验证完成实绩
          if (!row.performance || row.performance.trim() === '') {
            this.$set(row, 'performanceError', '请填写完成实绩');
            return false;
          }
        } else if (fieldType === 'dePoints') {
          // 验证加减分
          if (row.dePoints === null || row.dePoints === undefined || row.dePoints === '') {
            this.$set(row, 'dePointsError', '请填写加减分');
            return false;
          }
        } else if (fieldType === 'pointsReason') {
          // 验证加减分原因
          if (row.dePoints != 0 && (!row.pointsReason || row.pointsReason.trim() === '')) {
            // 检查是否为月度重点工作
            let noSpaceStr = row.item.replace(/\s+/g, '');
            if (!noSpaceStr.includes("月度重点工作")) {
              // 非月度重点工作需要填写原因
              this.$set(row, 'pointsReasonError', '有加减分的请填写原因');
              return false;
            }
          }
        }

        return true;
      },

      /** 清除所有验证错误 */
      clearValidationErrors() {
        this.list.forEach(item => {
          this.$set(item, 'performanceError', '');
          this.$set(item, 'dePointsError', '');
          this.$set(item, 'pointsReasonError', '');
        });
      },

      /** 定位到字段并显示详细错误信息 */
      focusAndScrollToField(refName, rowNumber, fieldName) {
        console.log(`开始定位到字段: ${refName}, 行号: ${rowNumber}, 字段名: ${fieldName}`);

        this.$nextTick(() => {
          // 等待DOM更新后再执行
          setTimeout(() => {
            try {
              const field = this.$refs[refName];
              console.log('找到的字段元素:', field);

              let targetElement = null;
              let inputElement = null;

              if (field && field.length > 0) {
                // 数组形式的ref（v-for中的ref）
                inputElement = field[0];
                targetElement = inputElement.$el || inputElement;
              } else if (field) {
                // 单个ref
                inputElement = field;
                targetElement = inputElement.$el || inputElement;
              }

              if (targetElement && inputElement) {
                console.log('找到目标元素，开始定位');

                // 1. 先滚动到目标位置
                targetElement.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center',
                  inline: 'nearest'
                });

                // 2. 等待滚动完成后聚焦
                setTimeout(() => {
                  try {
                    inputElement.focus();
                    console.log('已聚焦到输入框');
                  } catch (focusError) {
                    console.warn('聚焦失败:', focusError);
                  }
                }, 500);

                // 3. 显示错误信息
                this.$message({
                  type: 'warning',
                  message: `第${rowNumber}行 ${fieldName} 未填写完整，请检查并填写`,
                  duration: 5000
                });

              } else {
                // 如果找不到具体字段，尝试定位到表格行
                console.warn(`找不到字段 ${refName}，尝试定位到表格行`);
                this.scrollToTableRow(rowNumber - 1);

                this.$message({
                  type: 'warning',
                  message: `第${rowNumber}行 ${fieldName} 未填写完整，请检查并填写`,
                  duration: 5000
                });
              }

            } catch (error) {
              console.error('定位字段时发生错误:', error);
              // 降级处理：至少滚动到表格区域
              this.scrollToTable();

              this.$message({
                type: 'warning',
                message: `第${rowNumber}行 ${fieldName} 未填写完整，请检查并填写`,
                duration: 5000
              });
            }
          }, 100); // 给一点时间让错误状态更新
        });
      },

      /** 滚动到指定表格行 */
      scrollToTableRow(rowIndex) {
        try {
          const tableRows = this.$el.querySelectorAll('.el-table__body tr');
          if (tableRows && tableRows[rowIndex]) {
            tableRows[rowIndex].scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            });
            console.log(`已滚动到第${rowIndex + 1}行`);
          } else {
            console.warn(`找不到第${rowIndex + 1}行，滚动到表格`);
            this.scrollToTable();
          }
        } catch (error) {
          console.error('滚动到表格行失败:', error);
          this.scrollToTable();
        }
      },

      /** 滚动到表格区域 */
      scrollToTable() {
        try {
          const table = this.$el.querySelector('.el-table');
          if (table) {
            table.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
            console.log('已滚动到表格区域');
          }
        } catch (error) {
          console.error('滚动到表格失败:', error);
        }
      }
    },
  };
</script>
<style>
  .table-striped{
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%;
    text-align: center;
    border: 1px #888;
    border-collapse: collapse;
  }
  .table-striped th{
    height: 32px;
    border: 1px solid #888;
    background-color: #dedede;
  }
  .table-striped td{
    min-height: 32px;
    border: 1px solid #888;
  }
  .table-input .el-textarea__inner{
    border: 0 !important;
    resize: none !important;
  }
  .table-input .el-input__inner{
    border: 0 !important;
  }

  /* 错误状态样式 */
  .table-input.is-error .el-input__inner,
  .table-input.is-error .el-textarea__inner {
    border: 1px solid #F56C6C !important;
    background-color: #fef0f0 !important;
  }

  /* 错误提示样式 */
  .el-form-item__error {
    color: #F56C6C;
    font-size: 12px;
    line-height: 1;
    padding-top: 4px;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1;
  }

  /* 表格单元格相对定位，用于错误提示定位 */
  .el-table .cell {
    position: relative;
  }
  
  .reason-text {
    width: 100%;
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-left: 3px solid #409EFF;
    border-radius: 4px;
  }

  .reason-label {
    font-weight: 500;
    color: #606266;
    margin-right: 8px;
  }

  .reason-content {
    color: #303133;
    line-height: 1.6;
  }
  /* .myUpload .el-upload--picture-card{
    display:none !important; 
  } */
  </style>
