{"remainingRequest": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPrice\\chartMethods.js", "dependencies": [{"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\src\\views\\purchaseDashboardPrice\\chartMethods.js", "mtime": 1756456493851}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\babel.config.js", "mtime": 1688548084091}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\java_workspace\\new_workspace\\xctg\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_default", "exports", "default", "methods", "getCokingCoalMaterialColorMap", "baseColors", "allMaterialTypes", "inventoryData", "cokingCoalInventoryData", "for<PERSON>ach", "item", "materialName", "class2Name", "includes", "push", "sort", "colorMap", "index", "length", "reinitChart", "chartId", "chartDom", "document", "getElementById", "console", "error", "concat", "chartInstances", "intervalId", "clearInterval", "existingInstance", "getInstanceByDom", "dispose", "log", "err", "innerHTML", "new<PERSON>hart", "init", "initMonthlyInventoryChart", "JSON", "stringify", "yearlyInventoryData", "myChart", "monthNames", "yearColorMap", "seriesData", "legendData", "yearData", "yearAmounts", "Array", "fill", "monthlyResultVoList", "monthData", "monthIndex", "parseFloat", "amount", "year", "color", "seriesItem", "name", "type", "data", "smooth", "symbol", "symbolSize", "lineStyle", "width", "itemStyle", "connectNulls", "option", "backgroundColor", "tooltip", "trigger", "axisPointer", "crossStyle", "formatter", "params", "tooltipText", "param", "value", "undefined", "marker", "seriesName", "toFixed", "legend", "textStyle", "top", "grid", "left", "right", "bottom", "containLabel", "xAxis", "axisLine", "axisLabel", "splitLine", "show", "yAxis", "series", "setOption", "initMaterialStatisticsChart", "materialStatisticsData", "statisticsData", "axisValueLabel", "_typeof2", "formattedDisplay", "map", "itemName", "interval", "rotate", "position", "min", "max", "inAmt", "itemIndex", "itemData", "borderRadius", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "borderWidth", "borderColor", "yAxisIndex", "arriveRate", "off", "on", "componentType", "seriesType", "currentUrl", "window", "location", "href", "baseUrl", "replace", "timeFlag", "getTimeFlagByDimensionType", "currentDimensionType", "itemTypeMap", "itemType", "selectedMaterialCategory", "itemId", "selectedMaterialItem", "url", "open", "bind", "getZr", "pointInPixel", "offsetX", "offsetY", "containPixel", "setCursorStyle", "initFactoryStockChart", "_this", "factoryStockData", "stockData", "selectedFactoryMaterialType", "filter", "class1", "materialTypeMap", "allDates", "_toConsumableArray2", "Set", "kgDate", "date<PERSON><PERSON><PERSON>", "groupedData", "materialType", "stockMoney", "colors", "colorIndex", "Object", "keys", "date", "axisValue", "s", "substring", "initRealTimeInventoryChart", "realTimeInventoryData", "stack", "centerInventoryAmount", "machineSideInventoryAmount", "totalInventoryAmount", "initCokingCoalInventoryChart", "initCokingCoalPieChart", "initCokingCoalLineChart", "_this2", "pieChartDom", "cokingCoal<PERSON><PERSON><PERSON>hart", "<PERSON><PERSON><PERSON>", "filteredData", "selectedCokingCoalType", "latestDate", "purchaseCokingDailyDetailList", "detail", "instockDate", "pieData", "totalInventory", "latestDetail", "find", "invQty", "percentage", "pieOption", "orient", "fontSize", "itemGap", "itemWidth", "itemHeight", "radius", "center", "avoidLabelOverlap", "label", "fontWeight", "_this3", "lineChartDom", "cokingCoalLineChart", "lineChart", "add", "sortedDates", "from", "formattedDates", "dateStr", "month", "day", "typeName", "lineData", "_item$purchaseCokingD", "d", "materialColor", "lineOption"], "sources": ["E:/java_workspace/new_workspace/xctg/ruoyi-ui/src/views/purchaseDashboardPrice/chartMethods.js"], "sourcesContent": ["import * as echarts from 'echarts'\r\n\r\nexport default {\r\n  methods: {\r\n    // 获取矿焦煤物料类型的颜色映射\r\n    getCokingCoalMaterialColorMap() {\r\n      // 使用月度库存金额图表的颜色方案\r\n      const baseColors = ['#0066ff', '#00ff00', '#ff0000', '#8b00ff', '#ffff00', '#ffffff']\r\n\r\n      // 基于所有原始数据为每个物料类型分配固定颜色，确保过滤时颜色保持一致\r\n      const allMaterialTypes = []\r\n      const inventoryData = this.cokingCoalInventoryData || []\r\n\r\n      // 收集所有物料类型\r\n      inventoryData.forEach(item => {\r\n        const materialName = item.class2Name || '未知物料'\r\n        if (!allMaterialTypes.includes(materialName)) {\r\n          allMaterialTypes.push(materialName)\r\n        }\r\n      })\r\n\r\n      // 按字母顺序排序，确保颜色分配的一致性\r\n      allMaterialTypes.sort()\r\n\r\n      // 为每个物料类型分配固定颜色\r\n      const colorMap = {}\r\n      allMaterialTypes.forEach((materialName, index) => {\r\n        colorMap[materialName] = baseColors[index % baseColors.length]\r\n      })\r\n\r\n      return colorMap\r\n    },\r\n\r\n    // 清理并重新初始化图表\r\n    reinitChart(chartId) {\r\n      const chartDom = document.getElementById(chartId)\r\n      if (!chartDom) {\r\n        console.error(`找不到图表DOM: ${chartId}`)\r\n        return null\r\n      }\r\n\r\n      if (this.chartInstances[chartId] && this.chartInstances[chartId].intervalId) {\r\n        clearInterval(this.chartInstances[chartId].intervalId)\r\n        this.chartInstances[chartId].intervalId = null\r\n      }\r\n\r\n      const existingInstance = echarts.getInstanceByDom(chartDom)\r\n\r\n      if (existingInstance) {\r\n        try {\r\n          echarts.dispose(existingInstance)\r\n          console.log(`ECharts instance successfully disposed for: ${chartId}`)\r\n        } catch (err) {\r\n          console.error(`Error disposing ECharts instance for ${chartId}:`, err)\r\n          chartDom.innerHTML = ''\r\n        }\r\n      }\r\n\r\n      chartDom.innerHTML = '<div class=\"chart-placeholder\">数据加载中...</div>'\r\n      this.chartInstances[chartId] = null\r\n\r\n      try {\r\n        const newChart = echarts.init(chartDom)\r\n        this.chartInstances[chartId] = newChart\r\n        console.log(`创建新图表: ${chartId}`)\r\n        return newChart\r\n      } catch (err) {\r\n        console.error(`创建图表失败: ${chartId}`, err)\r\n        chartDom.innerHTML = '<div class=\"chart-placeholder\">图表加载失败</div>'\r\n        return null\r\n      }\r\n    },\r\n\r\n    // 中心仓库月度库存金额曲线图\r\n    initMonthlyInventoryChart() {\r\n      console.log('initMonthlyInventoryChart started with data:', JSON.stringify(this.yearlyInventoryData))\r\n      const myChart = this.reinitChart('monthlyInventoryChart')\r\n      if (!myChart) return\r\n\r\n      const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']\r\n\r\n      // 年份对应颜色映射\r\n      const yearColorMap = {\r\n        '2020': '#0066ff',  // 蓝色\r\n        '2021': '#00ff00',  // 绿色\r\n        '2022': '#ff0000',  // 红色\r\n        '2023': '#ffff00',  // 黄色\r\n        '2024': '#8b00ff',  // 紫色\r\n        '2025': '#ffffff'   // 白色\r\n      }\r\n\r\n      // 构建每年的数据系列\r\n      const seriesData = []\r\n      const legendData = []\r\n\r\n      this.yearlyInventoryData.forEach((yearData, index) => {\r\n        const yearAmounts = new Array(12).fill(null)\r\n\r\n        // 填充每个月的数据\r\n        yearData.monthlyResultVoList.forEach(monthData => {\r\n          const monthIndex = monthData.monthIndex - 1 // 转换为0-11的索引\r\n          if (monthIndex >= 0 && monthIndex < 12) {\r\n            yearAmounts[monthIndex] = parseFloat(monthData.amount) || 0\r\n          }\r\n        })\r\n\r\n        // 根据年份获取对应颜色，如果没有预定义颜色，使用默认颜色\r\n        const year = yearData.year\r\n        const color = yearColorMap[year] || '#83bff6'\r\n\r\n        const seriesItem = {\r\n          name: `${yearData.year}年`,\r\n          type: 'line',\r\n          data: yearAmounts,\r\n          smooth: true,\r\n          symbol: 'circle',\r\n          symbolSize: 6,\r\n          lineStyle: {\r\n            width: 3,\r\n            color: color\r\n          },\r\n          itemStyle: {\r\n            color: color\r\n          },\r\n          connectNulls: false\r\n        }\r\n\r\n        seriesData.push(seriesItem)\r\n        legendData.push(`${yearData.year}年`)\r\n      })\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              if (param.value !== null && param.value !== undefined) {\r\n                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 万元<br/>`\r\n              }\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '8%',\r\n          bottom: '10%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: monthNames,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee'\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '库存金额 (万元)',\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: '{value}'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: seriesData\r\n      }\r\n\r\n      myChart.setOption(option, true)\r\n    },\r\n\r\n    // 物料入库统计图表\r\n    initMaterialStatisticsChart() {\r\n      console.log('initMaterialStatisticsChart started with data:', JSON.stringify(this.materialStatisticsData))\r\n      const myChart = this.reinitChart('materialStatisticsChart')\r\n      if (!myChart) return\r\n\r\n      const statisticsData = this.materialStatisticsData || []\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].axisValueLabel + '<br/>'\r\n            params.forEach(param => {\r\n              let value = typeof param.value === 'object' ? param.value.value : param.value\r\n              let formattedDisplay = ''\r\n              if (param.seriesName === '入库金额') {\r\n                formattedDisplay = value + ' 万元'\r\n              } else if (param.seriesName === '到货完成度%') {\r\n                formattedDisplay = (typeof value === 'number' ? value.toFixed(2) : value) + '%'\r\n              } else {\r\n                formattedDisplay = value\r\n              }\r\n              tooltipText += `${param.marker}${param.seriesName}: ${formattedDisplay}<br/>`\r\n            })\r\n            tooltipText += '<span style=\"color: #00d4ff;\">点击查看详情</span>'\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['入库金额', '到货完成度%'],\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '8%',\r\n          bottom: '10%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: statisticsData.map(item => item.itemName || '未知物料'),\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 0,\r\n            rotate: 30\r\n          }\r\n        },\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            name: '入库金额 (万元)',\r\n            position: 'left',\r\n            axisLabel: {\r\n              color: '#eee'\r\n            },\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#eee'\r\n              }\r\n            },\r\n            splitLine: {\r\n              lineStyle: {\r\n                color: 'rgba(255,255,255,0.1)'\r\n              }\r\n            }\r\n          },\r\n          {\r\n            type: 'value',\r\n            name: '到货完成度%',\r\n            position: 'right',\r\n            min: 0,\r\n            max: 100,\r\n            interval: 20,\r\n            axisLabel: {\r\n              formatter: '{value}%',\r\n              color: '#eee'\r\n            },\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: '#eee'\r\n              }\r\n            },\r\n            splitLine: {\r\n              show: false\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: '入库金额',\r\n            type: 'bar',\r\n            data: statisticsData.map((item, index) => ({\r\n              value: parseFloat(item.inAmt) || 0,\r\n              itemIndex: index,\r\n              itemData: item\r\n            })),\r\n            itemStyle: {\r\n              color: '#83bff6',\r\n              borderRadius: [4, 4, 0, 0]\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                color: '#83bff6',\r\n                borderRadius: [4, 4, 0, 0],\r\n                shadowBlur: 10,\r\n                shadowColor: 'rgba(255, 255, 255, 0.5)',\r\n                borderWidth: 2,\r\n                borderColor: '#fff'\r\n              }\r\n            }\r\n          },\r\n          {\r\n            name: '到货完成度%',\r\n            type: 'bar',\r\n            yAxisIndex: 1,\r\n            data: statisticsData.map((item, index) => ({\r\n              value: parseFloat(item.arriveRate) || 0,\r\n              itemIndex: index,\r\n              itemData: item\r\n            })),\r\n            itemStyle: {\r\n              color: '#ea7ccc',\r\n              borderRadius: [4, 4, 0, 0]\r\n            },\r\n            emphasis: {\r\n              itemStyle: {\r\n                color: '#ea7ccc',\r\n                borderRadius: [4, 4, 0, 0],\r\n                shadowBlur: 10,\r\n                shadowColor: 'rgba(255, 255, 255, 0.5)',\r\n                borderWidth: 2,\r\n                borderColor: '#fff'\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      }\r\n\r\n      // 添加点击事件监听器\r\n      myChart.off('click') // 先移除之前的点击事件\r\n      myChart.on('click', function(params) {\r\n        if (params.componentType === 'series' && params.seriesType === 'bar') {\r\n          // 获取当前页面URL并替换路径\r\n          const currentUrl = window.location.href\r\n          const baseUrl = currentUrl.replace('purchaseDashboard', 'purchaseMaterial')\r\n\r\n          // 获取参数\r\n          const timeFlag = this.getTimeFlagByDimensionType(this.currentDimensionType)\r\n          // 将数字转换为对应的分类字符串\r\n          const itemTypeMap = {\r\n            '1': 'CLASS1',\r\n            '2': 'CLASS2',\r\n            '3': 'CLASS3',\r\n            '4': 'LEAF'\r\n          }\r\n          const itemType = itemTypeMap[this.selectedMaterialCategory] || 'CLASS1'\r\n          const itemId = this.selectedMaterialItem || ''\r\n\r\n          // 构建跳转URL\r\n          const url = `${baseUrl}?timeFlag=${timeFlag}&itemType=${itemType}&itemId=${itemId}`\r\n\r\n          console.log('跳转到采购物料页面:', url)\r\n          console.log('跳转参数:', { timeFlag, itemType, itemId })\r\n\r\n          // 在新窗口中打开页面\r\n          window.open(url, '_blank')\r\n        }\r\n      }.bind(this))\r\n\r\n      // 设置鼠标样式，表示可点击\r\n      myChart.getZr().on('mousemove', function(params) {\r\n        const pointInPixel = [params.offsetX, params.offsetY]\r\n        if (myChart.containPixel('grid', pointInPixel)) {\r\n          myChart.getZr().setCursorStyle('pointer')\r\n        } else {\r\n          myChart.getZr().setCursorStyle('default')\r\n        }\r\n      })\r\n\r\n      myChart.setOption(option, true)\r\n    },\r\n\r\n    // 机旁库当前库存曲线图\r\n    initFactoryStockChart() {\r\n      console.log('initFactoryStockChart started with data:', JSON.stringify(this.factoryStockData))\r\n      const myChart = this.reinitChart('factoryStockChart')\r\n      if (!myChart) return\r\n\r\n      let stockData = this.factoryStockData || []\r\n\r\n      // 根据选中的物料类型筛选数据\r\n      if (this.selectedFactoryMaterialType && this.selectedFactoryMaterialType !== '') {\r\n        stockData = stockData.filter(item => item.class1 === this.selectedFactoryMaterialType)\r\n      }\r\n\r\n      // 物料类型映射\r\n      const materialTypeMap = {\r\n        'A': '通用备件',\r\n        'B': '专用备件',\r\n        'C': '材料类',\r\n        'D': '原材料',\r\n        'E': '辅耐材',\r\n        'G': '办公'\r\n      }\r\n\r\n      // 获取所有日期并排序\r\n      const allDates = [...new Set(stockData.map(item => item.kgDate))].sort()\r\n\r\n      // 自适应日期范围 - 如果数据不足31天，使用实际数据的日期范围\r\n      const dateRange = allDates.length > 0 ? allDates : []\r\n\r\n      // 按class1分组数据\r\n      const groupedData = {}\r\n      stockData.forEach(item => {\r\n        const materialType = item.class1\r\n        if (!groupedData[materialType]) {\r\n          groupedData[materialType] = {}\r\n        }\r\n        groupedData[materialType][item.kgDate] = parseFloat(item.stockMoney) || 0\r\n      })\r\n\r\n      // 生成系列数据\r\n      const series = []\r\n      const colors = ['#83bff6', '#ea7ccc', '#fac858', '#ee6666', '#73c0de', '#3ba272']\r\n      let colorIndex = 0\r\n\r\n      Object.keys(groupedData).forEach(materialType => {\r\n        const materialName = materialTypeMap[materialType] || materialType\r\n        const data = dateRange.map(date => {\r\n          return groupedData[materialType][date] || 0\r\n        })\r\n\r\n        series.push({\r\n          name: materialName,\r\n          type: 'line',\r\n          smooth: true,\r\n          data: data,\r\n          lineStyle: {\r\n            width: 2,\r\n            color: colors[colorIndex % colors.length]\r\n          },\r\n          itemStyle: {\r\n            color: colors[colorIndex % colors.length]\r\n          },\r\n          symbol: 'circle',\r\n          symbolSize: 4\r\n        })\r\n        colorIndex++\r\n      })\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].axisValue + '<br/>'\r\n            params.forEach(param => {\r\n              if (param.value !== null && param.value !== undefined) {\r\n                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 万元<br/>`\r\n              }\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          data: series.map(s => s.name),\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '8%',\r\n          bottom: '10%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: dateRange.map(date => {\r\n            // 格式化日期显示\r\n            if (date && date.length === 8) {\r\n              return date.substring(4, 6) + '/' + date.substring(6, 8)\r\n            }\r\n            return date\r\n          }),\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 'auto',\r\n            rotate: 30\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '库存金额 (万元)',\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: '{value}'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: series\r\n      }\r\n\r\n      myChart.setOption(option, true)\r\n    },\r\n\r\n    // 实时库存图表\r\n    initRealTimeInventoryChart() {\r\n      console.log('initRealTimeInventoryChart started with data:', JSON.stringify(this.realTimeInventoryData))\r\n      const myChart = this.reinitChart('realTimeInventoryChart')\r\n      if (!myChart) return\r\n\r\n      const inventoryData = this.realTimeInventoryData || []\r\n\r\n      const option = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              if (param.value !== null && param.value !== undefined) {\r\n                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 万元<br/>`\r\n              }\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['中心仓库库存', '机旁库库存', '总库存'],\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '3%',\r\n          right: '8%',\r\n          bottom: '10%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: inventoryData.map(item => item.materialName || '未知物料'),\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee',\r\n            interval: 0,\r\n            rotate: 30\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '库存金额 (万元)',\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: '{value}'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '中心仓库库存',\r\n            type: 'bar',\r\n            stack: '库存',\r\n            data: inventoryData.map(item => parseFloat(item.centerInventoryAmount) || 0),\r\n            itemStyle: {\r\n              color: '#83bff6',\r\n              borderRadius: [4, 4, 0, 0]\r\n            }\r\n          },\r\n          {\r\n            name: '机旁库库存',\r\n            type: 'bar',\r\n            stack: '库存',\r\n            data: inventoryData.map(item => parseFloat(item.machineSideInventoryAmount) || 0),\r\n            itemStyle: {\r\n              color: '#ea7ccc',\r\n              borderRadius: [0, 0, 0, 0]\r\n            }\r\n          },\r\n          {\r\n            name: '总库存',\r\n            type: 'line',\r\n            data: inventoryData.map(item => parseFloat(item.totalInventoryAmount) || 0),\r\n            smooth: true,\r\n            symbol: 'circle',\r\n            symbolSize: 6,\r\n            lineStyle: {\r\n              width: 3,\r\n              color: '#5fd8b6'\r\n            },\r\n            itemStyle: {\r\n              color: '#5fd8b6'\r\n            }\r\n          }\r\n        ]\r\n      }\r\n\r\n      myChart.setOption(option, true)\r\n    },\r\n\r\n    // 矿焦煤库存图表（饼图+曲线图组合）\r\n    initCokingCoalInventoryChart() {\r\n      console.log('initCokingCoalInventoryChart started with data:', JSON.stringify(this.cokingCoalInventoryData))\r\n\r\n      // 初始化饼图和曲线图\r\n      this.initCokingCoalPieChart()\r\n      this.initCokingCoalLineChart()\r\n    },\r\n\r\n    // 矿焦煤库存饼图（最新一天数据）\r\n    initCokingCoalPieChart() {\r\n      const pieChartDom = document.getElementById('cokingCoalPieChart')\r\n      if (!pieChartDom) {\r\n        console.error('找不到饼图DOM: cokingCoalPieChart')\r\n        return null\r\n      }\r\n\r\n      // 清理现有实例\r\n      if (this.chartInstances.cokingCoalPieChart) {\r\n        this.chartInstances.cokingCoalPieChart.dispose()\r\n      }\r\n\r\n      const pieChart = echarts.init(pieChartDom)\r\n      this.chartInstances.cokingCoalPieChart = pieChart\r\n\r\n      const inventoryData = this.cokingCoalInventoryData || []\r\n\r\n      // 根据选中的类型过滤数据\r\n      let filteredData = inventoryData\r\n      if (this.selectedCokingCoalType && this.selectedCokingCoalType !== '') {\r\n        filteredData = inventoryData.filter(item => {\r\n          return item.class2Name === this.selectedCokingCoalType ||\r\n                 item.class2Name.includes(this.selectedCokingCoalType) ||\r\n                 this.selectedCokingCoalType.includes(item.class2Name)\r\n        })\r\n      }\r\n\r\n      // 找到最新日期\r\n      let latestDate = ''\r\n      filteredData.forEach(item => {\r\n        if (item.purchaseCokingDailyDetailList) {\r\n          item.purchaseCokingDailyDetailList.forEach(detail => {\r\n            if (detail.instockDate > latestDate) {\r\n              latestDate = detail.instockDate\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      // 构建最新一天的饼图数据\r\n      const pieData = []\r\n      let totalInventory = 0\r\n\r\n      filteredData.forEach(item => {\r\n        if (item.purchaseCokingDailyDetailList) {\r\n          const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)\r\n          if (latestDetail) {\r\n            const value = parseFloat(latestDetail.invQty) || 0\r\n            totalInventory += value\r\n            pieData.push({\r\n              name: item.class2Name || '未知物料',\r\n              value: value\r\n            })\r\n          }\r\n        }\r\n      })\r\n\r\n      // 计算百分比\r\n      pieData.forEach(item => {\r\n        item.percentage = totalInventory > 0 ? ((item.value / totalInventory) * 100).toFixed(1) : 0\r\n      })\r\n\r\n      // 获取统一的颜色映射\r\n      const colorMap = this.getCokingCoalMaterialColorMap()\r\n\r\n      // 为饼图数据分配颜色\r\n      pieData.forEach(item => {\r\n        item.itemStyle = {\r\n          color: colorMap[item.name] || '#83bff6',\r\n          borderRadius: 8,\r\n          borderColor: '#fff',\r\n          borderWidth: 1\r\n        }\r\n      })\r\n\r\n      const pieOption = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: function(params) {\r\n            return `${params.name}<br/>库存量: ${params.value.toFixed(2)} 吨<br/>占比: ${params.data.percentage}%`\r\n          }\r\n        },\r\n        legend: {\r\n          orient: 'vertical',\r\n          left: '5%',\r\n          top: 'center',\r\n          textStyle: {\r\n            color: '#fff',\r\n            fontSize: 10\r\n          },\r\n          itemGap: 6,\r\n          itemWidth: 10,\r\n          itemHeight: 10\r\n        },\r\n        series: [\r\n          {\r\n            name: '矿焦煤库存',\r\n            type: 'pie',\r\n            radius: ['30%', '60%'],\r\n            center: ['65%', '50%'],\r\n            avoidLabelOverlap: false,\r\n            label: {\r\n              show: false\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: '12',\r\n                fontWeight: 'bold',\r\n                color: '#fff',\r\n                formatter: function(params) {\r\n                  return `${params.name}\\n${params.data.percentage}%`\r\n                }\r\n              }\r\n            },\r\n            data: pieData\r\n          }\r\n        ]\r\n      }\r\n\r\n      pieChart.setOption(pieOption, true)\r\n    },\r\n\r\n    // 矿焦煤库存曲线图（近七天趋势）\r\n    initCokingCoalLineChart() {\r\n      const lineChartDom = document.getElementById('cokingCoalLineChart')\r\n      if (!lineChartDom) {\r\n        console.error('找不到曲线图DOM: cokingCoalLineChart')\r\n        return null\r\n      }\r\n\r\n      // 清理现有实例\r\n      if (this.chartInstances.cokingCoalLineChart) {\r\n        this.chartInstances.cokingCoalLineChart.dispose()\r\n      }\r\n\r\n      const lineChart = echarts.init(lineChartDom)\r\n      this.chartInstances.cokingCoalLineChart = lineChart\r\n\r\n      const inventoryData = this.cokingCoalInventoryData || []\r\n\r\n      // 根据选中的类型过滤数据\r\n      let filteredData = inventoryData\r\n      if (this.selectedCokingCoalType && this.selectedCokingCoalType !== '') {\r\n        filteredData = inventoryData.filter(item => {\r\n          return item.class2Name === this.selectedCokingCoalType ||\r\n                 item.class2Name.includes(this.selectedCokingCoalType) ||\r\n                 this.selectedCokingCoalType.includes(item.class2Name)\r\n        })\r\n      }\r\n\r\n      // 提取所有日期并排序\r\n      const allDates = new Set()\r\n      filteredData.forEach(item => {\r\n        if (item.purchaseCokingDailyDetailList) {\r\n          item.purchaseCokingDailyDetailList.forEach(detail => {\r\n            allDates.add(detail.instockDate)\r\n          })\r\n        }\r\n      })\r\n\r\n      const sortedDates = Array.from(allDates).sort()\r\n\r\n      // 格式化日期显示（从yyyyMMdd转换为MM-dd）\r\n      const formattedDates = sortedDates.map(dateStr => {\r\n        if (dateStr && dateStr.length === 8) {\r\n          const month = dateStr.substring(4, 6)\r\n          const day = dateStr.substring(6, 8)\r\n          return `${month}-${day}`\r\n        }\r\n        return dateStr\r\n      })\r\n\r\n      // 构建每个类型的曲线数据\r\n      const seriesData = []\r\n      const legendData = []\r\n\r\n      // 获取统一的颜色映射\r\n      const colorMap = this.getCokingCoalMaterialColorMap()\r\n\r\n      filteredData.forEach((item, index) => {\r\n        const typeName = item.class2Name || '未知物料'\r\n\r\n        // 为每个日期构建数据点\r\n        const lineData = sortedDates.map(date => {\r\n          const detail = item.purchaseCokingDailyDetailList?.find(d => d.instockDate === date)\r\n          return detail ? parseFloat(detail.invQty) || 0 : null\r\n        })\r\n\r\n        // 使用统一的颜色映射\r\n        const materialColor = colorMap[typeName] || '#83bff6'\r\n\r\n        const seriesItem = {\r\n          name: typeName,\r\n          type: 'line',\r\n          data: lineData,\r\n          smooth: true,\r\n          symbol: 'circle',\r\n          symbolSize: 6,\r\n          lineStyle: {\r\n            width: 3,\r\n            color: materialColor\r\n          },\r\n          itemStyle: {\r\n            color: materialColor\r\n          },\r\n          connectNulls: false\r\n        }\r\n\r\n        seriesData.push(seriesItem)\r\n        legendData.push(typeName)\r\n      })\r\n\r\n      const lineOption = {\r\n        backgroundColor: 'transparent',\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross',\r\n            crossStyle: {\r\n              color: '#999'\r\n            }\r\n          },\r\n          formatter: function(params) {\r\n            let tooltipText = params[0].name + '<br/>'\r\n            params.forEach(param => {\r\n              if (param.value !== null && param.value !== undefined) {\r\n                tooltipText += `${param.marker}${param.seriesName}: ${param.value.toFixed(2)} 吨<br/>`\r\n              }\r\n            })\r\n            return tooltipText\r\n          }\r\n        },\r\n        legend: {\r\n          data: legendData,\r\n          textStyle: {\r\n            color: '#fff'\r\n          },\r\n          top: '5%'\r\n        },\r\n        grid: {\r\n          left: '8%',\r\n          right: '5%',\r\n          bottom: '10%',\r\n          top: '20%',\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'category',\r\n          data: formattedDates,\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: '#eee'\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          }\r\n        },\r\n        yAxis: {\r\n          type: 'value',\r\n          name: '库存量 (吨)',\r\n          axisLabel: {\r\n            color: '#eee',\r\n            formatter: '{value}'\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              color: '#eee'\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: 'rgba(255,255,255,0.1)'\r\n            }\r\n          }\r\n        },\r\n        series: seriesData\r\n      }\r\n\r\n      lineChart.setOption(lineOption, true)\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAkC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEnB;EACbC,OAAO,EAAE;IACP;IACAC,6BAA6B,WAA7BA,6BAA6BA,CAAA,EAAG;MAC9B;MACA,IAAMC,UAAU,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;;MAErF;MACA,IAAMC,gBAAgB,GAAG,EAAE;MAC3B,IAAMC,aAAa,GAAG,IAAI,CAACC,uBAAuB,IAAI,EAAE;;MAExD;MACAD,aAAa,CAACE,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC5B,IAAMC,YAAY,GAAGD,IAAI,CAACE,UAAU,IAAI,MAAM;QAC9C,IAAI,CAACN,gBAAgB,CAACO,QAAQ,CAACF,YAAY,CAAC,EAAE;UAC5CL,gBAAgB,CAACQ,IAAI,CAACH,YAAY,CAAC;QACrC;MACF,CAAC,CAAC;;MAEF;MACAL,gBAAgB,CAACS,IAAI,CAAC,CAAC;;MAEvB;MACA,IAAMC,QAAQ,GAAG,CAAC,CAAC;MACnBV,gBAAgB,CAACG,OAAO,CAAC,UAACE,YAAY,EAAEM,KAAK,EAAK;QAChDD,QAAQ,CAACL,YAAY,CAAC,GAAGN,UAAU,CAACY,KAAK,GAAGZ,UAAU,CAACa,MAAM,CAAC;MAChE,CAAC,CAAC;MAEF,OAAOF,QAAQ;IACjB,CAAC;IAED;IACAG,WAAW,WAAXA,WAAWA,CAACC,OAAO,EAAE;MACnB,IAAMC,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAACH,OAAO,CAAC;MACjD,IAAI,CAACC,QAAQ,EAAE;QACbG,OAAO,CAACC,KAAK,uCAAAC,MAAA,CAAcN,OAAO,CAAE,CAAC;QACrC,OAAO,IAAI;MACb;MAEA,IAAI,IAAI,CAACO,cAAc,CAACP,OAAO,CAAC,IAAI,IAAI,CAACO,cAAc,CAACP,OAAO,CAAC,CAACQ,UAAU,EAAE;QAC3EC,aAAa,CAAC,IAAI,CAACF,cAAc,CAACP,OAAO,CAAC,CAACQ,UAAU,CAAC;QACtD,IAAI,CAACD,cAAc,CAACP,OAAO,CAAC,CAACQ,UAAU,GAAG,IAAI;MAChD;MAEA,IAAME,gBAAgB,GAAGjC,OAAO,CAACkC,gBAAgB,CAACV,QAAQ,CAAC;MAE3D,IAAIS,gBAAgB,EAAE;QACpB,IAAI;UACFjC,OAAO,CAACmC,OAAO,CAACF,gBAAgB,CAAC;UACjCN,OAAO,CAACS,GAAG,gDAAAP,MAAA,CAAgDN,OAAO,CAAE,CAAC;QACvE,CAAC,CAAC,OAAOc,GAAG,EAAE;UACZV,OAAO,CAACC,KAAK,yCAAAC,MAAA,CAAyCN,OAAO,QAAKc,GAAG,CAAC;UACtEb,QAAQ,CAACc,SAAS,GAAG,EAAE;QACzB;MACF;MAEAd,QAAQ,CAACc,SAAS,GAAG,+CAA+C;MACpE,IAAI,CAACR,cAAc,CAACP,OAAO,CAAC,GAAG,IAAI;MAEnC,IAAI;QACF,IAAMgB,QAAQ,GAAGvC,OAAO,CAACwC,IAAI,CAAChB,QAAQ,CAAC;QACvC,IAAI,CAACM,cAAc,CAACP,OAAO,CAAC,GAAGgB,QAAQ;QACvCZ,OAAO,CAACS,GAAG,oCAAAP,MAAA,CAAWN,OAAO,CAAE,CAAC;QAChC,OAAOgB,QAAQ;MACjB,CAAC,CAAC,OAAOF,GAAG,EAAE;QACZV,OAAO,CAACC,KAAK,0CAAAC,MAAA,CAAYN,OAAO,GAAIc,GAAG,CAAC;QACxCb,QAAQ,CAACc,SAAS,GAAG,6CAA6C;QAClE,OAAO,IAAI;MACb;IACF,CAAC;IAED;IACAG,yBAAyB,WAAzBA,yBAAyBA,CAAA,EAAG;MAC1Bd,OAAO,CAACS,GAAG,CAAC,8CAA8C,EAAEM,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC;MACrG,IAAMC,OAAO,GAAG,IAAI,CAACvB,WAAW,CAAC,uBAAuB,CAAC;MACzD,IAAI,CAACuB,OAAO,EAAE;MAEd,IAAMC,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;;MAE9F;MACA,IAAMC,YAAY,GAAG;QACnB,MAAM,EAAE,SAAS;QAAG;QACpB,MAAM,EAAE,SAAS;QAAG;QACpB,MAAM,EAAE,SAAS;QAAG;QACpB,MAAM,EAAE,SAAS;QAAG;QACpB,MAAM,EAAE,SAAS;QAAG;QACpB,MAAM,EAAE,SAAS,CAAG;MACtB,CAAC;;MAED;MACA,IAAMC,UAAU,GAAG,EAAE;MACrB,IAAMC,UAAU,GAAG,EAAE;MAErB,IAAI,CAACL,mBAAmB,CAAChC,OAAO,CAAC,UAACsC,QAAQ,EAAE9B,KAAK,EAAK;QACpD,IAAM+B,WAAW,GAAG,IAAIC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;;QAE5C;QACAH,QAAQ,CAACI,mBAAmB,CAAC1C,OAAO,CAAC,UAAA2C,SAAS,EAAI;UAChD,IAAMC,UAAU,GAAGD,SAAS,CAACC,UAAU,GAAG,CAAC,EAAC;UAC5C,IAAIA,UAAU,IAAI,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;YACtCL,WAAW,CAACK,UAAU,CAAC,GAAGC,UAAU,CAACF,SAAS,CAACG,MAAM,CAAC,IAAI,CAAC;UAC7D;QACF,CAAC,CAAC;;QAEF;QACA,IAAMC,IAAI,GAAGT,QAAQ,CAACS,IAAI;QAC1B,IAAMC,KAAK,GAAGb,YAAY,CAACY,IAAI,CAAC,IAAI,SAAS;QAE7C,IAAME,UAAU,GAAG;UACjBC,IAAI,KAAAjC,MAAA,CAAKqB,QAAQ,CAACS,IAAI,WAAG;UACzBI,IAAI,EAAE,MAAM;UACZC,IAAI,EAAEb,WAAW;UACjBc,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,QAAQ;UAChBC,UAAU,EAAE,CAAC;UACbC,SAAS,EAAE;YACTC,KAAK,EAAE,CAAC;YACRT,KAAK,EAAEA;UACT,CAAC;UACDU,SAAS,EAAE;YACTV,KAAK,EAAEA;UACT,CAAC;UACDW,YAAY,EAAE;QAChB,CAAC;QAEDvB,UAAU,CAAC/B,IAAI,CAAC4C,UAAU,CAAC;QAC3BZ,UAAU,CAAChC,IAAI,IAAAY,MAAA,CAAIqB,QAAQ,CAACS,IAAI,WAAG,CAAC;MACtC,CAAC,CAAC;MAEF,IAAMa,MAAM,GAAG;QACbC,eAAe,EAAE,aAAa;QAC9BC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXb,IAAI,EAAE,OAAO;YACbc,UAAU,EAAE;cACVjB,KAAK,EAAE;YACT;UACF,CAAC;UACDkB,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIC,WAAW,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACjB,IAAI,GAAG,OAAO;YAC1CiB,MAAM,CAACnE,OAAO,CAAC,UAAAqE,KAAK,EAAI;cACtB,IAAIA,KAAK,CAACC,KAAK,KAAK,IAAI,IAAID,KAAK,CAACC,KAAK,KAAKC,SAAS,EAAE;gBACrDH,WAAW,OAAAnD,MAAA,CAAOoD,KAAK,CAACG,MAAM,EAAAvD,MAAA,CAAGoD,KAAK,CAACI,UAAU,QAAAxD,MAAA,CAAKoD,KAAK,CAACC,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,uBAAU;cACxF;YACF,CAAC,CAAC;YACF,OAAON,WAAW;UACpB;QACF,CAAC;QACDO,MAAM,EAAE;UACNvB,IAAI,EAAEf,UAAU;UAChBuC,SAAS,EAAE;YACT5B,KAAK,EAAE;UACT,CAAC;UACD6B,GAAG,EAAE;QACP,CAAC;QACDC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbJ,GAAG,EAAE,KAAK;UACVK,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLhC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAElB,UAAU;UAChBkD,QAAQ,EAAE;YACR5B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF,CAAC;UACDqC,SAAS,EAAE;YACTrC,KAAK,EAAE;UACT,CAAC;UACDsC,SAAS,EAAE;YACTC,IAAI,EAAE;UACR;QACF,CAAC;QACDC,KAAK,EAAE;UACLrC,IAAI,EAAE,OAAO;UACbD,IAAI,EAAE,WAAW;UACjBmC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbkB,SAAS,EAAE;UACb,CAAC;UACDkB,QAAQ,EAAE;YACR5B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF,CAAC;UACDsC,SAAS,EAAE;YACT9B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACDyC,MAAM,EAAErD;MACV,CAAC;MAEDH,OAAO,CAACyD,SAAS,CAAC9B,MAAM,EAAE,IAAI,CAAC;IACjC,CAAC;IAED;IACA+B,2BAA2B,WAA3BA,2BAA2BA,CAAA,EAAG;MAC5B5E,OAAO,CAACS,GAAG,CAAC,gDAAgD,EAAEM,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC6D,sBAAsB,CAAC,CAAC;MAC1G,IAAM3D,OAAO,GAAG,IAAI,CAACvB,WAAW,CAAC,yBAAyB,CAAC;MAC3D,IAAI,CAACuB,OAAO,EAAE;MAEd,IAAM4D,cAAc,GAAG,IAAI,CAACD,sBAAsB,IAAI,EAAE;MAExD,IAAMhC,MAAM,GAAG;QACbC,eAAe,EAAE,aAAa;QAC9BC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXb,IAAI,EAAE;UACR,CAAC;UACDe,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIC,WAAW,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC2B,cAAc,GAAG,OAAO;YACpD3B,MAAM,CAACnE,OAAO,CAAC,UAAAqE,KAAK,EAAI;cACtB,IAAIC,KAAK,GAAG,IAAAyB,QAAA,CAAAtG,OAAA,EAAO4E,KAAK,CAACC,KAAK,MAAK,QAAQ,GAAGD,KAAK,CAACC,KAAK,CAACA,KAAK,GAAGD,KAAK,CAACC,KAAK;cAC7E,IAAI0B,gBAAgB,GAAG,EAAE;cACzB,IAAI3B,KAAK,CAACI,UAAU,KAAK,MAAM,EAAE;gBAC/BuB,gBAAgB,GAAG1B,KAAK,GAAG,KAAK;cAClC,CAAC,MAAM,IAAID,KAAK,CAACI,UAAU,KAAK,QAAQ,EAAE;gBACxCuB,gBAAgB,GAAG,CAAC,OAAO1B,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,GAAGJ,KAAK,IAAI,GAAG;cACjF,CAAC,MAAM;gBACL0B,gBAAgB,GAAG1B,KAAK;cAC1B;cACAF,WAAW,OAAAnD,MAAA,CAAOoD,KAAK,CAACG,MAAM,EAAAvD,MAAA,CAAGoD,KAAK,CAACI,UAAU,QAAAxD,MAAA,CAAK+E,gBAAgB,UAAO;YAC/E,CAAC,CAAC;YACF5B,WAAW,IAAI,6CAA6C;YAC5D,OAAOA,WAAW;UACpB;QACF,CAAC;QACDO,MAAM,EAAE;UACNvB,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;UACxBwB,SAAS,EAAE;YACT5B,KAAK,EAAE;UACT,CAAC;UACD6B,GAAG,EAAE;QACP,CAAC;QACDC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbJ,GAAG,EAAE,KAAK;UACVK,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLhC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAEyC,cAAc,CAACI,GAAG,CAAC,UAAAhG,IAAI;YAAA,OAAIA,IAAI,CAACiG,QAAQ,IAAI,MAAM;UAAA,EAAC;UACzDd,QAAQ,EAAE;YACR5B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF,CAAC;UACDqC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbmD,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE;UACV;QACF,CAAC;QACDZ,KAAK,EAAE,CACL;UACErC,IAAI,EAAE,OAAO;UACbD,IAAI,EAAE,WAAW;UACjBmD,QAAQ,EAAE,MAAM;UAChBhB,SAAS,EAAE;YACTrC,KAAK,EAAE;UACT,CAAC;UACDoC,QAAQ,EAAE;YACR5B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF,CAAC;UACDsC,SAAS,EAAE;YACT9B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF;QACF,CAAC,EACD;UACEG,IAAI,EAAE,OAAO;UACbD,IAAI,EAAE,QAAQ;UACdmD,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE,GAAG;UACRJ,QAAQ,EAAE,EAAE;UACZd,SAAS,EAAE;YACTnB,SAAS,EAAE,UAAU;YACrBlB,KAAK,EAAE;UACT,CAAC;UACDoC,QAAQ,EAAE;YACR5B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF,CAAC;UACDsC,SAAS,EAAE;YACTC,IAAI,EAAE;UACR;QACF,CAAC,CACF;QACDE,MAAM,EAAE,CACN;UACEvC,IAAI,EAAE,MAAM;UACZC,IAAI,EAAE,KAAK;UACXC,IAAI,EAAEyC,cAAc,CAACI,GAAG,CAAC,UAAChG,IAAI,EAAEO,KAAK;YAAA,OAAM;cACzC8D,KAAK,EAAEzB,UAAU,CAAC5C,IAAI,CAACuG,KAAK,CAAC,IAAI,CAAC;cAClCC,SAAS,EAAEjG,KAAK;cAChBkG,QAAQ,EAAEzG;YACZ,CAAC;UAAA,CAAC,CAAC;UACHyD,SAAS,EAAE;YACTV,KAAK,EAAE,SAAS;YAChB2D,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UAC3B,CAAC;UACDC,QAAQ,EAAE;YACRlD,SAAS,EAAE;cACTV,KAAK,EAAE,SAAS;cAChB2D,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;cAC1BE,UAAU,EAAE,EAAE;cACdC,WAAW,EAAE,0BAA0B;cACvCC,WAAW,EAAE,CAAC;cACdC,WAAW,EAAE;YACf;UACF;QACF,CAAC,EACD;UACE9D,IAAI,EAAE,QAAQ;UACdC,IAAI,EAAE,KAAK;UACX8D,UAAU,EAAE,CAAC;UACb7D,IAAI,EAAEyC,cAAc,CAACI,GAAG,CAAC,UAAChG,IAAI,EAAEO,KAAK;YAAA,OAAM;cACzC8D,KAAK,EAAEzB,UAAU,CAAC5C,IAAI,CAACiH,UAAU,CAAC,IAAI,CAAC;cACvCT,SAAS,EAAEjG,KAAK;cAChBkG,QAAQ,EAAEzG;YACZ,CAAC;UAAA,CAAC,CAAC;UACHyD,SAAS,EAAE;YACTV,KAAK,EAAE,SAAS;YAChB2D,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UAC3B,CAAC;UACDC,QAAQ,EAAE;YACRlD,SAAS,EAAE;cACTV,KAAK,EAAE,SAAS;cAChB2D,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;cAC1BE,UAAU,EAAE,EAAE;cACdC,WAAW,EAAE,0BAA0B;cACvCC,WAAW,EAAE,CAAC;cACdC,WAAW,EAAE;YACf;UACF;QACF,CAAC;MAEL,CAAC;;MAED;MACA/E,OAAO,CAACkF,GAAG,CAAC,OAAO,CAAC,EAAC;MACrBlF,OAAO,CAACmF,EAAE,CAAC,OAAO,EAAE,UAASjD,MAAM,EAAE;QACnC,IAAIA,MAAM,CAACkD,aAAa,KAAK,QAAQ,IAAIlD,MAAM,CAACmD,UAAU,KAAK,KAAK,EAAE;UACpE;UACA,IAAMC,UAAU,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;UACvC,IAAMC,OAAO,GAAGJ,UAAU,CAACK,OAAO,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;;UAE3E;UACA,IAAMC,QAAQ,GAAG,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAACC,oBAAoB,CAAC;UAC3E;UACA,IAAMC,WAAW,GAAG;YAClB,GAAG,EAAE,QAAQ;YACb,GAAG,EAAE,QAAQ;YACb,GAAG,EAAE,QAAQ;YACb,GAAG,EAAE;UACP,CAAC;UACD,IAAMC,QAAQ,GAAGD,WAAW,CAAC,IAAI,CAACE,wBAAwB,CAAC,IAAI,QAAQ;UACvE,IAAMC,MAAM,GAAG,IAAI,CAACC,oBAAoB,IAAI,EAAE;;UAE9C;UACA,IAAMC,GAAG,MAAApH,MAAA,CAAM0G,OAAO,gBAAA1G,MAAA,CAAa4G,QAAQ,gBAAA5G,MAAA,CAAagH,QAAQ,cAAAhH,MAAA,CAAWkH,MAAM,CAAE;UAEnFpH,OAAO,CAACS,GAAG,CAAC,YAAY,EAAE6G,GAAG,CAAC;UAC9BtH,OAAO,CAACS,GAAG,CAAC,OAAO,EAAE;YAAEqG,QAAQ,EAARA,QAAQ;YAAEI,QAAQ,EAARA,QAAQ;YAAEE,MAAM,EAANA;UAAO,CAAC,CAAC;;UAEpD;UACAX,MAAM,CAACc,IAAI,CAACD,GAAG,EAAE,QAAQ,CAAC;QAC5B;MACF,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;;MAEb;MACAtG,OAAO,CAACuG,KAAK,CAAC,CAAC,CAACpB,EAAE,CAAC,WAAW,EAAE,UAASjD,MAAM,EAAE;QAC/C,IAAMsE,YAAY,GAAG,CAACtE,MAAM,CAACuE,OAAO,EAAEvE,MAAM,CAACwE,OAAO,CAAC;QACrD,IAAI1G,OAAO,CAAC2G,YAAY,CAAC,MAAM,EAAEH,YAAY,CAAC,EAAE;UAC9CxG,OAAO,CAACuG,KAAK,CAAC,CAAC,CAACK,cAAc,CAAC,SAAS,CAAC;QAC3C,CAAC,MAAM;UACL5G,OAAO,CAACuG,KAAK,CAAC,CAAC,CAACK,cAAc,CAAC,SAAS,CAAC;QAC3C;MACF,CAAC,CAAC;MAEF5G,OAAO,CAACyD,SAAS,CAAC9B,MAAM,EAAE,IAAI,CAAC;IACjC,CAAC;IAED;IACAkF,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACtBhI,OAAO,CAACS,GAAG,CAAC,0CAA0C,EAAEM,IAAI,CAACC,SAAS,CAAC,IAAI,CAACiH,gBAAgB,CAAC,CAAC;MAC9F,IAAM/G,OAAO,GAAG,IAAI,CAACvB,WAAW,CAAC,mBAAmB,CAAC;MACrD,IAAI,CAACuB,OAAO,EAAE;MAEd,IAAIgH,SAAS,GAAG,IAAI,CAACD,gBAAgB,IAAI,EAAE;;MAE3C;MACA,IAAI,IAAI,CAACE,2BAA2B,IAAI,IAAI,CAACA,2BAA2B,KAAK,EAAE,EAAE;QAC/ED,SAAS,GAAGA,SAAS,CAACE,MAAM,CAAC,UAAAlJ,IAAI;UAAA,OAAIA,IAAI,CAACmJ,MAAM,KAAKL,KAAI,CAACG,2BAA2B;QAAA,EAAC;MACxF;;MAEA;MACA,IAAMG,eAAe,GAAG;QACtB,GAAG,EAAE,MAAM;QACX,GAAG,EAAE,MAAM;QACX,GAAG,EAAE,KAAK;QACV,GAAG,EAAE,KAAK;QACV,GAAG,EAAE,KAAK;QACV,GAAG,EAAE;MACP,CAAC;;MAED;MACA,IAAMC,QAAQ,GAAG,IAAAC,mBAAA,CAAA9J,OAAA,EAAI,IAAI+J,GAAG,CAACP,SAAS,CAAChD,GAAG,CAAC,UAAAhG,IAAI;QAAA,OAAIA,IAAI,CAACwJ,MAAM;MAAA,EAAC,CAAC,EAAEnJ,IAAI,CAAC,CAAC;;MAExE;MACA,IAAMoJ,SAAS,GAAGJ,QAAQ,CAAC7I,MAAM,GAAG,CAAC,GAAG6I,QAAQ,GAAG,EAAE;;MAErD;MACA,IAAMK,WAAW,GAAG,CAAC,CAAC;MACtBV,SAAS,CAACjJ,OAAO,CAAC,UAAAC,IAAI,EAAI;QACxB,IAAM2J,YAAY,GAAG3J,IAAI,CAACmJ,MAAM;QAChC,IAAI,CAACO,WAAW,CAACC,YAAY,CAAC,EAAE;UAC9BD,WAAW,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC;QAChC;QACAD,WAAW,CAACC,YAAY,CAAC,CAAC3J,IAAI,CAACwJ,MAAM,CAAC,GAAG5G,UAAU,CAAC5C,IAAI,CAAC4J,UAAU,CAAC,IAAI,CAAC;MAC3E,CAAC,CAAC;;MAEF;MACA,IAAMpE,MAAM,GAAG,EAAE;MACjB,IAAMqE,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MACjF,IAAIC,UAAU,GAAG,CAAC;MAElBC,MAAM,CAACC,IAAI,CAACN,WAAW,CAAC,CAAC3J,OAAO,CAAC,UAAA4J,YAAY,EAAI;QAC/C,IAAM1J,YAAY,GAAGmJ,eAAe,CAACO,YAAY,CAAC,IAAIA,YAAY;QAClE,IAAMxG,IAAI,GAAGsG,SAAS,CAACzD,GAAG,CAAC,UAAAiE,IAAI,EAAI;UACjC,OAAOP,WAAW,CAACC,YAAY,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC;QAC7C,CAAC,CAAC;QAEFzE,MAAM,CAACpF,IAAI,CAAC;UACV6C,IAAI,EAAEhD,YAAY;UAClBiD,IAAI,EAAE,MAAM;UACZE,MAAM,EAAE,IAAI;UACZD,IAAI,EAAEA,IAAI;UACVI,SAAS,EAAE;YACTC,KAAK,EAAE,CAAC;YACRT,KAAK,EAAE8G,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACrJ,MAAM;UAC1C,CAAC;UACDiD,SAAS,EAAE;YACTV,KAAK,EAAE8G,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACrJ,MAAM;UAC1C,CAAC;UACD6C,MAAM,EAAE,QAAQ;UAChBC,UAAU,EAAE;QACd,CAAC,CAAC;QACFwG,UAAU,EAAE;MACd,CAAC,CAAC;MAEF,IAAMnG,MAAM,GAAG;QACbC,eAAe,EAAE,aAAa;QAC9BC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXb,IAAI,EAAE;UACR,CAAC;UACDe,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIC,WAAW,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACgG,SAAS,GAAG,OAAO;YAC/ChG,MAAM,CAACnE,OAAO,CAAC,UAAAqE,KAAK,EAAI;cACtB,IAAIA,KAAK,CAACC,KAAK,KAAK,IAAI,IAAID,KAAK,CAACC,KAAK,KAAKC,SAAS,EAAE;gBACrDH,WAAW,OAAAnD,MAAA,CAAOoD,KAAK,CAACG,MAAM,EAAAvD,MAAA,CAAGoD,KAAK,CAACI,UAAU,QAAAxD,MAAA,CAAKoD,KAAK,CAACC,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,uBAAU;cACxF;YACF,CAAC,CAAC;YACF,OAAON,WAAW;UACpB;QACF,CAAC;QACDO,MAAM,EAAE;UACNvB,IAAI,EAAEqC,MAAM,CAACQ,GAAG,CAAC,UAAAmE,CAAC;YAAA,OAAIA,CAAC,CAAClH,IAAI;UAAA,EAAC;UAC7B0B,SAAS,EAAE;YACT5B,KAAK,EAAE;UACT,CAAC;UACD6B,GAAG,EAAE;QACP,CAAC;QACDC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbJ,GAAG,EAAE,KAAK;UACVK,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLhC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAEsG,SAAS,CAACzD,GAAG,CAAC,UAAAiE,IAAI,EAAI;YAC1B;YACA,IAAIA,IAAI,IAAIA,IAAI,CAACzJ,MAAM,KAAK,CAAC,EAAE;cAC7B,OAAOyJ,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGH,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YAC1D;YACA,OAAOH,IAAI;UACb,CAAC,CAAC;UACF9E,QAAQ,EAAE;YACR5B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF,CAAC;UACDqC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbmD,QAAQ,EAAE,MAAM;YAChBC,MAAM,EAAE;UACV,CAAC;UACDd,SAAS,EAAE;YACTC,IAAI,EAAE;UACR;QACF,CAAC;QACDC,KAAK,EAAE;UACLrC,IAAI,EAAE,OAAO;UACbD,IAAI,EAAE,WAAW;UACjBmC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbkB,SAAS,EAAE;UACb,CAAC;UACDkB,QAAQ,EAAE;YACR5B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF,CAAC;UACDsC,SAAS,EAAE;YACT9B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACDyC,MAAM,EAAEA;MACV,CAAC;MAEDxD,OAAO,CAACyD,SAAS,CAAC9B,MAAM,EAAE,IAAI,CAAC;IACjC,CAAC;IAED;IACA0G,0BAA0B,WAA1BA,0BAA0BA,CAAA,EAAG;MAC3BvJ,OAAO,CAACS,GAAG,CAAC,+CAA+C,EAAEM,IAAI,CAACC,SAAS,CAAC,IAAI,CAACwI,qBAAqB,CAAC,CAAC;MACxG,IAAMtI,OAAO,GAAG,IAAI,CAACvB,WAAW,CAAC,wBAAwB,CAAC;MAC1D,IAAI,CAACuB,OAAO,EAAE;MAEd,IAAMnC,aAAa,GAAG,IAAI,CAACyK,qBAAqB,IAAI,EAAE;MAEtD,IAAM3G,MAAM,GAAG;QACbC,eAAe,EAAE,aAAa;QAC9BC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXb,IAAI,EAAE;UACR,CAAC;UACDe,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIC,WAAW,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACjB,IAAI,GAAG,OAAO;YAC1CiB,MAAM,CAACnE,OAAO,CAAC,UAAAqE,KAAK,EAAI;cACtB,IAAIA,KAAK,CAACC,KAAK,KAAK,IAAI,IAAID,KAAK,CAACC,KAAK,KAAKC,SAAS,EAAE;gBACrDH,WAAW,OAAAnD,MAAA,CAAOoD,KAAK,CAACG,MAAM,EAAAvD,MAAA,CAAGoD,KAAK,CAACI,UAAU,QAAAxD,MAAA,CAAKoD,KAAK,CAACC,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,uBAAU;cACxF;YACF,CAAC,CAAC;YACF,OAAON,WAAW;UACpB;QACF,CAAC;QACDO,MAAM,EAAE;UACNvB,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC;UAChCwB,SAAS,EAAE;YACT5B,KAAK,EAAE;UACT,CAAC;UACD6B,GAAG,EAAE;QACP,CAAC;QACDC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbJ,GAAG,EAAE,KAAK;UACVK,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLhC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAEtD,aAAa,CAACmG,GAAG,CAAC,UAAAhG,IAAI;YAAA,OAAIA,IAAI,CAACC,YAAY,IAAI,MAAM;UAAA,EAAC;UAC5DkF,QAAQ,EAAE;YACR5B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF,CAAC;UACDqC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbmD,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE;UACV,CAAC;UACDd,SAAS,EAAE;YACTC,IAAI,EAAE;UACR;QACF,CAAC;QACDC,KAAK,EAAE;UACLrC,IAAI,EAAE,OAAO;UACbD,IAAI,EAAE,WAAW;UACjBmC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbkB,SAAS,EAAE;UACb,CAAC;UACDkB,QAAQ,EAAE;YACR5B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF,CAAC;UACDsC,SAAS,EAAE;YACT9B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACDyC,MAAM,EAAE,CACN;UACEvC,IAAI,EAAE,QAAQ;UACdC,IAAI,EAAE,KAAK;UACXqH,KAAK,EAAE,IAAI;UACXpH,IAAI,EAAEtD,aAAa,CAACmG,GAAG,CAAC,UAAAhG,IAAI;YAAA,OAAI4C,UAAU,CAAC5C,IAAI,CAACwK,qBAAqB,CAAC,IAAI,CAAC;UAAA,EAAC;UAC5E/G,SAAS,EAAE;YACTV,KAAK,EAAE,SAAS;YAChB2D,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UAC3B;QACF,CAAC,EACD;UACEzD,IAAI,EAAE,OAAO;UACbC,IAAI,EAAE,KAAK;UACXqH,KAAK,EAAE,IAAI;UACXpH,IAAI,EAAEtD,aAAa,CAACmG,GAAG,CAAC,UAAAhG,IAAI;YAAA,OAAI4C,UAAU,CAAC5C,IAAI,CAACyK,0BAA0B,CAAC,IAAI,CAAC;UAAA,EAAC;UACjFhH,SAAS,EAAE;YACTV,KAAK,EAAE,SAAS;YAChB2D,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UAC3B;QACF,CAAC,EACD;UACEzD,IAAI,EAAE,KAAK;UACXC,IAAI,EAAE,MAAM;UACZC,IAAI,EAAEtD,aAAa,CAACmG,GAAG,CAAC,UAAAhG,IAAI;YAAA,OAAI4C,UAAU,CAAC5C,IAAI,CAAC0K,oBAAoB,CAAC,IAAI,CAAC;UAAA,EAAC;UAC3EtH,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,QAAQ;UAChBC,UAAU,EAAE,CAAC;UACbC,SAAS,EAAE;YACTC,KAAK,EAAE,CAAC;YACRT,KAAK,EAAE;UACT,CAAC;UACDU,SAAS,EAAE;YACTV,KAAK,EAAE;UACT;QACF,CAAC;MAEL,CAAC;MAEDf,OAAO,CAACyD,SAAS,CAAC9B,MAAM,EAAE,IAAI,CAAC;IACjC,CAAC;IAED;IACAgH,4BAA4B,WAA5BA,4BAA4BA,CAAA,EAAG;MAC7B7J,OAAO,CAACS,GAAG,CAAC,iDAAiD,EAAEM,IAAI,CAACC,SAAS,CAAC,IAAI,CAAChC,uBAAuB,CAAC,CAAC;;MAE5G;MACA,IAAI,CAAC8K,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAChC,CAAC;IAED;IACAD,sBAAsB,WAAtBA,sBAAsBA,CAAA,EAAG;MAAA,IAAAE,MAAA;MACvB,IAAMC,WAAW,GAAGnK,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC;MACjE,IAAI,CAACkK,WAAW,EAAE;QAChBjK,OAAO,CAACC,KAAK,CAAC,8BAA8B,CAAC;QAC7C,OAAO,IAAI;MACb;;MAEA;MACA,IAAI,IAAI,CAACE,cAAc,CAAC+J,kBAAkB,EAAE;QAC1C,IAAI,CAAC/J,cAAc,CAAC+J,kBAAkB,CAAC1J,OAAO,CAAC,CAAC;MAClD;MAEA,IAAM2J,QAAQ,GAAG9L,OAAO,CAACwC,IAAI,CAACoJ,WAAW,CAAC;MAC1C,IAAI,CAAC9J,cAAc,CAAC+J,kBAAkB,GAAGC,QAAQ;MAEjD,IAAMpL,aAAa,GAAG,IAAI,CAACC,uBAAuB,IAAI,EAAE;;MAExD;MACA,IAAIoL,YAAY,GAAGrL,aAAa;MAChC,IAAI,IAAI,CAACsL,sBAAsB,IAAI,IAAI,CAACA,sBAAsB,KAAK,EAAE,EAAE;QACrED,YAAY,GAAGrL,aAAa,CAACqJ,MAAM,CAAC,UAAAlJ,IAAI,EAAI;UAC1C,OAAOA,IAAI,CAACE,UAAU,KAAK4K,MAAI,CAACK,sBAAsB,IAC/CnL,IAAI,CAACE,UAAU,CAACC,QAAQ,CAAC2K,MAAI,CAACK,sBAAsB,CAAC,IACrDL,MAAI,CAACK,sBAAsB,CAAChL,QAAQ,CAACH,IAAI,CAACE,UAAU,CAAC;QAC9D,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIkL,UAAU,GAAG,EAAE;MACnBF,YAAY,CAACnL,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3B,IAAIA,IAAI,CAACqL,6BAA6B,EAAE;UACtCrL,IAAI,CAACqL,6BAA6B,CAACtL,OAAO,CAAC,UAAAuL,MAAM,EAAI;YACnD,IAAIA,MAAM,CAACC,WAAW,GAAGH,UAAU,EAAE;cACnCA,UAAU,GAAGE,MAAM,CAACC,WAAW;YACjC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;;MAEF;MACA,IAAMC,OAAO,GAAG,EAAE;MAClB,IAAIC,cAAc,GAAG,CAAC;MAEtBP,YAAY,CAACnL,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3B,IAAIA,IAAI,CAACqL,6BAA6B,EAAE;UACtC,IAAMK,YAAY,GAAG1L,IAAI,CAACqL,6BAA6B,CAACM,IAAI,CAAC,UAAAL,MAAM;YAAA,OAAIA,MAAM,CAACC,WAAW,KAAKH,UAAU;UAAA,EAAC;UACzG,IAAIM,YAAY,EAAE;YAChB,IAAMrH,KAAK,GAAGzB,UAAU,CAAC8I,YAAY,CAACE,MAAM,CAAC,IAAI,CAAC;YAClDH,cAAc,IAAIpH,KAAK;YACvBmH,OAAO,CAACpL,IAAI,CAAC;cACX6C,IAAI,EAAEjD,IAAI,CAACE,UAAU,IAAI,MAAM;cAC/BmE,KAAK,EAAEA;YACT,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;;MAEF;MACAmH,OAAO,CAACzL,OAAO,CAAC,UAAAC,IAAI,EAAI;QACtBA,IAAI,CAAC6L,UAAU,GAAGJ,cAAc,GAAG,CAAC,GAAG,CAAEzL,IAAI,CAACqE,KAAK,GAAGoH,cAAc,GAAI,GAAG,EAAEhH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;MAC7F,CAAC,CAAC;;MAEF;MACA,IAAMnE,QAAQ,GAAG,IAAI,CAACZ,6BAA6B,CAAC,CAAC;;MAErD;MACA8L,OAAO,CAACzL,OAAO,CAAC,UAAAC,IAAI,EAAI;QACtBA,IAAI,CAACyD,SAAS,GAAG;UACfV,KAAK,EAAEzC,QAAQ,CAACN,IAAI,CAACiD,IAAI,CAAC,IAAI,SAAS;UACvCyD,YAAY,EAAE,CAAC;UACfK,WAAW,EAAE,MAAM;UACnBD,WAAW,EAAE;QACf,CAAC;MACH,CAAC,CAAC;MAEF,IAAMgF,SAAS,GAAG;QAChBlI,eAAe,EAAE,aAAa;QAC9BC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfG,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,UAAAlD,MAAA,CAAUkD,MAAM,CAACjB,IAAI,+BAAAjC,MAAA,CAAakD,MAAM,CAACG,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,gCAAAzD,MAAA,CAAckD,MAAM,CAACf,IAAI,CAAC0I,UAAU;UAC/F;QACF,CAAC;QACDnH,MAAM,EAAE;UACNqH,MAAM,EAAE,UAAU;UAClBjH,IAAI,EAAE,IAAI;UACVF,GAAG,EAAE,QAAQ;UACbD,SAAS,EAAE;YACT5B,KAAK,EAAE,MAAM;YACbiJ,QAAQ,EAAE;UACZ,CAAC;UACDC,OAAO,EAAE,CAAC;UACVC,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE;QACd,CAAC;QACD3G,MAAM,EAAE,CACN;UACEvC,IAAI,EAAE,OAAO;UACbC,IAAI,EAAE,KAAK;UACXkJ,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,iBAAiB,EAAE,KAAK;UACxBC,KAAK,EAAE;YACLjH,IAAI,EAAE;UACR,CAAC;UACDqB,QAAQ,EAAE;YACR4F,KAAK,EAAE;cACLjH,IAAI,EAAE,IAAI;cACV0G,QAAQ,EAAE,IAAI;cACdQ,UAAU,EAAE,MAAM;cAClBzJ,KAAK,EAAE,MAAM;cACbkB,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;gBAC1B,UAAAlD,MAAA,CAAUkD,MAAM,CAACjB,IAAI,QAAAjC,MAAA,CAAKkD,MAAM,CAACf,IAAI,CAAC0I,UAAU;cAClD;YACF;UACF,CAAC;UACD1I,IAAI,EAAEqI;QACR,CAAC;MAEL,CAAC;MAEDP,QAAQ,CAACxF,SAAS,CAACqG,SAAS,EAAE,IAAI,CAAC;IACrC,CAAC;IAED;IACAjB,uBAAuB,WAAvBA,uBAAuBA,CAAA,EAAG;MAAA,IAAA4B,MAAA;MACxB,IAAMC,YAAY,GAAG9L,QAAQ,CAACC,cAAc,CAAC,qBAAqB,CAAC;MACnE,IAAI,CAAC6L,YAAY,EAAE;QACjB5L,OAAO,CAACC,KAAK,CAAC,gCAAgC,CAAC;QAC/C,OAAO,IAAI;MACb;;MAEA;MACA,IAAI,IAAI,CAACE,cAAc,CAAC0L,mBAAmB,EAAE;QAC3C,IAAI,CAAC1L,cAAc,CAAC0L,mBAAmB,CAACrL,OAAO,CAAC,CAAC;MACnD;MAEA,IAAMsL,SAAS,GAAGzN,OAAO,CAACwC,IAAI,CAAC+K,YAAY,CAAC;MAC5C,IAAI,CAACzL,cAAc,CAAC0L,mBAAmB,GAAGC,SAAS;MAEnD,IAAM/M,aAAa,GAAG,IAAI,CAACC,uBAAuB,IAAI,EAAE;;MAExD;MACA,IAAIoL,YAAY,GAAGrL,aAAa;MAChC,IAAI,IAAI,CAACsL,sBAAsB,IAAI,IAAI,CAACA,sBAAsB,KAAK,EAAE,EAAE;QACrED,YAAY,GAAGrL,aAAa,CAACqJ,MAAM,CAAC,UAAAlJ,IAAI,EAAI;UAC1C,OAAOA,IAAI,CAACE,UAAU,KAAKuM,MAAI,CAACtB,sBAAsB,IAC/CnL,IAAI,CAACE,UAAU,CAACC,QAAQ,CAACsM,MAAI,CAACtB,sBAAsB,CAAC,IACrDsB,MAAI,CAACtB,sBAAsB,CAAChL,QAAQ,CAACH,IAAI,CAACE,UAAU,CAAC;QAC9D,CAAC,CAAC;MACJ;;MAEA;MACA,IAAMmJ,QAAQ,GAAG,IAAIE,GAAG,CAAC,CAAC;MAC1B2B,YAAY,CAACnL,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3B,IAAIA,IAAI,CAACqL,6BAA6B,EAAE;UACtCrL,IAAI,CAACqL,6BAA6B,CAACtL,OAAO,CAAC,UAAAuL,MAAM,EAAI;YACnDjC,QAAQ,CAACwD,GAAG,CAACvB,MAAM,CAACC,WAAW,CAAC;UAClC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,IAAMuB,WAAW,GAAGvK,KAAK,CAACwK,IAAI,CAAC1D,QAAQ,CAAC,CAAChJ,IAAI,CAAC,CAAC;;MAE/C;MACA,IAAM2M,cAAc,GAAGF,WAAW,CAAC9G,GAAG,CAAC,UAAAiH,OAAO,EAAI;QAChD,IAAIA,OAAO,IAAIA,OAAO,CAACzM,MAAM,KAAK,CAAC,EAAE;UACnC,IAAM0M,KAAK,GAAGD,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;UACrC,IAAM+C,GAAG,GAAGF,OAAO,CAAC7C,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;UACnC,UAAApJ,MAAA,CAAUkM,KAAK,OAAAlM,MAAA,CAAImM,GAAG;QACxB;QACA,OAAOF,OAAO;MAChB,CAAC,CAAC;;MAEF;MACA,IAAM9K,UAAU,GAAG,EAAE;MACrB,IAAMC,UAAU,GAAG,EAAE;;MAErB;MACA,IAAM9B,QAAQ,GAAG,IAAI,CAACZ,6BAA6B,CAAC,CAAC;MAErDwL,YAAY,CAACnL,OAAO,CAAC,UAACC,IAAI,EAAEO,KAAK,EAAK;QACpC,IAAM6M,QAAQ,GAAGpN,IAAI,CAACE,UAAU,IAAI,MAAM;;QAE1C;QACA,IAAMmN,QAAQ,GAAGP,WAAW,CAAC9G,GAAG,CAAC,UAAAiE,IAAI,EAAI;UAAA,IAAAqD,qBAAA;UACvC,IAAMhC,MAAM,IAAAgC,qBAAA,GAAGtN,IAAI,CAACqL,6BAA6B,cAAAiC,qBAAA,uBAAlCA,qBAAA,CAAoC3B,IAAI,CAAC,UAAA4B,CAAC;YAAA,OAAIA,CAAC,CAAChC,WAAW,KAAKtB,IAAI;UAAA,EAAC;UACpF,OAAOqB,MAAM,GAAG1I,UAAU,CAAC0I,MAAM,CAACM,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI;QACvD,CAAC,CAAC;;QAEF;QACA,IAAM4B,aAAa,GAAGlN,QAAQ,CAAC8M,QAAQ,CAAC,IAAI,SAAS;QAErD,IAAMpK,UAAU,GAAG;UACjBC,IAAI,EAAEmK,QAAQ;UACdlK,IAAI,EAAE,MAAM;UACZC,IAAI,EAAEkK,QAAQ;UACdjK,MAAM,EAAE,IAAI;UACZC,MAAM,EAAE,QAAQ;UAChBC,UAAU,EAAE,CAAC;UACbC,SAAS,EAAE;YACTC,KAAK,EAAE,CAAC;YACRT,KAAK,EAAEyK;UACT,CAAC;UACD/J,SAAS,EAAE;YACTV,KAAK,EAAEyK;UACT,CAAC;UACD9J,YAAY,EAAE;QAChB,CAAC;QAEDvB,UAAU,CAAC/B,IAAI,CAAC4C,UAAU,CAAC;QAC3BZ,UAAU,CAAChC,IAAI,CAACgN,QAAQ,CAAC;MAC3B,CAAC,CAAC;MAEF,IAAMK,UAAU,GAAG;QACjB7J,eAAe,EAAE,aAAa;QAC9BC,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,WAAW,EAAE;YACXb,IAAI,EAAE,OAAO;YACbc,UAAU,EAAE;cACVjB,KAAK,EAAE;YACT;UACF,CAAC;UACDkB,SAAS,EAAE,SAAXA,SAASA,CAAWC,MAAM,EAAE;YAC1B,IAAIC,WAAW,GAAGD,MAAM,CAAC,CAAC,CAAC,CAACjB,IAAI,GAAG,OAAO;YAC1CiB,MAAM,CAACnE,OAAO,CAAC,UAAAqE,KAAK,EAAI;cACtB,IAAIA,KAAK,CAACC,KAAK,KAAK,IAAI,IAAID,KAAK,CAACC,KAAK,KAAKC,SAAS,EAAE;gBACrDH,WAAW,OAAAnD,MAAA,CAAOoD,KAAK,CAACG,MAAM,EAAAvD,MAAA,CAAGoD,KAAK,CAACI,UAAU,QAAAxD,MAAA,CAAKoD,KAAK,CAACC,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,iBAAS;cACvF;YACF,CAAC,CAAC;YACF,OAAON,WAAW;UACpB;QACF,CAAC;QACDO,MAAM,EAAE;UACNvB,IAAI,EAAEf,UAAU;UAChBuC,SAAS,EAAE;YACT5B,KAAK,EAAE;UACT,CAAC;UACD6B,GAAG,EAAE;QACP,CAAC;QACDC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,KAAK;UACbJ,GAAG,EAAE,KAAK;UACVK,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLhC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE6J,cAAc;UACpB7H,QAAQ,EAAE;YACR5B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF,CAAC;UACDqC,SAAS,EAAE;YACTrC,KAAK,EAAE;UACT,CAAC;UACDsC,SAAS,EAAE;YACTC,IAAI,EAAE;UACR;QACF,CAAC;QACDC,KAAK,EAAE;UACLrC,IAAI,EAAE,OAAO;UACbD,IAAI,EAAE,SAAS;UACfmC,SAAS,EAAE;YACTrC,KAAK,EAAE,MAAM;YACbkB,SAAS,EAAE;UACb,CAAC;UACDkB,QAAQ,EAAE;YACR5B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF,CAAC;UACDsC,SAAS,EAAE;YACT9B,SAAS,EAAE;cACTR,KAAK,EAAE;YACT;UACF;QACF,CAAC;QACDyC,MAAM,EAAErD;MACV,CAAC;MAEDyK,SAAS,CAACnH,SAAS,CAACgI,UAAU,EAAE,IAAI,CAAC;IACvC;EACF;AACF,CAAC", "ignoreList": []}]}