package com.ruoyi.app.purchase.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购计划全景跟踪数量汇总对象 TPOPI99_PLAN
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
public class PurchasePlan extends BaseEntity {
    /** 记录创建责任者 */
    @Excel(name = "记录创建责任者")
    private String recCreator;

    /** 记录创建时刻 */
    @Excel(name = "记录创建时刻")
    private String recCreateTime;

    /** 记录修改责任者 */
    @Excel(name = "记录修改责任者")
    private String recRevisor;

    /** 记录修改时刻 */
    @Excel(name = "记录修改时刻")
    private String recReviseTime;

    //中位数天数
    private String midDays;

    //平均天数
    private String avgDays;

    //统计类型 A-接收至挂单 B-挂单至入库 C-入库至领用
    private String countType;

    //物料类型
    private String itemType;

    //会计期
    private String period;

    //超期未领用条数
    private BigDecimal lyNum;

    //超期未入库条数
    private BigDecimal rkNum;

    public String getRecCreator() {
        return recCreator;
    }

    public void setRecCreator(String recCreator) {
        this.recCreator = recCreator;
    }

    public String getRecCreateTime() {
        return recCreateTime;
    }

    public void setRecCreateTime(String recCreateTime) {
        this.recCreateTime = recCreateTime;
    }

    public String getRecRevisor() {
        return recRevisor;
    }

    public void setRecRevisor(String recRevisor) {
        this.recRevisor = recRevisor;
    }

    public String getRecReviseTime() {
        return recReviseTime;
    }

    public void setRecReviseTime(String recReviseTime) {
        this.recReviseTime = recReviseTime;
    }

    public String getMidDays() {
        return midDays;
    }

    public void setMidDays(String midDays) {
        this.midDays = midDays;
    }

    public String getAvgDays() {
        return avgDays;
    }

    public void setAvgDays(String avgDays) {
        this.avgDays = avgDays;
    }

    public String getCountType() {
        return countType;
    }

    public void setCountType(String countType) {
        this.countType = countType;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public BigDecimal getLyNum() {
        return lyNum;
    }

    public void setLyNum(BigDecimal lyNum) {
        this.lyNum = lyNum;
    }

    public BigDecimal getRkNum() {
        return rkNum;
    }

    public void setRkNum(BigDecimal rkNum) {
        this.rkNum = rkNum;
    }
}
