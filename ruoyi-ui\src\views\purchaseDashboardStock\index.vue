<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1>采购库存看板</h1>
      <div class="header-controls">
        <div class="fullscreen-btn" @click="toggleFullscreen" :title="isFullscreen ? '退出全屏' : '进入全屏'">
          <i :class="isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'"></i>
        </div>
        <div class="time-filter">
          <button
            v-for="filter in timeFilters"
            :key="filter.id"
            :class="['time-filter-btn', { active: filter.id === activeFilter }]"
            @click="handleTimeFilterChange(filter.id, filter.value)"
          >
            {{ filter.label }}
          </button>
        </div>
      </div>
    </div>

    <div class="dashboard-grid">
      <!-- 第一行：中心仓库月度库存金额 -->
      <div class="card" style="grid-column: span 6; grid-row: 1;">
        <h2 class="card-title">
          中心仓库月度库存金额
          <div class="chart-filter-dropdown-container">
            <select
              v-model="selectedYear"
              @change="handleYearChange"
              style="margin-right: 10px;"
            >
              <option value="">全部年份</option>
              <option v-for="year in availableYears" :key="year" :value="year">
                {{ year }}年
              </option>
            </select>
            <select
              v-model="selectedMaterialType"
              @change="handleMaterialTypeChange"
            >
              <option value="">总和</option>
              <option value="A">通用备件</option>
              <option value="B">专用备件</option>
              <option value="C">材料类</option>
              <option value="D">原材料</option>
              <option value="E">辅耐材</option>
              <option value="G">办公</option>
            </select>
          </div>
        </h2>
        <div id="monthlyInventoryChart" class="chart"></div>
      </div>

      <!-- 第一行：机旁库当前库存 -->
      <div class="card" style="grid-column: span 6; grid-row: 1;">
        <h2 class="card-title">
          机旁库当前库存
          <div class="chart-filter-dropdown-container">
            <select
              v-model="selectedFactoryDep"
              @change="handleFactoryDepChange"
              style="margin-right: 10px;"
            >
              <option value="">全部分厂</option>
              <option v-for="depName in factoryDepOptions" :key="depName" :value="depName">
                {{ depName }}
              </option>
            </select>
            <select
              v-model="selectedFactoryMaterialType"
              @change="handleFactoryMaterialTypeChange"
            >
              <option value="">全部物料</option>
              <option value="A">通用备件</option>
              <option value="B">专用备件</option>
              <option value="C">材料类</option>
              <option value="D">原材料</option>
              <option value="E">辅耐材</option>
              <option value="G">办公</option>
            </select>
          </div>
        </h2>
        <div id="factoryStockChart" class="chart"></div>
      </div>

      <!-- 第二行：矿焦煤实时库存 -->
      <div class="card" style="grid-column: span 6; grid-row: 2;">
        <h2 class="card-title">
          <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
            <div style="display: flex; align-items: center; gap: 15px;">
              <span>矿焦煤实时库存</span>
              <span class="inventory-total">
                合计: {{ calculateCokingCoalTotal() }}万吨
              </span>
            </div>
            <div class="chart-filter-dropdown-container">
              <select
                v-model="selectedCokingCoalType"
                @change="handleCokingCoalTypeChange"
              >
                <option value="">全部</option>
                <option value="矿料类">矿料类</option>
                <option value="焦炭">焦炭</option>
                <option value="煤焦类">煤焦类</option>
                <option value="合金类">合金类</option>
                <option value="辅助类/电极">辅助类/电极</option>
              </select>
            </div>
          </div>
        </h2>
        <div class="chart" style="display: flex; height: 100%;">
          <div id="cokingCoalPieChart" style="width: 25%; height: 100%;"></div>
          <div id="cokingCoalLineChart" style="width: 75%; height: 100%;"></div>
        </div>
      </div>

      <!-- 物料入库统计 -->
      <div class="card material-chart-card" style="grid-column: span 6; grid-row: 2;">
        <h2 class="card-title">
          物料入库统计
          <div class="chart-filter-dropdown-container">
            <select
              v-model="selectedMaterialCategory"
              @change="handleMaterialCategoryChange"
              style="margin-right: 10px;"
            >
              <option value="1">大类</option>
              <option value="2">中类</option>
              <option value="3">细类</option>
              <option value="4">叶类</option>
            </select>
            <select
              v-model="selectedMaterialItem"
              @change="handleMaterialItemChange"
            >
              <option value="">全部</option>
              <option v-for="item in materialItemOptions" :key="item.itemId" :value="item.itemId">
                {{ item.itemName }}
              </option>
            </select>
          </div>
        </h2>
        <div id="materialStatisticsChart" class="chart"></div>
      </div>

    </div>

    <!-- 物料价格对比弹框 -->
    <el-dialog
      title="物料价格趋势对比分析"
      :visible.sync="comparisonDialogVisible"
      width="90%"
      :before-close="closeComparisonDialog"
      custom-class="comparison-dialog"
    >
      <div class="comparison-content">
        <div class="comparison-header">
          <div class="comparison-title">
            <span class="base-material">{{ currentComparison.itemName }}</span>
            <span class="vs-text">VS</span>
            <span class="compare-material">{{ currentComparison.compareItemName }}</span>
            <span class="similarity-info">相似度：{{ currentComparison.score }}</span>
          </div>
        </div>

        <div class="comparison-chart-container">
          <div
            id="comparisonChart"
            class="comparison-chart"
            v-loading="comparisonChartLoading"
            element-loading-text="正在加载对比数据..."
          ></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import chartMethods from './chartMethods'
import extendedChartMethods from './chartMethodsExtended'
import screenfull from 'screenfull'
import { showYearlyAmount, showRealTimeAmount, showCokingCoalAmount, showKeyIndicators, showItemTypeList, showMaterialList, showData, showSuppList, showHighFrequencyMaterialList, showPurchaseSuppRisk, getMaterialFuturePrice, getMaterialNameList, getPurchasePriceAndStore, getMaterialNameListFromNewTables, getPurchasePriceAndStoreFromNewTables } from '@/api/purchaseDashboard/purchaseDashboard'
import { listSimilarByItemNames } from '@/api/purchase/similar'
import { getDepNameList, getListMonthly } from '@/api/purchase/purdchaseFactoryStock'

export default {
  name: 'PurchaseDashboard',
  mixins: [chartMethods, extendedChartMethods],
  data() {
    return {
      // 时间过滤器选项
      timeFilters: [
        { id: 'filter-3m', label: '近三个月', value: 1 },
        { id: 'filter-6m', label: '近六个月', value: 2 },
        { id: 'filter-1y', label: '近一年', value: 3 }
      ],
      activeFilter: 'filter-1y',
      currentDimensionType: 3,

      // 数据
      dashboardData: {},
      purchaseStats: {},

      // 下拉选项
      topSuppliersOptions: [],

      // 选中的过滤器值
      selectedTopSuppliersFilter: '',
      selectedOrderType: 'TOP', // 排序类型，默认为TOP

      // 图表实例
      chartInstances: {},

      // 原始数据备份
      originalTopSuppliersData: [],

      // 库存图表相关
      selectedYear: '',
      selectedMaterialType: '',
      availableYears: (() => {
        const currentYear = new Date().getFullYear()
        const years = []
        for (let year = 2020; year <= currentYear; year++) {
          years.push(year.toString())
        }
        return years
      })(),
      yearlyInventoryData: [],
      realTimeInventoryData: [],
      cokingCoalInventoryData: [],

      // 矿焦煤库存图表相关
      selectedCokingCoalType: '', // 选中的矿焦煤类型，默认为空（全部）

      // 物料入库统计相关
      selectedMaterialCategory: '1',
      selectedMaterialItem: '',
      materialItemOptions: [],
      materialStatisticsData: [],

      // 高频采购物料相关
      selectedCodeType: 'ALL',
      selectedItemType: 'CLASS3',
      highFrequencyMaterialData: [],

      // 供应商风险数据
      supplierRiskData: [],

      // AI价格预测相关
      pricePredictions: [], // 改为数组，支持多个物料的预测
      predictionLoading: false,

      // 物料价格趋势图相关
      materialNameOptions: [],
      selectedMaterial: 'PB块',
      selectedMaterialCategory: '1', // 默认选择矿石
      priceAndStoreData: null,

      // 新的价格趋势图相关属性
      // 采购量曲线
      purchaseAmountCategories: [99], // 默认选择全部
      selectedPurchaseAmountMaterials: [],
      purchaseAmountMaterialOptions: [],

      // 市场价曲线
      marketPriceCategories: [99], // 默认选择全部
      selectedMarketPriceMaterials: [],
      marketPriceMaterialOptions: [],

      // 获取数据状态
      fetchingPriceData: false,
      newPriceAndStoreData: null,

      // 初始化标志
      hasInitializedPriceChart: false,

      // 相似物料数据
      similarMaterialsData: [],
      similarMaterialsLoading: false,

      // 对比弹框相关
      comparisonDialogVisible: false,
      comparisonChartLoading: false,
      currentComparison: {},
      comparisonChartInstance: null,
      comparisonPriceData: null,

      // 机旁库当前库存相关
      selectedFactoryDep: '', // 选中的分厂
      selectedFactoryMaterialType: '', // 选中的物料类型
      factoryDepOptions: [], // 分厂选项列表
      factoryStockData: [] // 机旁库存数据
    }
  },

  computed: {
    isFullscreen() {
      return this.$store.state.app.isFullscreenMode
    },

    // 按itemName、category、priceType联合索引分组相似物料数据
    groupedSimilarMaterials() {
      const grouped = {}
      this.similarMaterialsData.forEach(item => {
        // 创建联合索引key
        const groupKey = `${item.itemName}_${item.category}_${item.priceType}`
        const displayKey = `${item.itemName} (${this.getCategoryName(item.category)} - ${this.getPriceTypeName(item.priceType)})`

        if (!grouped[displayKey]) {
          grouped[displayKey] = {
            groupKey: groupKey,
            items: []
          }
        }
        grouped[displayKey].items.push(item)
      })

      // 对每个组内的数据按排名排序
      Object.keys(grouped).forEach(key => {
        grouped[key].items.sort((a, b) => a.rank - b.rank)
      })

      return grouped
    }
  },

  mounted() {
    this.checkEchartsAvailability()
    this.fetchDashboardData(3)
    this.fetchYearlyInventoryData()
    this.fetchRealTimeInventoryData()
    this.fetchCokingCoalInventoryData()
    // 初始化物料入库统计的下拉框选项和数据
    this.updateMaterialItemOptions().then(() => {
      this.fetchMaterialStatisticsData()
    })
    // 初始化高频采购物料数据
    this.fetchHighFrequencyMaterialData()
    // 初始化供应商风险数据
    this.fetchSupplierRiskData()

    // 初始化新的物料名称列表（会自动触发默认选中PB块和数据获取）
    this.fetchPurchaseAmountMaterialList()
    this.fetchMarketPriceMaterialList()

    // 初始化机旁库存数据
    this.fetchFactoryDepOptions()

    this.setupResizeObserver()
    this.initFullscreenListener()

    // 监听窗口大小变化
    window.addEventListener('resize', this.resizeAllCharts)
  },

  beforeDestroy() {
    // 清理定时器和事件监听器
    this.clearAllIntervals()
    this.removeFullscreenListener()
    window.removeEventListener('resize', this.resizeAllCharts)

    // 确保退出全屏模式
    this.$store.dispatch('app/setFullscreenMode', false)
  },

  methods: {
    // 初始化全屏监听器
    initFullscreenListener() {
      if (screenfull && screenfull.isEnabled) {
        screenfull.on('change', this.handleFullscreenChange)
      }
    },

    // 移除全屏监听器
    removeFullscreenListener() {
      if (screenfull && screenfull.isEnabled) {
        screenfull.off('change', this.handleFullscreenChange)
      }
    },

    // 处理全屏状态变化
    handleFullscreenChange() {
      if (screenfull && screenfull.isEnabled) {
        const isFullscreen = screenfull.isFullscreen
        this.$store.dispatch('app/setFullscreenMode', isFullscreen)

        // 全屏状态变化后，重新调整图表大小
        this.$nextTick(() => {
          setTimeout(() => {
            this.resizeAllCharts()
          }, 300) // 给布局变化一些时间
        })
      }
    },

    // API调用方法
    async getDashboardData(dimensionType) {
      return await showData({ dimensionType: dimensionType })
    },

    async getItemTypeList(itemType) {
      return await showItemTypeList({ itemType: itemType })
    },

    async getMaterialList(params) {
      return await showMaterialList(params)
    },

    async getSupplierList(params) {
      return await showSuppList(params)
    },

    async getYearlyAmount(params) {
      return await showYearlyAmount(params)
    },

    async getRealTimeAmount() {
      return await showRealTimeAmount()
    },

    async getCokingCoalAmount() {
      return await showCokingCoalAmount()
    },

    async getKeyIndicators(params) {
      return await showKeyIndicators(params)
    },

    async getHighFrequencyMaterialList(params) {
      return await showHighFrequencyMaterialList(params)
    },

    async getPurchaseSuppRisk(params) {
      return await showPurchaseSuppRisk(params)
    },

    // 根据dimensionType获取timeFlag
    getTimeFlagByDimensionType(dimensionType) {
      switch(dimensionType) {
        case 1: return '03' // 近三个月
        case 2: return '06' // 近六个月
        case 3: return '12' // 近一年
        default: return '03'
      }
    },

    // 检查ECharts可用性
    checkEchartsAvailability() {
      if (!echarts) {
        console.error('ECharts库未能加载，使用备用显示方式')
        document.querySelectorAll('.chart').forEach(el => {
          el.innerHTML = '<div class="chart-placeholder">图表加载失败</div>'
        })
        return false
      }
      return true
    },

    // 获取仪表板数据
    async fetchDashboardData(dimensionTypeParam = 1) {
      this.currentDimensionType = dimensionTypeParam

      // 清除所有定时器
      this.clearAllIntervals()

      try {
        // 并行获取仪表板数据和关键指标数据
        const [dashboardResponse, keyIndicatorsResponse] = await Promise.all([
          this.getDashboardData(dimensionTypeParam),
          this.getKeyIndicators({ dimensionType: dimensionTypeParam })
        ])

        // 处理仪表板数据
        if (dashboardResponse && dashboardResponse.data) {
          this.dashboardData = dashboardResponse.data
          console.log('获取仪表板数据成功:', this.dashboardData)
        } else {
          console.error('API数据格式不正确或缺少data字段', dashboardResponse)
          this.showErrorMessage('API数据格式不正确或缺少data字段')
        }

        // 处理关键指标数据
        if (keyIndicatorsResponse && keyIndicatorsResponse.data) {
          this.purchaseStats = keyIndicatorsResponse.data || {}
          console.log('获取关键指标数据成功:', this.purchaseStats)
        } else {
          console.error('获取关键指标数据失败', keyIndicatorsResponse)
          this.purchaseStats = {}
        }

        this.initAllCharts()
      } catch (error) {
        console.error('API请求或数据处理失败', error)
        this.showErrorMessage('数据加载失败: ' + error.message)
      }
    },

    // 显示错误信息
    showErrorMessage(message) {
      document.querySelectorAll('.chart').forEach(chart => {
        chart.innerHTML = `<div class="chart-placeholder">${message}</div>`
      })
    },

    // 时间过滤器变化处理
    handleTimeFilterChange(filterId, dimensionType) {
      this.activeFilter = filterId
      this.currentDimensionType = dimensionType
      console.log('选择的时间范围:', filterId, '维度:', dimensionType)

      this.clearAllIntervals()
      this.fetchDashboardData(dimensionType)
      // 同时更新高频物料数据
      this.fetchHighFrequencyMaterialData()
      // 同时更新供应商风险数据
      this.fetchSupplierRiskData()
      // 同时更新物料入库统计数据
      this.fetchMaterialStatisticsData()
      // 注意：价格趋势数据只在用户主动点击按钮时获取，不在时间过滤器变化时自动获取

      // 同时更新新的物料列表（用于下拉框选项），但不会自动触发数据获取
      this.fetchPurchaseAmountMaterialList()
      this.fetchMarketPriceMaterialList()
    },

    // 清除所有定时器
    clearAllIntervals() {
      Object.values(this.chartInstances).forEach(instance => {
        if (instance && instance.intervalId) {
          clearInterval(instance.intervalId)
          instance.intervalId = null
        }
      })
    },

    // 重新调整所有图表大小
    resizeAllCharts() {
      Object.values(this.chartInstances).forEach(instance => {
        if (instance) {
          try {
            instance.resize()
          } catch(err) {
            console.error('图表大小调整失败:', err)
          }
        }
      })
    },

    // 初始化所有图表
    initAllCharts() {
      console.log('initAllCharts started')
      try {
        // 注意：实时库存图表和矿焦煤库存图表会在各自数据获取完成后单独初始化
        // 注意：月度库存金额图表会在fetchYearlyInventoryData完成后单独初始化
        // 注意：物料入库统计图表会在fetchMaterialStatisticsData完成后单独初始化
        // 注意：机旁库存图表会在fetchFactoryStockData完成后单独初始化

        // 初始化物料词云图
        this.initMaterialCloud()

        // 初始化TOP供应商图
        this.initTopSuppliersChart()
        this.populateItemDropdown('topSuppliersFilter', 1, 'topSuppliersOptions')

        // 注意：供应商风险图表会在fetchSupplierRiskData完成后单独初始化

        // 注意：采购价格趋势图会在fetchPriceAndStoreData完成后单独初始化

        console.log('所有图表初始化完成')
      } catch (err) {
        console.error('图表初始化主流程失败:', err)
        this.showErrorMessage('图表初始化失败: ' + err.message)
      }
    },

    // 填充物料类型下拉框
    async populateItemDropdown(selectElementId, itemType, dataPropertyName) {
      try {
        const response = await this.getItemTypeList(itemType)

        if (response && response.data && Array.isArray(response.data)) {
          this[dataPropertyName] = response.data
        } else {
          console.error(`Invalid data format from showItemTypeList for itemType ${itemType}:`, response)
          this[dataPropertyName] = []
        }
      } catch (error) {
        console.error(`Error fetching item types for ${selectElementId}:`, error)
        this[dataPropertyName] = []
      }
    },

    // 下拉框变化处理方法
    async handleTopSuppliersFilterChange() {
      await this.refreshTopSuppliersChart()
    },

    async handleOrderTypeChange() {
      console.log('排序类型变化:', this.selectedOrderType)
      await this.refreshTopSuppliersChart()
    },

    async refreshTopSuppliersChart() {
      console.log(`Top supplier filter selected item ID: ${this.selectedTopSuppliersFilter}, orderType: ${this.selectedOrderType}`)
      const myChart = this.chartInstances.topSuppliersChart
      if (!myChart) {
        console.error("TOP10供应商图表实例未找到")
        return
      }

      if (myChart.intervalId) {
        clearInterval(myChart.intervalId)
        myChart.intervalId = null
      }

      if (!this.selectedTopSuppliersFilter || this.selectedTopSuppliersFilter === "") {
        // 使用原始数据，但需要根据orderType重新获取
        myChart.showLoading()
        try {
          const response = await this.getSupplierList({
            dimensionType: this.currentDimensionType,
            orderType: this.selectedOrderType
          })

          let newSupplierData = []
          if (response && response.data && Array.isArray(response.data)) {
            newSupplierData = response.data
          } else {
            console.error('从showSuppList API获取的数据无效:', response)
            newSupplierData = this.originalTopSuppliersData
          }
          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)
        } catch (error) {
          console.error(`为topSuppliersChart获取供应商列表失败:`, error)
          this.renderAndPaginateTopSuppliers(myChart, this.originalTopSuppliersData)
        } finally {
          myChart.hideLoading()
        }
      } else {
        myChart.showLoading()
        try {
          const response = await this.getSupplierList({
            dimensionType: this.currentDimensionType,
            itemId: this.selectedTopSuppliersFilter,
            orderType: this.selectedOrderType
          })

          let newSupplierData = []
          if (response && response.data && Array.isArray(response.data)) {
            newSupplierData = response.data
          } else {
            console.error('从showSuppList API获取的数据无效:', response)
          }
          this.renderAndPaginateTopSuppliers(myChart, newSupplierData)
        } catch (error) {
          console.error(`为topSuppliersChart获取供应商列表失败:`, error)
          document.getElementById('topSuppliersChart').innerHTML = '<div class="chart-placeholder">供应商数据加载失败</div>'
        } finally {
          myChart.hideLoading()
        }
      }
    },

    // 设置大小调整观察器
    setupResizeObserver() {
      const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
          const charts = entry.target.querySelectorAll('.chart')
          charts.forEach(chart => {
            if (chart.id) {
              const instance = echarts.getInstanceByDom(document.getElementById(chart.id))
              if (instance) {
                instance.resize()
              }
            }
          })
        }
      })

      document.querySelectorAll('.card').forEach(card => {
        resizeObserver.observe(card)
      })
    },

    toggleFullscreen() {
      if (screenfull && screenfull.isEnabled) {
        screenfull.toggle()
      } else {
        this.$message({
          message: '您的浏览器不支持全屏功能',
          type: 'warning'
        })
      }
    },

    async handleYearChange() {
      console.log('年份变化:', this.selectedYear)
      await this.fetchYearlyInventoryData()
    },

    async handleMaterialTypeChange() {
      console.log('物料类型变化:', this.selectedMaterialType)
      await this.fetchYearlyInventoryData()
    },

    // 获取年度库存数据
    async fetchYearlyInventoryData() {
      try {
        const params = {}

        // 只有当materialType不为空时才传递该参数
        if (this.selectedMaterialType && this.selectedMaterialType !== '') {
          params.materialType = this.selectedMaterialType
        }

        // 如果选择了具体年份，只查询该年份，否则查询所有年份
        if (this.selectedYear) {
          params.yearList = [this.selectedYear]
        } else {
          params.yearList = this.availableYears
        }

        console.log('fetchYearlyInventoryData - 请求参数:', params)
        const response = await this.getYearlyAmount(params)
        console.log('fetchYearlyInventoryData - 完整响应:', response)

        if (response && response.data) {
          this.yearlyInventoryData = response.data || []
          console.log('fetchYearlyInventoryData - 设置的数据:', this.yearlyInventoryData)
        } else {
          // 使用模拟数据
          this.yearlyInventoryData = this.getMockYearlyData()
          console.log('fetchYearlyInventoryData - 使用模拟数据:', this.yearlyInventoryData)
        }
      } catch (error) {
        console.error('获取年度库存数据失败，使用模拟数据:', error)
        // 使用模拟数据
        this.yearlyInventoryData = this.getMockYearlyData()
      }

      // 重新初始化图表
      this.initMonthlyInventoryChart()
    },

    // 生成模拟数据
    getMockYearlyData() {
      return [
        {
          year: '2023',
          monthlyResultVoList: [
            { monthIndex: 1, amount: 1200.50 },
            { monthIndex: 2, amount: 1350.75 },
            { monthIndex: 3, amount: 1180.20 },
            { monthIndex: 4, amount: 1420.30 },
            { monthIndex: 5, amount: 1380.90 },
            { monthIndex: 6, amount: 1520.40 },
            { monthIndex: 7, amount: 1650.60 },
            { monthIndex: 8, amount: 1480.85 },
            { monthIndex: 9, amount: 1390.25 },
            { monthIndex: 10, amount: 1610.70 },
            { monthIndex: 11, amount: 1580.35 },
            { monthIndex: 12, amount: 1720.95 }
          ]
        },
        {
          year: '2024',
          monthlyResultVoList: [
            { monthIndex: 1, amount: 1320.80 },
            { monthIndex: 2, amount: 1450.60 },
            { monthIndex: 3, amount: 1280.40 },
            { monthIndex: 4, amount: 1540.70 },
            { monthIndex: 5, amount: 1480.20 },
            { monthIndex: 6, amount: 1620.50 },
            { monthIndex: 7, amount: 1750.30 },
            { monthIndex: 8, amount: 1580.90 },
            { monthIndex: 9, amount: 1490.60 },
            { monthIndex: 10, amount: 1710.40 },
            { monthIndex: 11, amount: 1680.80 },
            { monthIndex: 12, amount: 1820.20 }
          ]
        }
      ]
    },

    async fetchRealTimeInventoryData() {
      try {
        const response = await this.getRealTimeAmount()
        console.log('fetchRealTimeInventoryData - 完整响应:', response)

        if (response && response.data) {
          this.realTimeInventoryData = response.data || []
          console.log('fetchRealTimeInventoryData - 设置的数据:', this.realTimeInventoryData)
        } else {
          console.error('获取实时库存数据失败，使用模拟数据', response)
          // 使用模拟数据
          this.realTimeInventoryData = this.getMockRealTimeData()
        }
      } catch (error) {
        console.error('获取实时库存数据失败，使用模拟数据:', error)
        // 使用模拟数据
        this.realTimeInventoryData = this.getMockRealTimeData()
      }

      // 数据获取完成后重新初始化图表
      this.$nextTick(() => {
        this.initRealTimeInventoryChart()
      })
    },

    // 生成模拟实时库存数据
    getMockRealTimeData() {
      return [
        {
          materialType: 'A',
          materialName: '通用备件',
          centerInventoryAmount: 1250.30,
          machineSideInventoryAmount: 380.50,
          totalInventoryAmount: 1630.80
        },
        {
          materialType: 'B',
          materialName: '专用备件',
          centerInventoryAmount: 980.75,
          machineSideInventoryAmount: 420.25,
          totalInventoryAmount: 1401.00
        },
        {
          materialType: 'C',
          materialName: '材料类',
          centerInventoryAmount: 2150.60,
          machineSideInventoryAmount: 650.40,
          totalInventoryAmount: 2801.00
        },
        {
          materialType: 'D',
          materialName: '原材料',
          centerInventoryAmount: 3200.90,
          machineSideInventoryAmount: 890.10,
          totalInventoryAmount: 4091.00
        },
        {
          materialType: 'E',
          materialName: '辅耐材',
          centerInventoryAmount: 1580.40,
          machineSideInventoryAmount: 320.60,
          totalInventoryAmount: 1901.00
        },
        {
          materialType: 'G',
          materialName: '办公',
          centerInventoryAmount: 150.20,
          machineSideInventoryAmount: 50.80,
          totalInventoryAmount: 201.00
        }
      ]
    },

    async fetchCokingCoalInventoryData() {
      try {
        const response = await this.getCokingCoalAmount()
        console.log('fetchCokingCoalInventoryData - 完整响应:', response)

        if (response && response.data) {
          this.cokingCoalInventoryData = response.data || []
          console.log('fetchCokingCoalInventoryData - 设置的数据:', this.cokingCoalInventoryData)
        } else {
          console.error('获取矿焦煤库存数据失败', response)
          this.cokingCoalInventoryData = []
        }
      } catch (error) {
        console.error('获取矿焦煤库存数据失败:', error)
        this.cokingCoalInventoryData = []
      }

      // 数据获取完成后重新初始化图表
      this.$nextTick(() => {
        this.initCokingCoalInventoryChart()
      })
    },



    // 物料入库统计相关方法
    async handleMaterialCategoryChange() {
      console.log('物料类别变化:', this.selectedMaterialCategory)
      this.selectedMaterialItem = '' // 重置第二个下拉框
      await this.updateMaterialItemOptions()
      await this.fetchMaterialStatisticsData()
    },

    async handleMaterialItemChange() {
      console.log('物料项目变化:', this.selectedMaterialItem)
      await this.fetchMaterialStatisticsData()
    },

    async updateMaterialItemOptions() {
      if (this.selectedMaterialCategory === '1') {
        // 大类：只有全部选项
        this.materialItemOptions = []
      } else {
        // 中类、细类、叶类：获取对应的选项
        const itemType = parseInt(this.selectedMaterialCategory) - 1 // 1->0, 2->1, 3->2, 4->3
        try {
          const response = await this.getItemTypeList(itemType)
          if (response && response.data && Array.isArray(response.data)) {
            this.materialItemOptions = response.data
          } else {
            this.materialItemOptions = []
          }
        } catch (error) {
          console.error('获取物料项目选项失败:', error)
          this.materialItemOptions = []
        }
      }
    },

    async fetchMaterialStatisticsData() {
      try {
        const params = {
          itemType: parseInt(this.selectedMaterialCategory),
          dimensionType: this.currentDimensionType
        }

        // 如果选择了具体物料项目，添加itemId参数
        if (this.selectedMaterialItem && this.selectedMaterialItem !== '') {
          params.itemId = this.selectedMaterialItem
        }

        console.log('fetchMaterialStatisticsData - 请求参数:', params)
        const response = await this.getMaterialList(params)
        console.log('fetchMaterialStatisticsData - 完整响应:', response)

        if (response && response.data) {
          this.materialStatisticsData = response.data || []
          console.log('fetchMaterialStatisticsData - 设置的数据:', this.materialStatisticsData)
        } else {
          console.error('获取物料统计数据失败，使用模拟数据', response)
          this.materialStatisticsData = this.getMockMaterialStatisticsData()
        }
      } catch (error) {
        console.error('获取物料统计数据失败，使用模拟数据:', error)
        this.materialStatisticsData = this.getMockMaterialStatisticsData()
      }

      // 数据获取完成后重新初始化图表
      this.$nextTick(() => {
        this.initMaterialStatisticsChart()
      })
    },

    // 生成模拟物料统计数据
    getMockMaterialStatisticsData() {
      return [
        { itemName: '通用备件', inAmt: 1250.30, arriveRate: 85.5 },
        { itemName: '专用备件', inAmt: 980.75, arriveRate: 78.2 },
        { itemName: '材料类', inAmt: 2150.60, arriveRate: 92.1 },
        { itemName: '原材料', inAmt: 3200.90, arriveRate: 88.7 },
        { itemName: '辅耐材', inAmt: 1580.40, arriveRate: 91.3 },
        { itemName: '办公', inAmt: 150.20, arriveRate: 95.0 }
      ]
    },

    async fetchHighFrequencyMaterialData() {
      try {
        const params = {
          dimensionType: this.currentDimensionType,
          codeType: this.selectedCodeType,
          itemType: this.selectedItemType
        }

        console.log('fetchHighFrequencyMaterialData - 请求参数:', params)
        const response = await this.getHighFrequencyMaterialList(params)
        console.log('fetchHighFrequencyMaterialData - 完整响应:', response)

        if (response && response.data) {
          this.highFrequencyMaterialData = response.data || []
          console.log('fetchHighFrequencyMaterialData - 设置的数据:', this.highFrequencyMaterialData)
        } else {
          console.error('获取高频物料数据失败，使用模拟数据', response)
          this.highFrequencyMaterialData = this.getMockHighFrequencyData()
        }
      } catch (error) {
        console.error('获取高频物料数据失败，使用模拟数据:', error)
        this.highFrequencyMaterialData = this.getMockHighFrequencyData()
      }

      // 数据获取完成后重新初始化图表
      this.$nextTick(() => {
        this.initMaterialCloud()
      })
    },

    // 生成模拟高频物料数据
    getMockHighFrequencyData() {
      return [
        { itemName: '粗粉', inAmt: 392467.2, inNum: 5421293 },
        { itemName: '精粉', inAmt: 280350.5, inNum: 4250180 },
        { itemName: '球团', inAmt: 195200.8, inNum: 3180970 },
        { itemName: '烧结', inAmt: 150420.3, inNum: 2890540 },
        { itemName: '焦炭', inAmt: 125680.7, inNum: 2350210 },
        { itemName: '煤炭', inAmt: 98750.2, inNum: 1980760 }
      ]
    },

    async handleCodeTypeChange() {
      console.log('大类类型变化:', this.selectedCodeType)
      await this.fetchHighFrequencyMaterialData()
    },

    async handleItemTypeChange() {
      console.log('维度变化:', this.selectedItemType)
      await this.fetchHighFrequencyMaterialData()
    },

    // 获取供应商风险数据
    async fetchSupplierRiskData() {
      try {
        const params = {
          timeFlag: this.getTimeFlagByDimensionType(this.currentDimensionType)
        }

        console.log('fetchSupplierRiskData - 请求参数:', params)
        const response = await this.getPurchaseSuppRisk(params)
        console.log('fetchSupplierRiskData - 完整响应:', response)

        if (response && response.data) {
          this.supplierRiskData = response.data || []
          console.log('fetchSupplierRiskData - 设置的数据:', this.supplierRiskData)
        } else {
          console.error('获取供应商风险数据失败', response)
          this.supplierRiskData = []
        }
      } catch (error) {
        console.error('获取供应商风险数据失败:', error)
        this.supplierRiskData = []
      }

      // 数据获取完成后重新初始化图表
      this.$nextTick(() => {
        this.initSupplierRiskChart()
      })
    },

    // 获取多个物料的AI价格预测
    async fetchMultiplePricePredictions(materialNames) {
      this.predictionLoading = true
      this.pricePredictions = [] // 清空之前的预测结果

      try {
        // 并行调用所有物料的预测接口
        const predictionPromises = materialNames.map(async (materialName) => {
          try {
            const params = {
              materialName: materialName,
              materialType: '1' // 默认使用矿石类型，可以根据需要调整
            }

            console.log(`fetchPricePrediction - ${materialName} 请求参数:`, params)
            const response = await getMaterialFuturePrice(params)
            console.log(`fetchPricePrediction - ${materialName} 完整响应:`, response)

            if (response && response.code && response.code === 200 && response.data) {
              return {
                materialName: materialName,
                question: response.data.question || `关于${materialName}的价格预测`,
                prediction: response.data.answer || response.data.prediction || response.msg,
                success: response.data.success !== false
              }
            } else {
              console.error(`获取${materialName}价格预测数据失败`, response)
              return {
                materialName: materialName,
                question: `关于${materialName}的价格预测`,
                prediction: `获取${materialName}价格预测失败`,
                success: false
              }
            }
          } catch (error) {
            console.error(`获取${materialName}价格预测数据失败:`, error)
            return {
              materialName: materialName,
              question: `关于${materialName}的价格预测`,
              prediction: `获取${materialName}价格预测失败：${error.message}`,
              success: false
            }
          }
        })

        // 等待所有预测结果
        const results = await Promise.all(predictionPromises)
        this.pricePredictions = results
        console.log('fetchMultiplePricePredictions - 设置的预测数据:', this.pricePredictions)

        const successCount = results.filter(r => r.success).length
        const totalCount = results.length

        if (successCount > 0) {
          this.$message.success(`成功获取${successCount}/${totalCount}个物料的价格预测`)
        } else {
          this.$message.error('所有物料的价格预测获取失败')
        }
      } catch (error) {
        console.error('批量获取价格预测数据失败:', error)
        this.$message.error('批量获取价格预测失败：' + error.message)
      } finally {
        this.predictionLoading = false
      }
    },

    // 获取物料名称列表
    async fetchMaterialNameList() {
      try {
        const params = {
          category: parseInt(this.selectedMaterialCategory)
        }

        const response = await getMaterialNameList(params)
        console.log('fetchMaterialNameList - 完整响应:', response)

        if (response && response.data && Array.isArray(response.data)) {
          this.materialNameOptions = response.data
          console.log('fetchMaterialNameList - 设置的数据:', this.materialNameOptions)

          // 设置默认选中PB块，如果存在的话
          const pbMaterial = this.materialNameOptions.find(item => item.itemName === 'PB块')
          if (pbMaterial) {
            this.selectedMaterial = 'PB块'
          } else if (this.materialNameOptions.length > 0) {
            // 如果没有PB块，选择第一个
            this.selectedMaterial = this.materialNameOptions[0].itemName
          }

          // 获取价格数据
          this.fetchPriceAndStoreData()
        } else {
          console.error('获取物料名称列表失败', response)
          this.materialNameOptions = []
        }
      } catch (error) {
        console.error('获取物料名称列表失败:', error)
        this.materialNameOptions = []
      }
    },

    // 获取物料价格和采购量数据
    async fetchPriceAndStoreData() {
      try {
        const params = {
          dimensionType: this.currentDimensionType,
          itemName: this.selectedMaterial
        }

        console.log('fetchPriceAndStoreData - 请求参数:', params)
        const response = await getPurchasePriceAndStore(params)
        console.log('fetchPriceAndStoreData - 完整响应:', response)

        if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
          this.priceAndStoreData = response.data[0] // 取第一个元素
          console.log('fetchPriceAndStoreData - 设置的数据:', this.priceAndStoreData)
        } else {
          console.error('获取价格和采购量数据失败', response)
          this.priceAndStoreData = null
        }
      } catch (error) {
        console.error('获取价格和采购量数据失败:', error)
        this.priceAndStoreData = null
      }

      // 数据获取完成后重新初始化价格趋势图
      this.$nextTick(() => {
        this.initPriceTrendChart()
      })
    },

    // 处理物资类型切换
    async handleMaterialCategoryTypeChange() {
      console.log('物资类型变化:', this.selectedMaterialCategory)
      // 重新获取物料名称列表
      await this.fetchMaterialNameList()
    },

    // 处理物料选择变化
    async handleMaterialChange() {
      console.log('物料选择变化:', this.selectedMaterial)
      await this.fetchPriceAndStoreData()
      // 不再自动触发AI预测，等用户点击按钮后再触发
    },

    calculateRealTimeInventoryTotal() {
      let total = 0
      if (this.realTimeInventoryData && this.realTimeInventoryData.length > 0) {
        this.realTimeInventoryData.forEach(item => {
          total += parseFloat(item.totalInventoryAmount) || 0
        })
      }
      return total.toFixed(2)
    },

    calculateCokingCoalTotal() {
      let total = 0
      if (this.cokingCoalInventoryData && this.cokingCoalInventoryData.length > 0) {
        // 找到所有数据中的最新日期
        let latestDate = ''
        this.cokingCoalInventoryData.forEach(item => {
          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {
            item.purchaseCokingDailyDetailList.forEach(detail => {
              if (detail.instockDate > latestDate) {
                latestDate = detail.instockDate
              }
            })
          }
        })

        // 计算最新日期各个物料的库存量合计
        this.cokingCoalInventoryData.forEach(item => {
          if (item.purchaseCokingDailyDetailList && item.purchaseCokingDailyDetailList.length > 0) {
            const latestDetail = item.purchaseCokingDailyDetailList.find(detail => detail.instockDate === latestDate)
            if (latestDetail) {
              total += parseFloat(latestDetail.invQty) || 0
            }
          }
        })
      }
      return (total / 10000).toFixed(2) // 转换为万吨
    },

    // 处理矿焦煤类型下拉框变化
    async handleCokingCoalTypeChange() {
      console.log('矿焦煤类型变化:', this.selectedCokingCoalType)
      // 重新初始化图表以应用过滤
      this.$nextTick(() => {
        this.initCokingCoalInventoryChart()
      })
    },

    // 机旁库存相关方法
    // 获取分厂选项列表
    async fetchFactoryDepOptions() {
      try {
        const response = await getDepNameList()
        console.log('fetchFactoryDepOptions - 完整响应:', response)

        if (response && response.data && Array.isArray(response.data)) {
          this.factoryDepOptions = response.data
          console.log('fetchFactoryDepOptions - 设置的数据:', this.factoryDepOptions)
        } else {
          console.error('获取分厂选项列表失败', response)
          this.factoryDepOptions = []
        }
      } catch (error) {
        console.error('获取分厂选项列表失败:', error)
        this.factoryDepOptions = []
      }

      // 获取默认数据（全部）
      this.fetchFactoryStockData()
    },

    // 处理分厂选择变化
    async handleFactoryDepChange() {
      console.log('分厂选择变化:', this.selectedFactoryDep)
      await this.fetchFactoryStockData()
    },

    // 处理物料类型选择变化
    async handleFactoryMaterialTypeChange() {
      console.log('物料类型选择变化:', this.selectedFactoryMaterialType)
      // 重新初始化图表以应用筛选
      this.$nextTick(() => {
        this.initFactoryStockChart()
      })
    },

    // 获取机旁库存数据
    async fetchFactoryStockData() {
      try {
        const depName = this.selectedFactoryDep || '' // 空字符串表示全部
        console.log('fetchFactoryStockData - 请求参数:', depName)

        const response = await getListMonthly(depName)
        console.log('fetchFactoryStockData - 完整响应:', response)

        if (response && response.data && Array.isArray(response.data)) {
          this.factoryStockData = response.data
          console.log('fetchFactoryStockData - 设置的数据:', this.factoryStockData)
        } else {
          console.error('获取机旁库存数据失败', response)
          this.factoryStockData = []
        }
      } catch (error) {
        console.error('获取机旁库存数据失败:', error)
        this.factoryStockData = []
      }

      // 数据获取完成后重新初始化图表
      this.$nextTick(() => {
        this.initFactoryStockChart()
      })
    },

    // 新增方法：处理采购量曲线物料类型变化
    async handlePurchaseAmountCategoriesChange() {
      console.log('采购量曲线物料类型变化:', this.purchaseAmountCategories)
      this.selectedPurchaseAmountMaterials = [] // 重置选中的物料
      await this.fetchPurchaseAmountMaterialList()
    },

    // 新增方法：处理市场价曲线物料类型变化
    async handleMarketPriceCategoriesChange() {
      console.log('市场价曲线物料类型变化:', this.marketPriceCategories)
      this.selectedMarketPriceMaterials = [] // 重置选中的物料
      await this.fetchMarketPriceMaterialList()
    },

    // 新增方法：获取采购量曲线物料列表
    async fetchPurchaseAmountMaterialList() {
      try {
        const params = {
          categories: this.purchaseAmountCategories,
          curveType: 2, // 采购量曲线
          dimensionType: this.currentDimensionType
        }

        console.log('fetchPurchaseAmountMaterialList - 请求参数:', params)
        const response = await getMaterialNameListFromNewTables(params)
        console.log('fetchPurchaseAmountMaterialList - 完整响应:', response)

        if (response && response.data && Array.isArray(response.data)) {
          this.purchaseAmountMaterialOptions = response.data
          console.log('fetchPurchaseAmountMaterialList - 设置的数据:', this.purchaseAmountMaterialOptions)

          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块
          if (this.selectedPurchaseAmountMaterials.length === 0 && !this.hasInitializedPriceChart) {
            const pbMaterial = this.purchaseAmountMaterialOptions.find(item => item.itemName === 'PB块')
            if (pbMaterial) {
              this.selectedPurchaseAmountMaterials = ['PB块']
              console.log('默认选中PB块 - 采购量曲线')

              // 检查市场价曲线是否也已经设置好默认值，如果是则触发数据获取
              this.checkAndTriggerInitialDataFetch()
            }
          }
        } else {
          console.error('获取采购量曲线物料列表失败', response)
          this.purchaseAmountMaterialOptions = []
        }
      } catch (error) {
        console.error('获取采购量曲线物料列表失败:', error)
        this.purchaseAmountMaterialOptions = []
      }
    },

    // 新增方法：获取市场价曲线物料列表
    async fetchMarketPriceMaterialList() {
      try {
        const params = {
          categories: this.marketPriceCategories,
          curveType: 1, // 价格曲线
          dimensionType: this.currentDimensionType
        }

        console.log('fetchMarketPriceMaterialList - 请求参数:', params)
        const response = await getMaterialNameListFromNewTables(params)
        console.log('fetchMarketPriceMaterialList - 完整响应:', response)

        if (response && response.data && Array.isArray(response.data)) {
          this.marketPriceMaterialOptions = response.data
          console.log('fetchMarketPriceMaterialList - 设置的数据:', this.marketPriceMaterialOptions)

          // 只在页面初始化时（第一次加载且无选中物料时）设置默认选中PB块
          if (this.selectedMarketPriceMaterials.length === 0 && !this.hasInitializedPriceChart) {
            const pbMaterial = this.marketPriceMaterialOptions.find(item => item.itemName === 'PB块')
            if (pbMaterial) {
              this.selectedMarketPriceMaterials = ['PB块']
              console.log('默认选中PB块 - 市场价曲线')

              // 检查采购量曲线是否也已经设置好默认值，如果是则触发数据获取
              this.checkAndTriggerInitialDataFetch()
            }
          }
        } else {
          console.error('获取市场价曲线物料列表失败', response)
          this.marketPriceMaterialOptions = []
        }
      } catch (error) {
        console.error('获取市场价曲线物料列表失败:', error)
        this.marketPriceMaterialOptions = []
      }
    },



    // 新增方法：获取物料采购价格数据（用于新的价格趋势图）
    async fetchPriceAndStoreDataForNewChart() {
      if (this.selectedPurchaseAmountMaterials.length === 0 && this.selectedMarketPriceMaterials.length === 0) {
        this.$message.warning('请至少选择一个物料')
        return
      }

      this.fetchingPriceData = true
      try {
        // 构建itemList
        const itemList = []

        // 添加采购量曲线的物料
        this.selectedPurchaseAmountMaterials.forEach(itemName => {
          itemList.push({
            curveType: 2, // 采购量曲线
            itemName: itemName
          })
        })

        // 添加市场价曲线的物料
        this.selectedMarketPriceMaterials.forEach(itemName => {
          itemList.push({
            curveType: 1, // 价格曲线
            itemName: itemName
          })
        })

        const params = {
          dimensionType: this.currentDimensionType,
          itemList: itemList
        }

        console.log('fetchPriceAndStoreData - 请求参数:', params)
        const response = await getPurchasePriceAndStoreFromNewTables(params)
        console.log('fetchPriceAndStoreData - 完整响应:', response)

        if (response && response.data) {
          this.newPriceAndStoreData = response.data
          console.log('fetchPriceAndStoreData - 设置的数据:', this.newPriceAndStoreData)

          // 重新渲染图表
          this.$nextTick(() => {
            this.initNewPriceTrendChart()
          })

          // 获取所有选中物料的去重列表
          const allSelectedMaterials = [...new Set([
            ...this.selectedPurchaseAmountMaterials,
            ...this.selectedMarketPriceMaterials
          ])]

          // 为每个物料调用AI预测接口
          if (allSelectedMaterials.length > 0) {
            this.fetchMultiplePricePredictions(allSelectedMaterials)
          }

          // 如果市场价曲线有选中物料，获取相似物料信息
          if (this.selectedMarketPriceMaterials.length > 0) {
            this.fetchSimilarMaterials(this.selectedMarketPriceMaterials)
          } else {
            // 清空相似物料数据
            this.similarMaterialsData = []
          }

          this.$message.success('数据获取成功')
        } else {
          console.error('获取物料采购价格数据失败', response)
          this.$message.error('获取数据失败')
        }
      } catch (error) {
        console.error('获取物料采购价格数据失败:', error)
        this.$message.error('获取数据失败：' + error.message)
      } finally {
        this.fetchingPriceData = false
      }
    },

    // 获取相似物料信息
    async fetchSimilarMaterials(itemNames) {
      this.similarMaterialsLoading = true
      try {
        const params = {
          itemNames: itemNames
        }

        console.log('fetchSimilarMaterials - 请求参数:', params)
        const response = await listSimilarByItemNames(params)
        console.log('fetchSimilarMaterials - 完整响应:', response)

        if (response && response.data && Array.isArray(response.data)) {
          this.similarMaterialsData = response.data
          console.log('fetchSimilarMaterials - 设置的数据:', this.similarMaterialsData)
        } else {
          console.error('获取相似物料数据失败', response)
          this.similarMaterialsData = []
        }
      } catch (error) {
        console.error('获取相似物料数据失败:', error)
        this.similarMaterialsData = []
      } finally {
        this.similarMaterialsLoading = false
      }
    },

    // 获取排名样式类
    getRankClass(rank) {
      if (rank === 1) return 'rank-first'
      if (rank === 2) return 'rank-second'
      if (rank === 3) return 'rank-third'
      return 'rank-default'
    },

    // 获取商品分类名称
    getCategoryName(category) {
      const categoryMap = {
        1: '矿石',
        2: '煤炭',
        3: '合金',
        4: '废钢'
      }
      return categoryMap[category] || '未知'
    },

    // 获取价格类型名称
    getPriceTypeName(priceType) {
      const priceTypeMap = {
        1: '现货价',
        2: '市场采购到厂价',
        3: '兴澄废钢收购价(车运)',
        4: '兴澄废钢收购价(船运)',
        5: '沙钢废钢收购价(车运)',
        6: '沙钢废钢收购价(船运)'
      }
      return priceTypeMap[priceType] || '未知'
    },

    // 打开对比弹框
    openComparisonDialog(item) {
      console.log('openComparisonDialog - 传入的item数据:', item)
      this.currentComparison = { ...item }
      console.log('openComparisonDialog - 设置的currentComparison:', this.currentComparison)
      this.comparisonDialogVisible = true

      // 弹框打开后获取对比数据
      this.$nextTick(() => {
        this.fetchComparisonData()
      })
    },

    // 关闭对比弹框
    closeComparisonDialog() {
      this.comparisonDialogVisible = false
      this.currentComparison = {}
      this.comparisonPriceData = null

      // 清理图表实例
      if (this.comparisonChartInstance) {
        try {
          this.comparisonChartInstance.dispose()
          this.comparisonChartInstance = null
        } catch (err) {
          console.error('清理对比图表实例失败:', err)
        }
      }
    },

    // 获取对比数据（独立实现，不耦合现有趋势图）
    async fetchComparisonData() {
      this.comparisonChartLoading = true
      try {
        // 构建两个物料的对比请求，只获取价格曲线
        const itemList = [
          {
            curveType: 1, // 价格曲线
            itemName: this.currentComparison.itemName
          },
          {
            curveType: 1, // 价格曲线
            itemName: this.currentComparison.compareItemName
          }
        ]

        const params = {
          dimensionType: this.currentDimensionType,
          itemList: itemList
        }

        console.log('fetchComparisonData - 请求参数:', params)
        const response = await getPurchasePriceAndStoreFromNewTables(params)
        console.log('fetchComparisonData - 完整响应:', response)

        if (response && response.data && Array.isArray(response.data)) {
          // 对返回的数据进行筛选，确保基准物料和相似物料的指定价格类型都能被提取
          const filteredData = []

          // 获取基准物料和相似物料的目标价格类型名称
          const basePriceTypeName = this.getPriceTypeName(this.currentComparison.priceType)
          const comparePriceTypeName = this.getPriceTypeName(this.currentComparison.comparePriceType)

          console.log('筛选条件:', {
            baseItemName: this.currentComparison.itemName,
            basePriceTypeName: basePriceTypeName,
            compareItemName: this.currentComparison.compareItemName,
            comparePriceTypeName: comparePriceTypeName
          })

          response.data.forEach(materialData => {
            const filteredMaterialData = { ...materialData }

            if (filteredMaterialData.procurementPriceVoList) {
              // 只保留匹配的价格类型
              filteredMaterialData.procurementPriceVoList = filteredMaterialData.procurementPriceVoList.filter(priceGroup => {
                let isMatch = false
                // 基准物料：匹配物料名称和基准价格类型
                if (materialData.itemName === this.currentComparison.itemName) {
                  isMatch = priceGroup.priceName === basePriceTypeName
                  console.log(`基准物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${basePriceTypeName}] 匹配:${isMatch}`)
                }

                if(isMatch){
                  return isMatch
                }else{
                  if (materialData.itemName === this.currentComparison.compareItemName) {
                    const isMatch = priceGroup.priceName === comparePriceTypeName
                    console.log(`相似物料[${materialData.itemName}] 价格类型[${priceGroup.priceName}] 目标类型[${comparePriceTypeName}] 匹配:${isMatch}`)
                    return isMatch
                  }
                }


                return false
              })

              console.log(111111111)
              console.log(filteredMaterialData.procurementPriceVoList)

              // 只有当该物料有匹配的价格类型时才加入结果
              if (filteredMaterialData.procurementPriceVoList.length > 0) {
                filteredData.push(filteredMaterialData)
                console.log(`添加物料[${materialData.itemName}]，包含${filteredMaterialData.procurementPriceVoList.length}个价格组`)
              }
            }
          })

          this.comparisonPriceData = filteredData
          console.log('fetchComparisonData - 筛选后的数据:', this.comparisonPriceData)
          console.log('筛选结果统计:', {
            totalMaterials: filteredData.length,
            materials: filteredData.map(m => ({
              name: m.itemName,
              priceGroupCount: m.procurementPriceVoList?.length || 0,
              priceGroups: m.procurementPriceVoList?.map(p => p.priceName) || []
            }))
          })

          // 渲染对比图表
          this.$nextTick(() => {
            this.renderComparisonChart()
          })
        } else {
          console.error('获取对比数据失败', response)
          this.$message.error('获取对比数据失败')
        }
      } catch (error) {
        console.error('获取对比数据失败:', error)
        this.$message.error('获取对比数据失败：' + error.message)
      } finally {
        this.comparisonChartLoading = false
      }
    },

    // 渲染对比图表（独立实现，不耦合现有趋势图）
    renderComparisonChart() {
      const chartDom = document.getElementById('comparisonChart')
      if (!chartDom) {
        console.error('找不到对比图表DOM元素')
        return
      }

      // 清理现有实例
      if (this.comparisonChartInstance) {
        try {
          this.comparisonChartInstance.dispose()
        } catch (err) {
          console.error('清理现有对比图表实例失败:', err)
        }
      }

      // 创建新的图表实例
      try {
        this.comparisonChartInstance = echarts.init(chartDom)
      } catch (err) {
        console.error('创建对比图表实例失败:', err)
        return
      }

      if (!this.comparisonPriceData || this.comparisonPriceData.length === 0) {
        chartDom.innerHTML = '<div class="chart-placeholder">暂无对比数据</div>'
        return
      }

      const formatDate = (dateStr) => {
        const year = dateStr.substring(0, 4)
        const month = dateStr.substring(4, 6)
        const day = dateStr.substring(6, 8)
        return `${year}年${month}月${day}日`
      }

      // 收集所有日期
      let allDates = new Set()

      this.comparisonPriceData.forEach(materialData => {
        if (materialData.procurementPriceVoList) {
          materialData.procurementPriceVoList.forEach(priceGroup => {
            if (priceGroup.priceList) {
              priceGroup.priceList.forEach(item => {
                allDates.add(item.recordDate)
              })
            }
          })
        }
      })

      allDates = Array.from(allDates).sort()
      const xAxisData = allDates.map(formatDate)

      if (allDates.length === 0) {
        chartDom.innerHTML = '<div class="chart-placeholder">暂无对比数据</div>'
        return
      }

      // 构建系列数据
      const series = []
      const legendData = []
      const colors = ['#8fe9ff', '#ff9f7f', '#5fd8b6', '#ffb980']
      let colorIndex = 0

      console.log('=== 开始处理对比数据 ===')
      console.log('对比数据总览:', {
        materialCount: this.comparisonPriceData.length,
        baseMaterial: this.currentComparison.itemName,
        compareMaterial: this.currentComparison.compareItemName
      })

      this.comparisonPriceData.forEach(materialData => {
        const materialName = materialData.itemName
        console.log(`\n处理物料: ${materialName}`)

        if (materialData.procurementPriceVoList) {
          console.log(`  该物料有 ${materialData.procurementPriceVoList.length} 个价格组`)
          materialData.procurementPriceVoList.forEach((priceGroup, index) => {
            console.log(`  价格组 ${index + 1}: ${priceGroup.priceName}，数据点数量: ${priceGroup.priceList?.length || 0}`)
          })

          // 数据已经在fetchComparisonData中预先筛选过，这里直接处理所有匹配的价格组
          materialData.procurementPriceVoList.forEach((priceGroup, groupIndex) => {
            const priceData = allDates.map(date => {
              const found = priceGroup.priceList.find(item => item.recordDate === date)
              return found ? parseFloat(found.price) : null
            })

            // 统计有效数据点
            const validDataCount = priceData.filter(v => v !== null && v !== undefined).length
            console.log(`    处理价格组[${priceGroup.priceName}]，有效数据点: ${validDataCount}/${priceData.length}`)

            // 确保每条曲线都有唯一的名称和颜色，即使数据相同
            const uniqueName = `${materialName}-${priceGroup.priceName}`
            const lineColor = colors[colorIndex % colors.length]

            // 检查是否已经有相同的数据，如果有则添加轻微偏移
            const dataStr = JSON.stringify(priceData)
            const existingSeries = series.find(s => JSON.stringify(s.data) === dataStr)
            let adjustedData = priceData

            if (existingSeries && priceData.some(v => v !== null)) {
              // 为重复数据添加极小的偏移量（0.01），确保两条线都能显示
              adjustedData = priceData.map(value => value !== null ? value + 0.01 : null)
              console.log(`    检测到重复数据，为 ${uniqueName} 添加偏移`)
            }

            series.push({
              name: uniqueName,
              type: 'line',
              data: adjustedData,
              smooth: true,
              lineStyle: {
                width: 3,
                color: lineColor,
                // 如果是偏移的数据，使用虚线样式区分
                type: adjustedData !== priceData ? 'dashed' : 'solid'
              },
              itemStyle: {
                color: lineColor
              },
              symbol: 'circle',
              symbolSize: 6,
              connectNulls: true,
              // 添加z-index确保两条线都能显示
              z: colorIndex + 1
            })

            legendData.push(uniqueName)
            colorIndex++
            console.log(`    ✓ 添加曲线: ${uniqueName}，颜色: ${lineColor}，有效数据: ${validDataCount}`)
          })
        }
      })

      console.log(`\n=== 图表数据处理完成 ===`)
      console.log(`总计添加 ${series.length} 条曲线:`)
      series.forEach((s, i) => {
        const validCount = s.data.filter(v => v !== null && v !== undefined).length
        console.log(`  ${i + 1}. ${s.name} (有效数据: ${validCount})`)
      })

      // 计算Y轴范围
      let priceMin, priceMax
      const priceValues = series.flatMap(s => s.data.filter(v => v !== null && v !== undefined))
      if (priceValues.length > 0) {
        priceMin = Math.min(...priceValues)
        priceMax = Math.max(...priceValues)
      }

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let str = params[0].axisValueLabel + '<br/>'
            params.forEach(item => {
              if (item.value !== null && item.value !== undefined) {
                str += `${item.marker}${item.seriesName}: ${item.value} 元/吨<br/>`
              } else {
                str += `${item.marker}${item.seriesName}: -<br/>`
              }
            })
            return str
          }
        },
        legend: {
          data: legendData,
          textStyle: {
            color: '#fff'
          },
          top: '5%'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '12%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLabel: {
            color: '#eee',
            interval: function(index, value) {
              if (index >= allDates.length || !allDates.length) return false

              const uniqueMonths = new Set()
              allDates.forEach(dateStr => {
                const year = dateStr.substring(0, 4)
                const month = dateStr.substring(4, 6)
                uniqueMonths.add(`${year}${month}`)
              })

              const monthsCount = uniqueMonths.size
              if (monthsCount <= 1) return true

              const totalDataPoints = allDates.length
              const idealInterval = Math.floor(totalDataPoints / Math.min(monthsCount, 8))

              return index % Math.max(idealInterval, 1) === 0
            },
            formatter: function(value, index) {
              if (index >= allDates.length) return ''
              const originalDateStr = allDates[index]
              if (!originalDateStr) return ''

              const year = originalDateStr.substring(0, 4)
              const month = parseInt(originalDateStr.substring(4, 6))
              return `${year}.${month}`
            }
          },
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '价格（元/吨）',
          min: priceMin,
          max: priceMax,
          axisLine: {
            lineStyle: {
              color: '#eee'
            }
          },
          axisLabel: {
            color: '#eee'
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: series
      }

      this.comparisonChartInstance.setOption(option, true)
    },

    // 检查两个曲线是否都已设置默认值，如果是则触发初始数据获取
    checkAndTriggerInitialDataFetch() {
      // 检查两个曲线是否都已经设置了默认的PB块
      if (this.selectedPurchaseAmountMaterials.includes('PB块') &&
        this.selectedMarketPriceMaterials.includes('PB块') &&
        !this.hasInitializedPriceChart) {

        this.hasInitializedPriceChart = true // 标记已经初始化过
        console.log('两个曲线都已设置默认值，自动触发数据获取')

        // 自动触发数据获取
        this.$nextTick(() => {
          this.fetchPriceAndStoreDataForNewChart()
        })
      }
    }
  }
}
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Microsoft YaHei", sans-serif;
}

.dashboard-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #191970, #4B0082, #800080);
  color: #fff;
  overflow-x: hidden;
  padding: 10px;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 10px;
  position: relative;
  padding: 5px 0;
}

.dashboard-header h1 {
  font-size: 24px;
  position: relative;
  display: inline-block;
  padding: 5px 40px;
}

.dashboard-header::before,
.dashboard-header::after {
  content: "";
  position: absolute;
  top: 50%;
  width: 30%;
  height: 2px;
  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);
}

.dashboard-header::before {
  left: 0;
}

.dashboard-header::after {
  right: 0;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: auto auto auto auto auto auto;
  gap: 10px;
  min-height: calc(100vh - 80px);
}

.card {
  background-color: rgba(33, 10, 56, 0.7);
  border-radius: 5px;
  padding: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(0,212,255,0) 0%, rgba(0,212,255,1) 50%, rgba(0,212,255,0) 100%);
}

.card-title {
  font-size: 14px;
  margin-bottom: 5px;
  font-weight: normal;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-filter-dropdown-container {
  z-index: 10;
}

.chart-filter-dropdown-container select {
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(138, 43, 226, 0.7);
  color: #fff;
  border: 1px solid rgba(0, 212, 255, 0.3);
  font-size: 12px;
}

.chart {
  width: 100%;
  height: calc(100% - 20px);
  min-height: 200px;
  flex: 1;
}

.stat-cards {
  display: flex;
  justify-content: space-around;
  height: 100%;
  align-items: center;
}

.stat-card {
  text-align: center;
  flex-grow: 1;
}

.stat-value {
  font-size: 34px;
  font-weight: bold;
  color: #00ffff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 18px;
  opacity: 0.8;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(255,255,255,0.5);
  font-size: 14px;
}

.material-chart-card {
  height: auto;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.material-chart-card .chart {
  flex-grow: 1;
  height: 250px;
  min-height: 250px;
}

.time-filter {
  display: flex;
  gap: 10px;
}

.time-filter-btn {
  padding: 6px 12px;
  border: none;
  background-color: rgba(33, 10, 56, 0.7);
  color: #eee;
  border-radius: 20px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 212, 255, 0.2);
  position: relative;
}

.time-filter-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.time-filter-btn.active {
  background-color: rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.7);
  color: #00ffff;
  font-weight: 500;
}

.header-controls {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 15px;
  z-index: 1000;
}

.fullscreen-btn {
  padding: 8px 12px;
  border: none;
  background-color: rgba(33, 10, 56, 0.7);
  color: #eee;
  border-radius: 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 212, 255, 0.2);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 32px;
}

.fullscreen-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
  background-color: rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.7);
  color: #00ffff;
}

/* AI价格预测区域样式 */
.price-prediction-section {
  margin-top: 15px;
  margin-bottom: 15px;
  padding: 15px;
  background-color: rgba(16, 7, 33, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.prediction-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 13px;
}

.prediction-header i {
  color: #00ffff;
  margin-right: 8px;
  font-size: 16px;
}

.model-info {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.prediction-content {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 15px;
  border-left: 3px solid #00ffff;
  position: relative;
}

.prediction-placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  text-align: center;
  padding: 20px;
  font-size: 12px;
}



/* 多个预测结果的样式 */
.predictions-container {
  max-height: 500px;
  overflow-y: auto;
  padding-right: 5px;
}

.prediction-item {
  margin-bottom: 15px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  border-left: 3px solid #00ffff;
  overflow: hidden;
}

.prediction-item.prediction-error {
  border-left-color: #ff6b6b;
}

.prediction-material-title {
  background-color: rgba(0, 212, 255, 0.1);
  padding: 8px 12px;
  font-size: 13px;
  font-weight: 600;
  color: #00ffff;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.prediction-item.prediction-error .prediction-material-title {
  background-color: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
  border-bottom-color: rgba(255, 107, 107, 0.2);
}

.prediction-material-title i {
  margin-right: 6px;
}

.loading-info {
  color: #00ffff;
  font-size: 12px;
  margin-left: 10px;
  font-style: italic;
}

/* 预测容器滚动条样式 */
.predictions-container::-webkit-scrollbar {
  width: 4px;
}

.predictions-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.predictions-container::-webkit-scrollbar-thumb {
  background: rgba(0, 212, 255, 0.5);
  border-radius: 2px;
}

.predictions-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 212, 255, 0.8);
}

/* 一问一答样式 */
.qa-section {
  padding: 0;
}

.question-section, .answer-section {
  margin-bottom: 15px;
}

.answer-section {
  margin-bottom: 0;
}

.qa-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 13px;
}

.question-label {
  color: #ffb980;
}

.answer-label {
  color: #00ffff;
}

.qa-label i {
  margin-right: 6px;
  font-size: 14px;
}

.question-text, .answer-text {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 12px 15px;
  line-height: 1.6;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  white-space: pre-wrap;
  word-wrap: break-word;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.question-text {
  border-left: 3px solid #ffb980;
}

.answer-text {
  border-left: 3px solid #00ffff;
  max-height: 200px;
  overflow-y: auto;
  padding-right: 18px;
}

/* 问题文本样式 */
.question-text {
  font-style: italic;
  color: rgba(255, 200, 150, 0.9);
}

/* 答案文本滚动条样式 */
.answer-text::-webkit-scrollbar {
  width: 4px;
}

.answer-text::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.answer-text::-webkit-scrollbar-thumb {
  background: rgba(0, 212, 255, 0.5);
  border-radius: 2px;
}

.answer-text::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 212, 255, 0.8);
}

/* 价格趋势卡片特殊样式 */
.price-trend-card {
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 400px;
}

.price-trend-card .chart {
  flex-shrink: 0;
  height: 300px !important;
  min-height: 300px;
}

.price-trend-card .price-prediction-section {
  flex-shrink: 0;
  margin-top: 15px;
  margin-bottom: 0;
}

.inventory-total {
  font-size: 12px;
  color: #00ffff;
  font-weight: normal;
  opacity: 0.9;
}

/* 新增：价格趋势图控件样式 */
.price-trend-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding: 10px;
  background-color: rgba(16, 7, 33, 0.4);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.left-controls, .right-controls {
  flex: 1;
  max-width: 45%;
}

.curve-label {
  font-size: 13px;
  color: #00ffff;
  margin-bottom: 8px;
  font-weight: bold;
}

.left-controls .curve-label {
  text-align: left;
}

.right-controls .curve-label {
  text-align: right;
}

.dropdown-row {
  display: flex;
  gap: 10px;
  align-items: center;
}

.right-controls .dropdown-row {
  justify-content: flex-end;
}

.fetch-data-btn-container {
  text-align: right;
  margin-bottom: 15px;
}

.modern-fetch-btn {
  background: rgba(138, 43, 226, 0.7);
  border: none;
  border-radius: 12px;
  padding: 10px 20px;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(138, 43, 226, 0.3);
  position: relative;
  overflow: hidden;
}

.modern-fetch-btn:hover:not(:disabled) {
  background: rgba(138, 43, 226, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(138, 43, 226, 0.5);
}

.modern-fetch-btn:active {
  transform: translateY(0);
}

.modern-fetch-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.modern-fetch-btn.loading {
  background: rgba(138, 43, 226, 0.7);
}

.modern-fetch-btn i {
  margin-right: 8px;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Element UI 下拉框样式覆盖 */
.price-trend-controls .el-select {
  background-color: transparent !important;
}

.price-trend-controls .el-select .el-input__inner {
  background-color: #4a1c5a !important;
  border: 1px solid rgba(116, 75, 162, 0.5) !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  font-size: 13px !important;
  transition: all 0.3s ease !important;
}

.price-trend-controls .el-select .el-input__inner:hover {
  border-color: rgba(116, 75, 162, 0.8) !important;
  box-shadow: 0 0 8px rgba(116, 75, 162, 0.3) !important;
}

.price-trend-controls .el-select .el-input__inner:focus {
  border-color: #764ba2 !important;
  box-shadow: 0 0 12px rgba(116, 75, 162, 0.5) !important;
}

.price-trend-controls .el-select .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.7) !important;
}

.price-trend-controls .el-select .el-input__suffix {
  color: #ffffff !important;
}

.price-trend-controls .el-select .el-input__suffix i {
  color: #ffffff !important;
}

.price-trend-controls .el-tag {
  background-color: rgba(116, 75, 162, 0.6) !important;
  border-color: rgba(116, 75, 162, 0.8) !important;
  color: #ffffff !important;
  border-radius: 6px !important;
}

.price-trend-controls .el-tag .el-tag__close {
  color: #ffffff !important;
}

.price-trend-controls .el-tag .el-tag__close:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

/* 相似物料区域样式 */
.similar-materials-section {
  margin: 20px 0;
  padding: 15px;
  background-color: rgba(16, 7, 33, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.2);
}

.similar-materials-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 14px;
  flex-wrap: wrap;
  gap: 8px;
}

.similar-materials-header i {
  color: #00ffff;
  margin-right: 8px;
  font-size: 16px;
}

.section-title {
  color: #00ffff;
  font-weight: bold;
  font-size: 14px;
}

.loading-info {
  color: #00ffff;
  font-size: 12px;
  margin-left: 10px;
  font-style: italic;
}

.similar-materials-container {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 10px;
  overflow-x: auto;
}

.materials-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

.materials-table th {
  background-color: rgba(0, 212, 255, 0.1);
  color: #00ffff;
  padding: 8px 12px;
  text-align: left;
  border-bottom: 2px solid rgba(0, 212, 255, 0.3);
  font-weight: 600;
}

.materials-table td {
  padding: 8px 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.material-row {
  transition: background-color 0.3s ease;
}

.material-row:hover {
  background-color: rgba(0, 212, 255, 0.05);
}

.rank-cell {
  text-align: center;
  width: 60px;
}

.rank-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 50%;
  color: #fff;
  font-weight: bold;
  font-size: 12px;
  min-width: 20px;
  text-align: center;
}

.rank-first {
  background: linear-gradient(135deg, #ffd700, #ffb347);
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.rank-second {
  background: linear-gradient(135deg, #c0c0c0, #a8a8a8);
  box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.rank-third {
  background: linear-gradient(135deg, #cd7f32, #b8860b);
  box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.rank-default {
  background-color: rgba(138, 43, 226, 0.7);
}

.material-name, .compare-material-name {
  font-weight: 500;
  color: #fff;
}

.compare-material-name {
  color: #00ffff;
}

.score-cell {
  text-align: center;
  width: 120px;
  min-width: 120px;
}

.score-value {
  display: inline-block;
  padding: 2px 6px;
  background-color: rgba(0, 212, 255, 0.2);
  border-radius: 4px;
  color: #00ffff;
  font-weight: bold;
}

.score-desc {
  color: #ffb980;
  font-style: italic;
}

.category-cell {
  color: #5fd8b6;
  font-weight: 500;
}

.similar-materials-placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  text-align: center;
  padding: 20px;
  font-size: 12px;
}

.similar-materials-group {
  margin-bottom: 10px;
}

.similar-materials-group:last-child {
  margin-bottom: 0;
}

.group-title {
  color: #00ffff;
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 10px;
  padding: 8px 12px;
  background-color: rgba(0, 212, 255, 0.1);
  border-radius: 6px;
  border-left: 3px solid #00ffff;
}

.price-type-cell {
  color: #e879ed;
  font-size: 11px;
  max-width: 120px;
  word-wrap: break-word;
}

.algorithm-desc {
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
  font-style: italic;
  margin-left: 8px;
}

.action-cell {
  text-align: center;
  width: 100px;
}

.view-comparison-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  color: #fff;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  min-width: 70px;
}

.view-comparison-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.view-comparison-btn:active {
  transform: translateY(0);
}

.view-comparison-btn i {
  font-size: 13px;
}

/* 对比弹框样式 */
.comparison-dialog .el-dialog {
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, #191970, #4B0082, #800080);
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.comparison-dialog .el-dialog__header {
  background: linear-gradient(135deg, rgba(33, 10, 56, 0.9), rgba(0, 212, 255, 0.2));
  padding: 20px 24px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.comparison-dialog .el-dialog__title {
  color: #00ffff;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.comparison-dialog .el-dialog__headerbtn .el-dialog__close {
  color: #00ffff;
  font-size: 20px;
  transition: all 0.3s ease;
}

.comparison-dialog .el-dialog__headerbtn .el-dialog__close:hover {
  color: #fff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
}

.comparison-dialog .el-dialog__body {
  padding: 0;
  background: transparent;
}

.comparison-content {
  padding: 20px;
  background: transparent;
}

.comparison-header {
  margin-bottom: 20px;
}

.comparison-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  font-size: 16px;
  font-weight: 600;
}

.base-material {
  color: #00ffff;
  padding: 8px 16px;
  background-color: rgba(0, 212, 255, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.5);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.vs-text {
  color: #fff;
  font-size: 14px;
  font-weight: normal;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.compare-material {
  color: #ea7ccc;
  padding: 8px 16px;
  background-color: rgba(234, 124, 204, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(234, 124, 204, 0.5);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.similarity-info {
  color: #ffb980;
  font-size: 14px;
  font-weight: normal;
  padding: 4px 12px;
  background-color: rgba(255, 185, 128, 0.2);
  border-radius: 6px;
  border: 1px solid rgba(255, 185, 128, 0.4);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.comparison-chart-container {
  background-color: rgba(33, 10, 56, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
}

.comparison-chart {
  width: 100%;
  height: 500px;
}
</style>
